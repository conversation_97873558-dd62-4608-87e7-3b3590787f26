(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-70ca55ea"],{"0b25":function(e,t,r){"use strict";var n=r("5926"),a=r("50c4"),i=RangeError;e.exports=function(e){if(void 0===e)return 0;var t=n(e),r=a(t);if(t!==r)throw new i("Wrong length or index");return r}},"13a6":function(e,t,r){"use strict";var n=Math.round;e.exports=function(e){var t=n(e);return t<0?0:t>255?255:255&t}},1448:function(e,t,r){"use strict";var n=r("dfb9"),a=r("b6b7");e.exports=function(e,t){return n(a(e),t)}},"145e":function(e,t,r){"use strict";var n=r("7b0b"),a=r("23cb"),i=r("07fa"),s=r("083a"),o=Math.min;e.exports=[].copyWithin||function(e,t){var r=n(this),f=i(r),l=a(e,f),c=a(t,f),h=arguments.length>2?arguments[2]:void 0,u=o((void 0===h?f:a(h,f))-c,f-l),d=1;c<l&&l<c+u&&(d=-1,c+=u-1,l+=u-1);while(u-- >0)c in r?r[l]=r[c]:s(r,l),l+=d,c+=d;return r}},"170b":function(e,t,r){"use strict";var n=r("ebb5"),a=r("50c4"),i=r("23cb"),s=r("b6b7"),o=n.aTypedArray,f=n.exportTypedArrayMethod;f("subarray",(function(e,t){var r=o(this),n=r.length,f=i(e,n),l=s(r);return new l(r.buffer,r.byteOffset+f*r.BYTES_PER_ELEMENT,a((void 0===t?n:i(t,n))-f))}))},"182d":function(e,t,r){"use strict";var n=r("f8cd"),a=RangeError;e.exports=function(e,t){var r=n(e);if(r%t)throw new a("Wrong offset");return r}},"1d02":function(e,t,r){"use strict";var n=r("ebb5"),a=r("a258").findLastIndex,i=n.aTypedArray,s=n.exportTypedArrayMethod;s("findLastIndex",(function(e){return a(i(this),e,arguments.length>1?arguments[1]:void 0)}))},"219c":function(e,t,r){"use strict";var n=r("da84"),a=r("4625"),i=r("d039"),s=r("59ed"),o=r("addb"),f=r("ebb5"),l=r("04d1"),c=r("d998"),h=r("2d00"),u=r("512ce"),d=f.aTypedArray,p=f.exportTypedArrayMethod,m=n.Uint16Array,g=m&&a(m.prototype.sort),v=!!g&&!(i((function(){g(new m(2),null)}))&&i((function(){g(new m(2),{})}))),T=!!g&&!i((function(){if(h)return h<74;if(l)return l<67;if(c)return!0;if(u)return u<602;var e,t,r=new m(516),n=Array(516);for(e=0;e<516;e++)t=e%4,r[e]=515-e,n[e]=e-2*t+3;for(g(r,(function(e,t){return(e/4|0)-(t/4|0)})),e=0;e<516;e++)if(r[e]!==n[e])return!0})),b=function(e){return function(t,r){return void 0!==e?+e(t,r)||0:r!==r?-1:t!==t?1:0===t&&0===r?1/t>0&&1/r<0?1:-1:t>r}};p("sort",(function(e){return void 0!==e&&s(e),T?g(this,e):o(d(this),b(e))}),!T||v)},"249d":function(e,t,r){"use strict";var n=r("23e7"),a=r("41f6");a&&n({target:"ArrayBuffer",proto:!0},{transfer:function(){return a(this,arguments.length?arguments[0]:void 0,!0)}})},"25a1":function(e,t,r){"use strict";var n=r("ebb5"),a=r("d58f").right,i=n.aTypedArray,s=n.exportTypedArrayMethod;s("reduceRight",(function(e){var t=arguments.length;return a(i(this),e,t,t>1?arguments[1]:void 0)}))},"25ca":function(e,t,r){"use strict";r.d(t,"c",(function(){return gu})),r.d(t,"b",(function(){return Mu})),r.d(t,"a",(function(){return Ge}));
/*! xlsx.js (C) 2013-present SheetJS -- http://sheetjs.com */
var n={version:"0.18.5"},a=1200,i=1252,s=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],o={0:1252,1:65001,2:65001,77:1e4,128:932,129:949,130:1361,134:936,136:950,161:1253,162:1254,163:1258,177:1255,178:1256,186:1257,204:1251,222:874,238:1250,255:1252,69:6969},f=function(e){-1!=s.indexOf(e)&&(i=o[0]=e)};function l(){f(1252)}var c=function(e){a=e,f(e)};function h(){c(1200),l()}function u(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r+1)+(e.charCodeAt(2*r)<<8));return t.join("")}var d,p=function(e){return String.fromCharCode(e)},m=function(e){return String.fromCharCode(e)};var g=null,v=!0,T="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function b(e){for(var t="",r=0,n=0,a=0,i=0,s=0,o=0,f=0,l=0;l<e.length;)r=e.charCodeAt(l++),i=r>>2,n=e.charCodeAt(l++),s=(3&r)<<4|n>>4,a=e.charCodeAt(l++),o=(15&n)<<2|a>>6,f=63&a,isNaN(n)?o=f=64:isNaN(a)&&(f=64),t+=T.charAt(i)+T.charAt(s)+T.charAt(o)+T.charAt(f);return t}function w(e){var t="",r=0,n=0,a=0,i=0,s=0,o=0,f=0;e=e.replace(/[^\w\+\/\=]/g,"");for(var l=0;l<e.length;)i=T.indexOf(e.charAt(l++)),s=T.indexOf(e.charAt(l++)),r=i<<2|s>>4,t+=String.fromCharCode(r),o=T.indexOf(e.charAt(l++)),n=(15&s)<<4|o>>2,64!==o&&(t+=String.fromCharCode(n)),f=T.indexOf(e.charAt(l++)),a=(3&o)<<6|f,64!==f&&(t+=String.fromCharCode(a));return t}var E=function(){return"undefined"!==typeof Buffer&&"undefined"!==typeof process&&"undefined"!==typeof process.versions&&!!process.versions.node}(),A=function(){if("undefined"!==typeof Buffer){var e=!Buffer.from;if(!e)try{Buffer.from("foo","utf8")}catch(t){e=!0}return e?function(e,t){return t?new Buffer(e,t):new Buffer(e)}:Buffer.from.bind(Buffer)}return function(){}}();function y(e){return E?Buffer.alloc?Buffer.alloc(e):new Buffer(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):new Array(e)}function S(e){return E?Buffer.allocUnsafe?Buffer.allocUnsafe(e):new Buffer(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):new Array(e)}var _=function(e){return E?A(e,"binary"):e.split("").map((function(e){return 255&e.charCodeAt(0)}))};function x(e){if("undefined"===typeof ArrayBuffer)return _(e);for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),n=0;n!=e.length;++n)r[n]=255&e.charCodeAt(n);return t}function O(e){if(Array.isArray(e))return e.map((function(e){return String.fromCharCode(e)})).join("");for(var t=[],r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}function C(e){if("undefined"===typeof Uint8Array)throw new Error("Unsupported");return new Uint8Array(e)}var R=E?function(e){return Buffer.concat(e.map((function(e){return Buffer.isBuffer(e)?e:A(e)})))}:function(e){if("undefined"!==typeof Uint8Array){var t=0,r=0;for(t=0;t<e.length;++t)r+=e[t].length;var n=new Uint8Array(r),a=0;for(t=0,r=0;t<e.length;r+=a,++t)if(a=e[t].length,e[t]instanceof Uint8Array)n.set(e[t],r);else{if("string"==typeof e[t])throw"wtf";n.set(new Uint8Array(e[t]),r)}return n}return[].concat.apply([],e.map((function(e){return Array.isArray(e)?e:[].slice.call(e)})))};function k(e){for(var t=[],r=0,n=e.length+250,a=y(e.length+255),i=0;i<e.length;++i){var s=e.charCodeAt(i);if(s<128)a[r++]=s;else if(s<2048)a[r++]=192|s>>6&31,a[r++]=128|63&s;else if(s>=55296&&s<57344){s=64+(1023&s);var o=1023&e.charCodeAt(++i);a[r++]=240|s>>8&7,a[r++]=128|s>>2&63,a[r++]=128|o>>6&15|(3&s)<<4,a[r++]=128|63&o}else a[r++]=224|s>>12&15,a[r++]=128|s>>6&63,a[r++]=128|63&s;r>n&&(t.push(a.slice(0,r)),r=0,a=y(65535),n=65530)}return t.push(a.slice(0,r)),R(t)}var I=/\u0000/g,N=/[\u0001-\u0006]/g;function D(e){var t="",r=e.length-1;while(r>=0)t+=e.charAt(r--);return t}function P(e,t){var r=""+e;return r.length>=t?r:pt("0",t-r.length)+r}function L(e,t){var r=""+e;return r.length>=t?r:pt(" ",t-r.length)+r}function M(e,t){var r=""+e;return r.length>=t?r:r+pt(" ",t-r.length)}function F(e,t){var r=""+Math.round(e);return r.length>=t?r:pt("0",t-r.length)+r}function U(e,t){var r=""+e;return r.length>=t?r:pt("0",t-r.length)+r}var B=Math.pow(2,32);function W(e,t){if(e>B||e<-B)return F(e,t);var r=Math.round(e);return U(r,t)}function H(e,t){return t=t||0,e.length>=7+t&&103===(32|e.charCodeAt(t))&&101===(32|e.charCodeAt(t+1))&&110===(32|e.charCodeAt(t+2))&&101===(32|e.charCodeAt(t+3))&&114===(32|e.charCodeAt(t+4))&&97===(32|e.charCodeAt(t+5))&&108===(32|e.charCodeAt(t+6))}var G=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],V=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]];function j(e){return e||(e={}),e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"上午/下午 "hh"時"mm"分"ss"秒 "',e}var z={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"上午/下午 "hh"時"mm"分"ss"秒 "'},Y={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},X={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function K(e,t,r){var n=e<0?-1:1,a=e*n,i=0,s=1,o=0,f=1,l=0,c=0,h=Math.floor(a);while(l<t){if(h=Math.floor(a),o=h*s+i,c=h*l+f,a-h<5e-8)break;a=1/(a-h),i=s,s=o,f=l,l=c}if(c>t&&(l>t?(c=f,o=i):(c=l,o=s)),!r)return[0,n*o,c];var u=Math.floor(n*o/c);return[u,n*o-u*c,c]}function J(e,t,r){if(e>2958465||e<0)return null;var n=0|e,a=Math.floor(86400*(e-n)),i=0,s=[],o={D:n,T:a,u:86400*(e-n)-a,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(Math.abs(o.u)<1e-6&&(o.u=0),t&&t.date1904&&(n+=1462),o.u>.9999&&(o.u=0,86400==++a&&(o.T=a=0,++n,++o.D)),60===n)s=r?[1317,10,29]:[1900,2,29],i=3;else if(0===n)s=r?[1317,8,29]:[1900,1,0],i=6;else{n>60&&--n;var f=new Date(1900,0,1);f.setDate(f.getDate()+n-1),s=[f.getFullYear(),f.getMonth()+1,f.getDate()],i=f.getDay(),n<60&&(i=(i+6)%7),r&&(i=oe(f,s))}return o.y=s[0],o.m=s[1],o.d=s[2],o.S=a%60,a=Math.floor(a/60),o.M=a%60,a=Math.floor(a/60),o.H=a,o.q=i,o}var Z=new Date(1899,11,31,0,0,0),q=Z.getTime(),Q=new Date(1900,2,1,0,0,0);function ee(e,t){var r=e.getTime();return t?r-=1262304e5:e>=Q&&(r+=864e5),(r-(q+6e4*(e.getTimezoneOffset()-Z.getTimezoneOffset())))/864e5}function te(e){return-1==e.indexOf(".")?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function re(e){return-1==e.indexOf("E")?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2")}function ne(e){var t=e<0?12:11,r=te(e.toFixed(12));return r.length<=t?r:(r=e.toPrecision(10),r.length<=t?r:e.toExponential(5))}function ae(e){var t=te(e.toFixed(11));return t.length>(e<0?12:11)||"0"===t||"-0"===t?e.toPrecision(6):t}function ie(e){var t,r=Math.floor(Math.log(Math.abs(e))*Math.LOG10E);return t=r>=-4&&r<=-1?e.toPrecision(10+r):Math.abs(r)<=9?ne(e):10===r?e.toFixed(10).substr(0,12):ae(e),te(re(t.toUpperCase()))}function se(e,t){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(0|e)===e?e.toString(10):ie(e);case"undefined":return"";case"object":if(null==e)return"";if(e instanceof Date)return Ue(14,ee(e,t&&t.date1904),t)}throw new Error("unsupported value in General format: "+e)}function oe(e,t){t[0]-=581;var r=e.getDay();return e<60&&(r=(r+6)%7),r}function fe(e,t,r,n){var a,i="",s=0,o=0,f=r.y,l=0;switch(e){case 98:f=r.y+543;case 121:switch(t.length){case 1:case 2:a=f%100,l=2;break;default:a=f%1e4,l=4;break}break;case 109:switch(t.length){case 1:case 2:a=r.m,l=t.length;break;case 3:return V[r.m-1][1];case 5:return V[r.m-1][0];default:return V[r.m-1][2]}break;case 100:switch(t.length){case 1:case 2:a=r.d,l=t.length;break;case 3:return G[r.q][0];default:return G[r.q][1]}break;case 104:switch(t.length){case 1:case 2:a=1+(r.H+11)%12,l=t.length;break;default:throw"bad hour format: "+t}break;case 72:switch(t.length){case 1:case 2:a=r.H,l=t.length;break;default:throw"bad hour format: "+t}break;case 77:switch(t.length){case 1:case 2:a=r.M,l=t.length;break;default:throw"bad minute format: "+t}break;case 115:if("s"!=t&&"ss"!=t&&".0"!=t&&".00"!=t&&".000"!=t)throw"bad second format: "+t;return 0!==r.u||"s"!=t&&"ss"!=t?(o=n>=2?3===n?1e3:100:1===n?10:1,s=Math.round(o*(r.S+r.u)),s>=60*o&&(s=0),"s"===t?0===s?"0":""+s/o:(i=P(s,2+n),"ss"===t?i.substr(0,2):"."+i.substr(2,t.length-1))):P(r.S,t.length);case 90:switch(t){case"[h]":case"[hh]":a=24*r.D+r.H;break;case"[m]":case"[mm]":a=60*(24*r.D+r.H)+r.M;break;case"[s]":case"[ss]":a=60*(60*(24*r.D+r.H)+r.M)+Math.round(r.S+r.u);break;default:throw"bad abstime format: "+t}l=3===t.length?1:2;break;case 101:a=f,l=1;break}var c=l>0?P(a,l):"";return c}function le(e){var t=3;if(e.length<=t)return e;for(var r=e.length%t,n=e.substr(0,r);r!=e.length;r+=t)n+=(n.length>0?",":"")+e.substr(r,t);return n}var ce=/%/g;function he(e,t,r){var n=t.replace(ce,""),a=t.length-n.length;return ke(e,n,r*Math.pow(10,2*a))+pt("%",a)}function ue(e,t,r){var n=t.length-1;while(44===t.charCodeAt(n-1))--n;return ke(e,t.substr(0,n),r/Math.pow(10,3*(t.length-n)))}function de(e,t){var r,n=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(0==t)return"0.0E+0";if(t<0)return"-"+de(e,-t);var a=e.indexOf(".");-1===a&&(a=e.indexOf("E"));var i=Math.floor(Math.log(t)*Math.LOG10E)%a;if(i<0&&(i+=a),r=(t/Math.pow(10,i)).toPrecision(n+1+(a+i)%a),-1===r.indexOf("e")){var s=Math.floor(Math.log(t)*Math.LOG10E);-1===r.indexOf(".")?r=r.charAt(0)+"."+r.substr(1)+"E+"+(s-r.length+i):r+="E+"+(s-i);while("0."===r.substr(0,2))r=r.charAt(0)+r.substr(2,a)+"."+r.substr(2+a),r=r.replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,(function(e,t,r,n){return t+r+n.substr(0,(a+i)%a)+"."+n.substr(i)+"E"}))}else r=t.toExponential(n);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}var pe=/# (\?+)( ?)\/( ?)(\d+)/;function me(e,t,r){var n=parseInt(e[4],10),a=Math.round(t*n),i=Math.floor(a/n),s=a-i*n,o=n;return r+(0===i?"":""+i)+" "+(0===s?pt(" ",e[1].length+1+e[4].length):L(s,e[1].length)+e[2]+"/"+e[3]+P(o,e[4].length))}function ge(e,t,r){return r+(0===t?"":""+t)+pt(" ",e[1].length+2+e[4].length)}var ve=/^#*0*\.([0#]+)/,Te=/\).*[0#]/,be=/\(###\) ###\\?-####/;function we(e){for(var t,r="",n=0;n!=e.length;++n)switch(t=e.charCodeAt(n)){case 35:break;case 63:r+=" ";break;case 48:r+="0";break;default:r+=String.fromCharCode(t)}return r}function Ee(e,t){var r=Math.pow(10,t);return""+Math.round(e*r)/r}function Ae(e,t){var r=e-Math.floor(e),n=Math.pow(10,t);return t<(""+Math.round(r*n)).length?0:Math.round(r*n)}function ye(e,t){return t<(""+Math.round((e-Math.floor(e))*Math.pow(10,t))).length?1:0}function Se(e){return e<2147483647&&e>-2147483648?""+(e>=0?0|e:e-1|0):""+Math.floor(e)}function _e(e,t,r){if(40===e.charCodeAt(0)&&!t.match(Te)){var n=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?_e("n",n,r):"("+_e("n",n,-r)+")"}if(44===t.charCodeAt(t.length-1))return ue(e,t,r);if(-1!==t.indexOf("%"))return he(e,t,r);if(-1!==t.indexOf("E"))return de(t,r);if(36===t.charCodeAt(0))return"$"+_e(e,t.substr(" "==t.charAt(1)?2:1),r);var a,i,s,o,f=Math.abs(r),l=r<0?"-":"";if(t.match(/^00+$/))return l+W(f,t.length);if(t.match(/^[#?]+$/))return a=W(r,0),"0"===a&&(a=""),a.length>t.length?a:we(t.substr(0,t.length-a.length))+a;if(i=t.match(pe))return me(i,f,l);if(t.match(/^#+0+$/))return l+W(f,t.length-t.indexOf("0"));if(i=t.match(ve))return a=Ee(r,i[1].length).replace(/^([^\.]+)$/,"$1."+we(i[1])).replace(/\.$/,"."+we(i[1])).replace(/\.(\d*)$/,(function(e,t){return"."+t+pt("0",we(i[1]).length-t.length)})),-1!==t.indexOf("0.")?a:a.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),i=t.match(/^(0*)\.(#*)$/))return l+Ee(f,i[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,i[1].length?"0.":".");if(i=t.match(/^#{1,3},##0(\.?)$/))return l+le(W(f,0));if(i=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+_e(e,t,-r):le(""+(Math.floor(r)+ye(r,i[1].length)))+"."+P(Ae(r,i[1].length),i[1].length);if(i=t.match(/^#,#*,#0/))return _e(e,t.replace(/^#,#*,/,""),r);if(i=t.match(/^([0#]+)(\\?-([0#]+))+$/))return a=D(_e(e,t.replace(/[\\-]/g,""),r)),s=0,D(D(t.replace(/\\/g,"")).replace(/[0#]/g,(function(e){return s<a.length?a.charAt(s++):"0"===e?"0":""})));if(t.match(be))return a=_e(e,"##########",r),"("+a.substr(0,3)+") "+a.substr(3,3)+"-"+a.substr(6);var c="";if(i=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(i[4].length,7),o=K(f,Math.pow(10,s)-1,!1),a=""+l,c=ke("n",i[1],o[1])," "==c.charAt(c.length-1)&&(c=c.substr(0,c.length-1)+"0"),a+=c+i[2]+"/"+i[3],c=M(o[2],s),c.length<i[4].length&&(c=we(i[4].substr(i[4].length-c.length))+c),a+=c,a;if(i=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(Math.max(i[1].length,i[4].length),7),o=K(f,Math.pow(10,s)-1,!0),l+(o[0]||(o[1]?"":"0"))+" "+(o[1]?L(o[1],s)+i[2]+"/"+i[3]+M(o[2],s):pt(" ",2*s+1+i[2].length+i[3].length));if(i=t.match(/^[#0?]+$/))return a=W(r,0),t.length<=a.length?a:we(t.substr(0,t.length-a.length))+a;if(i=t.match(/^([#0?]+)\.([#0]+)$/)){a=""+r.toFixed(Math.min(i[2].length,10)).replace(/([^0])0+$/,"$1"),s=a.indexOf(".");var h=t.indexOf(".")-s,u=t.length-a.length-h;return we(t.substr(0,h)+a+t.substr(t.length-u))}if(i=t.match(/^00,000\.([#0]*0)$/))return s=Ae(r,i[1].length),r<0?"-"+_e(e,t,-r):le(Se(r)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,(function(e){return"00,"+(e.length<3?P(0,3-e.length):"")+e}))+"."+P(s,i[1].length);switch(t){case"###,##0.00":return _e(e,"#,##0.00",r);case"###,###":case"##,###":case"#,###":var d=le(W(f,0));return"0"!==d?l+d:"";case"###,###.00":return _e(e,"###,##0.00",r).replace(/^0\./,".");case"#,###.00":return _e(e,"#,##0.00",r).replace(/^0\./,".");default:}throw new Error("unsupported format |"+t+"|")}function xe(e,t,r){var n=t.length-1;while(44===t.charCodeAt(n-1))--n;return ke(e,t.substr(0,n),r/Math.pow(10,3*(t.length-n)))}function Oe(e,t,r){var n=t.replace(ce,""),a=t.length-n.length;return ke(e,n,r*Math.pow(10,2*a))+pt("%",a)}function Ce(e,t){var r,n=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(0==t)return"0.0E+0";if(t<0)return"-"+Ce(e,-t);var a=e.indexOf(".");-1===a&&(a=e.indexOf("E"));var i=Math.floor(Math.log(t)*Math.LOG10E)%a;if(i<0&&(i+=a),r=(t/Math.pow(10,i)).toPrecision(n+1+(a+i)%a),!r.match(/[Ee]/)){var s=Math.floor(Math.log(t)*Math.LOG10E);-1===r.indexOf(".")?r=r.charAt(0)+"."+r.substr(1)+"E+"+(s-r.length+i):r+="E+"+(s-i),r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,(function(e,t,r,n){return t+r+n.substr(0,(a+i)%a)+"."+n.substr(i)+"E"}))}else r=t.toExponential(n);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}function Re(e,t,r){if(40===e.charCodeAt(0)&&!t.match(Te)){var n=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?Re("n",n,r):"("+Re("n",n,-r)+")"}if(44===t.charCodeAt(t.length-1))return xe(e,t,r);if(-1!==t.indexOf("%"))return Oe(e,t,r);if(-1!==t.indexOf("E"))return Ce(t,r);if(36===t.charCodeAt(0))return"$"+Re(e,t.substr(" "==t.charAt(1)?2:1),r);var a,i,s,o,f=Math.abs(r),l=r<0?"-":"";if(t.match(/^00+$/))return l+P(f,t.length);if(t.match(/^[#?]+$/))return a=""+r,0===r&&(a=""),a.length>t.length?a:we(t.substr(0,t.length-a.length))+a;if(i=t.match(pe))return ge(i,f,l);if(t.match(/^#+0+$/))return l+P(f,t.length-t.indexOf("0"));if(i=t.match(ve))return a=(""+r).replace(/^([^\.]+)$/,"$1."+we(i[1])).replace(/\.$/,"."+we(i[1])),a=a.replace(/\.(\d*)$/,(function(e,t){return"."+t+pt("0",we(i[1]).length-t.length)})),-1!==t.indexOf("0.")?a:a.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),i=t.match(/^(0*)\.(#*)$/))return l+(""+f).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,i[1].length?"0.":".");if(i=t.match(/^#{1,3},##0(\.?)$/))return l+le(""+f);if(i=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+Re(e,t,-r):le(""+r)+"."+pt("0",i[1].length);if(i=t.match(/^#,#*,#0/))return Re(e,t.replace(/^#,#*,/,""),r);if(i=t.match(/^([0#]+)(\\?-([0#]+))+$/))return a=D(Re(e,t.replace(/[\\-]/g,""),r)),s=0,D(D(t.replace(/\\/g,"")).replace(/[0#]/g,(function(e){return s<a.length?a.charAt(s++):"0"===e?"0":""})));if(t.match(be))return a=Re(e,"##########",r),"("+a.substr(0,3)+") "+a.substr(3,3)+"-"+a.substr(6);var c="";if(i=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(i[4].length,7),o=K(f,Math.pow(10,s)-1,!1),a=""+l,c=ke("n",i[1],o[1])," "==c.charAt(c.length-1)&&(c=c.substr(0,c.length-1)+"0"),a+=c+i[2]+"/"+i[3],c=M(o[2],s),c.length<i[4].length&&(c=we(i[4].substr(i[4].length-c.length))+c),a+=c,a;if(i=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(Math.max(i[1].length,i[4].length),7),o=K(f,Math.pow(10,s)-1,!0),l+(o[0]||(o[1]?"":"0"))+" "+(o[1]?L(o[1],s)+i[2]+"/"+i[3]+M(o[2],s):pt(" ",2*s+1+i[2].length+i[3].length));if(i=t.match(/^[#0?]+$/))return a=""+r,t.length<=a.length?a:we(t.substr(0,t.length-a.length))+a;if(i=t.match(/^([#0]+)\.([#0]+)$/)){a=""+r.toFixed(Math.min(i[2].length,10)).replace(/([^0])0+$/,"$1"),s=a.indexOf(".");var h=t.indexOf(".")-s,u=t.length-a.length-h;return we(t.substr(0,h)+a+t.substr(t.length-u))}if(i=t.match(/^00,000\.([#0]*0)$/))return r<0?"-"+Re(e,t,-r):le(""+r).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,(function(e){return"00,"+(e.length<3?P(0,3-e.length):"")+e}))+"."+P(0,i[1].length);switch(t){case"###,###":case"##,###":case"#,###":var d=le(""+f);return"0"!==d?l+d:"";default:if(t.match(/\.[0#?]*$/))return Re(e,t.slice(0,t.lastIndexOf(".")),r)+we(t.slice(t.lastIndexOf(".")))}throw new Error("unsupported format |"+t+"|")}function ke(e,t,r){return(0|r)===r?Re(e,t,r):_e(e,t,r)}function Ie(e){for(var t=[],r=!1,n=0,a=0;n<e.length;++n)switch(e.charCodeAt(n)){case 34:r=!r;break;case 95:case 42:case 92:++n;break;case 59:t[t.length]=e.substr(a,n-a),a=n+1}if(t[t.length]=e.substr(a),!0===r)throw new Error("Format |"+e+"| unterminated string ");return t}var Ne=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function De(e){var t=0,r="",n="";while(t<e.length)switch(r=e.charAt(t)){case"G":H(e,t)&&(t+=6),t++;break;case'"':for(;34!==e.charCodeAt(++t)&&t<e.length;);++t;break;case"\\":t+=2;break;case"_":t+=2;break;case"@":++t;break;case"B":case"b":if("1"===e.charAt(t+1)||"2"===e.charAt(t+1))return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"上":if("A/P"===e.substr(t,3).toUpperCase())return!0;if("AM/PM"===e.substr(t,5).toUpperCase())return!0;if("上午/下午"===e.substr(t,5).toUpperCase())return!0;++t;break;case"[":n=r;while("]"!==e.charAt(t++)&&t<e.length)n+=e.charAt(t);if(n.match(Ne))return!0;break;case".":case"0":case"#":while(t<e.length&&("0#?.,E+-%".indexOf(r=e.charAt(++t))>-1||"\\"==r&&"-"==e.charAt(t+1)&&"0#".indexOf(e.charAt(t+2))>-1));break;case"?":while(e.charAt(++t)===r);break;case"*":++t," "!=e.charAt(t)&&"*"!=e.charAt(t)||++t;break;case"(":case")":++t;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":while(t<e.length&&"0123456789".indexOf(e.charAt(++t))>-1);break;case" ":++t;break;default:++t;break}return!1}function Pe(e,t,r,n){var a,i,s,o=[],f="",l=0,c="",h="t",u="H";while(l<e.length)switch(c=e.charAt(l)){case"G":if(!H(e,l))throw new Error("unrecognized character "+c+" in "+e);o[o.length]={t:"G",v:"General"},l+=7;break;case'"':for(f="";34!==(s=e.charCodeAt(++l))&&l<e.length;)f+=String.fromCharCode(s);o[o.length]={t:"t",v:f},++l;break;case"\\":var d=e.charAt(++l),p="("===d||")"===d?d:"t";o[o.length]={t:p,v:d},++l;break;case"_":o[o.length]={t:"t",v:" "},l+=2;break;case"@":o[o.length]={t:"T",v:t},++l;break;case"B":case"b":if("1"===e.charAt(l+1)||"2"===e.charAt(l+1)){if(null==a&&(a=J(t,r,"2"===e.charAt(l+1)),null==a))return"";o[o.length]={t:"X",v:e.substr(l,2)},h=c,l+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":c=c.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(t<0)return"";if(null==a&&(a=J(t,r),null==a))return"";f=c;while(++l<e.length&&e.charAt(l).toLowerCase()===c)f+=c;"m"===c&&"h"===h.toLowerCase()&&(c="M"),"h"===c&&(c=u),o[o.length]={t:c,v:f},h=c;break;case"A":case"a":case"上":var m={t:c,v:c};if(null==a&&(a=J(t,r)),"A/P"===e.substr(l,3).toUpperCase()?(null!=a&&(m.v=a.H>=12?"P":"A"),m.t="T",u="h",l+=3):"AM/PM"===e.substr(l,5).toUpperCase()?(null!=a&&(m.v=a.H>=12?"PM":"AM"),m.t="T",l+=5,u="h"):"上午/下午"===e.substr(l,5).toUpperCase()?(null!=a&&(m.v=a.H>=12?"下午":"上午"),m.t="T",l+=5,u="h"):(m.t="t",++l),null==a&&"T"===m.t)return"";o[o.length]=m,h=c;break;case"[":f=c;while("]"!==e.charAt(l++)&&l<e.length)f+=e.charAt(l);if("]"!==f.slice(-1))throw'unterminated "[" block: |'+f+"|";if(f.match(Ne)){if(null==a&&(a=J(t,r),null==a))return"";o[o.length]={t:"Z",v:f.toLowerCase()},h=f.charAt(1)}else f.indexOf("$")>-1&&(f=(f.match(/\$([^-\[\]]*)/)||[])[1]||"$",De(e)||(o[o.length]={t:"t",v:f}));break;case".":if(null!=a){f=c;while(++l<e.length&&"0"===(c=e.charAt(l)))f+=c;o[o.length]={t:"s",v:f};break}case"0":case"#":f=c;while(++l<e.length&&"0#?.,E+-%".indexOf(c=e.charAt(l))>-1)f+=c;o[o.length]={t:"n",v:f};break;case"?":f=c;while(e.charAt(++l)===c)f+=c;o[o.length]={t:c,v:f},h=c;break;case"*":++l," "!=e.charAt(l)&&"*"!=e.charAt(l)||++l;break;case"(":case")":o[o.length]={t:1===n?"t":c,v:c},++l;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":f=c;while(l<e.length&&"0123456789".indexOf(e.charAt(++l))>-1)f+=e.charAt(l);o[o.length]={t:"D",v:f};break;case" ":o[o.length]={t:c,v:c},++l;break;case"$":o[o.length]={t:"t",v:"$"},++l;break;default:if(-1===",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(c))throw new Error("unrecognized character "+c+" in "+e);o[o.length]={t:"t",v:c},++l;break}var g,v=0,T=0;for(l=o.length-1,h="t";l>=0;--l)switch(o[l].t){case"h":case"H":o[l].t=u,h="h",v<1&&(v=1);break;case"s":(g=o[l].v.match(/\.0+$/))&&(T=Math.max(T,g[0].length-1)),v<3&&(v=3);case"d":case"y":case"M":case"e":h=o[l].t;break;case"m":"s"===h&&(o[l].t="M",v<2&&(v=2));break;case"X":break;case"Z":v<1&&o[l].v.match(/[Hh]/)&&(v=1),v<2&&o[l].v.match(/[Mm]/)&&(v=2),v<3&&o[l].v.match(/[Ss]/)&&(v=3)}switch(v){case 0:break;case 1:a.u>=.5&&(a.u=0,++a.S),a.S>=60&&(a.S=0,++a.M),a.M>=60&&(a.M=0,++a.H);break;case 2:a.u>=.5&&(a.u=0,++a.S),a.S>=60&&(a.S=0,++a.M);break}var b,w="";for(l=0;l<o.length;++l)switch(o[l].t){case"t":case"T":case" ":case"D":break;case"X":o[l].v="",o[l].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":o[l].v=fe(o[l].t.charCodeAt(0),o[l].v,a,T),o[l].t="t";break;case"n":case"?":b=l+1;while(null!=o[b]&&("?"===(c=o[b].t)||"D"===c||(" "===c||"t"===c)&&null!=o[b+1]&&("?"===o[b+1].t||"t"===o[b+1].t&&"/"===o[b+1].v)||"("===o[l].t&&(" "===c||"n"===c||")"===c)||"t"===c&&("/"===o[b].v||" "===o[b].v&&null!=o[b+1]&&"?"==o[b+1].t)))o[l].v+=o[b].v,o[b]={v:"",t:";"},++b;w+=o[l].v,l=b-1;break;case"G":o[l].t="t",o[l].v=se(t,r);break}var E,A,y="";if(w.length>0){40==w.charCodeAt(0)?(E=t<0&&45===w.charCodeAt(0)?-t:t,A=ke("n",w,E)):(E=t<0&&n>1?-t:t,A=ke("n",w,E),E<0&&o[0]&&"t"==o[0].t&&(A=A.substr(1),o[0].v="-"+o[0].v)),b=A.length-1;var S=o.length;for(l=0;l<o.length;++l)if(null!=o[l]&&"t"!=o[l].t&&o[l].v.indexOf(".")>-1){S=l;break}var _=o.length;if(S===o.length&&-1===A.indexOf("E")){for(l=o.length-1;l>=0;--l)null!=o[l]&&-1!=="n?".indexOf(o[l].t)&&(b>=o[l].v.length-1?(b-=o[l].v.length,o[l].v=A.substr(b+1,o[l].v.length)):b<0?o[l].v="":(o[l].v=A.substr(0,b+1),b=-1),o[l].t="t",_=l);b>=0&&_<o.length&&(o[_].v=A.substr(0,b+1)+o[_].v)}else if(S!==o.length&&-1===A.indexOf("E")){for(b=A.indexOf(".")-1,l=S;l>=0;--l)if(null!=o[l]&&-1!=="n?".indexOf(o[l].t)){for(i=o[l].v.indexOf(".")>-1&&l===S?o[l].v.indexOf(".")-1:o[l].v.length-1,y=o[l].v.substr(i+1);i>=0;--i)b>=0&&("0"===o[l].v.charAt(i)||"#"===o[l].v.charAt(i))&&(y=A.charAt(b--)+y);o[l].v=y,o[l].t="t",_=l}for(b>=0&&_<o.length&&(o[_].v=A.substr(0,b+1)+o[_].v),b=A.indexOf(".")+1,l=S;l<o.length;++l)if(null!=o[l]&&(-1!=="n?(".indexOf(o[l].t)||l===S)){for(i=o[l].v.indexOf(".")>-1&&l===S?o[l].v.indexOf(".")+1:0,y=o[l].v.substr(0,i);i<o[l].v.length;++i)b<A.length&&(y+=A.charAt(b++));o[l].v=y,o[l].t="t",_=l}}}for(l=0;l<o.length;++l)null!=o[l]&&"n?".indexOf(o[l].t)>-1&&(E=n>1&&t<0&&l>0&&"-"===o[l-1].v?-t:t,o[l].v=ke(o[l].t,o[l].v,E),o[l].t="t");var x="";for(l=0;l!==o.length;++l)null!=o[l]&&(x+=o[l].v);return x}var Le=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function Me(e,t){if(null==t)return!1;var r=parseFloat(t[2]);switch(t[1]){case"=":if(e==r)return!0;break;case">":if(e>r)return!0;break;case"<":if(e<r)return!0;break;case"<>":if(e!=r)return!0;break;case">=":if(e>=r)return!0;break;case"<=":if(e<=r)return!0;break}return!1}function Fe(e,t){var r=Ie(e),n=r.length,a=r[n-1].indexOf("@");if(n<4&&a>-1&&--n,r.length>4)throw new Error("cannot find right format for |"+r.join("|")+"|");if("number"!==typeof t)return[4,4===r.length||a>-1?r[r.length-1]:"@"];switch(r.length){case 1:r=a>-1?["General","General","General",r[0]]:[r[0],r[0],r[0],"@"];break;case 2:r=a>-1?[r[0],r[0],r[0],r[1]]:[r[0],r[1],r[0],"@"];break;case 3:r=a>-1?[r[0],r[1],r[0],r[2]]:[r[0],r[1],r[2],"@"];break;case 4:break}var i=t>0?r[0]:t<0?r[1]:r[2];if(-1===r[0].indexOf("[")&&-1===r[1].indexOf("["))return[n,i];if(null!=r[0].match(/\[[=<>]/)||null!=r[1].match(/\[[=<>]/)){var s=r[0].match(Le),o=r[1].match(Le);return Me(t,s)?[n,r[0]]:Me(t,o)?[n,r[1]]:[n,r[null!=s&&null!=o?2:1]]}return[n,i]}function Ue(e,t,r){null==r&&(r={});var n="";switch(typeof e){case"string":n="m/d/yy"==e&&r.dateNF?r.dateNF:e;break;case"number":n=14==e&&r.dateNF?r.dateNF:(null!=r.table?r.table:z)[e],null==n&&(n=r.table&&r.table[Y[e]]||z[Y[e]]),null==n&&(n=X[e]||"General");break}if(H(n,0))return se(t,r);t instanceof Date&&(t=ee(t,r.date1904));var a=Fe(n,t);if(H(a[1]))return se(t,r);if(!0===t)t="TRUE";else if(!1===t)t="FALSE";else if(""===t||null==t)return"";return Pe(a[1],t,r,a[0])}function Be(e,t){if("number"!=typeof t){t=+t||-1;for(var r=0;r<392;++r)if(void 0!=z[r]){if(z[r]==e){t=r;break}}else t<0&&(t=r);t<0&&(t=391)}return z[t]=e,t}function We(e){for(var t=0;392!=t;++t)void 0!==e[t]&&Be(e[t],t)}function He(){z=j()}var Ge={format:Ue,load:Be,_table:z,load_table:We,parse_date_code:J,is_date:De,get_table:function(){return Ge._table=z}},Ve=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g;function je(e){var t="number"==typeof e?z[e]:e;return t=t.replace(Ve,"(\\d+)"),new RegExp("^"+t+"$")}function ze(e,t,r){var n=-1,a=-1,i=-1,s=-1,o=-1,f=-1;(t.match(Ve)||[]).forEach((function(e,t){var l=parseInt(r[t+1],10);switch(e.toLowerCase().charAt(0)){case"y":n=l;break;case"d":i=l;break;case"h":s=l;break;case"s":f=l;break;case"m":s>=0?o=l:a=l;break}})),f>=0&&-1==o&&a>=0&&(o=a,a=-1);var l=(""+(n>=0?n:(new Date).getFullYear())).slice(-4)+"-"+("00"+(a>=1?a:1)).slice(-2)+"-"+("00"+(i>=1?i:1)).slice(-2);7==l.length&&(l="0"+l),8==l.length&&(l="20"+l);var c=("00"+(s>=0?s:0)).slice(-2)+":"+("00"+(o>=0?o:0)).slice(-2)+":"+("00"+(f>=0?f:0)).slice(-2);return-1==s&&-1==o&&-1==f?l:-1==n&&-1==a&&-1==i?c:l+"T"+c}var $e=function(){var e={};function t(){for(var e=0,t=new Array(256),r=0;256!=r;++r)e=r,e=1&e?-306674912^e>>>1:e>>>1,e=1&e?-306674912^e>>>1:e>>>1,e=1&e?-306674912^e>>>1:e>>>1,e=1&e?-306674912^e>>>1:e>>>1,e=1&e?-306674912^e>>>1:e>>>1,e=1&e?-306674912^e>>>1:e>>>1,e=1&e?-306674912^e>>>1:e>>>1,e=1&e?-306674912^e>>>1:e>>>1,t[r]=e;return"undefined"!==typeof Int32Array?new Int32Array(t):t}e.version="1.2.0";var r=t();function n(e){var t=0,r=0,n=0,a="undefined"!==typeof Int32Array?new Int32Array(4096):new Array(4096);for(n=0;256!=n;++n)a[n]=e[n];for(n=0;256!=n;++n)for(r=e[n],t=256+n;t<4096;t+=256)r=a[t]=r>>>8^e[255&r];var i=[];for(n=1;16!=n;++n)i[n-1]="undefined"!==typeof Int32Array?a.subarray(256*n,256*n+256):a.slice(256*n,256*n+256);return i}var a=n(r),i=a[0],s=a[1],o=a[2],f=a[3],l=a[4],c=a[5],h=a[6],u=a[7],d=a[8],p=a[9],m=a[10],g=a[11],v=a[12],T=a[13],b=a[14];function w(e,t){for(var n=-1^t,a=0,i=e.length;a<i;)n=n>>>8^r[255&(n^e.charCodeAt(a++))];return~n}function E(e,t){for(var n=-1^t,a=e.length-15,w=0;w<a;)n=b[e[w++]^255&n]^T[e[w++]^n>>8&255]^v[e[w++]^n>>16&255]^g[e[w++]^n>>>24]^m[e[w++]]^p[e[w++]]^d[e[w++]]^u[e[w++]]^h[e[w++]]^c[e[w++]]^l[e[w++]]^f[e[w++]]^o[e[w++]]^s[e[w++]]^i[e[w++]]^r[e[w++]];a+=15;while(w<a)n=n>>>8^r[255&(n^e[w++])];return~n}function A(e,t){for(var n=-1^t,a=0,i=e.length,s=0,o=0;a<i;)s=e.charCodeAt(a++),s<128?n=n>>>8^r[255&(n^s)]:s<2048?(n=n>>>8^r[255&(n^(192|s>>6&31))],n=n>>>8^r[255&(n^(128|63&s))]):s>=55296&&s<57344?(s=64+(1023&s),o=1023&e.charCodeAt(a++),n=n>>>8^r[255&(n^(240|s>>8&7))],n=n>>>8^r[255&(n^(128|s>>2&63))],n=n>>>8^r[255&(n^(128|o>>6&15|(3&s)<<4))],n=n>>>8^r[255&(n^(128|63&o))]):(n=n>>>8^r[255&(n^(224|s>>12&15))],n=n>>>8^r[255&(n^(128|s>>6&63))],n=n>>>8^r[255&(n^(128|63&s))]);return~n}return e.table=r,e.bstr=w,e.buf=E,e.str=A,e}(),Ye=function(){var e,t={};function r(e,t){for(var r=e.split("/"),n=t.split("/"),a=0,i=0,s=Math.min(r.length,n.length);a<s;++a){if(i=r[a].length-n[a].length)return i;if(r[a]!=n[a])return r[a]<n[a]?-1:1}return r.length-n.length}function n(e){if("/"==e.charAt(e.length-1))return-1===e.slice(0,-1).indexOf("/")?e:n(e.slice(0,-1));var t=e.lastIndexOf("/");return-1===t?e:e.slice(0,t+1)}function a(e){if("/"==e.charAt(e.length-1))return a(e.slice(0,-1));var t=e.lastIndexOf("/");return-1===t?e:e.slice(t+1)}function i(e,t){"string"===typeof t&&(t=new Date(t));var r=t.getHours();r=r<<6|t.getMinutes(),r=r<<5|t.getSeconds()>>>1,e.write_shift(2,r);var n=t.getFullYear()-1980;n=n<<4|t.getMonth()+1,n=n<<5|t.getDate(),e.write_shift(2,n)}function s(e){var t=65535&e.read_shift(2),r=65535&e.read_shift(2),n=new Date,a=31&r;r>>>=5;var i=15&r;r>>>=4,n.setMilliseconds(0),n.setFullYear(r+1980),n.setMonth(i-1),n.setDate(a);var s=31&t;t>>>=5;var o=63&t;return t>>>=6,n.setHours(t),n.setMinutes(o),n.setSeconds(s<<1),n}function o(e){kr(e,0);var t={},r=0;while(e.l<=e.length-4){var n=e.read_shift(2),a=e.read_shift(2),i=e.l+a,s={};switch(n){case 21589:r=e.read_shift(1),1&r&&(s.mtime=e.read_shift(4)),a>5&&(2&r&&(s.atime=e.read_shift(4)),4&r&&(s.ctime=e.read_shift(4))),s.mtime&&(s.mt=new Date(1e3*s.mtime));break}e.l=i,t[n]=s}return t}function f(){return e||(e={})}function l(e,t){if(80==e[0]&&75==e[1])return Ie(e,t);if(109==(32|e[0])&&105==(32|e[1]))return We(e,t);if(e.length<512)throw new Error("CFB file size "+e.length+" < 512");var r=3,n=512,a=0,i=0,s=0,o=0,f=0,l=[],p=e.slice(0,512);kr(p,0);var g=c(p);switch(r=g[0],r){case 3:n=512;break;case 4:n=4096;break;case 0:if(0==g[1])return Ie(e,t);default:throw new Error("Major Version: Expected 3 or 4 saw "+r)}512!==n&&(p=e.slice(0,n),kr(p,28));var b=e.slice(0,n);h(p,r);var w=p.read_shift(4,"i");if(3===r&&0!==w)throw new Error("# Directory Sectors: Expected 0 saw "+w);p.l+=4,s=p.read_shift(4,"i"),p.l+=4,p.chk("00100000","Mini Stream Cutoff Size: "),o=p.read_shift(4,"i"),a=p.read_shift(4,"i"),f=p.read_shift(4,"i"),i=p.read_shift(4,"i");for(var E=-1,A=0;A<109;++A){if(E=p.read_shift(4,"i"),E<0)break;l[A]=E}var y=u(e,n);m(f,i,y,n,l);var S=v(y,s,l,n);S[s].name="!Directory",a>0&&o!==B&&(S[o].name="!MiniFAT"),S[l[0]].name="!FAT",S.fat_addrs=l,S.ssz=n;var _={},x=[],O=[],C=[];T(s,S,y,x,a,_,O,o),d(O,C,x),x.shift();var R={FileIndex:O,FullPaths:C};return t&&t.raw&&(R.raw={header:b,sectors:y}),R}function c(e){if(80==e[e.l]&&75==e[e.l+1])return[0,0];e.chk(W,"Header Signature: "),e.l+=16;var t=e.read_shift(2,"u");return[e.read_shift(2,"u"),t]}function h(e,t){var r=9;switch(e.l+=2,r=e.read_shift(2)){case 9:if(3!=t)throw new Error("Sector Shift: Expected 9 saw "+r);break;case 12:if(4!=t)throw new Error("Sector Shift: Expected 12 saw "+r);break;default:throw new Error("Sector Shift: Expected 9 or 12 saw "+r)}e.chk("0600","Mini Sector Shift: "),e.chk("000000000000","Reserved: ")}function u(e,t){for(var r=Math.ceil(e.length/t)-1,n=[],a=1;a<r;++a)n[a-1]=e.slice(a*t,(a+1)*t);return n[r-1]=e.slice(r*t),n}function d(e,t,r){for(var n=0,a=0,i=0,s=0,o=0,f=r.length,l=[],c=[];n<f;++n)l[n]=c[n]=n,t[n]=r[n];for(;o<c.length;++o)n=c[o],a=e[n].L,i=e[n].R,s=e[n].C,l[n]===n&&(-1!==a&&l[a]!==a&&(l[n]=l[a]),-1!==i&&l[i]!==i&&(l[n]=l[i])),-1!==s&&(l[s]=n),-1!==a&&n!=l[n]&&(l[a]=l[n],c.lastIndexOf(a)<o&&c.push(a)),-1!==i&&n!=l[n]&&(l[i]=l[n],c.lastIndexOf(i)<o&&c.push(i));for(n=1;n<f;++n)l[n]===n&&(-1!==i&&l[i]!==i?l[n]=l[i]:-1!==a&&l[a]!==a&&(l[n]=l[a]));for(n=1;n<f;++n)if(0!==e[n].type){if(o=n,o!=l[o])do{o=l[o],t[n]=t[o]+"/"+t[n]}while(0!==o&&-1!==l[o]&&o!=l[o]);l[n]=-1}for(t[0]+="/",n=1;n<f;++n)2!==e[n].type&&(t[n]+="/")}function p(e,t,r){var n=e.start,a=e.size,i=[],s=n;while(r&&a>0&&s>=0)i.push(t.slice(s*U,s*U+U)),a-=U,s=Ar(r,4*s);return 0===i.length?Nr(0):R(i).slice(0,e.size)}function m(e,t,r,n,a){var i=B;if(e===B){if(0!==t)throw new Error("DIFAT chain shorter than expected")}else if(-1!==e){var s=r[e],o=(n>>>2)-1;if(!s)return;for(var f=0;f<o;++f){if((i=Ar(s,4*f))===B)break;a.push(i)}m(Ar(s,n-4),t-1,r,n,a)}}function g(e,t,r,n,a){var i=[],s=[];a||(a=[]);var o=n-1,f=0,l=0;for(f=t;f>=0;){a[f]=!0,i[i.length]=f,s.push(e[f]);var c=r[Math.floor(4*f/n)];if(l=4*f&o,n<4+l)throw new Error("FAT boundary crossed: "+f+" 4 "+n);if(!e[c])break;f=Ar(e[c],l)}return{nodes:i,data:Zt([s])}}function v(e,t,r,n){var a=e.length,i=[],s=[],o=[],f=[],l=n-1,c=0,h=0,u=0,d=0;for(c=0;c<a;++c)if(o=[],u=c+t,u>=a&&(u-=a),!s[u]){f=[];var p=[];for(h=u;h>=0;){p[h]=!0,s[h]=!0,o[o.length]=h,f.push(e[h]);var m=r[Math.floor(4*h/n)];if(d=4*h&l,n<4+d)throw new Error("FAT boundary crossed: "+h+" 4 "+n);if(!e[m])break;if(h=Ar(e[m],d),p[h])break}i[u]={nodes:o,data:Zt([f])}}return i}function T(e,t,r,n,a,i,s,o){for(var f,l=0,c=n.length?2:0,h=t[e].data,u=0,d=0;u<h.length;u+=128){var m=h.slice(u,u+128);kr(m,64),d=m.read_shift(2),f=Qt(m,0,d-c),n.push(f);var v={name:f,type:m.read_shift(1),color:m.read_shift(1),L:m.read_shift(4,"i"),R:m.read_shift(4,"i"),C:m.read_shift(4,"i"),clsid:m.read_shift(16),state:m.read_shift(4,"i"),start:0,size:0},T=m.read_shift(2)+m.read_shift(2)+m.read_shift(2)+m.read_shift(2);0!==T&&(v.ct=x(m,m.l-8));var b=m.read_shift(2)+m.read_shift(2)+m.read_shift(2)+m.read_shift(2);0!==b&&(v.mt=x(m,m.l-8)),v.start=m.read_shift(4,"i"),v.size=m.read_shift(4,"i"),v.size<0&&v.start<0&&(v.size=v.type=0,v.start=B,v.name=""),5===v.type?(l=v.start,a>0&&l!==B&&(t[l].name="!StreamData")):v.size>=4096?(v.storage="fat",void 0===t[v.start]&&(t[v.start]=g(r,v.start,t.fat_addrs,t.ssz)),t[v.start].name=v.name,v.content=t[v.start].data.slice(0,v.size)):(v.storage="minifat",v.size<0?v.size=0:l!==B&&v.start!==B&&t[l]&&(v.content=p(v,t[l].data,(t[o]||{}).data))),v.content&&kr(v.content,0),i[f]=v,s.push(v)}}function x(e,t){return new Date(1e3*(Er(e,t+4)/1e7*Math.pow(2,32)+Er(e,t)/1e7-11644473600))}function O(t,r){return f(),l(e.readFileSync(t),r)}function C(e,t){var r=t&&t.type;switch(r||E&&Buffer.isBuffer(e)&&(r="buffer"),r||"base64"){case"file":return O(e,t);case"base64":return l(_(w(e)),t);case"binary":return l(_(e),t)}return l(e,t)}function k(e,t){var r=t||{},n=r.root||"Root Entry";if(e.FullPaths||(e.FullPaths=[]),e.FileIndex||(e.FileIndex=[]),e.FullPaths.length!==e.FileIndex.length)throw new Error("inconsistent CFB structure");0===e.FullPaths.length&&(e.FullPaths[0]=n+"/",e.FileIndex[0]={name:n,type:5}),r.CLSID&&(e.FileIndex[0].clsid=r.CLSID),D(e)}function D(e){var t="Sh33tJ5";if(!Ye.find(e,"/"+t)){var r=Nr(4);r[0]=55,r[1]=r[3]=50,r[2]=54,e.FileIndex.push({name:t,type:2,content:r,size:4,L:69,R:69,C:69}),e.FullPaths.push(e.FullPaths[0]+t),P(e)}}function P(e,t){k(e);for(var i=!1,s=!1,o=e.FullPaths.length-1;o>=0;--o){var f=e.FileIndex[o];switch(f.type){case 0:s?i=!0:(e.FileIndex.pop(),e.FullPaths.pop());break;case 1:case 2:case 5:s=!0,isNaN(f.R*f.L*f.C)&&(i=!0),f.R>-1&&f.L>-1&&f.R==f.L&&(i=!0);break;default:i=!0;break}}if(i||t){var l=new Date(1987,1,19),c=0,h=Object.create?Object.create(null):{},u=[];for(o=0;o<e.FullPaths.length;++o)h[e.FullPaths[o]]=!0,0!==e.FileIndex[o].type&&u.push([e.FullPaths[o],e.FileIndex[o]]);for(o=0;o<u.length;++o){var d=n(u[o][0]);s=h[d],s||(u.push([d,{name:a(d).replace("/",""),type:1,clsid:G,ct:l,mt:l,content:null}]),h[d]=!0)}for(u.sort((function(e,t){return r(e[0],t[0])})),e.FullPaths=[],e.FileIndex=[],o=0;o<u.length;++o)e.FullPaths[o]=u[o][0],e.FileIndex[o]=u[o][1];for(o=0;o<u.length;++o){var p=e.FileIndex[o],m=e.FullPaths[o];if(p.name=a(m).replace("/",""),p.L=p.R=p.C=-(p.color=1),p.size=p.content?p.content.length:0,p.start=0,p.clsid=p.clsid||G,0===o)p.C=u.length>1?1:-1,p.size=0,p.type=5;else if("/"==m.slice(-1)){for(c=o+1;c<u.length;++c)if(n(e.FullPaths[c])==m)break;for(p.C=c>=u.length?-1:c,c=o+1;c<u.length;++c)if(n(e.FullPaths[c])==n(m))break;p.R=c>=u.length?-1:c,p.type=1}else n(e.FullPaths[o+1]||"")==n(m)&&(p.R=o+1),p.type=2}}}function L(e,t){var r=t||{};if("mad"==r.fileType)return He(e,r);switch(P(e),r.fileType){case"zip":return De(e,r)}var n=function(e){for(var t=0,r=0,n=0;n<e.FileIndex.length;++n){var a=e.FileIndex[n];if(a.content){var i=a.content.length;i>0&&(i<4096?t+=i+63>>6:r+=i+511>>9)}}var s=e.FullPaths.length+3>>2,o=t+7>>3,f=t+127>>7,l=o+r+s+f,c=l+127>>7,h=c<=109?0:Math.ceil((c-109)/127);while(l+c+h+127>>7>c)h=++c<=109?0:Math.ceil((c-109)/127);var u=[1,h,c,f,s,r,t,0];return e.FileIndex[0].size=t<<6,u[7]=(e.FileIndex[0].start=u[0]+u[1]+u[2]+u[3]+u[4]+u[5])+(u[6]+7>>3),u}(e),a=Nr(n[7]<<9),i=0,s=0;for(i=0;i<8;++i)a.write_shift(1,H[i]);for(i=0;i<8;++i)a.write_shift(2,0);for(a.write_shift(2,62),a.write_shift(2,3),a.write_shift(2,65534),a.write_shift(2,9),a.write_shift(2,6),i=0;i<3;++i)a.write_shift(2,0);for(a.write_shift(4,0),a.write_shift(4,n[2]),a.write_shift(4,n[0]+n[1]+n[2]+n[3]-1),a.write_shift(4,0),a.write_shift(4,4096),a.write_shift(4,n[3]?n[0]+n[1]+n[2]-1:B),a.write_shift(4,n[3]),a.write_shift(-4,n[1]?n[0]-1:B),a.write_shift(4,n[1]),i=0;i<109;++i)a.write_shift(-4,i<n[2]?n[1]+i:-1);if(n[1])for(s=0;s<n[1];++s){for(;i<236+127*s;++i)a.write_shift(-4,i<n[2]?n[1]+i:-1);a.write_shift(-4,s===n[1]-1?B:s+1)}var o=function(e){for(s+=e;i<s-1;++i)a.write_shift(-4,i+1);e&&(++i,a.write_shift(-4,B))};for(s=i=0,s+=n[1];i<s;++i)a.write_shift(-4,V.DIFSECT);for(s+=n[2];i<s;++i)a.write_shift(-4,V.FATSECT);o(n[3]),o(n[4]);for(var f=0,l=0,c=e.FileIndex[0];f<e.FileIndex.length;++f)c=e.FileIndex[f],c.content&&(l=c.content.length,l<4096||(c.start=s,o(l+511>>9)));o(n[6]+7>>3);while(511&a.l)a.write_shift(-4,V.ENDOFCHAIN);for(s=i=0,f=0;f<e.FileIndex.length;++f)c=e.FileIndex[f],c.content&&(l=c.content.length,!l||l>=4096||(c.start=s,o(l+63>>6)));while(511&a.l)a.write_shift(-4,V.ENDOFCHAIN);for(i=0;i<n[4]<<2;++i){var h=e.FullPaths[i];if(h&&0!==h.length){c=e.FileIndex[i],0===i&&(c.start=c.size?c.start-1:B);var u=0===i&&r.root||c.name;if(l=2*(u.length+1),a.write_shift(64,u,"utf16le"),a.write_shift(2,l),a.write_shift(1,c.type),a.write_shift(1,c.color),a.write_shift(-4,c.L),a.write_shift(-4,c.R),a.write_shift(-4,c.C),c.clsid)a.write_shift(16,c.clsid,"hex");else for(f=0;f<4;++f)a.write_shift(4,0);a.write_shift(4,c.state||0),a.write_shift(4,0),a.write_shift(4,0),a.write_shift(4,0),a.write_shift(4,0),a.write_shift(4,c.start),a.write_shift(4,c.size),a.write_shift(4,0)}else{for(f=0;f<17;++f)a.write_shift(4,0);for(f=0;f<3;++f)a.write_shift(4,-1);for(f=0;f<12;++f)a.write_shift(4,0)}}for(i=1;i<e.FileIndex.length;++i)if(c=e.FileIndex[i],c.size>=4096)if(a.l=c.start+1<<9,E&&Buffer.isBuffer(c.content))c.content.copy(a,a.l,0,c.size),a.l+=c.size+511&-512;else{for(f=0;f<c.size;++f)a.write_shift(1,c.content[f]);for(;511&f;++f)a.write_shift(1,0)}for(i=1;i<e.FileIndex.length;++i)if(c=e.FileIndex[i],c.size>0&&c.size<4096)if(E&&Buffer.isBuffer(c.content))c.content.copy(a,a.l,0,c.size),a.l+=c.size+63&-64;else{for(f=0;f<c.size;++f)a.write_shift(1,c.content[f]);for(;63&f;++f)a.write_shift(1,0)}if(E)a.l=a.length;else while(a.l<a.length)a.write_shift(1,0);return a}function M(e,t){var r=e.FullPaths.map((function(e){return e.toUpperCase()})),n=r.map((function(e){var t=e.split("/");return t[t.length-("/"==e.slice(-1)?2:1)]})),a=!1;47===t.charCodeAt(0)?(a=!0,t=r[0].slice(0,-1)+t):a=-1!==t.indexOf("/");var i=t.toUpperCase(),s=!0===a?r.indexOf(i):n.indexOf(i);if(-1!==s)return e.FileIndex[s];var o=!i.match(N);for(i=i.replace(I,""),o&&(i=i.replace(N,"!")),s=0;s<r.length;++s){if((o?r[s].replace(N,"!"):r[s]).replace(I,"")==i)return e.FileIndex[s];if((o?n[s].replace(N,"!"):n[s]).replace(I,"")==i)return e.FileIndex[s]}return null}t.version="1.2.1";var F,U=64,B=-2,W="d0cf11e0a1b11ae1",H=[208,207,17,224,161,177,26,225],G="00000000000000000000000000000000",V={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:B,FREESECT:-1,HEADER_SIGNATURE:W,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:G,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function j(t,r,n){f();var a=L(t,n);e.writeFileSync(r,a)}function z(e){for(var t=new Array(e.length),r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}function $(t,r){var n=L(t,r);switch(r&&r.type||"buffer"){case"file":return f(),e.writeFileSync(r.filename,n),n;case"binary":return"string"==typeof n?n:z(n);case"base64":return b("string"==typeof n?n:z(n));case"buffer":if(E)return Buffer.isBuffer(n)?n:A(n);case"array":return"string"==typeof n?_(n):n}return n}function Y(e){try{var t=e.InflateRaw,r=new t;if(r._processChunk(new Uint8Array([3,0]),r._finishFlushFlag),!r.bytesRead)throw new Error("zlib does not expose bytesRead");F=e}catch(n){console.error("cannot use native zlib: "+(n.message||n))}}function X(e,t){if(!F)return Re(e,t);var r=F.InflateRaw,n=new r,a=n._processChunk(e.slice(e.l),n._finishFlushFlag);return e.l+=n.bytesRead,a}function K(e){return F?F.deflateRawSync(e):Ee(e)}var J=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],Z=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],q=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577];function Q(e){var t=139536&(e<<1|e<<11)|558144&(e<<5|e<<15);return 255&(t>>16|t>>8|t)}for(var ee="undefined"!==typeof Uint8Array,te=ee?new Uint8Array(256):[],re=0;re<256;++re)te[re]=Q(re);function ne(e,t){var r=te[255&e];return t<=8?r>>>8-t:(r=r<<8|te[e>>8&255],t<=16?r>>>16-t:(r=r<<8|te[e>>16&255],r>>>24-t))}function ae(e,t){var r=7&t,n=t>>>3;return(e[n]|(r<=6?0:e[n+1]<<8))>>>r&3}function ie(e,t){var r=7&t,n=t>>>3;return(e[n]|(r<=5?0:e[n+1]<<8))>>>r&7}function se(e,t){var r=7&t,n=t>>>3;return(e[n]|(r<=4?0:e[n+1]<<8))>>>r&15}function oe(e,t){var r=7&t,n=t>>>3;return(e[n]|(r<=3?0:e[n+1]<<8))>>>r&31}function fe(e,t){var r=7&t,n=t>>>3;return(e[n]|(r<=1?0:e[n+1]<<8))>>>r&127}function le(e,t,r){var n=7&t,a=t>>>3,i=(1<<r)-1,s=e[a]>>>n;return r<8-n?s&i:(s|=e[a+1]<<8-n,r<16-n?s&i:(s|=e[a+2]<<16-n,r<24-n||(s|=e[a+3]<<24-n),s&i))}function ce(e,t,r){var n=7&t,a=t>>>3;return n<=5?e[a]|=(7&r)<<n:(e[a]|=r<<n&255,e[a+1]=(7&r)>>8-n),t+3}function he(e,t,r){var n=7&t,a=t>>>3;return r=(1&r)<<n,e[a]|=r,t+1}function ue(e,t,r){var n=7&t,a=t>>>3;return r<<=n,e[a]|=255&r,r>>>=8,e[a+1]=r,t+8}function de(e,t,r){var n=7&t,a=t>>>3;return r<<=n,e[a]|=255&r,r>>>=8,e[a+1]=255&r,e[a+2]=r>>>8,t+16}function pe(e,t){var r=e.length,n=2*r>t?2*r:t+5,a=0;if(r>=t)return e;if(E){var i=S(n);if(e.copy)e.copy(i);else for(;a<e.length;++a)i[a]=e[a];return i}if(ee){var s=new Uint8Array(n);if(s.set)s.set(e);else for(;a<r;++a)s[a]=e[a];return s}return e.length=n,e}function me(e){for(var t=new Array(e),r=0;r<e;++r)t[r]=0;return t}function ge(e,t,r){var n=1,a=0,i=0,s=0,o=0,f=e.length,l=ee?new Uint16Array(32):me(32);for(i=0;i<32;++i)l[i]=0;for(i=f;i<r;++i)e[i]=0;f=e.length;var c=ee?new Uint16Array(f):me(f);for(i=0;i<f;++i)l[a=e[i]]++,n<a&&(n=a),c[i]=0;for(l[0]=0,i=1;i<=n;++i)l[i+16]=o=o+l[i-1]<<1;for(i=0;i<f;++i)o=e[i],0!=o&&(c[i]=l[o+16]++);var h=0;for(i=0;i<f;++i)if(h=e[i],0!=h)for(o=ne(c[i],n)>>n-h,s=(1<<n+4-h)-1;s>=0;--s)t[o|s<<h]=15&h|i<<4;return n}var ve=ee?new Uint16Array(512):me(512),Te=ee?new Uint16Array(32):me(32);if(!ee){for(var be=0;be<512;++be)ve[be]=0;for(be=0;be<32;++be)Te[be]=0}(function(){for(var e=[],t=0;t<32;t++)e.push(5);ge(e,Te,32);var r=[];for(t=0;t<=143;t++)r.push(8);for(;t<=255;t++)r.push(9);for(;t<=279;t++)r.push(7);for(;t<=287;t++)r.push(8);ge(r,ve,288)})();var we=function(){for(var e=ee?new Uint8Array(32768):[],t=0,r=0;t<q.length-1;++t)for(;r<q[t+1];++r)e[r]=t;for(;r<32768;++r)e[r]=29;var n=ee?new Uint8Array(259):[];for(t=0,r=0;t<Z.length-1;++t)for(;r<Z[t+1];++r)n[r]=t;function a(e,t){var r=0;while(r<e.length){var n=Math.min(65535,e.length-r),a=r+n==e.length;t.write_shift(1,+a),t.write_shift(2,n),t.write_shift(2,65535&~n);while(n-- >0)t[t.l++]=e[r++]}return t.l}function i(t,r){var a=0,i=0,s=ee?new Uint16Array(32768):[];while(i<t.length){var o=Math.min(65535,t.length-i);if(o<10){a=ce(r,a,+!(i+o!=t.length)),7&a&&(a+=8-(7&a)),r.l=a/8|0,r.write_shift(2,o),r.write_shift(2,65535&~o);while(o-- >0)r[r.l++]=t[i++];a=8*r.l}else{a=ce(r,a,+!(i+o!=t.length)+2);var f=0;while(o-- >0){var l=t[i];f=32767&(f<<5^l);var c=-1,h=0;if((c=s[f])&&(c|=-32768&i,c>i&&(c-=32768),c<i))while(t[c+h]==t[i+h]&&h<250)++h;if(h>2){l=n[h],l<=22?a=ue(r,a,te[l+1]>>1)-1:(ue(r,a,3),a+=5,ue(r,a,te[l-23]>>5),a+=3);var u=l<8?0:l-4>>2;u>0&&(de(r,a,h-Z[l]),a+=u),l=e[i-c],a=ue(r,a,te[l]>>3),a-=3;var d=l<4?0:l-2>>1;d>0&&(de(r,a,i-c-q[l]),a+=d);for(var p=0;p<h;++p)s[f]=32767&i,f=32767&(f<<5^t[i]),++i;o-=h-1}else l<=143?l+=48:a=he(r,a,1),a=ue(r,a,te[l]),s[f]=32767&i,++i}a=ue(r,a,0)-1}}return r.l=(a+7)/8|0,r.l}return function(e,t){return e.length<8?a(e,t):i(e,t)}}();function Ee(e){var t=Nr(50+Math.floor(1.1*e.length)),r=we(e,t);return t.slice(0,r)}var Ae=ee?new Uint16Array(32768):me(32768),ye=ee?new Uint16Array(32768):me(32768),Se=ee?new Uint16Array(128):me(128),_e=1,xe=1;function Oe(e,t){var r=oe(e,t)+257;t+=5;var n=oe(e,t)+1;t+=5;var a=se(e,t)+4;t+=4;for(var i=0,s=ee?new Uint8Array(19):me(19),o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],f=1,l=ee?new Uint8Array(8):me(8),c=ee?new Uint8Array(8):me(8),h=s.length,u=0;u<a;++u)s[J[u]]=i=ie(e,t),f<i&&(f=i),l[i]++,t+=3;var d=0;for(l[0]=0,u=1;u<=f;++u)c[u]=d=d+l[u-1]<<1;for(u=0;u<h;++u)0!=(d=s[u])&&(o[u]=c[d]++);var p=0;for(u=0;u<h;++u)if(p=s[u],0!=p){d=te[o[u]]>>8-p;for(var m=(1<<7-p)-1;m>=0;--m)Se[d|m<<p]=7&p|u<<3}var g=[];for(f=1;g.length<r+n;)switch(d=Se[fe(e,t)],t+=7&d,d>>>=3){case 16:i=3+ae(e,t),t+=2,d=g[g.length-1];while(i-- >0)g.push(d);break;case 17:i=3+ie(e,t),t+=3;while(i-- >0)g.push(0);break;case 18:i=11+fe(e,t),t+=7;while(i-- >0)g.push(0);break;default:g.push(d),f<d&&(f=d);break}var v=g.slice(0,r),T=g.slice(r);for(u=r;u<286;++u)v[u]=0;for(u=n;u<30;++u)T[u]=0;return _e=ge(v,Ae,286),xe=ge(T,ye,30),t}function Ce(e,t){if(3==e[0]&&!(3&e[1]))return[y(t),2];var r=0,n=0,a=S(t||1<<18),i=0,s=a.length>>>0,o=0,f=0;while(0==(1&n))if(n=ie(e,r),r+=3,n>>>1!=0)for(n>>1==1?(o=9,f=5):(r=Oe(e,r),o=_e,f=xe);;){!t&&s<i+32767&&(a=pe(a,i+32767),s=a.length);var l=le(e,r,o),c=n>>>1==1?ve[l]:Ae[l];if(r+=15&c,c>>>=4,0===(c>>>8&255))a[i++]=c;else{if(256==c)break;c-=257;var h=c<8?0:c-4>>2;h>5&&(h=0);var u=i+Z[c];h>0&&(u+=le(e,r,h),r+=h),l=le(e,r,f),c=n>>>1==1?Te[l]:ye[l],r+=15&c,c>>>=4;var d=c<4?0:c-2>>1,p=q[c];d>0&&(p+=le(e,r,d),r+=d),!t&&s<u&&(a=pe(a,u+100),s=a.length);while(i<u)a[i]=a[i-p],++i}}else{7&r&&(r+=8-(7&r));var m=e[r>>>3]|e[1+(r>>>3)]<<8;if(r+=32,m>0){!t&&s<i+m&&(a=pe(a,i+m),s=a.length);while(m-- >0)a[i++]=e[r>>>3],r+=8}}return t?[a,r+7>>>3]:[a.slice(0,i),r+7>>>3]}function Re(e,t){var r=e.slice(e.l||0),n=Ce(r,t);return e.l+=n[1],n[0]}function ke(e,t){if(!e)throw new Error(t);"undefined"!==typeof console&&console.error(t)}function Ie(e,t){var r=e;kr(r,0);var n=[],a=[],i={FileIndex:n,FullPaths:a};k(i,{root:t.root});var s=r.length-4;while((80!=r[s]||75!=r[s+1]||5!=r[s+2]||6!=r[s+3])&&s>=0)--s;r.l=s+4,r.l+=4;var f=r.read_shift(2);r.l+=6;var l=r.read_shift(4);for(r.l=l,s=0;s<f;++s){r.l+=20;var c=r.read_shift(4),h=r.read_shift(4),u=r.read_shift(2),d=r.read_shift(2),p=r.read_shift(2);r.l+=8;var m=r.read_shift(4),g=o(r.slice(r.l+u,r.l+u+d));r.l+=u+d+p;var v=r.l;r.l=m+4,Ne(r,c,h,i,g),r.l=v}return i}function Ne(e,t,r,n,a){e.l+=2;var i=e.read_shift(2),f=e.read_shift(2),l=s(e);if(8257&i)throw new Error("Unsupported ZIP encryption");for(var c=e.read_shift(4),h=e.read_shift(4),u=e.read_shift(4),d=e.read_shift(2),p=e.read_shift(2),m="",g=0;g<d;++g)m+=String.fromCharCode(e[e.l++]);if(p){var v=o(e.slice(e.l,e.l+p));(v[21589]||{}).mt&&(l=v[21589].mt),((a||{})[21589]||{}).mt&&(l=a[21589].mt)}e.l+=p;var T=e.slice(e.l,e.l+h);switch(f){case 8:T=X(e,u);break;case 0:break;default:throw new Error("Unsupported ZIP Compression method "+f)}var b=!1;8&i&&(c=e.read_shift(4),134695760==c&&(c=e.read_shift(4),b=!0),h=e.read_shift(4),u=e.read_shift(4)),h!=t&&ke(b,"Bad compressed size: "+t+" != "+h),u!=r&&ke(b,"Bad uncompressed size: "+r+" != "+u),Ve(n,m,T,{unsafe:!0,mt:l})}function De(e,t){var r=t||{},n=[],a=[],s=Nr(1),o=r.compression?8:0,f=0,l=!1;l&&(f|=8);var c=0,h=0,u=0,d=0,p=e.FullPaths[0],m=p,g=e.FileIndex[0],v=[],T=0;for(c=1;c<e.FullPaths.length;++c)if(m=e.FullPaths[c].slice(p.length),g=e.FileIndex[c],g.size&&g.content&&"Sh33tJ5"!=m){var b=u,w=Nr(m.length);for(h=0;h<m.length;++h)w.write_shift(1,127&m.charCodeAt(h));w=w.slice(0,w.l),v[d]=$e.buf(g.content,0);var E=g.content;8==o&&(E=K(E)),s=Nr(30),s.write_shift(4,67324752),s.write_shift(2,20),s.write_shift(2,f),s.write_shift(2,o),g.mt?i(s,g.mt):s.write_shift(4,0),s.write_shift(-4,8&f?0:v[d]),s.write_shift(4,8&f?0:E.length),s.write_shift(4,8&f?0:g.content.length),s.write_shift(2,w.length),s.write_shift(2,0),u+=s.length,n.push(s),u+=w.length,n.push(w),u+=E.length,n.push(E),8&f&&(s=Nr(12),s.write_shift(-4,v[d]),s.write_shift(4,E.length),s.write_shift(4,g.content.length),u+=s.l,n.push(s)),s=Nr(46),s.write_shift(4,33639248),s.write_shift(2,0),s.write_shift(2,20),s.write_shift(2,f),s.write_shift(2,o),s.write_shift(4,0),s.write_shift(-4,v[d]),s.write_shift(4,E.length),s.write_shift(4,g.content.length),s.write_shift(2,w.length),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(4,0),s.write_shift(4,b),T+=s.l,a.push(s),T+=w.length,a.push(w),++d}return s=Nr(22),s.write_shift(4,101010256),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(2,d),s.write_shift(2,d),s.write_shift(4,T),s.write_shift(4,u),s.write_shift(2,0),R([R(n),R(a),s])}var Pe={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function Le(e,t){if(e.ctype)return e.ctype;var r=e.name||"",n=r.match(/\.([^\.]+)$/);return n&&Pe[n[1]]||t&&(n=(r=t).match(/[\.\\]([^\.\\])+$/),n&&Pe[n[1]])?Pe[n[1]]:"application/octet-stream"}function Me(e){for(var t=b(e),r=[],n=0;n<t.length;n+=76)r.push(t.slice(n,n+76));return r.join("\r\n")+"\r\n"}function Fe(e){var t=e.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,(function(e){var t=e.charCodeAt(0).toString(16).toUpperCase();return"="+(1==t.length?"0"+t:t)}));t=t.replace(/ $/gm,"=20").replace(/\t$/gm,"=09"),"\n"==t.charAt(0)&&(t="=0D"+t.slice(1)),t=t.replace(/\r(?!\n)/gm,"=0D").replace(/\n\n/gm,"\n=0A").replace(/([^\r\n])\n/gm,"$1=0A");for(var r=[],n=t.split("\r\n"),a=0;a<n.length;++a){var i=n[a];if(0!=i.length)for(var s=0;s<i.length;){var o=76,f=i.slice(s,s+o);"="==f.charAt(o-1)?o--:"="==f.charAt(o-2)?o-=2:"="==f.charAt(o-3)&&(o-=3),f=i.slice(s,s+o),s+=o,s<i.length&&(f+="="),r.push(f)}else r.push("")}return r.join("\r\n")}function Ue(e){for(var t=[],r=0;r<e.length;++r){var n=e[r];while(r<=e.length&&"="==n.charAt(n.length-1))n=n.slice(0,n.length-1)+e[++r];t.push(n)}for(var a=0;a<t.length;++a)t[a]=t[a].replace(/[=][0-9A-Fa-f]{2}/g,(function(e){return String.fromCharCode(parseInt(e.slice(1),16))}));return _(t.join("\r\n"))}function Be(e,t,r){for(var n,a="",i="",s="",o=0;o<10;++o){var f=t[o];if(!f||f.match(/^\s*$/))break;var l=f.match(/^(.*?):\s*([^\s].*)$/);if(l)switch(l[1].toLowerCase()){case"content-location":a=l[2].trim();break;case"content-type":s=l[2].trim();break;case"content-transfer-encoding":i=l[2].trim();break}}switch(++o,i.toLowerCase()){case"base64":n=_(w(t.slice(o).join("")));break;case"quoted-printable":n=Ue(t.slice(o));break;default:throw new Error("Unsupported Content-Transfer-Encoding "+i)}var c=Ve(e,a.slice(r.length),n,{unsafe:!0});s&&(c.ctype=s)}function We(e,t){if("mime-version:"!=z(e.slice(0,13)).toLowerCase())throw new Error("Unsupported MAD header");var r=t&&t.root||"",n=(E&&Buffer.isBuffer(e)?e.toString("binary"):z(e)).split("\r\n"),a=0,i="";for(a=0;a<n.length;++a)if(i=n[a],/^Content-Location:/i.test(i)&&(i=i.slice(i.indexOf("file")),r||(r=i.slice(0,i.lastIndexOf("/")+1)),i.slice(0,r.length)!=r))while(r.length>0)if(r=r.slice(0,r.length-1),r=r.slice(0,r.lastIndexOf("/")+1),i.slice(0,r.length)==r)break;var s=(n[1]||"").match(/boundary="(.*?)"/);if(!s)throw new Error("MAD cannot find boundary");var o="--"+(s[1]||""),f=[],l=[],c={FileIndex:f,FullPaths:l};k(c);var h,u=0;for(a=0;a<n.length;++a){var d=n[a];d!==o&&d!==o+"--"||(u++&&Be(c,n.slice(h,a),r),h=a)}return c}function He(e,t){var r=t||{},n=r.boundary||"SheetJS";n="------="+n;for(var a=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+n.slice(2)+'"',"","",""],i=e.FullPaths[0],s=i,o=e.FileIndex[0],f=1;f<e.FullPaths.length;++f)if(s=e.FullPaths[f].slice(i.length),o=e.FileIndex[f],o.size&&o.content&&"Sh33tJ5"!=s){s=s.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,(function(e){return"_x"+e.charCodeAt(0).toString(16)+"_"})).replace(/[\u0080-\uFFFF]/g,(function(e){return"_u"+e.charCodeAt(0).toString(16)+"_"}));for(var l=o.content,c=E&&Buffer.isBuffer(l)?l.toString("binary"):z(l),h=0,u=Math.min(1024,c.length),d=0,p=0;p<=u;++p)(d=c.charCodeAt(p))>=32&&d<128&&++h;var m=h>=4*u/5;a.push(n),a.push("Content-Location: "+(r.root||"file:///C:/SheetJS/")+s),a.push("Content-Transfer-Encoding: "+(m?"quoted-printable":"base64")),a.push("Content-Type: "+Le(o,s)),a.push(""),a.push(m?Fe(c):Me(c))}return a.push(n+"--\r\n"),a.join("\r\n")}function Ge(e){var t={};return k(t,e),t}function Ve(e,t,r,n){var i=n&&n.unsafe;i||k(e);var s=!i&&Ye.find(e,t);if(!s){var o=e.FullPaths[0];t.slice(0,o.length)==o?o=t:("/"!=o.slice(-1)&&(o+="/"),o=(o+t).replace("//","/")),s={name:a(t),type:2},e.FileIndex.push(s),e.FullPaths.push(o),i||Ye.utils.cfb_gc(e)}return s.content=r,s.size=r?r.length:0,n&&(n.CLSID&&(s.clsid=n.CLSID),n.mt&&(s.mt=n.mt),n.ct&&(s.ct=n.ct)),s}function je(e,t){k(e);var r=Ye.find(e,t);if(r)for(var n=0;n<e.FileIndex.length;++n)if(e.FileIndex[n]==r)return e.FileIndex.splice(n,1),e.FullPaths.splice(n,1),!0;return!1}function ze(e,t,r){k(e);var n=Ye.find(e,t);if(n)for(var i=0;i<e.FileIndex.length;++i)if(e.FileIndex[i]==n)return e.FileIndex[i].name=a(r),e.FullPaths[i]=r,!0;return!1}function Xe(e){P(e,!0)}return t.find=M,t.read=C,t.parse=l,t.write=$,t.writeFile=j,t.utils={cfb_new:Ge,cfb_add:Ve,cfb_del:je,cfb_mov:ze,cfb_gc:Xe,ReadShift:Sr,CheckField:Rr,prep_blob:kr,bconcat:R,use_zlib:Y,_deflateRaw:Ee,_inflateRaw:Re,consts:V},t}();let Xe=void 0;function Ke(e){return"string"===typeof e?x(e):Array.isArray(e)?C(e):e}function Je(e,t,r){if("undefined"!==typeof Xe&&Xe.writeFileSync)return r?Xe.writeFileSync(e,t,r):Xe.writeFileSync(e,t);if("undefined"!==typeof Deno){if(r&&"string"==typeof t)switch(r){case"utf8":t=new TextEncoder(r).encode(t);break;case"binary":t=x(t);break;default:throw new Error("Unsupported encoding "+r)}return Deno.writeFileSync(e,t)}var n="utf8"==r?Ft(t):t;if("undefined"!==typeof IE_SaveFile)return IE_SaveFile(n,e);if("undefined"!==typeof Blob){var a=new Blob([Ke(n)],{type:"application/octet-stream"});if("undefined"!==typeof navigator&&navigator.msSaveBlob)return navigator.msSaveBlob(a,e);if("undefined"!==typeof saveAs)return saveAs(a,e);if("undefined"!==typeof URL&&"undefined"!==typeof document&&document.createElement&&URL.createObjectURL){var i=URL.createObjectURL(a);if("object"===typeof chrome&&"function"==typeof(chrome.downloads||{}).download)return URL.revokeObjectURL&&"undefined"!==typeof setTimeout&&setTimeout((function(){URL.revokeObjectURL(i)}),6e4),chrome.downloads.download({url:i,filename:e,saveAs:!0});var s=document.createElement("a");if(null!=s.download)return s.download=e,s.href=i,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL&&"undefined"!==typeof setTimeout&&setTimeout((function(){URL.revokeObjectURL(i)}),6e4),i}}if("undefined"!==typeof $&&"undefined"!==typeof File&&"undefined"!==typeof Folder)try{var o=File(e);return o.open("w"),o.encoding="binary",Array.isArray(t)&&(t=O(t)),o.write(t),o.close(),t}catch(f){if(!f.message||!f.message.match(/onstruct/))throw f}throw new Error("cannot save file "+e)}function Ze(e){for(var t=Object.keys(e),r=[],n=0;n<t.length;++n)Object.prototype.hasOwnProperty.call(e,t[n])&&r.push(t[n]);return r}function qe(e,t){for(var r=[],n=Ze(e),a=0;a!==n.length;++a)null==r[e[n[a]][t]]&&(r[e[n[a]][t]]=n[a]);return r}function Qe(e){for(var t=[],r=Ze(e),n=0;n!==r.length;++n)t[e[r[n]]]=r[n];return t}function et(e){for(var t=[],r=Ze(e),n=0;n!==r.length;++n)t[e[r[n]]]=parseInt(r[n],10);return t}function tt(e){for(var t=[],r=Ze(e),n=0;n!==r.length;++n)null==t[e[r[n]]]&&(t[e[r[n]]]=[]),t[e[r[n]]].push(r[n]);return t}var rt=new Date(1899,11,30,0,0,0);function nt(e,t){var r=e.getTime();t&&(r-=1263168e5);var n=rt.getTime()+6e4*(e.getTimezoneOffset()-rt.getTimezoneOffset());return(r-n)/864e5}var at=new Date,it=rt.getTime()+6e4*(at.getTimezoneOffset()-rt.getTimezoneOffset()),st=at.getTimezoneOffset();function ot(e){var t=new Date;return t.setTime(24*e*60*60*1e3+it),t.getTimezoneOffset()!==st&&t.setTime(t.getTime()+6e4*(t.getTimezoneOffset()-st)),t}var ft=new Date("2017-02-19T19:06:09.000Z"),lt=isNaN(ft.getFullYear())?new Date("2/19/17"):ft,ct=2017==lt.getFullYear();function ht(e,t){var r=new Date(e);if(ct)return t>0?r.setTime(r.getTime()+60*r.getTimezoneOffset()*1e3):t<0&&r.setTime(r.getTime()-60*r.getTimezoneOffset()*1e3),r;if(e instanceof Date)return e;if(1917==lt.getFullYear()&&!isNaN(r.getFullYear())){var n=r.getFullYear();return e.indexOf(""+n)>-1||r.setFullYear(r.getFullYear()+100),r}var a=e.match(/\d+/g)||["2017","2","19","0","0","0"],i=new Date(+a[0],+a[1]-1,+a[2],+a[3]||0,+a[4]||0,+a[5]||0);return e.indexOf("Z")>-1&&(i=new Date(i.getTime()-60*i.getTimezoneOffset()*1e3)),i}function ut(e,t){if(E&&Buffer.isBuffer(e)){if(t){if(255==e[0]&&254==e[1])return Ft(e.slice(2).toString("utf16le"));if(254==e[1]&&255==e[2])return Ft(u(e.slice(2).toString("binary")))}return e.toString("binary")}if("undefined"!==typeof TextDecoder)try{if(t){if(255==e[0]&&254==e[1])return Ft(new TextDecoder("utf-16le").decode(e.slice(2)));if(254==e[0]&&255==e[1])return Ft(new TextDecoder("utf-16be").decode(e.slice(2)))}var r={"€":"","‚":"","ƒ":"","„":"","…":"","†":"","‡":"","ˆ":"","‰":"","Š":"","‹":"","Œ":"","Ž":"","‘":"","’":"","“":"","”":"","•":"","–":"","—":"","˜":"","™":"","š":"","›":"","œ":"","ž":"","Ÿ":""};return Array.isArray(e)&&(e=new Uint8Array(e)),new TextDecoder("latin1").decode(e).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,(function(e){return r[e]||e}))}catch(i){}for(var n=[],a=0;a!=e.length;++a)n.push(String.fromCharCode(e[a]));return n.join("")}function dt(e){if("undefined"!=typeof JSON&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if("object"!=typeof e||null==e)return e;if(e instanceof Date)return new Date(e.getTime());var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=dt(e[r]));return t}function pt(e,t){var r="";while(r.length<t)r+=e;return r}function mt(e){var t=Number(e);if(!isNaN(t))return isFinite(t)?t:NaN;if(!/\d/.test(e))return t;var r=1,n=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,(function(){return r*=100,""}));return isNaN(t=Number(n))?(n=n.replace(/[(](.*)[)]/,(function(e,t){return r=-r,t})),isNaN(t=Number(n))?t:t/r):t/r}var gt=["january","february","march","april","may","june","july","august","september","october","november","december"];function vt(e){var t=new Date(e),r=new Date(NaN),n=t.getYear(),a=t.getMonth(),i=t.getDate();if(isNaN(i))return r;var s=e.toLowerCase();if(s.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if(s=s.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,""),s.length>3&&-1==gt.indexOf(s))return r}else if(s.match(/[a-z]/))return r;return n<0||n>8099?r:(a>0||i>1)&&101!=n?t:e.match(/[^-0-9:,\/\\]/)?r:t}function Tt(e,t,r){if(e.FullPaths){var n;if("string"==typeof r)return n=E?A(r):k(r),Ye.utils.cfb_add(e,t,n);Ye.utils.cfb_add(e,t,r)}else e.file(t,r)}function bt(){return Ye.utils.cfb_new()}var wt='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r\n';var Et={"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"},At=Qe(Et),yt=/[&<>'"]/g,St=/[\u0000-\u0008\u000b-\u001f]/g;function _t(e){var t=e+"";return t.replace(yt,(function(e){return At[e]})).replace(St,(function(e){return"_x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+"_"}))}function xt(e){return _t(e).replace(/ /g,"_x0020_")}var Ot=/[\u0000-\u001f]/g;function Ct(e){var t=e+"";return t.replace(yt,(function(e){return At[e]})).replace(/\n/g,"<br/>").replace(Ot,(function(e){return"&#x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+";"}))}function Rt(e){var t=e+"";return t.replace(yt,(function(e){return At[e]})).replace(Ot,(function(e){return"&#x"+e.charCodeAt(0).toString(16).toUpperCase()+";"}))}function kt(e){return e.replace(/(\r\n|[\r\n])/g,"&#10;")}function It(e){switch(e){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}function Nt(e){var t="",r=0,n=0,a=0,i=0,s=0,o=0;while(r<e.length)n=e.charCodeAt(r++),n<128?t+=String.fromCharCode(n):(a=e.charCodeAt(r++),n>191&&n<224?(s=(31&n)<<6,s|=63&a,t+=String.fromCharCode(s)):(i=e.charCodeAt(r++),n<240?t+=String.fromCharCode((15&n)<<12|(63&a)<<6|63&i):(s=e.charCodeAt(r++),o=((7&n)<<18|(63&a)<<12|(63&i)<<6|63&s)-65536,t+=String.fromCharCode(55296+(o>>>10&1023)),t+=String.fromCharCode(56320+(1023&o)))));return t}function Dt(e){var t,r,n,a=y(2*e.length),i=1,s=0,o=0;for(r=0;r<e.length;r+=i)i=1,(n=e.charCodeAt(r))<128?t=n:n<224?(t=64*(31&n)+(63&e.charCodeAt(r+1)),i=2):n<240?(t=4096*(15&n)+64*(63&e.charCodeAt(r+1))+(63&e.charCodeAt(r+2)),i=3):(i=4,t=262144*(7&n)+4096*(63&e.charCodeAt(r+1))+64*(63&e.charCodeAt(r+2))+(63&e.charCodeAt(r+3)),t-=65536,o=55296+(t>>>10&1023),t=56320+(1023&t)),0!==o&&(a[s++]=255&o,a[s++]=o>>>8,o=0),a[s++]=t%256,a[s++]=t>>>8;return a.slice(0,s).toString("ucs2")}function Pt(e){return A(e,"binary").toString("utf8")}var Lt="foo bar bazâð£",Mt=E&&(Pt(Lt)==Nt(Lt)&&Pt||Dt(Lt)==Nt(Lt)&&Dt)||Nt,Ft=E?function(e){return A(e,"utf8").toString("binary")}:function(e){var t=[],r=0,n=0,a=0;while(r<e.length)switch(n=e.charCodeAt(r++),!0){case n<128:t.push(String.fromCharCode(n));break;case n<2048:t.push(String.fromCharCode(192+(n>>6))),t.push(String.fromCharCode(128+(63&n)));break;case n>=55296&&n<57344:n-=55296,a=e.charCodeAt(r++)-56320+(n<<10),t.push(String.fromCharCode(240+(a>>18&7))),t.push(String.fromCharCode(144+(a>>12&63))),t.push(String.fromCharCode(128+(a>>6&63))),t.push(String.fromCharCode(128+(63&a)));break;default:t.push(String.fromCharCode(224+(n>>12))),t.push(String.fromCharCode(128+(n>>6&63))),t.push(String.fromCharCode(128+(63&n)))}return t.join("")},Ut=function(){var e=[["nbsp"," "],["middot","·"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map((function(e){return[new RegExp("&"+e[0]+";","ig"),e[1]]}));return function(t){for(var r=t.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,"\n").replace(/<[^>]*>/g,""),n=0;n<e.length;++n)r=r.replace(e[n][0],e[n][1]);return r}}();var Bt=/(^\s|\s$|\n)/;function Wt(e,t){return"<"+e+(t.match(Bt)?' xml:space="preserve"':"")+">"+t+"</"+e+">"}function Ht(e){return Ze(e).map((function(t){return" "+t+'="'+e[t]+'"'})).join("")}function Gt(e,t,r){return"<"+e+(null!=r?Ht(r):"")+(null!=t?(t.match(Bt)?' xml:space="preserve"':"")+">"+t+"</"+e:"/")+">"}function Vt(e,t){try{return e.toISOString().replace(/\.\d*/,"")}catch(r){if(t)throw r}return""}function jt(e,t){switch(typeof e){case"string":var r=Gt("vt:lpwstr",_t(e));return t&&(r=r.replace(/&quot;/g,"_x0022_")),r;case"number":return Gt((0|e)==e?"vt:i4":"vt:r8",_t(String(e)));case"boolean":return Gt("vt:bool",e?"true":"false")}if(e instanceof Date)return Gt("vt:filetime",Vt(e));throw new Error("Unable to serialize "+e)}var zt={CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/metadata/core-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/custom-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/extended-properties",CT:"http://schemas.openxmlformats.org/package/2006/content-types",RELS:"http://schemas.openxmlformats.org/package/2006/relationships",TCMNT:"http://schemas.microsoft.com/office/spreadsheetml/2018/threadedcomments",dc:"http://purl.org/dc/elements/1.1/",dcterms:"http://purl.org/dc/terms/",dcmitype:"http://purl.org/dc/dcmitype/",mx:"http://schemas.microsoft.com/office/mac/excel/2008/main",r:"http://schemas.openxmlformats.org/officeDocument/2006/relationships",sjs:"http://schemas.openxmlformats.org/package/2006/sheetjs/core-properties",vt:"http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes",xsi:"http://www.w3.org/2001/XMLSchema-instance",xsd:"http://www.w3.org/2001/XMLSchema"},$t=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"],Yt={o:"urn:schemas-microsoft-com:office:office",x:"urn:schemas-microsoft-com:office:excel",ss:"urn:schemas-microsoft-com:office:spreadsheet",dt:"uuid:C2F41010-65B3-11d1-A29F-00AA00C14882",mv:"http://macVmlSchemaUri",v:"urn:schemas-microsoft-com:vml",html:"http://www.w3.org/TR/REC-html40"};function Xt(e,t){for(var r=1-2*(e[t+7]>>>7),n=((127&e[t+7])<<4)+(e[t+6]>>>4&15),a=15&e[t+6],i=5;i>=0;--i)a=256*a+e[t+i];return 2047==n?0==a?r*(1/0):NaN:(0==n?n=-1022:(n-=1023,a+=Math.pow(2,52)),r*Math.pow(2,n-52)*a)}function Kt(e,t,r){var n=(t<0||1/t==-1/0?1:0)<<7,a=0,i=0,s=n?-t:t;isFinite(s)?0==s?a=i=0:(a=Math.floor(Math.log(s)/Math.LN2),i=s*Math.pow(2,52-a),a<=-1023&&(!isFinite(i)||i<Math.pow(2,52))?a=-1022:(i-=Math.pow(2,52),a+=1023)):(a=2047,i=isNaN(t)?26985:0);for(var o=0;o<=5;++o,i/=256)e[r+o]=255&i;e[r+6]=(15&a)<<4|15&i,e[r+7]=a>>4|n}var Jt=function(e){for(var t=[],r=10240,n=0;n<e[0].length;++n)if(e[0][n])for(var a=0,i=e[0][n].length;a<i;a+=r)t.push.apply(t,e[0][n].slice(a,a+r));return t},Zt=E?function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0].map((function(e){return Buffer.isBuffer(e)?e:A(e)}))):Jt(e)}:Jt,qt=function(e,t,r){for(var n=[],a=t;a<r;a+=2)n.push(String.fromCharCode(br(e,a)));return n.join("").replace(I,"")},Qt=E?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf16le",t,r).replace(I,""):qt(e,t,r)}:qt,er=function(e,t,r){for(var n=[],a=t;a<t+r;++a)n.push(("0"+e[a].toString(16)).slice(-2));return n.join("")},tr=E?function(e,t,r){return Buffer.isBuffer(e)?e.toString("hex",t,t+r):er(e,t,r)}:er,rr=function(e,t,r){for(var n=[],a=t;a<r;a++)n.push(String.fromCharCode(Tr(e,a)));return n.join("")},nr=E?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf8",t,r):rr(e,t,r)}:rr,ar=function(e,t){var r=Er(e,t);return r>0?nr(e,t+4,t+4+r-1):""},ir=ar,sr=function(e,t){var r=Er(e,t);return r>0?nr(e,t+4,t+4+r-1):""},or=sr,fr=function(e,t){var r=2*Er(e,t);return r>0?nr(e,t+4,t+4+r-1):""},lr=fr,cr=function(e,t){var r=Er(e,t);return r>0?Qt(e,t+4,t+4+r):""},hr=cr,ur=function(e,t){var r=Er(e,t);return r>0?nr(e,t+4,t+4+r):""},dr=ur,pr=function(e,t){return Xt(e,t)},mr=pr,gr=function(e){return Array.isArray(e)||"undefined"!==typeof Uint8Array&&e instanceof Uint8Array};function vr(){Qt=function(e,t,r){return d.utils.decode(1200,e.slice(t,r)).replace(I,"")},nr=function(e,t,r){return d.utils.decode(65001,e.slice(t,r))},ir=function(e,t){var r=Er(e,t);return r>0?d.utils.decode(i,e.slice(t+4,t+4+r-1)):""},or=function(e,t){var r=Er(e,t);return r>0?d.utils.decode(a,e.slice(t+4,t+4+r-1)):""},lr=function(e,t){var r=2*Er(e,t);return r>0?d.utils.decode(1200,e.slice(t+4,t+4+r-1)):""},hr=function(e,t){var r=Er(e,t);return r>0?d.utils.decode(1200,e.slice(t+4,t+4+r)):""},dr=function(e,t){var r=Er(e,t);return r>0?d.utils.decode(65001,e.slice(t+4,t+4+r)):""}}E&&(ir=function(e,t){if(!Buffer.isBuffer(e))return ar(e,t);var r=e.readUInt32LE(t);return r>0?e.toString("utf8",t+4,t+4+r-1):""},or=function(e,t){if(!Buffer.isBuffer(e))return sr(e,t);var r=e.readUInt32LE(t);return r>0?e.toString("utf8",t+4,t+4+r-1):""},lr=function(e,t){if(!Buffer.isBuffer(e))return fr(e,t);var r=2*e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r-1)},hr=function(e,t){if(!Buffer.isBuffer(e))return cr(e,t);var r=e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r)},dr=function(e,t){if(!Buffer.isBuffer(e))return ur(e,t);var r=e.readUInt32LE(t);return e.toString("utf8",t+4,t+4+r)},mr=function(e,t){return Buffer.isBuffer(e)?e.readDoubleLE(t):pr(e,t)},gr=function(e){return Buffer.isBuffer(e)||Array.isArray(e)||"undefined"!==typeof Uint8Array&&e instanceof Uint8Array}),"undefined"!==typeof d&&vr();var Tr=function(e,t){return e[t]},br=function(e,t){return 256*e[t+1]+e[t]},wr=function(e,t){var r=256*e[t+1]+e[t];return r<32768?r:-1*(65535-r+1)},Er=function(e,t){return e[t+3]*(1<<24)+(e[t+2]<<16)+(e[t+1]<<8)+e[t]},Ar=function(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]},yr=function(e,t){return e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3]};function Sr(e,t){var r,n,i,s,o,f,l="",c=[];switch(t){case"dbcs":if(f=this.l,E&&Buffer.isBuffer(this))l=this.slice(this.l,this.l+2*e).toString("utf16le");else for(o=0;o<e;++o)l+=String.fromCharCode(br(this,f)),f+=2;e*=2;break;case"utf8":l=nr(this,this.l,this.l+e);break;case"utf16le":e*=2,l=Qt(this,this.l,this.l+e);break;case"wstr":if("undefined"===typeof d)return Sr.call(this,e,"dbcs");l=d.utils.decode(a,this.slice(this.l,this.l+2*e)),e*=2;break;case"lpstr-ansi":l=ir(this,this.l),e=4+Er(this,this.l);break;case"lpstr-cp":l=or(this,this.l),e=4+Er(this,this.l);break;case"lpwstr":l=lr(this,this.l),e=4+2*Er(this,this.l);break;case"lpp4":e=4+Er(this,this.l),l=hr(this,this.l),2&e&&(e+=2);break;case"8lpp4":e=4+Er(this,this.l),l=dr(this,this.l),3&e&&(e+=4-(3&e));break;case"cstr":e=0,l="";while(0!==(i=Tr(this,this.l+e++)))c.push(p(i));l=c.join("");break;case"_wstr":e=0,l="";while(0!==(i=br(this,this.l+e)))c.push(p(i)),e+=2;e+=2,l=c.join("");break;case"dbcs-cont":for(l="",f=this.l,o=0;o<e;++o){if(this.lens&&-1!==this.lens.indexOf(f))return i=Tr(this,f),this.l=f+1,s=Sr.call(this,e-o,i?"dbcs-cont":"sbcs-cont"),c.join("")+s;c.push(p(br(this,f))),f+=2}l=c.join(""),e*=2;break;case"cpstr":if("undefined"!==typeof d){l=d.utils.decode(a,this.slice(this.l,this.l+e));break}case"sbcs-cont":for(l="",f=this.l,o=0;o!=e;++o){if(this.lens&&-1!==this.lens.indexOf(f))return i=Tr(this,f),this.l=f+1,s=Sr.call(this,e-o,i?"dbcs-cont":"sbcs-cont"),c.join("")+s;c.push(p(Tr(this,f))),f+=1}l=c.join("");break;default:switch(e){case 1:return r=Tr(this,this.l),this.l++,r;case 2:return r=("i"===t?wr:br)(this,this.l),this.l+=2,r;case 4:case-4:return"i"===t||0===(128&this[this.l+3])?(r=(e>0?Ar:yr)(this,this.l),this.l+=4,r):(n=Er(this,this.l),this.l+=4,n);case 8:case-8:if("f"===t)return n=8==e?mr(this,this.l):mr([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,n;e=8;case 16:l=tr(this,this.l,e);break}}return this.l+=e,l}var _r=function(e,t,r){e[r]=255&t,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24&255},xr=function(e,t,r){e[r]=255&t,e[r+1]=t>>8&255,e[r+2]=t>>16&255,e[r+3]=t>>24&255},Or=function(e,t,r){e[r]=255&t,e[r+1]=t>>>8&255};function Cr(e,t,r){var n=0,a=0;if("dbcs"===r){for(a=0;a!=t.length;++a)Or(this,t.charCodeAt(a),this.l+2*a);n=2*t.length}else if("sbcs"===r){if("undefined"!==typeof d&&874==i)for(a=0;a!=t.length;++a){var s=d.utils.encode(i,t.charAt(a));this[this.l+a]=s[0]}else for(t=t.replace(/[^\x00-\x7F]/g,"_"),a=0;a!=t.length;++a)this[this.l+a]=255&t.charCodeAt(a);n=t.length}else{if("hex"===r){for(;a<e;++a)this[this.l++]=parseInt(t.slice(2*a,2*a+2),16)||0;return this}if("utf16le"===r){var o=Math.min(this.l+e,this.length);for(a=0;a<Math.min(t.length,e);++a){var f=t.charCodeAt(a);this[this.l++]=255&f,this[this.l++]=f>>8}while(this.l<o)this[this.l++]=0;return this}switch(e){case 1:n=1,this[this.l]=255&t;break;case 2:n=2,this[this.l]=255&t,t>>>=8,this[this.l+1]=255&t;break;case 3:n=3,this[this.l]=255&t,t>>>=8,this[this.l+1]=255&t,t>>>=8,this[this.l+2]=255&t;break;case 4:n=4,_r(this,t,this.l);break;case 8:if(n=8,"f"===r){Kt(this,t,this.l);break}case 16:break;case-4:n=4,xr(this,t,this.l);break}}return this.l+=n,this}function Rr(e,t){var r=tr(this,this.l,e.length>>1);if(r!==e)throw new Error(t+"Expected "+e+" saw "+r);this.l+=e.length>>1}function kr(e,t){e.l=t,e.read_shift=Sr,e.chk=Rr,e.write_shift=Cr}function Ir(e,t){e.l+=t}function Nr(e){var t=y(e);return kr(t,0),t}function Dr(){var e=[],t=E?256:2048,r=function(e){var t=Nr(e);return kr(t,0),t},n=r(t),a=function(){n&&(n.length>n.l&&(n=n.slice(0,n.l),n.l=n.length),n.length>0&&e.push(n),n=null)},i=function(e){return n&&e<n.length-n.l?n:(a(),n=r(Math.max(e+1,t)))},s=function(){return a(),R(e)},o=function(e){a(),n=e,null==n.l&&(n.l=n.length),i(t)};return{next:i,push:o,end:s,_bufs:e}}function Pr(e,t,r,n){var a,i=+t;if(!isNaN(i)){n||(n=ah[i].p||(r||[]).length||0),a=1+(i>=128?1:0)+1,n>=128&&++a,n>=16384&&++a,n>=2097152&&++a;var s=e.next(a);i<=127?s.write_shift(1,i):(s.write_shift(1,128+(127&i)),s.write_shift(1,i>>7));for(var o=0;4!=o;++o){if(!(n>=128)){s.write_shift(1,n);break}s.write_shift(1,128+(127&n)),n>>=7}n>0&&gr(r)&&e.push(r)}}function Lr(e,t,r){var n=dt(e);if(t.s?(n.cRel&&(n.c+=t.s.c),n.rRel&&(n.r+=t.s.r)):(n.cRel&&(n.c+=t.c),n.rRel&&(n.r+=t.r)),!r||r.biff<12){while(n.c>=256)n.c-=256;while(n.r>=65536)n.r-=65536}return n}function Mr(e,t,r){var n=dt(e);return n.s=Lr(n.s,t.s,r),n.e=Lr(n.e,t.s,r),n}function Fr(e,t){if(e.cRel&&e.c<0){e=dt(e);while(e.c<0)e.c+=t>8?16384:256}if(e.rRel&&e.r<0){e=dt(e);while(e.r<0)e.r+=t>8?1048576:t>5?65536:16384}var r=Kr(e);return e.cRel||null==e.cRel||(r=zr(r)),e.rRel||null==e.rRel||(r=Hr(r)),r}function Ur(e,t){return 0!=e.s.r||e.s.rRel||e.e.r!=(t.biff>=12?1048575:t.biff>=8?65536:16384)||e.e.rRel?0!=e.s.c||e.s.cRel||e.e.c!=(t.biff>=12?16383:255)||e.e.cRel?Fr(e.s,t.biff)+":"+Fr(e.e,t.biff):(e.s.rRel?"":"$")+Wr(e.s.r)+":"+(e.e.rRel?"":"$")+Wr(e.e.r):(e.s.cRel?"":"$")+jr(e.s.c)+":"+(e.e.cRel?"":"$")+jr(e.e.c)}function Br(e){return parseInt(Gr(e),10)-1}function Wr(e){return""+(e+1)}function Hr(e){return e.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")}function Gr(e){return e.replace(/\$(\d+)$/,"$1")}function Vr(e){for(var t=$r(e),r=0,n=0;n!==t.length;++n)r=26*r+t.charCodeAt(n)-64;return r-1}function jr(e){if(e<0)throw new Error("invalid column "+e);var t="";for(++e;e;e=Math.floor((e-1)/26))t=String.fromCharCode((e-1)%26+65)+t;return t}function zr(e){return e.replace(/^([A-Z])/,"$$$1")}function $r(e){return e.replace(/^\$([A-Z])/,"$1")}function Yr(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")}function Xr(e){for(var t=0,r=0,n=0;n<e.length;++n){var a=e.charCodeAt(n);a>=48&&a<=57?t=10*t+(a-48):a>=65&&a<=90&&(r=26*r+(a-64))}return{c:r-1,r:t-1}}function Kr(e){for(var t=e.c+1,r="";t;t=(t-1)/26|0)r=String.fromCharCode((t-1)%26+65)+r;return r+(e.r+1)}function Jr(e){var t=e.indexOf(":");return-1==t?{s:Xr(e),e:Xr(e)}:{s:Xr(e.slice(0,t)),e:Xr(e.slice(t+1))}}function Zr(e,t){return"undefined"===typeof t||"number"===typeof t?Zr(e.s,e.e):("string"!==typeof e&&(e=Kr(e)),"string"!==typeof t&&(t=Kr(t)),e==t?e:e+":"+t)}function qr(e){var t={s:{c:0,r:0},e:{c:0,r:0}},r=0,n=0,a=0,i=e.length;for(r=0;n<i;++n){if((a=e.charCodeAt(n)-64)<1||a>26)break;r=26*r+a}for(t.s.c=--r,r=0;n<i;++n){if((a=e.charCodeAt(n)-48)<0||a>9)break;r=10*r+a}if(t.s.r=--r,n===i||10!=a)return t.e.c=t.s.c,t.e.r=t.s.r,t;for(++n,r=0;n!=i;++n){if((a=e.charCodeAt(n)-64)<1||a>26)break;r=26*r+a}for(t.e.c=--r,r=0;n!=i;++n){if((a=e.charCodeAt(n)-48)<0||a>9)break;r=10*r+a}return t.e.r=--r,t}function Qr(e,t){var r="d"==e.t&&t instanceof Date;if(null!=e.z)try{return e.w=Ue(e.z,r?nt(t):t)}catch(n){}try{return e.w=Ue((e.XF||{}).numFmtId||(r?14:0),r?nt(t):t)}catch(n){return""+t}}function en(e,t,r){return null==e||null==e.t||"z"==e.t?"":void 0!==e.w?e.w:("d"==e.t&&!e.z&&r&&r.dateNF&&(e.z=r.dateNF),"e"==e.t?Qn[e.v]||e.v:Qr(e,void 0==t?e.v:t))}function tn(e,t){var r=t&&t.sheet?t.sheet:"Sheet1",n={};return n[r]=e,{SheetNames:[r],Sheets:n}}function rn(e,t,r){var n=r||{},a=e?Array.isArray(e):n.dense;null!=g&&null==a&&(a=g);var i=e||(a?[]:{}),s=0,o=0;if(i&&null!=n.origin){if("number"==typeof n.origin)s=n.origin;else{var f="string"==typeof n.origin?Xr(n.origin):n.origin;s=f.r,o=f.c}i["!ref"]||(i["!ref"]="A1:A1")}var l={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(i["!ref"]){var c=qr(i["!ref"]);l.s.c=c.s.c,l.s.r=c.s.r,l.e.c=Math.max(l.e.c,c.e.c),l.e.r=Math.max(l.e.r,c.e.r),-1==s&&(l.e.r=s=c.e.r+1)}for(var h=0;h!=t.length;++h)if(t[h]){if(!Array.isArray(t[h]))throw new Error("aoa_to_sheet expects an array of arrays");for(var u=0;u!=t[h].length;++u)if("undefined"!==typeof t[h][u]){var d={v:t[h][u]},p=s+h,m=o+u;if(l.s.r>p&&(l.s.r=p),l.s.c>m&&(l.s.c=m),l.e.r<p&&(l.e.r=p),l.e.c<m&&(l.e.c=m),!t[h][u]||"object"!==typeof t[h][u]||Array.isArray(t[h][u])||t[h][u]instanceof Date)if(Array.isArray(d.v)&&(d.f=t[h][u][1],d.v=d.v[0]),null===d.v)if(d.f)d.t="n";else if(n.nullError)d.t="e",d.v=0;else{if(!n.sheetStubs)continue;d.t="z"}else"number"===typeof d.v?d.t="n":"boolean"===typeof d.v?d.t="b":d.v instanceof Date?(d.z=n.dateNF||z[14],n.cellDates?(d.t="d",d.w=Ue(d.z,nt(d.v))):(d.t="n",d.v=nt(d.v),d.w=Ue(d.z,d.v))):d.t="s";else d=t[h][u];if(a)i[p]||(i[p]=[]),i[p][m]&&i[p][m].z&&(d.z=i[p][m].z),i[p][m]=d;else{var v=Kr({c:m,r:p});i[v]&&i[v].z&&(d.z=i[v].z),i[v]=d}}}return l.s.c<1e7&&(i["!ref"]=Zr(l)),i}function nn(e,t){return rn(null,e,t)}function an(e){return e.read_shift(4,"i")}function sn(e,t){return t||(t=Nr(4)),t.write_shift(4,e),t}function on(e){var t=e.read_shift(4);return 0===t?"":e.read_shift(t,"dbcs")}function fn(e,t){var r=!1;return null==t&&(r=!0,t=Nr(4+2*e.length)),t.write_shift(4,e.length),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}function ln(e){return{ich:e.read_shift(2),ifnt:e.read_shift(2)}}function cn(e,t){return t||(t=Nr(4)),t.write_shift(2,e.ich||0),t.write_shift(2,e.ifnt||0),t}function hn(e,t){var r=e.l,n=e.read_shift(1),a=on(e),i=[],s={t:a,h:a};if(0!==(1&n)){for(var o=e.read_shift(4),f=0;f!=o;++f)i.push(ln(e));s.r=i}else s.r=[{ich:0,ifnt:0}];return e.l=r+t,s}function un(e,t){var r=!1;return null==t&&(r=!0,t=Nr(15+4*e.t.length)),t.write_shift(1,0),fn(e.t,t),r?t.slice(0,t.l):t}var dn=hn;function pn(e,t){var r=!1;return null==t&&(r=!0,t=Nr(23+4*e.t.length)),t.write_shift(1,1),fn(e.t,t),t.write_shift(4,1),cn({ich:0,ifnt:0},t),r?t.slice(0,t.l):t}function mn(e){var t=e.read_shift(4),r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:t,iStyleRef:r}}function gn(e,t){return null==t&&(t=Nr(8)),t.write_shift(-4,e.c),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}function vn(e){var t=e.read_shift(2);return t+=e.read_shift(1)<<16,e.l++,{c:-1,iStyleRef:t}}function Tn(e,t){return null==t&&(t=Nr(4)),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}var bn=on,wn=fn;function En(e){var t=e.read_shift(4);return 0===t||4294967295===t?"":e.read_shift(t,"dbcs")}function An(e,t){var r=!1;return null==t&&(r=!0,t=Nr(127)),t.write_shift(4,e.length>0?e.length:4294967295),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}var yn=on,Sn=En,_n=An;function xn(e){var t=e.slice(e.l,e.l+4),r=1&t[0],n=2&t[0];e.l+=4;var a=0===n?mr([0,0,0,0,252&t[0],t[1],t[2],t[3]],0):Ar(t,0)>>2;return r?a/100:a}function On(e,t){null==t&&(t=Nr(4));var r=0,n=0,a=100*e;if(e==(0|e)&&e>=-(1<<29)&&e<1<<29?n=1:a==(0|a)&&a>=-(1<<29)&&a<1<<29&&(n=1,r=1),!n)throw new Error("unsupported RkNumber "+e);t.write_shift(-4,((r?a:e)<<2)+(r+2))}function Cn(e){var t={s:{},e:{}};return t.s.r=e.read_shift(4),t.e.r=e.read_shift(4),t.s.c=e.read_shift(4),t.e.c=e.read_shift(4),t}function Rn(e,t){return t||(t=Nr(16)),t.write_shift(4,e.s.r),t.write_shift(4,e.e.r),t.write_shift(4,e.s.c),t.write_shift(4,e.e.c),t}var kn=Cn,In=Rn;function Nn(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e.read_shift(8,"f")}function Dn(e,t){return(t||Nr(8)).write_shift(8,e,"f")}function Pn(e){var t={},r=e.read_shift(1),n=r>>>1,a=e.read_shift(1),i=e.read_shift(2,"i"),s=e.read_shift(1),o=e.read_shift(1),f=e.read_shift(1);switch(e.l++,n){case 0:t.auto=1;break;case 1:t.index=a;var l=qn[a];l&&(t.rgb=Pi(l));break;case 2:t.rgb=Pi([s,o,f]);break;case 3:t.theme=a;break}return 0!=i&&(t.tint=i>0?i/32767:i/32768),t}function Ln(e,t){if(t||(t=Nr(8)),!e||e.auto)return t.write_shift(4,0),t.write_shift(4,0),t;null!=e.index?(t.write_shift(1,2),t.write_shift(1,e.index)):null!=e.theme?(t.write_shift(1,6),t.write_shift(1,e.theme)):(t.write_shift(1,5),t.write_shift(1,0));var r=e.tint||0;if(r>0?r*=32767:r<0&&(r*=32768),t.write_shift(2,r),e.rgb&&null==e.theme){var n=e.rgb||"FFFFFF";"number"==typeof n&&(n=("000000"+n.toString(16)).slice(-6)),t.write_shift(1,parseInt(n.slice(0,2),16)),t.write_shift(1,parseInt(n.slice(2,4),16)),t.write_shift(1,parseInt(n.slice(4,6),16)),t.write_shift(1,255)}else t.write_shift(2,0),t.write_shift(1,0),t.write_shift(1,0);return t}function Mn(e){var t=e.read_shift(1);e.l++;var r={fBold:1&t,fItalic:2&t,fUnderline:4&t,fStrikeout:8&t,fOutline:16&t,fShadow:32&t,fCondense:64&t,fExtend:128&t};return r}function Fn(e,t){t||(t=Nr(2));var r=(e.italic?2:0)|(e.strike?8:0)|(e.outline?16:0)|(e.shadow?32:0)|(e.condense?64:0)|(e.extend?128:0);return t.write_shift(1,r),t.write_shift(1,0),t}var Un=2,Bn=3,Wn=11,Hn=19,Gn=64,Vn=65,jn=71,zn=4108,$n=4126,Yn=80,Xn={1:{n:"CodePage",t:Un},2:{n:"Category",t:Yn},3:{n:"PresentationFormat",t:Yn},4:{n:"ByteCount",t:Bn},5:{n:"LineCount",t:Bn},6:{n:"ParagraphCount",t:Bn},7:{n:"SlideCount",t:Bn},8:{n:"NoteCount",t:Bn},9:{n:"HiddenCount",t:Bn},10:{n:"MultimediaClipCount",t:Bn},11:{n:"ScaleCrop",t:Wn},12:{n:"HeadingPairs",t:zn},13:{n:"TitlesOfParts",t:$n},14:{n:"Manager",t:Yn},15:{n:"Company",t:Yn},16:{n:"LinksUpToDate",t:Wn},17:{n:"CharacterCount",t:Bn},19:{n:"SharedDoc",t:Wn},22:{n:"HyperlinksChanged",t:Wn},23:{n:"AppVersion",t:Bn,p:"version"},24:{n:"DigSig",t:Vn},26:{n:"ContentType",t:Yn},27:{n:"ContentStatus",t:Yn},28:{n:"Language",t:Yn},29:{n:"Version",t:Yn},255:{},2147483648:{n:"Locale",t:Hn},2147483651:{n:"Behavior",t:Hn},1919054434:{}},Kn={1:{n:"CodePage",t:Un},2:{n:"Title",t:Yn},3:{n:"Subject",t:Yn},4:{n:"Author",t:Yn},5:{n:"Keywords",t:Yn},6:{n:"Comments",t:Yn},7:{n:"Template",t:Yn},8:{n:"LastAuthor",t:Yn},9:{n:"RevNumber",t:Yn},10:{n:"EditTime",t:Gn},11:{n:"LastPrinted",t:Gn},12:{n:"CreatedDate",t:Gn},13:{n:"ModifiedDate",t:Gn},14:{n:"PageCount",t:Bn},15:{n:"WordCount",t:Bn},16:{n:"CharCount",t:Bn},17:{n:"Thumbnail",t:jn},18:{n:"Application",t:Yn},19:{n:"DocSecurity",t:Bn},255:{},2147483648:{n:"Locale",t:Hn},2147483651:{n:"Behavior",t:Hn},1919054434:{}};function Jn(e){return e.map((function(e){return[e>>16&255,e>>8&255,255&e]}))}var Zn=Jn([0,16777215,16711680,65280,255,16776960,16711935,65535,0,16777215,16711680,65280,255,16776960,16711935,65535,8388608,32768,128,8421376,8388736,32896,12632256,8421504,10066431,10040166,16777164,13434879,6684774,16744576,26316,13421823,128,16711935,16776960,65535,8388736,8388608,32896,255,52479,13434879,13434828,16777113,10079487,16751052,13408767,16764057,3368703,3394764,10079232,16763904,16750848,16737792,6710937,9868950,13158,3381606,13056,3355392,10040064,10040166,3355545,3355443,16777215,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]),qn=dt(Zn),Qn={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},ea={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"},ta={workbooks:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.main+xml",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.main",xlam:"application/vnd.ms-excel.addin.macroEnabled.main+xml",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml"},strs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml",xlsb:"application/vnd.ms-excel.sharedStrings"},comments:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml",xlsb:"application/vnd.ms-excel.comments"},sheets:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml",xlsb:"application/vnd.ms-excel.worksheet"},charts:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml",xlsb:"application/vnd.ms-excel.chartsheet"},dialogs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml",xlsb:"application/vnd.ms-excel.dialogsheet"},macros:{xlsx:"application/vnd.ms-excel.macrosheet+xml",xlsb:"application/vnd.ms-excel.macrosheet"},metadata:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml",xlsb:"application/vnd.ms-excel.sheetMetadata"},styles:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml",xlsb:"application/vnd.ms-excel.styles"}};function ra(){return{workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""}}function na(e,t){var r,n=tt(ea),a=[];a[a.length]=wt,a[a.length]=Gt("Types",null,{xmlns:zt.CT,"xmlns:xsd":zt.xsd,"xmlns:xsi":zt.xsi}),a=a.concat([["xml","application/xml"],["bin","application/vnd.ms-excel.sheet.binary.macroEnabled.main"],["vml","application/vnd.openxmlformats-officedocument.vmlDrawing"],["data","application/vnd.openxmlformats-officedocument.model+data"],["bmp","image/bmp"],["png","image/png"],["gif","image/gif"],["emf","image/x-emf"],["wmf","image/x-wmf"],["jpg","image/jpeg"],["jpeg","image/jpeg"],["tif","image/tiff"],["tiff","image/tiff"],["pdf","application/pdf"],["rels","application/vnd.openxmlformats-package.relationships+xml"]].map((function(e){return Gt("Default",null,{Extension:e[0],ContentType:e[1]})})));var i=function(n){e[n]&&e[n].length>0&&(r=e[n][0],a[a.length]=Gt("Override",null,{PartName:("/"==r[0]?"":"/")+r,ContentType:ta[n][t.bookType]||ta[n]["xlsx"]}))},s=function(r){(e[r]||[]).forEach((function(e){a[a.length]=Gt("Override",null,{PartName:("/"==e[0]?"":"/")+e,ContentType:ta[r][t.bookType]||ta[r]["xlsx"]})}))},o=function(t){(e[t]||[]).forEach((function(e){a[a.length]=Gt("Override",null,{PartName:("/"==e[0]?"":"/")+e,ContentType:n[t][0]})}))};return i("workbooks"),s("sheets"),s("charts"),o("themes"),["strs","styles"].forEach(i),["coreprops","extprops","custprops"].forEach(o),o("vba"),o("comments"),o("threadedcomments"),o("drawings"),s("metadata"),o("people"),a.length>2&&(a[a.length]="</Types>",a[1]=a[1].replace("/>",">")),a.join("")}var aa={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",SHEET:"http://sheetjs.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",XLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLink",CXML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXml",CXMLP:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXmlProps",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",CHART:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chart",CHARTEX:"http://schemas.microsoft.com/office/2014/relationships/chartEx",CS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chartsheet",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/dialogsheet",MS:"http://schemas.microsoft.com/office/2006/relationships/xlMacrosheet",IMG:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function ia(e){var t=e.lastIndexOf("/");return e.slice(0,t+1)+"_rels/"+e.slice(t+1)+".rels"}function sa(e){var t=[wt,Gt("Relationships",null,{xmlns:zt.RELS})];return Ze(e["!id"]).forEach((function(r){t[t.length]=Gt("Relationship",null,e["!id"][r])})),t.length>2&&(t[t.length]="</Relationships>",t[1]=t[1].replace("/>",">")),t.join("")}function oa(e,t,r,n,a,i){if(a||(a={}),e["!id"]||(e["!id"]={}),e["!idx"]||(e["!idx"]=1),t<0)for(t=e["!idx"];e["!id"]["rId"+t];++t);if(e["!idx"]=t+1,a.Id="rId"+t,a.Type=n,a.Target=r,i?a.TargetMode=i:[aa.HLINK,aa.XPATH,aa.XMISS].indexOf(a.Type)>-1&&(a.TargetMode="External"),e["!id"][a.Id])throw new Error("Cannot rewrite rId "+t);return e["!id"][a.Id]=a,e[("/"+a.Target).replace("//","/")]=a,t}function fa(e){var t=[wt];t.push('<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0" manifest:version="1.2">\n'),t.push('  <manifest:file-entry manifest:full-path="/" manifest:version="1.2" manifest:media-type="application/vnd.oasis.opendocument.spreadsheet"/>\n');for(var r=0;r<e.length;++r)t.push('  <manifest:file-entry manifest:full-path="'+e[r][0]+'" manifest:media-type="'+e[r][1]+'"/>\n');return t.push("</manifest:manifest>"),t.join("")}function la(e,t,r){return['  <rdf:Description rdf:about="'+e+'">\n','    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/'+(r||"odf")+"#"+t+'"/>\n',"  </rdf:Description>\n"].join("")}function ca(e,t){return['  <rdf:Description rdf:about="'+e+'">\n','    <ns0:hasPart xmlns:ns0="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#" rdf:resource="'+t+'"/>\n',"  </rdf:Description>\n"].join("")}function ha(e){var t=[wt];t.push('<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">\n');for(var r=0;r!=e.length;++r)t.push(la(e[r][0],e[r][1])),t.push(ca("",e[r][0]));return t.push(la("","Document","pkg")),t.push("</rdf:RDF>"),t.join("")}function ua(){return'<office:document-meta xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:xlink="http://www.w3.org/1999/xlink" office:version="1.2"><office:meta><meta:generator>SheetJS '+n.version+"</meta:generator></office:meta></office:document-meta>"}var da=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]];function pa(e,t,r,n,a){null==a[e]&&null!=t&&""!==t&&(a[e]=t,t=_t(t),n[n.length]=r?Gt(e,t,r):Wt(e,t))}function ma(e,t){var r=t||{},n=[wt,Gt("cp:coreProperties",null,{"xmlns:cp":zt.CORE_PROPS,"xmlns:dc":zt.dc,"xmlns:dcterms":zt.dcterms,"xmlns:dcmitype":zt.dcmitype,"xmlns:xsi":zt.xsi})],a={};if(!e&&!r.Props)return n.join("");e&&(null!=e.CreatedDate&&pa("dcterms:created","string"===typeof e.CreatedDate?e.CreatedDate:Vt(e.CreatedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},n,a),null!=e.ModifiedDate&&pa("dcterms:modified","string"===typeof e.ModifiedDate?e.ModifiedDate:Vt(e.ModifiedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},n,a));for(var i=0;i!=da.length;++i){var s=da[i],o=r.Props&&null!=r.Props[s[1]]?r.Props[s[1]]:e?e[s[1]]:null;!0===o?o="1":!1===o?o="0":"number"==typeof o&&(o=String(o)),null!=o&&pa(s[0],o,null,n,a)}return n.length>2&&(n[n.length]="</cp:coreProperties>",n[1]=n[1].replace("/>",">")),n.join("")}var ga=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]],va=["Worksheets","SheetNames","NamedRanges","DefinedNames","Chartsheets","ChartNames"];function Ta(e){var t=[],r=Gt;return e||(e={}),e.Application="SheetJS",t[t.length]=wt,t[t.length]=Gt("Properties",null,{xmlns:zt.EXT_PROPS,"xmlns:vt":zt.vt}),ga.forEach((function(n){if(void 0!==e[n[1]]){var a;switch(n[2]){case"string":a=_t(String(e[n[1]]));break;case"bool":a=e[n[1]]?"true":"false";break}void 0!==a&&(t[t.length]=r(n[0],a))}})),t[t.length]=r("HeadingPairs",r("vt:vector",r("vt:variant","<vt:lpstr>Worksheets</vt:lpstr>")+r("vt:variant",r("vt:i4",String(e.Worksheets))),{size:2,baseType:"variant"})),t[t.length]=r("TitlesOfParts",r("vt:vector",e.SheetNames.map((function(e){return"<vt:lpstr>"+_t(e)+"</vt:lpstr>"})).join(""),{size:e.Worksheets,baseType:"lpstr"})),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}function ba(e){var t=[wt,Gt("Properties",null,{xmlns:zt.CUST_PROPS,"xmlns:vt":zt.vt})];if(!e)return t.join("");var r=1;return Ze(e).forEach((function(n){++r,t[t.length]=Gt("property",jt(e[n],!0),{fmtid:"{D5CDD505-2E9C-101B-9397-08002B2CF9AE}",pid:r,name:_t(n)})})),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var wa={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"};function Ea(e,t){var r=[];return Ze(wa).map((function(e){for(var t=0;t<da.length;++t)if(da[t][1]==e)return da[t];for(t=0;t<ga.length;++t)if(ga[t][1]==e)return ga[t];throw e})).forEach((function(n){if(null!=e[n[1]]){var a=t&&t.Props&&null!=t.Props[n[1]]?t.Props[n[1]]:e[n[1]];switch(n[2]){case"date":a=new Date(a).toISOString().replace(/\.\d*Z/,"Z");break}"number"==typeof a?a=String(a):!0===a||!1===a?a=a?"1":"0":a instanceof Date&&(a=new Date(a).toISOString().replace(/\.\d*Z/,"")),r.push(Wt(wa[n[1]]||n[1],a))}})),Gt("DocumentProperties",r.join(""),{xmlns:Yt.o})}function Aa(e,t){var r=["Worksheets","SheetNames"],n="CustomDocumentProperties",a=[];return e&&Ze(e).forEach((function(t){if(Object.prototype.hasOwnProperty.call(e,t)){for(var n=0;n<da.length;++n)if(t==da[n][1])return;for(n=0;n<ga.length;++n)if(t==ga[n][1])return;for(n=0;n<r.length;++n)if(t==r[n])return;var i=e[t],s="string";"number"==typeof i?(s="float",i=String(i)):!0===i||!1===i?(s="boolean",i=i?"1":"0"):i=String(i),a.push(Gt(xt(t),i,{"dt:dt":s}))}})),t&&Ze(t).forEach((function(r){if(Object.prototype.hasOwnProperty.call(t,r)&&(!e||!Object.prototype.hasOwnProperty.call(e,r))){var n=t[r],i="string";"number"==typeof n?(i="float",n=String(n)):!0===n||!1===n?(i="boolean",n=n?"1":"0"):n instanceof Date?(i="dateTime.tz",n=n.toISOString()):n=String(n),a.push(Gt(xt(r),n,{"dt:dt":i}))}})),"<"+n+' xmlns="'+Yt.o+'">'+a.join("")+"</"+n+">"}function ya(e){var t="string"==typeof e?new Date(Date.parse(e)):e,r=t.getTime()/1e3+11644473600,n=r%Math.pow(2,32),a=(r-n)/Math.pow(2,32);n*=1e7,a*=1e7;var i=n/Math.pow(2,32)|0;i>0&&(n%=Math.pow(2,32),a+=i);var s=Nr(8);return s.write_shift(4,n),s.write_shift(4,a),s}function Sa(e,t){var r=Nr(4),n=Nr(4);switch(r.write_shift(4,80==e?31:e),e){case 3:n.write_shift(-4,t);break;case 5:n=Nr(8),n.write_shift(8,t,"f");break;case 11:n.write_shift(4,t?1:0);break;case 64:n=ya(t);break;case 31:case 80:n=Nr(4+2*(t.length+1)+(t.length%2?0:2)),n.write_shift(4,t.length+1),n.write_shift(0,t,"dbcs");while(n.l!=n.length)n.write_shift(1,0);break;default:throw new Error("TypedPropertyValue unrecognized type "+e+" "+t)}return R([r,n])}var _a=["CodePage","Thumbnail","_PID_LINKBASE","_PID_HLINKS","SystemIdentifier","FMTID"];function xa(e){switch(typeof e){case"boolean":return 11;case"number":return(0|e)==e?3:5;case"string":return 31;case"object":if(e instanceof Date)return 64;break}return-1}function Oa(e,t,r){var n=Nr(8),a=[],i=[],s=8,o=0,f=Nr(8),l=Nr(8);if(f.write_shift(4,2),f.write_shift(4,1200),l.write_shift(4,1),i.push(f),a.push(l),s+=8+f.length,!t){l=Nr(8),l.write_shift(4,0),a.unshift(l);var c=[Nr(4)];for(c[0].write_shift(4,e.length),o=0;o<e.length;++o){var h=e[o][0];f=Nr(8+2*(h.length+1)+(h.length%2?0:2)),f.write_shift(4,o+2),f.write_shift(4,h.length+1),f.write_shift(0,h,"dbcs");while(f.l!=f.length)f.write_shift(1,0);c.push(f)}f=R(c),i.unshift(f),s+=8+f.length}for(o=0;o<e.length;++o)if((!t||t[e[o][0]])&&!(_a.indexOf(e[o][0])>-1||va.indexOf(e[o][0])>-1)&&null!=e[o][1]){var u=e[o][1],d=0;if(t){d=+t[e[o][0]];var p=r[d];if("version"==p.p&&"string"==typeof u){var m=u.split(".");u=(+m[0]<<16)+(+m[1]||0)}f=Sa(p.t,u)}else{var g=xa(u);-1==g&&(g=31,u=String(u)),f=Sa(g,u)}i.push(f),l=Nr(8),l.write_shift(4,t?d:2+o),a.push(l),s+=8+f.length}var v=8*(i.length+1);for(o=0;o<i.length;++o)a[o].write_shift(4,v),v+=i[o].length;return n.write_shift(4,s),n.write_shift(4,i.length),R([n].concat(a).concat(i))}function Ca(e,t,r,n,a,i){var s=Nr(a?68:48),o=[s];s.write_shift(2,65534),s.write_shift(2,0),s.write_shift(4,842412599),s.write_shift(16,Ye.utils.consts.HEADER_CLSID,"hex"),s.write_shift(4,a?2:1),s.write_shift(16,t,"hex"),s.write_shift(4,a?68:48);var f=Oa(e,r,n);if(o.push(f),a){var l=Oa(a,null,null);s.write_shift(16,i,"hex"),s.write_shift(4,68+f.length),o.push(l)}return R(o)}function Ra(e,t){t||(t=Nr(e));for(var r=0;r<e;++r)t.write_shift(1,0);return t}function ka(e,t){return 1===e.read_shift(t)}function Ia(e,t){return t||(t=Nr(2)),t.write_shift(2,+!!e),t}function Na(e){return e.read_shift(2,"u")}function Da(e,t){return t||(t=Nr(2)),t.write_shift(2,e),t}function Pa(e,t,r){return r||(r=Nr(2)),r.write_shift(1,"e"==t?+e:+!!e),r.write_shift(1,"e"==t?1:0),r}function La(e,t,r){var n=e.read_shift(r&&r.biff>=12?2:1),i="sbcs-cont",s=a;if(r&&r.biff>=8&&(a=1200),r&&8!=r.biff)12==r.biff&&(i="wstr");else{var o=e.read_shift(1);o&&(i="dbcs-cont")}r.biff>=2&&r.biff<=5&&(i="cpstr");var f=n?e.read_shift(n,i):"";return a=s,f}function Ma(e){var t=e.t||"",r=1,n=Nr(3+(r>1?2:0));n.write_shift(2,t.length),n.write_shift(1,1|(r>1?8:0)),r>1&&n.write_shift(2,r);var a=Nr(2*t.length);a.write_shift(2*t.length,t,"utf16le");var i=[n,a];return R(i)}function Fa(e,t,r){var n;if(r){if(r.biff>=2&&r.biff<=5)return e.read_shift(t,"cpstr");if(r.biff>=12)return e.read_shift(t,"dbcs-cont")}var a=e.read_shift(1);return n=0===a?e.read_shift(t,"sbcs-cont"):e.read_shift(t,"dbcs-cont"),n}function Ua(e,t,r){var n=e.read_shift(r&&2==r.biff?1:2);return 0===n?(e.l++,""):Fa(e,n,r)}function Ba(e,t,r){if(r.biff>5)return Ua(e,t,r);var n=e.read_shift(1);return 0===n?(e.l++,""):e.read_shift(n,r.biff<=4||!e.lens?"cpstr":"sbcs-cont")}function Wa(e,t,r){return r||(r=Nr(3+2*e.length)),r.write_shift(2,e.length),r.write_shift(1,1),r.write_shift(31,e,"utf16le"),r}function Ha(e,t){t||(t=Nr(6+2*e.length)),t.write_shift(4,1+e.length);for(var r=0;r<e.length;++r)t.write_shift(2,e.charCodeAt(r));return t.write_shift(2,0),t}function Ga(e){var t=Nr(512),r=0,n=e.Target;"file://"==n.slice(0,7)&&(n=n.slice(7));var a=n.indexOf("#"),i=a>-1?31:23;switch(n.charAt(0)){case"#":i=28;break;case".":i&=-3;break}t.write_shift(4,2),t.write_shift(4,i);var s=[8,6815827,6619237,4849780,83];for(r=0;r<s.length;++r)t.write_shift(4,s[r]);if(28==i)n=n.slice(1),Ha(n,t);else if(2&i){for(s="e0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),r=0;r<s.length;++r)t.write_shift(1,parseInt(s[r],16));var o=a>-1?n.slice(0,a):n;for(t.write_shift(4,2*(o.length+1)),r=0;r<o.length;++r)t.write_shift(2,o.charCodeAt(r));t.write_shift(2,0),8&i&&Ha(a>-1?n.slice(a+1):"",t)}else{for(s="03 03 00 00 00 00 00 00 c0 00 00 00 00 00 00 46".split(" "),r=0;r<s.length;++r)t.write_shift(1,parseInt(s[r],16));var f=0;while("../"==n.slice(3*f,3*f+3)||"..\\"==n.slice(3*f,3*f+3))++f;for(t.write_shift(2,f),t.write_shift(4,n.length-3*f+1),r=0;r<n.length-3*f;++r)t.write_shift(1,255&n.charCodeAt(r+3*f));for(t.write_shift(1,0),t.write_shift(2,65535),t.write_shift(2,57005),r=0;r<6;++r)t.write_shift(4,0)}return t.slice(0,t.l)}function Va(e,t,r,n){return n||(n=Nr(6)),n.write_shift(2,e),n.write_shift(2,t),n.write_shift(2,r||0),n}function ja(e,t,r){var n=r.biff>8?4:2,a=e.read_shift(n),i=e.read_shift(n,"i"),s=e.read_shift(n,"i");return[a,i,s]}function za(e){var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(2),a=e.read_shift(2);return{s:{c:n,r:t},e:{c:a,r:r}}}function $a(e,t){return t||(t=Nr(8)),t.write_shift(2,e.s.r),t.write_shift(2,e.e.r),t.write_shift(2,e.s.c),t.write_shift(2,e.e.c),t}function Ya(e,t,r){var n=1536,a=16;switch(r.bookType){case"biff8":break;case"biff5":n=1280,a=8;break;case"biff4":n=4,a=6;break;case"biff3":n=3,a=6;break;case"biff2":n=2,a=4;break;case"xla":break;default:throw new Error("unsupported BIFF version")}var i=Nr(a);return i.write_shift(2,n),i.write_shift(2,t),a>4&&i.write_shift(2,29282),a>6&&i.write_shift(2,1997),a>8&&(i.write_shift(2,49161),i.write_shift(2,1),i.write_shift(2,1798),i.write_shift(2,0)),i}function Xa(e,t){var r=!t||8==t.biff,n=Nr(r?112:54);n.write_shift(8==t.biff?2:1,7),r&&n.write_shift(1,0),n.write_shift(4,859007059),n.write_shift(4,5458548|(r?0:536870912));while(n.l<n.length)n.write_shift(1,r?0:32);return n}function Ka(e,t){var r=!t||t.biff>=8?2:1,n=Nr(8+r*e.name.length);n.write_shift(4,e.pos),n.write_shift(1,e.hs||0),n.write_shift(1,e.dt),n.write_shift(1,e.name.length),t.biff>=8&&n.write_shift(1,1),n.write_shift(r*e.name.length,e.name,t.biff<8?"sbcs":"utf16le");var a=n.slice(0,n.l);return a.l=n.l,a}function Ja(e,t){var r=Nr(8);r.write_shift(4,e.Count),r.write_shift(4,e.Unique);for(var n=[],a=0;a<e.length;++a)n[a]=Ma(e[a],t);var i=R([r].concat(n));return i.parts=[r.length].concat(n.map((function(e){return e.length}))),i}function Za(){var e=Nr(18);return e.write_shift(2,0),e.write_shift(2,0),e.write_shift(2,29280),e.write_shift(2,17600),e.write_shift(2,56),e.write_shift(2,0),e.write_shift(2,0),e.write_shift(2,1),e.write_shift(2,500),e}function qa(e){var t=Nr(18),r=1718;return e&&e.RTL&&(r|=64),t.write_shift(2,r),t.write_shift(4,0),t.write_shift(4,64),t.write_shift(4,0),t.write_shift(4,0),t}function Qa(e,t){var r=e.name||"Arial",n=t&&5==t.biff,a=n?15+r.length:16+2*r.length,i=Nr(a);return i.write_shift(2,20*(e.sz||12)),i.write_shift(4,0),i.write_shift(2,400),i.write_shift(4,0),i.write_shift(2,0),i.write_shift(1,r.length),n||i.write_shift(1,1),i.write_shift((n?1:2)*r.length,r,n?"sbcs":"utf16le"),i}function ei(e,t,r,n){var a=Nr(10);return Va(e,t,n,a),a.write_shift(4,r),a}function ti(e,t,r,n,a){var i=!a||8==a.biff,s=Nr(+i+8+(1+i)*r.length);return Va(e,t,n,s),s.write_shift(2,r.length),i&&s.write_shift(1,1),s.write_shift((1+i)*r.length,r,i?"utf16le":"sbcs"),s}function ri(e,t,r,n){var a=r&&5==r.biff;n||(n=Nr(a?3+t.length:5+2*t.length)),n.write_shift(2,e),n.write_shift(a?1:2,t.length),a||n.write_shift(1,1),n.write_shift((a?1:2)*t.length,t,a?"sbcs":"utf16le");var i=n.length>n.l?n.slice(0,n.l):n;return null==i.l&&(i.l=i.length),i}function ni(e,t){var r=8!=t.biff&&t.biff?2:4,n=Nr(2*r+6);return n.write_shift(r,e.s.r),n.write_shift(r,e.e.r+1),n.write_shift(2,e.s.c),n.write_shift(2,e.e.c+1),n.write_shift(2,0),n}function ai(e,t,r,n){var a=r&&5==r.biff;n||(n=Nr(a?16:20)),n.write_shift(2,0),e.style?(n.write_shift(2,e.numFmtId||0),n.write_shift(2,65524)):(n.write_shift(2,e.numFmtId||0),n.write_shift(2,t<<4));var i=0;return e.numFmtId>0&&a&&(i|=1024),n.write_shift(4,i),n.write_shift(4,0),a||n.write_shift(4,0),n.write_shift(2,0),n}function ii(e){var t=Nr(8);return t.write_shift(4,0),t.write_shift(2,e[0]?e[0]+1:0),t.write_shift(2,e[1]?e[1]+1:0),t}function si(e,t,r,n,a,i){var s=Nr(8);return Va(e,t,n,s),Pa(r,i,s),s}function oi(e,t,r,n){var a=Nr(14);return Va(e,t,n,a),Dn(r,a),a}function fi(e,t,r){if(r.biff<8)return li(e,t,r);var n=[],a=e.l+t,i=e.read_shift(r.biff>8?4:2);while(0!==i--)n.push(ja(e,r.biff>8?12:6,r));if(e.l!=a)throw new Error("Bad ExternSheet: "+e.l+" != "+a);return n}function li(e,t,r){3==e[e.l+1]&&e[e.l]++;var n=La(e,t,r);return 3==n.charCodeAt(0)?n.slice(1):n}function ci(e){var t=Nr(2+8*e.length);t.write_shift(2,e.length);for(var r=0;r<e.length;++r)$a(e[r],t);return t}function hi(e){var t=Nr(24),r=Xr(e[0]);t.write_shift(2,r.r),t.write_shift(2,r.r),t.write_shift(2,r.c),t.write_shift(2,r.c);for(var n="d0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),a=0;a<16;++a)t.write_shift(1,parseInt(n[a],16));return R([t,Ga(e[1])])}function ui(e){var t=e[1].Tooltip,r=Nr(10+2*(t.length+1));r.write_shift(2,2048);var n=Xr(e[0]);r.write_shift(2,n.r),r.write_shift(2,n.r),r.write_shift(2,n.c),r.write_shift(2,n.c);for(var a=0;a<t.length;++a)r.write_shift(2,t.charCodeAt(a));return r.write_shift(2,0),r}function di(e){return e||(e=Nr(4)),e.write_shift(2,1),e.write_shift(2,1),e}function pi(e,t,r){if(!r.cellStyles)return Ir(e,t);var n=r&&r.biff>=12?4:2,a=e.read_shift(n),i=e.read_shift(n),s=e.read_shift(n),o=e.read_shift(n),f=e.read_shift(2);2==n&&(e.l+=2);var l={s:a,e:i,w:s,ixfe:o,flags:f};return(r.biff>=5||!r.biff)&&(l.level=f>>8&7),l}function mi(e,t){var r=Nr(12);r.write_shift(2,t),r.write_shift(2,t),r.write_shift(2,256*e.width),r.write_shift(2,0);var n=0;return e.hidden&&(n|=1),r.write_shift(1,n),n=e.level||0,r.write_shift(1,n),r.write_shift(2,0),r}function gi(e){for(var t=Nr(2*e),r=0;r<e;++r)t.write_shift(2,r+1);return t}function vi(e,t,r){var n=Nr(15);return oh(n,e,t),n.write_shift(8,r,"f"),n}function Ti(e,t,r){var n=Nr(9);return oh(n,e,t),n.write_shift(2,r),n}var bi=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},t=Qe({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function r(t,r){var n=[],a=y(1);switch(r.type){case"base64":a=_(w(t));break;case"binary":a=_(t);break;case"buffer":case"array":a=t;break}kr(a,0);var i=a.read_shift(1),s=!!(136&i),o=!1,f=!1;switch(i){case 2:break;case 3:break;case 48:o=!0,s=!0;break;case 49:o=!0,s=!0;break;case 131:break;case 139:break;case 140:f=!0;break;case 245:break;default:throw new Error("DBF Unsupported Version: "+i.toString(16))}var l=0,c=521;2==i&&(l=a.read_shift(2)),a.l+=3,2!=i&&(l=a.read_shift(4)),l>1048576&&(l=1e6),2!=i&&(c=a.read_shift(2));var h=a.read_shift(2),u=r.codepage||1252;2!=i&&(a.l+=16,a.read_shift(1),0!==a[a.l]&&(u=e[a[a.l]]),a.l+=1,a.l+=2),f&&(a.l+=36);var p=[],m={},g=Math.min(a.length,2==i?521:c-10-(o?264:0)),v=f?32:11;while(a.l<g&&13!=a[a.l])switch(m={},m.name=d.utils.decode(u,a.slice(a.l,a.l+v)).replace(/[\u0000\r\n].*$/g,""),a.l+=v,m.type=String.fromCharCode(a.read_shift(1)),2==i||f||(m.offset=a.read_shift(4)),m.len=a.read_shift(1),2==i&&(m.offset=a.read_shift(2)),m.dec=a.read_shift(1),m.name.length&&p.push(m),2!=i&&(a.l+=f?13:14),m.type){case"B":o&&8==m.len||!r.WTF||console.log("Skipping "+m.name+":"+m.type);break;case"G":case"P":r.WTF&&console.log("Skipping "+m.name+":"+m.type);break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw new Error("Unknown Field Type: "+m.type)}if(13!==a[a.l]&&(a.l=c-1),13!==a.read_shift(1))throw new Error("DBF Terminator not found "+a.l+" "+a[a.l]);a.l=c;var T=0,b=0;for(n[0]=[],b=0;b!=p.length;++b)n[0][b]=p[b].name;while(l-- >0)if(42!==a[a.l])for(++a.l,n[++T]=[],b=0,b=0;b!=p.length;++b){var E=a.slice(a.l,a.l+p[b].len);a.l+=p[b].len,kr(E,0);var A=d.utils.decode(u,E);switch(p[b].type){case"C":A.trim().length&&(n[T][b]=A.replace(/\s+$/,""));break;case"D":8===A.length?n[T][b]=new Date(+A.slice(0,4),+A.slice(4,6)-1,+A.slice(6,8)):n[T][b]=A;break;case"F":n[T][b]=parseFloat(A.trim());break;case"+":case"I":n[T][b]=f?2147483648^E.read_shift(-4,"i"):E.read_shift(4,"i");break;case"L":switch(A.trim().toUpperCase()){case"Y":case"T":n[T][b]=!0;break;case"N":case"F":n[T][b]=!1;break;case"":case"?":break;default:throw new Error("DBF Unrecognized L:|"+A+"|")}break;case"M":if(!s)throw new Error("DBF Unexpected MEMO for type "+i.toString(16));n[T][b]="##MEMO##"+(f?parseInt(A.trim(),10):E.read_shift(4));break;case"N":A=A.replace(/\u0000/g,"").trim(),A&&"."!=A&&(n[T][b]=+A||0);break;case"@":n[T][b]=new Date(E.read_shift(-8,"f")-621356832e5);break;case"T":n[T][b]=new Date(864e5*(E.read_shift(4)-2440588)+E.read_shift(4));break;case"Y":n[T][b]=E.read_shift(4,"i")/1e4+E.read_shift(4,"i")/1e4*Math.pow(2,32);break;case"O":n[T][b]=-E.read_shift(-8,"f");break;case"B":if(o&&8==p[b].len){n[T][b]=E.read_shift(8,"f");break}case"G":case"P":E.l+=p[b].len;break;case"0":if("_NullFlags"===p[b].name)break;default:throw new Error("DBF Unsupported data type "+p[b].type)}}else a.l+=h;if(2!=i&&a.l<a.length&&26!=a[a.l++])throw new Error("DBF EOF Marker missing "+(a.l-1)+" of "+a.length+" "+a[a.l-1].toString(16));return r&&r.sheetRows&&(n=n.slice(0,r.sheetRows)),r.DBF=p,n}function n(e,t){var n=t||{};n.dateNF||(n.dateNF="yyyymmdd");var a=nn(r(e,n),n);return a["!cols"]=n.DBF.map((function(e){return{wch:e.len,DBF:e}})),delete n.DBF,a}function a(e,t){try{return tn(n(e,t),t)}catch(r){if(t&&t.WTF)throw r}return{SheetNames:[],Sheets:{}}}var s={B:8,C:250,L:1,D:8,"?":0,"":0};function o(e,r){var n=r||{};if(+n.codepage>=0&&c(+n.codepage),"string"==n.type)throw new Error("Cannot write DBF to JS string");var a=Dr(),o=Tu(e,{header:1,raw:!0,cellDates:!0}),f=o[0],l=o.slice(1),h=e["!cols"]||[],u=0,d=0,p=0,m=1;for(u=0;u<f.length;++u)if(((h[u]||{}).DBF||{}).name)f[u]=h[u].DBF.name,++p;else if(null!=f[u]){if(++p,"number"===typeof f[u]&&(f[u]=f[u].toString(10)),"string"!==typeof f[u])throw new Error("DBF Invalid column name "+f[u]+" |"+typeof f[u]+"|");if(f.indexOf(f[u])!==u)for(d=0;d<1024;++d)if(-1==f.indexOf(f[u]+"_"+d)){f[u]+="_"+d;break}}var g=qr(e["!ref"]),v=[],T=[],b=[];for(u=0;u<=g.e.c-g.s.c;++u){var w="",E="",A=0,y=[];for(d=0;d<l.length;++d)null!=l[d][u]&&y.push(l[d][u]);if(0!=y.length&&null!=f[u]){for(d=0;d<y.length;++d){switch(typeof y[d]){case"number":E="B";break;case"string":E="C";break;case"boolean":E="L";break;case"object":E=y[d]instanceof Date?"D":"C";break;default:E="C"}A=Math.max(A,String(y[d]).length),w=w&&w!=E?"C":E}A>250&&(A=250),E=((h[u]||{}).DBF||{}).type,"C"==E&&h[u].DBF.len>A&&(A=h[u].DBF.len),"B"==w&&"N"==E&&(w="N",b[u]=h[u].DBF.dec,A=h[u].DBF.len),T[u]="C"==w||"N"==E?A:s[w]||0,m+=T[u],v[u]=w}else v[u]="?"}var S=a.next(32);for(S.write_shift(4,318902576),S.write_shift(4,l.length),S.write_shift(2,296+32*p),S.write_shift(2,m),u=0;u<4;++u)S.write_shift(4,0);for(S.write_shift(4,0|(+t[i]||3)<<8),u=0,d=0;u<f.length;++u)if(null!=f[u]){var _=a.next(32),x=(f[u].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);_.write_shift(1,x,"sbcs"),_.write_shift(1,"?"==v[u]?"C":v[u],"sbcs"),_.write_shift(4,d),_.write_shift(1,T[u]||s[v[u]]||0),_.write_shift(1,b[u]||0),_.write_shift(1,2),_.write_shift(4,0),_.write_shift(1,0),_.write_shift(4,0),_.write_shift(4,0),d+=T[u]||s[v[u]]||0}var O=a.next(264);for(O.write_shift(4,13),u=0;u<65;++u)O.write_shift(4,0);for(u=0;u<l.length;++u){var C=a.next(m);for(C.write_shift(1,0),d=0;d<f.length;++d)if(null!=f[d])switch(v[d]){case"L":C.write_shift(1,null==l[u][d]?63:l[u][d]?84:70);break;case"B":C.write_shift(8,l[u][d]||0,"f");break;case"N":var R="0";for("number"==typeof l[u][d]&&(R=l[u][d].toFixed(b[d]||0)),p=0;p<T[d]-R.length;++p)C.write_shift(1,32);C.write_shift(1,R,"sbcs");break;case"D":l[u][d]?(C.write_shift(4,("0000"+l[u][d].getFullYear()).slice(-4),"sbcs"),C.write_shift(2,("00"+(l[u][d].getMonth()+1)).slice(-2),"sbcs"),C.write_shift(2,("00"+l[u][d].getDate()).slice(-2),"sbcs")):C.write_shift(8,"00000000","sbcs");break;case"C":var k=String(null!=l[u][d]?l[u][d]:"").slice(0,T[d]);for(C.write_shift(1,k,"sbcs"),p=0;p<T[d]-k.length;++p)C.write_shift(1,32);break}}return a.next(1).write_shift(1,26),a.end()}return{to_workbook:a,to_sheet:n,from_sheet:o}}(),wi=function(){var e={AA:"À",BA:"Á",CA:"Â",DA:195,HA:"Ä",JA:197,AE:"È",BE:"É",CE:"Ê",HE:"Ë",AI:"Ì",BI:"Í",CI:"Î",HI:"Ï",AO:"Ò",BO:"Ó",CO:"Ô",DO:213,HO:"Ö",AU:"Ù",BU:"Ú",CU:"Û",HU:"Ü",Aa:"à",Ba:"á",Ca:"â",Da:227,Ha:"ä",Ja:229,Ae:"è",Be:"é",Ce:"ê",He:"ë",Ai:"ì",Bi:"í",Ci:"î",Hi:"ï",Ao:"ò",Bo:"ó",Co:"ô",Do:245,Ho:"ö",Au:"ù",Bu:"ú",Cu:"û",Hu:"ü",KC:"Ç",Kc:"ç",q:"æ",z:"œ",a:"Æ",j:"Œ",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},t=new RegExp("N("+Ze(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),r=function(t,r){var n=e[r];return"number"==typeof n?m(n):n},n=function(e,t,r){var n=t.charCodeAt(0)-32<<4|r.charCodeAt(0)-48;return 59==n?e:m(n)};function a(e,t){switch(t.type){case"base64":return i(w(e),t);case"binary":return i(e,t);case"buffer":return i(E&&Buffer.isBuffer(e)?e.toString("binary"):O(e),t);case"array":return i(ut(e),t)}throw new Error("Unrecognized type "+t.type)}function i(e,a){var i,s=e.split(/[\n\r]+/),o=-1,f=-1,l=0,h=0,u=[],p=[],m=null,g={},v=[],T=[],b=[],w=0;for(+a.codepage>=0&&c(+a.codepage);l!==s.length;++l){w=0;var E,A=s[l].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,n).replace(t,r),y=A.replace(/;;/g,"\0").split(";").map((function(e){return e.replace(/\u0000/g,";")})),S=y[0];if(A.length>0)switch(S){case"ID":break;case"E":break;case"B":break;case"O":break;case"W":break;case"P":"P"==y[1].charAt(0)&&p.push(A.slice(3).replace(/;;/g,";"));break;case"C":var _=!1,x=!1,O=!1,C=!1,R=-1,k=-1;for(h=1;h<y.length;++h)switch(y[h].charAt(0)){case"A":break;case"X":f=parseInt(y[h].slice(1))-1,x=!0;break;case"Y":for(o=parseInt(y[h].slice(1))-1,x||(f=0),i=u.length;i<=o;++i)u[i]=[];break;case"K":E=y[h].slice(1),'"'===E.charAt(0)?E=E.slice(1,E.length-1):"TRUE"===E?E=!0:"FALSE"===E?E=!1:isNaN(mt(E))?isNaN(vt(E).getDate())||(E=ht(E)):(E=mt(E),null!==m&&De(m)&&(E=ot(E))),"undefined"!==typeof d&&"string"==typeof E&&"string"!=(a||{}).type&&(a||{}).codepage&&(E=d.utils.decode(a.codepage,E)),_=!0;break;case"E":C=!0;var I=Vs(y[h].slice(1),{r:o,c:f});u[o][f]=[u[o][f],I];break;case"S":O=!0,u[o][f]=[u[o][f],"S5S"];break;case"G":break;case"R":R=parseInt(y[h].slice(1))-1;break;case"C":k=parseInt(y[h].slice(1))-1;break;default:if(a&&a.WTF)throw new Error("SYLK bad record "+A)}if(_&&(u[o][f]&&2==u[o][f].length?u[o][f][0]=E:u[o][f]=E,m=null),O){if(C)throw new Error("SYLK shared formula cannot have own formula");var N=R>-1&&u[R][k];if(!N||!N[1])throw new Error("SYLK shared formula cannot find base");u[o][f][1]=$s(N[1],{r:o-R,c:f-k})}break;case"F":var D=0;for(h=1;h<y.length;++h)switch(y[h].charAt(0)){case"X":f=parseInt(y[h].slice(1))-1,++D;break;case"Y":for(o=parseInt(y[h].slice(1))-1,i=u.length;i<=o;++i)u[i]=[];break;case"M":w=parseInt(y[h].slice(1))/20;break;case"F":break;case"G":break;case"P":m=p[parseInt(y[h].slice(1))];break;case"S":break;case"D":break;case"N":break;case"W":for(b=y[h].slice(1).split(" "),i=parseInt(b[0],10);i<=parseInt(b[1],10);++i)w=parseInt(b[2],10),T[i-1]=0===w?{hidden:!0}:{wch:w},Wi(T[i-1]);break;case"C":f=parseInt(y[h].slice(1))-1,T[f]||(T[f]={});break;case"R":o=parseInt(y[h].slice(1))-1,v[o]||(v[o]={}),w>0?(v[o].hpt=w,v[o].hpx=ji(w)):0===w&&(v[o].hidden=!0);break;default:if(a&&a.WTF)throw new Error("SYLK bad record "+A)}D<1&&(m=null);break;default:if(a&&a.WTF)throw new Error("SYLK bad record "+A)}}return v.length>0&&(g["!rows"]=v),T.length>0&&(g["!cols"]=T),a&&a.sheetRows&&(u=u.slice(0,a.sheetRows)),[u,g]}function s(e,t){var r=a(e,t),n=r[0],i=r[1],s=nn(n,t);return Ze(i).forEach((function(e){s[e]=i[e]})),s}function o(e,t){return tn(s(e,t),t)}function f(e,t,r,n){var a="C;Y"+(r+1)+";X"+(n+1)+";K";switch(e.t){case"n":a+=e.v||0,e.f&&!e.F&&(a+=";E"+zs(e.f,{r:r,c:n}));break;case"b":a+=e.v?"TRUE":"FALSE";break;case"e":a+=e.w||e.v;break;case"d":a+='"'+(e.w||e.v)+'"';break;case"s":a+='"'+e.v.replace(/"/g,"").replace(/;/g,";;")+'"';break}return a}function l(e,t){t.forEach((function(t,r){var n="F;W"+(r+1)+" "+(r+1)+" ";t.hidden?n+="0":("number"!=typeof t.width||t.wpx||(t.wpx=Fi(t.width)),"number"!=typeof t.wpx||t.wch||(t.wch=Ui(t.wpx)),"number"==typeof t.wch&&(n+=Math.round(t.wch)))," "!=n.charAt(n.length-1)&&e.push(n)}))}function h(e,t){t.forEach((function(t,r){var n="F;";t.hidden?n+="M0;":t.hpt?n+="M"+20*t.hpt+";":t.hpx&&(n+="M"+20*Vi(t.hpx)+";"),n.length>2&&e.push(n+"R"+(r+1))}))}function u(e,t){var r,n=["ID;PWXL;N;E"],a=[],i=qr(e["!ref"]),s=Array.isArray(e),o="\r\n";n.push("P;PGeneral"),n.push("F;P0;DG0G8;M255"),e["!cols"]&&l(n,e["!cols"]),e["!rows"]&&h(n,e["!rows"]),n.push("B;Y"+(i.e.r-i.s.r+1)+";X"+(i.e.c-i.s.c+1)+";D"+[i.s.c,i.s.r,i.e.c,i.e.r].join(" "));for(var c=i.s.r;c<=i.e.r;++c)for(var u=i.s.c;u<=i.e.c;++u){var d=Kr({r:c,c:u});r=s?(e[c]||[])[u]:e[d],r&&(null!=r.v||r.f&&!r.F)&&a.push(f(r,e,c,u,t))}return n.join(o)+o+a.join(o)+o+"E"+o}return e["|"]=254,{to_workbook:o,to_sheet:s,from_sheet:u}}(),Ei=function(){function e(e,r){switch(r.type){case"base64":return t(w(e),r);case"binary":return t(e,r);case"buffer":return t(E&&Buffer.isBuffer(e)?e.toString("binary"):O(e),r);case"array":return t(ut(e),r)}throw new Error("Unrecognized type "+r.type)}function t(e,t){for(var r=e.split("\n"),n=-1,a=-1,i=0,s=[];i!==r.length;++i)if("BOT"!==r[i].trim()){if(!(n<0)){var o=r[i].trim().split(","),f=o[0],l=o[1];++i;var c=r[i]||"";while(1&(c.match(/["]/g)||[]).length&&i<r.length-1)c+="\n"+r[++i];switch(c=c.trim(),+f){case-1:if("BOT"===c){s[++n]=[],a=0;continue}if("EOD"!==c)throw new Error("Unrecognized DIF special command "+c);break;case 0:"TRUE"===c?s[n][a]=!0:"FALSE"===c?s[n][a]=!1:isNaN(mt(l))?isNaN(vt(l).getDate())?s[n][a]=l:s[n][a]=ht(l):s[n][a]=mt(l),++a;break;case 1:c=c.slice(1,c.length-1),c=c.replace(/""/g,'"'),v&&c&&c.match(/^=".*"$/)&&(c=c.slice(2,-1)),s[n][a++]=""!==c?c:null;break}if("EOD"===c)break}}else s[++n]=[],a=0;return t&&t.sheetRows&&(s=s.slice(0,t.sheetRows)),s}function r(t,r){return nn(e(t,r),r)}function n(e,t){return tn(r(e,t),t)}var a=function(){var e=function(e,t,r,n,a){e.push(t),e.push(r+","+n),e.push('"'+a.replace(/"/g,'""')+'"')},t=function(e,t,r,n){e.push(t+","+r),e.push(1==t?'"'+n.replace(/"/g,'""')+'"':n)};return function(r){var n,a=[],i=qr(r["!ref"]),s=Array.isArray(r);e(a,"TABLE",0,1,"sheetjs"),e(a,"VECTORS",0,i.e.r-i.s.r+1,""),e(a,"TUPLES",0,i.e.c-i.s.c+1,""),e(a,"DATA",0,0,"");for(var o=i.s.r;o<=i.e.r;++o){t(a,-1,0,"BOT");for(var f=i.s.c;f<=i.e.c;++f){var l=Kr({r:o,c:f});if(n=s?(r[o]||[])[f]:r[l],n)switch(n.t){case"n":var c=v?n.w:n.v;c||null==n.v||(c=n.v),null==c?v&&n.f&&!n.F?t(a,1,0,"="+n.f):t(a,1,0,""):t(a,0,c,"V");break;case"b":t(a,0,n.v?1:0,n.v?"TRUE":"FALSE");break;case"s":t(a,1,0,!v||isNaN(n.v)?n.v:'="'+n.v+'"');break;case"d":n.w||(n.w=Ue(n.z||z[14],nt(ht(n.v)))),v?t(a,0,n.w,"V"):t(a,1,0,n.w);break;default:t(a,1,0,"")}else t(a,1,0,"")}}t(a,-1,0,"EOD");var h="\r\n",u=a.join(h);return u}}();return{to_workbook:n,to_sheet:r,from_sheet:a}}(),Ai=function(){function e(e){return e.replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,"\n")}function t(e){return e.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function r(t,r){for(var n=t.split("\n"),a=-1,i=-1,s=0,o=[];s!==n.length;++s){var f=n[s].trim().split(":");if("cell"===f[0]){var l=Xr(f[1]);if(o.length<=l.r)for(a=o.length;a<=l.r;++a)o[a]||(o[a]=[]);switch(a=l.r,i=l.c,f[2]){case"t":o[a][i]=e(f[3]);break;case"v":o[a][i]=+f[3];break;case"vtf":var c=f[f.length-1];case"vtc":switch(f[3]){case"nl":o[a][i]=!!+f[4];break;default:o[a][i]=+f[4];break}"vtf"==f[2]&&(o[a][i]=[o[a][i],c])}}}return r&&r.sheetRows&&(o=o.slice(0,r.sheetRows)),o}function n(e,t){return nn(r(e,t),t)}function a(e,t){return tn(n(e,t),t)}var i=["socialcalc:version:1.5","MIME-Version: 1.0","Content-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave"].join("\n"),s=["--SocialCalcSpreadsheetControlSave","Content-type: text/plain; charset=UTF-8"].join("\n")+"\n",o=["# SocialCalc Spreadsheet Control Save","part:sheet"].join("\n"),f="--SocialCalcSpreadsheetControlSave--";function l(e){if(!e||!e["!ref"])return"";for(var r,n=[],a=[],i="",s=Jr(e["!ref"]),o=Array.isArray(e),f=s.s.r;f<=s.e.r;++f)for(var l=s.s.c;l<=s.e.c;++l)if(i=Kr({r:f,c:l}),r=o?(e[f]||[])[l]:e[i],r&&null!=r.v&&"z"!==r.t){switch(a=["cell",i,"t"],r.t){case"s":case"str":a.push(t(r.v));break;case"n":r.f?(a[2]="vtf",a[3]="n",a[4]=r.v,a[5]=t(r.f)):(a[2]="v",a[3]=r.v);break;case"b":a[2]="vt"+(r.f?"f":"c"),a[3]="nl",a[4]=r.v?"1":"0",a[5]=t(r.f||(r.v?"TRUE":"FALSE"));break;case"d":var c=nt(ht(r.v));a[2]="vtc",a[3]="nd",a[4]=""+c,a[5]=r.w||Ue(r.z||z[14],c);break;case"e":continue}n.push(a.join(":"))}return n.push("sheet:c:"+(s.e.c-s.s.c+1)+":r:"+(s.e.r-s.s.r+1)+":tvf:1"),n.push("valueformat:1:text-wiki"),n.join("\n")}function c(e){return[i,s,o,s,l(e),f].join("\n")}return{to_workbook:a,to_sheet:n,from_sheet:c}}(),yi=function(){function e(e,t,r,n,a){a.raw?t[r][n]=e:""===e||("TRUE"===e?t[r][n]=!0:"FALSE"===e?t[r][n]=!1:isNaN(mt(e))?isNaN(vt(e).getDate())?t[r][n]=e:t[r][n]=ht(e):t[r][n]=mt(e))}function t(t,r){var n=r||{},a=[];if(!t||0===t.length)return a;var i=t.split(/[\r\n]/),s=i.length-1;while(s>=0&&0===i[s].length)--s;for(var o=10,f=0,l=0;l<=s;++l)f=i[l].indexOf(" "),-1==f?f=i[l].length:f++,o=Math.max(o,f);for(l=0;l<=s;++l){a[l]=[];var c=0;for(e(i[l].slice(0,o).trim(),a,l,c,n),c=1;c<=(i[l].length-o)/10+1;++c)e(i[l].slice(o+10*(c-1),o+10*c).trim(),a,l,c,n)}return n.sheetRows&&(a=a.slice(0,n.sheetRows)),a}var r={44:",",9:"\t",59:";",124:"|"},n={44:3,9:2,59:1,124:0};function a(e){for(var t={},a=!1,i=0,s=0;i<e.length;++i)34==(s=e.charCodeAt(i))?a=!a:!a&&s in r&&(t[s]=(t[s]||0)+1);for(i in s=[],t)Object.prototype.hasOwnProperty.call(t,i)&&s.push([t[i],i]);if(!s.length)for(i in t=n,t)Object.prototype.hasOwnProperty.call(t,i)&&s.push([t[i],i]);return s.sort((function(e,t){return e[0]-t[0]||n[e[1]]-n[t[1]]})),r[s.pop()[1]]||44}function i(e,t){var r=t||{},n="";null!=g&&null==r.dense&&(r.dense=g);var i=r.dense?[]:{},s={s:{c:0,r:0},e:{c:0,r:0}};"sep="==e.slice(0,4)?13==e.charCodeAt(5)&&10==e.charCodeAt(6)?(n=e.charAt(4),e=e.slice(7)):13==e.charCodeAt(5)||10==e.charCodeAt(5)?(n=e.charAt(4),e=e.slice(6)):n=a(e.slice(0,1024)):n=r&&r.FS?r.FS:a(e.slice(0,1024));var o=0,f=0,l=0,c=0,h=0,u=n.charCodeAt(0),d=!1,p=0,m=e.charCodeAt(0);e=e.replace(/\r\n/gm,"\n");var v=null!=r.dateNF?je(r.dateNF):null;function T(){var t=e.slice(c,h),n={};if('"'==t.charAt(0)&&'"'==t.charAt(t.length-1)&&(t=t.slice(1,-1).replace(/""/g,'"')),0===t.length)n.t="z";else if(r.raw)n.t="s",n.v=t;else if(0===t.trim().length)n.t="s",n.v=t;else if(61==t.charCodeAt(0))34==t.charCodeAt(1)&&34==t.charCodeAt(t.length-1)?(n.t="s",n.v=t.slice(2,-1).replace(/""/g,'"')):Ys(t)?(n.t="n",n.f=t.slice(1)):(n.t="s",n.v=t);else if("TRUE"==t)n.t="b",n.v=!0;else if("FALSE"==t)n.t="b",n.v=!1;else if(isNaN(l=mt(t)))if(!isNaN(vt(t).getDate())||v&&t.match(v)){n.z=r.dateNF||z[14];var a=0;v&&t.match(v)&&(t=ze(t,r.dateNF,t.match(v)||[]),a=1),r.cellDates?(n.t="d",n.v=ht(t,a)):(n.t="n",n.v=nt(ht(t,a))),!1!==r.cellText&&(n.w=Ue(n.z,n.v instanceof Date?nt(n.v):n.v)),r.cellNF||delete n.z}else n.t="s",n.v=t;else n.t="n",!1!==r.cellText&&(n.w=t),n.v=l;if("z"==n.t||(r.dense?(i[o]||(i[o]=[]),i[o][f]=n):i[Kr({c:f,r:o})]=n),c=h+1,m=e.charCodeAt(c),s.e.c<f&&(s.e.c=f),s.e.r<o&&(s.e.r=o),p==u)++f;else if(f=0,++o,r.sheetRows&&r.sheetRows<=o)return!0}e:for(;h<e.length;++h)switch(p=e.charCodeAt(h)){case 34:34===m&&(d=!d);break;case u:case 10:case 13:if(!d&&T())break e;break;default:break}return h-c>0&&T(),i["!ref"]=Zr(s),i}function s(e,r){return r&&r.PRN?r.FS||"sep="==e.slice(0,4)||e.indexOf("\t")>=0||e.indexOf(",")>=0||e.indexOf(";")>=0?i(e,r):nn(t(e,r),r):i(e,r)}function o(e,t){var r="",n="string"==t.type?[0,0,0,0]:fu(e,t);switch(t.type){case"base64":r=w(e);break;case"binary":r=e;break;case"buffer":r=65001==t.codepage?e.toString("utf8"):t.codepage&&"undefined"!==typeof d?d.utils.decode(t.codepage,e):E&&Buffer.isBuffer(e)?e.toString("binary"):O(e);break;case"array":r=ut(e);break;case"string":r=e;break;default:throw new Error("Unrecognized type "+t.type)}return 239==n[0]&&187==n[1]&&191==n[2]?r=Mt(r.slice(3)):"string"!=t.type&&"buffer"!=t.type&&65001==t.codepage?r=Mt(r):"binary"==t.type&&"undefined"!==typeof d&&t.codepage&&(r=d.utils.decode(t.codepage,d.utils.encode(28591,r))),"socialcalc:version:"==r.slice(0,19)?Ai.to_sheet("string"==t.type?r:Mt(r),t):s(r,t)}function f(e,t){return tn(o(e,t),t)}function l(e){for(var t,r=[],n=qr(e["!ref"]),a=Array.isArray(e),i=n.s.r;i<=n.e.r;++i){for(var s=[],o=n.s.c;o<=n.e.c;++o){var f=Kr({r:i,c:o});if(t=a?(e[i]||[])[o]:e[f],t&&null!=t.v){var l=(t.w||(en(t),t.w)||"").slice(0,10);while(l.length<10)l+=" ";s.push(l+(0===o?" ":""))}else s.push("          ")}r.push(s.join(""))}return r.join("\n")}return{to_workbook:f,to_sheet:o,from_sheet:l}}();var Si=function(){function e(e,t,r){if(e){kr(e,e.l||0);var n=r.Enum||W;while(e.l<e.length){var a=e.read_shift(2),i=n[a]||n[65535],s=e.read_shift(2),o=e.l+s,f=i.f&&i.f(e,s,r);if(e.l=o,t(f,i,a))return}}}function t(e,t){switch(t.type){case"base64":return r(_(w(e)),t);case"binary":return r(_(e),t);case"buffer":case"array":return r(e,t)}throw"Unsupported type "+t.type}function r(t,r){if(!t)return t;var n=r||{};null!=g&&null==n.dense&&(n.dense=g);var a=n.dense?[]:{},i="Sheet1",s="",o=0,f={},l=[],c=[],h={s:{r:0,c:0},e:{r:0,c:0}},u=n.sheetRows||0;if(0==t[2]&&(8==t[3]||9==t[3])&&t.length>=16&&5==t[14]&&108===t[15])throw new Error("Unsupported Works 3 for Mac file");if(2==t[2])n.Enum=W,e(t,(function(e,t,r){switch(r){case 0:n.vers=e,e>=4096&&(n.qpro=!0);break;case 6:h=e;break;case 204:e&&(s=e);break;case 222:s=e;break;case 15:case 51:n.qpro||(e[1].v=e[1].v.slice(1));case 13:case 14:case 16:14==r&&112==(112&e[2])&&(15&e[2])>1&&(15&e[2])<15&&(e[1].z=n.dateNF||z[14],n.cellDates&&(e[1].t="d",e[1].v=ot(e[1].v))),n.qpro&&e[3]>o&&(a["!ref"]=Zr(h),f[i]=a,l.push(i),a=n.dense?[]:{},h={s:{r:0,c:0},e:{r:0,c:0}},o=e[3],i=s||"Sheet"+(o+1),s="");var c=n.dense?(a[e[0].r]||[])[e[0].c]:a[Kr(e[0])];if(c){c.t=e[1].t,c.v=e[1].v,null!=e[1].z&&(c.z=e[1].z),null!=e[1].f&&(c.f=e[1].f);break}n.dense?(a[e[0].r]||(a[e[0].r]=[]),a[e[0].r][e[0].c]=e[1]):a[Kr(e[0])]=e[1];break;default:}}),n);else{if(26!=t[2]&&14!=t[2])throw new Error("Unrecognized LOTUS BOF "+t[2]);n.Enum=H,14==t[2]&&(n.qpro=!0,t.l=0),e(t,(function(e,t,r){switch(r){case 204:i=e;break;case 22:e[1].v=e[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(e[3]>o&&(a["!ref"]=Zr(h),f[i]=a,l.push(i),a=n.dense?[]:{},h={s:{r:0,c:0},e:{r:0,c:0}},o=e[3],i="Sheet"+(o+1)),u>0&&e[0].r>=u)break;n.dense?(a[e[0].r]||(a[e[0].r]=[]),a[e[0].r][e[0].c]=e[1]):a[Kr(e[0])]=e[1],h.e.c<e[0].c&&(h.e.c=e[0].c),h.e.r<e[0].r&&(h.e.r=e[0].r);break;case 27:e[14e3]&&(c[e[14e3][0]]=e[14e3][1]);break;case 1537:c[e[0]]=e[1],e[0]==o&&(i=e[1]);break;default:break}}),n)}if(a["!ref"]=Zr(h),f[s||i]=a,l.push(s||i),!c.length)return{SheetNames:l,Sheets:f};for(var d={},p=[],m=0;m<c.length;++m)f[l[m]]?(p.push(c[m]||l[m]),d[c[m]]=f[c[m]]||f[l[m]]):(p.push(c[m]),d[c[m]]={"!ref":"A1"});return{SheetNames:p,Sheets:d}}function n(e,t){var r=t||{};if(+r.codepage>=0&&c(+r.codepage),"string"==r.type)throw new Error("Cannot write WK1 to JS string");var n=Dr(),a=qr(e["!ref"]),s=Array.isArray(e),o=[];ih(n,0,i(1030)),ih(n,6,f(a));for(var l=Math.min(a.e.r,8191),h=a.s.r;h<=l;++h)for(var d=Wr(h),m=a.s.c;m<=a.e.c;++m){h===a.s.r&&(o[m]=jr(m));var g=o[m]+d,T=s?(e[h]||[])[m]:e[g];if(T&&"z"!=T.t)if("n"==T.t)(0|T.v)==T.v&&T.v>=-32768&&T.v<=32767?ih(n,13,p(h,m,T.v)):ih(n,14,v(h,m,T.v));else{var b=en(T);ih(n,15,u(h,m,b.slice(0,239)))}}return ih(n,1),n.end()}function a(e,t){var r=t||{};if(+r.codepage>=0&&c(+r.codepage),"string"==r.type)throw new Error("Cannot write WK3 to JS string");var n=Dr();ih(n,0,s(e));for(var a=0,i=0;a<e.SheetNames.length;++a)(e.Sheets[e.SheetNames[a]]||{})["!ref"]&&ih(n,27,B(e.SheetNames[a],i++));var o=0;for(a=0;a<e.SheetNames.length;++a){var f=e.Sheets[e.SheetNames[a]];if(f&&f["!ref"]){for(var l=qr(f["!ref"]),h=Array.isArray(f),u=[],d=Math.min(l.e.r,8191),p=l.s.r;p<=d;++p)for(var m=Wr(p),g=l.s.c;g<=l.e.c;++g){p===l.s.r&&(u[g]=jr(g));var v=u[g]+m,T=h?(f[p]||[])[g]:f[v];if(T&&"z"!=T.t)if("n"==T.t)ih(n,23,k(p,g,o,T.v));else{var b=en(T);ih(n,22,O(p,g,o,b.slice(0,239)))}}++o}}return ih(n,1),n.end()}function i(e){var t=Nr(2);return t.write_shift(2,e),t}function s(e){var t=Nr(26);t.write_shift(2,4096),t.write_shift(2,4),t.write_shift(4,0);for(var r=0,n=0,a=0,i=0;i<e.SheetNames.length;++i){var s=e.SheetNames[i],o=e.Sheets[s];if(o&&o["!ref"]){++a;var f=Jr(o["!ref"]);r<f.e.r&&(r=f.e.r),n<f.e.c&&(n=f.e.c)}}return r>8191&&(r=8191),t.write_shift(2,r),t.write_shift(1,a),t.write_shift(1,n),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(1,1),t.write_shift(1,2),t.write_shift(4,0),t.write_shift(4,0),t}function o(e,t,r){var n={s:{c:0,r:0},e:{c:0,r:0}};return 8==t&&r.qpro?(n.s.c=e.read_shift(1),e.l++,n.s.r=e.read_shift(2),n.e.c=e.read_shift(1),e.l++,n.e.r=e.read_shift(2),n):(n.s.c=e.read_shift(2),n.s.r=e.read_shift(2),12==t&&r.qpro&&(e.l+=2),n.e.c=e.read_shift(2),n.e.r=e.read_shift(2),12==t&&r.qpro&&(e.l+=2),65535==n.s.c&&(n.s.c=n.e.c=n.s.r=n.e.r=0),n)}function f(e){var t=Nr(8);return t.write_shift(2,e.s.c),t.write_shift(2,e.s.r),t.write_shift(2,e.e.c),t.write_shift(2,e.e.r),t}function l(e,t,r){var n=[{c:0,r:0},{t:"n",v:0},0,0];return r.qpro&&20768!=r.vers?(n[0].c=e.read_shift(1),n[3]=e.read_shift(1),n[0].r=e.read_shift(2),e.l+=2):(n[2]=e.read_shift(1),n[0].c=e.read_shift(2),n[0].r=e.read_shift(2)),n}function h(e,t,r){var n=e.l+t,a=l(e,t,r);if(a[1].t="s",20768==r.vers){e.l++;var i=e.read_shift(1);return a[1].v=e.read_shift(i,"utf8"),a}return r.qpro&&e.l++,a[1].v=e.read_shift(n-e.l,"cstr"),a}function u(e,t,r){var n=Nr(7+r.length);n.write_shift(1,255),n.write_shift(2,t),n.write_shift(2,e),n.write_shift(1,39);for(var a=0;a<n.length;++a){var i=r.charCodeAt(a);n.write_shift(1,i>=128?95:i)}return n.write_shift(1,0),n}function d(e,t,r){var n=l(e,t,r);return n[1].v=e.read_shift(2,"i"),n}function p(e,t,r){var n=Nr(7);return n.write_shift(1,255),n.write_shift(2,t),n.write_shift(2,e),n.write_shift(2,r,"i"),n}function m(e,t,r){var n=l(e,t,r);return n[1].v=e.read_shift(8,"f"),n}function v(e,t,r){var n=Nr(13);return n.write_shift(1,255),n.write_shift(2,t),n.write_shift(2,e),n.write_shift(8,r,"f"),n}function T(e,t,r){var n=e.l+t,a=l(e,t,r);if(a[1].v=e.read_shift(8,"f"),r.qpro)e.l=n;else{var i=e.read_shift(2);y(e.slice(e.l,e.l+i),a),e.l+=i}return a}function b(e,t,r){var n=32768&t;return t&=-32769,t=(n?e:0)+(t>=8192?t-16384:t),(n?"":"$")+(r?jr(t):Wr(t))}var E={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},A=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function y(e,t){kr(e,0);var r=[],n=0,a="",i="",s="",o="";while(e.l<e.length){var f=e[e.l++];switch(f){case 0:r.push(e.read_shift(8,"f"));break;case 1:i=b(t[0].c,e.read_shift(2),!0),a=b(t[0].r,e.read_shift(2),!1),r.push(i+a);break;case 2:var l=b(t[0].c,e.read_shift(2),!0),c=b(t[0].r,e.read_shift(2),!1);i=b(t[0].c,e.read_shift(2),!0),a=b(t[0].r,e.read_shift(2),!1),r.push(l+c+":"+i+a);break;case 3:if(e.l<e.length)return void console.error("WK1 premature formula end");break;case 4:r.push("("+r.pop()+")");break;case 5:r.push(e.read_shift(2));break;case 6:var h="";while(f=e[e.l++])h+=String.fromCharCode(f);r.push('"'+h.replace(/"/g,'""')+'"');break;case 8:r.push("-"+r.pop());break;case 23:r.push("+"+r.pop());break;case 22:r.push("NOT("+r.pop()+")");break;case 20:case 21:o=r.pop(),s=r.pop(),r.push(["AND","OR"][f-20]+"("+s+","+o+")");break;default:if(f<32&&A[f])o=r.pop(),s=r.pop(),r.push(s+A[f]+o);else{if(!E[f])return f<=7?console.error("WK1 invalid opcode "+f.toString(16)):f<=24?console.error("WK1 unsupported op "+f.toString(16)):f<=30?console.error("WK1 invalid opcode "+f.toString(16)):f<=115?console.error("WK1 unsupported function opcode "+f.toString(16)):console.error("WK1 unrecognized opcode "+f.toString(16));if(n=E[f][1],69==n&&(n=e[e.l++]),n>r.length)return void console.error("WK1 bad formula parse 0x"+f.toString(16)+":|"+r.join("|")+"|");var u=r.slice(-n);r.length-=n,r.push(E[f][0]+"("+u.join(",")+")")}}}1==r.length?t[1].f=""+r[0]:console.error("WK1 bad formula parse |"+r.join("|")+"|")}function S(e){var t=[{c:0,r:0},{t:"n",v:0},0];return t[0].r=e.read_shift(2),t[3]=e[e.l++],t[0].c=e[e.l++],t}function x(e,t){var r=S(e,t);return r[1].t="s",r[1].v=e.read_shift(t-4,"cstr"),r}function O(e,t,r,n){var a=Nr(6+n.length);a.write_shift(2,e),a.write_shift(1,r),a.write_shift(1,t),a.write_shift(1,39);for(var i=0;i<n.length;++i){var s=n.charCodeAt(i);a.write_shift(1,s>=128?95:s)}return a.write_shift(1,0),a}function C(e,t){var r=S(e,t);r[1].v=e.read_shift(2);var n=r[1].v>>1;if(1&r[1].v)switch(7&n){case 0:n=5e3*(n>>3);break;case 1:n=500*(n>>3);break;case 2:n=(n>>3)/20;break;case 3:n=(n>>3)/200;break;case 4:n=(n>>3)/2e3;break;case 5:n=(n>>3)/2e4;break;case 6:n=(n>>3)/16;break;case 7:n=(n>>3)/64;break}return r[1].v=n,r}function R(e,t){var r=S(e,t),n=e.read_shift(4),a=e.read_shift(4),i=e.read_shift(2);if(65535==i)return 0===n&&3221225472===a?(r[1].t="e",r[1].v=15):0===n&&3489660928===a?(r[1].t="e",r[1].v=42):r[1].v=0,r;var s=32768&i;return i=(32767&i)-16446,r[1].v=(1-2*s)*(a*Math.pow(2,i+32)+n*Math.pow(2,i)),r}function k(e,t,r,n){var a=Nr(14);if(a.write_shift(2,e),a.write_shift(1,r),a.write_shift(1,t),0==n)return a.write_shift(4,0),a.write_shift(4,0),a.write_shift(2,65535),a;var i=0,s=0,o=0,f=0;return n<0&&(i=1,n=-n),s=0|Math.log2(n),n/=Math.pow(2,s-31),f=n>>>0,0==(2147483648&f)&&(n/=2,++s,f=n>>>0),n-=f,f|=2147483648,f>>>=0,n*=Math.pow(2,32),o=n>>>0,a.write_shift(4,o),a.write_shift(4,f),s+=16383+(i?32768:0),a.write_shift(2,s),a}function I(e,t){var r=R(e,14);return e.l+=t-14,r}function N(e,t){var r=S(e,t),n=e.read_shift(4);return r[1].v=n>>6,r}function D(e,t){var r=S(e,t),n=e.read_shift(8,"f");return r[1].v=n,r}function P(e,t){var r=D(e,14);return e.l+=t-10,r}function L(e,t){return 0==e[e.l+t-1]?e.read_shift(t,"cstr"):""}function M(e,t){var r=e[e.l++];r>t-1&&(r=t-1);var n="";while(n.length<r)n+=String.fromCharCode(e[e.l++]);return n}function F(e,t,r){if(r.qpro&&!(t<21)){var n=e.read_shift(1);e.l+=17,e.l+=1,e.l+=2;var a=e.read_shift(t-21,"cstr");return[n,a]}}function U(e,t){var r={},n=e.l+t;while(e.l<n){var a=e.read_shift(2);if(14e3==a){r[a]=[0,""],r[a][0]=e.read_shift(2);while(e[e.l])r[a][1]+=String.fromCharCode(e[e.l]),e.l++;e.l++}}return r}function B(e,t){var r=Nr(5+e.length);r.write_shift(2,14e3),r.write_shift(2,t);for(var n=0;n<e.length;++n){var a=e.charCodeAt(n);r[r.l++]=a>127?95:a}return r[r.l++]=0,r}var W={0:{n:"BOF",f:Na},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f:o},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:d},14:{n:"NUMBER",f:m},15:{n:"LABEL",f:h},16:{n:"FORMULA",f:T},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:h},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:L},222:{n:"SHEETNAMELP",f:M},65535:{n:""}},H={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:x},23:{n:"NUMBER17",f:R},24:{n:"NUMBER18",f:C},25:{n:"FORMULA19",f:I},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:U},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:N},38:{n:"??"},39:{n:"NUMBER27",f:D},40:{n:"FORMULA28",f:P},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:L},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:F},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:n,book_to_wk3:a,to_workbook:t}}();var _i=/^\s|\s$|[\t\n\r]/;function xi(e,t){if(!t.bookSST)return"";var r=[wt];r[r.length]=Gt("sst",null,{xmlns:$t[0],count:e.Count,uniqueCount:e.Unique});for(var n=0;n!=e.length;++n)if(null!=e[n]){var a=e[n],i="<si>";a.r?i+=a.r:(i+="<t",a.t||(a.t=""),a.t.match(_i)&&(i+=' xml:space="preserve"'),i+=">"+_t(a.t)+"</t>"),i+="</si>",r[r.length]=i}return r.length>2&&(r[r.length]="</sst>",r[1]=r[1].replace("/>",">")),r.join("")}function Oi(e){return[e.read_shift(4),e.read_shift(4)]}function Ci(e,t){return t||(t=Nr(8)),t.write_shift(4,e.Count),t.write_shift(4,e.Unique),t}var Ri=un;function ki(e){var t=Dr();Pr(t,159,Ci(e));for(var r=0;r<e.length;++r)Pr(t,19,Ri(e[r]));return Pr(t,160),t.end()}function Ii(e){if("undefined"!==typeof d)return d.utils.encode(i,e);for(var t=[],r=e.split(""),n=0;n<r.length;++n)t[n]=r[n].charCodeAt(0);return t}function Ni(e){var t,r,n,a,i,s,o=0,f=Ii(e),l=f.length+1;for(t=y(l),t[0]=f.length,r=1;r!=l;++r)t[r]=f[r-1];for(r=l-1;r>=0;--r)n=t[r],a=0===(16384&o)?0:1,i=o<<1&32767,s=a|i,o=s^n;return 52811^o}var Di=function(){function e(e,r){switch(r.type){case"base64":return t(w(e),r);case"binary":return t(e,r);case"buffer":return t(E&&Buffer.isBuffer(e)?e.toString("binary"):O(e),r);case"array":return t(ut(e),r)}throw new Error("Unrecognized type "+r.type)}function t(e,t){var r=t||{},n=r.dense?[]:{},a=e.match(/\\trowd.*?\\row\b/g);if(!a.length)throw new Error("RTF missing table");var i={s:{c:0,r:0},e:{c:0,r:a.length-1}};return a.forEach((function(e,t){Array.isArray(n)&&(n[t]=[]);var r,a=/\\\w+\b/g,s=0,o=-1;while(r=a.exec(e)){switch(r[0]){case"\\cell":var f=e.slice(s,a.lastIndex-r[0].length);if(" "==f[0]&&(f=f.slice(1)),++o,f.length){var l={v:f,t:"s"};Array.isArray(n)?n[t][o]=l:n[Kr({r:t,c:o})]=l}break}s=a.lastIndex}o>i.e.c&&(i.e.c=o)})),n["!ref"]=Zr(i),n}function r(t,r){return tn(e(t,r),r)}function n(e){for(var t,r=["{\\rtf1\\ansi"],n=qr(e["!ref"]),a=Array.isArray(e),i=n.s.r;i<=n.e.r;++i){r.push("\\trowd\\trautofit1");for(var s=n.s.c;s<=n.e.c;++s)r.push("\\cellx"+(s+1));for(r.push("\\pard\\intbl"),s=n.s.c;s<=n.e.c;++s){var o=Kr({r:i,c:s});t=a?(e[i]||[])[s]:e[o],t&&(null!=t.v||t.f&&!t.F)&&(r.push(" "+(t.w||(en(t),t.w))),r.push("\\cell"))}r.push("\\pard\\intbl\\row")}return r.join("")+"}"}return{to_workbook:r,to_sheet:e,from_sheet:n}}();function Pi(e){for(var t=0,r=1;3!=t;++t)r=256*r+(e[t]>255?255:e[t]<0?0:e[t]);return r.toString(16).toUpperCase().slice(1)}var Li=6,Mi=Li;function Fi(e){return Math.floor((e+Math.round(128/Mi)/256)*Mi)}function Ui(e){return Math.floor((e-5)/Mi*100+.5)/100}function Bi(e){return Math.round((e*Mi+5)/Mi*256)/256}function Wi(e){e.width?(e.wpx=Fi(e.width),e.wch=Ui(e.wpx),e.MDW=Mi):e.wpx?(e.wch=Ui(e.wpx),e.width=Bi(e.wch),e.MDW=Mi):"number"==typeof e.wch&&(e.width=Bi(e.wch),e.wpx=Fi(e.width),e.MDW=Mi),e.customWidth&&delete e.customWidth}var Hi=96,Gi=Hi;function Vi(e){return 96*e/Gi}function ji(e){return e*Gi/96}function zi(e){var t=["<numFmts>"];return[[5,8],[23,26],[41,44],[50,392]].forEach((function(r){for(var n=r[0];n<=r[1];++n)null!=e[n]&&(t[t.length]=Gt("numFmt",null,{numFmtId:n,formatCode:_t(e[n])}))})),1===t.length?"":(t[t.length]="</numFmts>",t[0]=Gt("numFmts",null,{count:t.length-2}).replace("/>",">"),t.join(""))}function $i(e){var t=[];return t[t.length]=Gt("cellXfs",null),e.forEach((function(e){t[t.length]=Gt("xf",null,e)})),t[t.length]="</cellXfs>",2===t.length?"":(t[0]=Gt("cellXfs",null,{count:t.length-2}).replace("/>",">"),t.join(""))}function Yi(e,t){var r,n=[wt,Gt("styleSheet",null,{xmlns:$t[0],"xmlns:vt":zt.vt})];return e.SSF&&null!=(r=zi(e.SSF))&&(n[n.length]=r),n[n.length]='<fonts count="1"><font><sz val="12"/><color theme="1"/><name val="Calibri"/><family val="2"/><scheme val="minor"/></font></fonts>',n[n.length]='<fills count="2"><fill><patternFill patternType="none"/></fill><fill><patternFill patternType="gray125"/></fill></fills>',n[n.length]='<borders count="1"><border><left/><right/><top/><bottom/><diagonal/></border></borders>',n[n.length]='<cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0"/></cellStyleXfs>',(r=$i(t.cellXfs))&&(n[n.length]=r),n[n.length]='<cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0"/></cellStyles>',n[n.length]='<dxfs count="0"/>',n[n.length]='<tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4"/>',n.length>2&&(n[n.length]="</styleSheet>",n[1]=n[1].replace("/>",">")),n.join("")}function Xi(e,t){var r=e.read_shift(2),n=on(e,t-2);return[r,n]}function Ki(e,t,r){r||(r=Nr(6+4*t.length)),r.write_shift(2,e),fn(t,r);var n=r.length>r.l?r.slice(0,r.l):r;return null==r.l&&(r.l=r.length),n}function Ji(e,t,r){var n={};n.sz=e.read_shift(2)/20;var a=Mn(e,2,r);a.fItalic&&(n.italic=1),a.fCondense&&(n.condense=1),a.fExtend&&(n.extend=1),a.fShadow&&(n.shadow=1),a.fOutline&&(n.outline=1),a.fStrikeout&&(n.strike=1);var i=e.read_shift(2);switch(700===i&&(n.bold=1),e.read_shift(2)){case 1:n.vertAlign="superscript";break;case 2:n.vertAlign="subscript";break}var s=e.read_shift(1);0!=s&&(n.underline=s);var o=e.read_shift(1);o>0&&(n.family=o);var f=e.read_shift(1);switch(f>0&&(n.charset=f),e.l++,n.color=Pn(e,8),e.read_shift(1)){case 1:n.scheme="major";break;case 2:n.scheme="minor";break}return n.name=on(e,t-21),n}function Zi(e,t){t||(t=Nr(153)),t.write_shift(2,20*e.sz),Fn(e,t),t.write_shift(2,e.bold?700:400);var r=0;"superscript"==e.vertAlign?r=1:"subscript"==e.vertAlign&&(r=2),t.write_shift(2,r),t.write_shift(1,e.underline||0),t.write_shift(1,e.family||0),t.write_shift(1,e.charset||0),t.write_shift(1,0),Ln(e.color,t);var n=0;return"major"==e.scheme&&(n=1),"minor"==e.scheme&&(n=2),t.write_shift(1,n),fn(e.name,t),t.length>t.l?t.slice(0,t.l):t}var qi,Qi=["none","solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"],es=Ir;function ts(e,t){t||(t=Nr(84)),qi||(qi=Qe(Qi));var r=qi[e.patternType];null==r&&(r=40),t.write_shift(4,r);var n=0;if(40!=r)for(Ln({auto:1},t),Ln({auto:1},t);n<12;++n)t.write_shift(4,0);else{for(;n<4;++n)t.write_shift(4,0);for(;n<12;++n)t.write_shift(4,0)}return t.length>t.l?t.slice(0,t.l):t}function rs(e,t){var r=e.l+t,n=e.read_shift(2),a=e.read_shift(2);return e.l=r,{ixfe:n,numFmtId:a}}function ns(e,t,r){r||(r=Nr(16)),r.write_shift(2,t||0),r.write_shift(2,e.numFmtId||0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(1,0),r.write_shift(1,0);var n=0;return r.write_shift(1,n),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r}function as(e,t){return t||(t=Nr(10)),t.write_shift(1,0),t.write_shift(1,0),t.write_shift(4,0),t.write_shift(4,0),t}var is=Ir;function ss(e,t){return t||(t=Nr(51)),t.write_shift(1,0),as(null,t),as(null,t),as(null,t),as(null,t),as(null,t),t.length>t.l?t.slice(0,t.l):t}function os(e,t){return t||(t=Nr(52)),t.write_shift(4,e.xfId),t.write_shift(2,1),t.write_shift(1,+e.builtinId),t.write_shift(1,0),An(e.name||"",t),t.length>t.l?t.slice(0,t.l):t}function fs(e,t,r){var n=Nr(2052);return n.write_shift(4,e),An(t,n),An(r,n),n.length>n.l?n.slice(0,n.l):n}function ls(e,t){if(t){var r=0;[[5,8],[23,26],[41,44],[50,392]].forEach((function(e){for(var n=e[0];n<=e[1];++n)null!=t[n]&&++r})),0!=r&&(Pr(e,615,sn(r)),[[5,8],[23,26],[41,44],[50,392]].forEach((function(r){for(var n=r[0];n<=r[1];++n)null!=t[n]&&Pr(e,44,Ki(n,t[n]))})),Pr(e,616))}}function cs(e){var t=1;0!=t&&(Pr(e,611,sn(t)),Pr(e,43,Zi({sz:12,color:{theme:1},name:"Calibri",family:2,scheme:"minor"})),Pr(e,612))}function hs(e){var t=2;0!=t&&(Pr(e,603,sn(t)),Pr(e,45,ts({patternType:"none"})),Pr(e,45,ts({patternType:"gray125"})),Pr(e,604))}function us(e){var t=1;0!=t&&(Pr(e,613,sn(t)),Pr(e,46,ss({})),Pr(e,614))}function ds(e){var t=1;Pr(e,626,sn(t)),Pr(e,47,ns({numFmtId:0,fontId:0,fillId:0,borderId:0},65535)),Pr(e,627)}function ps(e,t){Pr(e,617,sn(t.length)),t.forEach((function(t){Pr(e,47,ns(t,0))})),Pr(e,618)}function ms(e){var t=1;Pr(e,619,sn(t)),Pr(e,48,os({xfId:0,builtinId:0,name:"Normal"})),Pr(e,620)}function gs(e){var t=0;Pr(e,505,sn(t)),Pr(e,506)}function vs(e){var t=0;Pr(e,508,fs(t,"TableStyleMedium9","PivotStyleMedium4")),Pr(e,509)}function Ts(){}function bs(e,t){var r=Dr();return Pr(r,278),ls(r,e.SSF),cs(r,e),hs(r,e),us(r,e),ds(r,e),ps(r,t.cellXfs),ms(r,e),gs(r,e),vs(r,e),Ts(r,e),Pr(r,279),r.end()}function ws(e,t){if(t&&t.themeXLSX)return t.themeXLSX;if(e&&"string"==typeof e.raw)return e.raw;var r=[wt];return r[r.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',r[r.length]="<a:themeElements>",r[r.length]='<a:clrScheme name="Office">',r[r.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',r[r.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',r[r.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',r[r.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',r[r.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',r[r.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',r[r.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',r[r.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',r[r.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',r[r.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',r[r.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',r[r.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',r[r.length]="</a:clrScheme>",r[r.length]='<a:fontScheme name="Office">',r[r.length]="<a:majorFont>",r[r.length]='<a:latin typeface="Cambria"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Times New Roman"/>',r[r.length]='<a:font script="Hebr" typeface="Times New Roman"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="MoolBoran"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Times New Roman"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:majorFont>",r[r.length]="<a:minorFont>",r[r.length]='<a:latin typeface="Calibri"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Arial"/>',r[r.length]='<a:font script="Hebr" typeface="Arial"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="DaunPenh"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Arial"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:minorFont>",r[r.length]="</a:fontScheme>",r[r.length]='<a:fmtScheme name="Office">',r[r.length]="<a:fillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="1"/>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="0"/>',r[r.length]="</a:gradFill>",r[r.length]="</a:fillStyleLst>",r[r.length]="<a:lnStyleLst>",r[r.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]="</a:lnStyleLst>",r[r.length]="<a:effectStyleLst>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',r[r.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',r[r.length]="</a:effectStyle>",r[r.length]="</a:effectStyleLst>",r[r.length]="<a:bgFillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]="</a:bgFillStyleLst>",r[r.length]="</a:fmtScheme>",r[r.length]="</a:themeElements>",r[r.length]="<a:objectDefaults>",r[r.length]="<a:spDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',r[r.length]="</a:spDef>",r[r.length]="<a:lnDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',r[r.length]="</a:lnDef>",r[r.length]="</a:objectDefaults>",r[r.length]="<a:extraClrSchemeLst/>",r[r.length]="</a:theme>",r.join("")}function Es(e,t){return{flags:e.read_shift(4),version:e.read_shift(4),name:on(e,t-8)}}function As(e){var t=Nr(12+2*e.name.length);return t.write_shift(4,e.flags),t.write_shift(4,e.version),fn(e.name,t),t.slice(0,t.l)}function ys(e){var t=[],r=e.read_shift(4);while(r-- >0)t.push([e.read_shift(4),e.read_shift(4)]);return t}function Ss(e){var t=Nr(4+8*e.length);t.write_shift(4,e.length);for(var r=0;r<e.length;++r)t.write_shift(4,e[r][0]),t.write_shift(4,e[r][1]);return t}function _s(e,t){var r=Nr(8+2*t.length);return r.write_shift(4,e),fn(t,r),r.slice(0,r.l)}function xs(e){return e.l+=4,0!=e.read_shift(4)}function Os(e,t){var r=Nr(8);return r.write_shift(4,e),r.write_shift(4,t?1:0),r}function Cs(){var e=Dr();return Pr(e,332),Pr(e,334,sn(1)),Pr(e,335,As({name:"XLDAPR",version:12e4,flags:3496657072})),Pr(e,336),Pr(e,339,_s(1,"XLDAPR")),Pr(e,52),Pr(e,35,sn(514)),Pr(e,4096,sn(0)),Pr(e,4097,Da(1)),Pr(e,36),Pr(e,53),Pr(e,340),Pr(e,337,Os(1,!0)),Pr(e,51,Ss([[1,0]])),Pr(e,338),Pr(e,333),e.end()}function Rs(){var e=[wt];return e.push('<metadata xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:xlrd="http://schemas.microsoft.com/office/spreadsheetml/2017/richdata" xmlns:xda="http://schemas.microsoft.com/office/spreadsheetml/2017/dynamicarray">\n  <metadataTypes count="1">\n    <metadataType name="XLDAPR" minSupportedVersion="120000" copy="1" pasteAll="1" pasteValues="1" merge="1" splitFirst="1" rowColShift="1" clearFormats="1" clearComments="1" assign="1" coerce="1" cellMeta="1"/>\n  </metadataTypes>\n  <futureMetadata name="XLDAPR" count="1">\n    <bk>\n      <extLst>\n        <ext uri="{bdbb8cdc-fa1e-496e-a857-3c3f30c029c3}">\n          <xda:dynamicArrayProperties fDynamic="1" fCollapsed="0"/>\n        </ext>\n      </extLst>\n    </bk>\n  </futureMetadata>\n  <cellMetadata count="1">\n    <bk>\n      <rc t="1" v="0"/>\n    </bk>\n  </cellMetadata>\n</metadata>'),e.join("")}function ks(e){var t={};t.i=e.read_shift(4);var r={};r.r=e.read_shift(4),r.c=e.read_shift(4),t.r=Kr(r);var n=e.read_shift(1);return 2&n&&(t.l="1"),8&n&&(t.a="1"),t}var Is=1024;function Ns(e,t){var r=[21600,21600],n=["m0,0l0",r[1],r[0],r[1],r[0],"0xe"].join(","),a=[Gt("xml",null,{"xmlns:v":Yt.v,"xmlns:o":Yt.o,"xmlns:x":Yt.x,"xmlns:mv":Yt.mv}).replace(/\/>/,">"),Gt("o:shapelayout",Gt("o:idmap",null,{"v:ext":"edit",data:e}),{"v:ext":"edit"}),Gt("v:shapetype",[Gt("v:stroke",null,{joinstyle:"miter"}),Gt("v:path",null,{gradientshapeok:"t","o:connecttype":"rect"})].join(""),{id:"_x0000_t202","o:spt":202,coordsize:r.join(","),path:n})];while(Is<1e3*e)Is+=1e3;return t.forEach((function(e){var t=Xr(e[0]),r={color2:"#BEFF82",type:"gradient"};"gradient"==r.type&&(r.angle="-180");var n="gradient"==r.type?Gt("o:fill",null,{type:"gradientUnscaled","v:ext":"view"}):null,i=Gt("v:fill",n,r),s={on:"t",obscured:"t"};++Is,a=a.concat(["<v:shape"+Ht({id:"_x0000_s"+Is,type:"#_x0000_t202",style:"position:absolute; margin-left:80pt;margin-top:5pt;width:104pt;height:64pt;z-index:10"+(e[1].hidden?";visibility:hidden":""),fillcolor:"#ECFAD4",strokecolor:"#edeaa1"})+">",i,Gt("v:shadow",null,s),Gt("v:path",null,{"o:connecttype":"none"}),'<v:textbox><div style="text-align:left"></div></v:textbox>','<x:ClientData ObjectType="Note">',"<x:MoveWithCells/>","<x:SizeWithCells/>",Wt("x:Anchor",[t.c+1,0,t.r+1,0,t.c+3,20,t.r+5,20].join(",")),Wt("x:AutoFill","False"),Wt("x:Row",String(t.r)),Wt("x:Column",String(t.c)),e[1].hidden?"":"<x:Visible/>","</x:ClientData>","</v:shape>"])})),a.push("</xml>"),a.join("")}function Ds(e){var t=[wt,Gt("comments",null,{xmlns:$t[0]})],r=[];return t.push("<authors>"),e.forEach((function(e){e[1].forEach((function(e){var n=_t(e.a);-1==r.indexOf(n)&&(r.push(n),t.push("<author>"+n+"</author>")),e.T&&e.ID&&-1==r.indexOf("tc="+e.ID)&&(r.push("tc="+e.ID),t.push("<author>tc="+e.ID+"</author>"))}))})),0==r.length&&(r.push("SheetJ5"),t.push("<author>SheetJ5</author>")),t.push("</authors>"),t.push("<commentList>"),e.forEach((function(e){var n=0,a=[];if(e[1][0]&&e[1][0].T&&e[1][0].ID?n=r.indexOf("tc="+e[1][0].ID):e[1].forEach((function(e){e.a&&(n=r.indexOf(_t(e.a))),a.push(e.t||"")})),t.push('<comment ref="'+e[0]+'" authorId="'+n+'"><text>'),a.length<=1)t.push(Wt("t",_t(a[0]||"")));else{for(var i="Comment:\n    "+a[0]+"\n",s=1;s<a.length;++s)i+="Reply:\n    "+a[s]+"\n";t.push(Wt("t",_t(i)))}t.push("</text></comment>")})),t.push("</commentList>"),t.length>2&&(t[t.length]="</comments>",t[1]=t[1].replace("/>",">")),t.join("")}function Ps(e,t,r){var n=[wt,Gt("ThreadedComments",null,{xmlns:zt.TCMNT}).replace(/[\/]>/,">")];return e.forEach((function(e){var a="";(e[1]||[]).forEach((function(i,s){if(i.T){i.a&&-1==t.indexOf(i.a)&&t.push(i.a);var o={ref:e[0],id:"{54EE7951-7262-4200-6969-"+("000000000000"+r.tcid++).slice(-12)+"}"};0==s?a=o.id:o.parentId=a,i.ID=o.id,i.a&&(o.personId="{54EE7950-7262-4200-6969-"+("000000000000"+t.indexOf(i.a)).slice(-12)+"}"),n.push(Gt("threadedComment",Wt("text",i.t||""),o))}else delete i.ID}))})),n.push("</ThreadedComments>"),n.join("")}function Ls(e){var t=[wt,Gt("personList",null,{xmlns:zt.TCMNT,"xmlns:x":$t[0]}).replace(/[\/]>/,">")];return e.forEach((function(e,r){t.push(Gt("person",null,{displayName:e,id:"{54EE7950-7262-4200-6969-"+("000000000000"+r).slice(-12)+"}",userId:e,providerId:"None"}))})),t.push("</personList>"),t.join("")}function Ms(e){var t={};t.iauthor=e.read_shift(4);var r=kn(e,16);return t.rfx=r.s,t.ref=Kr(r.s),e.l+=16,t}function Fs(e,t){return null==t&&(t=Nr(36)),t.write_shift(4,e[1].iauthor),In(e[0],t),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t}var Us=on;function Bs(e){return fn(e.slice(0,54))}function Ws(e){var t=Dr(),r=[];return Pr(t,628),Pr(t,630),e.forEach((function(e){e[1].forEach((function(e){r.indexOf(e.a)>-1||(r.push(e.a.slice(0,54)),Pr(t,632,Bs(e.a)))}))})),Pr(t,631),Pr(t,633),e.forEach((function(e){e[1].forEach((function(n){n.iauthor=r.indexOf(n.a);var a={s:Xr(e[0]),e:Xr(e[0])};Pr(t,635,Fs([a,n])),n.t&&n.t.length>0&&Pr(t,637,pn(n)),Pr(t,636),delete n.iauthor}))})),Pr(t,634),Pr(t,629),t.end()}function Hs(e,t){t.FullPaths.forEach((function(r,n){if(0!=n){var a=r.replace(/[^\/]*[\/]/,"/_VBA_PROJECT_CUR/");"/"!==a.slice(-1)&&Ye.utils.cfb_add(e,a,t.FileIndex[n].content)}}))}var Gs=["xlsb","xlsm","xlam","biff8","xla"];var Vs=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,t={r:0,c:0};function r(e,r,n,a){var i=!1,s=!1;0==n.length?s=!0:"["==n.charAt(0)&&(s=!0,n=n.slice(1,-1)),0==a.length?i=!0:"["==a.charAt(0)&&(i=!0,a=a.slice(1,-1));var o=n.length>0?0|parseInt(n,10):0,f=a.length>0?0|parseInt(a,10):0;return i?f+=t.c:--f,s?o+=t.r:--o,r+(i?"":"$")+jr(f)+(s?"":"$")+Wr(o)}return function(n,a){return t=a,n.replace(e,r)}}(),js=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,zs=function(){return function(e,t){return e.replace(js,(function(e,r,n,a,i,s){var o=Vr(a)-(n?0:t.c),f=Br(s)-(i?0:t.r),l=0==f?"":i?f+1:"["+f+"]",c=0==o?"":n?o+1:"["+o+"]";return r+"R"+l+"C"+c}))}}();function $s(e,t){return e.replace(js,(function(e,r,n,a,i,s){return r+("$"==n?n+a:jr(Vr(a)+t.c))+("$"==i?i+s:Wr(Br(s)+t.r))}))}function Ys(e){return 1!=e.length}function Xs(e){e.l+=1}function Ks(e,t){var r=e.read_shift(1==t?1:2);return[16383&r,r>>14&1,r>>15&1]}function Js(e,t,r){var n=2;if(r){if(r.biff>=2&&r.biff<=5)return Zs(e,t,r);12==r.biff&&(n=4)}var a=e.read_shift(n),i=e.read_shift(n),s=Ks(e,2),o=Ks(e,2);return{s:{r:a,c:s[0],cRel:s[1],rRel:s[2]},e:{r:i,c:o[0],cRel:o[1],rRel:o[2]}}}function Zs(e){var t=Ks(e,2),r=Ks(e,2),n=e.read_shift(1),a=e.read_shift(1);return{s:{r:t[0],c:n,cRel:t[1],rRel:t[2]},e:{r:r[0],c:a,cRel:r[1],rRel:r[2]}}}function qs(e,t,r){if(r.biff<8)return Zs(e,t,r);var n=e.read_shift(12==r.biff?4:2),a=e.read_shift(12==r.biff?4:2),i=Ks(e,2),s=Ks(e,2);return{s:{r:n,c:i[0],cRel:i[1],rRel:i[2]},e:{r:a,c:s[0],cRel:s[1],rRel:s[2]}}}function Qs(e,t,r){if(r&&r.biff>=2&&r.biff<=5)return eo(e,t,r);var n=e.read_shift(r&&12==r.biff?4:2),a=Ks(e,2);return{r:n,c:a[0],cRel:a[1],rRel:a[2]}}function eo(e){var t=Ks(e,2),r=e.read_shift(1);return{r:t[0],c:r,cRel:t[1],rRel:t[2]}}function to(e){var t=e.read_shift(2),r=e.read_shift(2);return{r:t,c:255&r,fQuoted:!!(16384&r),cRel:r>>15,rRel:r>>15}}function ro(e,t,r){var n=r&&r.biff?r.biff:8;if(n>=2&&n<=5)return no(e,t,r);var a=e.read_shift(n>=12?4:2),i=e.read_shift(2),s=(16384&i)>>14,o=(32768&i)>>15;if(i&=16383,1==o)while(a>524287)a-=1048576;if(1==s)while(i>8191)i-=16384;return{r:a,c:i,cRel:s,rRel:o}}function no(e){var t=e.read_shift(2),r=e.read_shift(1),n=(32768&t)>>15,a=(16384&t)>>14;return t&=16383,1==n&&t>=8192&&(t-=16384),1==a&&r>=128&&(r-=256),{r:t,c:r,cRel:a,rRel:n}}function ao(e,t,r){var n=(96&e[e.l++])>>5,a=Js(e,r.biff>=2&&r.biff<=5?6:8,r);return[n,a]}function io(e,t,r){var n=(96&e[e.l++])>>5,a=e.read_shift(2,"i"),i=8;if(r)switch(r.biff){case 5:e.l+=12,i=6;break;case 12:i=12;break}var s=Js(e,i,r);return[n,a,s]}function so(e,t,r){var n=(96&e[e.l++])>>5;return e.l+=r&&r.biff>8?12:r.biff<8?6:8,[n]}function oo(e,t,r){var n=(96&e[e.l++])>>5,a=e.read_shift(2),i=8;if(r)switch(r.biff){case 5:e.l+=12,i=6;break;case 12:i=12;break}return e.l+=i,[n,a]}function fo(e,t,r){var n=(96&e[e.l++])>>5,a=qs(e,t-1,r);return[n,a]}function lo(e,t,r){var n=(96&e[e.l++])>>5;return e.l+=2==r.biff?6:12==r.biff?14:7,[n]}function co(e){var t=1&e[e.l+1],r=1;return e.l+=4,[t,r]}function ho(e,t,r){e.l+=2;for(var n=e.read_shift(r&&2==r.biff?1:2),a=[],i=0;i<=n;++i)a.push(e.read_shift(r&&2==r.biff?1:2));return a}function uo(e,t,r){var n=255&e[e.l+1]?1:0;return e.l+=2,[n,e.read_shift(r&&2==r.biff?1:2)]}function po(e,t,r){var n=255&e[e.l+1]?1:0;return e.l+=2,[n,e.read_shift(r&&2==r.biff?1:2)]}function mo(e){var t=255&e[e.l+1]?1:0;return e.l+=2,[t,e.read_shift(2)]}function go(e,t,r){var n=255&e[e.l+1]?1:0;return e.l+=r&&2==r.biff?3:4,[n]}function vo(e){var t=e.read_shift(1),r=e.read_shift(1);return[t,r]}function To(e){return e.read_shift(2),vo(e,2)}function bo(e){return e.read_shift(2),vo(e,2)}function wo(e,t,r){var n=(96&e[e.l])>>5;e.l+=1;var a=Qs(e,0,r);return[n,a]}function Eo(e,t,r){var n=(96&e[e.l])>>5;e.l+=1;var a=ro(e,0,r);return[n,a]}function Ao(e,t,r){var n=(96&e[e.l])>>5;e.l+=1;var a=e.read_shift(2);r&&5==r.biff&&(e.l+=12);var i=Qs(e,0,r);return[n,a,i]}function yo(e,t,r){var n=(96&e[e.l])>>5;e.l+=1;var a=e.read_shift(r&&r.biff<=3?1:2);return[Pf[a],Df[a],n]}function So(e,t,r){var n=e[e.l++],a=e.read_shift(1),i=r&&r.biff<=3?[88==n?-1:0,e.read_shift(1)]:_o(e);return[a,(0===i[0]?Df:Nf)[i[1]]]}function _o(e){return[e[e.l+1]>>7,32767&e.read_shift(2)]}function xo(e,t,r){e.l+=r&&2==r.biff?3:4}function Oo(e,t,r){if(e.l++,r&&12==r.biff)return[e.read_shift(4,"i"),0];var n=e.read_shift(2),a=e.read_shift(r&&2==r.biff?1:2);return[n,a]}function Co(e){return e.l++,Qn[e.read_shift(1)]}function Ro(e){return e.l++,e.read_shift(2)}function ko(e){return e.l++,0!==e.read_shift(1)}function Io(e){return e.l++,Nn(e,8)}function No(e,t,r){return e.l++,La(e,t-1,r)}function Do(e,t){var r=[e.read_shift(1)];if(12==t)switch(r[0]){case 2:r[0]=4;break;case 4:r[0]=16;break;case 0:r[0]=1;break;case 1:r[0]=2;break}switch(r[0]){case 4:r[1]=ka(e,1)?"TRUE":"FALSE",12!=t&&(e.l+=7);break;case 37:case 16:r[1]=Qn[e[e.l]],e.l+=12==t?4:8;break;case 0:e.l+=8;break;case 1:r[1]=Nn(e,8);break;case 2:r[1]=Ba(e,0,{biff:t>0&&t<8?2:t});break;default:throw new Error("Bad SerAr: "+r[0])}return r}function Po(e,t,r){for(var n=e.read_shift(12==r.biff?4:2),a=[],i=0;i!=n;++i)a.push((12==r.biff?kn:za)(e,8));return a}function Lo(e,t,r){var n=0,a=0;12==r.biff?(n=e.read_shift(4),a=e.read_shift(4)):(a=1+e.read_shift(1),n=1+e.read_shift(2)),r.biff>=2&&r.biff<8&&(--n,0==--a&&(a=256));for(var i=0,s=[];i!=n&&(s[i]=[]);++i)for(var o=0;o!=a;++o)s[i][o]=Do(e,r.biff);return s}function Mo(e,t,r){var n=e.read_shift(1)>>>5&3,a=!r||r.biff>=8?4:2,i=e.read_shift(a);switch(r.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12;break}return[n,0,i]}function Fo(e,t,r){if(5==r.biff)return Uo(e,t,r);var n=e.read_shift(1)>>>5&3,a=e.read_shift(2),i=e.read_shift(4);return[n,a,i]}function Uo(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2,"i");e.l+=8;var n=e.read_shift(2);return e.l+=12,[t,r,n]}function Bo(e,t,r){var n=e.read_shift(1)>>>5&3;e.l+=r&&2==r.biff?3:4;var a=e.read_shift(r&&2==r.biff?1:2);return[n,a]}function Wo(e,t,r){var n=e.read_shift(1)>>>5&3,a=e.read_shift(r&&2==r.biff?1:2);return[n,a]}function Ho(e,t,r){var n=e.read_shift(1)>>>5&3;return e.l+=4,r.biff<8&&e.l--,12==r.biff&&(e.l+=2),[n]}function Go(e,t,r){var n=(96&e[e.l++])>>5,a=e.read_shift(2),i=4;if(r)switch(r.biff){case 5:i=15;break;case 12:i=6;break}return e.l+=i,[n,a]}var Vo=Ir,jo=Ir,zo=Ir;function $o(e,t,r){return e.l+=2,[to(e,4,r)]}function Yo(e){return e.l+=6,[]}var Xo=$o,Ko=Yo,Jo=Yo,Zo=$o;function qo(e){return e.l+=2,[Na(e),1&e.read_shift(2)]}var Qo=$o,ef=qo,tf=Yo,rf=$o,nf=$o,af=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"];function sf(e){e.l+=2;var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(4),a=e.read_shift(2),i=e.read_shift(2),s=af[r>>2&31];return{ixti:t,coltype:3&r,rt:s,idx:n,c:a,C:i}}function of(e){return e.l+=2,[e.read_shift(4)]}function ff(e,t,r){return e.l+=5,e.l+=2,e.l+=2==r.biff?1:4,["PTGSHEET"]}function lf(e,t,r){return e.l+=2==r.biff?4:5,["PTGENDSHEET"]}function cf(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[t,r]}function hf(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[t,r]}function uf(e){return e.l+=4,[0,0]}var df={1:{n:"PtgExp",f:Oo},2:{n:"PtgTbl",f:zo},3:{n:"PtgAdd",f:Xs},4:{n:"PtgSub",f:Xs},5:{n:"PtgMul",f:Xs},6:{n:"PtgDiv",f:Xs},7:{n:"PtgPower",f:Xs},8:{n:"PtgConcat",f:Xs},9:{n:"PtgLt",f:Xs},10:{n:"PtgLe",f:Xs},11:{n:"PtgEq",f:Xs},12:{n:"PtgGe",f:Xs},13:{n:"PtgGt",f:Xs},14:{n:"PtgNe",f:Xs},15:{n:"PtgIsect",f:Xs},16:{n:"PtgUnion",f:Xs},17:{n:"PtgRange",f:Xs},18:{n:"PtgUplus",f:Xs},19:{n:"PtgUminus",f:Xs},20:{n:"PtgPercent",f:Xs},21:{n:"PtgParen",f:Xs},22:{n:"PtgMissArg",f:Xs},23:{n:"PtgStr",f:No},26:{n:"PtgSheet",f:ff},27:{n:"PtgEndSheet",f:lf},28:{n:"PtgErr",f:Co},29:{n:"PtgBool",f:ko},30:{n:"PtgInt",f:Ro},31:{n:"PtgNum",f:Io},32:{n:"PtgArray",f:lo},33:{n:"PtgFunc",f:yo},34:{n:"PtgFuncVar",f:So},35:{n:"PtgName",f:Mo},36:{n:"PtgRef",f:wo},37:{n:"PtgArea",f:ao},38:{n:"PtgMemArea",f:Bo},39:{n:"PtgMemErr",f:Vo},40:{n:"PtgMemNoMem",f:jo},41:{n:"PtgMemFunc",f:Wo},42:{n:"PtgRefErr",f:Ho},43:{n:"PtgAreaErr",f:so},44:{n:"PtgRefN",f:Eo},45:{n:"PtgAreaN",f:fo},46:{n:"PtgMemAreaN",f:cf},47:{n:"PtgMemNoMemN",f:hf},57:{n:"PtgNameX",f:Fo},58:{n:"PtgRef3d",f:Ao},59:{n:"PtgArea3d",f:io},60:{n:"PtgRefErr3d",f:Go},61:{n:"PtgAreaErr3d",f:oo},255:{}},pf={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},mf={1:{n:"PtgElfLel",f:qo},2:{n:"PtgElfRw",f:rf},3:{n:"PtgElfCol",f:Xo},6:{n:"PtgElfRwV",f:nf},7:{n:"PtgElfColV",f:Zo},10:{n:"PtgElfRadical",f:Qo},11:{n:"PtgElfRadicalS",f:tf},13:{n:"PtgElfColS",f:Ko},15:{n:"PtgElfColSV",f:Jo},16:{n:"PtgElfRadicalLel",f:ef},25:{n:"PtgList",f:sf},29:{n:"PtgSxName",f:of},255:{}},gf={0:{n:"PtgAttrNoop",f:uf},1:{n:"PtgAttrSemi",f:go},2:{n:"PtgAttrIf",f:po},4:{n:"PtgAttrChoose",f:ho},8:{n:"PtgAttrGoto",f:uo},16:{n:"PtgAttrSum",f:xo},32:{n:"PtgAttrBaxcel",f:co},33:{n:"PtgAttrBaxcel",f:co},64:{n:"PtgAttrSpace",f:To},65:{n:"PtgAttrSpaceSemi",f:bo},128:{n:"PtgAttrIfError",f:mo},255:{}};function vf(e,t,r,n){if(n.biff<8)return Ir(e,t);for(var a=e.l+t,i=[],s=0;s!==r.length;++s)switch(r[s][0]){case"PtgArray":r[s][1]=Lo(e,0,n),i.push(r[s][1]);break;case"PtgMemArea":r[s][2]=Po(e,r[s][1],n),i.push(r[s][2]);break;case"PtgExp":n&&12==n.biff&&(r[s][1][1]=e.read_shift(4),i.push(r[s][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+r[s][0];default:break}return t=a-e.l,0!==t&&i.push(Ir(e,t)),i}function Tf(e,t,r){var n,a,i=e.l+t,s=[];while(i!=e.l)t=i-e.l,a=e[e.l],n=df[a]||df[pf[a]],24!==a&&25!==a||(n=(24===a?mf:gf)[e[e.l+1]]),n&&n.f?s.push([n.n,n.f(e,t,r)]):Ir(e,t);return s}function bf(e){for(var t=[],r=0;r<e.length;++r){for(var n=e[r],a=[],i=0;i<n.length;++i){var s=n[i];if(s)switch(s[0]){case 2:a.push('"'+s[1].replace(/"/g,'""')+'"');break;default:a.push(s[1])}else a.push("")}t.push(a.join(","))}return t.join(";")}var wf={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function Ef(e,t){if(!e&&!(t&&t.biff<=5&&t.biff>=2))throw new Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e)?"'"+e+"'":e}function Af(e,t,r){if(!e)return"SH33TJSERR0";if(r.biff>8&&(!e.XTI||!e.XTI[t]))return e.SheetNames[t];if(!e.XTI)return"SH33TJSERR6";var n=e.XTI[t];if(r.biff<8)return t>1e4&&(t-=65536),t<0&&(t=-t),0==t?"":e.XTI[t-1];if(!n)return"SH33TJSERR1";var a="";if(r.biff>8)switch(e[n[0]][0]){case 357:return a=-1==n[1]?"#REF":e.SheetNames[n[1]],n[1]==n[2]?a:a+":"+e.SheetNames[n[2]];case 358:return null!=r.SID?e.SheetNames[r.SID]:"SH33TJSSAME"+e[n[0]][0];case 355:default:return"SH33TJSSRC"+e[n[0]][0]}switch(e[n[0]][0][0]){case 1025:return a=-1==n[1]?"#REF":e.SheetNames[n[1]]||"SH33TJSERR3",n[1]==n[2]?a:a+":"+e.SheetNames[n[2]];case 14849:return e[n[0]].slice(1).map((function(e){return e.Name})).join(";;");default:return e[n[0]][0][3]?(a=-1==n[1]?"#REF":e[n[0]][0][3][n[1]]||"SH33TJSERR4",n[1]==n[2]?a:a+":"+e[n[0]][0][3][n[2]]):"SH33TJSERR2"}}function yf(e,t,r){var n=Af(e,t,r);return"#REF"==n?n:Ef(n,r)}function Sf(e,t,r,n,a){var i,s,o,f,l=a&&a.biff||8,c={s:{c:0,r:0},e:{c:0,r:0}},h=[],u=0,d=0,p="";if(!e[0]||!e[0][0])return"";for(var m=-1,g="",v=0,T=e[0].length;v<T;++v){var b=e[0][v];switch(b[0]){case"PtgUminus":h.push("-"+h.pop());break;case"PtgUplus":h.push("+"+h.pop());break;case"PtgPercent":h.push(h.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(i=h.pop(),s=h.pop(),m>=0){switch(e[0][m][1][0]){case 0:g=pt(" ",e[0][m][1][1]);break;case 1:g=pt("\r",e[0][m][1][1]);break;default:if(g="",a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}s+=g,m=-1}h.push(s+wf[b[0]]+i);break;case"PtgIsect":i=h.pop(),s=h.pop(),h.push(s+" "+i);break;case"PtgUnion":i=h.pop(),s=h.pop(),h.push(s+","+i);break;case"PtgRange":i=h.pop(),s=h.pop(),h.push(s+":"+i);break;case"PtgAttrChoose":break;case"PtgAttrGoto":break;case"PtgAttrIf":break;case"PtgAttrIfError":break;case"PtgRef":o=Lr(b[1][1],c,a),h.push(Fr(o,l));break;case"PtgRefN":o=r?Lr(b[1][1],r,a):b[1][1],h.push(Fr(o,l));break;case"PtgRef3d":u=b[1][1],o=Lr(b[1][2],c,a),p=yf(n,u,a);h.push(p+"!"+Fr(o,l));break;case"PtgFunc":case"PtgFuncVar":var w=b[1][0],E=b[1][1];w||(w=0),w&=127;var A=0==w?[]:h.slice(-w);h.length-=w,"User"===E&&(E=A.shift()),h.push(E+"("+A.join(",")+")");break;case"PtgBool":h.push(b[1]?"TRUE":"FALSE");break;case"PtgInt":h.push(b[1]);break;case"PtgNum":h.push(String(b[1]));break;case"PtgStr":h.push('"'+b[1].replace(/"/g,'""')+'"');break;case"PtgErr":h.push(b[1]);break;case"PtgAreaN":f=Mr(b[1][1],r?{s:r}:c,a),h.push(Ur(f,a));break;case"PtgArea":f=Mr(b[1][1],c,a),h.push(Ur(f,a));break;case"PtgArea3d":u=b[1][1],f=b[1][2],p=yf(n,u,a),h.push(p+"!"+Ur(f,a));break;case"PtgAttrSum":h.push("SUM("+h.pop()+")");break;case"PtgAttrBaxcel":case"PtgAttrSemi":break;case"PtgName":d=b[1][2];var y=(n.names||[])[d-1]||(n[0]||[])[d],S=y?y.Name:"SH33TJSNAME"+String(d);S&&"_xlfn."==S.slice(0,6)&&!a.xlfn&&(S=S.slice(6)),h.push(S);break;case"PtgNameX":var _,x=b[1][1];if(d=b[1][2],!(a.biff<=5)){var O="";if(14849==((n[x]||[])[0]||[])[0]||(1025==((n[x]||[])[0]||[])[0]?n[x][d]&&n[x][d].itab>0&&(O=n.SheetNames[n[x][d].itab-1]+"!"):O=n.SheetNames[d-1]+"!"),n[x]&&n[x][d])O+=n[x][d].Name;else if(n[0]&&n[0][d])O+=n[0][d].Name;else{var C=(Af(n,x,a)||"").split(";;");C[d-1]?O=C[d-1]:O+="SH33TJSERRX"}h.push(O);break}x<0&&(x=-x),n[x]&&(_=n[x][d]),_||(_={Name:"SH33TJSERRY"}),h.push(_.Name);break;case"PtgParen":var R="(",k=")";if(m>=0){switch(g="",e[0][m][1][0]){case 2:R=pt(" ",e[0][m][1][1])+R;break;case 3:R=pt("\r",e[0][m][1][1])+R;break;case 4:k=pt(" ",e[0][m][1][1])+k;break;case 5:k=pt("\r",e[0][m][1][1])+k;break;default:if(a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}m=-1}h.push(R+h.pop()+k);break;case"PtgRefErr":h.push("#REF!");break;case"PtgRefErr3d":h.push("#REF!");break;case"PtgExp":o={c:b[1][1],r:b[1][0]};var I={c:r.c,r:r.r};if(n.sharedf[Kr(o)]){var N=n.sharedf[Kr(o)];h.push(Sf(N,c,I,n,a))}else{var D=!1;for(i=0;i!=n.arrayf.length;++i)if(s=n.arrayf[i],!(o.c<s[0].s.c||o.c>s[0].e.c)&&!(o.r<s[0].s.r||o.r>s[0].e.r)){h.push(Sf(s[1],c,I,n,a)),D=!0;break}D||h.push(b[1])}break;case"PtgArray":h.push("{"+bf(b[1])+"}");break;case"PtgMemArea":break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":m=v;break;case"PtgTbl":break;case"PtgMemErr":break;case"PtgMissArg":h.push("");break;case"PtgAreaErr":h.push("#REF!");break;case"PtgAreaErr3d":h.push("#REF!");break;case"PtgList":h.push("Table"+b[1].idx+"[#"+b[1].rt+"]");break;case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":break;case"PtgMemFunc":break;case"PtgMemNoMem":break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw new Error("Unsupported ELFs");case"PtgSxName":throw new Error("Unrecognized Formula Token: "+String(b));default:throw new Error("Unrecognized Formula Token: "+String(b))}var P=["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"];if(3!=a.biff&&m>=0&&-1==P.indexOf(e[0][v][0])){b=e[0][m];var L=!0;switch(b[1][0]){case 4:L=!1;case 0:g=pt(" ",b[1][1]);break;case 5:L=!1;case 1:g=pt("\r",b[1][1]);break;default:if(g="",a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+b[1][0])}h.push((L?g:"")+h.pop()+(L?"":g)),m=-1}}if(h.length>1&&a.WTF)throw new Error("bad formula stack");return h[0]}function _f(e){if(null==e){var t=Nr(8);return t.write_shift(1,3),t.write_shift(1,0),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(2,65535),t}return Dn("number"==typeof e?e:0)}function xf(e,t,r,n,a){var i=Va(t,r,a),s=_f(e.v),o=Nr(6),f=33;o.write_shift(2,f),o.write_shift(4,0);for(var l=Nr(e.bf.length),c=0;c<e.bf.length;++c)l[c]=e.bf[c];var h=R([i,s,o,l]);return h}function Of(e,t,r){var n=e.read_shift(4),a=Tf(e,n,r),i=e.read_shift(4),s=i>0?vf(e,i,a,r):null;return[a,s]}var Cf=Of,Rf=Of,kf=Of,If=Of,Nf={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},Df={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},Pf={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};function Lf(e){var t="of:="+e.replace(js,"$1[.$2$3$4$5]").replace(/\]:\[/g,":");return t.replace(/;/g,"|").replace(/,/g,";")}function Mf(e){return e.replace(/\./,"!")}var Ff="undefined"!==typeof Map;function Uf(e,t,r){var n=0,a=e.length;if(r){if(Ff?r.has(t):Object.prototype.hasOwnProperty.call(r,t))for(var i=Ff?r.get(t):r[t];n<i.length;++n)if(e[i[n]].t===t)return e.Count++,i[n]}else for(;n<a;++n)if(e[n].t===t)return e.Count++,n;return e[a]={t:t},e.Count++,e.Unique++,r&&(Ff?(r.has(t)||r.set(t,[]),r.get(t).push(a)):(Object.prototype.hasOwnProperty.call(r,t)||(r[t]=[]),r[t].push(a))),a}function Bf(e,t){var r={min:e+1,max:e+1},n=-1;return t.MDW&&(Mi=t.MDW),null!=t.width?r.customWidth=1:null!=t.wpx?n=Ui(t.wpx):null!=t.wch&&(n=t.wch),n>-1?(r.width=Bi(n),r.customWidth=1):null!=t.width&&(r.width=t.width),t.hidden&&(r.hidden=!0),null!=t.level&&(r.outlineLevel=r.level=t.level),r}function Wf(e,t){if(e){var r=[.7,.7,.75,.75,.3,.3];"xlml"==t&&(r=[1,1,1,1,.5,.5]),null==e.left&&(e.left=r[0]),null==e.right&&(e.right=r[1]),null==e.top&&(e.top=r[2]),null==e.bottom&&(e.bottom=r[3]),null==e.header&&(e.header=r[4]),null==e.footer&&(e.footer=r[5])}}function Hf(e,t,r){var n=r.revssf[null!=t.z?t.z:"General"],a=60,i=e.length;if(null==n&&r.ssf)for(;a<392;++a)if(null==r.ssf[a]){Be(t.z,a),r.ssf[a]=t.z,r.revssf[t.z]=n=a;break}for(a=0;a!=i;++a)if(e[a].numFmtId===n)return a;return e[i]={numFmtId:n,fontId:0,fillId:0,borderId:0,xfId:0,applyNumberFormat:1},i}function Gf(e,t,r){if(e&&e["!ref"]){var n=qr(e["!ref"]);if(n.e.c<n.s.c||n.e.r<n.s.r)throw new Error("Bad range ("+r+"): "+e["!ref"])}}function Vf(e){if(0===e.length)return"";for(var t='<mergeCells count="'+e.length+'">',r=0;r!=e.length;++r)t+='<mergeCell ref="'+Zr(e[r])+'"/>';return t+"</mergeCells>"}function jf(e,t,r,n,a){var i=!1,s={},o=null;if("xlsx"!==n.bookType&&t.vbaraw){var f=t.SheetNames[r];try{t.Workbook&&(f=t.Workbook.Sheets[r].CodeName||f)}catch(c){}i=!0,s.codeName=Ft(_t(f))}if(e&&e["!outline"]){var l={summaryBelow:1,summaryRight:1};e["!outline"].above&&(l.summaryBelow=0),e["!outline"].left&&(l.summaryRight=0),o=(o||"")+Gt("outlinePr",null,l)}(i||o)&&(a[a.length]=Gt("sheetPr",o,s))}var zf=["objects","scenarios","selectLockedCells","selectUnlockedCells"],$f=["formatColumns","formatRows","formatCells","insertColumns","insertRows","insertHyperlinks","deleteColumns","deleteRows","sort","autoFilter","pivotTables"];function Yf(e){var t={sheet:1};return zf.forEach((function(r){null!=e[r]&&e[r]&&(t[r]="1")})),$f.forEach((function(r){null==e[r]||e[r]||(t[r]="0")})),e.password&&(t.password=Ni(e.password).toString(16).toUpperCase()),Gt("sheetProtection",null,t)}function Xf(e){return Wf(e),Gt("pageMargins",null,e)}function Kf(e,t){for(var r,n=["<cols>"],a=0;a!=t.length;++a)(r=t[a])&&(n[n.length]=Gt("col",null,Bf(a,r)));return n[n.length]="</cols>",n.join("")}function Jf(e,t,r,n){var a="string"==typeof e.ref?e.ref:Zr(e.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var i=r.Workbook.Names,s=Jr(a);s.s.r==s.e.r&&(s.e.r=Jr(t["!ref"]).e.r,a=Zr(s));for(var o=0;o<i.length;++o){var f=i[o];if("_xlnm._FilterDatabase"==f.Name&&f.Sheet==n){f.Ref="'"+r.SheetNames[n]+"'!"+a;break}}return o==i.length&&i.push({Name:"_xlnm._FilterDatabase",Sheet:n,Ref:"'"+r.SheetNames[n]+"'!"+a}),Gt("autoFilter",null,{ref:a})}function Zf(e,t,r,n){var a={workbookViewId:"0"};return(((n||{}).Workbook||{}).Views||[])[0]&&(a.rightToLeft=n.Workbook.Views[0].RTL?"1":"0"),Gt("sheetViews",Gt("sheetView",null,a),{})}function qf(e,t,r,n){if(e.c&&r["!comments"].push([t,e.c]),void 0===e.v&&"string"!==typeof e.f||"z"===e.t&&!e.f)return"";var a="",i=e.t,s=e.v;if("z"!==e.t)switch(e.t){case"b":a=e.v?"1":"0";break;case"n":a=""+e.v;break;case"e":a=Qn[e.v];break;case"d":n&&n.cellDates?a=ht(e.v,-1).toISOString():(e=dt(e),e.t="n",a=""+(e.v=nt(ht(e.v)))),"undefined"===typeof e.z&&(e.z=z[14]);break;default:a=e.v;break}var o=Wt("v",_t(a)),f={r:t},l=Hf(n.cellXfs,e,n);switch(0!==l&&(f.s=l),e.t){case"n":break;case"d":f.t="d";break;case"b":f.t="b";break;case"e":f.t="e";break;case"z":break;default:if(null==e.v){delete e.t;break}if(e.v.length>32767)throw new Error("Text length must not exceed 32767 characters");if(n&&n.bookSST){o=Wt("v",""+Uf(n.Strings,e.v,n.revStrings)),f.t="s";break}f.t="str";break}if(e.t!=i&&(e.t=i,e.v=s),"string"==typeof e.f&&e.f){var c=e.F&&e.F.slice(0,t.length)==t?{t:"array",ref:e.F}:null;o=Gt("f",_t(e.f),c)+(null!=e.v?o:"")}return e.l&&r["!links"].push([t,e.l]),e.D&&(f.cm=1),Gt("c",o,f)}function Qf(e,t,r,n){var a,i,s=[],o=[],f=qr(e["!ref"]),l="",c="",h=[],u=0,d=0,p=e["!rows"],m=Array.isArray(e),g={r:c},v=-1;for(d=f.s.c;d<=f.e.c;++d)h[d]=jr(d);for(u=f.s.r;u<=f.e.r;++u){for(o=[],c=Wr(u),d=f.s.c;d<=f.e.c;++d){a=h[d]+c;var T=m?(e[u]||[])[d]:e[a];void 0!==T&&(null!=(l=qf(T,a,e,t,r,n))&&o.push(l))}(o.length>0||p&&p[u])&&(g={r:c},p&&p[u]&&(i=p[u],i.hidden&&(g.hidden=1),v=-1,i.hpx?v=Vi(i.hpx):i.hpt&&(v=i.hpt),v>-1&&(g.ht=v,g.customHeight=1),i.level&&(g.outlineLevel=i.level)),s[s.length]=Gt("row",o.join(""),g))}if(p)for(;u<p.length;++u)p&&p[u]&&(g={r:u+1},i=p[u],i.hidden&&(g.hidden=1),v=-1,i.hpx?v=Vi(i.hpx):i.hpt&&(v=i.hpt),v>-1&&(g.ht=v,g.customHeight=1),i.level&&(g.outlineLevel=i.level),s[s.length]=Gt("row","",g));return s.join("")}function el(e,t,r,n){var a=[wt,Gt("worksheet",null,{xmlns:$t[0],"xmlns:r":zt.r})],i=r.SheetNames[e],s=0,o="",f=r.Sheets[i];null==f&&(f={});var l=f["!ref"]||"A1",c=qr(l);if(c.e.c>16383||c.e.r>1048575){if(t.WTF)throw new Error("Range "+l+" exceeds format limit A1:XFD1048576");c.e.c=Math.min(c.e.c,16383),c.e.r=Math.min(c.e.c,1048575),l=Zr(c)}n||(n={}),f["!comments"]=[];var h=[];jf(f,r,e,t,a),a[a.length]=Gt("dimension",null,{ref:l}),a[a.length]=Zf(f,t,e,r),t.sheetFormat&&(a[a.length]=Gt("sheetFormatPr",null,{defaultRowHeight:t.sheetFormat.defaultRowHeight||"16",baseColWidth:t.sheetFormat.baseColWidth||"10",outlineLevelRow:t.sheetFormat.outlineLevelRow||"7"})),null!=f["!cols"]&&f["!cols"].length>0&&(a[a.length]=Kf(f,f["!cols"])),a[s=a.length]="<sheetData/>",f["!links"]=[],null!=f["!ref"]&&(o=Qf(f,t,e,r,n),o.length>0&&(a[a.length]=o)),a.length>s+1&&(a[a.length]="</sheetData>",a[s]=a[s].replace("/>",">")),f["!protect"]&&(a[a.length]=Yf(f["!protect"])),null!=f["!autofilter"]&&(a[a.length]=Jf(f["!autofilter"],f,r,e)),null!=f["!merges"]&&f["!merges"].length>0&&(a[a.length]=Vf(f["!merges"]));var u,d=-1,p=-1;return f["!links"].length>0&&(a[a.length]="<hyperlinks>",f["!links"].forEach((function(e){e[1].Target&&(u={ref:e[0]},"#"!=e[1].Target.charAt(0)&&(p=oa(n,-1,_t(e[1].Target).replace(/#.*$/,""),aa.HLINK),u["r:id"]="rId"+p),(d=e[1].Target.indexOf("#"))>-1&&(u.location=_t(e[1].Target.slice(d+1))),e[1].Tooltip&&(u.tooltip=_t(e[1].Tooltip)),a[a.length]=Gt("hyperlink",null,u))})),a[a.length]="</hyperlinks>"),delete f["!links"],null!=f["!margins"]&&(a[a.length]=Xf(f["!margins"])),t&&!t.ignoreEC&&void 0!=t.ignoreEC||(a[a.length]=Wt("ignoredErrors",Gt("ignoredError",null,{numberStoredAsText:1,sqref:l}))),h.length>0&&(p=oa(n,-1,"../drawings/drawing"+(e+1)+".xml",aa.DRAW),a[a.length]=Gt("drawing",null,{"r:id":"rId"+p}),f["!drawing"]=h),f["!comments"].length>0&&(p=oa(n,-1,"../drawings/vmlDrawing"+(e+1)+".vml",aa.VML),a[a.length]=Gt("legacyDrawing",null,{"r:id":"rId"+p}),f["!legacy"]=p),a.length>1&&(a[a.length]="</worksheet>",a[1]=a[1].replace("/>",">")),a.join("")}function tl(e,t){var r={},n=e.l+t;r.r=e.read_shift(4),e.l+=4;var a=e.read_shift(2);e.l+=1;var i=e.read_shift(1);return e.l=n,7&i&&(r.level=7&i),16&i&&(r.hidden=!0),32&i&&(r.hpt=a/20),r}function rl(e,t,r){var n=Nr(145),a=(r["!rows"]||[])[e]||{};n.write_shift(4,e),n.write_shift(4,0);var i=320;a.hpx?i=20*Vi(a.hpx):a.hpt&&(i=20*a.hpt),n.write_shift(2,i),n.write_shift(1,0);var s=0;a.level&&(s|=a.level),a.hidden&&(s|=16),(a.hpx||a.hpt)&&(s|=32),n.write_shift(1,s),n.write_shift(1,0);var o=0,f=n.l;n.l+=4;for(var l={r:e,c:0},c=0;c<16;++c)if(!(t.s.c>c+1<<10||t.e.c<c<<10)){for(var h=-1,u=-1,d=c<<10;d<c+1<<10;++d){l.c=d;var p=Array.isArray(r)?(r[l.r]||[])[l.c]:r[Kr(l)];p&&(h<0&&(h=d),u=d)}h<0||(++o,n.write_shift(4,h),n.write_shift(4,u))}var m=n.l;return n.l=f,n.write_shift(4,o),n.l=m,n.length>n.l?n.slice(0,n.l):n}function nl(e,t,r,n){var a=rl(n,r,t);(a.length>17||(t["!rows"]||[])[n])&&Pr(e,0,a)}var al=kn,il=In;function sl(){}function ol(e,t){var r={},n=e[e.l];return++e.l,r.above=!(64&n),r.left=!(128&n),e.l+=18,r.name=bn(e,t-19),r}function fl(e,t,r){null==r&&(r=Nr(84+4*e.length));var n=192;t&&(t.above&&(n&=-65),t.left&&(n&=-129)),r.write_shift(1,n);for(var a=1;a<3;++a)r.write_shift(1,0);return Ln({auto:1},r),r.write_shift(-4,-1),r.write_shift(-4,-1),wn(e,r),r.slice(0,r.l)}function ll(e){var t=mn(e);return[t]}function cl(e,t,r){return null==r&&(r=Nr(8)),gn(t,r)}function hl(e){var t=vn(e);return[t]}function ul(e,t,r){return null==r&&(r=Nr(4)),Tn(t,r)}function dl(e){var t=mn(e),r=e.read_shift(1);return[t,r,"b"]}function pl(e,t,r){return null==r&&(r=Nr(9)),gn(t,r),r.write_shift(1,e.v?1:0),r}function ml(e){var t=vn(e),r=e.read_shift(1);return[t,r,"b"]}function gl(e,t,r){return null==r&&(r=Nr(5)),Tn(t,r),r.write_shift(1,e.v?1:0),r}function vl(e){var t=mn(e),r=e.read_shift(1);return[t,r,"e"]}function Tl(e,t,r){return null==r&&(r=Nr(9)),gn(t,r),r.write_shift(1,e.v),r}function bl(e){var t=vn(e),r=e.read_shift(1);return[t,r,"e"]}function wl(e,t,r){return null==r&&(r=Nr(8)),Tn(t,r),r.write_shift(1,e.v),r.write_shift(2,0),r.write_shift(1,0),r}function El(e){var t=mn(e),r=e.read_shift(4);return[t,r,"s"]}function Al(e,t,r){return null==r&&(r=Nr(12)),gn(t,r),r.write_shift(4,t.v),r}function yl(e){var t=vn(e),r=e.read_shift(4);return[t,r,"s"]}function Sl(e,t,r){return null==r&&(r=Nr(8)),Tn(t,r),r.write_shift(4,t.v),r}function _l(e){var t=mn(e),r=Nn(e);return[t,r,"n"]}function xl(e,t,r){return null==r&&(r=Nr(16)),gn(t,r),Dn(e.v,r),r}function Ol(e){var t=vn(e),r=Nn(e);return[t,r,"n"]}function Cl(e,t,r){return null==r&&(r=Nr(12)),Tn(t,r),Dn(e.v,r),r}function Rl(e){var t=mn(e),r=xn(e);return[t,r,"n"]}function kl(e,t,r){return null==r&&(r=Nr(12)),gn(t,r),On(e.v,r),r}function Il(e){var t=vn(e),r=xn(e);return[t,r,"n"]}function Nl(e,t,r){return null==r&&(r=Nr(8)),Tn(t,r),On(e.v,r),r}function Dl(e){var t=mn(e),r=hn(e);return[t,r,"is"]}function Pl(e){var t=mn(e),r=on(e);return[t,r,"str"]}function Ll(e,t,r){return null==r&&(r=Nr(12+4*e.v.length)),gn(t,r),fn(e.v,r),r.length>r.l?r.slice(0,r.l):r}function Ml(e){var t=vn(e),r=on(e);return[t,r,"str"]}function Fl(e,t,r){return null==r&&(r=Nr(8+4*e.v.length)),Tn(t,r),fn(e.v,r),r.length>r.l?r.slice(0,r.l):r}function Ul(e,t,r){var n=e.l+t,a=mn(e);a.r=r["!row"];var i=e.read_shift(1),s=[a,i,"b"];if(r.cellFormula){e.l+=2;var o=Rf(e,n-e.l,r);s[3]=Sf(o,null,a,r.supbooks,r)}else e.l=n;return s}function Bl(e,t,r){var n=e.l+t,a=mn(e);a.r=r["!row"];var i=e.read_shift(1),s=[a,i,"e"];if(r.cellFormula){e.l+=2;var o=Rf(e,n-e.l,r);s[3]=Sf(o,null,a,r.supbooks,r)}else e.l=n;return s}function Wl(e,t,r){var n=e.l+t,a=mn(e);a.r=r["!row"];var i=Nn(e),s=[a,i,"n"];if(r.cellFormula){e.l+=2;var o=Rf(e,n-e.l,r);s[3]=Sf(o,null,a,r.supbooks,r)}else e.l=n;return s}function Hl(e,t,r){var n=e.l+t,a=mn(e);a.r=r["!row"];var i=on(e),s=[a,i,"str"];if(r.cellFormula){e.l+=2;var o=Rf(e,n-e.l,r);s[3]=Sf(o,null,a,r.supbooks,r)}else e.l=n;return s}var Gl=kn,Vl=In;function jl(e,t){return null==t&&(t=Nr(4)),t.write_shift(4,e),t}function zl(e,t){var r=e.l+t,n=kn(e,16),a=En(e),i=on(e),s=on(e),o=on(e);e.l=r;var f={rfx:n,relId:a,loc:i,display:o};return s&&(f.Tooltip=s),f}function $l(e,t){var r=Nr(50+4*(e[1].Target.length+(e[1].Tooltip||"").length));In({s:Xr(e[0]),e:Xr(e[0])},r),_n("rId"+t,r);var n=e[1].Target.indexOf("#"),a=-1==n?"":e[1].Target.slice(n+1);return fn(a||"",r),fn(e[1].Tooltip||"",r),fn("",r),r.slice(0,r.l)}function Yl(){}function Xl(e,t,r){var n=e.l+t,a=Cn(e,16),i=e.read_shift(1),s=[a];if(s[2]=i,r.cellFormula){var o=Cf(e,n-e.l,r);s[1]=o}else e.l=n;return s}function Kl(e,t,r){var n=e.l+t,a=kn(e,16),i=[a];if(r.cellFormula){var s=If(e,n-e.l,r);i[1]=s,e.l=n}else e.l=n;return i}function Jl(e,t,r){null==r&&(r=Nr(18));var n=Bf(e,t);r.write_shift(-4,e),r.write_shift(-4,e),r.write_shift(4,256*(n.width||10)),r.write_shift(4,0);var a=0;return t.hidden&&(a|=1),"number"==typeof n.width&&(a|=2),t.level&&(a|=t.level<<8),r.write_shift(2,a),r}var Zl=["left","right","top","bottom","header","footer"];function ql(e){var t={};return Zl.forEach((function(r){t[r]=Nn(e,8)})),t}function Ql(e,t){return null==t&&(t=Nr(48)),Wf(e),Zl.forEach((function(r){Dn(e[r],t)})),t}function ec(e){var t=e.read_shift(2);return e.l+=28,{RTL:32&t}}function tc(e,t,r){null==r&&(r=Nr(30));var n=924;return(((t||{}).Views||[])[0]||{}).RTL&&(n|=32),r.write_shift(2,n),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(2,0),r.write_shift(2,100),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(4,0),r}function rc(e){var t=Nr(24);return t.write_shift(4,4),t.write_shift(4,1),In(e,t),t}function nc(e,t){return null==t&&(t=Nr(66)),t.write_shift(2,e.password?Ni(e.password):0),t.write_shift(4,1),[["objects",!1],["scenarios",!1],["formatCells",!0],["formatColumns",!0],["formatRows",!0],["insertColumns",!0],["insertRows",!0],["insertHyperlinks",!0],["deleteColumns",!0],["deleteRows",!0],["selectLockedCells",!1],["sort",!0],["autoFilter",!0],["pivotTables",!0],["selectUnlockedCells",!1]].forEach((function(r){r[1]?t.write_shift(4,null==e[r[0]]||e[r[0]]?0:1):t.write_shift(4,null!=e[r[0]]&&e[r[0]]?0:1)})),t}function ac(){}function ic(){}function sc(e,t,r,n,a,i,s){if(void 0===t.v)return!1;var o="";switch(t.t){case"b":o=t.v?"1":"0";break;case"d":t=dt(t),t.z=t.z||z[14],t.v=nt(ht(t.v)),t.t="n";break;case"n":case"e":o=""+t.v;break;default:o=t.v;break}var f={r:r,c:n};switch(f.s=Hf(a.cellXfs,t,a),t.l&&i["!links"].push([Kr(f),t.l]),t.c&&i["!comments"].push([Kr(f),t.c]),t.t){case"s":case"str":return a.bookSST?(o=Uf(a.Strings,t.v,a.revStrings),f.t="s",f.v=o,s?Pr(e,18,Sl(t,f)):Pr(e,7,Al(t,f))):(f.t="str",s?Pr(e,17,Fl(t,f)):Pr(e,6,Ll(t,f))),!0;case"n":return t.v==(0|t.v)&&t.v>-1e3&&t.v<1e3?s?Pr(e,13,Nl(t,f)):Pr(e,2,kl(t,f)):s?Pr(e,16,Cl(t,f)):Pr(e,5,xl(t,f)),!0;case"b":return f.t="b",s?Pr(e,15,gl(t,f)):Pr(e,4,pl(t,f)),!0;case"e":return f.t="e",s?Pr(e,14,wl(t,f)):Pr(e,3,Tl(t,f)),!0}return s?Pr(e,12,ul(t,f)):Pr(e,1,cl(t,f)),!0}function oc(e,t,r,n){var a,i=qr(t["!ref"]||"A1"),s="",o=[];Pr(e,145);var f=Array.isArray(t),l=i.e.r;t["!rows"]&&(l=Math.max(i.e.r,t["!rows"].length-1));for(var c=i.s.r;c<=l;++c){s=Wr(c),nl(e,t,i,c);var h=!1;if(c<=i.e.r)for(var u=i.s.c;u<=i.e.c;++u){c===i.s.r&&(o[u]=jr(u)),a=o[u]+s;var d=f?(t[c]||[])[u]:t[a];d?h=sc(e,d,c,u,n,t,h):h=!1}}Pr(e,146)}function fc(e,t){t&&t["!merges"]&&(Pr(e,177,jl(t["!merges"].length)),t["!merges"].forEach((function(t){Pr(e,176,Vl(t))})),Pr(e,178))}function lc(e,t){t&&t["!cols"]&&(Pr(e,390),t["!cols"].forEach((function(t,r){t&&Pr(e,60,Jl(r,t))})),Pr(e,391))}function cc(e,t){t&&t["!ref"]&&(Pr(e,648),Pr(e,649,rc(qr(t["!ref"]))),Pr(e,650))}function hc(e,t,r){t["!links"].forEach((function(t){if(t[1].Target){var n=oa(r,-1,t[1].Target.replace(/#.*$/,""),aa.HLINK);Pr(e,494,$l(t,n))}})),delete t["!links"]}function uc(e,t,r,n){if(t["!comments"].length>0){var a=oa(n,-1,"../drawings/vmlDrawing"+(r+1)+".vml",aa.VML);Pr(e,551,_n("rId"+a)),t["!legacy"]=a}}function dc(e,t,r,n){if(t["!autofilter"]){var a=t["!autofilter"],i="string"===typeof a.ref?a.ref:Zr(a.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var s=r.Workbook.Names,o=Jr(i);o.s.r==o.e.r&&(o.e.r=Jr(t["!ref"]).e.r,i=Zr(o));for(var f=0;f<s.length;++f){var l=s[f];if("_xlnm._FilterDatabase"==l.Name&&l.Sheet==n){l.Ref="'"+r.SheetNames[n]+"'!"+i;break}}f==s.length&&s.push({Name:"_xlnm._FilterDatabase",Sheet:n,Ref:"'"+r.SheetNames[n]+"'!"+i}),Pr(e,161,In(qr(i))),Pr(e,162)}}function pc(e,t,r){Pr(e,133),Pr(e,137,tc(t,r)),Pr(e,138),Pr(e,134)}function mc(){}function gc(e,t){t["!protect"]&&Pr(e,535,nc(t["!protect"]))}function vc(e,t,r,n){var a=Dr(),i=r.SheetNames[e],s=r.Sheets[i]||{},o=i;try{r&&r.Workbook&&(o=r.Workbook.Sheets[e].CodeName||o)}catch(l){}var f=qr(s["!ref"]||"A1");if(f.e.c>16383||f.e.r>1048575){if(t.WTF)throw new Error("Range "+(s["!ref"]||"A1")+" exceeds format limit A1:XFD1048576");f.e.c=Math.min(f.e.c,16383),f.e.r=Math.min(f.e.c,1048575)}return s["!links"]=[],s["!comments"]=[],Pr(a,129),(r.vbaraw||s["!outline"])&&Pr(a,147,fl(o,s["!outline"])),Pr(a,148,il(f)),pc(a,s,r.Workbook),mc(a,s),lc(a,s,e,t,r),oc(a,s,e,t,r),gc(a,s),dc(a,s,r,e),fc(a,s),hc(a,s,n),s["!margins"]&&Pr(a,476,Ql(s["!margins"])),t&&!t.ignoreEC&&void 0!=t.ignoreEC||cc(a,s),uc(a,s,e,n),Pr(a,130),a.end()}function Tc(e,t){e.l+=10;var r=on(e,t-10);return{name:r}}var bc=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]];function wc(e){return e.Workbook&&e.Workbook.WBProps&&It(e.Workbook.WBProps.date1904)?"true":"false"}var Ec="][*?/\\".split("");function Ac(e,t){if(e.length>31){if(t)return!1;throw new Error("Sheet names cannot exceed 31 chars")}var r=!0;return Ec.forEach((function(n){if(-1!=e.indexOf(n)){if(!t)throw new Error("Sheet name cannot contain : \\ / ? * [ ]");r=!1}})),r}function yc(e,t,r){e.forEach((function(n,a){Ac(n);for(var i=0;i<a;++i)if(n==e[i])throw new Error("Duplicate Sheet Name: "+n);if(r){var s=t&&t[a]&&t[a].CodeName||n;if(95==s.charCodeAt(0)&&s.length>22)throw new Error("Bad Code Name: Worksheet"+s)}}))}function Sc(e){if(!e||!e.SheetNames||!e.Sheets)throw new Error("Invalid Workbook");if(!e.SheetNames.length)throw new Error("Workbook is empty");var t=e.Workbook&&e.Workbook.Sheets||[];yc(e.SheetNames,t,!!e.vbaraw);for(var r=0;r<e.SheetNames.length;++r)Gf(e.Sheets[e.SheetNames[r]],e.SheetNames[r],r)}function _c(e){var t=[wt];t[t.length]=Gt("workbook",null,{xmlns:$t[0],"xmlns:r":zt.r});var r=e.Workbook&&(e.Workbook.Names||[]).length>0,n={codeName:"ThisWorkbook"};e.Workbook&&e.Workbook.WBProps&&(bc.forEach((function(t){null!=e.Workbook.WBProps[t[0]]&&e.Workbook.WBProps[t[0]]!=t[1]&&(n[t[0]]=e.Workbook.WBProps[t[0]])})),e.Workbook.WBProps.CodeName&&(n.codeName=e.Workbook.WBProps.CodeName,delete n.CodeName)),t[t.length]=Gt("workbookPr",null,n);var a=e.Workbook&&e.Workbook.Sheets||[],i=0;if(a&&a[0]&&a[0].Hidden){for(t[t.length]="<bookViews>",i=0;i!=e.SheetNames.length;++i){if(!a[i])break;if(!a[i].Hidden)break}i==e.SheetNames.length&&(i=0),t[t.length]='<workbookView firstSheet="'+i+'" activeTab="'+i+'"/>',t[t.length]="</bookViews>"}for(t[t.length]="<sheets>",i=0;i!=e.SheetNames.length;++i){var s={name:_t(e.SheetNames[i].slice(0,31))};if(s.sheetId=""+(i+1),s["r:id"]="rId"+(i+1),a[i])switch(a[i].Hidden){case 1:s.state="hidden";break;case 2:s.state="veryHidden";break}t[t.length]=Gt("sheet",null,s)}return t[t.length]="</sheets>",r&&(t[t.length]="<definedNames>",e.Workbook&&e.Workbook.Names&&e.Workbook.Names.forEach((function(e){var r={name:e.Name};e.Comment&&(r.comment=e.Comment),null!=e.Sheet&&(r.localSheetId=""+e.Sheet),e.Hidden&&(r.hidden="1"),e.Ref&&(t[t.length]=Gt("definedName",_t(e.Ref),r))})),t[t.length]="</definedNames>"),t.length>2&&(t[t.length]="</workbook>",t[1]=t[1].replace("/>",">")),t.join("")}function xc(e,t){var r={};return r.Hidden=e.read_shift(4),r.iTabID=e.read_shift(4),r.strRelID=Sn(e,t-8),r.name=on(e),r}function Oc(e,t){return t||(t=Nr(127)),t.write_shift(4,e.Hidden),t.write_shift(4,e.iTabID),_n(e.strRelID,t),fn(e.name.slice(0,31),t),t.length>t.l?t.slice(0,t.l):t}function Cc(e,t){var r={},n=e.read_shift(4);r.defaultThemeVersion=e.read_shift(4);var a=t>8?on(e):"";return a.length>0&&(r.CodeName=a),r.autoCompressPictures=!!(65536&n),r.backupFile=!!(64&n),r.checkCompatibility=!!(4096&n),r.date1904=!!(1&n),r.filterPrivacy=!!(8&n),r.hidePivotFieldList=!!(1024&n),r.promptedSolutions=!!(16&n),r.publishItems=!!(2048&n),r.refreshAllConnections=!!(262144&n),r.saveExternalLinkValues=!!(128&n),r.showBorderUnselectedTables=!!(4&n),r.showInkAnnotation=!!(32&n),r.showObjects=["all","placeholders","none"][n>>13&3],r.showPivotChartFilter=!!(32768&n),r.updateLinks=["userSet","never","always"][n>>8&3],r}function Rc(e,t){t||(t=Nr(72));var r=0;return e&&e.filterPrivacy&&(r|=8),t.write_shift(4,r),t.write_shift(4,0),wn(e&&e.CodeName||"ThisWorkbook",t),t.slice(0,t.l)}function kc(e,t,r){var n=e.l+t;e.l+=4,e.l+=1;var a=e.read_shift(4),i=yn(e),s=kf(e,0,r),o=En(e);e.l=n;var f={Name:i,Ptg:s};return a<268435455&&(f.Sheet=a),o&&(f.Comment=o),f}function Ic(e,t){Pr(e,143);for(var r=0;r!=t.SheetNames.length;++r){var n=t.Workbook&&t.Workbook.Sheets&&t.Workbook.Sheets[r]&&t.Workbook.Sheets[r].Hidden||0,a={Hidden:n,iTabID:r+1,strRelID:"rId"+(r+1),name:t.SheetNames[r]};Pr(e,156,Oc(a))}Pr(e,144)}function Nc(e,t){t||(t=Nr(127));for(var r=0;4!=r;++r)t.write_shift(4,0);return fn("SheetJS",t),fn(n.version,t),fn(n.version,t),fn("7262",t),t.length>t.l?t.slice(0,t.l):t}function Dc(e,t){t||(t=Nr(29)),t.write_shift(-4,0),t.write_shift(-4,460),t.write_shift(4,28800),t.write_shift(4,17600),t.write_shift(4,500),t.write_shift(4,e),t.write_shift(4,e);var r=120;return t.write_shift(1,r),t.length>t.l?t.slice(0,t.l):t}function Pc(e,t){if(t.Workbook&&t.Workbook.Sheets){for(var r=t.Workbook.Sheets,n=0,a=-1,i=-1;n<r.length;++n)!r[n]||!r[n].Hidden&&-1==a?a=n:1==r[n].Hidden&&-1==i&&(i=n);i>a||(Pr(e,135),Pr(e,158,Dc(a)),Pr(e,136))}}function Lc(e,t){var r=Dr();return Pr(r,131),Pr(r,128,Nc()),Pr(r,153,Rc(e.Workbook&&e.Workbook.WBProps||null)),Pc(r,e,t),Ic(r,e,t),Pr(r,132),r.end()}function Mc(e,t,r){return(".bin"===t.slice(-4)?Lc:_c)(e,r)}function Fc(e,t,r,n,a){return(".bin"===t.slice(-4)?vc:el)(e,r,n,a)}function Uc(e,t,r){return(".bin"===t.slice(-4)?bs:Yi)(e,r)}function Bc(e,t,r){return(".bin"===t.slice(-4)?ki:xi)(e,r)}function Wc(e,t,r){return(".bin"===t.slice(-4)?Ws:Ds)(e,r)}function Hc(e){return(".bin"===e.slice(-4)?Cs:Rs)()}function Gc(e,t){var r=[];return e.Props&&r.push(Ea(e.Props,t)),e.Custprops&&r.push(Aa(e.Props,e.Custprops,t)),r.join("")}function Vc(){return""}function jc(e,t){var r=['<Style ss:ID="Default" ss:Name="Normal"><NumberFormat/></Style>'];return t.cellXfs.forEach((function(e,t){var n=[];n.push(Gt("NumberFormat",null,{"ss:Format":_t(z[e.numFmtId])}));var a={"ss:ID":"s"+(21+t)};r.push(Gt("Style",n.join(""),a))})),Gt("Styles",r.join(""))}function zc(e){return Gt("NamedRange",null,{"ss:Name":e.Name,"ss:RefersTo":"="+zs(e.Ref,{r:0,c:0})})}function $c(e){if(!((e||{}).Workbook||{}).Names)return"";for(var t=e.Workbook.Names,r=[],n=0;n<t.length;++n){var a=t[n];null==a.Sheet&&(a.Name.match(/^_xlfn\./)||r.push(zc(a)))}return Gt("Names",r.join(""))}function Yc(e,t,r,n){if(!e)return"";if(!((n||{}).Workbook||{}).Names)return"";for(var a=n.Workbook.Names,i=[],s=0;s<a.length;++s){var o=a[s];o.Sheet==r&&(o.Name.match(/^_xlfn\./)||i.push(zc(o)))}return i.join("")}function Xc(e,t,r,n){if(!e)return"";var a=[];if(e["!margins"]&&(a.push("<PageSetup>"),e["!margins"].header&&a.push(Gt("Header",null,{"x:Margin":e["!margins"].header})),e["!margins"].footer&&a.push(Gt("Footer",null,{"x:Margin":e["!margins"].footer})),a.push(Gt("PageMargins",null,{"x:Bottom":e["!margins"].bottom||"0.75","x:Left":e["!margins"].left||"0.7","x:Right":e["!margins"].right||"0.7","x:Top":e["!margins"].top||"0.75"})),a.push("</PageSetup>")),n&&n.Workbook&&n.Workbook.Sheets&&n.Workbook.Sheets[r])if(n.Workbook.Sheets[r].Hidden)a.push(Gt("Visible",1==n.Workbook.Sheets[r].Hidden?"SheetHidden":"SheetVeryHidden",{}));else{for(var i=0;i<r;++i)if(n.Workbook.Sheets[i]&&!n.Workbook.Sheets[i].Hidden)break;i==r&&a.push("<Selected/>")}return((((n||{}).Workbook||{}).Views||[])[0]||{}).RTL&&a.push("<DisplayRightToLeft/>"),e["!protect"]&&(a.push(Wt("ProtectContents","True")),e["!protect"].objects&&a.push(Wt("ProtectObjects","True")),e["!protect"].scenarios&&a.push(Wt("ProtectScenarios","True")),null==e["!protect"].selectLockedCells||e["!protect"].selectLockedCells?null==e["!protect"].selectUnlockedCells||e["!protect"].selectUnlockedCells||a.push(Wt("EnableSelection","UnlockedCells")):a.push(Wt("EnableSelection","NoSelection")),[["formatCells","AllowFormatCells"],["formatColumns","AllowSizeCols"],["formatRows","AllowSizeRows"],["insertColumns","AllowInsertCols"],["insertRows","AllowInsertRows"],["insertHyperlinks","AllowInsertHyperlinks"],["deleteColumns","AllowDeleteCols"],["deleteRows","AllowDeleteRows"],["sort","AllowSort"],["autoFilter","AllowFilter"],["pivotTables","AllowUsePivotTables"]].forEach((function(t){e["!protect"][t[0]]&&a.push("<"+t[1]+"/>")}))),0==a.length?"":Gt("WorksheetOptions",a.join(""),{xmlns:Yt.x})}function Kc(e){return e.map((function(e){var t=kt(e.t||""),r=Gt("ss:Data",t,{xmlns:"http://www.w3.org/TR/REC-html40"});return Gt("Comment",r,{"ss:Author":e.a})})).join("")}function Jc(e,t,r,n,a,i,s){if(!e||void 0==e.v&&void 0==e.f)return"";var o={};if(e.f&&(o["ss:Formula"]="="+_t(zs(e.f,s))),e.F&&e.F.slice(0,t.length)==t){var f=Xr(e.F.slice(t.length+1));o["ss:ArrayRange"]="RC:R"+(f.r==s.r?"":"["+(f.r-s.r)+"]")+"C"+(f.c==s.c?"":"["+(f.c-s.c)+"]")}if(e.l&&e.l.Target&&(o["ss:HRef"]=_t(e.l.Target),e.l.Tooltip&&(o["x:HRefScreenTip"]=_t(e.l.Tooltip))),r["!merges"])for(var l=r["!merges"],c=0;c!=l.length;++c)l[c].s.c==s.c&&l[c].s.r==s.r&&(l[c].e.c>l[c].s.c&&(o["ss:MergeAcross"]=l[c].e.c-l[c].s.c),l[c].e.r>l[c].s.r&&(o["ss:MergeDown"]=l[c].e.r-l[c].s.r));var h="",u="";switch(e.t){case"z":if(!n.sheetStubs)return"";break;case"n":h="Number",u=String(e.v);break;case"b":h="Boolean",u=e.v?"1":"0";break;case"e":h="Error",u=Qn[e.v];break;case"d":h="DateTime",u=new Date(e.v).toISOString(),null==e.z&&(e.z=e.z||z[14]);break;case"s":h="String",u=Rt(e.v||"");break}var d=Hf(n.cellXfs,e,n);o["ss:StyleID"]="s"+(21+d),o["ss:Index"]=s.c+1;var p=null!=e.v?u:"",m="z"==e.t?"":'<Data ss:Type="'+h+'">'+p+"</Data>";return(e.c||[]).length>0&&(m+=Kc(e.c)),Gt("Cell",m,o)}function Zc(e,t){var r='<Row ss:Index="'+(e+1)+'"';return t&&(t.hpt&&!t.hpx&&(t.hpx=ji(t.hpt)),t.hpx&&(r+=' ss:AutoFitHeight="0" ss:Height="'+t.hpx+'"'),t.hidden&&(r+=' ss:Hidden="1"')),r+">"}function qc(e,t,r,n){if(!e["!ref"])return"";var a=qr(e["!ref"]),i=e["!merges"]||[],s=0,o=[];e["!cols"]&&e["!cols"].forEach((function(e,t){Wi(e);var r=!!e.width,n=Bf(t,e),a={"ss:Index":t+1};r&&(a["ss:Width"]=Fi(n.width)),e.hidden&&(a["ss:Hidden"]="1"),o.push(Gt("Column",null,a))}));for(var f=Array.isArray(e),l=a.s.r;l<=a.e.r;++l){for(var c=[Zc(l,(e["!rows"]||[])[l])],h=a.s.c;h<=a.e.c;++h){var u=!1;for(s=0;s!=i.length;++s)if(!(i[s].s.c>h)&&!(i[s].s.r>l)&&!(i[s].e.c<h)&&!(i[s].e.r<l)){i[s].s.c==h&&i[s].s.r==l||(u=!0);break}if(!u){var d={r:l,c:h},p=Kr(d),m=f?(e[l]||[])[h]:e[p];c.push(Jc(m,p,e,t,r,n,d))}}c.push("</Row>"),c.length>2&&o.push(c.join(""))}return o.join("")}function Qc(e,t,r){var n=[],a=r.SheetNames[e],i=r.Sheets[a],s=i?Yc(i,t,e,r):"";return s.length>0&&n.push("<Names>"+s+"</Names>"),s=i?qc(i,t,e,r):"",s.length>0&&n.push("<Table>"+s+"</Table>"),n.push(Xc(i,t,e,r)),n.join("")}function eh(e,t){t||(t={}),e.SSF||(e.SSF=dt(z)),e.SSF&&(He(),We(e.SSF),t.revssf=et(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF,t.cellXfs=[],Hf(t.cellXfs,{},{revssf:{General:0}}));var r=[];r.push(Gc(e,t)),r.push(Vc(e,t)),r.push(""),r.push("");for(var n=0;n<e.SheetNames.length;++n)r.push(Gt("Worksheet",Qc(n,t,e),{"ss:Name":_t(e.SheetNames[n])}));return r[2]=jc(e,t),r[3]=$c(e,t),wt+Gt("Workbook",r.join(""),{xmlns:Yt.ss,"xmlns:o":Yt.o,"xmlns:x":Yt.x,"xmlns:ss":Yt.ss,"xmlns:dt":Yt.dt,"xmlns:html":Yt.html})}var th={SI:"e0859ff2f94f6810ab9108002b27b3d9",DSI:"02d5cdd59c2e1b10939708002b2cf9ae",UDI:"05d5cdd59c2e1b10939708002b2cf9ae"};function rh(e,t){var r,n=[],a=[],i=[],s=0,o=qe(Xn,"n"),f=qe(Kn,"n");if(e.Props)for(r=Ze(e.Props),s=0;s<r.length;++s)(Object.prototype.hasOwnProperty.call(o,r[s])?n:Object.prototype.hasOwnProperty.call(f,r[s])?a:i).push([r[s],e.Props[r[s]]]);if(e.Custprops)for(r=Ze(e.Custprops),s=0;s<r.length;++s)Object.prototype.hasOwnProperty.call(e.Props||{},r[s])||(Object.prototype.hasOwnProperty.call(o,r[s])?n:Object.prototype.hasOwnProperty.call(f,r[s])?a:i).push([r[s],e.Custprops[r[s]]]);var l=[];for(s=0;s<i.length;++s)_a.indexOf(i[s][0])>-1||va.indexOf(i[s][0])>-1||null!=i[s][1]&&l.push(i[s]);a.length&&Ye.utils.cfb_add(t,"/SummaryInformation",Ca(a,th.SI,f,Kn)),(n.length||l.length)&&Ye.utils.cfb_add(t,"/DocumentSummaryInformation",Ca(n,th.DSI,o,Xn,l.length?l:null,th.UDI))}function nh(e,t){var r=t||{},n=Ye.utils.cfb_new({root:"R"}),a="/Workbook";switch(r.bookType||"xls"){case"xls":r.bookType="biff8";case"xla":r.bookType||(r.bookType="xla");case"biff8":a="/Workbook",r.biff=8;break;case"biff5":a="/Book",r.biff=5;break;default:throw new Error("invalid type "+r.bookType+" for XLS CFB")}return Ye.utils.cfb_add(n,a,yh(e,r)),8==r.biff&&(e.Props||e.Custprops)&&rh(e,n),8==r.biff&&e.vbaraw&&Hs(n,Ye.read(e.vbaraw,{type:"string"==typeof e.vbaraw?"binary":"buffer"})),n}var ah={0:{f:tl},1:{f:ll},2:{f:Rl},3:{f:vl},4:{f:dl},5:{f:_l},6:{f:Pl},7:{f:El},8:{f:Hl},9:{f:Wl},10:{f:Ul},11:{f:Bl},12:{f:hl},13:{f:Il},14:{f:bl},15:{f:ml},16:{f:Ol},17:{f:Ml},18:{f:yl},19:{f:hn},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:kc},40:{},42:{},43:{f:Ji},44:{f:Xi},45:{f:es},46:{f:is},47:{f:rs},48:{},49:{f:an},50:{},51:{f:ys},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:pi},62:{f:Dl},63:{f:ks},64:{f:ac},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:Ir,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:ec},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:ol},148:{f:al,p:16},151:{f:Yl},152:{},153:{f:Cc},154:{},155:{},156:{f:xc},157:{},158:{},159:{T:1,f:Oi},160:{T:-1},161:{T:1,f:kn},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:Gl},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:Es},336:{T:-1},337:{f:xs,T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:Sn},357:{},358:{},359:{},360:{T:1},361:{},362:{f:fi},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:Xl},427:{f:Kl},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:ql},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:sl},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:zl},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:Sn},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:Us},633:{T:1},634:{T:-1},635:{T:1,f:Ms},636:{T:-1},637:{f:dn},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:Tc},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:ic},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}};function ih(e,t,r,n){var a=t;if(!isNaN(a)){var i=n||(r||[]).length||0,s=e.next(4);s.write_shift(2,a),s.write_shift(2,i),i>0&&gr(r)&&e.push(r)}}function sh(e,t,r,n){var a=n||(r||[]).length||0;if(a<=8224)return ih(e,t,r,a);var i=t;if(!isNaN(i)){var s=r.parts||[],o=0,f=0,l=0;while(l+(s[o]||8224)<=8224)l+=s[o]||8224,o++;var c=e.next(4);c.write_shift(2,i),c.write_shift(2,l),e.push(r.slice(f,f+l)),f+=l;while(f<a){c=e.next(4),c.write_shift(2,60),l=0;while(l+(s[o]||8224)<=8224)l+=s[o]||8224,o++;c.write_shift(2,l),e.push(r.slice(f,f+l)),f+=l}}}function oh(e,t,r){return e||(e=Nr(7)),e.write_shift(2,t),e.write_shift(2,r),e.write_shift(2,0),e.write_shift(1,0),e}function fh(e,t,r,n){var a=Nr(9);return oh(a,e,t),Pa(r,n||"b",a),a}function lh(e,t,r){var n=Nr(8+2*r.length);return oh(n,e,t),n.write_shift(1,r.length),n.write_shift(r.length,r,"sbcs"),n.l<n.length?n.slice(0,n.l):n}function ch(e,t,r,n){if(null!=t.v)switch(t.t){case"d":case"n":var a="d"==t.t?nt(ht(t.v)):t.v;return void(a==(0|a)&&a>=0&&a<65536?ih(e,2,Ti(r,n,a)):ih(e,3,vi(r,n,a)));case"b":case"e":return void ih(e,5,fh(r,n,t.v,t.t));case"s":case"str":return void ih(e,4,lh(r,n,(t.v||"").slice(0,255)))}ih(e,1,oh(null,r,n))}function hh(e,t,r,n){var a,i=Array.isArray(t),s=qr(t["!ref"]||"A1"),o="",f=[];if(s.e.c>255||s.e.r>16383){if(n.WTF)throw new Error("Range "+(t["!ref"]||"A1")+" exceeds format limit A1:IV16384");s.e.c=Math.min(s.e.c,255),s.e.r=Math.min(s.e.c,16383),a=Zr(s)}for(var l=s.s.r;l<=s.e.r;++l){o=Wr(l);for(var c=s.s.c;c<=s.e.c;++c){l===s.s.r&&(f[c]=jr(c)),a=f[c]+o;var h=i?(t[l]||[])[c]:t[a];h&&ch(e,h,l,c,n)}}}function uh(e,t){var r=t||{};null!=g&&null==r.dense&&(r.dense=g);for(var n=Dr(),a=0,i=0;i<e.SheetNames.length;++i)e.SheetNames[i]==r.sheet&&(a=i);if(0==a&&r.sheet&&e.SheetNames[0]!=r.sheet)throw new Error("Sheet not found: "+r.sheet);return ih(n,4==r.biff?1033:3==r.biff?521:9,Ya(e,16,r)),hh(n,e.Sheets[e.SheetNames[a]],a,r,e),ih(n,10),n.end()}function dh(e,t,r){ih(e,49,Qa({sz:12,color:{theme:1},name:"Arial",family:2,scheme:"minor"},r))}function ph(e,t,r){t&&[[5,8],[23,26],[41,44],[50,392]].forEach((function(n){for(var a=n[0];a<=n[1];++a)null!=t[a]&&ih(e,1054,ri(a,t[a],r))}))}function mh(e,t){var r=Nr(19);r.write_shift(4,2151),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,1),r.write_shift(4,0),ih(e,2151,r),r=Nr(39),r.write_shift(4,2152),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,0),r.write_shift(4,0),r.write_shift(2,1),r.write_shift(4,4),r.write_shift(2,0),$a(qr(t["!ref"]||"A1"),r),r.write_shift(4,4),ih(e,2152,r)}function gh(e,t){for(var r=0;r<16;++r)ih(e,224,ai({numFmtId:0,style:!0},0,t));t.cellXfs.forEach((function(r){ih(e,224,ai(r,0,t))}))}function vh(e,t){for(var r=0;r<t["!links"].length;++r){var n=t["!links"][r];ih(e,440,hi(n)),n[1].Tooltip&&ih(e,2048,ui(n))}delete t["!links"]}function Th(e,t){if(t){var r=0;t.forEach((function(t,n){++r<=256&&t&&ih(e,125,mi(Bf(n,t),n))}))}}function bh(e,t,r,n,a){var i=16+Hf(a.cellXfs,t,a);if(null!=t.v||t.bf)if(t.bf)ih(e,6,xf(t,r,n,a,i));else switch(t.t){case"d":case"n":var s="d"==t.t?nt(ht(t.v)):t.v;ih(e,515,oi(r,n,s,i,a));break;case"b":case"e":ih(e,517,si(r,n,t.v,i,a,t.t));break;case"s":case"str":if(a.bookSST){var o=Uf(a.Strings,t.v,a.revStrings);ih(e,253,ei(r,n,o,i,a))}else ih(e,516,ti(r,n,(t.v||"").slice(0,255),i,a));break;default:ih(e,513,Va(r,n,i))}else ih(e,513,Va(r,n,i))}function wh(e,t,r){var n,a=Dr(),i=r.SheetNames[e],s=r.Sheets[i]||{},o=(r||{}).Workbook||{},f=(o.Sheets||[])[e]||{},l=Array.isArray(s),c=8==t.biff,h="",u=[],d=qr(s["!ref"]||"A1"),p=c?65536:16384;if(d.e.c>255||d.e.r>=p){if(t.WTF)throw new Error("Range "+(s["!ref"]||"A1")+" exceeds format limit A1:IV16384");d.e.c=Math.min(d.e.c,255),d.e.r=Math.min(d.e.c,p-1)}ih(a,2057,Ya(r,16,t)),ih(a,13,Da(1)),ih(a,12,Da(100)),ih(a,15,Ia(!0)),ih(a,17,Ia(!1)),ih(a,16,Dn(.001)),ih(a,95,Ia(!0)),ih(a,42,Ia(!1)),ih(a,43,Ia(!1)),ih(a,130,Da(1)),ih(a,128,ii([0,0])),ih(a,131,Ia(!1)),ih(a,132,Ia(!1)),c&&Th(a,s["!cols"]),ih(a,512,ni(d,t)),c&&(s["!links"]=[]);for(var m=d.s.r;m<=d.e.r;++m){h=Wr(m);for(var g=d.s.c;g<=d.e.c;++g){m===d.s.r&&(u[g]=jr(g)),n=u[g]+h;var v=l?(s[m]||[])[g]:s[n];v&&(bh(a,v,m,g,t),c&&v.l&&s["!links"].push([n,v.l]))}}var T=f.CodeName||f.name||i;return c&&ih(a,574,qa((o.Views||[])[0])),c&&(s["!merges"]||[]).length&&ih(a,229,ci(s["!merges"])),c&&vh(a,s),ih(a,442,Wa(T,t)),c&&mh(a,s),ih(a,10),a.end()}function Eh(e,t,r){var n=Dr(),a=(e||{}).Workbook||{},i=a.Sheets||[],s=a.WBProps||{},o=8==r.biff,f=5==r.biff;if(ih(n,2057,Ya(e,5,r)),"xla"==r.bookType&&ih(n,135),ih(n,225,o?Da(1200):null),ih(n,193,Ra(2)),f&&ih(n,191),f&&ih(n,192),ih(n,226),ih(n,92,Xa("SheetJS",r)),ih(n,66,Da(o?1200:1252)),o&&ih(n,353,Da(0)),o&&ih(n,448),ih(n,317,gi(e.SheetNames.length)),o&&e.vbaraw&&ih(n,211),o&&e.vbaraw){var l=s.CodeName||"ThisWorkbook";ih(n,442,Wa(l,r))}ih(n,156,Da(17)),ih(n,25,Ia(!1)),ih(n,18,Ia(!1)),ih(n,19,Da(0)),o&&ih(n,431,Ia(!1)),o&&ih(n,444,Da(0)),ih(n,61,Za(r)),ih(n,64,Ia(!1)),ih(n,141,Da(0)),ih(n,34,Ia("true"==wc(e))),ih(n,14,Ia(!0)),o&&ih(n,439,Ia(!1)),ih(n,218,Da(0)),dh(n,e,r),ph(n,e.SSF,r),gh(n,r),o&&ih(n,352,Ia(!1));var c=n.end(),h=Dr();o&&ih(h,140,di()),o&&r.Strings&&sh(h,252,Ja(r.Strings,r)),ih(h,10);var u=h.end(),d=Dr(),p=0,m=0;for(m=0;m<e.SheetNames.length;++m)p+=(o?12:11)+(o?2:1)*e.SheetNames[m].length;var g=c.length+p+u.length;for(m=0;m<e.SheetNames.length;++m){var v=i[m]||{};ih(d,133,Ka({pos:g,hs:v.Hidden||0,dt:0,name:e.SheetNames[m]},r)),g+=t[m].length}var T=d.end();if(p!=T.length)throw new Error("BS8 "+p+" != "+T.length);var b=[];return c.length&&b.push(c),T.length&&b.push(T),u.length&&b.push(u),R(b)}function Ah(e,t){var r=t||{},n=[];e&&!e.SSF&&(e.SSF=dt(z)),e&&e.SSF&&(He(),We(e.SSF),r.revssf=et(e.SSF),r.revssf[e.SSF[65535]]=0,r.ssf=e.SSF),r.Strings=[],r.Strings.Count=0,r.Strings.Unique=0,au(r),r.cellXfs=[],Hf(r.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});for(var a=0;a<e.SheetNames.length;++a)n[n.length]=wh(a,r,e);return n.unshift(Eh(e,n,r)),R(n)}function yh(e,t){for(var r=0;r<=e.SheetNames.length;++r){var n=e.Sheets[e.SheetNames[r]];if(n&&n["!ref"]){var a=Jr(n["!ref"]);a.e.c>255&&"undefined"!=typeof console&&console.error&&console.error("Worksheet '"+e.SheetNames[r]+"' extends beyond column IV (255).  Data may be lost.")}}var i=t||{};switch(i.biff||2){case 8:case 5:return Ah(e,t);case 4:case 3:case 2:return uh(e,t)}throw new Error("invalid type "+i.bookType+" for BIFF")}function Sh(e,t,r,n){for(var a=e["!merges"]||[],i=[],s=t.s.c;s<=t.e.c;++s){for(var o=0,f=0,l=0;l<a.length;++l)if(!(a[l].s.r>r||a[l].s.c>s)&&!(a[l].e.r<r||a[l].e.c<s)){if(a[l].s.r<r||a[l].s.c<s){o=-1;break}o=a[l].e.r-a[l].s.r+1,f=a[l].e.c-a[l].s.c+1;break}if(!(o<0)){var c=Kr({r:r,c:s}),h=n.dense?(e[r]||[])[s]:e[c],u=h&&null!=h.v&&(h.h||Ct(h.w||(en(h),h.w)||""))||"",d={};o>1&&(d.rowspan=o),f>1&&(d.colspan=f),n.editable?u='<span contenteditable="true">'+u+"</span>":h&&(d["data-t"]=h&&h.t||"z",null!=h.v&&(d["data-v"]=h.v),null!=h.z&&(d["data-z"]=h.z),h.l&&"#"!=(h.l.Target||"#").charAt(0)&&(u='<a href="'+h.l.Target+'">'+u+"</a>")),d.id=(n.id||"sjs")+"-"+c,i.push(Gt("td",u,d))}}var p="<tr>";return p+i.join("")+"</tr>"}var _h='<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',xh="</body></html>";function Oh(e,t,r){var n=[];return n.join("")+"<table"+(r&&r.id?' id="'+r.id+'"':"")+">"}function Ch(e,t){var r=t||{},n=null!=r.header?r.header:_h,a=null!=r.footer?r.footer:xh,i=[n],s=Jr(e["!ref"]);r.dense=Array.isArray(e),i.push(Oh(e,s,r));for(var o=s.s.r;o<=s.e.r;++o)i.push(Sh(e,s,o,r));return i.push("</table>"+a),i.join("")}function Rh(e,t,r){var n=r||{};null!=g&&(n.dense=g);var a=0,i=0;if(null!=n.origin)if("number"==typeof n.origin)a=n.origin;else{var s="string"==typeof n.origin?Xr(n.origin):n.origin;a=s.r,i=s.c}var o=t.getElementsByTagName("tr"),f=Math.min(n.sheetRows||1e7,o.length),l={s:{r:0,c:0},e:{r:a,c:i}};if(e["!ref"]){var c=Jr(e["!ref"]);l.s.r=Math.min(l.s.r,c.s.r),l.s.c=Math.min(l.s.c,c.s.c),l.e.r=Math.max(l.e.r,c.e.r),l.e.c=Math.max(l.e.c,c.e.c),-1==a&&(l.e.r=a=c.e.r+1)}var h=[],u=0,d=e["!rows"]||(e["!rows"]=[]),p=0,m=0,v=0,T=0,b=0,w=0;for(e["!cols"]||(e["!cols"]=[]);p<o.length&&m<f;++p){var E=o[p];if(Nh(E)){if(n.display)continue;d[m]={hidden:!0}}var A=E.children;for(v=T=0;v<A.length;++v){var y=A[v];if(!n.display||!Nh(y)){var S=y.hasAttribute("data-v")?y.getAttribute("data-v"):y.hasAttribute("v")?y.getAttribute("v"):Ut(y.innerHTML),_=y.getAttribute("data-z")||y.getAttribute("z");for(u=0;u<h.length;++u){var x=h[u];x.s.c==T+i&&x.s.r<m+a&&m+a<=x.e.r&&(T=x.e.c+1-i,u=-1)}w=+y.getAttribute("colspan")||1,((b=+y.getAttribute("rowspan")||1)>1||w>1)&&h.push({s:{r:m+a,c:T+i},e:{r:m+a+(b||1)-1,c:T+i+(w||1)-1}});var O={t:"s",v:S},C=y.getAttribute("data-t")||y.getAttribute("t")||"";null!=S&&(0==S.length?O.t=C||"z":n.raw||0==S.trim().length||"s"==C||("TRUE"===S?O={t:"b",v:!0}:"FALSE"===S?O={t:"b",v:!1}:isNaN(mt(S))?isNaN(vt(S).getDate())||(O={t:"d",v:ht(S)},n.cellDates||(O={t:"n",v:nt(O.v)}),O.z=n.dateNF||z[14]):O={t:"n",v:mt(S)})),void 0===O.z&&null!=_&&(O.z=_);var R="",k=y.getElementsByTagName("A");if(k&&k.length)for(var I=0;I<k.length;++I)if(k[I].hasAttribute("href")&&(R=k[I].getAttribute("href"),"#"!=R.charAt(0)))break;R&&"#"!=R.charAt(0)&&(O.l={Target:R}),n.dense?(e[m+a]||(e[m+a]=[]),e[m+a][T+i]=O):e[Kr({c:T+i,r:m+a})]=O,l.e.c<T+i&&(l.e.c=T+i),T+=w}}++m}return h.length&&(e["!merges"]=(e["!merges"]||[]).concat(h)),l.e.r=Math.max(l.e.r,m-1+a),e["!ref"]=Zr(l),m>=f&&(e["!fullref"]=Zr((l.e.r=o.length-p+m-1+a,l))),e}function kh(e,t){var r=t||{},n=r.dense?[]:{};return Rh(n,e,t)}function Ih(e,t){return tn(kh(e,t),t)}function Nh(e){var t="",r=Dh(e);return r&&(t=r(e).getPropertyValue("display")),t||(t=e.style&&e.style.display),"none"===t}function Dh(e){return e.ownerDocument.defaultView&&"function"===typeof e.ownerDocument.defaultView.getComputedStyle?e.ownerDocument.defaultView.getComputedStyle:"function"===typeof getComputedStyle?getComputedStyle:null}var Ph=function(){var e=["<office:master-styles>",'<style:master-page style:name="mp1" style:page-layout-name="mp1">',"<style:header/>",'<style:header-left style:display="false"/>',"<style:footer/>",'<style:footer-left style:display="false"/>',"</style:master-page>","</office:master-styles>"].join(""),t="<office:document-styles "+Ht({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","office:version":"1.2"})+">"+e+"</office:document-styles>";return function(){return wt+t}}(),Lh=function(){var e=function(e){return _t(e).replace(/  +/g,(function(e){return'<text:s text:c="'+e.length+'"/>'})).replace(/\t/g,"<text:tab/>").replace(/\n/g,"</text:p><text:p>").replace(/^ /,"<text:s/>").replace(/ $/,"<text:s/>")},t="          <table:table-cell />\n",r="          <table:covered-table-cell/>\n",n=function(n,a,i){var s=[];s.push('      <table:table table:name="'+_t(a.SheetNames[i])+'" table:style-name="ta1">\n');var o=0,f=0,l=Jr(n["!ref"]||"A1"),c=n["!merges"]||[],h=0,u=Array.isArray(n);if(n["!cols"])for(f=0;f<=l.e.c;++f)s.push("        <table:table-column"+(n["!cols"][f]?' table:style-name="co'+n["!cols"][f].ods+'"':"")+"></table:table-column>\n");var d="",p=n["!rows"]||[];for(o=0;o<l.s.r;++o)d=p[o]?' table:style-name="ro'+p[o].ods+'"':"",s.push("        <table:table-row"+d+"></table:table-row>\n");for(;o<=l.e.r;++o){for(d=p[o]?' table:style-name="ro'+p[o].ods+'"':"",s.push("        <table:table-row"+d+">\n"),f=0;f<l.s.c;++f)s.push(t);for(;f<=l.e.c;++f){var m=!1,g={},v="";for(h=0;h!=c.length;++h)if(!(c[h].s.c>f)&&!(c[h].s.r>o)&&!(c[h].e.c<f)&&!(c[h].e.r<o)){c[h].s.c==f&&c[h].s.r==o||(m=!0),g["table:number-columns-spanned"]=c[h].e.c-c[h].s.c+1,g["table:number-rows-spanned"]=c[h].e.r-c[h].s.r+1;break}if(m)s.push(r);else{var T=Kr({r:o,c:f}),b=u?(n[o]||[])[f]:n[T];if(b&&b.f&&(g["table:formula"]=_t(Lf(b.f)),b.F&&b.F.slice(0,T.length)==T)){var w=Jr(b.F);g["table:number-matrix-columns-spanned"]=w.e.c-w.s.c+1,g["table:number-matrix-rows-spanned"]=w.e.r-w.s.r+1}if(b){switch(b.t){case"b":v=b.v?"TRUE":"FALSE",g["office:value-type"]="boolean",g["office:boolean-value"]=b.v?"true":"false";break;case"n":v=b.w||String(b.v||0),g["office:value-type"]="float",g["office:value"]=b.v||0;break;case"s":case"str":v=null==b.v?"":b.v,g["office:value-type"]="string";break;case"d":v=b.w||ht(b.v).toISOString(),g["office:value-type"]="date",g["office:date-value"]=ht(b.v).toISOString(),g["table:style-name"]="ce1";break;default:s.push(t);continue}var E=e(v);if(b.l&&b.l.Target){var A=b.l.Target;A="#"==A.charAt(0)?"#"+Mf(A.slice(1)):A,"#"==A.charAt(0)||A.match(/^\w+:/)||(A="../"+A),E=Gt("text:a",E,{"xlink:href":A.replace(/&/g,"&amp;")})}s.push("          "+Gt("table:table-cell",Gt("text:p",E,{}),g)+"\n")}else s.push(t)}}s.push("        </table:table-row>\n")}return s.push("      </table:table>\n"),s.join("")},a=function(e,t){e.push(" <office:automatic-styles>\n"),e.push('  <number:date-style style:name="N37" number:automatic-order="true">\n'),e.push('   <number:month number:style="long"/>\n'),e.push("   <number:text>/</number:text>\n"),e.push('   <number:day number:style="long"/>\n'),e.push("   <number:text>/</number:text>\n"),e.push("   <number:year/>\n"),e.push("  </number:date-style>\n");var r=0;t.SheetNames.map((function(e){return t.Sheets[e]})).forEach((function(t){if(t&&t["!cols"])for(var n=0;n<t["!cols"].length;++n)if(t["!cols"][n]){var a=t["!cols"][n];if(null==a.width&&null==a.wpx&&null==a.wch)continue;Wi(a),a.ods=r;var i=t["!cols"][n].wpx+"px";e.push('  <style:style style:name="co'+r+'" style:family="table-column">\n'),e.push('   <style:table-column-properties fo:break-before="auto" style:column-width="'+i+'"/>\n'),e.push("  </style:style>\n"),++r}}));var n=0;t.SheetNames.map((function(e){return t.Sheets[e]})).forEach((function(t){if(t&&t["!rows"])for(var r=0;r<t["!rows"].length;++r)if(t["!rows"][r]){t["!rows"][r].ods=n;var a=t["!rows"][r].hpx+"px";e.push('  <style:style style:name="ro'+n+'" style:family="table-row">\n'),e.push('   <style:table-row-properties fo:break-before="auto" style:row-height="'+a+'"/>\n'),e.push("  </style:style>\n"),++n}})),e.push('  <style:style style:name="ta1" style:family="table" style:master-page-name="mp1">\n'),e.push('   <style:table-properties table:display="true" style:writing-mode="lr-tb"/>\n'),e.push("  </style:style>\n"),e.push('  <style:style style:name="ce1" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N37"/>\n'),e.push(" </office:automatic-styles>\n")};return function(e,t){var r=[wt],i=Ht({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:meta":"urn:oasis:names:tc:opendocument:xmlns:meta:1.0","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:presentation":"urn:oasis:names:tc:opendocument:xmlns:presentation:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:chart":"urn:oasis:names:tc:opendocument:xmlns:chart:1.0","xmlns:dr3d":"urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0","xmlns:math":"http://www.w3.org/1998/Math/MathML","xmlns:form":"urn:oasis:names:tc:opendocument:xmlns:form:1.0","xmlns:script":"urn:oasis:names:tc:opendocument:xmlns:script:1.0","xmlns:ooo":"http://openoffice.org/2004/office","xmlns:ooow":"http://openoffice.org/2004/writer","xmlns:oooc":"http://openoffice.org/2004/calc","xmlns:dom":"http://www.w3.org/2001/xml-events","xmlns:xforms":"http://www.w3.org/2002/xforms","xmlns:xsd":"http://www.w3.org/2001/XMLSchema","xmlns:xsi":"http://www.w3.org/2001/XMLSchema-instance","xmlns:sheet":"urn:oasis:names:tc:opendocument:sh33tjs:1.0","xmlns:rpt":"http://openoffice.org/2005/report","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","xmlns:xhtml":"http://www.w3.org/1999/xhtml","xmlns:grddl":"http://www.w3.org/2003/g/data-view#","xmlns:tableooo":"http://openoffice.org/2009/table","xmlns:drawooo":"http://openoffice.org/2010/draw","xmlns:calcext":"urn:org:documentfoundation:names:experimental:calc:xmlns:calcext:1.0","xmlns:loext":"urn:org:documentfoundation:names:experimental:office:xmlns:loext:1.0","xmlns:field":"urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0","xmlns:formx":"urn:openoffice:names:experimental:ooxml-odf-interop:xmlns:form:1.0","xmlns:css3t":"http://www.w3.org/TR/css3-text/","office:version":"1.2"}),s=Ht({"xmlns:config":"urn:oasis:names:tc:opendocument:xmlns:config:1.0","office:mimetype":"application/vnd.oasis.opendocument.spreadsheet"});"fods"==t.bookType?(r.push("<office:document"+i+s+">\n"),r.push(ua().replace(/office:document-meta/g,"office:meta"))):r.push("<office:document-content"+i+">\n"),a(r,e),r.push("  <office:body>\n"),r.push("    <office:spreadsheet>\n");for(var o=0;o!=e.SheetNames.length;++o)r.push(n(e.Sheets[e.SheetNames[o]],e,o,t));return r.push("    </office:spreadsheet>\n"),r.push("  </office:body>\n"),"fods"==t.bookType?r.push("</office:document>"):r.push("</office:document-content>"),r.join("")}}();function Mh(e,t){if("fods"==t.bookType)return Lh(e,t);var r=bt(),n="",a=[],i=[];return n="mimetype",Tt(r,n,"application/vnd.oasis.opendocument.spreadsheet"),n="content.xml",Tt(r,n,Lh(e,t)),a.push([n,"text/xml"]),i.push([n,"ContentFile"]),n="styles.xml",Tt(r,n,Ph(e,t)),a.push([n,"text/xml"]),i.push([n,"StylesFile"]),n="meta.xml",Tt(r,n,wt+ua()),a.push([n,"text/xml"]),i.push([n,"MetadataFile"]),n="manifest.rdf",Tt(r,n,ha(i)),a.push([n,"application/rdf+xml"]),n="META-INF/manifest.xml",Tt(r,n,fa(a)),r}
/*! sheetjs (C) 2013-present SheetJS -- http://sheetjs.com */function Fh(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function Uh(e){return"undefined"!=typeof TextEncoder?(new TextEncoder).encode(e):_(Ft(e))}function Bh(e,t){e:for(var r=0;r<=e.length-t.length;++r){for(var n=0;n<t.length;++n)if(e[r+n]!=t[n])continue e;return!0}return!1}function Wh(e){var t=e.reduce((function(e,t){return e+t.length}),0),r=new Uint8Array(t),n=0;return e.forEach((function(e){r.set(e,n),n+=e.length})),r}function Hh(e,t,r){var n=Math.floor(0==r?0:Math.LOG10E*Math.log(Math.abs(r)))+6176-20,a=r/Math.pow(10,n-6176);e[t+15]|=n>>7,e[t+14]|=(127&n)<<1;for(var i=0;a>=1;++i,a/=256)e[t+i]=255&a;e[t+15]|=r>=0?0:128}function Gh(e,t){var r=t?t[0]:0,n=127&e[r];e:if(e[r++]>=128){if(n|=(127&e[r])<<7,e[r++]<128)break e;if(n|=(127&e[r])<<14,e[r++]<128)break e;if(n|=(127&e[r])<<21,e[r++]<128)break e;if(n+=(127&e[r])*Math.pow(2,28),++r,e[r++]<128)break e;if(n+=(127&e[r])*Math.pow(2,35),++r,e[r++]<128)break e;if(n+=(127&e[r])*Math.pow(2,42),++r,e[r++]<128)break e}return t&&(t[0]=r),n}function Vh(e){var t=new Uint8Array(7);t[0]=127&e;var r=1;e:if(e>127){if(t[r-1]|=128,t[r]=e>>7&127,++r,e<=16383)break e;if(t[r-1]|=128,t[r]=e>>14&127,++r,e<=2097151)break e;if(t[r-1]|=128,t[r]=e>>21&127,++r,e<=268435455)break e;if(t[r-1]|=128,t[r]=e/256>>>21&127,++r,e<=34359738367)break e;if(t[r-1]|=128,t[r]=e/65536>>>21&127,++r,e<=4398046511103)break e;t[r-1]|=128,t[r]=e/16777216>>>21&127,++r}return t.slice(0,r)}function jh(e){var t=0,r=127&e[t];e:if(e[t++]>=128){if(r|=(127&e[t])<<7,e[t++]<128)break e;if(r|=(127&e[t])<<14,e[t++]<128)break e;if(r|=(127&e[t])<<21,e[t++]<128)break e;r|=(127&e[t])<<28}return r}function zh(e){var t=[],r=[0];while(r[0]<e.length){var n=r[0],a=Gh(e,r),i=7&a;a=Math.floor(a/8);var s,o=0;if(0==a)break;switch(i){case 0:var f=r[0];while(e[r[0]++]>=128);s=e.slice(f,r[0]);break;case 5:o=4,s=e.slice(r[0],r[0]+o),r[0]+=o;break;case 1:o=8,s=e.slice(r[0],r[0]+o),r[0]+=o;break;case 2:o=Gh(e,r),s=e.slice(r[0],r[0]+o),r[0]+=o;break;case 3:case 4:default:throw new Error("PB Type ".concat(i," for Field ").concat(a," at offset ").concat(n))}var l={data:s,type:i};null==t[a]?t[a]=[l]:t[a].push(l)}return t}function $h(e){var t=[];return e.forEach((function(e,r){e.forEach((function(e){e.data&&(t.push(Vh(8*r+e.type)),2==e.type&&t.push(Vh(e.data.length)),t.push(e.data))}))})),Wh(t)}function Yh(e){var t,r=[],n=[0];while(n[0]<e.length){var a=Gh(e,n),i=zh(e.slice(n[0],n[0]+a));n[0]+=a;var s={id:jh(i[1][0].data),messages:[]};i[2].forEach((function(t){var r=zh(t.data),a=jh(r[3][0].data);s.messages.push({meta:r,data:e.slice(n[0],n[0]+a)}),n[0]+=a})),(null==(t=i[3])?void 0:t[0])&&(s.merge=jh(i[3][0].data)>>>0>0),r.push(s)}return r}function Xh(e){var t=[];return e.forEach((function(e){var r=[];r[1]=[{data:Vh(e.id),type:0}],r[2]=[],null!=e.merge&&(r[3]=[{data:Vh(+!!e.merge),type:0}]);var n=[];e.messages.forEach((function(e){n.push(e.data),e.meta[3]=[{type:0,data:Vh(e.data.length)}],r[2].push({data:$h(e.meta),type:2})}));var a=$h(r);t.push(Vh(a.length)),t.push(a),n.forEach((function(e){return t.push(e)}))})),Wh(t)}function Kh(e,t){if(0!=e)throw new Error("Unexpected Snappy chunk type ".concat(e));var r=[0],n=Gh(t,r),a=[];while(r[0]<t.length){var i=3&t[r[0]];if(0!=i){var s=0,o=0;if(1==i?(o=4+(t[r[0]]>>2&7),s=(224&t[r[0]++])<<3,s|=t[r[0]++]):(o=1+(t[r[0]++]>>2),2==i?(s=t[r[0]]|t[r[0]+1]<<8,r[0]+=2):(s=(t[r[0]]|t[r[0]+1]<<8|t[r[0]+2]<<16|t[r[0]+3]<<24)>>>0,r[0]+=4)),a=[Wh(a)],0==s)throw new Error("Invalid offset 0");if(s>a[0].length)throw new Error("Invalid offset beyond length");if(o>=s){a.push(a[0].slice(-s)),o-=s;while(o>=a[a.length-1].length)a.push(a[a.length-1]),o-=a[a.length-1].length}a.push(a[0].slice(-s,-s+o))}else{var f=t[r[0]++]>>2;if(f<60)++f;else{var l=f-59;f=t[r[0]],l>1&&(f|=t[r[0]+1]<<8),l>2&&(f|=t[r[0]+2]<<16),l>3&&(f|=t[r[0]+3]<<24),f>>>=0,f++,r[0]+=l}a.push(t.slice(r[0],r[0]+f)),r[0]+=f}}var c=Wh(a);if(c.length!=n)throw new Error("Unexpected length: ".concat(c.length," != ").concat(n));return c}function Jh(e){var t=[],r=0;while(r<e.length){var n=e[r++],a=e[r]|e[r+1]<<8|e[r+2]<<16;r+=3,t.push(Kh(n,e.slice(r,r+a))),r+=a}if(r!==e.length)throw new Error("data is not a valid framed stream!");return Wh(t)}function Zh(e){var t=[],r=0;while(r<e.length){var n=Math.min(e.length-r,268435455),a=new Uint8Array(4);t.push(a);var i=Vh(n),s=i.length;t.push(i),n<=60?(s++,t.push(new Uint8Array([n-1<<2]))):n<=256?(s+=2,t.push(new Uint8Array([240,n-1&255]))):n<=65536?(s+=3,t.push(new Uint8Array([244,n-1&255,n-1>>8&255]))):n<=16777216?(s+=4,t.push(new Uint8Array([248,n-1&255,n-1>>8&255,n-1>>16&255]))):n<=4294967296&&(s+=5,t.push(new Uint8Array([252,n-1&255,n-1>>8&255,n-1>>16&255,n-1>>>24&255]))),t.push(e.slice(r,r+n)),s+=n,a[0]=0,a[1]=255&s,a[2]=s>>8&255,a[3]=s>>16&255,r+=n}return Wh(t)}function qh(e,t){var r=new Uint8Array(32),n=Fh(r),a=12,i=0;switch(r[0]=5,e.t){case"n":r[1]=2,Hh(r,a,e.v),i|=1,a+=16;break;case"b":r[1]=6,n.setFloat64(a,e.v?1:0,!0),i|=2,a+=8;break;case"s":if(-1==t.indexOf(e.v))throw new Error("Value ".concat(e.v," missing from SST!"));r[1]=3,n.setUint32(a,t.indexOf(e.v),!0),i|=8,a+=4;break;default:throw"unsupported cell type "+e.t}return n.setUint32(8,i,!0),r.slice(0,a)}function Qh(e,t){var r=new Uint8Array(32),n=Fh(r),a=12,i=0;switch(r[0]=3,e.t){case"n":r[2]=2,n.setFloat64(a,e.v,!0),i|=32,a+=8;break;case"b":r[2]=6,n.setFloat64(a,e.v?1:0,!0),i|=32,a+=8;break;case"s":if(-1==t.indexOf(e.v))throw new Error("Value ".concat(e.v," missing from SST!"));r[2]=3,n.setUint32(a,t.indexOf(e.v),!0),i|=16,a+=4;break;default:throw"unsupported cell type "+e.t}return n.setUint32(4,i,!0),r.slice(0,a)}function eu(e){var t=zh(e);return Gh(t[1][0].data)}function tu(e,t,r){var n,a,i,s;if(!(null==(n=e[6])?void 0:n[0])||!(null==(a=e[7])?void 0:a[0]))throw"Mutation only works on post-BNC storages!";var o=(null==(s=null==(i=e[8])?void 0:i[0])?void 0:s.data)&&jh(e[8][0].data)>0||!1;if(o)throw"Math only works with normal offsets";for(var f=0,l=Fh(e[7][0].data),c=0,h=[],u=Fh(e[4][0].data),d=0,p=[],m=0;m<t.length;++m)if(null!=t[m]){var g,v;switch(l.setUint16(2*m,c,!0),u.setUint16(2*m,d,!0),typeof t[m]){case"string":g=qh({t:"s",v:t[m]},r),v=Qh({t:"s",v:t[m]},r);break;case"number":g=qh({t:"n",v:t[m]},r),v=Qh({t:"n",v:t[m]},r);break;case"boolean":g=qh({t:"b",v:t[m]},r),v=Qh({t:"b",v:t[m]},r);break;default:throw new Error("Unsupported value "+t[m])}h.push(g),c+=g.length,p.push(v),d+=v.length,++f}else l.setUint16(2*m,65535,!0),u.setUint16(2*m,65535);for(e[2][0].data=Vh(f);m<e[7][0].data.length/2;++m)l.setUint16(2*m,65535,!0),u.setUint16(2*m,65535,!0);return e[6][0].data=Wh(h),e[3][0].data=Wh(p),f}function ru(e,t){if(!t||!t.numbers)throw new Error("Must pass a `numbers` option -- check the README");var r=e.Sheets[e.SheetNames[0]];e.SheetNames.length>1&&console.error("The Numbers writer currently writes only the first table");var n=Jr(r["!ref"]);n.s.r=n.s.c=0;var a=!1;n.e.c>9&&(a=!0,n.e.c=9),n.e.r>49&&(a=!0,n.e.r=49),a&&console.error("The Numbers writer is currently limited to ".concat(Zr(n)));var i=Tu(r,{range:n,header:1}),s=["~Sh33tJ5~"];i.forEach((function(e){return e.forEach((function(e){"string"==typeof e&&s.push(e)}))}));var o={},f=[],l=Ye.read(t.numbers,{type:"base64"});l.FileIndex.map((function(e,t){return[e,l.FullPaths[t]]})).forEach((function(e){var t=e[0],r=e[1];if(2==t.type&&t.name.match(/\.iwa/)){var n=t.content,a=Jh(n),i=Yh(a);i.forEach((function(e){f.push(e.id),o[e.id]={deps:[],location:r,type:jh(e.messages[0].meta[1][0].data)}}))}})),f.sort((function(e,t){return e-t}));var c=f.filter((function(e){return e>1})).map((function(e){return[e,Vh(e)]}));l.FileIndex.map((function(e,t){return[e,l.FullPaths[t]]})).forEach((function(e){var t=e[0];e[1];if(t.name.match(/\.iwa/)){var r=Yh(Jh(t.content));r.forEach((function(e){e.messages.forEach((function(t){c.forEach((function(t){e.messages.some((function(e){return 11006!=jh(e.meta[1][0].data)&&Bh(e.data,t[1])}))&&o[t[0]].deps.push(e.id)}))}))}))}}));for(var h,u=Ye.find(l,o[1].location),d=Yh(Jh(u.content)),p=0;p<d.length;++p){var m=d[p];1==m.id&&(h=m)}var g=eu(zh(h.messages[0].data)[1][0].data);for(u=Ye.find(l,o[g].location),d=Yh(Jh(u.content)),p=0;p<d.length;++p)m=d[p],m.id==g&&(h=m);for(g=eu(zh(h.messages[0].data)[2][0].data),u=Ye.find(l,o[g].location),d=Yh(Jh(u.content)),p=0;p<d.length;++p)m=d[p],m.id==g&&(h=m);for(g=eu(zh(h.messages[0].data)[2][0].data),u=Ye.find(l,o[g].location),d=Yh(Jh(u.content)),p=0;p<d.length;++p)m=d[p],m.id==g&&(h=m);var v=zh(h.messages[0].data);v[6][0].data=Vh(n.e.r+1),v[7][0].data=Vh(n.e.c+1);for(var T=eu(v[46][0].data),b=Ye.find(l,o[T].location),w=Yh(Jh(b.content)),E=0;E<w.length;++E)if(w[E].id==T)break;if(w[E].id!=T)throw"Bad ColumnRowUIDMapArchive";var A=zh(w[E].messages[0].data);A[1]=[],A[2]=[],A[3]=[];for(var y=0;y<=n.e.c;++y){var S=[];S[1]=S[2]=[{type:0,data:Vh(y+420690)}],A[1].push({type:2,data:$h(S)}),A[2].push({type:0,data:Vh(y)}),A[3].push({type:0,data:Vh(y)})}A[4]=[],A[5]=[],A[6]=[];for(var _=0;_<=n.e.r;++_)S=[],S[1]=S[2]=[{type:0,data:Vh(_+726270)}],A[4].push({type:2,data:$h(S)}),A[5].push({type:0,data:Vh(_)}),A[6].push({type:0,data:Vh(_)});w[E].messages[0].data=$h(A),b.content=Zh(Xh(w)),b.size=b.content.length,delete v[46];var x=zh(v[4][0].data);x[7][0].data=Vh(n.e.r+1);var O=zh(x[1][0].data),C=eu(O[2][0].data);if(b=Ye.find(l,o[C].location),w=Yh(Jh(b.content)),w[0].id!=C)throw"Bad HeaderStorageBucket";var R=zh(w[0].messages[0].data);for(_=0;_<i.length;++_){var k=zh(R[2][0].data);k[1][0].data=Vh(_),k[4][0].data=Vh(i[_].length),R[2][_]={type:R[2][0].type,data:$h(k)}}w[0].messages[0].data=$h(R),b.content=Zh(Xh(w)),b.size=b.content.length;var I=eu(x[2][0].data);if(b=Ye.find(l,o[I].location),w=Yh(Jh(b.content)),w[0].id!=I)throw"Bad HeaderStorageBucket";for(R=zh(w[0].messages[0].data),y=0;y<=n.e.c;++y)k=zh(R[2][0].data),k[1][0].data=Vh(y),k[4][0].data=Vh(n.e.r+1),R[2][y]={type:R[2][0].type,data:$h(k)};w[0].messages[0].data=$h(R),b.content=Zh(Xh(w)),b.size=b.content.length;var N=eu(x[4][0].data);(function(){for(var e,t=Ye.find(l,o[N].location),r=Yh(Jh(t.content)),n=0;n<r.length;++n){var a=r[n];a.id==N&&(e=a)}var i=zh(e.messages[0].data);i[3]=[];var f=[];s.forEach((function(e,t){f[1]=[{type:0,data:Vh(t)}],f[2]=[{type:0,data:Vh(1)}],f[3]=[{type:2,data:Uh(e)}],i[3].push({type:2,data:$h(f)})})),e.messages[0].data=$h(i);var c=Xh(r),h=Zh(c);t.content=h,t.size=t.content.length})();var D=zh(x[3][0].data),P=D[1][0];delete D[2];var L=zh(P.data),M=eu(L[2][0].data);(function(){for(var e,t=Ye.find(l,o[M].location),r=Yh(Jh(t.content)),a=0;a<r.length;++a){var f=r[a];f.id==M&&(e=f)}var c=zh(e.messages[0].data);delete c[6],delete D[7];var h=new Uint8Array(c[5][0].data);c[5]=[];for(var u=0,d=0;d<=n.e.r;++d){var p=zh(h);u+=tu(p,i[d],s),p[1][0].data=Vh(d),c[5].push({data:$h(p),type:2})}c[1]=[{type:0,data:Vh(n.e.c+1)}],c[2]=[{type:0,data:Vh(n.e.r+1)}],c[3]=[{type:0,data:Vh(u)}],c[4]=[{type:0,data:Vh(n.e.r+1)}],e.messages[0].data=$h(c);var m=Xh(r),g=Zh(m);t.content=g,t.size=t.content.length})(),P.data=$h(L),x[3][0].data=$h(D),v[4][0].data=$h(x),h.messages[0].data=$h(v);var F=Xh(d),U=Zh(F);return u.content=U,u.size=u.content.length,l}function nu(e){return function(t){for(var r=0;r!=e.length;++r){var n=e[r];void 0===t[n[0]]&&(t[n[0]]=n[1]),"n"===n[2]&&(t[n[0]]=Number(t[n[0]]))}}}function au(e){nu([["cellDates",!1],["bookSST",!1],["bookType","xlsx"],["compression",!1],["WTF",!1]])(e)}function iu(e,t){return"ods"==t.bookType?Mh(e,t):"numbers"==t.bookType?ru(e,t):"xlsb"==t.bookType?su(e,t):ou(e,t)}function su(e,t){Is=1024,e&&!e.SSF&&(e.SSF=dt(z)),e&&e.SSF&&(He(),We(e.SSF),t.revssf=et(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,Ff?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r="xlsb"==t.bookType?"bin":"xml",n=Gs.indexOf(t.bookType)>-1,a=ra();au(t=t||{});var i=bt(),s="",o=0;if(t.cellXfs=[],Hf(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),s="docProps/core.xml",Tt(i,s,ma(e.Props,t)),a.coreprops.push(s),oa(t.rels,2,s,aa.CORE_PROPS),s="docProps/app.xml",e.Props&&e.Props.SheetNames);else if(e.Workbook&&e.Workbook.Sheets){for(var f=[],l=0;l<e.SheetNames.length;++l)2!=(e.Workbook.Sheets[l]||{}).Hidden&&f.push(e.SheetNames[l]);e.Props.SheetNames=f}else e.Props.SheetNames=e.SheetNames;for(e.Props.Worksheets=e.Props.SheetNames.length,Tt(i,s,Ta(e.Props,t)),a.extprops.push(s),oa(t.rels,3,s,aa.EXT_PROPS),e.Custprops!==e.Props&&Ze(e.Custprops||{}).length>0&&(s="docProps/custom.xml",Tt(i,s,ba(e.Custprops,t)),a.custprops.push(s),oa(t.rels,4,s,aa.CUST_PROPS)),o=1;o<=e.SheetNames.length;++o){var c={"!id":{}},h=e.Sheets[e.SheetNames[o-1]],u=(h||{})["!type"]||"sheet";switch(u){case"chart":default:s="xl/worksheets/sheet"+o+"."+r,Tt(i,s,Fc(o-1,s,t,e,c)),a.sheets.push(s),oa(t.wbrels,-1,"worksheets/sheet"+o+"."+r,aa.WS[0])}if(h){var d=h["!comments"],p=!1,m="";d&&d.length>0&&(m="xl/comments"+o+"."+r,Tt(i,m,Wc(d,m,t)),a.comments.push(m),oa(c,-1,"../comments"+o+"."+r,aa.CMNT),p=!0),h["!legacy"]&&p&&Tt(i,"xl/drawings/vmlDrawing"+o+".vml",Ns(o,h["!comments"])),delete h["!comments"],delete h["!legacy"]}c["!id"].rId1&&Tt(i,ia(s),sa(c))}return null!=t.Strings&&t.Strings.length>0&&(s="xl/sharedStrings."+r,Tt(i,s,Bc(t.Strings,s,t)),a.strs.push(s),oa(t.wbrels,-1,"sharedStrings."+r,aa.SST)),s="xl/workbook."+r,Tt(i,s,Mc(e,s,t)),a.workbooks.push(s),oa(t.rels,1,s,aa.WB),s="xl/theme/theme1.xml",Tt(i,s,ws(e.Themes,t)),a.themes.push(s),oa(t.wbrels,-1,"theme/theme1.xml",aa.THEME),s="xl/styles."+r,Tt(i,s,Uc(e,s,t)),a.styles.push(s),oa(t.wbrels,-1,"styles."+r,aa.STY),e.vbaraw&&n&&(s="xl/vbaProject.bin",Tt(i,s,e.vbaraw),a.vba.push(s),oa(t.wbrels,-1,"vbaProject.bin",aa.VBA)),s="xl/metadata."+r,Tt(i,s,Hc(s)),a.metadata.push(s),oa(t.wbrels,-1,"metadata."+r,aa.XLMETA),Tt(i,"[Content_Types].xml",na(a,t)),Tt(i,"_rels/.rels",sa(t.rels)),Tt(i,"xl/_rels/workbook."+r+".rels",sa(t.wbrels)),delete t.revssf,delete t.ssf,i}function ou(e,t){Is=1024,e&&!e.SSF&&(e.SSF=dt(z)),e&&e.SSF&&(He(),We(e.SSF),t.revssf=et(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,Ff?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r="xml",n=Gs.indexOf(t.bookType)>-1,a=ra();au(t=t||{});var i=bt(),s="",o=0;if(t.cellXfs=[],Hf(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),s="docProps/core.xml",Tt(i,s,ma(e.Props,t)),a.coreprops.push(s),oa(t.rels,2,s,aa.CORE_PROPS),s="docProps/app.xml",e.Props&&e.Props.SheetNames);else if(e.Workbook&&e.Workbook.Sheets){for(var f=[],l=0;l<e.SheetNames.length;++l)2!=(e.Workbook.Sheets[l]||{}).Hidden&&f.push(e.SheetNames[l]);e.Props.SheetNames=f}else e.Props.SheetNames=e.SheetNames;e.Props.Worksheets=e.Props.SheetNames.length,Tt(i,s,Ta(e.Props,t)),a.extprops.push(s),oa(t.rels,3,s,aa.EXT_PROPS),e.Custprops!==e.Props&&Ze(e.Custprops||{}).length>0&&(s="docProps/custom.xml",Tt(i,s,ba(e.Custprops,t)),a.custprops.push(s),oa(t.rels,4,s,aa.CUST_PROPS));var c=["SheetJ5"];for(t.tcid=0,o=1;o<=e.SheetNames.length;++o){var h={"!id":{}},u=e.Sheets[e.SheetNames[o-1]],d=(u||{})["!type"]||"sheet";switch(d){case"chart":default:s="xl/worksheets/sheet"+o+"."+r,Tt(i,s,el(o-1,t,e,h)),a.sheets.push(s),oa(t.wbrels,-1,"worksheets/sheet"+o+"."+r,aa.WS[0])}if(u){var p=u["!comments"],m=!1,g="";if(p&&p.length>0){var v=!1;p.forEach((function(e){e[1].forEach((function(e){1==e.T&&(v=!0)}))})),v&&(g="xl/threadedComments/threadedComment"+o+"."+r,Tt(i,g,Ps(p,c,t)),a.threadedcomments.push(g),oa(h,-1,"../threadedComments/threadedComment"+o+"."+r,aa.TCMNT)),g="xl/comments"+o+"."+r,Tt(i,g,Ds(p,t)),a.comments.push(g),oa(h,-1,"../comments"+o+"."+r,aa.CMNT),m=!0}u["!legacy"]&&m&&Tt(i,"xl/drawings/vmlDrawing"+o+".vml",Ns(o,u["!comments"])),delete u["!comments"],delete u["!legacy"]}h["!id"].rId1&&Tt(i,ia(s),sa(h))}return null!=t.Strings&&t.Strings.length>0&&(s="xl/sharedStrings."+r,Tt(i,s,xi(t.Strings,t)),a.strs.push(s),oa(t.wbrels,-1,"sharedStrings."+r,aa.SST)),s="xl/workbook."+r,Tt(i,s,_c(e,t)),a.workbooks.push(s),oa(t.rels,1,s,aa.WB),s="xl/theme/theme1.xml",Tt(i,s,ws(e.Themes,t)),a.themes.push(s),oa(t.wbrels,-1,"theme/theme1.xml",aa.THEME),s="xl/styles."+r,Tt(i,s,Yi(e,t)),a.styles.push(s),oa(t.wbrels,-1,"styles."+r,aa.STY),e.vbaraw&&n&&(s="xl/vbaProject.bin",Tt(i,s,e.vbaraw),a.vba.push(s),oa(t.wbrels,-1,"vbaProject.bin",aa.VBA)),s="xl/metadata."+r,Tt(i,s,Rs()),a.metadata.push(s),oa(t.wbrels,-1,"metadata."+r,aa.XLMETA),c.length>1&&(s="xl/persons/person.xml",Tt(i,s,Ls(c,t)),a.people.push(s),oa(t.wbrels,-1,"persons/person.xml",aa.PEOPLE)),Tt(i,"[Content_Types].xml",na(a,t)),Tt(i,"_rels/.rels",sa(t.rels)),Tt(i,"xl/_rels/workbook."+r+".rels",sa(t.wbrels)),delete t.revssf,delete t.ssf,i}function fu(e,t){var r="";switch((t||{}).type||"base64"){case"buffer":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":r=w(e.slice(0,12));break;case"binary":r=e;break;case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];default:throw new Error("Unrecognized type "+(t&&t.type||"undefined"))}return[r.charCodeAt(0),r.charCodeAt(1),r.charCodeAt(2),r.charCodeAt(3),r.charCodeAt(4),r.charCodeAt(5),r.charCodeAt(6),r.charCodeAt(7)]}function lu(e,t){switch(t.type){case"base64":case"binary":break;case"buffer":case"array":t.type="";break;case"file":return Je(t.file,Ye.write(e,{type:E?"buffer":""}));case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");default:throw new Error("Unrecognized type "+t.type)}return Ye.write(e,t)}function cu(e,t){var r=dt(t||{}),n=iu(e,r);return hu(n,r)}function hu(e,t){var r={},n=E?"nodebuffer":"undefined"!==typeof Uint8Array?"array":"string";if(t.compression&&(r.compression="DEFLATE"),t.password)r.type=n;else switch(t.type){case"base64":r.type="base64";break;case"binary":r.type="string";break;case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");case"buffer":case"file":r.type=n;break;default:throw new Error("Unrecognized type "+t.type)}var a=e.FullPaths?Ye.write(e,{fileType:"zip",type:{nodebuffer:"buffer",string:"binary"}[r.type]||r.type,compression:!!t.compression}):e.generate(r);if("undefined"!==typeof Deno&&"string"==typeof a){if("binary"==t.type||"base64"==t.type)return a;a=new Uint8Array(x(a))}return t.password&&"undefined"!==typeof encrypt_agile?lu(encrypt_agile(a,t.password),t):"file"===t.type?Je(t.file,a):"string"==t.type?Mt(a):a}function uu(e,t){var r=t||{},n=nh(e,r);return lu(n,r)}function du(e,t,r){r||(r="");var n=r+e;switch(t.type){case"base64":return b(Ft(n));case"binary":return Ft(n);case"string":return e;case"file":return Je(t.file,n,"utf8");case"buffer":return E?A(n,"utf8"):"undefined"!==typeof TextEncoder?(new TextEncoder).encode(n):du(n,{type:"binary"}).split("").map((function(e){return e.charCodeAt(0)}))}throw new Error("Unrecognized type "+t.type)}function pu(e,t){switch(t.type){case"base64":return b(e);case"binary":return e;case"string":return e;case"file":return Je(t.file,e,"binary");case"buffer":return E?A(e,"binary"):e.split("").map((function(e){return e.charCodeAt(0)}))}throw new Error("Unrecognized type "+t.type)}function mu(e,t){switch(t.type){case"string":case"base64":case"binary":for(var r="",n=0;n<e.length;++n)r+=String.fromCharCode(e[n]);return"base64"==t.type?b(r):"string"==t.type?Mt(r):r;case"file":return Je(t.file,e);case"buffer":return e;default:throw new Error("Unrecognized type "+t.type)}}function gu(e,t){h(),Sc(e);var r=dt(t||{});if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),"array"==r.type){r.type="binary";var n=gu(e,r);return r.type="array",x(n)}var a=0;if(r.sheet&&(a="number"==typeof r.sheet?r.sheet:e.SheetNames.indexOf(r.sheet),!e.SheetNames[a]))throw new Error("Sheet not found: "+r.sheet+" : "+typeof r.sheet);switch(r.bookType||"xlsb"){case"xml":case"xlml":return du(eh(e,r),r);case"slk":case"sylk":return du(wi.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"htm":case"html":return du(Ch(e.Sheets[e.SheetNames[a]],r),r);case"txt":return pu(Au(e.Sheets[e.SheetNames[a]],r),r);case"csv":return du(Eu(e.Sheets[e.SheetNames[a]],r),r,"\ufeff");case"dif":return du(Ei.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"dbf":return mu(bi.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"prn":return du(yi.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"rtf":return du(Di.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"eth":return du(Ai.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"fods":return du(Mh(e,r),r);case"wk1":return mu(Si.sheet_to_wk1(e.Sheets[e.SheetNames[a]],r),r);case"wk3":return mu(Si.book_to_wk3(e,r),r);case"biff2":r.biff||(r.biff=2);case"biff3":r.biff||(r.biff=3);case"biff4":return r.biff||(r.biff=4),mu(yh(e,r),r);case"biff5":r.biff||(r.biff=5);case"biff8":case"xla":case"xls":return r.biff||(r.biff=8),uu(e,r);case"xlsx":case"xlsm":case"xlam":case"xlsb":case"numbers":case"ods":return cu(e,r);default:throw new Error("Unrecognized bookType |"+r.bookType+"|")}}function vu(e,t,r,n,a,i,s,o){var f=Wr(r),l=o.defval,c=o.raw||!Object.prototype.hasOwnProperty.call(o,"raw"),h=!0,u=1===a?[]:{};if(1!==a)if(Object.defineProperty)try{Object.defineProperty(u,"__rowNum__",{value:r,enumerable:!1})}catch(g){u.__rowNum__=r}else u.__rowNum__=r;if(!s||e[r])for(var d=t.s.c;d<=t.e.c;++d){var p=s?e[r][d]:e[n[d]+f];if(void 0!==p&&void 0!==p.t){var m=p.v;switch(p.t){case"z":if(null==m)break;continue;case"e":m=0==m?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw new Error("unrecognized type "+p.t)}if(null!=i[d]){if(null==m)if("e"==p.t&&null===m)u[i[d]]=null;else if(void 0!==l)u[i[d]]=l;else{if(!c||null!==m)continue;u[i[d]]=null}else u[i[d]]=c&&("n"!==p.t||"n"===p.t&&!1!==o.rawNumbers)?m:en(p,m,o);null!=m&&(h=!1)}}else{if(void 0===l)continue;null!=i[d]&&(u[i[d]]=l)}}return{row:u,isempty:h}}function Tu(e,t){if(null==e||null==e["!ref"])return[];var r={t:"n",v:0},n=0,a=1,i=[],s=0,o="",f={s:{r:0,c:0},e:{r:0,c:0}},l=t||{},c=null!=l.range?l.range:e["!ref"];switch(1===l.header?n=1:"A"===l.header?n=2:Array.isArray(l.header)?n=3:null==l.header&&(n=0),typeof c){case"string":f=qr(c);break;case"number":f=qr(e["!ref"]),f.s.r=c;break;default:f=c}n>0&&(a=0);var h=Wr(f.s.r),u=[],d=[],p=0,m=0,g=Array.isArray(e),v=f.s.r,T=0,b={};g&&!e[v]&&(e[v]=[]);var w=l.skipHidden&&e["!cols"]||[],E=l.skipHidden&&e["!rows"]||[];for(T=f.s.c;T<=f.e.c;++T)if(!(w[T]||{}).hidden)switch(u[T]=jr(T),r=g?e[v][T]:e[u[T]+h],n){case 1:i[T]=T-f.s.c;break;case 2:i[T]=u[T];break;case 3:i[T]=l.header[T-f.s.c];break;default:if(null==r&&(r={w:"__EMPTY",t:"s"}),o=s=en(r,null,l),m=b[s]||0,m){do{o=s+"_"+m++}while(b[o]);b[s]=m,b[o]=1}else b[s]=1;i[T]=o}for(v=f.s.r+a;v<=f.e.r;++v)if(!(E[v]||{}).hidden){var A=vu(e,f,v,u,n,i,g,l);(!1===A.isempty||(1===n?!1!==l.blankrows:l.blankrows))&&(d[p++]=A.row)}return d.length=p,d}var bu=/"/g;function wu(e,t,r,n,a,i,s,o){for(var f=!0,l=[],c="",h=Wr(r),u=t.s.c;u<=t.e.c;++u)if(n[u]){var d=o.dense?(e[r]||[])[u]:e[n[u]+h];if(null==d)c="";else if(null!=d.v){f=!1,c=""+(o.rawNumbers&&"n"==d.t?d.v:en(d,null,o));for(var p=0,m=0;p!==c.length;++p)if((m=c.charCodeAt(p))===a||m===i||34===m||o.forceQuotes){c='"'+c.replace(bu,'""')+'"';break}"ID"==c&&(c='"ID"')}else null==d.f||d.F?c="":(f=!1,c="="+d.f,c.indexOf(",")>=0&&(c='"'+c.replace(bu,'""')+'"'));l.push(c)}return!1===o.blankrows&&f?null:l.join(s)}function Eu(e,t){var r=[],n=null==t?{}:t;if(null==e||null==e["!ref"])return"";var a=qr(e["!ref"]),i=void 0!==n.FS?n.FS:",",s=i.charCodeAt(0),o=void 0!==n.RS?n.RS:"\n",f=o.charCodeAt(0),l=new RegExp(("|"==i?"\\|":i)+"+$"),c="",h=[];n.dense=Array.isArray(e);for(var u=n.skipHidden&&e["!cols"]||[],d=n.skipHidden&&e["!rows"]||[],p=a.s.c;p<=a.e.c;++p)(u[p]||{}).hidden||(h[p]=jr(p));for(var m=0,g=a.s.r;g<=a.e.r;++g)(d[g]||{}).hidden||(c=wu(e,a,g,h,s,f,i,n),null!=c&&(n.strip&&(c=c.replace(l,"")),(c||!1!==n.blankrows)&&r.push((m++?o:"")+c)));return delete n.dense,r.join("")}function Au(e,t){t||(t={}),t.FS="\t",t.RS="\n";var r=Eu(e,t);if("undefined"==typeof d||"string"==t.type)return r;var n=d.utils.encode(1200,r,"str");return String.fromCharCode(255)+String.fromCharCode(254)+n}function yu(e){var t,r="",n="";if(null==e||null==e["!ref"])return[];var a,i=qr(e["!ref"]),s="",o=[],f=[],l=Array.isArray(e);for(a=i.s.c;a<=i.e.c;++a)o[a]=jr(a);for(var c=i.s.r;c<=i.e.r;++c)for(s=Wr(c),a=i.s.c;a<=i.e.c;++a)if(r=o[a]+s,t=l?(e[c]||[])[a]:e[r],n="",void 0!==t){if(null!=t.F){if(r=t.F,!t.f)continue;n=t.f,-1==r.indexOf(":")&&(r=r+":"+r)}if(null!=t.f)n=t.f;else{if("z"==t.t)continue;if("n"==t.t&&null!=t.v)n=""+t.v;else if("b"==t.t)n=t.v?"TRUE":"FALSE";else if(void 0!==t.w)n="'"+t.w;else{if(void 0===t.v)continue;n="s"==t.t?"'"+t.v:""+t.v}}f[f.length]=r+"="+n}return f}function Su(e,t,r){var n,a=r||{},i=+!a.skipHeader,s=e||{},o=0,f=0;if(s&&null!=a.origin)if("number"==typeof a.origin)o=a.origin;else{var l="string"==typeof a.origin?Xr(a.origin):a.origin;o=l.r,f=l.c}var c={s:{c:0,r:0},e:{c:f,r:o+t.length-1+i}};if(s["!ref"]){var h=qr(s["!ref"]);c.e.c=Math.max(c.e.c,h.e.c),c.e.r=Math.max(c.e.r,h.e.r),-1==o&&(o=h.e.r+1,c.e.r=o+t.length-1+i)}else-1==o&&(o=0,c.e.r=t.length-1+i);var u=a.header||[],d=0;t.forEach((function(e,t){Ze(e).forEach((function(r){-1==(d=u.indexOf(r))&&(u[d=u.length]=r);var l=e[r],c="z",h="",p=Kr({c:f+d,r:o+t+i});n=xu(s,p),!l||"object"!==typeof l||l instanceof Date?("number"==typeof l?c="n":"boolean"==typeof l?c="b":"string"==typeof l?c="s":l instanceof Date?(c="d",a.cellDates||(c="n",l=nt(l)),h=a.dateNF||z[14]):null===l&&a.nullError&&(c="e",l=0),n?(n.t=c,n.v=l,delete n.w,delete n.R,h&&(n.z=h)):s[p]=n={t:c,v:l},h&&(n.z=h)):s[p]=l}))})),c.e.c=Math.max(c.e.c,f+u.length-1);var p=Wr(o);if(i)for(d=0;d<u.length;++d)s[jr(d+f)+p]={t:"s",v:u[d]};return s["!ref"]=Zr(c),s}function _u(e,t){return Su(null,e,t)}function xu(e,t,r){if("string"==typeof t){if(Array.isArray(e)){var n=Xr(t);return e[n.r]||(e[n.r]=[]),e[n.r][n.c]||(e[n.r][n.c]={t:"z"})}return e[t]||(e[t]={t:"z"})}return xu(e,Kr("number"!=typeof t?t:{r:t,c:r||0}))}function Ou(e,t){if("number"==typeof t){if(t>=0&&e.SheetNames.length>t)return t;throw new Error("Cannot find sheet # "+t)}if("string"==typeof t){var r=e.SheetNames.indexOf(t);if(r>-1)return r;throw new Error("Cannot find sheet name |"+t+"|")}throw new Error("Cannot find sheet |"+t+"|")}function Cu(){return{SheetNames:[],Sheets:{}}}function Ru(e,t,r,n){var a=1;if(!r)for(;a<=65535;++a,r=void 0)if(-1==e.SheetNames.indexOf(r="Sheet"+a))break;if(!r||e.SheetNames.length>=65535)throw new Error("Too many worksheets");if(n&&e.SheetNames.indexOf(r)>=0){var i=r.match(/(^.*?)(\d+)$/);a=i&&+i[2]||0;var s=i&&i[1]||r;for(++a;a<=65535;++a)if(-1==e.SheetNames.indexOf(r=s+a))break}if(Ac(r),e.SheetNames.indexOf(r)>=0)throw new Error("Worksheet with name |"+r+"| already exists!");return e.SheetNames.push(r),e.Sheets[r]=t,r}function ku(e,t,r){e.Workbook||(e.Workbook={}),e.Workbook.Sheets||(e.Workbook.Sheets=[]);var n=Ou(e,t);switch(e.Workbook.Sheets[n]||(e.Workbook.Sheets[n]={}),r){case 0:case 1:case 2:break;default:throw new Error("Bad sheet visibility setting "+r)}e.Workbook.Sheets[n].Hidden=r}function Iu(e,t){return e.z=t,e}function Nu(e,t,r){return t?(e.l={Target:t},r&&(e.l.Tooltip=r)):delete e.l,e}function Du(e,t,r){return Nu(e,"#"+t,r)}function Pu(e,t,r){e.c||(e.c=[]),e.c.push({t:t,a:r||"SheetJS"})}function Lu(e,t,r,n){for(var a="string"!=typeof t?t:qr(t),i="string"==typeof t?t:Zr(t),s=a.s.r;s<=a.e.r;++s)for(var o=a.s.c;o<=a.e.c;++o){var f=xu(e,s,o);f.t="n",f.F=i,delete f.v,s==a.s.r&&o==a.s.c&&(f.f=r,n&&(f.D=!0))}return e}var Mu={encode_col:jr,encode_row:Wr,encode_cell:Kr,encode_range:Zr,decode_col:Vr,decode_row:Br,split_cell:Yr,decode_cell:Xr,decode_range:Jr,format_cell:en,sheet_add_aoa:rn,sheet_add_json:Su,sheet_add_dom:Rh,aoa_to_sheet:nn,json_to_sheet:_u,table_to_sheet:kh,table_to_book:Ih,sheet_to_csv:Eu,sheet_to_txt:Au,sheet_to_json:Tu,sheet_to_html:Ch,sheet_to_formulae:yu,sheet_to_row_object_array:Tu,sheet_get_cell:xu,book_new:Cu,book_append_sheet:Ru,book_set_sheet_visibility:ku,cell_set_number_format:Iu,cell_set_hyperlink:Nu,cell_set_internal_link:Du,cell_add_comment:Pu,sheet_set_array_formula:Lu,consts:{SHEET_VISIBLE:0,SHEET_HIDDEN:1,SHEET_VERY_HIDDEN:2}};n.version},2834:function(e,t,r){"use strict";var n=r("ebb5"),a=r("e330"),i=r("59ed"),s=r("dfb9"),o=n.aTypedArray,f=n.getTypedArrayConstructor,l=n.exportTypedArrayMethod,c=a(n.TypedArrayPrototype.sort);l("toSorted",(function(e){void 0!==e&&i(e);var t=o(this),r=s(f(t),t);return c(r,e)}))},2954:function(e,t,r){"use strict";var n=r("ebb5"),a=r("b6b7"),i=r("d039"),s=r("f36a"),o=n.aTypedArray,f=n.exportTypedArrayMethod,l=i((function(){new Int8Array(1).slice()}));f("slice",(function(e,t){var r=s(o(this),e,t),n=a(this),i=0,f=r.length,l=new n(f);while(f>i)l[i]=r[i++];return l}),l)},"2c66":function(e,t,r){"use strict";var n=r("83ab"),a=r("edd0"),i=r("75bd"),s=ArrayBuffer.prototype;n&&!("detached"in s)&&a(s,"detached",{configurable:!0,get:function(){return i(this)}})},3280:function(e,t,r){"use strict";var n=r("ebb5"),a=r("2ba4"),i=r("e58c"),s=n.aTypedArray,o=n.exportTypedArrayMethod;o("lastIndexOf",(function(e){var t=arguments.length;return a(i,s(this),t>1?[e,arguments[1]]:[e])}))},"36f2":function(e,t,r){"use strict";var n,a,i,s,o=r("da84"),f=r("7c37"),l=r("dbe5"),c=o.structuredClone,h=o.ArrayBuffer,u=o.MessageChannel,d=!1;if(l)d=function(e){c(e,{transfer:[e]})};else if(h)try{u||(n=f("worker_threads"),n&&(u=n.MessageChannel)),u&&(a=new u,i=new h(2),s=function(e){a.port1.postMessage(null,[e])},2===i.byteLength&&(s(i),0===i.byteLength&&(d=s)))}catch(p){}e.exports=d},"3a7b":function(e,t,r){"use strict";var n=r("ebb5"),a=r("b727").findIndex,i=n.aTypedArray,s=n.exportTypedArrayMethod;s("findIndex",(function(e){return a(i(this),e,arguments.length>1?arguments[1]:void 0)}))},"3c5d":function(e,t,r){"use strict";var n=r("da84"),a=r("c65b"),i=r("ebb5"),s=r("07fa"),o=r("182d"),f=r("7b0b"),l=r("d039"),c=n.RangeError,h=n.Int8Array,u=h&&h.prototype,d=u&&u.set,p=i.aTypedArray,m=i.exportTypedArrayMethod,g=!l((function(){var e=new Uint8ClampedArray(2);return a(d,e,{length:1,0:3},1),3!==e[1]})),v=g&&i.NATIVE_ARRAY_BUFFER_VIEWS&&l((function(){var e=new h(2);return e.set(1),e.set("2",1),0!==e[0]||2!==e[1]}));m("set",(function(e){p(this);var t=o(arguments.length>1?arguments[1]:void 0,1),r=f(e);if(g)return a(d,this,r,t);var n=this.length,i=s(r),l=0;if(i+t>n)throw new c("Wrong length");while(l<i)this[t+l]=r[l++]}),!g||v)},"3fcc":function(e,t,r){"use strict";var n=r("ebb5"),a=r("b727").map,i=r("b6b7"),s=n.aTypedArray,o=n.exportTypedArrayMethod;o("map",(function(e){return a(s(this),e,arguments.length>1?arguments[1]:void 0,(function(e,t){return new(i(e))(t)}))}))},"40e9":function(e,t,r){"use strict";var n=r("23e7"),a=r("41f6");a&&n({target:"ArrayBuffer",proto:!0},{transferToFixedLength:function(){return a(this,arguments.length?arguments[0]:void 0,!1)}})},"41f6":function(e,t,r){"use strict";var n=r("da84"),a=r("e330"),i=r("7282"),s=r("0b25"),o=r("75bd"),f=r("b620"),l=r("36f2"),c=r("dbe5"),h=n.structuredClone,u=n.ArrayBuffer,d=n.DataView,p=n.TypeError,m=Math.min,g=u.prototype,v=d.prototype,T=a(g.slice),b=i(g,"resizable","get"),w=i(g,"maxByteLength","get"),E=a(v.getInt8),A=a(v.setInt8);e.exports=(c||l)&&function(e,t,r){var n,a=f(e),i=void 0===t?a:s(t),g=!b||!b(e);if(o(e))throw new p("ArrayBuffer is detached");if(c&&(e=h(e,{transfer:[e]}),a===i&&(r||g)))return e;if(a>=i&&(!r||g))n=T(e,0,i);else{var v=r&&!g&&w?{maxByteLength:w(e)}:void 0;n=new u(i,v);for(var y=new d(e),S=new d(n),_=m(i,a),x=0;x<_;x++)A(S,x,E(y,x))}return c||l(e),n}},"4b11":function(e,t,r){"use strict";e.exports="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView},"4ea1":function(e,t,r){"use strict";var n=r("d429"),a=r("ebb5"),i=r("bcbf"),s=r("5926"),o=r("f495"),f=a.aTypedArray,l=a.getTypedArrayConstructor,c=a.exportTypedArrayMethod,h=!!function(){try{new Int8Array(1)["with"](2,{valueOf:function(){throw 8}})}catch(e){return 8===e}}();c("with",{with:function(e,t){var r=f(this),a=s(e),c=i(r)?o(t):+t;return n(r,l(r),a,c)}}["with"],!h)},"5cc6":function(e,t,r){"use strict";var n=r("74e8");n("Uint8",(function(e){return function(t,r,n){return e(this,t,r,n)}}))},"5f96":function(e,t,r){"use strict";var n=r("ebb5"),a=r("e330"),i=n.aTypedArray,s=n.exportTypedArrayMethod,o=a([].join);s("join",(function(e){return o(i(this),e)}))},"60bd":function(e,t,r){"use strict";var n=r("da84"),a=r("d039"),i=r("e330"),s=r("ebb5"),o=r("e260"),f=r("b622"),l=f("iterator"),c=n.Uint8Array,h=i(o.values),u=i(o.keys),d=i(o.entries),p=s.aTypedArray,m=s.exportTypedArrayMethod,g=c&&c.prototype,v=!a((function(){g[l].call([1])})),T=!!g&&g.values&&g[l]===g.values&&"values"===g.values.name,b=function(){return h(p(this))};m("entries",(function(){return d(p(this))}),v),m("keys",(function(){return u(p(this))}),v),m("values",b,v||!T,{name:"values"}),m(l,b,v||!T,{name:"values"})},"621a":function(e,t,r){"use strict";var n=r("da84"),a=r("e330"),i=r("83ab"),s=r("4b11"),o=r("5e77"),f=r("9112"),l=r("edd0"),c=r("6964"),h=r("d039"),u=r("19aa"),d=r("5926"),p=r("50c4"),m=r("0b25"),g=r("be8e"),v=r("77a7"),T=r("e163"),b=r("d2bb"),w=r("81d5"),E=r("f36a"),A=r("7156"),y=r("e893"),S=r("d44e"),_=r("69f3"),x=o.PROPER,O=o.CONFIGURABLE,C="ArrayBuffer",R="DataView",k="prototype",I="Wrong length",N="Wrong index",D=_.getterFor(C),P=_.getterFor(R),L=_.set,M=n[C],F=M,U=F&&F[k],B=n[R],W=B&&B[k],H=Object.prototype,G=n.Array,V=n.RangeError,j=a(w),z=a([].reverse),$=v.pack,Y=v.unpack,X=function(e){return[255&e]},K=function(e){return[255&e,e>>8&255]},J=function(e){return[255&e,e>>8&255,e>>16&255,e>>24&255]},Z=function(e){return e[3]<<24|e[2]<<16|e[1]<<8|e[0]},q=function(e){return $(g(e),23,4)},Q=function(e){return $(e,52,8)},ee=function(e,t,r){l(e[k],t,{configurable:!0,get:function(){return r(this)[t]}})},te=function(e,t,r,n){var a=P(e),i=m(r),s=!!n;if(i+t>a.byteLength)throw new V(N);var o=a.bytes,f=i+a.byteOffset,l=E(o,f,f+t);return s?l:z(l)},re=function(e,t,r,n,a,i){var s=P(e),o=m(r),f=n(+a),l=!!i;if(o+t>s.byteLength)throw new V(N);for(var c=s.bytes,h=o+s.byteOffset,u=0;u<t;u++)c[h+u]=f[l?u:t-u-1]};if(s){var ne=x&&M.name!==C;h((function(){M(1)}))&&h((function(){new M(-1)}))&&!h((function(){return new M,new M(1.5),new M(NaN),1!==M.length||ne&&!O}))?ne&&O&&f(M,"name",C):(F=function(e){return u(this,U),A(new M(m(e)),this,F)},F[k]=U,U.constructor=F,y(F,M)),b&&T(W)!==H&&b(W,H);var ae=new B(new F(2)),ie=a(W.setInt8);ae.setInt8(0,2147483648),ae.setInt8(1,2147483649),!ae.getInt8(0)&&ae.getInt8(1)||c(W,{setInt8:function(e,t){ie(this,e,t<<24>>24)},setUint8:function(e,t){ie(this,e,t<<24>>24)}},{unsafe:!0})}else F=function(e){u(this,U);var t=m(e);L(this,{type:C,bytes:j(G(t),0),byteLength:t}),i||(this.byteLength=t,this.detached=!1)},U=F[k],B=function(e,t,r){u(this,W),u(e,U);var n=D(e),a=n.byteLength,s=d(t);if(s<0||s>a)throw new V("Wrong offset");if(r=void 0===r?a-s:p(r),s+r>a)throw new V(I);L(this,{type:R,buffer:e,byteLength:r,byteOffset:s,bytes:n.bytes}),i||(this.buffer=e,this.byteLength=r,this.byteOffset=s)},W=B[k],i&&(ee(F,"byteLength",D),ee(B,"buffer",P),ee(B,"byteLength",P),ee(B,"byteOffset",P)),c(W,{getInt8:function(e){return te(this,1,e)[0]<<24>>24},getUint8:function(e){return te(this,1,e)[0]},getInt16:function(e){var t=te(this,2,e,arguments.length>1&&arguments[1]);return(t[1]<<8|t[0])<<16>>16},getUint16:function(e){var t=te(this,2,e,arguments.length>1&&arguments[1]);return t[1]<<8|t[0]},getInt32:function(e){return Z(te(this,4,e,arguments.length>1&&arguments[1]))},getUint32:function(e){return Z(te(this,4,e,arguments.length>1&&arguments[1]))>>>0},getFloat32:function(e){return Y(te(this,4,e,arguments.length>1&&arguments[1]),23)},getFloat64:function(e){return Y(te(this,8,e,arguments.length>1&&arguments[1]),52)},setInt8:function(e,t){re(this,1,e,X,t)},setUint8:function(e,t){re(this,1,e,X,t)},setInt16:function(e,t){re(this,2,e,K,t,arguments.length>2&&arguments[2])},setUint16:function(e,t){re(this,2,e,K,t,arguments.length>2&&arguments[2])},setInt32:function(e,t){re(this,4,e,J,t,arguments.length>2&&arguments[2])},setUint32:function(e,t){re(this,4,e,J,t,arguments.length>2&&arguments[2])},setFloat32:function(e,t){re(this,4,e,q,t,arguments.length>2&&arguments[2])},setFloat64:function(e,t){re(this,8,e,Q,t,arguments.length>2&&arguments[2])}});S(F,C),S(B,R),e.exports={ArrayBuffer:F,DataView:B}},"649e":function(e,t,r){"use strict";var n=r("ebb5"),a=r("b727").some,i=n.aTypedArray,s=n.exportTypedArrayMethod;s("some",(function(e){return a(i(this),e,arguments.length>1?arguments[1]:void 0)}))},"6ce5":function(e,t,r){"use strict";var n=r("df7e"),a=r("ebb5"),i=a.aTypedArray,s=a.exportTypedArrayMethod,o=a.getTypedArrayConstructor;s("toReversed",(function(){return n(i(this),o(this))}))},"72f7":function(e,t,r){"use strict";var n=r("ebb5").exportTypedArrayMethod,a=r("d039"),i=r("da84"),s=r("e330"),o=i.Uint8Array,f=o&&o.prototype||{},l=[].toString,c=s([].join);a((function(){l.call({})}))&&(l=function(){return c(this)});var h=f.toString!==l;n("toString",l,h)},"735e":function(e,t,r){"use strict";var n=r("ebb5"),a=r("81d5"),i=r("f495"),s=r("f5df"),o=r("c65b"),f=r("e330"),l=r("d039"),c=n.aTypedArray,h=n.exportTypedArrayMethod,u=f("".slice),d=l((function(){var e=0;return new Int8Array(2).fill({valueOf:function(){return e++}}),1!==e}));h("fill",(function(e){var t=arguments.length;c(this);var r="Big"===u(s(this),0,3)?i(e):+e;return o(a,this,r,t>1?arguments[1]:void 0,t>2?arguments[2]:void 0)}),d)},"74e8":function(e,t,r){"use strict";var n=r("23e7"),a=r("da84"),i=r("c65b"),s=r("83ab"),o=r("8aa7"),f=r("ebb5"),l=r("621a"),c=r("19aa"),h=r("5c6c"),u=r("9112"),d=r("eac5"),p=r("50c4"),m=r("0b25"),g=r("182d"),v=r("13a6"),T=r("a04b"),b=r("1a2d"),w=r("f5df"),E=r("861d"),A=r("d9b5"),y=r("7c73"),S=r("3a9b"),_=r("d2bb"),x=r("241c").f,O=r("a078"),C=r("b727").forEach,R=r("2626"),k=r("edd0"),I=r("9bf2"),N=r("06cf"),D=r("dfb9"),P=r("69f3"),L=r("7156"),M=P.get,F=P.set,U=P.enforce,B=I.f,W=N.f,H=a.RangeError,G=l.ArrayBuffer,V=G.prototype,j=l.DataView,z=f.NATIVE_ARRAY_BUFFER_VIEWS,$=f.TYPED_ARRAY_TAG,Y=f.TypedArray,X=f.TypedArrayPrototype,K=f.isTypedArray,J="BYTES_PER_ELEMENT",Z="Wrong length",q=function(e,t){k(e,t,{configurable:!0,get:function(){return M(this)[t]}})},Q=function(e){var t;return S(V,e)||"ArrayBuffer"===(t=w(e))||"SharedArrayBuffer"===t},ee=function(e,t){return K(e)&&!A(t)&&t in e&&d(+t)&&t>=0},te=function(e,t){return t=T(t),ee(e,t)?h(2,e[t]):W(e,t)},re=function(e,t,r){return t=T(t),!(ee(e,t)&&E(r)&&b(r,"value"))||b(r,"get")||b(r,"set")||r.configurable||b(r,"writable")&&!r.writable||b(r,"enumerable")&&!r.enumerable?B(e,t,r):(e[t]=r.value,e)};s?(z||(N.f=te,I.f=re,q(X,"buffer"),q(X,"byteOffset"),q(X,"byteLength"),q(X,"length")),n({target:"Object",stat:!0,forced:!z},{getOwnPropertyDescriptor:te,defineProperty:re}),e.exports=function(e,t,r){var s=e.match(/\d+/)[0]/8,f=e+(r?"Clamped":"")+"Array",l="get"+e,h="set"+e,d=a[f],T=d,b=T&&T.prototype,w={},A=function(e,t){var r=M(e);return r.view[l](t*s+r.byteOffset,!0)},S=function(e,t,n){var a=M(e);a.view[h](t*s+a.byteOffset,r?v(n):n,!0)},k=function(e,t){B(e,t,{get:function(){return A(this,t)},set:function(e){return S(this,t,e)},enumerable:!0})};z?o&&(T=t((function(e,t,r,n){return c(e,b),L(function(){return E(t)?Q(t)?void 0!==n?new d(t,g(r,s),n):void 0!==r?new d(t,g(r,s)):new d(t):K(t)?D(T,t):i(O,T,t):new d(m(t))}(),e,T)})),_&&_(T,Y),C(x(d),(function(e){e in T||u(T,e,d[e])})),T.prototype=b):(T=t((function(e,t,r,n){c(e,b);var a,o,f,l=0,h=0;if(E(t)){if(!Q(t))return K(t)?D(T,t):i(O,T,t);a=t,h=g(r,s);var u=t.byteLength;if(void 0===n){if(u%s)throw new H(Z);if(o=u-h,o<0)throw new H(Z)}else if(o=p(n)*s,o+h>u)throw new H(Z);f=o/s}else f=m(t),o=f*s,a=new G(o);F(e,{buffer:a,byteOffset:h,byteLength:o,length:f,view:new j(a)});while(l<f)k(e,l++)})),_&&_(T,Y),b=T.prototype=y(X)),b.constructor!==T&&u(b,"constructor",T),U(b).TypedArrayConstructor=T,$&&u(b,$,f);var I=T!==d;w[f]=T,n({global:!0,constructor:!0,forced:I,sham:!z},w),J in T||u(T,J,s),J in b||u(b,J,s),R(f)}):e.exports=function(){}},"75bd":function(e,t,r){"use strict";var n=r("e330"),a=r("b620"),i=n(ArrayBuffer.prototype.slice);e.exports=function(e){if(0!==a(e))return!1;try{return i(e,0,0),!1}catch(t){return!0}}},"77a7":function(e,t,r){"use strict";var n=Array,a=Math.abs,i=Math.pow,s=Math.floor,o=Math.log,f=Math.LN2,l=function(e,t,r){var l,c,h,u=n(r),d=8*r-t-1,p=(1<<d)-1,m=p>>1,g=23===t?i(2,-24)-i(2,-77):0,v=e<0||0===e&&1/e<0?1:0,T=0;e=a(e),e!==e||e===1/0?(c=e!==e?1:0,l=p):(l=s(o(e)/f),h=i(2,-l),e*h<1&&(l--,h*=2),e+=l+m>=1?g/h:g*i(2,1-m),e*h>=2&&(l++,h/=2),l+m>=p?(c=0,l=p):l+m>=1?(c=(e*h-1)*i(2,t),l+=m):(c=e*i(2,m-1)*i(2,t),l=0));while(t>=8)u[T++]=255&c,c/=256,t-=8;l=l<<t|c,d+=t;while(d>0)u[T++]=255&l,l/=256,d-=8;return u[--T]|=128*v,u},c=function(e,t){var r,n=e.length,a=8*n-t-1,s=(1<<a)-1,o=s>>1,f=a-7,l=n-1,c=e[l--],h=127&c;c>>=7;while(f>0)h=256*h+e[l--],f-=8;r=h&(1<<-f)-1,h>>=-f,f+=t;while(f>0)r=256*r+e[l--],f-=8;if(0===h)h=1-o;else{if(h===s)return r?NaN:c?-1/0:1/0;r+=i(2,t),h-=o}return(c?-1:1)*r*i(2,h-t)};e.exports={pack:l,unpack:c}},"7c37":function(e,t,r){"use strict";var n=r("605d");e.exports=function(e){try{if(n)return Function('return require("'+e+'")')()}catch(t){}}},"82f8":function(e,t,r){"use strict";var n=r("ebb5"),a=r("4d64").includes,i=n.aTypedArray,s=n.exportTypedArrayMethod;s("includes",(function(e){return a(i(this),e,arguments.length>1?arguments[1]:void 0)}))},"8aa7":function(e,t,r){"use strict";var n=r("da84"),a=r("d039"),i=r("1c7e"),s=r("ebb5").NATIVE_ARRAY_BUFFER_VIEWS,o=n.ArrayBuffer,f=n.Int8Array;e.exports=!s||!a((function(){f(1)}))||!a((function(){new f(-1)}))||!i((function(e){new f,new f(null),new f(1.5),new f(e)}),!0)||a((function(){return 1!==new f(new o(2),1,void 0).length}))},"907a":function(e,t,r){"use strict";var n=r("ebb5"),a=r("07fa"),i=r("5926"),s=n.aTypedArray,o=n.exportTypedArrayMethod;o("at",(function(e){var t=s(this),r=a(t),n=i(e),o=n>=0?n:r+n;return o<0||o>=r?void 0:t[o]}))},"986a":function(e,t,r){"use strict";var n=r("ebb5"),a=r("a258").findLast,i=n.aTypedArray,s=n.exportTypedArrayMethod;s("findLast",(function(e){return a(i(this),e,arguments.length>1?arguments[1]:void 0)}))},"9a8c":function(e,t,r){"use strict";var n=r("e330"),a=r("ebb5"),i=r("145e"),s=n(i),o=a.aTypedArray,f=a.exportTypedArrayMethod;f("copyWithin",(function(e,t){return s(o(this),e,t,arguments.length>2?arguments[2]:void 0)}))},a078:function(e,t,r){"use strict";var n=r("0366"),a=r("c65b"),i=r("5087"),s=r("7b0b"),o=r("07fa"),f=r("9a1f"),l=r("35a1"),c=r("e95a"),h=r("bcbf"),u=r("ebb5").aTypedArrayConstructor,d=r("f495");e.exports=function(e){var t,r,p,m,g,v,T,b,w=i(this),E=s(e),A=arguments.length,y=A>1?arguments[1]:void 0,S=void 0!==y,_=l(E);if(_&&!c(_)){T=f(E,_),b=T.next,E=[];while(!(v=a(b,T)).done)E.push(v.value)}for(S&&A>2&&(y=n(y,arguments[2])),r=o(E),p=new(u(w))(r),m=h(p),t=0;r>t;t++)g=S?y(E[t],t):E[t],p[t]=m?d(g):+g;return p}},a258:function(e,t,r){"use strict";var n=r("0366"),a=r("44ad"),i=r("7b0b"),s=r("07fa"),o=function(e){var t=1===e;return function(r,o,f){var l,c,h=i(r),u=a(h),d=s(u),p=n(o,f);while(d-- >0)if(l=u[d],c=p(l,d,h),c)switch(e){case 0:return l;case 1:return d}return t?-1:void 0}};e.exports={findLast:o(0),findLastIndex:o(1)}},a975:function(e,t,r){"use strict";var n=r("ebb5"),a=r("b727").every,i=n.aTypedArray,s=n.exportTypedArrayMethod;s("every",(function(e){return a(i(this),e,arguments.length>1?arguments[1]:void 0)}))},ace4:function(e,t,r){"use strict";var n=r("23e7"),a=r("4625"),i=r("d039"),s=r("621a"),o=r("825a"),f=r("23cb"),l=r("50c4"),c=r("4840"),h=s.ArrayBuffer,u=s.DataView,d=u.prototype,p=a(h.prototype.slice),m=a(d.getUint8),g=a(d.setUint8),v=i((function(){return!new h(2).slice(1,void 0).byteLength}));n({target:"ArrayBuffer",proto:!0,unsafe:!0,forced:v},{slice:function(e,t){if(p&&void 0===t)return p(o(this),e);var r=o(this).byteLength,n=f(e,r),a=f(void 0===t?r:t,r),i=new(c(this,h))(l(a-n)),s=new u(this),d=new u(i),v=0;while(n<a)g(d,v++,m(s,n++));return i}})},b39a:function(e,t,r){"use strict";var n=r("da84"),a=r("2ba4"),i=r("ebb5"),s=r("d039"),o=r("f36a"),f=n.Int8Array,l=i.aTypedArray,c=i.exportTypedArrayMethod,h=[].toLocaleString,u=!!f&&s((function(){h.call(new f(1))})),d=s((function(){return[1,2].toLocaleString()!==new f([1,2]).toLocaleString()}))||!s((function(){f.prototype.toLocaleString.call([1,2])}));c("toLocaleString",(function(){return a(h,u?o(l(this)):l(this),o(arguments))}),d)},b620:function(e,t,r){"use strict";var n=r("7282"),a=r("c6b6"),i=TypeError;e.exports=n(ArrayBuffer.prototype,"byteLength","get")||function(e){if("ArrayBuffer"!==a(e))throw new i("ArrayBuffer expected");return e.byteLength}},b6b7:function(e,t,r){"use strict";var n=r("ebb5"),a=r("4840"),i=n.aTypedArrayConstructor,s=n.getTypedArrayConstructor;e.exports=function(e){return i(a(e,s(e)))}},bcbf:function(e,t,r){"use strict";var n=r("f5df");e.exports=function(e){var t=n(e);return"BigInt64Array"===t||"BigUint64Array"===t}},be8e:function(e,t,r){"use strict";var n=r("fc1b"),a=1.1920928955078125e-7,i=34028234663852886e22,s=11754943508222875e-54;e.exports=Math.fround||function(e){return n(e,a,i,s)}},c19f:function(e,t,r){"use strict";var n=r("23e7"),a=r("da84"),i=r("621a"),s=r("2626"),o="ArrayBuffer",f=i[o],l=a[o];n({global:!0,constructor:!0,forced:l!==f},{ArrayBuffer:f}),s(o)},c1ac:function(e,t,r){"use strict";var n=r("ebb5"),a=r("b727").filter,i=r("1448"),s=n.aTypedArray,o=n.exportTypedArrayMethod;o("filter",(function(e){var t=a(s(this),e,arguments.length>1?arguments[1]:void 0);return i(this,t)}))},ca91:function(e,t,r){"use strict";var n=r("ebb5"),a=r("d58f").left,i=n.aTypedArray,s=n.exportTypedArrayMethod;s("reduce",(function(e){var t=arguments.length;return a(i(this),e,t,t>1?arguments[1]:void 0)}))},cd26:function(e,t,r){"use strict";var n=r("ebb5"),a=n.aTypedArray,i=n.exportTypedArrayMethod,s=Math.floor;i("reverse",(function(){var e,t=this,r=a(t).length,n=s(r/2),i=0;while(i<n)e=t[i],t[i++]=t[--r],t[r]=e;return t}))},d139:function(e,t,r){"use strict";var n=r("ebb5"),a=r("b727").find,i=n.aTypedArray,s=n.exportTypedArrayMethod;s("find",(function(e){return a(i(this),e,arguments.length>1?arguments[1]:void 0)}))},d429:function(e,t,r){"use strict";var n=r("07fa"),a=r("5926"),i=RangeError;e.exports=function(e,t,r,s){var o=n(e),f=a(r),l=f<0?o+f:f;if(l>=o||l<0)throw new i("Incorrect index");for(var c=new t(o),h=0;h<o;h++)c[h]=h===l?s:e[h];return c}},d5d6:function(e,t,r){"use strict";var n=r("ebb5"),a=r("b727").forEach,i=n.aTypedArray,s=n.exportTypedArrayMethod;s("forEach",(function(e){a(i(this),e,arguments.length>1?arguments[1]:void 0)}))},dbe5:function(e,t,r){"use strict";var n=r("da84"),a=r("d039"),i=r("2d00"),s=r("6069"),o=r("6c59"),f=r("605d"),l=n.structuredClone;e.exports=!!l&&!a((function(){if(o&&i>92||f&&i>94||s&&i>97)return!1;var e=new ArrayBuffer(8),t=l(e,{transfer:[e]});return 0!==e.byteLength||8!==t.byteLength}))},df7e:function(e,t,r){"use strict";var n=r("07fa");e.exports=function(e,t){for(var r=n(e),a=new t(r),i=0;i<r;i++)a[i]=e[r-i-1];return a}},dfb9:function(e,t,r){"use strict";var n=r("07fa");e.exports=function(e,t,r){var a=0,i=arguments.length>2?r:n(t),s=new e(i);while(i>a)s[a]=t[a++];return s}},e58c:function(e,t,r){"use strict";var n=r("2ba4"),a=r("fc6a"),i=r("5926"),s=r("07fa"),o=r("a640"),f=Math.min,l=[].lastIndexOf,c=!!l&&1/[1].lastIndexOf(1,-0)<0,h=o("lastIndexOf"),u=c||!h;e.exports=u?function(e){if(c)return n(l,this,arguments)||0;var t=a(this),r=s(t);if(0===r)return-1;var o=r-1;for(arguments.length>1&&(o=f(o,i(arguments[1]))),o<0&&(o=r+o);o>=0;o--)if(o in t&&t[o]===e)return o||0;return-1}:l},e91f:function(e,t,r){"use strict";var n=r("ebb5"),a=r("4d64").indexOf,i=n.aTypedArray,s=n.exportTypedArrayMethod;s("indexOf",(function(e){return a(i(this),e,arguments.length>1?arguments[1]:void 0)}))},ebb5:function(e,t,r){"use strict";var n,a,i,s=r("4b11"),o=r("83ab"),f=r("da84"),l=r("1626"),c=r("861d"),h=r("1a2d"),u=r("f5df"),d=r("0d51"),p=r("9112"),m=r("cb2d"),g=r("edd0"),v=r("3a9b"),T=r("e163"),b=r("d2bb"),w=r("b622"),E=r("90e3"),A=r("69f3"),y=A.enforce,S=A.get,_=f.Int8Array,x=_&&_.prototype,O=f.Uint8ClampedArray,C=O&&O.prototype,R=_&&T(_),k=x&&T(x),I=Object.prototype,N=f.TypeError,D=w("toStringTag"),P=E("TYPED_ARRAY_TAG"),L="TypedArrayConstructor",M=s&&!!b&&"Opera"!==u(f.opera),F=!1,U={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},B={BigInt64Array:8,BigUint64Array:8},W=function(e){if(!c(e))return!1;var t=u(e);return"DataView"===t||h(U,t)||h(B,t)},H=function(e){var t=T(e);if(c(t)){var r=S(t);return r&&h(r,L)?r[L]:H(t)}},G=function(e){if(!c(e))return!1;var t=u(e);return h(U,t)||h(B,t)},V=function(e){if(G(e))return e;throw new N("Target is not a typed array")},j=function(e){if(l(e)&&(!b||v(R,e)))return e;throw new N(d(e)+" is not a typed array constructor")},z=function(e,t,r,n){if(o){if(r)for(var a in U){var i=f[a];if(i&&h(i.prototype,e))try{delete i.prototype[e]}catch(s){try{i.prototype[e]=t}catch(l){}}}k[e]&&!r||m(k,e,r?t:M&&x[e]||t,n)}},$=function(e,t,r){var n,a;if(o){if(b){if(r)for(n in U)if(a=f[n],a&&h(a,e))try{delete a[e]}catch(i){}if(R[e]&&!r)return;try{return m(R,e,r?t:M&&R[e]||t)}catch(i){}}for(n in U)a=f[n],!a||a[e]&&!r||m(a,e,t)}};for(n in U)a=f[n],i=a&&a.prototype,i?y(i)[L]=a:M=!1;for(n in B)a=f[n],i=a&&a.prototype,i&&(y(i)[L]=a);if((!M||!l(R)||R===Function.prototype)&&(R=function(){throw new N("Incorrect invocation")},M))for(n in U)f[n]&&b(f[n],R);if((!M||!k||k===I)&&(k=R.prototype,M))for(n in U)f[n]&&b(f[n].prototype,k);if(M&&T(C)!==k&&b(C,k),o&&!h(k,D))for(n in F=!0,g(k,D,{configurable:!0,get:function(){return c(this)?this[P]:void 0}}),U)f[n]&&p(f[n],P,n);e.exports={NATIVE_ARRAY_BUFFER_VIEWS:M,TYPED_ARRAY_TAG:F&&P,aTypedArray:V,aTypedArrayConstructor:j,exportTypedArrayMethod:z,exportTypedArrayStaticMethod:$,getTypedArrayConstructor:H,isView:W,isTypedArray:G,TypedArray:R,TypedArrayPrototype:k}},f495:function(e,t,r){"use strict";var n=r("c04e"),a=TypeError;e.exports=function(e){var t=n(e,"number");if("number"==typeof t)throw new a("Can't convert number to bigint");return BigInt(t)}},f748:function(e,t,r){"use strict";e.exports=Math.sign||function(e){var t=+e;return 0===t||t!==t?t:t<0?-1:1}},f8cd:function(e,t,r){"use strict";var n=r("5926"),a=RangeError;e.exports=function(e){var t=n(e);if(t<0)throw new a("The argument can't be less than 0");return t}},fc1b:function(e,t,r){"use strict";var n=r("f748"),a=Math.abs,i=2220446049250313e-31,s=1/i,o=function(e){return e+s-s};e.exports=function(e,t,r,s){var f=+e,l=a(f),c=n(f);if(l<s)return c*o(l/s/t)*s*t;var h=(1+t/i)*l,u=h-(h-l);return u>r||u!==u?c*(1/0):c*u}}}]);