(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-daf6ef8a"],{"08c8":function(t,e,s){"use strict";s.d(e,"d",(function(){return i})),s.d(e,"c",(function(){return o})),s.d(e,"a",(function(){return n})),s.d(e,"e",(function(){return l})),s.d(e,"b",(function(){return r}));var a=s("b775");function i(t){return Object(a["a"])({url:"/game/odds/list",method:"get",params:t})}function o(t){return Object(a["a"])({url:"/game/odds/"+t,method:"get"})}function n(t){return Object(a["a"])({url:"/game/odds",method:"post",data:t})}function l(t){return Object(a["a"])({url:"/game/odds",method:"put",data:t})}function r(t){return Object(a["a"])({url:"/game/odds/"+t,method:"delete"})}},1494:function(t,e,s){"use strict";s("8717")},8717:function(t,e,s){},f919:function(t,e,s){"use strict";s.r(e);var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"app-container odds-container"},[s("div",{directives:[{name:"show",rawName:"v-show",value:t.showSearch,expression:"showSearch"}],staticClass:"search-container"},[s("el-form",{ref:"queryForm",attrs:{model:t.queryParams,size:"small",inline:!0,"label-width":"80px"}},[s("el-form-item",{attrs:{label:"玩法名称",prop:"methodName"}},[s("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入玩法名称",clearable:"","prefix-icon":"el-icon-search"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.methodName,callback:function(e){t.$set(t.queryParams,"methodName",e)},expression:"queryParams.methodName"}})],1),s("el-form-item",{attrs:{label:"赔率范围",prop:"odds"}},[s("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入赔率",clearable:"","prefix-icon":"el-icon-money"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.odds,callback:function(e){t.$set(t.queryParams,"odds",e)},expression:"queryParams.odds"}})],1),s("el-form-item",[s("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:t.handleQuery}},[t._v("搜索")]),s("el-button",{attrs:{icon:"el-icon-refresh",size:"small"},on:{click:t.resetQuery}},[t._v("重置")])],1)],1)],1),s("div",{staticClass:"toolbar-container"},[s("div",{staticClass:"toolbar-left"},[s("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["game:odds:export"],expression:"['game:odds:export']"}],staticClass:"action-btn",attrs:{type:"info",icon:"el-icon-download",size:"small"},on:{click:t.handleExport}},[t._v("导出数据")])],1),s("div",{staticClass:"toolbar-right"},[s("right-toolbar",{attrs:{showSearch:t.showSearch},on:{"update:showSearch":function(e){t.showSearch=e},"update:show-search":function(e){t.showSearch=e},queryTable:t.getList}})],1)]),s("div",{staticClass:"table-container"},[s("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"odds-table",attrs:{data:t.oddsList,stripe:"",border:"","header-cell-style":{background:"#f8f9fa",color:"#606266",fontWeight:"bold"}},on:{"selection-change":t.handleSelectionChange}},[s("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),s("el-table-column",{attrs:{label:"玩法名称",align:"center",prop:"methodName","min-width":"200"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("div",{staticClass:"method-name"},[s("i",{staticClass:"el-icon-trophy",staticStyle:{color:"#409EFF","margin-right":"8px"}}),s("span",{staticStyle:{"font-weight":"500","font-size":"16px"}},[t._v(t._s(e.row.methodName))])])]}}])}),s("el-table-column",{attrs:{label:"赔率",align:"center",prop:"odds","min-width":"150"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("div",{staticClass:"odds-display"},[s("span",{staticClass:"odds-value"},[t._v(t._s(t.formatOdds(e.row.odds)))])])]}}])}),s("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width","min-width":"140"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("div",{staticClass:"action-buttons"},[s("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["game:odds:list"],expression:"['game:odds:list']"}],staticClass:"action-btn-mini edit-btn",attrs:{size:"mini",type:"primary"},on:{click:function(s){return t.handleUpdate(e.row)}}},[t._v("修改")])],1)]}}])})],1)],1),s("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total>0"}],attrs:{total:t.total,page:t.queryParams.pageNum,limit:t.queryParams.pageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"pageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"pageSize",e)},pagination:t.getList}}),s("el-dialog",{staticClass:"modern-odds-dialog",attrs:{title:t.title,visible:t.open,width:"580px","append-to-body":"","close-on-click-modal":!1,"show-close":!1,"custom-class":"beautiful-dialog"},on:{"update:visible":function(e){t.open=e}}},[s("div",{staticClass:"dialog-header",attrs:{slot:"title"},slot:"title"},[s("div",{staticClass:"header-content"},[s("div",{staticClass:"header-icon"},[s("i",{staticClass:"el-icon-s-data"})]),s("div",{staticClass:"header-text"},[s("h3",[t._v(t._s(t.title))]),s("p",[t._v("设置玩法赔率，管理投注收益比例")])])]),s("el-button",{staticClass:"close-btn",attrs:{type:"text",icon:"el-icon-close"},on:{click:t.cancel}})],1),s("div",{staticClass:"modern-dialog-content"},[s("el-form",{ref:"form",staticClass:"modern-odds-form",attrs:{model:t.form,rules:t.rules,"label-width":"0"}},[s("div",{staticClass:"form-card"},[s("div",{staticClass:"card-header"},[s("i",{staticClass:"el-icon-menu"}),s("span",[t._v("选择玩法")]),t.methodList.length?s("span",{staticClass:"method-count"},[t._v("共 "+t._s(t.methodList.length)+" 种玩法")]):t._e()]),s("div",{staticClass:"card-content"},[s("div",{staticClass:"method-select-wrapper"},[s("div",{staticClass:"select-label"},[s("i",{staticClass:"el-icon-s-data"}),s("span",[t._v("玩法类型")])]),s("el-form-item",{attrs:{prop:"methodId"}},[s("el-select",{staticClass:"modern-select full-width",attrs:{placeholder:"请选择要设置赔率的玩法",filterable:"","popper-class":"method-select-dropdown"},on:{change:t.handleMethodChange},model:{value:t.form.methodId,callback:function(e){t.$set(t.form,"methodId",e)},expression:"form.methodId"}},t._l(t.methodList,(function(e){return s("el-option",{key:e.methodId,staticClass:"method-option",attrs:{label:e.methodName,value:e.methodId}},[s("div",{staticClass:"option-content"},[s("span",{staticClass:"option-name"},[t._v(t._s(e.methodName))]),s("span",{staticClass:"option-id"},[t._v("ID: "+t._s(e.methodId))])])])})),1)],1),s("div",{staticClass:"method-tips"},[s("div",{staticClass:"tip-row"},[s("i",{staticClass:"el-icon-info"}),s("span",[t._v("支持输入关键词快速搜索玩法")])]),s("div",{staticClass:"tip-row"},[s("i",{staticClass:"el-icon-warning"}),s("span",[t._v("每种玩法只能设置一个赔率")])])])],1)])]),s("div",{staticClass:"form-card"},[s("div",{staticClass:"card-header"},[s("i",{staticClass:"el-icon-money"}),s("span",[t._v("设置赔率")])]),s("div",{staticClass:"card-content"},[s("el-form-item",{attrs:{prop:"odds"}},[s("div",{staticClass:"odds-input-wrapper"},[s("el-input",{staticClass:"modern-input",attrs:{placeholder:"请输入赔率倍数",type:"number",step:"0.01",min:"0.01",max:"999.99",size:"large"},on:{input:t.handleOddsChange},model:{value:t.form.odds,callback:function(e){t.$set(t.form,"odds",e)},expression:"form.odds"}},[s("template",{slot:"prepend"},[s("i",{staticClass:"el-icon-s-finance"}),t._v(" 倍率 ")]),s("template",{slot:"append"},[t._v("倍")])],2)],1),s("div",{staticClass:"input-tips"},[s("div",{staticClass:"tip-item"},[s("i",{staticClass:"el-icon-info"}),s("span",[t._v("赔率范围：0.01 - 999.99")])]),s("div",{staticClass:"tip-item"},[s("i",{staticClass:"el-icon-warning"}),s("span",[t._v("支持小数点后两位精度")])])])])],1)]),t.form.odds&&parseFloat(t.form.odds)>0?s("div",{staticClass:"form-card preview-card"},[s("div",{staticClass:"card-header preview-header"},[s("i",{staticClass:"el-icon-view"}),s("span",[t._v("收益预览")]),s("el-tag",{attrs:{type:"success",size:"mini"}},[t._v("实时计算")])],1),s("div",{staticClass:"card-content"},[s("div",{staticClass:"preview-grid"},[s("div",{staticClass:"preview-item"},[s("div",{staticClass:"preview-icon"},[s("i",{staticClass:"el-icon-wallet"})]),s("div",{staticClass:"preview-info"},[s("div",{staticClass:"preview-label"},[t._v("投注金额")]),s("div",{staticClass:"preview-value"},[t._v("￥100.00")])])]),s("div",{staticClass:"preview-arrow"},[s("i",{staticClass:"el-icon-right"})]),s("div",{staticClass:"preview-item"},[s("div",{staticClass:"preview-icon profit-icon"},[s("i",{staticClass:"el-icon-trophy"})]),s("div",{staticClass:"preview-info"},[s("div",{staticClass:"preview-label"},[t._v("预期收益")]),s("div",{staticClass:"preview-value profit"},[t._v(t._s(t.formatProfit(t.form.odds,100)))])])])]),s("div",{staticClass:"profit-rate"},[s("span",[t._v("收益率：")]),s("span",{staticClass:"rate-value"},[t._v(t._s(t.calculateProfitRate(t.form.odds))+"%")])])])]):t._e()])],1),s("div",{staticClass:"modern-dialog-footer",attrs:{slot:"footer"},slot:"footer"},[s("el-button",{staticClass:"cancel-btn",attrs:{size:"large"},on:{click:t.cancel}},[s("i",{staticClass:"el-icon-close"}),t._v(" 取消 ")]),s("el-button",{staticClass:"submit-btn",attrs:{type:"primary",size:"large"},on:{click:t.submitForm}},[s("i",{staticClass:"el-icon-check"}),t._v(" "+t._s(t.form.oddsId?"更新赔率":"创建赔率")+" ")])],1)])],1)},i=[],o=s("5530"),n=(s("7db0"),s("caad"),s("d81d"),s("b680"),s("d3b7"),s("25f0"),s("2532"),s("0643"),s("fffc"),s("a573"),s("08c8")),l=s("2449"),r={name:"Odds",data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,oddsList:[],methodList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:50,methodName:null,odds:null},form:{},rules:{methodId:[{required:!0,message:"玩法不能为空",trigger:"change"}],odds:[{required:!0,message:"赔率不能为空",trigger:"blur"}]}}},created:function(){this.getList(),this.getMethodList()},methods:{getList:function(){var t=this;this.loading=!0,Object(n["d"])(this.queryParams).then((function(e){t.oddsList=e.rows,t.total=e.total,t.loading=!1}))},getMethodList:function(){var t=this,e={pageNum:1,pageSize:1e3};Object(l["e"])(e).then((function(e){t.methodList=e.rows}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={oddsId:null,methodId:null,methodName:null,odds:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(t){this.ids=t.map((function(t){return t.oddsId})),this.single=1!==t.length,this.multiple=!t.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加赔率管理"},handleUpdate:function(t){var e=this;this.reset();var s=t.oddsId||this.ids;Object(n["c"])(s).then((function(t){e.form=t.data,e.open=!0,e.title="修改赔率管理"}))},handleMethodChange:function(t){var e=this.methodList.find((function(e){return e.methodId===t}));e&&(this.form.methodName=e.methodName)},submitForm:function(){var t=this;this.$refs["form"].validate((function(e){e&&(null!=t.form.oddsId?Object(n["e"])(t.form).then((function(){t.$modal.msgSuccess("修改成功"),t.open=!1,t.getList()})):Object(n["a"])(t.form).then((function(){t.$modal.msgSuccess("新增成功"),t.open=!1,t.getList()})))}))},handleDelete:function(t){var e=this,s=t.oddsId||this.ids;this.$modal.confirm('是否确认删除赔率管理编号为"'+s+'"的数据项？').then((function(){return Object(n["b"])(s)})).then((function(){e.getList(),e.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("game/odds/export",Object(o["a"])({},this.queryParams),"odds_".concat((new Date).getTime(),".xlsx"))},formatOdds:function(t){if(!t)return"0.00";var e=parseFloat(t);return e.toFixed(2)+"倍"},formatProfit:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;if(!t)return"￥0.00";var s=parseFloat(t),a=(s*e).toFixed(2);return"￥"+a},calculateProfitRate:function(t){if(!t)return"0";var e=(100*(parseFloat(t)-1)).toFixed(1);return e},handleOddsChange:function(t){if(t&&t.toString().includes(".")){var e=t.toString().split(".");e[1]&&e[1].length>2&&(this.form.odds=parseFloat(t).toFixed(2))}}}},d=r,c=(s("1494"),s("2877")),u=Object(c["a"])(d,a,i,!1,null,"c1ac194e",null);e["default"]=u.exports}}]);