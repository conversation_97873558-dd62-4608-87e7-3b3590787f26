(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-202bad4a"],{"0832":function(e,t,a){"use strict";a("4651")},"0ccb":function(e,t,a){"use strict";var s=a("e330"),i=a("50c4"),r=a("577e"),n=a("1148"),o=a("1d80"),l=s(n),c=s("".slice),u=Math.ceil,d=function(e){return function(t,a,s){var n,d,m=r(o(t)),p=i(a),h=m.length,v=void 0===s?" ":r(s);return p<=h||""===v?m:(n=p-h,d=l(v,u(n/v.length)),d.length>n&&(d=c(d,0,n)),e?m+d:d+m)}};e.exports={start:d(!1),end:d(!0)}},"1df0":function(e,t,a){},"1f34":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container user-management"},[e._m(0),a("div",{staticClass:"main-content"},[a("div",{staticClass:"content-wrapper"},[a("div",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],staticClass:"search-container"},[a("el-form",{ref:"queryForm",staticClass:"search-form",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"用户名称",prop:"userName"}},[a("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入用户名称",clearable:"","prefix-icon":"el-icon-user"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.userName,callback:function(t){e.$set(e.queryParams,"userName",t)},expression:"queryParams.userName"}})],1),a("el-form-item",{attrs:{label:"手机号码",prop:"phonenumber"}},[a("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入手机号码",clearable:"","prefix-icon":"el-icon-phone"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.phonenumber,callback:function(t){e.$set(e.queryParams,"phonenumber",t)},expression:"queryParams.phonenumber"}})],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-select",{staticStyle:{width:"150px"},attrs:{placeholder:"用户状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.sys_normal_disable,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"创建时间"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.dateRange,callback:function(t){e.dateRange=t},expression:"dateRange"}})],1),a("el-form-item",[a("el-button",{staticClass:"search-btn",attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{staticClass:"reset-btn",attrs:{icon:"el-icon-refresh",size:"small"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),a("div",{staticClass:"toolbar-container"},[a("div",{staticClass:"toolbar-left"},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:add"],expression:"['system:user:add']"}],staticClass:"action-btn primary-btn",attrs:{type:"primary",icon:"el-icon-plus",size:"small"},on:{click:e.handleAdd}},[e._v("新增用户")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:edit"],expression:"['system:user:edit']"}],staticClass:"action-btn success-btn",attrs:{type:"success",icon:"el-icon-edit",size:"small",disabled:e.single},on:{click:e.handleUpdate}},[e._v("批量修改")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:remove"],expression:"['system:user:remove']"}],staticClass:"action-btn danger-btn",attrs:{type:"danger",icon:"el-icon-delete",size:"small",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("批量删除")])],1),a("div",{staticClass:"toolbar-right"},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:import"],expression:"['system:user:import']"}],staticClass:"action-btn info-btn",attrs:{type:"info",icon:"el-icon-upload2",size:"small"},on:{click:e.handleImport}},[e._v("导入")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:export"],expression:"['system:user:export']"}],staticClass:"action-btn warning-btn",attrs:{type:"warning",icon:"el-icon-download",size:"small"},on:{click:e.handleExport}},[e._v("导出")]),a("right-toolbar",{attrs:{showSearch:e.showSearch,columns:e.columns},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1)]),a("div",{staticClass:"table-container"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"user-table",attrs:{data:e.userList,stripe:"",border:"","header-cell-style":{background:"#f8f9fa",color:"#606266",fontWeight:"bold"}},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),e.columns[0].visible?a("el-table-column",{key:"userId",attrs:{label:"用户编号",align:"center",prop:"userId","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"user-id"},[a("i",{staticClass:"el-icon-postcard"}),a("span",[e._v(e._s(t.row.userId))])])]}}],null,!1,1097652337)}):e._e(),e.columns[1].visible?a("el-table-column",{key:"userName",attrs:{label:"用户名称",align:"center",prop:"userName","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"user-name"},[a("i",{staticClass:"el-icon-user"}),a("span",[e._v(e._s(t.row.userName))])])]}}],null,!1,3150187820)}):e._e(),e.columns[2].visible?a("el-table-column",{key:"nickName",attrs:{label:"用户昵称",align:"center",prop:"nickName","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"nick-name"},[a("i",{staticClass:"el-icon-user-solid"}),a("span",[e._v(e._s(t.row.nickName))])])]}}],null,!1,3498846044)}):e._e(),e.columns[4].visible?a("el-table-column",{key:"phonenumber",attrs:{label:"手机号码",align:"center",prop:"phonenumber","min-width":"140"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"phone-number"},[a("i",{staticClass:"el-icon-phone"}),a("span",[e._v(e._s(t.row.phonenumber||"-"))])])]}}],null,!1,1983863692)}):e._e(),e.columns[5].visible?a("el-table-column",{key:"status",attrs:{label:"状态",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-switch",{attrs:{"active-value":"0","inactive-value":"1","active-color":"#67C23A","inactive-color":"#F56C6C"},on:{change:function(a){return e.handleStatusChange(t.row)}},model:{value:t.row.status,callback:function(a){e.$set(t.row,"status",a)},expression:"scope.row.status"}})]}}],null,!1,219140872)}):e._e(),a("el-table-column",{key:"showStatistics",attrs:{label:"统计权限",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-switch",{attrs:{"active-value":"1","inactive-value":"0","active-color":"#67C23A","inactive-color":"#F56C6C"},on:{change:function(a){return e.handleStatisticsChange(t.row)}},model:{value:t.row.showStatistics,callback:function(a){e.$set(t.row,"showStatistics",a)},expression:"scope.row.showStatistics"}})]}}])}),e.columns[6].visible?a("el-table-column",{key:"authStatus",attrs:{label:"授权状态",align:"center","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.userId?a("el-tag",{attrs:{type:"info",effect:"dark"}},[e._v("永久")]):"1"===t.row.authStatus?a("el-tag",{attrs:{type:"success",effect:"dark"}},[e._v("正常")]):"2"===t.row.authStatus?a("el-tag",{attrs:{type:"danger",effect:"dark"}},[e._v("已过期")]):a("el-tag",{attrs:{type:"warning",effect:"dark"}},[e._v("未授权")])]}}],null,!1,2198693918)}):e._e(),e.columns[7].visible?a("el-table-column",{attrs:{label:"授权到期时间",align:"center",prop:"authEndTime","min-width":"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"auth-time"},[a("i",{staticClass:"el-icon-time"}),1===t.row.userId?a("span",[e._v("永久")]):t.row.authEndTime?a("span",[e._v(e._s(e.parseTime(t.row.authEndTime,"{y}-{m}-{d}")))]):a("span",[e._v("-")])])]}}],null,!1,1379929966)}):e._e(),e.columns[8].visible?a("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime","min-width":"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"create-time"},[a("i",{staticClass:"el-icon-date"}),a("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d} {h}:{i}")))])])]}}],null,!1,3676748841)}):e._e(),a("el-table-column",{attrs:{label:"操作",align:"center","min-width":"200","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return 1!==t.row.userId?[a("div",{staticClass:"action-buttons"},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:edit"],expression:"['system:user:edit']"}],staticClass:"action-btn-mini edit-btn",attrs:{size:"mini",type:"primary",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:remove"],expression:"['system:user:remove']"}],staticClass:"action-btn-mini delete-btn",attrs:{size:"mini",type:"danger",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")]),a("el-dropdown",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:resetPwd","system:user:edit"],expression:"['system:user:resetPwd', 'system:user:edit']"}],staticClass:"more-dropdown",attrs:{size:"mini"},on:{command:function(a){return e.handleCommand(a,t.row)}}},[a("el-button",{staticClass:"action-btn-mini more-btn",attrs:{size:"mini",type:"info",icon:"el-icon-more"}},[e._v("更多")]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:resetPwd"],expression:"['system:user:resetPwd']"}],attrs:{command:"handleResetPwd",icon:"el-icon-key"}},[e._v("重置密码")]),a("el-dropdown-item",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:edit"],expression:"['system:user:edit']"}],attrs:{command:"handleAuthRole",icon:"el-icon-circle-check"}},[e._v("分配角色")]),a("el-dropdown-item",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:edit"],expression:"['system:user:edit']"}],attrs:{command:"handleRenewAuth",icon:"el-icon-time"}},[e._v("用户续费")]),a("el-dropdown-item",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:edit"],expression:"['system:user:edit']"}],attrs:{command:"handleUpdateAuthTime",icon:"el-icon-date"}},[e._v("修改授权时间")])],1)],1)],1)]:void 0}}],null,!0)})],1)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1)]),a("el-dialog",{staticClass:"user-dialog",attrs:{visible:e.open,width:"600px","append-to-body":"","show-close":!1,"custom-class":"modern-user-dialog"},on:{"update:visible":function(t){e.open=t}}},[a("div",{staticClass:"dialog-header",attrs:{slot:"title"},slot:"title"},[a("div",{staticClass:"header-content"},[a("div",{staticClass:"header-icon"},[a("i",{staticClass:"el-icon-user-solid"})]),a("div",{staticClass:"header-text"},[a("h3",[e._v(e._s(e.form.userId?"编辑用户":"新增用户"))]),a("p",[e._v(e._s(e.form.userId?"修改用户信息和权限设置":"创建新的系统用户账户"))])])]),a("el-button",{staticClass:"close-btn",attrs:{type:"text",icon:"el-icon-close"},on:{click:e.cancel}})],1),a("div",{staticClass:"dialog-content"},[a("el-form",{ref:"form",staticClass:"user-form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("div",{staticClass:"form-section"},[a("div",{staticClass:"section-title"},[a("i",{staticClass:"el-icon-user"}),a("span",[e._v("基本信息")])]),a("el-row",{staticClass:"form-row",attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{staticClass:"form-item-spaced",attrs:{label:"用户昵称",prop:"nickName"}},[a("el-input",{attrs:{placeholder:"请输入用户昵称",maxlength:"30","prefix-icon":"el-icon-user-solid",clearable:""},model:{value:e.form.nickName,callback:function(t){e.$set(e.form,"nickName",t)},expression:"form.nickName"}})],1)],1),void 0==e.form.userId?a("el-col",{attrs:{span:12}},[a("el-form-item",{staticClass:"form-item-spaced",attrs:{label:"用户名称",prop:"userName"}},[a("el-input",{attrs:{placeholder:"请输入用户名称",maxlength:"30","prefix-icon":"el-icon-user",clearable:""},model:{value:e.form.userName,callback:function(t){e.$set(e.form,"userName",t)},expression:"form.userName"}})],1)],1):e._e()],1),void 0==e.form.userId?a("el-row",{staticClass:"form-row",attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{staticClass:"form-item-spaced",attrs:{label:"用户密码",prop:"password"}},[a("el-input",{attrs:{placeholder:"请输入用户密码",type:"password",maxlength:"20","show-password":"","prefix-icon":"el-icon-lock",clearable:""},model:{value:e.form.password,callback:function(t){e.$set(e.form,"password",t)},expression:"form.password"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{staticClass:"form-item-spaced",attrs:{label:"确认密码",prop:"confirmPassword"}},[a("el-input",{attrs:{placeholder:"请再次输入密码",type:"password",maxlength:"20","show-password":"","prefix-icon":"el-icon-lock",clearable:""},model:{value:e.form.confirmPassword,callback:function(t){e.$set(e.form,"confirmPassword",t)},expression:"form.confirmPassword"}})],1)],1)],1):e._e()],1),a("div",{staticClass:"form-section"},[a("div",{staticClass:"section-title"},[a("i",{staticClass:"el-icon-key"}),a("span",[e._v("权限设置")])]),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"用户状态"}},[a("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.dict.type.sys_normal_disable,(function(t){return a("el-radio",{key:t.value,attrs:{label:t.value}},[e._v(" "+e._s(t.label)+" ")])})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"用户角色"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"",placeholder:"请选择角色"},model:{value:e.form.roleIds,callback:function(t){e.$set(e.form,"roleIds",t)},expression:"form.roleIds"}},e._l(e.roleOptions,(function(e){return a("el-option",{key:e.roleId,attrs:{label:e.roleName,value:e.roleId,disabled:1==e.status}})})),1)],1)],1)],1)],1),1!=e.form.userId?a("div",{staticClass:"form-section"},[a("div",{staticClass:"section-title"},[a("i",{staticClass:"el-icon-time"}),a("span",[e._v("授权设置")])]),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"授权期限",prop:"authDurationType"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择授权期限"},on:{change:e.handleAuthDurationChange},model:{value:e.form.authDurationType,callback:function(t){e.$set(e.form,"authDurationType",t)},expression:"form.authDurationType"}},[a("el-option",{attrs:{label:"一天",value:"DAY"}},[a("span",{staticStyle:{float:"left"}},[e._v("一天")]),a("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v("24小时")])]),a("el-option",{attrs:{label:"一个月",value:"MONTH"}},[a("span",{staticStyle:{float:"left"}},[e._v("一个月")]),a("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v("30天")])]),a("el-option",{attrs:{label:"一个季度",value:"QUARTER"}},[a("span",{staticStyle:{float:"left"}},[e._v("一个季度")]),a("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v("90天")])]),a("el-option",{attrs:{label:"一年",value:"YEAR"}},[a("span",{staticStyle:{float:"left"}},[e._v("一年")]),a("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v("365天")])])],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"授权状态"}},[a("div",{staticClass:"auth-status-display"},["1"===e.form.authStatus?a("el-tag",{attrs:{type:"success",effect:"dark"}},[e._v("正常")]):"2"===e.form.authStatus?a("el-tag",{attrs:{type:"danger",effect:"dark"}},[e._v("已过期")]):a("el-tag",{attrs:{type:"warning",effect:"dark"}},[e._v("未授权")]),e.form.authEndTime?a("div",{staticClass:"auth-end-time"},[a("i",{staticClass:"el-icon-time"}),a("span",[e._v("到期："+e._s(e.parseTime(e.form.authEndTime,"{y}-{m}-{d}")))])]):e._e()],1)])],1)],1)],1):e._e()])],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{staticClass:"cancel-btn",on:{click:e.cancel}},[a("i",{staticClass:"el-icon-close"}),e._v(" 取消 ")]),a("el-button",{staticClass:"submit-btn",attrs:{type:"primary"},on:{click:e.submitForm}},[a("i",{staticClass:"el-icon-check"}),e._v(" "+e._s(e.form.userId?"保存修改":"创建用户")+" ")])],1)]),a("el-dialog",{attrs:{title:e.upload.title,visible:e.upload.open,width:"400px","append-to-body":""},on:{"update:visible":function(t){return e.$set(e.upload,"open",t)}}},[a("el-upload",{ref:"upload",attrs:{limit:1,accept:".xlsx, .xls",headers:e.upload.headers,action:e.upload.url+"?updateSupport="+e.upload.updateSupport,disabled:e.upload.isUploading,"on-progress":e.handleFileUploadProgress,"on-success":e.handleFileSuccess,"auto-upload":!1,drag:""}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),a("em",[e._v("点击上传")])]),a("div",{staticClass:"el-upload__tip text-center",attrs:{slot:"tip"},slot:"tip"},[a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[a("el-checkbox",{model:{value:e.upload.updateSupport,callback:function(t){e.$set(e.upload,"updateSupport",t)},expression:"upload.updateSupport"}}),e._v("是否更新已经存在的用户数据 ")],1),a("span",[e._v("仅允许导入xls、xlsx格式文件。")]),a("el-link",{staticStyle:{"font-size":"12px","vertical-align":"baseline"},attrs:{type:"primary",underline:!1},on:{click:e.importTemplate}},[e._v("下载模板")])],1)]),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitFileForm}},[e._v("确 定")]),a("el-button",{on:{click:function(t){e.upload.open=!1}}},[e._v("取 消")])],1)],1),a("el-dialog",{staticClass:"renew-dialog modern-dialog compact-dialog",attrs:{visible:e.renewOpen,width:"320px","append-to-body":"","close-on-click-modal":!1,"show-close":!1,center:!0,"custom-class":"modern-renew-dialog"},on:{"update:visible":function(t){e.renewOpen=t}}},[a("div",{staticClass:"dialog-header renew-header",attrs:{slot:"title"},slot:"title"},[a("div",{staticClass:"header-content"},[a("div",{staticClass:"header-icon renew-icon"},[a("i",{staticClass:"el-icon-coin"})]),a("div",{staticClass:"header-text"},[a("h3",[e._v("用户续费")]),a("p",[e._v("为用户延长授权使用时间")])])]),a("el-button",{staticClass:"close-btn",attrs:{icon:"el-icon-close"},on:{click:function(t){e.renewOpen=!1}}})],1),a("div",{staticClass:"renew-dialog-content modern-content"},[a("div",{staticClass:"user-info-card modern-card-simple"},[a("div",{staticClass:"user-info-content"},[a("div",{staticClass:"user-avatar"},[a("i",{staticClass:"el-icon-user-solid"})]),a("div",{staticClass:"user-details"},[a("div",{staticClass:"user-name"},[e._v(e._s(e.renewForm.userName))]),a("div",{staticClass:"user-nickname"},[e._v(e._s(e.renewForm.nickName))])]),a("div",{staticClass:"user-status"},["1"===e.renewForm.authStatus?a("el-tag",{staticClass:"status-tag",attrs:{type:"success",effect:"dark"}},[a("i",{staticClass:"el-icon-check"}),e._v(" 正常 ")]):"2"===e.renewForm.authStatus?a("el-tag",{staticClass:"status-tag",attrs:{type:"danger",effect:"dark"}},[a("i",{staticClass:"el-icon-warning"}),e._v(" 已过期 ")]):a("el-tag",{staticClass:"status-tag",attrs:{type:"warning",effect:"dark"}},[a("i",{staticClass:"el-icon-time"}),e._v(" 未授权 ")])],1)])]),e.renewForm.authEndTime?a("div",{staticClass:"auth-info-card"},[a("div",{staticClass:"card-header"},[a("i",{staticClass:"el-icon-time"}),a("span",[e._v("当前授权信息")])]),a("div",{staticClass:"auth-time-info"},[a("div",{staticClass:"time-item"},[a("span",{staticClass:"label"},[e._v("到期时间：")]),a("span",{staticClass:"value"},[e._v(e._s(e.parseTime(e.renewForm.authEndTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]),a("div",{staticClass:"time-remaining"},[a("span",{staticClass:"label"},[e._v("剩余时间：")]),a("span",{staticClass:"value",class:e.getRemainingTimeClass(e.renewForm.authEndTime)},[e._v(" "+e._s(e.getRemainingTime(e.renewForm.authEndTime))+" ")])])])]):e._e(),a("div",{staticClass:"renew-options-card modern-card"},[a("div",{staticClass:"card-header"},[a("i",{staticClass:"el-icon-coin"}),a("span",[e._v("选择续费时长")])]),a("el-form",{ref:"renewForm",attrs:{model:e.renewForm,rules:e.renewRules,"label-width":"0"}},[a("el-form-item",{attrs:{prop:"durationType"}},[a("div",{staticClass:"duration-options modern-options"},e._l(e.durationOptions,(function(t){return a("div",{key:t.value,staticClass:"duration-option modern-option",class:{selected:e.renewForm.durationType===t.value},on:{click:function(a){e.renewForm.durationType=t.value}}},[a("div",{staticClass:"option-content"},[a("span",{staticClass:"option-title"},[e._v(e._s(t.label))])]),e.renewForm.durationType===t.value?a("div",{staticClass:"option-check"},[a("i",{staticClass:"el-icon-check"})]):e._e()])})),0)])],1)],1),e.renewForm.durationType?a("div",{staticClass:"preview-card modern-card"},[a("div",{staticClass:"card-header"},[a("i",{staticClass:"el-icon-view"}),a("span",[e._v("续费后信息预览")])]),a("div",{staticClass:"preview-content"},[a("div",{staticClass:"preview-item"},[a("div",{staticClass:"preview-icon"},[a("i",{staticClass:"el-icon-time"})]),a("div",{staticClass:"preview-details"},[a("div",{staticClass:"preview-label"},[e._v("新的到期时间")]),a("div",{staticClass:"preview-value highlight"},[e._v(e._s(e.getNewEndTime()))])])]),a("div",{staticClass:"preview-item"},[a("div",{staticClass:"preview-icon"},[a("i",{staticClass:"el-icon-plus"})]),a("div",{staticClass:"preview-details"},[a("div",{staticClass:"preview-label"},[e._v("续费时长")]),a("div",{staticClass:"preview-value"},[e._v(e._s(e.getDurationText(e.renewForm.durationType)))])])])])]):e._e()]),a("div",{staticClass:"dialog-footer modern-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{staticClass:"cancel-btn modern-cancel",on:{click:function(t){e.renewOpen=!1}}},[a("i",{staticClass:"el-icon-close"}),e._v(" 取消 ")]),a("el-button",{staticClass:"submit-btn modern-submit",attrs:{type:"primary",disabled:!e.renewForm.durationType},on:{click:e.submitRenewForm}},[a("i",{staticClass:"el-icon-coin"}),e._v(" 确认续费 ")])],1)]),a("el-dialog",{staticClass:"auth-time-dialog modern-dialog compact-dialog",attrs:{visible:e.authTimeOpen,width:"450px","append-to-body":"","close-on-click-modal":!1,"show-close":!1,center:!0,"custom-class":"modern-auth-dialog"},on:{"update:visible":function(t){e.authTimeOpen=t}}},[a("div",{staticClass:"dialog-header auth-header",attrs:{slot:"title"},slot:"title"},[a("div",{staticClass:"header-content"},[a("div",{staticClass:"header-icon auth-icon"},[a("i",{staticClass:"el-icon-date"})]),a("div",{staticClass:"header-text"},[a("h3",[e._v("修改授权时间")]),a("p",[e._v("灵活设置用户的授权时间范围")])])]),a("el-button",{staticClass:"close-btn",attrs:{icon:"el-icon-close"},on:{click:function(t){e.authTimeOpen=!1}}})],1),a("div",{staticClass:"auth-time-dialog-content modern-content"},[a("div",{staticClass:"user-info-card modern-card-simple"},[a("div",{staticClass:"user-info-content"},[a("div",{staticClass:"user-avatar"},[a("i",{staticClass:"el-icon-user-solid"})]),a("div",{staticClass:"user-details"},[a("div",{staticClass:"user-name"},[e._v(e._s(e.authTimeForm.userName))]),a("div",{staticClass:"user-nickname"},[e._v(e._s(e.authTimeForm.nickName))])]),a("div",{staticClass:"user-status"},["1"===e.authTimeForm.authStatus?a("el-tag",{staticClass:"status-tag",attrs:{type:"success",effect:"dark"}},[a("i",{staticClass:"el-icon-check"}),e._v(" 正常 ")]):"2"===e.authTimeForm.authStatus?a("el-tag",{staticClass:"status-tag",attrs:{type:"danger",effect:"dark"}},[a("i",{staticClass:"el-icon-warning"}),e._v(" 已过期 ")]):a("el-tag",{staticClass:"status-tag",attrs:{type:"warning",effect:"dark"}},[a("i",{staticClass:"el-icon-time"}),e._v(" 未授权 ")])],1)])]),a("div",{staticClass:"auth-time-card modern-card"},[a("div",{staticClass:"card-header"},[a("i",{staticClass:"el-icon-date"}),a("span",[e._v("授权时间设置")])]),a("el-form",{ref:"authTimeForm",staticClass:"auth-time-form",attrs:{model:e.authTimeForm,rules:e.authTimeRules,"label-width":"120px"}},[a("div",{staticClass:"time-setting-row"},[a("el-form-item",{staticClass:"time-form-item",attrs:{label:"授权开始时间",prop:"authStartTime"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"datetime",placeholder:"选择授权开始时间","value-format":"yyyy-MM-dd HH:mm:ss",format:"yyyy-MM-dd HH:mm:ss","prefix-icon":"el-icon-time"},on:{change:e.handleStartTimeChange},model:{value:e.authTimeForm.authStartTime,callback:function(t){e.$set(e.authTimeForm,"authStartTime",t)},expression:"authTimeForm.authStartTime"}})],1)],1),a("div",{staticClass:"time-setting-row"},[a("el-form-item",{staticClass:"time-form-item",attrs:{label:"授权结束时间",prop:"authEndTime"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"datetime",placeholder:"选择授权结束时间","value-format":"yyyy-MM-dd HH:mm:ss",format:"yyyy-MM-dd HH:mm:ss","prefix-icon":"el-icon-time"},model:{value:e.authTimeForm.authEndTime,callback:function(t){e.$set(e.authTimeForm,"authEndTime",t)},expression:"authTimeForm.authEndTime"}})],1)],1)])],1),a("div",{staticClass:"quick-setting-card modern-card"},[a("div",{staticClass:"card-header"},[a("i",{staticClass:"el-icon-magic-stick"}),a("span",[e._v("快速设置")])]),a("el-form",{ref:"authTimeForm2",attrs:{model:e.authTimeForm,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"授权时长",prop:"durationType"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择授权时长"},on:{change:e.handleQuickDurationChange},model:{value:e.authTimeForm.durationType,callback:function(t){e.$set(e.authTimeForm,"durationType",t)},expression:"authTimeForm.durationType"}},[a("el-option",{attrs:{label:"自定义时间",value:"CUSTOM"}},[a("span",{staticStyle:{float:"left"}},[e._v("自定义时间")]),a("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v("手动设置")])]),a("el-option",{attrs:{label:"1小时",value:"HOUR"}},[a("span",{staticStyle:{float:"left"}},[e._v("1小时")]),a("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v("60分钟")])]),a("el-option",{attrs:{label:"8小时",value:"EIGHT_HOURS"}},[a("span",{staticStyle:{float:"left"}},[e._v("8小时")]),a("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v("工作日")])]),a("el-option",{attrs:{label:"一天",value:"DAY"}},[a("span",{staticStyle:{float:"left"}},[e._v("一天")]),a("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v("24小时")])]),a("el-option",{attrs:{label:"一个月",value:"MONTH"}},[a("span",{staticStyle:{float:"left"}},[e._v("一个月")]),a("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v("30天")])]),a("el-option",{attrs:{label:"一个季度",value:"QUARTER"}},[a("span",{staticStyle:{float:"left"}},[e._v("一个季度")]),a("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v("90天")])]),a("el-option",{attrs:{label:"一年",value:"YEAR"}},[a("span",{staticStyle:{float:"left"}},[e._v("一年")]),a("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v("365天")])])],1)],1)],1)],1),e.authTimeForm.authStartTime&&e.authTimeForm.authEndTime?a("div",{staticClass:"time-preview-card modern-card"},[a("div",{staticClass:"card-header"},[a("i",{staticClass:"el-icon-view"}),a("span",[e._v("授权时间预览")])]),a("div",{staticClass:"preview-content"},[a("div",{staticClass:"time-range"},[a("div",{staticClass:"time-point start"},[a("div",{staticClass:"time-label"},[e._v("开始时间")]),a("div",{staticClass:"time-value"},[e._v(e._s(e.parseTime(e.authTimeForm.authStartTime,"{y}-{m}-{d} {h}:{i}")))])]),a("div",{staticClass:"time-arrow"},[a("i",{staticClass:"el-icon-right"})]),a("div",{staticClass:"time-point end"},[a("div",{staticClass:"time-label"},[e._v("结束时间")]),a("div",{staticClass:"time-value"},[e._v(e._s(e.parseTime(e.authTimeForm.authEndTime,"{y}-{m}-{d} {h}:{i}")))])])]),a("div",{staticClass:"duration-info"},[a("span",{staticClass:"duration-label"},[e._v("授权时长：")]),a("span",{staticClass:"duration-value"},[e._v(e._s(e.calculateDuration(e.authTimeForm.authStartTime,e.authTimeForm.authEndTime)))])])])]):e._e()]),a("div",{staticClass:"dialog-footer modern-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{staticClass:"cancel-btn modern-cancel",on:{click:function(t){e.authTimeOpen=!1}}},[a("i",{staticClass:"el-icon-close"}),e._v(" 取消 ")]),a("el-button",{staticClass:"submit-btn modern-submit",attrs:{type:"primary"},on:{click:e.submitAuthTimeForm}},[a("i",{staticClass:"el-icon-date"}),e._v(" 确认修改 ")])],1)])],1)},i=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"page-header"},[a("div",{staticClass:"header-content"},[a("div",{staticClass:"header-icon"},[a("i",{staticClass:"el-icon-user-solid"})]),a("div",{staticClass:"header-text"},[a("h2",[e._v("用户管理")]),a("p",[e._v("管理系统用户账户和权限")])])])])}],r=a("5530"),n=(a("d9e2"),a("99af"),a("4de4"),a("d81d"),a("14d9"),a("e9c4"),a("b64b"),a("d3b7"),a("ac1f"),a("00b4"),a("4d90"),a("0643"),a("2382"),a("a573"),a("c0c7")),o=a("5f87"),l=a("ca17"),c=a.n(l),u=(a("542c"),{name:"User",dicts:["sys_normal_disable","sys_user_sex"],components:{Treeselect:c.a},data:function(){var e=this;return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,userList:null,title:"",deptOptions:void 0,enabledDeptOptions:void 0,open:!1,deptName:void 0,initPassword:void 0,dateRange:[],postOptions:[],roleOptions:[],form:{},defaultProps:{children:"children",label:"label"},upload:{open:!1,title:"",isUploading:!1,updateSupport:0,headers:{Authorization:"Bearer "+Object(o["b"])()},url:"/prod-api/system/user/importData"},queryParams:{pageNum:1,pageSize:10,userName:void 0,phonenumber:void 0,status:void 0,deptId:void 0},columns:[{key:0,label:"用户编号",visible:!0},{key:1,label:"用户名称",visible:!0},{key:2,label:"用户昵称",visible:!0},{key:3,label:"部门",visible:!0},{key:4,label:"手机号码",visible:!0},{key:5,label:"状态",visible:!0},{key:6,label:"授权状态",visible:!0},{key:7,label:"授权到期时间",visible:!0},{key:8,label:"创建时间",visible:!0}],renewOpen:!1,renewForm:{},renewRules:{durationType:[{required:!0,message:"请选择续费时长",trigger:"change"}]},durationOptions:[{value:"DAY",label:"一天",desc:"24小时授权",price:"￥10",icon:"el-icon-time"},{value:"MONTH",label:"一个月",desc:"30天授权",price:"￥100",icon:"el-icon-date"},{value:"QUARTER",label:"一个季度",desc:"90天授权",price:"￥250",icon:"el-icon-calendar"},{value:"YEAR",label:"一年",desc:"365天授权",price:"￥800",icon:"el-icon-trophy"}],authTimeOpen:!1,authTimeForm:{},authTimeRules:{authStartTime:[{required:!0,message:"请选择授权开始时间",trigger:"change"}],authEndTime:[{required:!0,message:"请选择授权结束时间",trigger:"change"}],durationType:[{required:!0,message:"请选择授权时长类型",trigger:"change"}]},rules:{userName:[{required:!0,message:"用户名称不能为空",trigger:"blur"},{min:2,max:20,message:"用户名称长度必须介于 2 和 20 之间",trigger:"blur"}],nickName:[{required:!0,message:"用户昵称不能为空",trigger:"blur"}],password:[{required:!0,message:"用户密码不能为空",trigger:"blur"},{min:5,max:20,message:"用户密码长度必须介于 5 和 20 之间",trigger:"blur"},{pattern:/^[^<>"'|\\]+$/,message:"不能包含非法字符：< > \" ' \\ |",trigger:"blur"}],confirmPassword:[{required:!0,message:"请再次输入密码",trigger:"blur"},{validator:function(t,a,s){a!==e.form.password?s(new Error("两次输入的密码不一致")):s()},trigger:"blur"}],email:[{type:"email",message:"请输入正确的邮箱地址",trigger:["blur","change"]}],phonenumber:[{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"请输入正确的手机号码",trigger:"blur"}]}}},watch:{deptName:function(e){}},created:function(){var e=this;this.getList(),this.getDeptTree(),this.getConfigKey("sys.user.initPassword").then((function(t){e.initPassword=t.msg}))},methods:{getList:function(){var e=this;this.loading=!0,Object(n["listUser"])(this.addDateRange(this.queryParams,this.dateRange)).then((function(t){console.log("用户列表数据:",t.rows),e.userList=t.rows,e.total=t.total,e.loading=!1})).catch((function(){e.loading=!1}))},getDeptTree:function(){var e=this;Object(n["deptTreeSelect"])().then((function(t){e.deptOptions=t.data,e.enabledDeptOptions=e.filterDisabledDept(JSON.parse(JSON.stringify(t.data)))}))},filterDisabledDept:function(e){var t=this;return e.filter((function(e){return!e.disabled&&(e.children&&e.children.length&&(e.children=t.filterDisabledDept(e.children)),!0)}))},filterNode:function(e,t){return!e||-1!==t.label.indexOf(e)},handleNodeClick:function(e){this.queryParams.deptId=e.id,this.handleQuery()},handleStatusChange:function(e){var t=this,a="0"===e.status?"启用":"停用";this.$modal.confirm('确认要"'+a+'""'+e.userName+'"用户吗？').then((function(){return Object(n["changeUserStatus"])(e.userId,e.status)})).then((function(){t.$modal.msgSuccess(a+"成功")})).catch((function(){e.status="0"===e.status?"1":"0"}))},handleStatisticsChange:function(e){var t=this,a="1"===e.showStatistics?"开启":"关闭";this.$modal.confirm('确认要"'+a+'""'+e.userName+'"的统计权限吗？').then((function(){return Object(n["changeUserStatistics"])(e.userId,e.showStatistics)})).then((function(){t.$modal.msgSuccess(a+"成功")})).catch((function(){e.showStatistics="1"===e.showStatistics?"0":"1"}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={userId:void 0,deptId:void 0,userName:void 0,nickName:void 0,password:void 0,phonenumber:void 0,email:void 0,sex:void 0,status:"0",remark:void 0,postIds:[],roleIds:[],authDurationType:void 0,authStartTime:void 0,authEndTime:void 0,authStatus:"1"},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.dateRange=[],this.resetForm("queryForm"),this.queryParams.deptId=void 0,this.$refs.tree.setCurrentKey(null),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.userId})),this.single=1!=e.length,this.multiple=!e.length},handleCommand:function(e,t){switch(e){case"handleResetPwd":this.handleResetPwd(t);break;case"handleAuthRole":this.handleAuthRole(t);break;case"handleRenewAuth":this.handleRenewAuth(t);break;case"handleUpdateAuthTime":this.handleUpdateAuthTime(t);break;default:break}},handleAdd:function(){var e=this;this.reset(),Object(n["getUser"])().then((function(t){e.postOptions=t.posts,e.roleOptions=t.roles,e.open=!0,e.title="添加用户",e.form.password=e.initPassword}))},handleUpdate:function(e){var t=this;this.reset();var a=e.userId||this.ids;Object(n["getUser"])(a).then((function(e){t.form=e.data,t.postOptions=e.posts,t.roleOptions=e.roles,t.$set(t.form,"postIds",e.postIds),t.$set(t.form,"roleIds",e.roleIds),t.open=!0,t.title="修改用户",t.form.password=""}))},handleResetPwd:function(e){var t=this;this.$prompt('请输入"'+e.userName+'"的新密码',"提示",{confirmButtonText:"确定",cancelButtonText:"取消",closeOnClickModal:!1,inputPattern:/^.{5,20}$/,inputErrorMessage:"用户密码长度必须介于 5 和 20 之间",inputValidator:function(e){if(/<|>|"|'|\||\\/.test(e))return"不能包含非法字符：< > \" ' \\ |"}}).then((function(a){var s=a.value;Object(n["resetUserPwd"])(e.userId,s).then((function(e){t.$modal.msgSuccess("修改成功，新密码是："+s)}))})).catch((function(){}))},handleAuthRole:function(e){var t=e.userId;this.$router.push("/system/user-auth/role/"+t)},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(void 0!=e.form.userId?Object(n["updateUser"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(n["addUser"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.userId||this.ids;this.$modal.confirm('是否确认删除用户编号为"'+a+'"的数据项？').then((function(){return Object(n["delUser"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/user/export",Object(r["a"])({},this.queryParams),"user_".concat((new Date).getTime(),".xlsx"))},handleImport:function(){this.upload.title="用户导入",this.upload.open=!0},importTemplate:function(){this.download("system/user/importTemplate",{},"user_template_".concat((new Date).getTime(),".xlsx"))},handleFileUploadProgress:function(e,t,a){this.upload.isUploading=!0},handleFileSuccess:function(e,t,a){this.upload.open=!1,this.upload.isUploading=!1,this.$refs.upload.clearFiles(),this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>"+e.msg+"</div>","导入结果",{dangerouslyUseHTMLString:!0}),this.getList()},submitFileForm:function(){this.$refs.upload.submit()},handleRenewAuth:function(e){this.renewForm={userId:e.userId,userName:e.userName,nickName:e.nickName,authStatus:e.authStatus,authEndTime:e.authEndTime,durationType:"YEAR"},this.renewOpen=!0},submitRenewForm:function(){var e=this;this.$refs.renewForm.validate((function(t){t&&e.$modal.confirm('确认要为用户"'+e.renewForm.userName+'"续费'+e.getDurationText(e.renewForm.durationType)+"吗？").then((function(){return e.renewUserAuthApi(e.renewForm.userId,e.renewForm.durationType)})).then((function(){e.$modal.msgSuccess("续费成功"),e.renewOpen=!1,e.getList()})).catch((function(){}))}))},renewUserAuthApi:function(e,t){return Object(n["renewUserAuth"])({userId:e,durationType:t})},getDurationText:function(e){switch(e){case"DAY":return"一天";case"MONTH":return"一个月";case"QUARTER":return"一个季度";case"YEAR":return"一年";default:return""}},handleAuthDurationChange:function(e){if(e&&void 0===this.form.userId){var t=new Date,a=new Date(t);switch(e){case"DAY":a.setDate(a.getDate()+1);break;case"MONTH":a.setMonth(a.getMonth()+1);break;case"QUARTER":a.setMonth(a.getMonth()+3);break;case"YEAR":a.setFullYear(a.getFullYear()+1);break}this.form.authStartTime=t,this.form.authEndTime=a,this.form.authStatus="1"}},handleUpdateAuthTime:function(e){this.authTimeForm={userId:e.userId,userName:e.userName,nickName:e.nickName,authStatus:e.authStatus,authStartTime:e.authStartTime,authEndTime:e.authEndTime,durationType:e.authDurationType||"MONTH"},this.authTimeOpen=!0},handleDurationTypeChange:function(e){if(e&&this.authTimeForm.authStartTime&&"CUSTOM"!==e){var t=new Date(this.authTimeForm.authStartTime),a=new Date(t);switch(e){case"HOUR":a.setHours(t.getHours()+1);break;case"EIGHT_HOURS":a.setHours(t.getHours()+8);break;case"DAY":a.setDate(t.getDate()+1),a.setHours(23,59,59,999);break;case"MONTH":a.setMonth(t.getMonth()+1),a.setHours(23,59,59,999);break;case"QUARTER":a.setMonth(t.getMonth()+3),a.setHours(23,59,59,999);break;case"YEAR":a.setFullYear(t.getFullYear()+1),a.setHours(23,59,59,999);break;default:return}this.authTimeForm.authEndTime=this.formatDateTime(a)}},handleStartTimeChange:function(){this.authTimeForm.durationType&&this.authTimeForm.authStartTime&&this.handleDurationTypeChange(this.authTimeForm.durationType)},submitAuthTimeForm:function(){var e=this;this.$refs.authTimeForm.validate((function(t){if(t){if(e.authTimeForm.authStartTime&&e.authTimeForm.authEndTime){var a=new Date(e.authTimeForm.authStartTime),s=new Date(e.authTimeForm.authEndTime);if(a>=s)return void e.$modal.msgError("授权开始时间不能晚于或等于结束时间")}e.$modal.confirm('确认要修改用户"'+e.authTimeForm.userName+'"的授权时间吗？').then((function(){var t={userId:e.authTimeForm.userId,authStartTime:e.authTimeForm.authStartTime,authEndTime:e.authTimeForm.authEndTime,durationType:e.authTimeForm.durationType};return console.log("发送的数据:",t),Object(n["updateUserAuthTime"])(t)})).then((function(t){console.log("修改授权时间成功响应:",t),e.$modal.msgSuccess("授权时间修改成功"),e.authTimeOpen=!1,e.getList()})).catch((function(e){console.error("修改授权时间失败:",e)}))}}))},getRemainingTime:function(e){if(!e)return"未设置";var t=new Date,a=new Date(e),s=a-t;if(s<=0)return"已过期";var i=Math.floor(s/864e5),r=Math.floor(s%864e5/36e5);return i>0?"".concat(i,"天").concat(r,"小时"):"".concat(r,"小时")},getRemainingTimeClass:function(e){if(!e)return"";var t=new Date,a=new Date(e),s=a-t,i=Math.floor(s/864e5);return s<=0?"expired":i<=3?"warning":i<=7?"caution":"normal"},getNewEndTime:function(){if(!this.renewForm.durationType)return"";var e=this.renewForm.authEndTime?new Date(this.renewForm.authEndTime):new Date,t=new Date(e);switch(this.renewForm.durationType){case"DAY":t.setDate(e.getDate()+1);break;case"MONTH":t.setMonth(e.getMonth()+1);break;case"QUARTER":t.setMonth(e.getMonth()+3);break;case"YEAR":t.setFullYear(e.getFullYear()+1);break}return this.parseTime(t,"{y}-{m}-{d} {h}:{i}:{s}")},handleQuickDurationChange:function(e){e&&this.authTimeForm.authStartTime&&("CUSTOM"!==e?this.handleDurationTypeChange(e):this.$message.info("请手动设置授权结束时间，可以设置任意时间间隔"))},calculateDuration:function(e,t){if(!e||!t)return"";var a=new Date(e),s=new Date(t),i=s-a;if(i<=0)return"时间设置错误";var r=Math.floor(i/864e5),n=Math.floor(i%864e5/36e5);return r>0?"".concat(r,"天").concat(n>0?n+"小时":""):"".concat(n,"小时")},formatDateTime:function(e){if(!e)return null;var t=new Date(e),a=t.getFullYear(),s=String(t.getMonth()+1).padStart(2,"0"),i=String(t.getDate()).padStart(2,"0"),r=String(t.getHours()).padStart(2,"0"),n=String(t.getMinutes()).padStart(2,"0"),o=String(t.getSeconds()).padStart(2,"0");return"".concat(a,"-").concat(s,"-").concat(i," ").concat(r,":").concat(n,":").concat(o)}}}),d=u,m=(a("4ea7"),a("0832"),a("2877")),p=Object(m["a"])(d,s,i,!1,null,"052aa7c6",null);t["default"]=p.exports},4651:function(e,t,a){},"4d90":function(e,t,a){"use strict";var s=a("23e7"),i=a("0ccb").start,r=a("9a0c");s({target:"String",proto:!0,forced:r},{padStart:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}})},"4ea7":function(e,t,a){"use strict";a("1df0")},"9a0c":function(e,t,a){"use strict";var s=a("342f");e.exports=/Version\/10(?:\.\d+){1,2}(?: [\w./]+)?(?: Mobile\/\w+)? Safari\//.test(s)}}]);