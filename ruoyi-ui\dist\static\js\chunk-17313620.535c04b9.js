(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-17313620"],{2758:function(t,s,e){"use strict";e.r(s);var a=function(){var t=this,s=t.$createElement,e=t._self._c||s;return e("div",{staticClass:"app-container"},[e("el-row",{staticClass:"mb20",attrs:{gutter:20}},[e("el-col",{attrs:{span:6}},[e("div",{staticClass:"stat-card pending"},[e("div",{staticClass:"stat-icon"},[e("i",{staticClass:"el-icon-clock"})]),e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-number"},[t._v(t._s(t.statistics.pending))]),e("div",{staticClass:"stat-label"},[t._v("待处理")])])])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"stat-card processing"},[e("div",{staticClass:"stat-icon"},[e("i",{staticClass:"el-icon-loading"})]),e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-number"},[t._v(t._s(t.statistics.processing))]),e("div",{staticClass:"stat-label"},[t._v("处理中")])])])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"stat-card resolved"},[e("div",{staticClass:"stat-icon"},[e("i",{staticClass:"el-icon-check"})]),e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-number"},[t._v(t._s(t.statistics.resolved))]),e("div",{staticClass:"stat-label"},[t._v("已解决")])])])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"stat-card closed"},[e("div",{staticClass:"stat-icon"},[e("i",{staticClass:"el-icon-close"})]),e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-number"},[t._v(t._s(t.statistics.closed))]),e("div",{staticClass:"stat-label"},[t._v("已关闭")])])])])],1),e("div",{staticClass:"search-container"},[e("el-form",{directives:[{name:"show",rawName:"v-show",value:t.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:t.queryParams,size:"small",inline:!0,"label-width":"68px"}},[e("el-form-item",{attrs:{label:"工单编号",prop:"ticketNumber"}},[e("el-input",{attrs:{placeholder:"请输入工单编号",clearable:""},nativeOn:{keyup:function(s){return!s.type.indexOf("key")&&t._k(s.keyCode,"enter",13,s.key,"Enter")?null:t.handleQuery(s)}},model:{value:t.queryParams.ticketNumber,callback:function(s){t.$set(t.queryParams,"ticketNumber",s)},expression:"queryParams.ticketNumber"}})],1),e("el-form-item",{attrs:{label:"工单标题",prop:"title"}},[e("el-input",{attrs:{placeholder:"请输入工单标题",clearable:""},nativeOn:{keyup:function(s){return!s.type.indexOf("key")&&t._k(s.keyCode,"enter",13,s.key,"Enter")?null:t.handleQuery(s)}},model:{value:t.queryParams.title,callback:function(s){t.$set(t.queryParams,"title",s)},expression:"queryParams.title"}})],1),e("el-form-item",{attrs:{label:"提交用户",prop:"userName"}},[e("el-input",{attrs:{placeholder:"请输入用户名",clearable:""},nativeOn:{keyup:function(s){return!s.type.indexOf("key")&&t._k(s.keyCode,"enter",13,s.key,"Enter")?null:t.handleQuery(s)}},model:{value:t.queryParams.userName,callback:function(s){t.$set(t.queryParams,"userName",s)},expression:"queryParams.userName"}})],1),e("el-form-item",{attrs:{label:"工单状态",prop:"status"}},[e("el-select",{attrs:{placeholder:"请选择工单状态",clearable:""},model:{value:t.queryParams.status,callback:function(s){t.$set(t.queryParams,"status",s)},expression:"queryParams.status"}},[e("el-option",{attrs:{label:"待处理",value:"0"}}),e("el-option",{attrs:{label:"处理中",value:"1"}}),e("el-option",{attrs:{label:"已解决",value:"2"}}),e("el-option",{attrs:{label:"已关闭",value:"3"}})],1)],1),e("el-form-item",{attrs:{label:"优先级",prop:"priority"}},[e("el-select",{attrs:{placeholder:"请选择优先级",clearable:""},model:{value:t.queryParams.priority,callback:function(s){t.$set(t.queryParams,"priority",s)},expression:"queryParams.priority"}},[e("el-option",{attrs:{label:"低",value:"1"}}),e("el-option",{attrs:{label:"中",value:"2"}}),e("el-option",{attrs:{label:"高",value:"3"}}),e("el-option",{attrs:{label:"紧急",value:"4"}})],1)],1),e("el-form-item",[e("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:t.handleQuery}},[t._v("搜索")]),e("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:t.resetQuery}},[t._v("重置")])],1)],1)],1),e("div",{staticClass:"toolbar-container"},[e("el-row",{staticClass:"mb8",attrs:{gutter:10}},[e("el-col",{attrs:{span:1.5}},[e("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:ticket:remove"],expression:"['system:ticket:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:t.multiple},on:{click:t.handleDelete}},[t._v("删除")])],1),e("el-col",{attrs:{span:1.5}},[e("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:ticket:export"],expression:"['system:ticket:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:t.handleExport}},[t._v("导出")])],1),e("right-toolbar",{attrs:{showSearch:t.showSearch},on:{"update:showSearch":function(s){t.showSearch=s},"update:show-search":function(s){t.showSearch=s},queryTable:t.getList}})],1)],1),e("div",{staticClass:"table-container"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"modern-table",attrs:{data:t.ticketList,"row-class-name":t.getRowClassName},on:{"selection-change":t.handleSelectionChange}},[e("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),e("el-table-column",{attrs:{label:"工单编号",align:"center",prop:"ticketNumber",width:"180"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",{staticClass:"ticket-number"},[e("i",{staticClass:"el-icon-tickets"}),e("span",{staticClass:"number-text"},[t._v(t._s(s.row.ticketNumber))])])]}}])}),e("el-table-column",{attrs:{label:"工单信息",align:"left","min-width":"200"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",{staticClass:"ticket-info"},[e("div",{staticClass:"ticket-title"},[t._v(t._s(s.row.title))]),e("div",{staticClass:"ticket-meta"},[e("span",{staticClass:"user-info"},[e("i",{staticClass:"el-icon-user"}),t._v(" "+t._s(s.row.userName)+" ")]),e("span",{staticClass:"category-info"},[e("i",{staticClass:"el-icon-folder"}),t._v(" "+t._s(t.getCategoryName(s.row.category))+" ")])])])]}}])}),e("el-table-column",{attrs:{label:"状态",align:"center",prop:"status",width:"100"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",{staticClass:"status-wrapper"},["0"===s.row.status?e("el-tag",{staticClass:"status-tag pending-tag",attrs:{type:"info",effect:"dark"}},[e("i",{staticClass:"el-icon-clock"}),t._v(" 待处理 ")]):"1"===s.row.status?e("el-tag",{staticClass:"status-tag processing-tag",attrs:{type:"warning",effect:"dark"}},[e("i",{staticClass:"el-icon-loading"}),t._v(" 处理中 ")]):"2"===s.row.status?e("el-tag",{staticClass:"status-tag resolved-tag",attrs:{type:"success",effect:"dark"}},[e("i",{staticClass:"el-icon-check"}),t._v(" 已解决 ")]):"3"===s.row.status?e("el-tag",{staticClass:"status-tag closed-tag",attrs:{type:"danger",effect:"dark"}},[e("i",{staticClass:"el-icon-close"}),t._v(" 已关闭 ")]):t._e()],1)]}}])}),e("el-table-column",{attrs:{label:"优先级",align:"center",prop:"priority",width:"90"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",{staticClass:"priority-wrapper"},["1"===s.row.priority?e("el-tag",{staticClass:"priority-tag low",attrs:{type:"info",size:"small"}},[e("i",{staticClass:"el-icon-bottom"}),t._v(" 低 ")]):"2"===s.row.priority?e("el-tag",{staticClass:"priority-tag medium",attrs:{type:"warning",size:"small"}},[e("i",{staticClass:"el-icon-minus"}),t._v(" 中 ")]):"3"===s.row.priority?e("el-tag",{staticClass:"priority-tag high",attrs:{type:"danger",size:"small"}},[e("i",{staticClass:"el-icon-top"}),t._v(" 高 ")]):"4"===s.row.priority?e("el-tag",{staticClass:"priority-tag urgent",attrs:{type:"danger",size:"small"}},[e("i",{staticClass:"el-icon-warning"}),t._v(" 紧急 ")]):t._e()],1)]}}])}),e("el-table-column",{attrs:{label:"分配管理员",align:"center",prop:"assignedName",width:"130"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",{staticClass:"assigned-info"},[s.row.assignedName?e("div",{staticClass:"assigned-user"},[e("el-avatar",{staticClass:"user-avatar",attrs:{size:24}},[e("i",{staticClass:"el-icon-user-solid"})]),e("span",{staticClass:"user-name"},[t._v(t._s(s.row.assignedName))])],1):e("div",{staticClass:"unassigned"},[e("i",{staticClass:"el-icon-user"}),e("span",[t._v("未分配")])])])]}}])}),e("el-table-column",{attrs:{label:"时间信息",align:"center",width:"140"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",{staticClass:"time-info"},[e("div",{staticClass:"create-time"},[e("i",{staticClass:"el-icon-time"}),t._v(" "+t._s(t.parseTime(s.row.createTime,"{m}-{d} {h}:{i}"))+" ")]),s.row.closeTime?e("div",{staticClass:"close-time"},[e("i",{staticClass:"el-icon-circle-close"}),t._v(" "+t._s(t.parseTime(s.row.closeTime,"{m}-{d} {h}:{i}"))+" ")]):t._e()])]}}])}),e("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"260"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("div",{staticClass:"action-buttons"},[e("el-tooltip",{attrs:{content:"查看详情",placement:"top"}},[e("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:ticket:query"],expression:"['system:ticket:query']"}],staticClass:"action-btn view-btn",attrs:{size:"mini",type:"primary",icon:"el-icon-view"},on:{click:function(e){return t.handleView(s.row)}}},[t._v(" 查看 ")])],1),"3"!==s.row.status?e("el-tooltip",{attrs:{content:"回复工单",placement:"top"}},[e("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:ticket:edit"],expression:"['system:ticket:edit']"}],staticClass:"action-btn reply-btn",attrs:{size:"mini",type:"success",icon:"el-icon-chat-line-round"},on:{click:function(e){return t.handleReply(s.row)}}},[t._v(" 回复 ")])],1):t._e(),"0"===s.row.status?e("el-tooltip",{attrs:{content:"分配工单",placement:"top"}},[e("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:ticket:edit"],expression:"['system:ticket:edit']"}],staticClass:"action-btn assign-btn",attrs:{size:"mini",type:"warning",icon:"el-icon-user"},on:{click:function(e){return t.handleAssign(s.row)}}},[t._v(" 分配 ")])],1):t._e(),"3"!==s.row.status?e("el-tooltip",{attrs:{content:"关闭工单",placement:"top"}},[e("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:ticket:edit"],expression:"['system:ticket:edit']"}],staticClass:"action-btn close-btn",attrs:{size:"mini",type:"danger",icon:"el-icon-circle-close"},on:{click:function(e){return t.handleClose(s.row)}}},[t._v(" 关闭 ")])],1):t._e(),e("el-tooltip",{attrs:{content:"删除工单",placement:"top"}},[e("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:ticket:remove"],expression:"['system:ticket:remove']"}],staticClass:"action-btn delete-btn",attrs:{size:"mini",type:"danger",icon:"el-icon-delete"},on:{click:function(e){return t.handleDelete(s.row)}}},[t._v(" 删除 ")])],1)],1)]}}])})],1)],1),e("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total>0"}],attrs:{total:t.total,page:t.queryParams.pageNum,limit:t.queryParams.pageSize},on:{"update:page":function(s){return t.$set(t.queryParams,"pageNum",s)},"update:limit":function(s){return t.$set(t.queryParams,"pageSize",s)},pagination:t.getList}}),e("el-dialog",{staticClass:"view-dialog",attrs:{visible:t.viewOpen,width:"900px","append-to-body":"","close-on-click-modal":!1,"show-close":!1,title:null},on:{"update:visible":function(s){t.viewOpen=s}}},[e("div",{staticClass:"dialog-header"},[e("div",{staticClass:"header-icon"},[e("i",{staticClass:"el-icon-view"})]),e("div",{staticClass:"header-text"},[e("div",{staticClass:"header-title"},[t._v("工单详情")]),e("div",{staticClass:"header-desc"},[t._v("查看工单详细信息和沟通记录")])]),e("div",{staticClass:"header-close",on:{click:function(s){t.viewOpen=!1}}},[e("i",{staticClass:"el-icon-close"})])]),e("div",{staticClass:"view-dialog-content"},[e("div",{staticClass:"ticket-header"},[e("div",{staticClass:"ticket-number-badge"},[e("i",{staticClass:"el-icon-tickets"}),e("span",[t._v(t._s(t.viewForm.ticketNumber))])]),e("div",{staticClass:"ticket-status-badge"},["0"===t.viewForm.status?e("el-tag",{staticClass:"status-tag",attrs:{type:"info",effect:"dark"}},[e("i",{staticClass:"el-icon-clock"}),t._v(" 待处理 ")]):"1"===t.viewForm.status?e("el-tag",{staticClass:"status-tag",attrs:{type:"warning",effect:"dark"}},[e("i",{staticClass:"el-icon-loading"}),t._v(" 处理中 ")]):"2"===t.viewForm.status?e("el-tag",{staticClass:"status-tag",attrs:{type:"success",effect:"dark"}},[e("i",{staticClass:"el-icon-check"}),t._v(" 已解决 ")]):"3"===t.viewForm.status?e("el-tag",{staticClass:"status-tag",attrs:{type:"danger",effect:"dark"}},[e("i",{staticClass:"el-icon-close"}),t._v(" 已关闭 ")]):t._e()],1)]),e("div",{staticClass:"ticket-basic-info"},[e("div",{staticClass:"info-card"},[e("div",{staticClass:"card-header"},[e("i",{staticClass:"el-icon-document"}),e("span",[t._v("工单信息")])]),e("div",{staticClass:"card-content"},[e("div",{staticClass:"info-row"},[e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v("工单标题：")]),e("span",{staticClass:"value title"},[t._v(t._s(t.viewForm.title))])])]),e("div",{staticClass:"info-row"},[e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v("提交用户：")]),e("span",{staticClass:"value"},[t._v(t._s(t.viewForm.userName))])]),e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v("优先级：")]),"1"===t.viewForm.priority?e("el-tag",{staticClass:"priority-tag",attrs:{type:"info",size:"small"}},[e("i",{staticClass:"el-icon-bottom"}),t._v(" 低优先级 ")]):"2"===t.viewForm.priority?e("el-tag",{staticClass:"priority-tag",attrs:{type:"warning",size:"small"}},[e("i",{staticClass:"el-icon-minus"}),t._v(" 中优先级 ")]):"3"===t.viewForm.priority?e("el-tag",{staticClass:"priority-tag",attrs:{type:"danger",size:"small"}},[e("i",{staticClass:"el-icon-top"}),t._v(" 高优先级 ")]):"4"===t.viewForm.priority?e("el-tag",{staticClass:"priority-tag urgent",attrs:{type:"danger",size:"small"}},[e("i",{staticClass:"el-icon-warning"}),t._v(" 紧急 ")]):t._e()],1)]),e("div",{staticClass:"info-row"},[e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v("分配管理员：")]),e("span",{staticClass:"value"},[t._v(t._s(t.viewForm.assignedName||"未分配"))])]),e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v("创建时间：")]),e("span",{staticClass:"value"},[t._v(t._s(t.parseTime(t.viewForm.createTime,"{y}-{m}-{d} {h}:{i}:{s}")))])])]),t.viewForm.closeTime?e("div",{staticClass:"info-row"},[e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v("关闭时间：")]),e("span",{staticClass:"value"},[t._v(t._s(t.parseTime(t.viewForm.closeTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]),t.viewForm.closeReason?e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v("关闭原因：")]),e("span",{staticClass:"value"},[t._v(t._s(t.viewForm.closeReason))])]):t._e()]):t._e()])]),e("div",{staticClass:"content-card"},[e("div",{staticClass:"card-header"},[e("i",{staticClass:"el-icon-chat-line-square"}),e("span",[t._v("问题描述")])]),e("div",{staticClass:"card-content"},[e("div",{staticClass:"content-text"},[t._v(t._s(t.viewForm.content))])])])]),t.viewForm.sysTicketReplyList&&t.viewForm.sysTicketReplyList.length>0?e("div",{staticClass:"replies-section"},[e("div",{staticClass:"section-header"},[e("i",{staticClass:"el-icon-chat-dot-round"}),e("span",[t._v("沟通记录")]),e("div",{staticClass:"reply-count"},[t._v(t._s(t.viewForm.sysTicketReplyList.length)+" 条回复")])]),e("div",{staticClass:"replies-container"},t._l(t.viewForm.sysTicketReplyList,(function(s,a){return e("div",{key:s.replyId,staticClass:"reply-item",class:{"user-reply":"0"===s.replyType,"admin-reply":"1"===s.replyType}},[e("div",{staticClass:"reply-avatar"},[e("el-avatar",{staticClass:"avatar",attrs:{size:40}},["0"===s.replyType?e("i",{staticClass:"el-icon-user"}):e("i",{staticClass:"el-icon-service"})])],1),e("div",{staticClass:"reply-content"},[e("div",{staticClass:"reply-header"},[e("div",{staticClass:"reply-user"},[e("span",{staticClass:"user-name"},[t._v(t._s(s.userName))]),"0"===s.replyType?e("el-tag",{staticClass:"user-type-tag",attrs:{type:"primary",size:"mini"}},[t._v(" 用户 ")]):e("el-tag",{staticClass:"user-type-tag",attrs:{type:"success",size:"mini"}},[t._v(" 管理员 ")])],1),e("div",{staticClass:"reply-time"},[t._v(" "+t._s(t.parseTime(s.createTime,"{y}-{m}-{d} {h}:{i}:{s}"))+" ")])]),e("div",{staticClass:"reply-text"},[t._v(t._s(s.content))])])])})),0)]):t._e()])]),e("el-dialog",{staticClass:"reply-dialog",attrs:{visible:t.replyOpen,width:"650px","append-to-body":"","close-on-click-modal":!1,"show-close":!1,title:null},on:{"update:visible":function(s){t.replyOpen=s}}},[e("div",{staticClass:"dialog-header"},[e("div",{staticClass:"header-icon"},[e("i",{staticClass:"el-icon-chat-line-round"})]),e("div",{staticClass:"header-text"},[e("div",{staticClass:"header-title"},[t._v("回复工单")]),e("div",{staticClass:"header-desc"},[t._v("添加回复内容，与用户进行沟通")])]),e("div",{staticClass:"header-close",on:{click:t.cancelReply}},[e("i",{staticClass:"el-icon-close"})])]),e("div",{staticClass:"reply-dialog-content"},[e("div",{staticClass:"ticket-preview"},[e("div",{staticClass:"preview-header"},[e("i",{staticClass:"el-icon-tickets"}),e("span",[t._v(t._s(t.replyForm.ticketNumber||"工单回复"))])]),e("div",{staticClass:"preview-content"},[e("div",{staticClass:"preview-item"},[e("span",{staticClass:"label"},[t._v("工单标题：")]),e("span",{staticClass:"value"},[t._v(t._s(t.replyForm.title))])])])]),e("div",{staticClass:"reply-form-container"},[e("div",{staticClass:"form-header"},[e("i",{staticClass:"el-icon-chat-line-round"}),e("span",[t._v("添加回复")])]),e("el-form",{ref:"replyForm",attrs:{model:t.replyForm,rules:t.replyRules,"label-width":"0"}},[e("el-form-item",{attrs:{prop:"content"}},[e("el-input",{staticClass:"reply-textarea",attrs:{type:"textarea",rows:8,placeholder:"请输入您的回复内容...&#10;&#10;提示：&#10;• 详细回复用户的问题&#10;• 提供解决方案或指导&#10;• 保持专业和友好的沟通",maxlength:"1000","show-word-limit":""},model:{value:t.replyForm.content,callback:function(s){t.$set(t.replyForm,"content",s)},expression:"replyForm.content"}})],1)],1)],1),e("div",{staticClass:"reply-tips"},[e("div",{staticClass:"tips-header"},[e("i",{staticClass:"el-icon-info"}),e("span",[t._v("回复提示")])]),e("div",{staticClass:"tips-content"},[e("div",{staticClass:"tip-item"},[e("i",{staticClass:"el-icon-check"}),e("span",[t._v("回复后，用户将收到通知")])]),e("div",{staticClass:"tip-item"},[e("i",{staticClass:"el-icon-check"}),e("span",[t._v("请提供详细的解决方案")])]),e("div",{staticClass:"tip-item"},[e("i",{staticClass:"el-icon-check"}),e("span",[t._v("保持专业和友好的沟通")])])])])]),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{staticClass:"cancel-btn",on:{click:t.cancelReply}},[e("i",{staticClass:"el-icon-close"}),t._v(" 取消 ")]),e("el-button",{staticClass:"submit-btn",attrs:{type:"primary"},on:{click:t.submitReply}},[e("i",{staticClass:"el-icon-s-promotion"}),t._v(" 发送回复 ")])],1)]),e("el-dialog",{staticClass:"assign-dialog",attrs:{visible:t.assignOpen,width:"550px","append-to-body":"","close-on-click-modal":!1,"show-close":!1,title:null},on:{"update:visible":function(s){t.assignOpen=s}}},[e("div",{staticClass:"dialog-header"},[e("div",{staticClass:"header-icon"},[e("i",{staticClass:"el-icon-user"})]),e("div",{staticClass:"header-text"},[e("div",{staticClass:"header-title"},[t._v("分配工单")]),e("div",{staticClass:"header-desc"},[t._v("将工单分配给指定的管理员处理")])]),e("div",{staticClass:"header-close",on:{click:t.cancelAssign}},[e("i",{staticClass:"el-icon-close"})])]),e("div",{staticClass:"assign-dialog-content"},[e("div",{staticClass:"ticket-info-card"},[e("div",{staticClass:"card-header"},[e("i",{staticClass:"el-icon-document"}),e("span",[t._v("工单信息")])]),e("div",{staticClass:"card-content"},[e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v("工单编号：")]),e("span",{staticClass:"value"},[t._v(t._s(t.assignForm.ticketNumber))])]),e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v("工单标题：")]),e("span",{staticClass:"value"},[t._v(t._s(t.assignForm.title))])])])]),e("div",{staticClass:"assign-form-card"},[e("div",{staticClass:"card-header"},[e("i",{staticClass:"el-icon-user-solid"}),e("span",[t._v("分配管理员")])]),e("div",{staticClass:"card-content"},[e("el-form",{ref:"assignForm",attrs:{model:t.assignForm,rules:t.assignRules,"label-width":"120px"}},[e("el-form-item",{attrs:{label:"分配给管理员",prop:"assignedName"}},[e("el-input",{attrs:{placeholder:"请输入管理员姓名","prefix-icon":"el-icon-user-solid"},model:{value:t.assignForm.assignedName,callback:function(s){t.$set(t.assignForm,"assignedName",s)},expression:"assignForm.assignedName"}})],1)],1)],1)])]),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{staticClass:"cancel-btn",on:{click:t.cancelAssign}},[e("i",{staticClass:"el-icon-close"}),t._v(" 取消 ")]),e("el-button",{staticClass:"submit-btn",attrs:{type:"primary"},on:{click:t.submitAssign}},[e("i",{staticClass:"el-icon-check"}),t._v(" 确认分配 ")])],1)]),e("el-dialog",{staticClass:"close-dialog",attrs:{visible:t.closeOpen,width:"550px","append-to-body":"","close-on-click-modal":!1,"show-close":!1,title:null},on:{"update:visible":function(s){t.closeOpen=s}}},[e("div",{staticClass:"dialog-header"},[e("div",{staticClass:"header-icon"},[e("i",{staticClass:"el-icon-circle-close"})]),e("div",{staticClass:"header-text"},[e("div",{staticClass:"header-title"},[t._v("关闭工单")]),e("div",{staticClass:"header-desc"},[t._v("请填写关闭原因，工单将被标记为已关闭")])]),e("div",{staticClass:"header-close",on:{click:t.cancelClose}},[e("i",{staticClass:"el-icon-close"})])]),e("div",{staticClass:"close-dialog-content"},[e("div",{staticClass:"ticket-info-card"},[e("div",{staticClass:"card-header"},[e("i",{staticClass:"el-icon-document"}),e("span",[t._v("工单信息")])]),e("div",{staticClass:"card-content"},[e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v("工单编号：")]),e("span",{staticClass:"value"},[t._v(t._s(t.closeForm.ticketNumber))])]),e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v("工单标题：")]),e("span",{staticClass:"value"},[t._v(t._s(t.closeForm.title))])])])]),e("div",{staticClass:"close-form-card"},[e("div",{staticClass:"card-header"},[e("i",{staticClass:"el-icon-edit-outline"}),e("span",[t._v("关闭原因")])]),e("div",{staticClass:"card-content"},[e("el-form",{ref:"closeForm",attrs:{model:t.closeForm,rules:t.closeRules,"label-width":"0"}},[e("el-form-item",{attrs:{prop:"closeReason"}},[e("el-input",{attrs:{type:"textarea",rows:6,placeholder:"请详细说明关闭工单的原因...&#10;&#10;例如：&#10;• 问题已解决&#10;• 用户确认满意&#10;• 重复工单&#10;• 其他原因",maxlength:"500","show-word-limit":""},model:{value:t.closeForm.closeReason,callback:function(s){t.$set(t.closeForm,"closeReason",s)},expression:"closeForm.closeReason"}})],1)],1)],1)]),e("div",{staticClass:"close-warning"},[e("div",{staticClass:"warning-header"},[e("i",{staticClass:"el-icon-warning"}),e("span",[t._v("注意事项")])]),e("div",{staticClass:"warning-content"},[e("div",{staticClass:"warning-item"},[e("i",{staticClass:"el-icon-info"}),e("span",[t._v("关闭后工单将无法继续回复")])]),e("div",{staticClass:"warning-item"},[e("i",{staticClass:"el-icon-info"}),e("span",[t._v("请确保问题已得到妥善解决")])])])])]),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{staticClass:"cancel-btn",on:{click:t.cancelClose}},[e("i",{staticClass:"el-icon-close"}),t._v(" 取消 ")]),e("el-button",{staticClass:"submit-btn close-btn",attrs:{type:"danger"},on:{click:t.submitClose}},[e("i",{staticClass:"el-icon-circle-close"}),t._v(" 确认关闭 ")])],1)])],1)},i=[],l=e("5530"),c=(e("4de4"),e("d81d"),e("d3b7"),e("0643"),e("2382"),e("a573"),e("58dc")),n={name:"Ticket",data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,ticketList:[],statistics:{pending:0,processing:0,resolved:0,closed:0},viewOpen:!1,replyOpen:!1,assignOpen:!1,closeOpen:!1,queryParams:{pageNum:1,pageSize:10,ticketNumber:null,title:null,userName:null,status:null,priority:null,category:null},viewForm:{},replyForm:{},assignForm:{},closeForm:{},replyRules:{content:[{required:!0,message:"回复内容不能为空",trigger:"blur"}]},assignRules:{assignedName:[{required:!0,message:"管理员姓名不能为空",trigger:"blur"}]},closeRules:{closeReason:[{required:!0,message:"关闭原因不能为空",trigger:"blur"}]}}},created:function(){this.getList()},methods:{getList:function(){var t=this;this.loading=!0,Object(c["h"])(this.queryParams).then((function(s){t.ticketList=s.rows,t.total=s.total,t.calculateStatistics(),t.loading=!1}))},calculateStatistics:function(){this.statistics={pending:this.ticketList.filter((function(t){return"0"===t.status})).length,processing:this.ticketList.filter((function(t){return"1"===t.status})).length,resolved:this.ticketList.filter((function(t){return"2"===t.status})).length,closed:this.ticketList.filter((function(t){return"3"===t.status})).length}},getCategoryName:function(t){var s={system_error:"系统错误",feature_request:"功能建议",usage_issue:"使用问题",account_issue:"账户问题",other:"其他"};return s[t]||"其他"},getRowClassName:function(t){var s=t.row;return"4"===s.priority?"urgent-row":"3"===s.priority?"high-row":"3"===s.status?"closed-row":""},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(t){this.ids=t.map((function(t){return t.ticketId})),this.single=1!==t.length,this.multiple=!t.length},handleView:function(t){var s=this,e=t.ticketId;Object(c["f"])(e).then((function(t){s.viewForm=t.data,s.viewOpen=!0}))},handleReply:function(t){this.replyForm={ticketId:t.ticketId,ticketNumber:t.ticketNumber,title:t.title,content:""},this.replyOpen=!0},handleAssign:function(t){this.assignForm={ticketId:t.ticketId,ticketNumber:t.ticketNumber,title:t.title,assignedName:""},this.assignOpen=!0},handleClose:function(t){this.closeForm={ticketId:t.ticketId,ticketNumber:t.ticketNumber,title:t.title,closeReason:""},this.closeOpen=!0},handleDelete:function(t){var s=this,e=t.ticketId||this.ids;this.$modal.confirm('是否确认删除工单编号为"'+e+'"的数据项？').then((function(){return Object(c["d"])(e)})).then((function(){s.getList(),s.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/ticket/export",Object(l["a"])({},this.queryParams),"ticket_".concat((new Date).getTime(),".xlsx"))},submitReply:function(){var t=this;this.$refs["replyForm"].validate((function(s){s&&Object(c["a"])(t.replyForm.ticketId,t.replyForm).then((function(s){t.$modal.msgSuccess("回复成功"),t.replyOpen=!1,t.getList()}))}))},cancelReply:function(){this.replyOpen=!1,this.replyForm={}},submitAssign:function(){var t=this;this.$refs["assignForm"].validate((function(s){s&&Object(c["b"])(t.assignForm.ticketId,t.assignForm).then((function(s){t.$modal.msgSuccess("分配成功"),t.assignOpen=!1,t.getList()}))}))},cancelAssign:function(){this.assignOpen=!1,this.assignForm={}},submitClose:function(){var t=this;this.$refs["closeForm"].validate((function(s){s&&Object(c["c"])(t.closeForm.ticketId,t.closeForm).then((function(s){t.$modal.msgSuccess("关闭成功"),t.closeOpen=!1,t.getList()}))}))},cancelClose:function(){this.closeOpen=!1,this.closeForm={}}}},r=n,o=(e("d2c3"),e("2877")),d=Object(o["a"])(r,a,i,!1,null,null,null);s["default"]=d.exports},"58dc":function(t,s,e){"use strict";e.d(s,"h",(function(){return i})),e.d(s,"f",(function(){return l})),e.d(s,"d",(function(){return c})),e.d(s,"c",(function(){return n})),e.d(s,"b",(function(){return r})),e.d(s,"a",(function(){return o})),e.d(s,"g",(function(){return d})),e.d(s,"e",(function(){return u}));var a=e("b775");function i(t){return Object(a["a"])({url:"/system/ticket/list",method:"get",params:t})}function l(t){return Object(a["a"])({url:"/system/ticket/"+t,method:"get"})}function c(t){return Object(a["a"])({url:"/system/ticket/"+t,method:"delete"})}function n(t,s){return Object(a["a"])({url:"/system/ticket/close/"+t,method:"put",data:s})}function r(t,s){return Object(a["a"])({url:"/system/ticket/assign/"+t,method:"put",data:s})}function o(t,s){return Object(a["a"])({url:"/system/ticket/reply/"+t,method:"post",data:s})}function d(){return Object(a["a"])({url:"/system/ticket/stats",method:"get"})}function u(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:5;return Object(a["a"])({url:"/system/ticket/latest",method:"get",params:{limit:t}})}},7845:function(t,s,e){},d2c3:function(t,s,e){"use strict";e("7845")}}]);