<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统首页使用说明 - 彩票系统帮助文档</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        .header h1 {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        .back-btn {
            position: absolute;
            left: 30px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-50%) translateY(-2px);
        }
        .content {
            padding: 40px;
        }
        .section-toggle {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 12px;
            cursor: pointer;
            user-select: none;
            margin: 20px 0 10px 0;
            font-size: 18px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        .section-toggle:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        .toggle-icon {
            transition: transform 0.3s ease;
            font-size: 14px;
        }
        .toggle-icon.collapsed {
            transform: rotate(-90deg);
        }
        .section-content {
            max-height: 2000px;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 20px;
            padding: 20px;
        }
        .section-content.collapsed {
            max-height: 0;
            padding: 0 20px;
        }
        .subsection-toggle {
            cursor: pointer;
            user-select: none;
            display: flex;
            align-items: center;
            gap: 8px;
            color: #667eea;
            margin-top: 20px;
            margin-bottom: 10px;
            font-size: 16px;
            font-weight: 500;
        }
        .subsection-toggle:hover {
            color: #5a6fd8;
        }
        .subsection-toggle-icon {
            transition: transform 0.3s ease;
            font-size: 12px;
        }
        .subsection-toggle-icon.collapsed {
            transform: rotate(-90deg);
        }
        .subsection-content {
            max-height: 500px;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        .subsection-content.collapsed {
            max-height: 0;
        }
        .function-item {
            background: white;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            border-left: 4px solid #667eea;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        .function-title {
            font-weight: 600;
            color: #667eea;
            margin-bottom: 8px;
            font-size: 15px;
        }
        .function-desc {
            color: #666;
            margin-bottom: 8px;
            line-height: 1.6;
        }
        .function-usage {
            color: #888;
            font-size: 14px;
            font-style: italic;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }
        .feature-card {
            background: white;
            border-radius: 8px;
            padding: 16px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .tip {
            background: #e8f4fd;
            border: 1px solid #b3d8ff;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            color: #0066cc;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            color: #856404;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            color: #155724;
        }
        .highlight {
            background: #e8f4fd;
            padding: 2px 6px;
            border-radius: 4px;
            color: #0066cc;
            font-weight: 500;
        }
        .expand-all-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
        }
        .expand-all-btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
        }
        .step-list {
            counter-reset: step-counter;
        }
        .step-item {
            counter-increment: step-counter;
            position: relative;
            padding-left: 40px;
            margin-bottom: 16px;
        }
        .step-item::before {
            content: counter(step-counter);
            position: absolute;
            left: 0;
            top: 0;
            width: 24px;
            height: 24px;
            background: #667eea;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            margin: 8px 0;
            overflow-x: auto;
        }
        ul, ol {
            padding-left: 20px;
            margin: 8px 0;
        }
        li {
            margin-bottom: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <button class="back-btn" onclick="goBack()">← 返回</button>
            <h1>🏠 系统首页使用说明</h1>
            <p>详细介绍系统首页的所有功能和使用方法</p>
        </div>

        <div class="content">
            <div class="tip">
                <strong>页面概述：</strong>系统首页是彩票管理系统的主控制台，提供数据概览、快速导航、统计图表等核心功能，帮助用户快速了解系统状态和进行日常操作。
            </div>

            <h2 class="section-toggle" onclick="toggleSection('overview')">
                <span class="toggle-icon collapsed" id="overview-icon">▼</span>
                一、数据概览区域
            </h2>
            <div class="feature-section section-content collapsed" id="overview-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('overview-1-1')">
                    <span class="subsection-toggle-icon collapsed" id="overview-1-1-icon">▼</span>
                    1.1 统计卡片
                </h3>
                <div class="subsection-content collapsed" id="overview-1-1-content">
                    <div class="function-item">
                        <div class="function-title">实时数据展示</div>
                        <div class="function-desc">显示系统关键指标的实时统计数据</div>
                        <div class="function-usage">包含投注总额、中奖金额、用户数量、开奖期数等核心数据</div>
                    </div>
                    <div class="feature-grid">
                        <div class="feature-card">
                            <div class="function-title">💰 投注统计</div>
                            <div class="function-desc">显示总投注金额和今日投注</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">🏆 中奖统计</div>
                            <div class="function-desc">显示总中奖金额和中奖率</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">👥 用户统计</div>
                            <div class="function-desc">显示注册用户数和活跃用户</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">🎯 开奖统计</div>
                            <div class="function-desc">显示开奖期数和最新开奖</div>
                        </div>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('overview-1-2')">
                    <span class="subsection-toggle-icon collapsed" id="overview-1-2-icon">▼</span>
                    1.2 数据刷新
                </h3>
                <div class="subsection-content collapsed" id="overview-1-2-content">
                    <div class="function-item">
                        <div class="function-title">自动刷新机制</div>
                        <div class="function-desc">系统数据每30秒自动刷新一次，确保数据实时性</div>
                        <div class="function-usage">可通过刷新按钮手动刷新数据</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">数据加载状态</div>
                        <div class="function-desc">显示数据加载进度和状态提示</div>
                        <div class="function-usage">加载失败时会显示重试按钮</div>
                    </div>
                </div>
            </div>

            <h2 class="section-toggle" onclick="toggleSection('navigation')">
                <span class="toggle-icon collapsed" id="navigation-icon">▼</span>
                二、快速导航功能
            </h2>
            <div class="feature-section section-content collapsed" id="navigation-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('navigation-2-1')">
                    <span class="subsection-toggle-icon collapsed" id="navigation-2-1-icon">▼</span>
                    2.1 功能模块入口
                </h3>
                <div class="subsection-content collapsed" id="navigation-2-1-content">
                    <div class="function-item">
                        <div class="function-title">游戏管理模块</div>
                        <div class="function-desc">快速进入下注记录、开奖管理、中奖管理等核心功能</div>
                        <div class="function-usage">点击对应图标或文字即可跳转到相应页面</div>
                    </div>
                    <div class="feature-grid">
                        <div class="feature-card">
                            <div class="function-title">📊 下注记录</div>
                            <div class="function-desc">查看和管理所有投注记录</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">🎯 开奖管理</div>
                            <div class="function-desc">管理开奖信息和结果</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">🏆 中奖管理</div>
                            <div class="function-desc">处理中奖记录和奖金</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">👥 玩家管理</div>
                            <div class="function-desc">管理用户信息和权限</div>
                        </div>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('navigation-2-2')">
                    <span class="subsection-toggle-icon collapsed" id="navigation-2-2-icon">▼</span>
                    2.2 常用操作快捷方式
                </h3>
                <div class="subsection-content collapsed" id="navigation-2-2-content">
                    <div class="function-item">
                        <div class="function-title">快速投注</div>
                        <div class="function-desc">直接打开投注弹窗进行快速投注</div>
                        <div class="function-usage">适用于熟练用户的快速操作</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">最新开奖</div>
                        <div class="function-desc">查看最新一期的开奖结果</div>
                        <div class="function-usage">点击可查看详细开奖信息</div>
                    </div>
                </div>
            </div>

            <h2 class="section-toggle" onclick="toggleSection('charts')">
                <span class="toggle-icon collapsed" id="charts-icon">▼</span>
                三、统计图表分析
            </h2>
            <div class="feature-section section-content collapsed" id="charts-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('charts-3-1')">
                    <span class="subsection-toggle-icon collapsed" id="charts-3-1-icon">▼</span>
                    3.1 投注趋势图
                </h3>
                <div class="subsection-content collapsed" id="charts-3-1-content">
                    <div class="function-item">
                        <div class="function-title">时间趋势分析</div>
                        <div class="function-desc">显示近期投注金额的变化趋势</div>
                        <div class="function-usage">支持按日、周、月查看数据</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">玩法分布</div>
                        <div class="function-desc">展示不同玩法的投注比例</div>
                        <div class="function-usage">饼图形式直观显示数据分布</div>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('charts-3-2')">
                    <span class="subsection-toggle-icon collapsed" id="charts-3-2-icon">▼</span>
                    3.2 用户活跃度
                </h3>
                <div class="subsection-content collapsed" id="charts-3-2-content">
                    <div class="function-item">
                        <div class="function-title">活跃用户统计</div>
                        <div class="function-desc">显示日活跃、周活跃用户数量</div>
                        <div class="function-usage">帮助了解用户参与度</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">新用户增长</div>
                        <div class="function-desc">展示新注册用户的增长情况</div>
                        <div class="function-usage">折线图显示增长趋势</div>
                    </div>
                </div>
            </div>

            <h2 class="section-toggle" onclick="toggleSection('tips')">
                <span class="toggle-icon collapsed" id="tips-icon">▼</span>
                四、使用技巧与注意事项
            </h2>
            <div class="feature-section section-content collapsed" id="tips-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('tips-4-1')">
                    <span class="subsection-toggle-icon collapsed" id="tips-4-1-icon">▼</span>
                    4.1 使用技巧
                </h3>
                <div class="subsection-content collapsed" id="tips-4-1-content">
                    <div class="success">
                        <strong>💡 实用技巧：</strong>
                        <ul>
                            <li>定期查看首页数据概览，了解系统运营状况</li>
                            <li>利用快速导航功能提高工作效率</li>
                            <li>关注统计图表变化，及时发现异常情况</li>
                            <li>使用自动刷新功能保持数据最新</li>
                        </ul>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('tips-4-2')">
                    <span class="subsection-toggle-icon collapsed" id="tips-4-2-icon">▼</span>
                    4.2 注意事项
                </h3>
                <div class="subsection-content collapsed" id="tips-4-2-content">
                    <div class="warning">
                        <strong>⚠️ 重要提醒：</strong>
                        <ul>
                            <li>首页数据仅供参考，具体操作请进入相应功能页面</li>
                            <li>网络不稳定时可能影响数据刷新，请手动刷新</li>
                            <li>统计数据可能存在延迟，以实际业务数据为准</li>
                            <li>权限不足时部分功能入口可能不可见</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <button class="expand-all-btn" onclick="toggleAllSections()">
        <span id="expandAllText">📖 展开全部</span>
    </button>

    <script>
        // 切换章节显示/隐藏
        function toggleSection(sectionId) {
            const content = document.getElementById(sectionId + '-content');
            const icon = document.getElementById(sectionId + '-icon');
            
            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                icon.classList.remove('collapsed');
            } else {
                content.classList.add('collapsed');
                icon.classList.add('collapsed');
            }
        }

        // 切换子章节显示/隐藏
        function toggleSubsection(subsectionId) {
            const content = document.getElementById(subsectionId + '-content');
            const icon = document.getElementById(subsectionId + '-icon');
            
            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                icon.classList.remove('collapsed');
            } else {
                content.classList.add('collapsed');
                icon.classList.add('collapsed');
            }
        }

        // 切换全部章节和子章节
        function toggleAllSections() {
            const sections = ['overview', 'navigation', 'charts', 'tips'];
            const subsections = [
                'overview-1-1', 'overview-1-2',
                'navigation-2-1', 'navigation-2-2',
                'charts-3-1', 'charts-3-2',
                'tips-4-1', 'tips-4-2'
            ];

            const expandAllText = document.getElementById('expandAllText');
            const allCollapsed = sections.every(sectionId =>
                document.getElementById(sectionId + '-content').classList.contains('collapsed')
            );

            sections.forEach(sectionId => {
                const content = document.getElementById(sectionId + '-content');
                const icon = document.getElementById(sectionId + '-icon');

                if (allCollapsed) {
                    content.classList.remove('collapsed');
                    icon.classList.remove('collapsed');
                } else {
                    content.classList.add('collapsed');
                    icon.classList.add('collapsed');
                }
            });

            subsections.forEach(subsectionId => {
                const content = document.getElementById(subsectionId + '-content');
                const icon = document.getElementById(subsectionId + '-icon');

                if (allCollapsed) {
                    content.classList.remove('collapsed');
                    icon.classList.remove('collapsed');
                } else {
                    content.classList.add('collapsed');
                    icon.classList.add('collapsed');
                }
            });

            expandAllText.textContent = allCollapsed ? '📕 收起全部' : '📖 展开全部';
        }

        // 根据URL参数自动展开指定内容
        function autoExpandFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            const expandTarget = urlParams.get('expand');

            if (expandTarget) {
                // 查找包含目标文本的元素
                const allElements = document.querySelectorAll('.function-title, .subsection-toggle, .section-toggle');

                for (let element of allElements) {
                    const text = element.textContent || element.innerText;
                    if (text.includes(expandTarget)) {
                        // 找到匹配的元素，展开其所在的章节和子章节
                        expandToTarget(element);
                        // 滚动到目标位置
                        setTimeout(() => {
                            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            // 高亮显示目标元素
                            highlightElement(element);
                        }, 300);
                        break;
                    }
                }
            }
        }

        // 展开到目标元素
        function expandToTarget(targetElement) {
            let current = targetElement;

            // 向上查找并展开所有父级章节
            while (current) {
                if (current.classList && current.classList.contains('section-content')) {
                    // 展开章节
                    const sectionId = current.id.replace('-content', '');
                    const icon = document.getElementById(sectionId + '-icon');
                    if (icon && icon.classList.contains('collapsed')) {
                        toggleSection(sectionId);
                    }
                }

                if (current.classList && current.classList.contains('subsection-content')) {
                    // 展开子章节
                    const subsectionId = current.id.replace('-content', '');
                    const icon = document.getElementById(subsectionId + '-icon');
                    if (icon && icon.classList.contains('collapsed')) {
                        toggleSubsection(subsectionId);
                    }
                }

                current = current.parentElement;
            }
        }

        // 高亮显示元素
        function highlightElement(element) {
            element.style.backgroundColor = '#fff3cd';
            element.style.border = '2px solid #ffc107';
            element.style.borderRadius = '4px';
            element.style.padding = '8px';
            element.style.transition = 'all 0.3s ease';

            // 3秒后移除高亮
            setTimeout(() => {
                element.style.backgroundColor = '';
                element.style.border = '';
                element.style.borderRadius = '';
                element.style.padding = '';
            }, 3000);
        }

        // 返回功能
        function goBack() {
            const fromHelp = sessionStorage.getItem('helpFromPage');
            if (fromHelp) {
                sessionStorage.removeItem('helpFromPage');
                window.location.href = fromHelp;
            } else {
                window.location.href = 'index.html';
            }
        }

        // 页面加载时处理URL参数
        document.addEventListener('DOMContentLoaded', function() {
            autoExpandFromUrl();
        });
    </script>
</body>
</html>
