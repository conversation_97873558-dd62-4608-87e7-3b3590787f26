(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4a3a3d92"],{a3f4:function(t,e,s){"use strict";s.r(e);var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"app-container"},[s("div",{staticClass:"page-header"},[t._m(0),s("div",{staticClass:"header-actions"},[s("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["game:ticket:add"],expression:"['game:ticket:add']"}],staticClass:"create-ticket-btn",attrs:{type:"primary",icon:"el-icon-plus"},on:{click:t.handleAdd}},[t._v(" 新建工单 ")])],1)]),s("el-row",{staticClass:"stats-row",attrs:{gutter:20}},[s("el-col",{attrs:{span:6}},[s("div",{staticClass:"stat-card pending"},[s("div",{staticClass:"stat-icon"},[s("i",{staticClass:"el-icon-clock"})]),s("div",{staticClass:"stat-content"},[s("div",{staticClass:"stat-number"},[t._v(t._s(t.statistics.pending))]),s("div",{staticClass:"stat-label"},[t._v("待处理")])])])]),s("el-col",{attrs:{span:6}},[s("div",{staticClass:"stat-card processing"},[s("div",{staticClass:"stat-icon"},[s("i",{staticClass:"el-icon-loading"})]),s("div",{staticClass:"stat-content"},[s("div",{staticClass:"stat-number"},[t._v(t._s(t.statistics.processing))]),s("div",{staticClass:"stat-label"},[t._v("处理中")])])])]),s("el-col",{attrs:{span:6}},[s("div",{staticClass:"stat-card resolved"},[s("div",{staticClass:"stat-icon"},[s("i",{staticClass:"el-icon-check"})]),s("div",{staticClass:"stat-content"},[s("div",{staticClass:"stat-number"},[t._v(t._s(t.statistics.resolved))]),s("div",{staticClass:"stat-label"},[t._v("已解决")])])])]),s("el-col",{attrs:{span:6}},[s("div",{staticClass:"stat-card closed"},[s("div",{staticClass:"stat-icon"},[s("i",{staticClass:"el-icon-close"})]),s("div",{staticClass:"stat-content"},[s("div",{staticClass:"stat-number"},[t._v(t._s(t.statistics.closed))]),s("div",{staticClass:"stat-label"},[t._v("已关闭")])])])])],1),s("div",{staticClass:"search-container"},[s("el-form",{directives:[{name:"show",rawName:"v-show",value:t.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:t.queryParams,size:"small",inline:!0,"label-width":"68px"}},[s("el-form-item",{attrs:{label:"工单编号",prop:"ticketNumber"}},[s("el-input",{attrs:{placeholder:"请输入工单编号",clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.ticketNumber,callback:function(e){t.$set(t.queryParams,"ticketNumber",e)},expression:"queryParams.ticketNumber"}})],1),s("el-form-item",{attrs:{label:"工单标题",prop:"title"}},[s("el-input",{attrs:{placeholder:"请输入工单标题",clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.title,callback:function(e){t.$set(t.queryParams,"title",e)},expression:"queryParams.title"}})],1),s("el-form-item",{attrs:{label:"工单状态",prop:"status"}},[s("el-select",{attrs:{placeholder:"请选择工单状态",clearable:""},model:{value:t.queryParams.status,callback:function(e){t.$set(t.queryParams,"status",e)},expression:"queryParams.status"}},[s("el-option",{attrs:{label:"待处理",value:"0"}}),s("el-option",{attrs:{label:"处理中",value:"1"}}),s("el-option",{attrs:{label:"已解决",value:"2"}}),s("el-option",{attrs:{label:"已关闭",value:"3"}})],1)],1),s("el-form-item",[s("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:t.handleQuery}},[t._v("搜索")]),s("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:t.resetQuery}},[t._v("重置")])],1)],1)],1),s("div",{staticClass:"toolbar-container"},[s("el-row",{staticClass:"mb8",attrs:{gutter:10}},[s("el-col",{attrs:{span:1.5}},[s("el-button",{staticClass:"refresh-btn",attrs:{type:"success",icon:"el-icon-refresh",size:"mini"},on:{click:t.getList}},[t._v(" 刷新列表 ")])],1),s("right-toolbar",{attrs:{showSearch:t.showSearch},on:{"update:showSearch":function(e){t.showSearch=e},"update:show-search":function(e){t.showSearch=e},queryTable:t.getList}})],1)],1),s("div",{staticClass:"table-container"},[s("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"modern-table",attrs:{data:t.ticketList,"row-class-name":t.getRowClassName},on:{"selection-change":t.handleSelectionChange}},[s("el-table-column",{attrs:{label:"工单编号",align:"center",prop:"ticketNumber",width:"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("div",{staticClass:"ticket-number"},[s("i",{staticClass:"el-icon-tickets"}),s("span",{staticClass:"number-text"},[t._v(t._s(e.row.ticketNumber))])])]}}])}),s("el-table-column",{attrs:{label:"工单信息",align:"left","min-width":"250"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("div",{staticClass:"ticket-info"},[s("div",{staticClass:"ticket-title"},[t._v(t._s(e.row.title))]),s("div",{staticClass:"ticket-meta"},[s("span",{staticClass:"category-info"},[s("i",{staticClass:"el-icon-folder"}),t._v(" "+t._s(t.getCategoryName(e.row.category))+" ")]),s("span",{staticClass:"time-info"},[s("i",{staticClass:"el-icon-time"}),t._v(" "+t._s(t.parseTime(e.row.createTime,"{m}-{d} {h}:{i}"))+" ")])])])]}}])}),s("el-table-column",{attrs:{label:"状态",align:"center",prop:"status",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("div",{staticClass:"status-wrapper"},["0"===e.row.status?s("el-tag",{staticClass:"status-tag pending-tag",attrs:{type:"info",effect:"dark"}},[s("i",{staticClass:"el-icon-clock"}),t._v(" 待处理 ")]):"1"===e.row.status?s("el-tag",{staticClass:"status-tag processing-tag",attrs:{type:"warning",effect:"dark"}},[s("i",{staticClass:"el-icon-loading"}),t._v(" 处理中 ")]):"2"===e.row.status?s("el-tag",{staticClass:"status-tag resolved-tag",attrs:{type:"success",effect:"dark"}},[s("i",{staticClass:"el-icon-check"}),t._v(" 已解决 ")]):"3"===e.row.status?s("el-tag",{staticClass:"status-tag closed-tag",attrs:{type:"danger",effect:"dark"}},[s("i",{staticClass:"el-icon-close"}),t._v(" 已关闭 ")]):t._e()],1)]}}])}),s("el-table-column",{attrs:{label:"优先级",align:"center",prop:"priority",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("div",{staticClass:"priority-wrapper"},["1"===e.row.priority?s("el-tag",{staticClass:"priority-tag low",attrs:{type:"info",size:"small"}},[s("i",{staticClass:"el-icon-bottom"}),t._v(" 低 ")]):"2"===e.row.priority?s("el-tag",{staticClass:"priority-tag medium",attrs:{type:"warning",size:"small"}},[s("i",{staticClass:"el-icon-minus"}),t._v(" 中 ")]):"3"===e.row.priority?s("el-tag",{staticClass:"priority-tag high",attrs:{type:"danger",size:"small"}},[s("i",{staticClass:"el-icon-top"}),t._v(" 高 ")]):"4"===e.row.priority?s("el-tag",{staticClass:"priority-tag urgent",attrs:{type:"danger",size:"small"}},[s("i",{staticClass:"el-icon-warning"}),t._v(" 紧急 ")]):t._e()],1)]}}])}),s("el-table-column",{attrs:{label:"处理人员",align:"center",prop:"assignedName",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("div",{staticClass:"assigned-info"},[e.row.assignedName?s("div",{staticClass:"assigned-user"},[s("el-avatar",{staticClass:"user-avatar",attrs:{size:24}},[s("i",{staticClass:"el-icon-user-solid"})]),s("span",{staticClass:"user-name"},[t._v(t._s(e.row.assignedName))])],1):s("div",{staticClass:"unassigned"},[s("i",{staticClass:"el-icon-user"}),s("span",[t._v("待分配")])])])]}}])}),s("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("div",{staticClass:"action-buttons"},[s("el-tooltip",{attrs:{content:"查看详情",placement:"top"}},[s("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["game:ticket:query"],expression:"['game:ticket:query']"}],staticClass:"action-btn view-btn",attrs:{size:"mini",type:"primary",icon:"el-icon-view",circle:""},on:{click:function(s){return t.handleView(e.row)}}})],1),"0"===e.row.status?s("el-tooltip",{attrs:{content:"修改工单",placement:"top"}},[s("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["game:ticket:edit"],expression:"['game:ticket:edit']"}],staticClass:"action-btn edit-btn",attrs:{size:"mini",type:"warning",icon:"el-icon-edit",circle:""},on:{click:function(s){return t.handleUpdate(e.row)}}})],1):t._e(),"3"!==e.row.status?s("el-tooltip",{attrs:{content:"回复工单",placement:"top"}},[s("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["game:ticket:edit"],expression:"['game:ticket:edit']"}],staticClass:"action-btn reply-btn",attrs:{size:"mini",type:"success",icon:"el-icon-chat-line-round",circle:""},on:{click:function(s){return t.handleReply(e.row)}}})],1):t._e()],1)]}}])})],1)],1),s("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total>0"}],attrs:{total:t.total,page:t.queryParams.pageNum,limit:t.queryParams.pageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"pageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"pageSize",e)},pagination:t.getList}}),s("el-dialog",{staticClass:"ticket-dialog",attrs:{visible:t.open,width:"700px","append-to-body":"","close-on-click-modal":!1,"show-close":!1,title:null},on:{"update:visible":function(e){t.open=e}}},[s("div",{staticClass:"dialog-header"},[s("div",{staticClass:"header-icon"},[s("i",{staticClass:"el-icon-edit-outline"})]),s("div",{staticClass:"header-text"},[s("div",{staticClass:"header-title"},[t._v(t._s(t.title))]),s("div",{staticClass:"header-desc"},[t._v("请详细填写工单信息，以便我们更好地为您服务")])]),s("div",{staticClass:"header-close",on:{click:t.cancel}},[s("i",{staticClass:"el-icon-close"})])]),s("div",{staticClass:"dialog-content"},[s("el-form",{ref:"form",staticClass:"ticket-form",attrs:{model:t.form,rules:t.rules,"label-width":"100px"}},[s("div",{staticClass:"form-section"},[s("div",{staticClass:"section-title"},[s("i",{staticClass:"el-icon-document"}),s("span",[t._v("基本信息")])]),s("div",{staticClass:"form-row"},[s("el-form-item",{staticClass:"form-item-full",attrs:{label:"工单标题",prop:"title"}},[s("el-input",{attrs:{placeholder:"请输入工单标题，简要描述您的问题","prefix-icon":"el-icon-edit",maxlength:"100","show-word-limit":""},model:{value:t.form.title,callback:function(e){t.$set(t.form,"title",e)},expression:"form.title"}})],1)],1),s("div",{staticClass:"form-row"},[s("el-row",{attrs:{gutter:20}},[s("el-col",{attrs:{span:12}},[s("el-form-item",{attrs:{label:"优先级",prop:"priority"}},[s("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择优先级"},model:{value:t.form.priority,callback:function(e){t.$set(t.form,"priority",e)},expression:"form.priority"}},[s("el-option",{attrs:{label:"低优先级",value:"1"}},[s("span",{staticStyle:{float:"left"}},[t._v("低优先级")]),s("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[t._v("一般问题")])]),s("el-option",{attrs:{label:"中优先级",value:"2"}},[s("span",{staticStyle:{float:"left"}},[t._v("中优先级")]),s("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[t._v("影响使用")])]),s("el-option",{attrs:{label:"高优先级",value:"3"}},[s("span",{staticStyle:{float:"left"}},[t._v("高优先级")]),s("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[t._v("严重问题")])]),s("el-option",{attrs:{label:"紧急",value:"4"}},[s("span",{staticStyle:{float:"left"}},[t._v("紧急")]),s("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[t._v("无法使用")])])],1)],1)],1),s("el-col",{attrs:{span:12}},[s("el-form-item",{attrs:{label:"问题分类",prop:"category"}},[s("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择问题分类"},model:{value:t.form.category,callback:function(e){t.$set(t.form,"category",e)},expression:"form.category"}},[s("el-option",{attrs:{label:"系统错误",value:"system_error"}},[s("span",{staticStyle:{float:"left"}},[t._v("系统错误")]),s("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[t._v("🐛")])]),s("el-option",{attrs:{label:"功能建议",value:"feature_request"}},[s("span",{staticStyle:{float:"left"}},[t._v("功能建议")]),s("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[t._v("💡")])]),s("el-option",{attrs:{label:"使用问题",value:"usage_issue"}},[s("span",{staticStyle:{float:"left"}},[t._v("使用问题")]),s("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[t._v("❓")])]),s("el-option",{attrs:{label:"账户问题",value:"account_issue"}},[s("span",{staticStyle:{float:"left"}},[t._v("账户问题")]),s("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[t._v("👤")])]),s("el-option",{attrs:{label:"其他",value:"other"}},[s("span",{staticStyle:{float:"left"}},[t._v("其他")]),s("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[t._v("📝")])])],1)],1)],1)],1)],1)]),s("div",{staticClass:"form-section"},[s("div",{staticClass:"section-title"},[s("i",{staticClass:"el-icon-chat-line-square"}),s("span",[t._v("问题描述")])]),s("div",{staticClass:"form-row"},[s("el-form-item",{staticClass:"form-item-full",attrs:{label:"详细描述",prop:"content"}},[s("el-input",{attrs:{type:"textarea",rows:8,placeholder:"\n                请详细描述您遇到的问题，包括：\n                1. 问题出现的具体情况\n                2. 您期望的结果\n                3. 实际发生的情况\n                4. 重现步骤（如果有）",maxlength:"2000","show-word-limit":""},model:{value:t.form.content,callback:function(e){t.$set(t.form,"content",e)},expression:"form.content"}})],1)],1)])])],1),s("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[s("el-button",{staticClass:"cancel-btn",on:{click:t.cancel}},[s("i",{staticClass:"el-icon-close"}),t._v(" 取消 ")]),s("el-button",{staticClass:"submit-btn",attrs:{type:"primary"},on:{click:t.submitForm}},[s("i",{staticClass:"el-icon-check"}),t._v(" "+t._s(t.form.ticketId?"保存修改":"提交工单")+" ")])],1)]),s("el-dialog",{staticClass:"view-dialog",attrs:{visible:t.viewOpen,width:"900px","append-to-body":"","close-on-click-modal":!1,"show-close":!1,title:null},on:{"update:visible":function(e){t.viewOpen=e}}},[s("div",{staticClass:"dialog-header"},[s("div",{staticClass:"header-icon"},[s("i",{staticClass:"el-icon-view"})]),s("div",{staticClass:"header-text"},[s("div",{staticClass:"header-title"},[t._v("工单详情")]),s("div",{staticClass:"header-desc"},[t._v("查看工单详细信息和沟通记录")])]),s("div",{staticClass:"header-close",on:{click:function(e){t.viewOpen=!1}}},[s("i",{staticClass:"el-icon-close"})])]),s("div",{staticClass:"view-dialog-content"},[s("div",{staticClass:"ticket-header"},[s("div",{staticClass:"ticket-number-badge"},[s("i",{staticClass:"el-icon-tickets"}),s("span",[t._v(t._s(t.viewForm.ticketNumber))])]),s("div",{staticClass:"ticket-status-badge"},["0"===t.viewForm.status?s("el-tag",{staticClass:"status-tag",attrs:{type:"info",effect:"dark"}},[s("i",{staticClass:"el-icon-clock"}),t._v(" 待处理 ")]):"1"===t.viewForm.status?s("el-tag",{staticClass:"status-tag",attrs:{type:"warning",effect:"dark"}},[s("i",{staticClass:"el-icon-loading"}),t._v(" 处理中 ")]):"2"===t.viewForm.status?s("el-tag",{staticClass:"status-tag",attrs:{type:"success",effect:"dark"}},[s("i",{staticClass:"el-icon-check"}),t._v(" 已解决 ")]):"3"===t.viewForm.status?s("el-tag",{staticClass:"status-tag",attrs:{type:"danger",effect:"dark"}},[s("i",{staticClass:"el-icon-close"}),t._v(" 已关闭 ")]):t._e()],1)]),s("div",{staticClass:"ticket-basic-info"},[s("div",{staticClass:"info-card"},[s("div",{staticClass:"card-header"},[s("i",{staticClass:"el-icon-document"}),s("span",[t._v("工单信息")])]),s("div",{staticClass:"card-content"},[s("div",{staticClass:"info-row"},[s("div",{staticClass:"info-item"},[s("span",{staticClass:"label"},[t._v("工单标题：")]),s("span",{staticClass:"value title"},[t._v(t._s(t.viewForm.title))])])]),s("div",{staticClass:"info-row"},[s("div",{staticClass:"info-item"},[s("span",{staticClass:"label"},[t._v("优先级：")]),"1"===t.viewForm.priority?s("el-tag",{staticClass:"priority-tag",attrs:{type:"info",size:"small"}},[s("i",{staticClass:"el-icon-bottom"}),t._v(" 低优先级 ")]):"2"===t.viewForm.priority?s("el-tag",{staticClass:"priority-tag",attrs:{type:"warning",size:"small"}},[s("i",{staticClass:"el-icon-minus"}),t._v(" 中优先级 ")]):"3"===t.viewForm.priority?s("el-tag",{staticClass:"priority-tag",attrs:{type:"danger",size:"small"}},[s("i",{staticClass:"el-icon-top"}),t._v(" 高优先级 ")]):"4"===t.viewForm.priority?s("el-tag",{staticClass:"priority-tag urgent",attrs:{type:"danger",size:"small"}},[s("i",{staticClass:"el-icon-warning"}),t._v(" 紧急 ")]):t._e()],1),s("div",{staticClass:"info-item"},[s("span",{staticClass:"label"},[t._v("问题分类：")]),s("span",{staticClass:"value"},[t._v(t._s(t.getCategoryName(t.viewForm.category)))])])]),s("div",{staticClass:"info-row"},[s("div",{staticClass:"info-item"},[s("span",{staticClass:"label"},[t._v("处理人员：")]),s("span",{staticClass:"value"},[t._v(t._s(t.viewForm.assignedName||"待分配"))])]),s("div",{staticClass:"info-item"},[s("span",{staticClass:"label"},[t._v("创建时间：")]),s("span",{staticClass:"value"},[t._v(t._s(t.parseTime(t.viewForm.createTime,"{y}-{m}-{d} {h}:{i}:{s}")))])])])])]),s("div",{staticClass:"content-card"},[s("div",{staticClass:"card-header"},[s("i",{staticClass:"el-icon-chat-line-square"}),s("span",[t._v("问题描述")])]),s("div",{staticClass:"card-content"},[s("div",{staticClass:"content-text"},[t._v(t._s(t.viewForm.content))])])])]),t.viewForm.sysTicketReplyList&&t.viewForm.sysTicketReplyList.length>0?s("div",{staticClass:"replies-section"},[s("div",{staticClass:"section-header"},[s("i",{staticClass:"el-icon-chat-dot-round"}),s("span",[t._v("沟通记录")]),s("div",{staticClass:"reply-count"},[t._v(t._s(t.viewForm.sysTicketReplyList.length)+" 条回复")])]),s("div",{staticClass:"replies-container"},t._l(t.viewForm.sysTicketReplyList,(function(e,a){return s("div",{key:e.replyId,staticClass:"reply-item",class:{"user-reply":"0"===e.replyType,"admin-reply":"1"===e.replyType}},[s("div",{staticClass:"reply-avatar"},[s("el-avatar",{staticClass:"avatar",attrs:{size:40}},["0"===e.replyType?s("i",{staticClass:"el-icon-user"}):s("i",{staticClass:"el-icon-service"})])],1),s("div",{staticClass:"reply-content"},[s("div",{staticClass:"reply-header"},[s("div",{staticClass:"reply-user"},[s("span",{staticClass:"user-name"},[t._v(t._s(e.userName))]),"0"===e.replyType?s("el-tag",{staticClass:"user-type-tag",attrs:{type:"primary",size:"mini"}},[t._v(" 用户 ")]):s("el-tag",{staticClass:"user-type-tag",attrs:{type:"success",size:"mini"}},[t._v(" 客服 ")])],1),s("div",{staticClass:"reply-time"},[t._v(" "+t._s(t.parseTime(e.createTime,"{y}-{m}-{d} {h}:{i}:{s}"))+" ")])]),s("div",{staticClass:"reply-text"},[t._v(t._s(e.content))])])])})),0)]):t._e()])]),s("el-dialog",{staticClass:"reply-dialog",attrs:{visible:t.replyOpen,width:"650px","append-to-body":"","close-on-click-modal":!1,"show-close":!1,title:null},on:{"update:visible":function(e){t.replyOpen=e}}},[s("div",{staticClass:"dialog-header"},[s("div",{staticClass:"header-icon"},[s("i",{staticClass:"el-icon-chat-line-round"})]),s("div",{staticClass:"header-text"},[s("div",{staticClass:"header-title"},[t._v("回复工单")]),s("div",{staticClass:"header-desc"},[t._v("添加回复内容，与客服进行沟通")])]),s("div",{staticClass:"header-close",on:{click:t.cancelReply}},[s("i",{staticClass:"el-icon-close"})])]),s("div",{staticClass:"reply-dialog-content"},[s("div",{staticClass:"ticket-preview"},[s("div",{staticClass:"preview-header"},[s("i",{staticClass:"el-icon-tickets"}),s("span",[t._v(t._s(t.replyForm.ticketNumber||"工单回复"))])]),s("div",{staticClass:"preview-content"},[s("div",{staticClass:"preview-item"},[s("span",{staticClass:"label"},[t._v("工单标题：")]),s("span",{staticClass:"value"},[t._v(t._s(t.replyForm.title))])])])]),s("div",{staticClass:"reply-form-container"},[s("div",{staticClass:"form-header"},[s("i",{staticClass:"el-icon-chat-line-round"}),s("span",[t._v("添加回复")])]),s("el-form",{ref:"replyForm",attrs:{model:t.replyForm,rules:t.replyRules,"label-width":"0"}},[s("el-form-item",{attrs:{prop:"content"}},[s("el-input",{staticClass:"reply-textarea",attrs:{type:"textarea",rows:8,placeholder:"请输入您的回复内容...&#10;&#10;提示：&#10;• 详细描述您的问题或补充信息&#10;• 如果问题已解决，请说明解决方案&#10;• 保持礼貌和专业的沟通",maxlength:"1000","show-word-limit":""},model:{value:t.replyForm.content,callback:function(e){t.$set(t.replyForm,"content",e)},expression:"replyForm.content"}})],1)],1)],1),s("div",{staticClass:"reply-tips"},[s("div",{staticClass:"tips-header"},[s("i",{staticClass:"el-icon-info"}),s("span",[t._v("回复提示")])]),s("div",{staticClass:"tips-content"},[s("div",{staticClass:"tip-item"},[s("i",{staticClass:"el-icon-check"}),s("span",[t._v("回复后，管理员将收到通知并及时处理")])]),s("div",{staticClass:"tip-item"},[s("i",{staticClass:"el-icon-check"}),s("span",[t._v("请详细描述问题，有助于快速解决")])]),s("div",{staticClass:"tip-item"},[s("i",{staticClass:"el-icon-check"}),s("span",[t._v("您可以随时查看工单处理进度")])])])])]),s("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[s("el-button",{staticClass:"cancel-btn",on:{click:t.cancelReply}},[s("i",{staticClass:"el-icon-close"}),t._v(" 取消 ")]),s("el-button",{staticClass:"submit-btn",attrs:{type:"primary"},on:{click:t.submitReply}},[s("i",{staticClass:"el-icon-s-promotion"}),t._v(" 发送回复 ")])],1)])],1)},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"header-content"},[s("div",{staticClass:"header-title"},[s("i",{staticClass:"el-icon-message"}),s("span",[t._v("我的工单")])]),s("div",{staticClass:"header-desc"},[t._v("提交问题工单，获得专业技术支持")])])}],l=(s("4de4"),s("d81d"),s("d3b7"),s("0643"),s("2382"),s("a573"),s("b775"));function r(t){return Object(l["a"])({url:"/game/ticket/list",method:"get",params:t})}function c(t){return Object(l["a"])({url:"/game/ticket/"+t,method:"get"})}function n(t){return Object(l["a"])({url:"/game/ticket",method:"post",data:t})}function o(t){return Object(l["a"])({url:"/game/ticket",method:"put",data:t})}function d(t,e){return Object(l["a"])({url:"/game/ticket/reply/"+t,method:"post",data:e})}var u={name:"Ticket",data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,ticketList:[],statistics:{pending:0,processing:0,resolved:0,closed:0},title:"",open:!1,viewOpen:!1,replyOpen:!1,queryParams:{pageNum:1,pageSize:10,ticketNumber:null,title:null,status:null,priority:null,category:null},form:{},viewForm:{},replyForm:{},rules:{title:[{required:!0,message:"工单标题不能为空",trigger:"blur"}],content:[{required:!0,message:"工单内容不能为空",trigger:"blur"}],priority:[{required:!0,message:"优先级不能为空",trigger:"change"}],category:[{required:!0,message:"工单分类不能为空",trigger:"change"}]},replyRules:{content:[{required:!0,message:"回复内容不能为空",trigger:"blur"}]}}},created:function(){this.getList()},methods:{getList:function(){var t=this;this.loading=!0,r(this.queryParams).then((function(e){t.ticketList=e.rows,t.total=e.total,t.calculateStatistics(),t.loading=!1}))},calculateStatistics:function(){this.statistics={pending:this.ticketList.filter((function(t){return"0"===t.status})).length,processing:this.ticketList.filter((function(t){return"1"===t.status})).length,resolved:this.ticketList.filter((function(t){return"2"===t.status})).length,closed:this.ticketList.filter((function(t){return"3"===t.status})).length}},getCategoryName:function(t){var e={system_error:"系统错误",feature_request:"功能建议",usage_issue:"使用问题",account_issue:"账户问题",other:"其他"};return e[t]||"其他"},getRowClassName:function(t){var e=t.row;return"4"===e.priority?"urgent-row":"3"===e.priority?"high-row":"3"===e.status?"closed-row":""},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={ticketId:null,ticketNumber:null,title:null,content:null,status:null,priority:"1",category:"other",userId:null,userName:null,assignedTo:null,assignedName:null,createBy:null,createTime:null,updateBy:null,updateTime:null,closeTime:null,closeReason:null,remark:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(t){this.ids=t.map((function(t){return t.ticketId})),this.single=1!==t.length,this.multiple=!t.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加工单"},handleUpdate:function(t){var e=this;this.reset();var s=t.ticketId||this.ids;c(s).then((function(t){e.form=t.data,e.open=!0,e.title="修改工单"}))},handleView:function(t){var e=this,s=t.ticketId;c(s).then((function(t){e.viewForm=t.data,e.viewOpen=!0}))},handleReply:function(t){this.replyForm={ticketId:t.ticketId,ticketNumber:t.ticketNumber,title:t.title,content:""},this.replyOpen=!0},submitForm:function(){var t=this;this.$refs["form"].validate((function(e){e&&(null!=t.form.ticketId?o(t.form).then((function(e){t.$modal.msgSuccess("修改成功"),t.open=!1,t.getList()})):n(t.form).then((function(e){t.$modal.msgSuccess("新增成功"),t.open=!1,t.getList()})))}))},submitReply:function(){var t=this;this.$refs["replyForm"].validate((function(e){e&&d(t.replyForm.ticketId,t.replyForm).then((function(e){t.$modal.msgSuccess("回复成功"),t.replyOpen=!1,t.getList()}))}))},cancelReply:function(){this.replyOpen=!1,this.replyForm={}}}},p=u,v=(s("a497"),s("2877")),m=Object(v["a"])(p,a,i,!1,null,null,null);e["default"]=m.exports},a497:function(t,e,s){"use strict";s("b69d")},b69d:function(t,e,s){}}]);