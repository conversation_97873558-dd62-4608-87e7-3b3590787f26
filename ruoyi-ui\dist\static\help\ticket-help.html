<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>提交工单使用说明 - 彩票系统帮助文档</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        .header h1 {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        .back-btn {
            position: absolute;
            left: 30px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-50%) translateY(-2px);
        }
        .content {
            padding: 40px;
        }
        .section-toggle {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px 20px;
            background: linear-gradient(135deg, #409EFF, #67C23A);
            color: white;
            border-radius: 12px;
            cursor: pointer;
            user-select: none;
            margin: 20px 0 10px 0;
            font-size: 18px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        }
        .section-toggle:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
        }
        .toggle-icon {
            transition: transform 0.3s ease;
            font-size: 14px;
        }
        .toggle-icon.collapsed {
            transform: rotate(-90deg);
        }
        .section-content {
            max-height: 2000px;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 20px;
            padding: 20px;
        }
        .section-content.collapsed {
            max-height: 0;
            padding: 0 20px;
        }
        .subsection-toggle {
            cursor: pointer;
            user-select: none;
            display: flex;
            align-items: center;
            gap: 8px;
            color: #409EFF;
            margin-top: 20px;
            margin-bottom: 10px;
            font-size: 16px;
            font-weight: 500;
        }
        .subsection-toggle:hover {
            color: #3a8ee6;
        }
        .subsection-toggle-icon {
            transition: transform 0.3s ease;
            font-size: 12px;
        }
        .subsection-toggle-icon.collapsed {
            transform: rotate(-90deg);
        }
        .subsection-content {
            max-height: 500px;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        .subsection-content.collapsed {
            max-height: 0;
        }
        .function-item {
            background: white;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            border-left: 4px solid #409EFF;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        .function-title {
            font-weight: 600;
            color: #409EFF;
            margin-bottom: 8px;
            font-size: 15px;
        }
        .function-desc {
            color: #666;
            margin-bottom: 8px;
            line-height: 1.6;
        }
        .function-usage {
            color: #888;
            font-size: 14px;
            font-style: italic;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }
        .feature-card {
            background: white;
            border-radius: 8px;
            padding: 16px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .tip {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            color: #0050b3;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            color: #856404;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            color: #155724;
        }
        .highlight {
            background: #e6f7ff;
            padding: 2px 6px;
            border-radius: 4px;
            color: #0050b3;
            font-weight: 500;
        }
        .expand-all-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, #409EFF, #67C23A);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
        }
        .expand-all-btn:hover {
            background: #3a8ee6;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
        }
        .step-list {
            counter-reset: step-counter;
        }
        .step-item {
            counter-increment: step-counter;
            position: relative;
            padding-left: 40px;
            margin-bottom: 16px;
        }
        .step-item::before {
            content: counter(step-counter);
            position: absolute;
            left: 0;
            top: 0;
            width: 24px;
            height: 24px;
            background: #409EFF;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            margin: 8px 0;
            overflow-x: auto;
        }
        ul, ol {
            padding-left: 20px;
            margin: 8px 0;
        }
        li {
            margin-bottom: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <button class="back-btn" onclick="goBack()">← 返回</button>
            <h1>📋 提交工单使用说明</h1>
            <p>详细介绍工单提交和管理功能的使用方法</p>
        </div>

        <div class="content">
            <div class="tip">
                <strong>页面概述：</strong>工单系统用于用户提交问题反馈、需求建议和技术支持请求。普通用户可以创建工单、查看工单状态、回复工单等操作。
            </div>

            <h2 class="section-toggle" onclick="toggleSection('create')">
                <span class="toggle-icon collapsed" id="create-icon">▼</span>
                一、工单创建功能
            </h2>
            <div class="feature-section section-content collapsed" id="create-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('create-1-1')">
                    <span class="subsection-toggle-icon collapsed" id="create-1-1-icon">▼</span>
                    1.1 新建工单
                </h3>
                <div class="subsection-content collapsed" id="create-1-1-content">
                    <div class="function-item">
                        <div class="function-title">创建入口</div>
                        <div class="function-desc">点击页面右上角的"新建工单"按钮开始创建工单</div>
                        <div class="function-usage">使用方法：在工单列表页面点击"新建工单"按钮</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">工单标题</div>
                        <div class="function-desc">输入简要描述问题的工单标题，最多100个字符</div>
                        <div class="function-usage">建议：标题应简洁明了，能够概括问题的核心</div>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('create-1-2')">
                    <span class="subsection-toggle-icon collapsed" id="create-1-2-icon">▼</span>
                    1.2 工单分类与优先级
                </h3>
                <div class="subsection-content collapsed" id="create-1-2-content">
                    <div class="function-item">
                        <div class="function-title">工单分类</div>
                        <div class="function-desc">选择合适的工单分类，帮助技术人员快速定位问题类型</div>
                    </div>
                    <div class="feature-grid">
                        <div class="feature-card">
                            <div class="function-title">🐛 系统Bug</div>
                            <div class="function-desc">系统功能异常、错误等问题</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">💡 功能需求</div>
                            <div class="function-desc">新功能建议、功能改进等</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">❓ 使用咨询</div>
                            <div class="function-desc">功能使用方法、操作指导等</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">🔧 技术支持</div>
                            <div class="function-desc">技术问题、配置问题等</div>
                        </div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">优先级设置</div>
                        <div class="function-desc">根据问题的紧急程度选择合适的优先级</div>
                        <ul>
                            <li><span class="highlight">低优先级</span>：一般性问题，不影响正常使用</li>
                            <li><span class="highlight">中优先级</span>：影响部分功能，但有替代方案</li>
                            <li><span class="highlight">高优先级</span>：影响主要功能，需要尽快处理</li>
                            <li><span class="highlight">紧急</span>：系统无法使用，需要立即处理</li>
                        </ul>
                    </div>
                </div>
            </div>

            <h2 class="section-toggle" onclick="toggleSection('content')">
                <span class="toggle-icon collapsed" id="content-icon">▼</span>
                二、工单内容编写
            </h2>
            <div class="feature-section section-content collapsed" id="content-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('content-2-1')">
                    <span class="subsection-toggle-icon collapsed" id="content-2-1-icon">▼</span>
                    2.1 问题描述
                </h3>
                <div class="subsection-content collapsed" id="content-2-1-content">
                    <div class="function-item">
                        <div class="function-title">详细描述</div>
                        <div class="function-desc">在工单内容中详细描述遇到的问题或需求</div>
                        <div class="function-usage">建议包含：问题现象、操作步骤、期望结果、实际结果</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">富文本编辑</div>
                        <div class="function-desc">支持富文本编辑，可以添加格式、图片等内容</div>
                        <div class="function-usage">使用方法：使用编辑器工具栏进行文本格式化</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">图片上传</div>
                        <div class="function-desc">可以上传截图或相关图片来说明问题</div>
                        <div class="function-usage">支持格式：JPG、PNG、GIF等常见图片格式</div>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('content-2-2')">
                    <span class="subsection-toggle-icon collapsed" id="content-2-2-icon">▼</span>
                    2.2 工单提交
                </h3>
                <div class="subsection-content collapsed" id="content-2-2-content">
                    <div class="function-item">
                        <div class="function-title">表单验证</div>
                        <div class="function-desc">系统会自动验证必填项和格式要求</div>
                        <div class="function-usage">必填项：工单标题、工单内容、优先级、工单分类</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">提交确认</div>
                        <div class="function-desc">点击"确定"按钮提交工单，系统会自动生成工单编号</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">自动编号</div>
                        <div class="function-desc">系统会自动为工单生成唯一的编号，便于跟踪</div>
                    </div>
                </div>
            </div>

            <h2 class="section-toggle" onclick="toggleSection('manage')">
                <span class="toggle-icon collapsed" id="manage-icon">▼</span>
                三、工单管理功能
            </h2>
            <div class="feature-section section-content collapsed" id="manage-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('manage-3-1')">
                    <span class="subsection-toggle-icon collapsed" id="manage-3-1-icon">▼</span>
                    3.1 工单列表
                </h3>
                <div class="subsection-content collapsed" id="manage-3-1-content">
                    <div class="function-item">
                        <div class="function-title">我的工单</div>
                        <div class="function-desc">查看自己提交的所有工单列表</div>
                        <div class="function-usage">显示内容：工单编号、标题、状态、优先级、创建时间</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">状态筛选</div>
                        <div class="function-desc">可以按工单状态筛选查看</div>
                        <ul>
                            <li><span class="highlight">待处理</span>：刚提交的工单，等待技术人员处理</li>
                            <li><span class="highlight">处理中</span>：技术人员正在处理的工单</li>
                            <li><span class="highlight">已解决</span>：问题已解决，等待用户确认</li>
                            <li><span class="highlight">已关闭</span>：工单已完成并关闭</li>
                        </ul>
                    </div>
                    <div class="function-item">
                        <div class="function-title">搜索功能</div>
                        <div class="function-desc">支持按工单编号、标题等条件搜索工单</div>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('manage-3-2')">
                    <span class="subsection-toggle-icon collapsed" id="manage-3-2-icon">▼</span>
                    3.2 工单操作
                </h3>
                <div class="subsection-content collapsed" id="manage-3-2-content">
                    <div class="function-item">
                        <div class="function-title">查看详情</div>
                        <div class="function-desc">点击"查看"按钮可以查看工单的详细信息和处理进度</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">回复工单</div>
                        <div class="function-desc">可以对工单进行回复，与技术人员进行沟通</div>
                        <div class="function-usage">使用方法：点击"回复"按钮，输入回复内容</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">修改工单</div>
                        <div class="function-desc">在工单未被处理前，可以修改工单内容</div>
                        <div class="function-usage">注意：工单进入处理状态后无法修改</div>
                    </div>
                </div>
            </div>

            <h2 class="section-toggle" onclick="toggleSection('track')">
                <span class="toggle-icon collapsed" id="track-icon">▼</span>
                四、工单跟踪功能
            </h2>
            <div class="feature-section section-content collapsed" id="track-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('track-4-1')">
                    <span class="subsection-toggle-icon collapsed" id="track-4-1-icon">▼</span>
                    4.1 状态跟踪
                </h3>
                <div class="subsection-content collapsed" id="track-4-1-content">
                    <div class="function-item">
                        <div class="function-title">实时状态</div>
                        <div class="function-desc">工单状态会实时更新，可以随时查看处理进度</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">处理历史</div>
                        <div class="function-desc">查看工单的完整处理历史和回复记录</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">时间记录</div>
                        <div class="function-desc">记录工单创建时间、处理时间、解决时间等关键节点</div>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('track-4-2')">
                    <span class="subsection-toggle-icon collapsed" id="track-4-2-icon">▼</span>
                    4.2 通知提醒
                </h3>
                <div class="subsection-content collapsed" id="track-4-2-content">
                    <div class="function-item">
                        <div class="function-title">状态变更通知</div>
                        <div class="function-desc">工单状态发生变更时会收到系统通知</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">回复通知</div>
                        <div class="function-desc">技术人员回复工单时会收到通知提醒</div>
                    </div>
                </div>
            </div>

            <h2 class="section-toggle" onclick="toggleSection('tips')">
                <span class="toggle-icon collapsed" id="tips-icon">▼</span>
                五、使用技巧与注意事项
            </h2>
            <div class="feature-section section-content collapsed" id="tips-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('tips-5-1')">
                    <span class="subsection-toggle-icon collapsed" id="tips-5-1-icon">▼</span>
                    5.1 工单编写技巧
                </h3>
                <div class="subsection-content collapsed" id="tips-5-1-content">
                    <div class="step-list">
                        <div class="step-item">
                            <strong>明确问题</strong>：清楚描述遇到的具体问题或需求
                        </div>
                        <div class="step-item">
                            <strong>提供截图</strong>：上传相关截图有助于技术人员快速定位问题
                        </div>
                        <div class="step-item">
                            <strong>详细步骤</strong>：描述重现问题的具体操作步骤
                        </div>
                        <div class="step-item">
                            <strong>环境信息</strong>：提供浏览器版本、操作系统等环境信息
                        </div>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('tips-5-2')">
                    <span class="subsection-toggle-icon collapsed" id="tips-5-2-icon">▼</span>
                    5.2 注意事项
                </h3>
                <div class="subsection-content collapsed" id="tips-5-2-content">
                    <div class="function-item">
                        <div class="function-title">工单权限</div>
                        <div class="function-desc">用户只能查看和操作自己提交的工单</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">响应时间</div>
                        <div class="function-desc">不同优先级的工单有不同的响应时间要求</div>
                    </div>
                    <div class="warning">
                        <strong>重要提醒：</strong>请合理设置工单优先级，避免滥用紧急优先级
                    </div>
                    <div class="function-item">
                        <div class="function-title">工单关闭</div>
                        <div class="function-desc">工单解决后请及时确认，以便技术人员关闭工单</div>
                    </div>
                </div>
            </div>

            <div class="success">
                <strong>使用提示：</strong>工单系统是获得技术支持的重要渠道，详细准确的问题描述有助于快速解决问题。如有紧急问题可联系系统管理员。
            </div>
        </div>
    </div>

    <button class="expand-all-btn" onclick="toggleAllSections()">
        <span id="expandAllText">📖 展开全部</span>
    </button>

    <script>
        // 切换章节显示/隐藏
        function toggleSection(sectionId) {
            const content = document.getElementById(sectionId + '-content');
            const icon = document.getElementById(sectionId + '-icon');
            
            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                icon.classList.remove('collapsed');
            } else {
                content.classList.add('collapsed');
                icon.classList.add('collapsed');
            }
        }

        // 切换子章节显示/隐藏
        function toggleSubsection(subsectionId) {
            const content = document.getElementById(subsectionId + '-content');
            const icon = document.getElementById(subsectionId + '-icon');
            
            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                icon.classList.remove('collapsed');
            } else {
                content.classList.add('collapsed');
                icon.classList.add('collapsed');
            }
        }

        // 切换全部章节和子章节
        function toggleAllSections() {
            const sections = ['create', 'content', 'manage', 'track', 'tips'];
            const subsections = [
                'create-1-1', 'create-1-2',
                'content-2-1', 'content-2-2',
                'manage-3-1', 'manage-3-2',
                'track-4-1', 'track-4-2',
                'tips-5-1', 'tips-5-2'
            ];

            const expandAllText = document.getElementById('expandAllText');
            const allCollapsed = sections.every(sectionId => 
                document.getElementById(sectionId + '-content').classList.contains('collapsed')
            );

            sections.forEach(sectionId => {
                const content = document.getElementById(sectionId + '-content');
                const icon = document.getElementById(sectionId + '-icon');
                
                if (allCollapsed) {
                    content.classList.remove('collapsed');
                    icon.classList.remove('collapsed');
                } else {
                    content.classList.add('collapsed');
                    icon.classList.add('collapsed');
                }
            });

            subsections.forEach(subsectionId => {
                const content = document.getElementById(subsectionId + '-content');
                const icon = document.getElementById(subsectionId + '-icon');
                
                if (allCollapsed) {
                    content.classList.remove('collapsed');
                    icon.classList.remove('collapsed');
                } else {
                    content.classList.add('collapsed');
                    icon.classList.add('collapsed');
                }
            });

            expandAllText.textContent = allCollapsed ? '📕 收起全部' : '📖 展开全部';
        }

        // 根据URL参数自动展开指定内容
        function autoExpandFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            const expandTarget = urlParams.get('expand');

            if (expandTarget) {
                // 查找包含目标文本的元素
                const allElements = document.querySelectorAll('.function-title, .subsection-toggle, .section-toggle');

                for (let element of allElements) {
                    const text = element.textContent || element.innerText;
                    if (text.includes(expandTarget)) {
                        // 找到匹配的元素，展开其所在的章节和子章节
                        expandToTarget(element);
                        // 滚动到目标位置
                        setTimeout(() => {
                            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            // 高亮显示目标元素
                            highlightElement(element);
                        }, 300);
                        break;
                    }
                }
            }
        }

        // 展开到目标元素
        function expandToTarget(targetElement) {
            let current = targetElement;

            // 向上查找并展开所有父级章节
            while (current) {
                if (current.classList && current.classList.contains('section-content')) {
                    // 展开章节
                    const sectionId = current.id.replace('-content', '');
                    const icon = document.getElementById(sectionId + '-icon');
                    if (icon && icon.classList.contains('collapsed')) {
                        toggleSection(sectionId);
                    }
                }

                if (current.classList && current.classList.contains('subsection-content')) {
                    // 展开子章节
                    const subsectionId = current.id.replace('-content', '');
                    const icon = document.getElementById(subsectionId + '-icon');
                    if (icon && icon.classList.contains('collapsed')) {
                        toggleSubsection(subsectionId);
                    }
                }

                current = current.parentElement;
            }
        }

        // 高亮显示元素
        function highlightElement(element) {
            element.style.backgroundColor = '#fff3cd';
            element.style.border = '2px solid #ffc107';
            element.style.borderRadius = '4px';
            element.style.padding = '8px';
            element.style.transition = 'all 0.3s ease';

            // 3秒后移除高亮
            setTimeout(() => {
                element.style.backgroundColor = '';
                element.style.border = '';
                element.style.borderRadius = '';
                element.style.padding = '';
            }, 3000);
        }

        // 返回功能
        function goBack() {
            const fromHelp = sessionStorage.getItem('helpFromPage');
            if (fromHelp) {
                sessionStorage.removeItem('helpFromPage');
                window.location.href = fromHelp;
            } else {
                window.location.href = 'index.html';
            }
        }

        // 页面加载时处理URL参数
        document.addEventListener('DOMContentLoaded', function() {
            autoExpandFromUrl();
        });
    </script>
</body>
</html>
