(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0e1f8f"],{"7d5a":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[r("el-form-item",{attrs:{label:"彩种名称",prop:"lotteryName"}},[r("el-input",{attrs:{placeholder:"请输入彩种名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.lotteryName,callback:function(t){e.$set(e.queryParams,"lotteryName",t)},expression:"queryParams.lotteryName"}})],1),r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.lotteryList},on:{"selection-change":e.handleSelectionChange}},[r("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),r("el-table-column",{attrs:{label:"彩种ID",align:"center",prop:"lotteryId"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-tag",{staticStyle:{"font-size":"14px","font-weight":"700",height:"32px",width:"32px","line-height":"32px",padding:"0","min-width":"32px","border-radius":"50%",display:"inline-flex","align-items":"center","justify-content":"center"},attrs:{type:"primary",effect:"dark",size:"medium"}},[e._v(e._s(t.row.lotteryId))])]}}])}),r("el-table-column",{attrs:{label:"彩种名称",align:"center",prop:"lotteryName"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-tag",{staticStyle:{"font-size":"14px","font-weight":"700",height:"36px","line-height":"36px",padding:"0 15px","min-width":"80px"},attrs:{type:"primary",effect:"dark",size:"medium"}},[e._v(e._s(t.row.lotteryName))])]}}])}),r("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["game:lottery:edit"],expression:"['game:lottery:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(r){return e.handleUpdate(t.row)}}},[e._v("修改")]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["game:lottery:remove"],expression:"['game:lottery:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(r){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),r("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":"","close-on-click-modal":!1},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[r("el-form-item",{attrs:{label:"彩种名称",prop:"lotteryName"}},[r("el-input",{attrs:{placeholder:"请输入彩种名称"},model:{value:e.form.lotteryName,callback:function(t){e.$set(e.form,"lotteryName",t)},expression:"form.lotteryName"}})],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),r("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},n=[],i=r("5530"),o=(r("d81d"),r("d3b7"),r("0643"),r("a573"),r("b775"));function l(e){return Object(o["a"])({url:"/game/lottery/list",method:"get",params:e})}function s(e){return Object(o["a"])({url:"/game/lottery/"+e,method:"get"})}function u(e){return Object(o["a"])({url:"/game/lottery",method:"post",data:e})}function c(e){return Object(o["a"])({url:"/game/lottery",method:"put",data:e})}function m(e){return Object(o["a"])({url:"/game/lottery/"+e,method:"delete"})}var d={name:"Lottery",data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,lotteryList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,lotteryName:null},form:{},rules:{lotteryName:[{required:!0,message:"彩种名称不能为空",trigger:"blur"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,l(this.queryParams).then((function(t){e.lotteryList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={lotteryId:null,lotteryName:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.lotteryId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加彩种管理"},handleUpdate:function(e){var t=this;this.reset();var r=e.lotteryId||this.ids;s(r).then((function(e){t.form=e.data,t.open=!0,t.title="修改彩种管理"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.lotteryId?c(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):u(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,r=e.lotteryId||this.ids;this.$modal.confirm('是否确认删除彩种管理编号为"'+r+'"的数据项？').then((function(){return m(r)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("game/lottery/export",Object(i["a"])({},this.queryParams),"lottery_".concat((new Date).getTime(),".xlsx"))}}},h=d,p=r("2877"),f=Object(p["a"])(h,a,n,!1,null,null,null);t["default"]=f.exports}}]);