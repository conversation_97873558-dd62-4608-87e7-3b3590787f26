{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetStatistics.vue?vue&type=template&id=090740bc&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetStatistics.vue", "mtime": 1756000107447}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750942930085}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750942928768}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}