<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>玩家管理使用说明 - 彩票系统帮助文档</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #67C23A 0%, #409EFF 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #67C23A 0%, #409EFF 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        .header h1 {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        .back-btn {
            position: absolute;
            left: 30px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-50%) translateY(-2px);
        }
        .content {
            padding: 40px;
        }
        .section-toggle {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px 20px;
            background: linear-gradient(135deg, #67C23A, #409EFF);
            color: white;
            border-radius: 12px;
            cursor: pointer;
            user-select: none;
            margin: 20px 0 10px 0;
            font-size: 18px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
        }
        .section-toggle:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(103, 194, 58, 0.4);
        }
        .toggle-icon {
            transition: transform 0.3s ease;
            font-size: 14px;
        }
        .toggle-icon.collapsed {
            transform: rotate(-90deg);
        }
        .section-content {
            max-height: 2000px;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 20px;
            padding: 20px;
        }
        .section-content.collapsed {
            max-height: 0;
            padding: 0 20px;
        }
        .subsection-toggle {
            cursor: pointer;
            user-select: none;
            display: flex;
            align-items: center;
            gap: 8px;
            color: #409EFF;
            margin-top: 20px;
            margin-bottom: 10px;
            font-size: 16px;
            font-weight: 500;
        }
        .subsection-toggle:hover {
            color: #337ecc;
        }
        .subsection-toggle-icon {
            transition: transform 0.3s ease;
            font-size: 12px;
        }
        .subsection-toggle-icon.collapsed {
            transform: rotate(-90deg);
        }
        .subsection-content {
            max-height: 500px;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        .subsection-content.collapsed {
            max-height: 0;
        }
        .function-item {
            background: white;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            border-left: 4px solid #67C23A;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        .function-title {
            font-weight: 600;
            color: #67C23A;
            margin-bottom: 8px;
            font-size: 15px;
        }
        .function-desc {
            color: #666;
            margin-bottom: 8px;
            line-height: 1.6;
        }
        .function-usage {
            color: #888;
            font-size: 14px;
            font-style: italic;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }
        .feature-card {
            background: white;
            border-radius: 8px;
            padding: 16px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .tip {
            background: #f0f9ff;
            border: 1px solid #b3d8ff;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            color: #0066cc;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            color: #856404;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            color: #155724;
        }
        .highlight {
            background: #f0f9ff;
            padding: 2px 6px;
            border-radius: 4px;
            color: #0066cc;
            font-weight: 500;
        }
        .expand-all-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, #67C23A, #409EFF);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
        }
        .expand-all-btn:hover {
            background: #529b2e;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(103, 194, 58, 0.4);
        }
        .step-list {
            counter-reset: step-counter;
        }
        .step-item {
            counter-increment: step-counter;
            position: relative;
            padding-left: 40px;
            margin-bottom: 16px;
        }
        .step-item::before {
            content: counter(step-counter);
            position: absolute;
            left: 0;
            top: 0;
            width: 24px;
            height: 24px;
            background: #67C23A;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            margin: 8px 0;
            overflow-x: auto;
        }
        ul, ol {
            padding-left: 20px;
            margin: 8px 0;
        }
        li {
            margin-bottom: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <button class="back-btn" onclick="goBack()">← 返回</button>
            <h1>👥 玩家管理使用说明</h1>
            <p>详细介绍玩家管理页面的所有功能和使用方法</p>
        </div>

        <div class="content">
            <div class="tip">
                <strong>页面概述：</strong>玩家管理页面用于管理系统中的玩家信息，包括玩家注册、信息维护、状态管理等功能。普通用户可以查看和管理自己的玩家信息。
            </div>

            <h2 class="section-toggle" onclick="toggleSection('search')">
                <span class="toggle-icon collapsed" id="search-icon">▼</span>
                一、搜索功能区域
            </h2>
            <div class="feature-section section-content collapsed" id="search-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('search-1-1')">
                    <span class="subsection-toggle-icon collapsed" id="search-1-1-icon">▼</span>
                    1.1 玩家名称搜索
                </h3>
                <div class="subsection-content collapsed" id="search-1-1-content">
                    <div class="function-item">
                        <div class="function-title">名称输入框</div>
                        <div class="function-desc">支持输入玩家名称进行模糊搜索</div>
                        <div class="function-usage">使用方法：在搜索框中输入玩家名称，按回车或点击搜索按钮</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">快速清空</div>
                        <div class="function-desc">点击输入框右侧的清空按钮可快速清除搜索条件</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">重置功能</div>
                        <div class="function-desc">点击重置按钮可清空所有搜索条件并重新加载数据</div>
                    </div>
                </div>
            </div>

            <h2 class="section-toggle" onclick="toggleSection('toolbar')">
                <span class="toggle-icon collapsed" id="toolbar-icon">▼</span>
                二、工具栏功能
            </h2>
            <div class="feature-section section-content collapsed" id="toolbar-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('toolbar-2-1')">
                    <span class="subsection-toggle-icon collapsed" id="toolbar-2-1-icon">▼</span>
                    2.1 新增玩家
                </h3>
                <div class="subsection-content collapsed" id="toolbar-2-1-content">
                    <div class="function-item">
                        <div class="function-title">新增玩家按钮</div>
                        <div class="function-desc">点击可打开新增玩家弹窗，创建新的玩家账户</div>
                        <div class="function-usage">使用方法：点击"新增玩家"按钮，填写玩家信息后提交</div>
                    </div>
                    <div class="step-list">
                        <div class="step-item">输入玩家名称（必填项）</div>
                        <div class="step-item">设置总投注金额（可选）</div>
                        <div class="step-item">设置总中奖金额（可选）</div>
                        <div class="step-item">确认信息无误后点击创建</div>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('toolbar-2-2')">
                    <span class="subsection-toggle-icon collapsed" id="toolbar-2-2-icon">▼</span>
                    2.2 批量操作
                </h3>
                <div class="subsection-content collapsed" id="toolbar-2-2-content">
                    <div class="function-item">
                        <div class="function-title">批量删除</div>
                        <div class="function-desc">选择多个玩家后可进行批量删除操作</div>
                        <div class="function-usage">使用方法：勾选要删除的玩家，点击"批量删除"按钮</div>
                    </div>
                    <div class="warning">
                        <strong>注意：</strong>删除玩家会同时删除相关的投注记录，操作不可恢复
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('toolbar-2-3')">
                    <span class="subsection-toggle-icon collapsed" id="toolbar-2-3-icon">▼</span>
                    2.3 数据导出
                </h3>
                <div class="subsection-content collapsed" id="toolbar-2-3-content">
                    <div class="function-item">
                        <div class="function-title">导出Excel</div>
                        <div class="function-desc">将当前筛选条件下的玩家数据导出为Excel文件</div>
                        <div class="function-usage">使用方法：设置筛选条件后点击"导出数据"按钮</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">导出内容</div>
                        <div class="function-desc">包含玩家名称、投注统计、中奖统计等信息</div>
                    </div>
                </div>
            </div>

            <h2 class="section-toggle" onclick="toggleSection('table')">
                <span class="toggle-icon collapsed" id="table-icon">▼</span>
                三、数据表格功能
            </h2>
            <div class="feature-section section-content collapsed" id="table-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('table-3-1')">
                    <span class="subsection-toggle-icon collapsed" id="table-3-1-icon">▼</span>
                    3.1 表格显示
                </h3>
                <div class="subsection-content collapsed" id="table-3-1-content">
                    <div class="function-item">
                        <div class="function-title">玩家信息展示</div>
                        <div class="function-desc">表格显示玩家名称、投注统计、中奖统计等详细信息</div>
                    </div>
                    <div class="feature-grid">
                        <div class="feature-card">
                            <div class="function-title">👤 玩家名称</div>
                            <div class="function-desc">显示玩家的昵称或用户名</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">💰 总投注金额</div>
                            <div class="function-desc">显示玩家累计投注金额</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">🏆 总中奖金额</div>
                            <div class="function-desc">显示玩家累计中奖金额</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">📊 盈亏统计</div>
                            <div class="function-desc">自动计算投注与中奖的差额</div>
                        </div>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('table-3-2')">
                    <span class="subsection-toggle-icon collapsed" id="table-3-2-icon">▼</span>
                    3.2 操作功能
                </h3>
                <div class="subsection-content collapsed" id="table-3-2-content">
                    <div class="function-item">
                        <div class="function-title">编辑玩家</div>
                        <div class="function-desc">点击编辑按钮可修改玩家信息</div>
                        <div class="function-usage">使用方法：点击操作列的"修改"按钮</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">删除玩家</div>
                        <div class="function-desc">删除单个玩家记录</div>
                        <div class="function-usage">使用方法：点击操作列的"删除"按钮</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">多选功能</div>
                        <div class="function-desc">通过复选框选择多个玩家进行批量操作</div>
                    </div>
                </div>
            </div>

            <h2 class="section-toggle" onclick="toggleSection('dialog')">
                <span class="toggle-icon collapsed" id="dialog-icon">▼</span>
                四、玩家信息弹窗
            </h2>
            <div class="feature-section section-content collapsed" id="dialog-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('dialog-4-1')">
                    <span class="subsection-toggle-icon collapsed" id="dialog-4-1-icon">▼</span>
                    4.1 基本信息
                </h3>
                <div class="subsection-content collapsed" id="dialog-4-1-content">
                    <div class="function-item">
                        <div class="function-title">玩家名称</div>
                        <div class="function-desc">设置玩家的显示名称，必填项</div>
                        <div class="function-usage">要求：不能为空，建议使用易识别的名称</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">数据统计</div>
                        <div class="function-desc">可以手动设置或调整玩家的投注和中奖统计</div>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('dialog-4-2')">
                    <span class="subsection-toggle-icon collapsed" id="dialog-4-2-icon">▼</span>
                    4.2 操作按钮
                </h3>
                <div class="subsection-content collapsed" id="dialog-4-2-content">
                    <div class="function-item">
                        <div class="function-title">保存修改</div>
                        <div class="function-desc">确认修改玩家信息</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">创建玩家</div>
                        <div class="function-desc">确认创建新的玩家账户</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">取消操作</div>
                        <div class="function-desc">关闭弹窗并放弃当前操作</div>
                    </div>
                </div>
            </div>

            <h2 class="section-toggle" onclick="toggleSection('tips')">
                <span class="toggle-icon collapsed" id="tips-icon">▼</span>
                五、使用技巧与注意事项
            </h2>
            <div class="feature-section section-content collapsed" id="tips-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('tips-5-1')">
                    <span class="subsection-toggle-icon collapsed" id="tips-5-1-icon">▼</span>
                    5.1 玩家管理技巧
                </h3>
                <div class="subsection-content collapsed" id="tips-5-1-content">
                    <div class="function-item">
                        <div class="function-title">名称规范</div>
                        <div class="function-desc">建议使用有意义的玩家名称，便于识别和管理</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">数据准确性</div>
                        <div class="function-desc">投注和中奖金额会影响统计报表，请确保数据准确</div>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('tips-5-2')">
                    <span class="subsection-toggle-icon collapsed" id="tips-5-2-icon">▼</span>
                    5.2 权限说明
                </h3>
                <div class="subsection-content collapsed" id="tips-5-2-content">
                    <div class="function-item">
                        <div class="function-title">普通用户权限</div>
                        <div class="function-desc">普通用户只能查看和管理自己创建的玩家</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">数据隔离</div>
                        <div class="function-desc">系统会自动过滤，确保用户只能操作自己的数据</div>
                    </div>
                    <div class="tip">
                        <strong>权限提示：</strong>如果看不到某些功能按钮，说明当前用户权限不足
                    </div>
                </div>
            </div>

            <div class="success">
                <strong>使用提示：</strong>玩家管理是投注系统的基础功能，建议先创建玩家账户再进行投注操作。如遇问题可联系系统管理员。
            </div>
        </div>
    </div>

    <button class="expand-all-btn" onclick="toggleAllSections()">
        <span id="expandAllText">📖 展开全部</span>
    </button>

    <script>
        // 切换章节显示/隐藏
        function toggleSection(sectionId) {
            const content = document.getElementById(sectionId + '-content');
            const icon = document.getElementById(sectionId + '-icon');
            
            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                icon.classList.remove('collapsed');
            } else {
                content.classList.add('collapsed');
                icon.classList.add('collapsed');
            }
        }

        // 切换子章节显示/隐藏
        function toggleSubsection(subsectionId) {
            const content = document.getElementById(subsectionId + '-content');
            const icon = document.getElementById(subsectionId + '-icon');
            
            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                icon.classList.remove('collapsed');
            } else {
                content.classList.add('collapsed');
                icon.classList.add('collapsed');
            }
        }

        // 切换全部章节和子章节
        function toggleAllSections() {
            const sections = ['search', 'toolbar', 'table', 'dialog', 'tips'];
            const subsections = [
                // 第一章节：搜索功能区域
                'search-1-1',
                // 第二章节：工具栏功能
                'toolbar-2-1', 'toolbar-2-2', 'toolbar-2-3',
                // 第三章节：数据表格功能
                'table-3-1', 'table-3-2',
                // 第四章节：玩家信息弹窗
                'dialog-4-1', 'dialog-4-2',
                // 第五章节：使用技巧与注意事项
                'tips-5-1', 'tips-5-2'
            ];

            const expandAllText = document.getElementById('expandAllText');
            const allCollapsed = sections.every(sectionId => 
                document.getElementById(sectionId + '-content').classList.contains('collapsed')
            );

            sections.forEach(sectionId => {
                const content = document.getElementById(sectionId + '-content');
                const icon = document.getElementById(sectionId + '-icon');
                
                if (allCollapsed) {
                    content.classList.remove('collapsed');
                    icon.classList.remove('collapsed');
                } else {
                    content.classList.add('collapsed');
                    icon.classList.add('collapsed');
                }
            });

            subsections.forEach(subsectionId => {
                const content = document.getElementById(subsectionId + '-content');
                const icon = document.getElementById(subsectionId + '-icon');
                
                if (allCollapsed) {
                    content.classList.remove('collapsed');
                    icon.classList.remove('collapsed');
                } else {
                    content.classList.add('collapsed');
                    icon.classList.add('collapsed');
                }
            });

            expandAllText.textContent = allCollapsed ? '📕 收起全部' : '📖 展开全部';
        }

        // 根据URL参数自动展开指定内容
        function autoExpandFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            const expandTarget = urlParams.get('expand');

            if (expandTarget) {
                // 查找包含目标文本的元素
                const allElements = document.querySelectorAll('.function-title, .subsection-toggle, .section-toggle');

                for (let element of allElements) {
                    const text = element.textContent || element.innerText;
                    if (text.includes(expandTarget)) {
                        // 找到匹配的元素，展开其所在的章节和子章节
                        expandToTarget(element);
                        // 滚动到目标位置
                        setTimeout(() => {
                            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            // 高亮显示目标元素
                            highlightElement(element);
                        }, 300);
                        break;
                    }
                }
            }
        }

        // 展开到目标元素
        function expandToTarget(targetElement) {
            let current = targetElement;

            // 向上查找并展开所有父级章节
            while (current) {
                if (current.classList && current.classList.contains('section-content')) {
                    // 展开章节
                    const sectionId = current.id.replace('-content', '');
                    const icon = document.getElementById(sectionId + '-icon');
                    if (icon && icon.classList.contains('collapsed')) {
                        toggleSection(sectionId);
                    }
                }

                if (current.classList && current.classList.contains('subsection-content')) {
                    // 展开子章节
                    const subsectionId = current.id.replace('-content', '');
                    const icon = document.getElementById(subsectionId + '-icon');
                    if (icon && icon.classList.contains('collapsed')) {
                        toggleSubsection(subsectionId);
                    }
                }

                current = current.parentElement;
            }
        }

        // 高亮显示元素
        function highlightElement(element) {
            element.style.backgroundColor = '#fff3cd';
            element.style.border = '2px solid #ffc107';
            element.style.borderRadius = '4px';
            element.style.padding = '8px';
            element.style.transition = 'all 0.3s ease';

            // 3秒后移除高亮
            setTimeout(() => {
                element.style.backgroundColor = '';
                element.style.border = '';
                element.style.borderRadius = '';
                element.style.padding = '';
            }, 3000);
        }

        // 返回功能
        function goBack() {
            const fromHelp = sessionStorage.getItem('helpFromPage');
            if (fromHelp) {
                sessionStorage.removeItem('helpFromPage');
                window.location.href = fromHelp;
            } else {
                window.location.href = 'index.html';
            }
        }

        // 页面加载时处理URL参数
        document.addEventListener('DOMContentLoaded', function() {
            autoExpandFromUrl();
        });
    </script>
</body>
</html>
