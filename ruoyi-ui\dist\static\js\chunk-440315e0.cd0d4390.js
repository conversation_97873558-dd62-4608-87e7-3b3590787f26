(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-440315e0","chunk-2d22e11b","chunk-2d22e11b","chunk-2d22e11b","chunk-2d22e11b"],{f398:function(e,n,t){"use strict";t.r(n),t.d(n,"SINGLE_DOUBLE_PATTERNS",(function(){return u}));var r=t("2909"),I=t("b85c"),_=t("3835"),o=(t("99af"),t("4de4"),t("7db0"),t("a630"),t("caad"),t("a15b"),t("d81d"),t("14d9"),t("13d5"),t("4ec9"),t("b680"),t("b64b"),t("d3b7"),t("4d63"),t("c607"),t("ac1f"),t("2c3e"),t("00b4"),t("25f0"),t("8a79"),t("2532"),t("3ca3"),t("466d"),t("5319"),t("1276"),t("498a"),t("0643"),t("2382"),t("fffc"),t("4e3e"),t("a573"),t("9d4a"),t("159b"),t("ddb0"),t("f9d6")),u={BAODA_ZUSAN:{pattern:/^包打?组三(?:-(\d+))?$/,handler:function(e,n){var t=e[1]||"",r=(Object(o["parseLotteryType"])(n),[{methodId:o["METHOD_ID"].BAODA_ZUSAN,betNumbers:"",money:t}]);return console.log("包打组三 handler 返回:",r),r}},HEZHI_MULTI:{pattern:/^和值(?:(\d+(?:\/\d+)*?)各(\d+)|((?:\d+-\d+\/)*\d+-\d+))$/,handler:function(e,n){Object(o["parseLotteryType"])(n);if(e[1]){var t=e[1].split("/"),r=e[2],I=t.map((function(e){return{methodId:o["METHOD_ID"].HEZHI,hezhi:parseInt(e),money:r}}));return console.log("和值多个 handler 返回:",I),I}var u=e[3].split("/").map((function(e){var n=e.split("-"),t=Object(_["a"])(n,2),r=t[0],I=t[1];return{number:r,money:I}})),a=u.map((function(e){return{methodId:o["METHOD_ID"].HEZHI,hezhi:parseInt(e.number),money:e.money}}));return console.log("和值多个 handler 返回:",a),a}},HEZHI_SINGLE_DA_SUPER:{pattern:/^(和值?|和)(\d{1,2})[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s+吊赶打各]+(\d+)$/,handler:function(e,n){var r=t("f9d6"),I=r.getHezhiMethodId,_=r.parseLotteryType,o=r.METHOD_ID,u=e[2],a=e[3],A=(_(n),{methodId:I(u,o),hezhi:parseInt(u),money:a});return console.log("和值单个打（超级分隔符）handler 返回:",A),[A]}},HEZHI_MULTI_EACH_DA_SUPER:{pattern:/^(和值?|和)((?:\d{1,2}[\-—~～@#￥%…&一－。 !、\\/*,，.＝=·\s+吊赶打各\/\\]*)+)[各\-—~～@#￥%…&!、\\/*,一－。，.＝=·\s+吊赶打]+(\d+)$/,handler:function(e,n){var r=t("f9d6"),I=r.getHezhiMethodId,_=r.SUPER_SEPARATORS,o=r.parseLotteryType,u=r.METHOD_ID,a=e[2].split(_).map((function(e){return e.trim()})).filter(Boolean),A=e[3],s=(o(n),a.map((function(e){return{methodId:I(e,u),hezhi:parseInt(e),money:A}})));return console.log("和值各打（超级分隔符）handler 返回:",s),s}},PAO_DA_MONEY:{pattern:/^炮打(\d+)(元|米|块)?$/,handler:function(e,n){var t=["000","111","222","333","444","555","666","777","888","999"];return[{methodId:o["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:"1"}]}},PAO_GE_DA_MONEY:{pattern:/^炮各打(\d+)(元|米|块)?$/,handler:function(e,n){var t=["000","111","222","333","444","555","666","777","888","999"],r=e[1];return[{methodId:o["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:r}]}},PAO_GE_MONEY:{pattern:/^炮各(\d+)(元|米|块)?$/,handler:function(e,n){var t=["000","111","222","333","444","555","666","777","888","999"],r=e[1];return[{methodId:o["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:r}]}},PAO_MONEY:{pattern:/^炮(\d+)(元|米|块)?$/,handler:function(e,n){var t=["000","111","222","333","444","555","666","777","888","999"];return[{methodId:o["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:"1"}]}},PAO_GE_MONEY_GONG:{pattern:/^炮各(\d+)(元|米|块)?共\d+$/,handler:function(e,n){var t=["000","111","222","333","444","555","666","777","888","999"],r=e[1];return[{methodId:o["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:r}]}},TONGPAO_ALLPAO_FANGPAO_MONEY:{pattern:/^(通炮|全炮|防炮)(\d+)(元|米|块)?$/,handler:function(e,n){var t=["000","111","222","333","444","555","666","777","888","999"],r=(parseFloat(e[2])/10).toFixed(1).replace(/\.0$/,"");return[{methodId:o["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:r}]}},TONGPAO_ALLPAO_FANGPAO_GE_MONEY:{pattern:/^(通炮|全炮|防炮)各(\d+)(元|米|块)?$/,handler:function(e,n){var t=["000","111","222","333","444","555","666","777","888","999"],r=e[2];return[{methodId:o["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:r}]}},PAO_09_SLASH_GE_MONEY:{pattern:/^炮0-9[\/][\s]*各?(\d+)(元|米|块)?$/,handler:function(e,n){var t=["000","111","222","333","444","555","666","777","888","999"],r=e[1];return[{methodId:o["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:r}]}},PAO_09_KONG_GE_MONEY:{pattern:/^0[\s]*[-到~—]?[\s]*9[\s]*炮[\s]*各?(\d+)(元|米|块)?$/,handler:function(e,n){var t=["000","111","222","333","444","555","666","777","888","999"],r=e[1];return[{methodId:o["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:r}]}},KONGPAO_GE_MONEY:{pattern:/^空炮[各个]?(\d+)(元|米|块)?$/,handler:function(e,n){var t=["000","111","222","333","444","555","666","777","888","999"],r=e[1];return[{methodId:o["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:r}]}},BAOZI_MULTI_EACH_MONEY:{pattern:/^((?:\d{3}[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*)+)豹子各?(\d+)(元|米|块)?$/,handler:function(e,n){var t=e[1].split(/[\-—~～@#￥%…&!、\\/*,，.＝=·\s]+/).map((function(e){return e.trim()})).filter((function(e){return 3===e.length&&e[0]===e[1]&&e[1]===e[2]})),r=e[2];return[{methodId:o["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:r}]}},BAOZI_MULTI_MONEY:{pattern:/^((?:\d{3}[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*)+)豹子(\d+)(元|米|块)?$/,handler:function(e,n){var t=e[1].split(/[\-—~～@#￥%…&!、\\/*,，.＝=·\s]+/).map((function(e){return e.trim()})).filter((function(e){return 3===e.length&&e[0]===e[1]&&e[1]===e[2]}));return[{methodId:o["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:"1"}]}},PAO_SUPERSEP_MONEY:{pattern:/^炮[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s.。·、]+(\d+)(元|米|块)?$/,handler:function(e,n){var t=["000","111","222","333","444","555","666","777","888","999"];return[{methodId:o["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:"1"}]}},QUANPAO_MONEY:{pattern:/^全炮(各)?(\d+)(元|米|块)?$/,handler:function(e,n){var t=["000","111","222","333","444","555","666","777","888","999"],r=e[2];return[{methodId:o["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:r}]}},BAOZI_ALL_MONEY:{pattern:/^0到9豹子全包(\d+)(?:元|米|块)?$/,handler:function(e,n){var t=["000","111","222","333","444","555","666","777","888","999"],r=e[1];return/各/.test(n)||(r=(parseFloat(r)/10).toFixed(1).replace(/\.0$/,"")),[{methodId:o["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:r}]}},PAO_OTHER:{pattern:/^炮.*?各(\d+)(?:元|米|块)?$/,handler:function(e,n){var t=["000","111","222","333","444","555","666","777","888","999"];return[{methodId:o["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:e[1]}]}},BAODA_ZUSAN_VARIANTS:{pattern:/^(包?对子|对子|对吊|对一|对)[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([0-9]+)(元|块|米)?$/,handler:function(e,n){var t=e[2],r={"一":1,"二":2,"三":3,"四":4,"五":5,"六":6,"七":7,"八":8,"九":9,"十":10,"百":100,"千":1e3,"万":1e4},I=t;return/^[一二三四五六七八九十百千万]+$/.test(t)&&(I=t.split("").reduce((function(e,n){return e+(r[n]||0)}),0)||"1"),/^\d+$/.test(t)&&(I=t),I||(I="1"),[{methodId:o["METHOD_ID"].BAODA_ZUSAN,betNumbers:"",money:I}]}},HEZHI_SIMPLE:{pattern:/^(?:福|福彩|3D|体|体彩|排三|排)?和([\d/\-、,，.。\s]+)[/\-、,，.。\s]+(\d+)(元|块|米)?$/,handler:function(e,n){var r=t("f9d6"),I=r.getHezhiMethodId,_=r.METHOD_ID,o=r.parseLotteryType,u=e[1],a=e[2],A=(o(n),u.split(/[\/\-、,，.。\s]+/).map((function(e){return e.trim()})).filter(Boolean));return A.map((function(e){return{methodId:I(e,_),hezhi:parseInt(e),money:a}}))}},BAO_ZULIU_ALL:{pattern:/^(包打?组六|组六)[\-—~～@#￥%…&!、\\/*,一－。＝=·吊赶打各\s]*([0-9]+)$/,handler:function(e,n){var t=e[2]||"";Object(o["parseLotteryType"])(n);return[{methodId:o["METHOD_ID"].BAODA_ZULIU,betNumbers:"",money:t}]}},BAO_ZUSAN_ALL:{pattern:/^(包打?组三|组三)[\-—~～@#￥%…&!、\\/*,一－。＝=·吊赶打各\s]*([0-9]+)$/,handler:function(e,n){var t=e[2]||"";Object(o["parseLotteryType"])(n);return[{methodId:o["METHOD_ID"].BAODA_ZUSAN,betNumbers:"",money:t}]}},SHA_MA_SUPER:{pattern:/^杀([0-9]+)[\-—~～@#￥%…&!、\\/*,一－。.＝=·吊赶打各\s]+([0-9]+)[\-—~～@#￥%…&!、\\/*,一－。.＝=·吊赶打各\s]*$/,handler:function(e,n){var t=e[1].split("").join(","),r=e[2];return[{methodId:o["METHOD_ID"].SHA_MA,betNumbers:t,money:r}]}},HEZHI_DA_SUPER:{pattern:/^[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*(?:和值?|和)?[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*大[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([0-9]+)$/,handler:function(e,n){var t=e[1];Object(o["parseLotteryType"])(n);return[{methodId:o["METHOD_ID"].HEZHI_DAXIAO,daxiao:300,money:t,betNumbers:null}]}},HEZHI_XIAO_SUPER:{pattern:/^[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*(?:和值?|和)?[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*小[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([0-9]+)$/,handler:function(e,n){var t=e[1];Object(o["parseLotteryType"])(n);return[{methodId:o["METHOD_ID"].HEZHI_DAXIAO,daxiao:301,money:t,betNumbers:null}]}},HEZHI_DAN_SUPER:{pattern:/^[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*(?:和值?|和)?[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*单[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([0-9]+)$/,handler:function(e,n){var t=e[1];Object(o["parseLotteryType"])(n);return[{methodId:o["METHOD_ID"].HEZHI_DANSHUAN,danshuang:200,money:t,betNumbers:null}]}},HEZHI_SHUANG_SUPER:{pattern:/^[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*(?:和值?|和)?[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*双[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([0-9]+)$/,handler:function(e,n){var t=e[1];Object(o["parseLotteryType"])(n);return[{methodId:o["METHOD_ID"].HEZHI_DANSHUAN,danshuang:201,money:t,betNumbers:null}]}},BAOZI_MULTI_LINE_EACH_MONEY:{pattern:/^([\s\S]+)$/m,handler:function(e,n){var o,u=n.split(/\n+/).map((function(e){return e.trim()})).filter(Boolean),a=new Map,A=[],s=/^([0-9]{1,9})[ \t]*(豹子|炮)[ \t]*各([0-9]+)(元|米|块)?[^\d]*$/,l=Object(I["a"])(u);try{for(l.s();!(o=l.n()).done;){var d=o.value,N=d.match(s);if(!N)return console.log('【BAOZI_MULTI_LINE_EACH_MONEY】行 "'.concat(d,'" 不匹配豹子/炮格式，返回null')),null;A.push({line:d,match:N})}}catch(f){l.e(f)}finally{l.f()}console.log("【BAOZI_MULTI_LINE_EACH_MONEY】所有 ".concat(A.length," 行都是豹子/炮格式，继续处理"));for(var i=0,c=A;i<c.length;i++){var E,D=c[i].match,O=D[1].split("").filter((function(e){return/[0-9]/.test(e)})),m=D[3];a.has(m)||a.set(m,[]),(E=a.get(m)).push.apply(E,Object(r["a"])(O.map((function(e){return e+e+e}))))}if(0===a.size)return console.log("【BAOZI_MULTI_LINE_EACH_MONEY】没有有效的豹子/炮内容，返回null"),null;var H=Array.from(a.entries()).map((function(e){var n=Object(_["a"])(e,2),r=n[0],I=n[1];return{methodId:t("f9d6").METHOD_ID.SANMA_ZHIXUAN,betNumbers:I.join(","),money:r}}));return console.log("【BAOZI_MULTI_LINE_EACH_MONEY】处理完成，返回 ".concat(H.length," 个结果组")),H}},YIMA_DANSHUAN_SUPER:{pattern:/^(百位|百|十位|十|个位|个)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*(单|双)(\d+)(元|米|块)?$/,handler:function(e,n){var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},I=e[1],_=e[2],o=e[3];return[{methodId:t("f9d6").METHOD_ID.YIMA_DANSHUAN,dingwei:r[I],betNumbers:_,money:o}]}},YIMA_DAXIAO_SUPER:{pattern:/^(百位|百|十位|十|个位|个)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*(大|小)(\d+)(元|米|块)?$/,handler:function(e,n){var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},I=e[1],_=e[2],o=e[3];return[{methodId:t("f9d6").METHOD_ID.YIMA_DAXIAO,dingwei:r[I],betNumbers:_,money:o}]}},SPECIAL_FU_PAI_PAO:{pattern:/[。.,、\\/*\s\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([福3d3D福彩]+)[炮豹子]+(\d+)(元|米|块)?[。.,、\\/*\\s\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([排排三体体彩]+)[炮豹子]+(\d+)(元|米|块)?/,handler:function(e,n){var r=["000","111","222","333","444","555","666","777","888","999"],I=e[2],_=e[5],o=1,u=2;return[{methodId:t("f9d6").METHOD_ID.SANMA_ZHIXUAN,betNumbers:r.join(","),money:(parseFloat(I)/10).toString(),lotteryId:o},{methodId:t("f9d6").METHOD_ID.SANMA_ZHIXUAN,betNumbers:r.join(","),money:(parseFloat(_)/10).toString(),lotteryId:u}]}},SPECIAL_FU_PAI_PAO_EACH:{pattern:/[。.,、\\/*\s\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([福3d3D福彩]+)[炮豹子]+各(\d+)(元|米|块)?[。.,、\\/*\s\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([排排三体体彩]+)[炮豹子]+各(\d+)(元|米|块)?/,handler:function(e,n){var r=["000","111","222","333","444","555","666","777","888","999"],I=e[2],_=e[5],o=1,u=2;return[{methodId:t("f9d6").METHOD_ID.SANMA_ZHIXUAN,betNumbers:r.join(","),money:I,lotteryId:o},{methodId:t("f9d6").METHOD_ID.SANMA_ZHIXUAN,betNumbers:r.join(","),money:_,lotteryId:u}]}},YIMA_DINGWEI_FORMAT1_NEW:{pattern:/^(百位?|百)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*([0-9]+(?:[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*[0-9]+)*)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*(十位?|十)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*([0-9]+(?:[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*[0-9]+)*)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*(个位?|个)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*([0-9]+(?:[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*[0-9]+)*)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*各[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*([0-9]+)(?:元|块|快|米)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*(一码定位|一码|定)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*$/,handler:function(e,n){if(!e[1]||!e[3]||!e[5])return null;if(!e[8])return null;var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},I=(e[2].match(/[0-9]/g)||[]).join(","),_=(e[4].match(/[0-9]/g)||[]).join(","),o=(e[6].match(/[0-9]/g)||[]).join(","),u=e[7];return[{methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[e[1]],betNumbers:I,money:u},{methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[e[3]],betNumbers:_,money:u},{methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[e[5]],betNumbers:o,money:u}]}},YIMA_DINGWEI_FORMAT2_NEW:{pattern:/^([\s\S]+)$/m,handler:function(e,n){if(!/(?:百位?|百)|(?:十位?|十)|(?:个位?|个)/.test(n))return null;if(!/一码定位|一码|定/.test(n))return null;var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},_=n.split(/\n+/).map((function(e){return e.trim()})).filter(Boolean),o="",u={},a=/^(百位?|百|十位?|十|个位?|个)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*([0-9]+(?:[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*[0-9]+)*)/,A=/各[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*([0-9]+)(?:元|块|快|米)?/,s=/一码定位|一码|定/,l=n.match(A);l&&(o=l[1]);var d,N=Object(I["a"])(_);try{for(N.s();!(d=N.n()).done;){var i=d.value;if(!A.test(i)&&!s.test(i)){var c=i.match(a);if(c){var E=c[1],D=c[2],O=D.match(/[0-9]/g);O&&O.length>0&&(u[E]=O.join(","))}else if(i.trim()&&!/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*$/.test(i))return console.log('【YIMA_DINGWEI_FORMAT2_NEW】行 "'.concat(i,'" 不匹配一码定位格式，返回null')),null}}}catch(U){N.e(U)}finally{N.f()}var m=Object.keys(u).find((function(e){return e.includes("百")})),H=Object.keys(u).find((function(e){return e.includes("十")})),f=Object.keys(u).find((function(e){return e.includes("个")}));return m&&H&&f?(console.log("【YIMA_DINGWEI_FORMAT2_NEW】识别成功，三个位置都有数据"),[{methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[m],betNumbers:u[m],money:o},{methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[H],betNumbers:u[H],money:o},{methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[f],betNumbers:u[f],money:o}]):(console.log("【YIMA_DINGWEI_FORMAT2_NEW】未找到完整的三个位置，返回null"),null)}},YIMA_DINGWEI_FORMAT3_NEW:{pattern:/^([\s\S]+)$/m,handler:function(e,n){if(!/(?:百位?|百)|(?:十位?|十)|(?:个位?|个)/.test(n))return null;if(!/一码定位|一码|定/.test(n))return null;var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},I=n.split(/\n+/).map((function(e){return e.trim()})).filter(Boolean);if(I.length<2)return console.log("【YIMA_DINGWEI_FORMAT3_NEW】行数不足，返回null"),null;var _=I[0],o=_.match(/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*(百位?|百)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]+(十位?|十)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]+(个位?|个)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*$/);if(!o)return console.log('【YIMA_DINGWEI_FORMAT3_NEW】表头行 "'.concat(_,'" 不匹配格式，返回null')),null;for(var u="",a=1;a<I.length;a++)if(/^[\d\-—~～@#￥%…&!、\\/*,，.＝=·\s]+$/.test(I[a])){u=I[a];break}if(!u)return console.log("【YIMA_DINGWEI_FORMAT3_NEW】未找到数字行，返回null"),null;for(var A=/各[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*([0-9]+)(?:元|块|快|米)?/,s=/一码定位|一码|定/,l=1;l<I.length;l++){var d=I[l];if(d!==u&&(!A.test(d)&&!s.test(d)&&!/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*$/.test(d)))return console.log('【YIMA_DINGWEI_FORMAT3_NEW】行 "'.concat(d,'" 不符合表格格式，返回null')),null}var N=u.split(/[\-—~～@#￥%…&!、\\/*,，.＝=·\s]+/).filter(Boolean);if(3!==N.length)return console.log("【YIMA_DINGWEI_FORMAT3_NEW】数字行分组数量不是3个，实际".concat(N.length,"个，返回null")),null;var i="",c=n.match(A);c&&(i=c[1]);var E=(N[0].match(/[0-9]/g)||[]).join(","),D=(N[1].match(/[0-9]/g)||[]).join(","),O=(N[2].match(/[0-9]/g)||[]).join(",");return console.log("【YIMA_DINGWEI_FORMAT3_NEW】表格格式识别成功"),[{methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[o[1]],betNumbers:E,money:i},{methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[o[2]],betNumbers:D,money:i},{methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[o[3]],betNumbers:O,money:i}]}},YIMA_DINGWEI_TABLE_STANDARD:{pattern:/^([\s\S]+)$/m,handler:function(e,n){if(!/(?:百位?|百)|(?:十位?|十)|(?:个位?|个)/.test(n))return null;if(!/一码定位|一码|定/.test(n))return null;var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},I=n.split(/\n+/).map((function(e){return e.trim()})).filter(Boolean);if(I.length<2)return console.log("【YIMA_DINGWEI_TABLE_STANDARD】行数不足，返回null"),null;var _=I[0],o=_.match(/^[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*(百位?|百)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]+(十位?|十)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]+(个位?|个)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*$/);if(!o)return console.log('【YIMA_DINGWEI_TABLE_STANDARD】表头行 "'.concat(_,'" 不匹配格式，返回null')),null;for(var u="",a=1;a<I.length;a++)if(/^[\d\s\-—~～@#￥%…&!、\\/*,，.＝=·]+$/.test(I[a])){u=I[a];break}if(!u)return console.log("【YIMA_DINGWEI_TABLE_STANDARD】未找到数字行，返回null"),null;for(var A=/各[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+)(?:元|块|快|米)?/,s=/一码定位|一码|定/,l=1;l<I.length;l++){var d=I[l];if(d!==u&&(!A.test(d)&&!s.test(d)&&!/^[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*$/.test(d)))return console.log('【YIMA_DINGWEI_TABLE_STANDARD】行 "'.concat(d,'" 不符合标准表格格式，返回null')),null}var N=u.split(/[\s\-—~～@#￥%…&!、\\/*,，.＝=·]+/).filter(Boolean);if(3!==N.length)return console.log("【YIMA_DINGWEI_TABLE_STANDARD】数字行分组数量不是3个，实际".concat(N.length,"个，返回null")),null;var i="",c=n.match(A);c&&(i=c[1]);var E=(N[0].match(/[0-9]/g)||[]).join(","),D=(N[1].match(/[0-9]/g)||[]).join(","),O=(N[2].match(/[0-9]/g)||[]).join(",");return console.log("【YIMA_DINGWEI_TABLE_STANDARD】标准表格格式识别成功"),[{methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[o[1]],betNumbers:E,money:i},{methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[o[2]],betNumbers:D,money:i},{methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[o[3]],betNumbers:O,money:i}]}},YIMA_DINGWEI_SINGLE_POSITION:{pattern:/^(百位?|百|十位?|十|个位?|个)[ \t\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+(?:[ \t\-—~～@#￥%…&!、\\/*,，.＝=·]*[0-9]+)*)[ \t\-—~～@#￥%…&!、\\/*,，.＝=·]*各[ \t\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+)(?:元|块|快|米)?[ \t\-—~～@#￥%…&!、\\/*,，.＝=·]*$/,handler:function(e,n){var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},I=e[1],_=e[2],o=e[3],u=(_.match(/[0-9]/g)||[]).join(",");return[{methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[I],betNumbers:u,money:o}]}},YIMA_DINGWEI_SINGLE_POSITION_NO_GE:{pattern:/^(百位?|百|十位?|十|个位?|个)[ \t\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+(?:[ \t\-—~～@#￥%…&!、\\/*,，.＝=·]*[0-9]+)*)[ \t\-—~～@#￥%…&!、\\/*,，.＝=·]+([0-9]+)(?:元|块|快|米)*$/,handler:function(e,n){if(!/(?:百位?|百|十位?|十|个位?|个)/.test(n))return console.log('【YIMA_DINGWEI_SINGLE_POSITION_NO_GE】行 "'.concat(n,'" 不包含位置标识，返回null')),null;var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},I=e[1],_=e[2],o=e[3];if(!r[I])return console.log('【YIMA_DINGWEI_SINGLE_POSITION_NO_GE】位置标识 "'.concat(I,'" 无效，返回null')),null;var u=(_.match(/[0-9]/g)||[]).join(",");return u?(console.log("【YIMA_DINGWEI_SINGLE_POSITION_NO_GE】识别成功: 位置=".concat(I,", 号码=").concat(u,", 金额=").concat(o)),[{methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[I],betNumbers:u,money:o}]):(console.log("【YIMA_DINGWEI_SINGLE_POSITION_NO_GE】未提取到有效号码，返回null"),null)}},YIMA_DINGWEI_TWO_LINES:{pattern:/^([\s\S]+)$/m,handler:function(e,n){if(!/(?:百位?|百)|(?:十位?|十)|(?:个位?|个)/.test(n))return null;if(!/各[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+)(?:元|块|快|米)?/.test(n))return null;var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},_=n.split(/\n+/).map((function(e){return e.trim()})).filter(Boolean);if(2!==_.length)return console.log("【YIMA_DINGWEI_TWO_LINES】行数不是2行，实际".concat(_.length,"行，返回null")),null;var o="",u={},a=/^(百位?|百|十位?|十|个位?|个)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+(?:[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*[0-9]+)*)/,A=/各[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+)(?:元|块|快|米)?/,s=n.match(A);s&&(o=s[1]);var l,d=Object(I["a"])(_);try{for(d.s();!(l=d.n()).done;){var N=l.value;if(!A.test(N)){var i=N.match(a);if(i){var c=i[1],E=i[2],D=E.match(/[0-9]/g);D&&D.length>0&&(u[c]=D.join(","))}else if(N.trim()&&!/^[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*$/.test(N))return console.log('【YIMA_DINGWEI_TWO_LINES】行 "'.concat(N,'" 不匹配一码定位格式，返回null')),null}}}catch(T){d.e(T)}finally{d.f()}var O=Object.keys(u);if(2!==O.length)return console.log("【YIMA_DINGWEI_TWO_LINES】位置数量不是2个，实际".concat(O.length,"个，返回null")),null;console.log("【YIMA_DINGWEI_TWO_LINES】两行两位置格式识别成功");for(var m=[],H=0,f=O;H<f.length;H++){var U=f[H];m.push({methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[U],betNumbers:u[U],money:o})}return m}},YIMA_DINGWEI_ONE_LINE_TWO_POSITIONS:{pattern:/^(百位?|百|十位?|十|个位?|个)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+(?:[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*[0-9]+)*)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*(百位?|百|十位?|十|个位?|个)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+(?:[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*[0-9]+)*)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*各[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+)(?:元|块|快|米)?[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*$/,handler:function(e,n){var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},I=e[1],_=e[2],o=e[3],u=e[4],a=e[5];if(r[I]===r[o])return null;var A=(_.match(/[0-9]/g)||[]).join(","),s=(u.match(/[0-9]/g)||[]).join(",");return[{methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[I],betNumbers:A,money:a},{methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[o],betNumbers:s,money:a}]}}};Object.keys(u).forEach((function(e){var n=u[e].pattern;"string"===typeof n&&n.endsWith("$")?u[e].pattern=n.replace(/\$$/,"[，,./*-=。·、s]*$"):n instanceof RegExp&&n.source.endsWith("$")&&(u[e].pattern=new RegExp(n.source.replace(/\$$/,"[，,./*-=。·、s]*$")))}))},f9d6:function(e,n,t){"use strict";t.r(n),t.d(n,"BASE_PATTERNS",(function(){return I})),t.d(n,"LOTTERY_TYPE",(function(){return _})),t.d(n,"METHOD_ID",(function(){return o})),t.d(n,"SUPER_SEPARATORS",(function(){return u})),t.d(n,"cleanNumberString",(function(){return a})),t.d(n,"splitNumbers",(function(){return A})),t.d(n,"checkLotteryType",(function(){return s})),t.d(n,"parseMoney",(function(){return l})),t.d(n,"getNumberType",(function(){return d})),t.d(n,"generateTransformNumbers",(function(){return N})),t.d(n,"calculateSum",(function(){return i})),t.d(n,"parseLotteryType",(function(){return c})),t.d(n,"chineseToNumber",(function(){return E})),t.d(n,"getHezhiMethodId",(function(){return D})),t.d(n,"classifyThreeCodeNumbers",(function(){return O})),t.d(n,"getDantuoZusanMethodId",(function(){return m})),t.d(n,"getDantuoZuliuMethodId",(function(){return H})),t.d(n,"isDantuoMethod",(function(){return f})),t.d(n,"isDantuoZusan",(function(){return U})),t.d(n,"isDantuoZuliu",(function(){return T})),t.d(n,"getDantuoInfo",(function(){return M}));var r=t("b85c"),I=(t("4de4"),t("caad"),t("d81d"),t("14d9"),t("13d5"),t("fb6a"),t("d3b7"),t("ac1f"),t("00b4"),t("6062"),t("1e70"),t("79a4"),t("c1a1"),t("8b00"),t("a4e7"),t("1e5a"),t("72c3"),t("2532"),t("3ca3"),t("466d"),t("5319"),t("1276"),t("0643"),t("2382"),t("a573"),t("9d4a"),t("ddb0"),{SEPARATORS:/[,\s\/]+/,MONEY_SEPARATORS:/防对|[-买打/，组六组三~～@一－。#+￥%…&!、\\/*,，各吊 +—赶.=@&直组租值防快块元米链连\s]+/,LOTTERY_TYPES:{FC:1,TC:2},LOTTERY_PREFIX:{FUCAI:/(福|福彩|3D|福3D)/,TICAI:/(排|排三|体|体彩)/,BOTH:/(福排|福体)/},MONEY_PATTERNS:{DIRECT_GROUP:/[^0-9]*(\d+)\+(\d+)(?:块|元)?$/,SINGLE:/[^0-9]*(\d+)(?:块|元)?$/,EACH:/各(\d+)(?:块|元)?$/,VALUE_GROUP_EACH:/值组各(\d+)(?:块|元)?$/,DIRECT_GROUP_EXPLICIT:/直组(\d+)\+(\d+)(?:块|元)?$/,DIAO:/吊(\d+)(?:块|元)?$/}}),_={FC3D:1,TC3D:2},o={DUDAN:1,YIMA_DINGWEI:2,LIANGMA_ZUHE:3,LIANGMA_DUIZI:4,SANMA_ZHIXUAN:5,SANMA_ZUXUAN:6,SANMA_ZUSAN:7,SIMA_ZULIU:8,SIMA_ZUSAN:9,WUMA_ZULIU:10,WUMA_ZUSAN:11,LIUMA_ZULIU:12,LIUMA_ZUSAN:13,QIMA_ZULIU:14,QIMA_ZUSAN:15,BAMA_ZULIU:16,BAMA_ZUSAN:17,JIUMA_ZULIU:18,JIUMA_ZUSAN:19,BAODA_ZULIU:20,HEZHI_QI_ERSHI:21,HEZHI_BA_SHIJIU:22,HEZHI_JIU_SHIBA:23,HEZHI_SHI_SHIQI:24,HEZHI_SHIYI_SHILIU:25,HEZHI_SHIER_SHIWU:26,HEZHI_SHISAN_SHISI:27,HEZHI_LING_ERSHIQI:37,HEZHI_YI_ERSHILIU:38,HEZHI_ER_ERSHIWU:39,HEZHI_SAN_ERSHISI:40,HEZHI_SI_ERSHISAN:41,HEZHI_WU_ERSHIER:42,HEZHI_LIU_ERSHIYI:43,HEZHI_DAXIAO:30,HEZHI_DANSHUAN:29,BAODA_ZUSAN:31,LIANGMA_DINGWEI:34,YIMA_DANSHUAN:35,YIMA_DAXIAO:36,SHA_MA:33,SANMA_FANGDUI:100,DANTUO_ZUSAN_2:44,DANTUO_ZUSAN_3:45,DANTUO_ZUSAN_4:46,DANTUO_ZUSAN_5:47,DANTUO_ZUSAN_6:48,DANTUO_ZUSAN_7:49,DANTUO_ZUSAN_8:50,DANTUO_ZUSAN_9:51,DANTUO_ZULIU_2:52,DANTUO_ZULIU_3:53,DANTUO_ZULIU_4:54,DANTUO_ZULIU_5:55,DANTUO_ZULIU_6:56,DANTUO_ZULIU_7:57,DANTUO_ZULIU_8:58,DANTUO_ZULIU_9:59,KUADU_0:60,KUADU_1:61,KUADU_2:62,KUADU_3:63,KUADU_4:64,KUADU_5:65,KUADU_6:66,KUADU_7:67,KUADU_8:68,KUADU_9:69},u=/[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+/;function a(e){return e.replace(/[^\d]/g,"")}function A(e){return e.split(I.SEPARATORS).filter(Boolean)}function s(e){return I.LOTTERY_TYPES.FC===parseInt(e.split("")[0])?"FC":I.LOTTERY_TYPES.TC===parseInt(e.split("")[0])?"TC":"UNKNOWN"}function l(e){var n=I.MONEY_PATTERNS.DIRECT_GROUP.exec(e);if(n)return{direct:parseInt(n[1]),group:parseInt(n[2])};if(n=I.MONEY_PATTERNS.VALUE_GROUP_EACH.exec(e),n){var t=parseInt(n[1]);return{direct:t,group:t}}if(n=I.MONEY_PATTERNS.DIRECT_GROUP_EXPLICIT.exec(e),n)return{direct:parseInt(n[1]),group:parseInt(n[2])};if(n=I.MONEY_PATTERNS.EACH.exec(e),n){var r=parseInt(n[1]);return{single:r}}return n=I.MONEY_PATTERNS.DIAO.exec(e),n?{single:parseInt(n[1])}:(n=I.MONEY_PATTERNS.SINGLE.exec(e),n?{single:parseInt(n[1])}:null)}function d(e){var n=e.length,t=new Set(e.split("")).size;return{length:n,isAllSame:1===t,uniqueDigits:t}}function N(e){var n="0123456789",t=e.split(""),r=n.split("").filter((function(e){return!t.includes(e)}));return r.map((function(n){return e+n}))}function i(e){return e.split("").reduce((function(e,n){return e+parseInt(n,10)}),0)}function c(e){var n=e.match(I.LOTTERY_PREFIX.FUCAI);return n?{type:I.LOTTERY_TYPES.FC,id:1,line:e.slice(n[0].length)}:(n=e.match(I.LOTTERY_PREFIX.TICAI),n?{type:I.LOTTERY_TYPES.TC,id:2,line:e.slice(n[0].length)}:(n=e.match(I.LOTTERY_PREFIX.BOTH),n?{type:"BOTH",id:null,line:e.slice(n[0].length)}:null))}function E(e){if(!e)return 0;if(/^\d+$/.test(e))return parseInt(e,10);for(var n={"零":0,"一":1,"二":2,"两":2,"三":3,"四":4,"五":5,"六":6,"七":7,"八":8,"九":9,"十":10,"百":100,"千":1e3,"万":1e4},t=0,r=1,I=0,_=e.length-1;_>=0;_--){var o=n[e[_]];void 0!==o&&(o>=10&&0===_?I+=o:o>=10?r=o:(I+=o*r,r=1))}return t+=I,t}function D(e,n){switch(e=parseInt(e),e){case 0:case 27:return n.HEZHI_LING_ERSHIQI;case 1:case 26:return n.HEZHI_YI_ERSHILIU;case 2:case 25:return n.HEZHI_ER_ERSHIWU;case 3:case 24:return n.HEZHI_SAN_ERSHISI;case 4:case 23:return n.HEZHI_SI_ERSHISAN;case 5:case 22:return n.HEZHI_WU_ERSHIER;case 6:case 21:return n.HEZHI_LIU_ERSHIYI;case 7:case 20:return n.HEZHI_QI_ERSHI;case 8:case 19:return n.HEZHI_BA_SHIJIU;case 9:case 18:return n.HEZHI_JIU_SHIBA;case 10:case 17:return n.HEZHI_SHI_SHIQI;case 11:case 16:return n.HEZHI_SHIYI_SHILIU;case 12:case 15:return n.HEZHI_SHIER_SHIWU;case 13:case 14:return n.HEZHI_SHISAN_SHISI;default:return n.HEZHI_SHISAN_SHISI}}function O(e){var n,t=[],I=[],_=[],o=Object(r["a"])(e);try{for(o.s();!(n=o.n()).done;){var u=n.value;3===u.length&&(u[0]===u[1]&&u[1]===u[2]?t.push(u):u[0]===u[1]&&u[1]!==u[2]||u[0]===u[2]&&u[0]!==u[1]||u[1]===u[2]&&u[0]!==u[1]?I.push(u):_.push(u))}}catch(a){o.e(a)}finally{o.f()}return{baoziNumbers:t,zusanNumbers:I,zuxuanNumbers:_}}function m(e){switch(e=parseInt(e),e){case 2:return o.DANTUO_ZUSAN_2;case 3:return o.DANTUO_ZUSAN_3;case 4:return o.DANTUO_ZUSAN_4;case 5:return o.DANTUO_ZUSAN_5;case 6:return o.DANTUO_ZUSAN_6;case 7:return o.DANTUO_ZUSAN_7;case 8:return o.DANTUO_ZUSAN_8;case 9:return o.DANTUO_ZUSAN_9;default:return o.DANTUO_ZUSAN_3}}function H(e){switch(e=parseInt(e),e){case 2:return o.DANTUO_ZULIU_2;case 3:return o.DANTUO_ZULIU_3;case 4:return o.DANTUO_ZULIU_4;case 5:return o.DANTUO_ZULIU_5;case 6:return o.DANTUO_ZULIU_6;case 7:return o.DANTUO_ZULIU_7;case 8:return o.DANTUO_ZULIU_8;case 9:return o.DANTUO_ZULIU_9;default:return o.DANTUO_ZULIU_3}}function f(e){return e>=44&&e<=59}function U(e){return e>=44&&e<=51}function T(e){return e>=52&&e<=59}function M(e){return f(e)?U(e)?{type:"组三",tuomaCount:e-44+2}:{type:"组六",tuomaCount:e-52+2}:null}}}]);