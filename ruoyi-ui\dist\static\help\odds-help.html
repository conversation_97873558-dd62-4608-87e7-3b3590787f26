<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>赔率管理使用说明 - 彩票系统帮助文档</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #F56C6C 0%, #E6A23C 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #F56C6C 0%, #E6A23C 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        .header h1 {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        .back-btn {
            position: absolute;
            left: 30px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-50%) translateY(-2px);
        }
        .content {
            padding: 40px;
        }
        .section-toggle {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px 20px;
            background: linear-gradient(135deg, #F56C6C, #E6A23C);
            color: white;
            border-radius: 12px;
            cursor: pointer;
            user-select: none;
            margin: 20px 0 10px 0;
            font-size: 18px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
        }
        .section-toggle:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(245, 108, 108, 0.4);
        }
        .toggle-icon {
            transition: transform 0.3s ease;
            font-size: 14px;
        }
        .toggle-icon.collapsed {
            transform: rotate(-90deg);
        }
        .section-content {
            max-height: 2000px;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 20px;
            padding: 20px;
        }
        .section-content.collapsed {
            max-height: 0;
            padding: 0 20px;
        }
        .subsection-toggle {
            cursor: pointer;
            user-select: none;
            display: flex;
            align-items: center;
            gap: 8px;
            color: #F56C6C;
            margin-top: 20px;
            margin-bottom: 10px;
            font-size: 16px;
            font-weight: 500;
        }
        .subsection-toggle:hover {
            color: #e6453c;
        }
        .subsection-toggle-icon {
            transition: transform 0.3s ease;
            font-size: 12px;
        }
        .subsection-toggle-icon.collapsed {
            transform: rotate(-90deg);
        }
        .subsection-content {
            max-height: 500px;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        .subsection-content.collapsed {
            max-height: 0;
        }
        .function-item {
            background: white;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            border-left: 4px solid #F56C6C;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        .function-title {
            font-weight: 600;
            color: #F56C6C;
            margin-bottom: 8px;
            font-size: 15px;
        }
        .function-desc {
            color: #666;
            margin-bottom: 8px;
            line-height: 1.6;
        }
        .function-usage {
            color: #888;
            font-size: 14px;
            font-style: italic;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }
        .feature-card {
            background: white;
            border-radius: 8px;
            padding: 16px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .tip {
            background: #fff0f0;
            border: 1px solid #ffb3b3;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            color: #cc0000;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            color: #856404;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            color: #155724;
        }
        .highlight {
            background: #fff0f0;
            padding: 2px 6px;
            border-radius: 4px;
            color: #cc0000;
            font-weight: 500;
        }
        .expand-all-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, #F56C6C, #E6A23C);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
        }
        .expand-all-btn:hover {
            background: #e6453c;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(245, 108, 108, 0.4);
        }
        .step-list {
            counter-reset: step-counter;
        }
        .step-item {
            counter-increment: step-counter;
            position: relative;
            padding-left: 40px;
            margin-bottom: 16px;
        }
        .step-item::before {
            content: counter(step-counter);
            position: absolute;
            left: 0;
            top: 0;
            width: 24px;
            height: 24px;
            background: #F56C6C;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            margin: 8px 0;
            overflow-x: auto;
        }
        ul, ol {
            padding-left: 20px;
            margin: 8px 0;
        }
        li {
            margin-bottom: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <button class="back-btn" onclick="goBack()">← 返回</button>
            <h1>💰 赔率管理使用说明</h1>
            <p>详细介绍赔率管理页面的所有功能和使用方法</p>
        </div>

        <div class="content">
            <div class="tip">
                <strong>页面概述：</strong>赔率管理页面用于查看和管理各种彩票玩法的赔率设置。普通用户可以查看赔率信息、修改自己的赔率配置。
            </div>

            <h2 class="section-toggle" onclick="toggleSection('view')">
                <span class="toggle-icon collapsed" id="view-icon">▼</span>
                一、赔率查看功能
            </h2>
            <div class="feature-section section-content collapsed" id="view-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('view-1-1')">
                    <span class="subsection-toggle-icon collapsed" id="view-1-1-icon">▼</span>
                    1.1 赔率列表查看
                </h3>
                <div class="subsection-content collapsed" id="view-1-1-content">
                    <div class="function-item">
                        <div class="function-title">赔率信息展示</div>
                        <div class="function-desc">表格显示玩法名称、当前赔率、创建时间等详细信息</div>
                        <div class="function-usage">使用方法：进入赔率管理页面即可查看所有赔率配置</div>
                    </div>
                    <div class="feature-grid">
                        <div class="feature-card">
                            <div class="function-title">🎲 玩法名称</div>
                            <div class="function-desc">显示具体的彩票玩法类型</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">💰 赔率倍数</div>
                            <div class="function-desc">显示当前设置的赔率倍数</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">📅 创建时间</div>
                            <div class="function-desc">显示赔率配置的创建时间</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">👤 所属用户</div>
                            <div class="function-desc">显示赔率配置的所属用户</div>
                        </div>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('view-1-2')">
                    <span class="subsection-toggle-icon collapsed" id="view-1-2-icon">▼</span>
                    1.2 搜索筛选功能
                </h3>
                <div class="subsection-content collapsed" id="view-1-2-content">
                    <div class="function-item">
                        <div class="function-title">玩法筛选</div>
                        <div class="function-desc">可以按玩法名称筛选查看特定玩法的赔率</div>
                        <div class="function-usage">使用方法：在搜索框中输入玩法名称进行筛选</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">分页浏览</div>
                        <div class="function-desc">支持分页浏览，方便查看大量赔率数据</div>
                    </div>
                </div>
            </div>

            <h2 class="section-toggle" onclick="toggleSection('manage')">
                <span class="toggle-icon collapsed" id="manage-icon">▼</span>
                二、赔率管理功能
            </h2>
            <div class="feature-section section-content collapsed" id="manage-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('manage-2-1')">
                    <span class="subsection-toggle-icon collapsed" id="manage-2-1-icon">▼</span>
                    2.1 修改赔率
                </h3>
                <div class="subsection-content collapsed" id="manage-2-1-content">
                    <div class="function-item">
                        <div class="function-title">修改入口</div>
                        <div class="function-desc">点击赔率列表中的"修改"按钮进入赔率编辑界面</div>
                        <div class="function-usage">权限说明：用户只能修改自己的赔率配置</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">玩法选择</div>
                        <div class="function-desc">在修改界面可以重新选择玩法类型</div>
                        <div class="function-usage">支持搜索筛选，快速找到目标玩法</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">赔率设置</div>
                        <div class="function-desc">输入新的赔率倍数，支持小数点后两位精度</div>
                        <div class="function-usage">赔率范围：0.01 - 999.99倍</div>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('manage-2-2')">
                    <span class="subsection-toggle-icon collapsed" id="manage-2-2-icon">▼</span>
                    2.2 赔率计算预览
                </h3>
                <div class="subsection-content collapsed" id="manage-2-2-content">
                    <div class="function-item">
                        <div class="function-title">实时预览</div>
                        <div class="function-desc">修改赔率时可以实时预览投注收益效果</div>
                        <div class="function-usage">示例：投注100元，赔率2.5倍，中奖可得250元</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">收益率计算</div>
                        <div class="function-desc">自动计算并显示当前赔率对应的收益率</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">风险提示</div>
                        <div class="function-desc">系统会提示赔率设置的合理性和风险等级</div>
                    </div>
                </div>
            </div>

            <h2 class="section-toggle" onclick="toggleSection('operation')">
                <span class="toggle-icon collapsed" id="operation-icon">▼</span>
                三、操作功能详解
            </h2>
            <div class="feature-section section-content collapsed" id="operation-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('operation-3-1')">
                    <span class="subsection-toggle-icon collapsed" id="operation-3-1-icon">▼</span>
                    3.1 赔率编辑流程
                </h3>
                <div class="subsection-content collapsed" id="operation-3-1-content">
                    <div class="step-list">
                        <div class="step-item">
                            <strong>选择目标</strong>：在赔率列表中找到要修改的赔率配置
                        </div>
                        <div class="step-item">
                            <strong>点击修改</strong>：点击对应行的"修改"按钮
                        </div>
                        <div class="step-item">
                            <strong>编辑信息</strong>：在弹出的对话框中修改玩法和赔率
                        </div>
                        <div class="step-item">
                            <strong>预览效果</strong>：查看赔率计算预览和收益率
                        </div>
                        <div class="step-item">
                            <strong>确认保存</strong>：点击"更新赔率"按钮保存修改
                        </div>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('operation-3-2')">
                    <span class="subsection-toggle-icon collapsed" id="operation-3-2-icon">▼</span>
                    3.2 表格操作功能
                </h3>
                <div class="subsection-content collapsed" id="operation-3-2-content">
                    <div class="function-item">
                        <div class="function-title">排序功能</div>
                        <div class="function-desc">点击表头可以对赔率数据进行排序</div>
                        <div class="function-usage">支持按玩法名称、赔率倍数、创建时间等字段排序</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">刷新数据</div>
                        <div class="function-desc">点击刷新按钮获取最新的赔率数据</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">重置搜索</div>
                        <div class="function-desc">点击重置按钮清空搜索条件，显示所有赔率</div>
                    </div>
                </div>
            </div>

            

            <h2 class="section-toggle" onclick="toggleSection('tips')">
                <span class="toggle-icon collapsed" id="tips-icon">▼</span>
                四、使用技巧与注意事项
            </h2>
            <div class="feature-section section-content collapsed" id="tips-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('tips-5-1')">
                    <span class="subsection-toggle-icon collapsed" id="tips-5-1-icon">▼</span>
                    4.1 赔率设置技巧
                </h3>
                <div class="subsection-content collapsed" id="tips-5-1-content">
                    <div class="function-item">
                        <div class="function-title">合理设置</div>
                        <div class="function-desc">根据玩法难度和中奖概率合理设置赔率</div>
                        <div class="function-usage">建议：高难度玩法设置较高赔率，低难度玩法设置较低赔率</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">风险控制</div>
                        <div class="function-desc">避免设置过高的赔率，控制投注风险</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">定期调整</div>
                        <div class="function-desc">根据投注情况和收益状况定期调整赔率</div>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('tips-5-2')">
                    <span class="subsection-toggle-icon collapsed" id="tips-5-2-icon">▼</span>
                    4.2 注意事项
                </h3>
                <div class="subsection-content collapsed" id="tips-5-2-content">
                    <div class="function-item">
                        <div class="function-title">数据权限</div>
                        <div class="function-desc">用户只能管理自己的赔率配置，无法访问他人数据</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">赔率范围</div>
                        <div class="function-desc">赔率必须在0.01-999.99倍范围内</div>
                    </div>
                    <div class="warning">
                        <strong>重要提醒：</strong>赔率修改会影响后续投注的收益计算，请谨慎操作
                    </div>
                    <div class="function-item">
                        <div class="function-title">系统限制</div>
                        <div class="function-desc">某些系统玩法的赔率可能有特殊限制</div>
                    </div>
                </div>
            </div>

            <div class="success">
                <strong>使用提示：</strong>赔率管理是投注系统的核心功能，合理的赔率设置有助于平衡风险和收益。建议在修改赔率前仔细考虑其对投注收益的影响。
            </div>
        </div>
    </div>

    <button class="expand-all-btn" onclick="toggleAllSections()">
        <span id="expandAllText">📖 展开全部</span>
    </button>

    <script>
        // 切换章节显示/隐藏
        function toggleSection(sectionId) {
            const content = document.getElementById(sectionId + '-content');
            const icon = document.getElementById(sectionId + '-icon');
            
            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                icon.classList.remove('collapsed');
            } else {
                content.classList.add('collapsed');
                icon.classList.add('collapsed');
            }
        }

        // 切换子章节显示/隐藏
        function toggleSubsection(subsectionId) {
            const content = document.getElementById(subsectionId + '-content');
            const icon = document.getElementById(subsectionId + '-icon');
            
            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                icon.classList.remove('collapsed');
            } else {
                content.classList.add('collapsed');
                icon.classList.add('collapsed');
            }
        }

        // 切换全部章节和子章节
        function toggleAllSections() {
            const sections = ['view', 'manage', 'operation', 'permission', 'tips'];
            const subsections = [
                'view-1-1', 'view-1-2',
                'manage-2-1', 'manage-2-2',
                'operation-3-1', 'operation-3-2',
                'permission-4-1', 'permission-4-2',
                'tips-5-1', 'tips-5-2'
            ];

            const expandAllText = document.getElementById('expandAllText');
            const allCollapsed = sections.every(sectionId => 
                document.getElementById(sectionId + '-content').classList.contains('collapsed')
            );

            sections.forEach(sectionId => {
                const content = document.getElementById(sectionId + '-content');
                const icon = document.getElementById(sectionId + '-icon');
                
                if (allCollapsed) {
                    content.classList.remove('collapsed');
                    icon.classList.remove('collapsed');
                } else {
                    content.classList.add('collapsed');
                    icon.classList.add('collapsed');
                }
            });

            subsections.forEach(subsectionId => {
                const content = document.getElementById(subsectionId + '-content');
                const icon = document.getElementById(subsectionId + '-icon');
                
                if (allCollapsed) {
                    content.classList.remove('collapsed');
                    icon.classList.remove('collapsed');
                } else {
                    content.classList.add('collapsed');
                    icon.classList.add('collapsed');
                }
            });

            expandAllText.textContent = allCollapsed ? '📕 收起全部' : '📖 展开全部';
        }

        // 根据URL参数自动展开指定内容
        function autoExpandFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            const expandTarget = urlParams.get('expand');

            if (expandTarget) {
                // 查找包含目标文本的元素
                const allElements = document.querySelectorAll('.function-title, .subsection-toggle, .section-toggle');

                for (let element of allElements) {
                    const text = element.textContent || element.innerText;
                    if (text.includes(expandTarget)) {
                        // 找到匹配的元素，展开其所在的章节和子章节
                        expandToTarget(element);
                        // 滚动到目标位置
                        setTimeout(() => {
                            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            // 高亮显示目标元素
                            highlightElement(element);
                        }, 300);
                        break;
                    }
                }
            }
        }

        // 展开到目标元素
        function expandToTarget(targetElement) {
            let current = targetElement;

            // 向上查找并展开所有父级章节
            while (current) {
                if (current.classList && current.classList.contains('section-content')) {
                    // 展开章节
                    const sectionId = current.id.replace('-content', '');
                    const icon = document.getElementById(sectionId + '-icon');
                    if (icon && icon.classList.contains('collapsed')) {
                        toggleSection(sectionId);
                    }
                }

                if (current.classList && current.classList.contains('subsection-content')) {
                    // 展开子章节
                    const subsectionId = current.id.replace('-content', '');
                    const icon = document.getElementById(subsectionId + '-icon');
                    if (icon && icon.classList.contains('collapsed')) {
                        toggleSubsection(subsectionId);
                    }
                }

                current = current.parentElement;
            }
        }

        // 高亮显示元素
        function highlightElement(element) {
            element.style.backgroundColor = '#fff3cd';
            element.style.border = '2px solid #ffc107';
            element.style.borderRadius = '4px';
            element.style.padding = '8px';
            element.style.transition = 'all 0.3s ease';

            // 3秒后移除高亮
            setTimeout(() => {
                element.style.backgroundColor = '';
                element.style.border = '';
                element.style.borderRadius = '';
                element.style.padding = '';
            }, 3000);
        }

        // 返回功能
        function goBack() {
            const fromHelp = sessionStorage.getItem('helpFromPage');
            if (fromHelp) {
                sessionStorage.removeItem('helpFromPage');
                window.location.href = fromHelp;
            } else {
                window.location.href = 'index.html';
            }
        }

        // 页面加载时处理URL参数
        document.addEventListener('DOMContentLoaded', function() {
            autoExpandFromUrl();
        });
    </script>
</body>
</html>
