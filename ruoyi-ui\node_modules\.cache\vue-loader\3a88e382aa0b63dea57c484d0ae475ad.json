{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetStatistics.vue?vue&type=style&index=0&id=2f2087f9&prod&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetStatistics.vue", "mtime": 1756001963955}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750942927475}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750942929511}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750942928175}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750942928768}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgovKiDnrqHnkIblkZjmjqfliLbljLrln5/moLflvI8gKi8KLmFkbWluLWNvbnRyb2xzIHsKICBib3JkZXI6IDFweCBzb2xpZCAjZTRlN2VkOwogIGJvcmRlci1yYWRpdXM6IDZweDsKICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjhmOWZhIDAlLCAjZTllY2VmIDEwMCUpOwp9CgouYWRtaW4tY29udHJvbHMgLmVsLXNlbGVjdCB7CiAgYmFja2dyb3VuZDogd2hpdGU7Cn0KCi8qIOW9qeenjeagh+etvumhteagt+W8jyAqLwoubG90dGVyeS10eXBlLXRhYnMgewogIG1hcmdpbi1sZWZ0OiAyMHB4OwogIGZsZXg6IDE7Cn0KCi5sb3R0ZXJ5LXR5cGUtdGFicy1jb250YWluZXIgewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKfQoKLmxvdHRlcnktdHlwZS10YWJzIC5lbC10YWJzLS1jYXJkID4gLmVsLXRhYnNfX2hlYWRlciwKLmxvdHRlcnktdHlwZS10YWJzLWNvbnRhaW5lciAuZWwtdGFicy0tY2FyZCA+IC5lbC10YWJzX19oZWFkZXIgewogIGJvcmRlci1ib3R0b206IG5vbmU7CiAgbWFyZ2luOiAwOwp9CgoubG90dGVyeS10eXBlLXRhYnMgLmVsLXRhYnMtLWNhcmQgPiAuZWwtdGFic19faGVhZGVyIC5lbC10YWJzX19uYXYsCi5sb3R0ZXJ5LXR5cGUtdGFicy1jb250YWluZXIgLmVsLXRhYnMtLWNhcmQgPiAuZWwtdGFic19faGVhZGVyIC5lbC10YWJzX19uYXYgewogIGJvcmRlcjogbm9uZTsKICBib3JkZXItcmFkaXVzOiA4cHg7CiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2Y4ZjlmYSAwJSwgI2U5ZWNlZiAxMDAlKTsKICBwYWRkaW5nOiA0cHg7Cn0KCi5sb3R0ZXJ5LXR5cGUtdGFicyAuZWwtdGFicy0tY2FyZCA+IC5lbC10YWJzX19oZWFkZXIgLmVsLXRhYnNfX2l0ZW0sCi5sb3R0ZXJ5LXR5cGUtdGFicy1jb250YWluZXIgLmVsLXRhYnMtLWNhcmQgPiAuZWwtdGFic19faGVhZGVyIC5lbC10YWJzX19pdGVtIHsKICBib3JkZXI6IG5vbmU7CiAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7CiAgY29sb3I6ICM2MDYyNjY7CiAgZm9udC13ZWlnaHQ6IDUwMDsKICBwYWRkaW5nOiA4cHggMTZweDsKICBtYXJnaW4tcmlnaHQ6IDRweDsKICBib3JkZXItcmFkaXVzOiA2cHg7CiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsKICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKfQoKLmxvdHRlcnktdHlwZS10YWJzIC5lbC10YWJzLS1jYXJkID4gLmVsLXRhYnNfX2hlYWRlciAuZWwtdGFic19faXRlbTpob3ZlciwKLmxvdHRlcnktdHlwZS10YWJzLWNvbnRhaW5lciAuZWwtdGFicy0tY2FyZCA+IC5lbC10YWJzX19oZWFkZXIgLmVsLXRhYnNfX2l0ZW06aG92ZXIgewogIGNvbG9yOiAjNDA5ZWZmOwogIGJhY2tncm91bmQ6IHJnYmEoNjQsIDE1OCwgMjU1LCAwLjEpOwogIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTsKfQoKLmxvdHRlcnktdHlwZS10YWJzIC5lbC10YWJzLS1jYXJkID4gLmVsLXRhYnNfX2hlYWRlciAuZWwtdGFic19faXRlbS5pcy1hY3RpdmUsCi5sb3R0ZXJ5LXR5cGUtdGFicy1jb250YWluZXIgLmVsLXRhYnMtLWNhcmQgPiAuZWwtdGFic19faGVhZGVyIC5lbC10YWJzX19pdGVtLmlzLWFjdGl2ZSB7CiAgY29sb3I6IHdoaXRlOwogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7CiAgZm9udC13ZWlnaHQ6IDYwMDsKICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMTAyLCAxMjYsIDIzNCwgMC4zKTsKICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCk7Cn0KCi5sb3R0ZXJ5LXR5cGUtdGFicyAuZWwtdGFicy0tY2FyZCA+IC5lbC10YWJzX19oZWFkZXIgLmVsLXRhYnNfX2l0ZW0uaXMtYWN0aXZlOjpiZWZvcmUsCi5sb3R0ZXJ5LXR5cGUtdGFicy1jb250YWluZXIgLmVsLXRhYnMtLWNhcmQgPiAuZWwtdGFic19faGVhZGVyIC5lbC10YWJzX19pdGVtLmlzLWFjdGl2ZTo6YmVmb3JlIHsKICBjb250ZW50OiAnJzsKICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgdG9wOiAwOwogIGxlZnQ6IDA7CiAgcmlnaHQ6IDA7CiAgYm90dG9tOiAwOwogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHJnYmEoMjU1LDI1NSwyNTUsMC4yKSAwJSwgdHJhbnNwYXJlbnQgMTAwJSk7CiAgcG9pbnRlci1ldmVudHM6IG5vbmU7Cn0KCi5sb3R0ZXJ5LXR5cGUtdGFicyAuZWwtdGFicy0tY2FyZCA+IC5lbC10YWJzX19oZWFkZXIgLmVsLXRhYnNfX2l0ZW0gaSwKLmxvdHRlcnktdHlwZS10YWJzLWNvbnRhaW5lciAuZWwtdGFicy0tY2FyZCA+IC5lbC10YWJzX19oZWFkZXIgLmVsLXRhYnNfX2l0ZW0gaSB7CiAgbWFyZ2luLXJpZ2h0OiA2cHg7CiAgZm9udC1zaXplOiAxNnB4Owp9CgoubG90dGVyeS10eXBlLXRhYnMgLmVsLXRhYnNfX2NvbnRlbnQsCi5sb3R0ZXJ5LXR5cGUtdGFicy1jb250YWluZXIgLmVsLXRhYnNfX2NvbnRlbnQgewogIGRpc3BsYXk6IG5vbmU7IC8qIOmakOiXj+WGheWuueWMuuWfn++8jOWboOS4uuaIkeS7rOWPqueUqOagh+etvumhteWIh+aNoiAqLwp9Cgouc3RhdGlzdGljcy1kaWFsb2cgewogIC5lbC1kaWFsb2cgewogICAgYm9yZGVyLXJhZGl1czogMTJweDsKICB9CiAgCiAgLmVsLWRpYWxvZ19faGVhZGVyIHsKICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7CiAgICBjb2xvcjogd2hpdGU7CiAgICBwYWRkaW5nOiAyMHB4IDI0cHg7CiAgfQogIAogIC5lbC1kaWFsb2dfX3RpdGxlIHsKICAgIGNvbG9yOiB3aGl0ZTsKICAgIGZvbnQtd2VpZ2h0OiA2MDA7CiAgICBmb250LXNpemU6IDE4cHg7CiAgfQp9Cgouc3RhdGlzdGljcy1jb250YWluZXIgewogIHBhZGRpbmc6IDEwcHggMDsKfQoKLm1haW4tc2VjdGlvbi10aXRsZSB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGZvbnQtc2l6ZTogMTZweDsKICBmb250LXdlaWdodDogNjAwOwogIGNvbG9yOiAjMmMzZTUwOwogIG1hcmdpbi1ib3R0b206IDhweDsKICBwYWRkaW5nLWJvdHRvbTogNHB4OwogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZWNmMGYxOwp9CgoubWFpbi1zZWN0aW9uLXRpdGxlIGkgewogIG1hcmdpbi1yaWdodDogNHB4OwogIGNvbG9yOiAjNjY3ZWVhOwogIGZvbnQtc2l6ZTogMTRweDsKfQoKLyog546p5rOV5o6S6KGM5qac5qC35byPICovCi5tZXRob2QtcmFua2luZ3Mtc2VjdGlvbiB7CiAgbWFyZ2luLWJvdHRvbTogMTVweDsKfQoKLm1ldGhvZC1ncmlkIHsKICBkaXNwbGF5OiBncmlkOwogIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDUsIDFmcik7CiAgZ2FwOiA2cHg7Cn0KCkBtZWRpYSAobWF4LXdpZHRoOiAxNDAwcHgpIHsKICAubWV0aG9kLWdyaWQgewogICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoNCwgMWZyKTsKICB9Cn0KCkBtZWRpYSAobWF4LXdpZHRoOiAxMTAwcHgpIHsKICAubWV0aG9kLWdyaWQgewogICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoMywgMWZyKTsKICB9Cn0KCkBtZWRpYSAobWF4LXdpZHRoOiA4MDBweCkgewogIC5tZXRob2QtZ3JpZCB7CiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCgyLCAxZnIpOwogIH0KfQoKQG1lZGlhIChtYXgtd2lkdGg6IDUwMHB4KSB7CiAgLm1ldGhvZC1ncmlkIHsKICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyOwogIH0KfQoKLm1ldGhvZC1jYXJkIHsKICBiYWNrZ3JvdW5kOiB3aGl0ZTsKICBib3JkZXItcmFkaXVzOiAxMHB4OwogIGJveC1zaGFkb3c6IDAgM3B4IDEycHggcmdiYSgwLCAwLCAwLCAwLjEyKTsKICBvdmVyZmxvdzogaGlkZGVuOwogIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwLjNzIGVhc2UsIGJveC1zaGFkb3cgMC4zcyBlYXNlOwogIG1pbi13aWR0aDogMDsgLyog6Ziy5q2i5YaF5a655rqi5Ye6ICovCiAgbWluLWhlaWdodDogMjAwcHg7IC8qIOacgOWwj+mrmOW6piAqLwogIG1heC1oZWlnaHQ6IDUwMHB4OyAvKiDlop7liqDmnIDlpKfpq5jluqbku6XpgILlupTmm7TlpJrmlbDmja4gKi8KfQoKLm1ldGhvZC1jYXJkOmhvdmVyIHsKICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7CiAgYm94LXNoYWRvdzogMCA2cHggMjBweCByZ2JhKDAsIDAsIDAsIDAuMTgpOwp9CgoubWV0aG9kLWNhcmQtaGVhZGVyIHsKICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpOwogIGNvbG9yOiB3aGl0ZTsKICBwYWRkaW5nOiA4cHggMTJweDsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBhbGlnbi1pdGVtczogY2VudGVyOwp9CgoubWV0aG9kLW5hbWUgewogIGZvbnQtc2l6ZTogMTVweDsKICBmb250LXdlaWdodDogNzAwOwogIHdoaXRlLXNwYWNlOiBub3dyYXA7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsKfQoKLm1ldGhvZC1jb3VudCB7CiAgZm9udC1zaXplOiAxM3B4OwogIG9wYWNpdHk6IDAuOTsKfQoKLnJhbmtpbmctbGlzdCB7CiAgcGFkZGluZzogOHB4OwogIG1heC1oZWlnaHQ6IDQyMHB4OyAvKiDlop7liqDliJfooajpq5jluqbku6XpgILlupTmm7TlpJrmlbDmja4gKi8KICBvdmVyZmxvdy15OiBhdXRvOyAvKiDmt7vliqDlnoLnm7Tmu5rliqjmnaEgKi8KfQoKLyog5rua5Yqo5p2h5qC35byPICovCi5yYW5raW5nLWxpc3Q6Oi13ZWJraXQtc2Nyb2xsYmFyIHsKICB3aWR0aDogNHB4Owp9CgoucmFua2luZy1saXN0Ojotd2Via2l0LXNjcm9sbGJhci10cmFjayB7CiAgYmFja2dyb3VuZDogI2YxZjFmMTsKICBib3JkZXItcmFkaXVzOiAycHg7Cn0KCi5yYW5raW5nLWxpc3Q6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHsKICBiYWNrZ3JvdW5kOiAjYzFjMWMxOwogIGJvcmRlci1yYWRpdXM6IDJweDsKfQoKLnJhbmtpbmctbGlzdDo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWI6aG92ZXIgewogIGJhY2tncm91bmQ6ICNhOGE4YTg7Cn0KCi8qIOeDremXqOS4ieS9jeaVsOaOkuihjOamnOa7muWKqOadoeagt+W8jyAqLwoubnVtYmVycy1yYW5raW5nLWxpc3Q6Oi13ZWJraXQtc2Nyb2xsYmFyIHsKICB3aWR0aDogNHB4Owp9CgoubnVtYmVycy1yYW5raW5nLWxpc3Q6Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHsKICBiYWNrZ3JvdW5kOiAjZjFmMWYxOwogIGJvcmRlci1yYWRpdXM6IDJweDsKfQoKLm51bWJlcnMtcmFua2luZy1saXN0Ojotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7CiAgYmFja2dyb3VuZDogI2MxYzFjMTsKICBib3JkZXItcmFkaXVzOiAycHg7Cn0KCi5udW1iZXJzLXJhbmtpbmctbGlzdDo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWI6aG92ZXIgewogIGJhY2tncm91bmQ6ICNhOGE4YTg7Cn0KCi5yYW5raW5nLWl0ZW0gewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBwYWRkaW5nOiA0cHggOHB4OwogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZjBmMGYwOwp9CgoucmFua2luZy1pdGVtOmxhc3QtY2hpbGQgewogIGJvcmRlci1ib3R0b206IG5vbmU7Cn0KCi5yYW5rLWJhZGdlIHsKICBtaW4td2lkdGg6IDE4cHg7CiAgaGVpZ2h0OiAxOHB4OwogIHBhZGRpbmc6IDAgNHB4OwogIGJvcmRlci1yYWRpdXM6IDlweDsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgZm9udC1zaXplOiAxMHB4OwogIGNvbG9yOiB3aGl0ZTsKICBmbGV4OiAwIDAgYXV0bzsKICBtYXJnaW4tcmlnaHQ6IDhweDsKICB3aGl0ZS1zcGFjZTogbm93cmFwOwp9CgoucmFuay1maXJzdCB7CiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2ZmNmI2YiwgI2VlNWEyNCk7Cn0KCi5yYW5rLXNlY29uZCB7CiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2ZlY2E1NywgI2ZmOWZmMyk7Cn0KCi5yYW5rLXRoaXJkIHsKICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNDhkYmZiLCAjMGFiZGUzKTsKfQoKLnJhbmstb3RoZXIgewogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNhNGIwYmUsICM3NDdkOGMpOwp9CgouYmV0LWluZm8gewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBmbGV4OiAxOwogIG1pbi13aWR0aDogMDsKfQoKLmJldC1udW1iZXJzIHsKICBmb250LXNpemU6IDE1cHg7CiAgZm9udC13ZWlnaHQ6IDkwMDsKICBjb2xvcjogI2ZmZmZmZjsKICBmb250LWZhbWlseTogJ0NvdXJpZXIgTmV3JywgbW9ub3NwYWNlOwogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEsICM3NjRiYTIpOwogIHBhZGRpbmc6IDNweCA4cHg7CiAgYm9yZGVyLXJhZGl1czogNXB4OwogIHRleHQtYWxpZ246IGNlbnRlcjsKICBib3gtc2hhZG93OiAwIDJweCA2cHggcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjMpOwogIGxldHRlci1zcGFjaW5nOiAwLjVweDsKICBmbGV4OiAyOwogIG1hcmdpbi1yaWdodDogOHB4Owp9CgouYW1vdW50IHsKICBjb2xvcjogI2U3NGMzYzsKICBmb250LXdlaWdodDogNzAwOwogIGZvbnQtc2l6ZTogMTRweDsKICB3aGl0ZS1zcGFjZTogbm93cmFwOwogIGZsZXg6IDE7CiAgdGV4dC1hbGlnbjogcmlnaHQ7Cn0KCi5yZWNvcmQtY291bnQgewogIGNvbG9yOiAjOTVhNWE2OwogIGZvbnQtc2l6ZTogMTFweDsKICB0ZXh0LWFsaWduOiByaWdodDsKICBtYXJnaW4tdG9wOiAycHg7Cn0KCi8qIOeDremXqOS4ieS9jeaVsOagt+W8jyAqLwoubnVtYmVycy1zZWN0aW9uIHsKICBtYXJnaW4tYm90dG9tOiAzMHB4Owp9CgoucmFua2luZy10YWJsZSB7CiAgYm9yZGVyLXJhZGl1czogOHB4OwogIG92ZXJmbG93OiBoaWRkZW47CiAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4xKTsKfQoKLmFtb3VudC10ZXh0IHsKICBmb250LXdlaWdodDogNjAwOwogIGNvbG9yOiAjZTc0YzNjOwp9CgoucGVyY2VudGFnZS10ZXh0IHsKICBmb250LXdlaWdodDogNTAwOwogIGNvbG9yOiAjMzQ5OGRiOwp9CgovKiDng63pl6jkuInkvY3mlbDljaHniYfmoLflvI8gKi8KLm51bWJlcnMtcmFua2luZ3Mtc2VjdGlvbiB7CiAgbWFyZ2luLWJvdHRvbTogMTVweDsKfQoKLm51bWJlcnMtZ3JpZCB7CiAgZGlzcGxheTogZ3JpZDsKICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCg1LCAxZnIpOwogIGdhcDogNnB4Owp9CgpAbWVkaWEgKG1heC13aWR0aDogMTQwMHB4KSB7CiAgLm51bWJlcnMtZ3JpZCB7CiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCg0LCAxZnIpOwogIH0KfQoKQG1lZGlhIChtYXgtd2lkdGg6IDExMDBweCkgewogIC5udW1iZXJzLWdyaWQgewogICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoMywgMWZyKTsKICB9Cn0KCkBtZWRpYSAobWF4LXdpZHRoOiA4MDBweCkgewogIC5udW1iZXJzLWdyaWQgewogICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoMiwgMWZyKTsKICB9Cn0KCkBtZWRpYSAobWF4LXdpZHRoOiA1MDBweCkgewogIC5udW1iZXJzLWdyaWQgewogICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnI7CiAgfQp9CgoubnVtYmVycy1jYXJkIHsKICBiYWNrZ3JvdW5kOiB3aGl0ZTsKICBib3JkZXItcmFkaXVzOiA4cHg7CiAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4xKTsKICBvdmVyZmxvdzogaGlkZGVuOwogIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwLjNzIGVhc2UsIGJveC1zaGFkb3cgMC4zcyBlYXNlOwogIG1pbi13aWR0aDogMDsKICBtaW4taGVpZ2h0OiAxMjBweDsKICBtYXgtaGVpZ2h0OiA0MDBweDsgLyog5aKe5Yqg5pyA5aSn6auY5bqm5Lul6YCC5bqU5pu05aSa5pWw5o2uICovCn0KCi5udW1iZXJzLWNhcmQ6aG92ZXIgewogIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTsKICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7Cn0KCi5udW1iZXJzLWNhcmQtaGVhZGVyIHsKICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMjdhZTYwIDAlLCAjMmVjYzcxIDEwMCUpOwogIGNvbG9yOiB3aGl0ZTsKICBwYWRkaW5nOiA0cHggNnB4OwogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7Cn0KCi5udW1iZXJzLXJhbmtpbmctbGlzdCB7CiAgcGFkZGluZzogNHB4OwogIG1heC1oZWlnaHQ6IDMyMHB4OyAvKiDpmZDliLbliJfooajpq5jluqbku6XpgILlupTmm7TlpJrmlbDmja4gKi8KICBvdmVyZmxvdy15OiBhdXRvOyAvKiDmt7vliqDlnoLnm7Tmu5rliqjmnaEgKi8KfQoKLm51bWJlcnMtcmFua2luZy1pdGVtIHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgcGFkZGluZzogMnB4IDRweDsKICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2YwZjBmMDsKfQoKLm51bWJlcnMtcmFua2luZy1pdGVtOmxhc3QtY2hpbGQgewogIGJvcmRlci1ib3R0b206IG5vbmU7Cn0KCi5udW1iZXJzLWluZm8gewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBmbGV4OiAxOwogIG1pbi13aWR0aDogMDsKfQoKLm51bWJlcnMtaW5mbyAuaG90LW51bWJlciB7CiAgZm9udC1mYW1pbHk6ICdDb3VyaWVyIE5ldycsIG1vbm9zcGFjZTsKICBmb250LXdlaWdodDogOTAwOwogIGZvbnQtc2l6ZTogMTNweDsKICBjb2xvcjogI2ZmZmZmZjsKICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMjdhZTYwLCAjMmVjYzcxKTsKICBwYWRkaW5nOiAycHggNHB4OwogIGJvcmRlci1yYWRpdXM6IDNweDsKICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgZmxleDogMjsKICBtYXJnaW4tcmlnaHQ6IDRweDsKfQoKLm51bWJlcnMtaW5mbyAuYW1vdW50IHsKICBjb2xvcjogI2U3NGMzYzsKICBmb250LXdlaWdodDogNzAwOwogIGZvbnQtc2l6ZTogMTNweDsKICB3aGl0ZS1zcGFjZTogbm93cmFwOwogIGZsZXg6IDE7CiAgdGV4dC1hbGlnbjogcmlnaHQ7Cn0KCgoKLmRpYWxvZy1mb290ZXIgewogIHRleHQtYWxpZ246IHJpZ2h0OwogIHBhZGRpbmctdG9wOiAyMHB4OwogIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZWNmMGYxOwp9CgovKiDmkJzntKLlip/og73moLflvI8gKi8KLnNlYXJjaC1jb250YWluZXIgewogIHBhZGRpbmc6IDE1cHggMjBweDsKICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjhmOWZhIDAlLCAjZTllY2VmIDEwMCUpOwogIGJvcmRlci1yYWRpdXM6IDhweDsKICBib3JkZXI6IDFweCBzb2xpZCAjZTRlN2VkOwp9Cgouc2VhcmNoLWJveCB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGp1c3RpZnktY29udGVudDogY2VudGVyOwogIGZsZXgtd3JhcDogd3JhcDsKICBnYXA6IDEwcHg7Cn0KCi5zZWFyY2gtYm94IC5lbC1pbnB1dCB7CiAgbWF4LXdpZHRoOiAzMDBweDsKICBmbGV4OiAxOwogIG1pbi13aWR0aDogMjAwcHg7Cn0KCi5zZWFyY2gtYm94IC5lbC1idXR0b24gewogIGJvcmRlci1yYWRpdXM6IDZweDsKICBmb250LXdlaWdodDogNTAwOwogIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7Cn0KCi5zZWFyY2gtYm94IC5lbC1idXR0b246aG92ZXIgewogIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTsKICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7Cn0KCi5zZWFyY2gtcmVzdWx0LXRpcCB7CiAgdGV4dC1hbGlnbjogY2VudGVyOwp9Cgouc2VhcmNoLXJlc3VsdC10aXAgLmVsLWFsZXJ0IHsKICBib3JkZXItcmFkaXVzOiA2cHg7Cn0KCi5zZWFyY2gtcmVzdWx0LXRpcCAuZWwtYWxlcnQtLWluZm8gewogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoNjQsIDE1OCwgMjU1LCAwLjEpOwogIGJvcmRlci1jb2xvcjogcmdiYSg2NCwgMTU4LCAyNTUsIDAuMik7Cn0KCi8qIOWTjeW6lOW8j+iwg+aVtCAqLwpAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHsKICAuc2VhcmNoLWJveCB7CiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogICAgYWxpZ24taXRlbXM6IHN0cmV0Y2g7CiAgfQoKICAuc2VhcmNoLWJveCAuZWwtaW5wdXQgewogICAgbWF4LXdpZHRoOiBub25lOwogICAgd2lkdGg6IDEwMCU7CiAgfQoKICAuc2VhcmNoLWJveCAuZWwtYnV0dG9uIHsKICAgIHdpZHRoOiAxMDAlOwogICAgbWFyZ2luLXRvcDogNXB4OwogIH0KfQo="}, null]}