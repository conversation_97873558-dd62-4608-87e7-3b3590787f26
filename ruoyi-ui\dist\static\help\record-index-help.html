<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下注记录管理使用说明 - 彩票系统帮助文档</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        .header h1 {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        .back-btn {
            position: absolute;
            left: 30px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-50%) translateY(-2px);
        }
        .content {
            padding: 40px;
        }
        .section-toggle {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px 20px;
            background: linear-gradient(135deg, #409EFF, #67C23A);
            color: white;
            border-radius: 12px;
            cursor: pointer;
            user-select: none;
            margin: 20px 0 10px 0;
            font-size: 18px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        }
        .section-toggle:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
        }
        .toggle-icon {
            transition: transform 0.3s ease;
            font-size: 14px;
        }
        .toggle-icon.collapsed {
            transform: rotate(-90deg);
        }
        .section-content {
            max-height: 2000px;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 20px;
            padding: 20px;
        }
        .section-content.collapsed {
            max-height: 0;
            padding: 0 20px;
        }
        .subsection-toggle {
            cursor: pointer;
            user-select: none;
            display: flex;
            align-items: center;
            gap: 8px;
            color: #67C23A;
            margin-top: 20px;
            margin-bottom: 10px;
            font-size: 16px;
            font-weight: 500;
        }
        .subsection-toggle:hover {
            color: #529b2e;
        }
        .subsection-toggle-icon {
            transition: transform 0.3s ease;
            font-size: 12px;
        }
        .subsection-toggle-icon.collapsed {
            transform: rotate(-90deg);
        }
        .subsection-content {
            max-height: 500px;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        .subsection-content.collapsed {
            max-height: 0;
        }
        .function-item {
            background: white;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            border-left: 4px solid #409EFF;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        .function-title {
            font-weight: 600;
            color: #409EFF;
            margin-bottom: 8px;
            font-size: 15px;
        }
        .function-desc {
            color: #666;
            margin-bottom: 8px;
            line-height: 1.6;
        }
        .function-usage {
            color: #888;
            font-size: 14px;
            font-style: italic;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }
        .feature-card {
            background: white;
            border-radius: 8px;
            padding: 16px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .tip {
            background: #e8f4fd;
            border: 1px solid #b3d8ff;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            color: #0066cc;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            color: #856404;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            color: #155724;
        }
        .highlight {
            background: #fff3cd;
            padding: 2px 6px;
            border-radius: 4px;
            color: #856404;
            font-weight: 500;
        }
        .expand-all-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, #409EFF, #67C23A);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
        }
        .expand-all-btn:hover {
            background: #337ecc;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
        }
        .step-list {
            counter-reset: step-counter;
        }
        .step-item {
            counter-increment: step-counter;
            position: relative;
            padding-left: 40px;
            margin-bottom: 16px;
        }
        .step-item::before {
            content: counter(step-counter);
            position: absolute;
            left: 0;
            top: 0;
            width: 24px;
            height: 24px;
            background: #409EFF;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            margin: 8px 0;
            overflow-x: auto;
        }
        ul, ol {
            padding-left: 20px;
            margin: 8px 0;
        }
        li {
            margin-bottom: 4px;
        }
        .toggle-icon {
            transition: transform 0.3s ease;
            font-size: 14px;
        }
        .toggle-icon.collapsed {
            transform: rotate(-90deg);
        }
        .section-content {
            max-height: 1000px;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        .section-content.collapsed {
            max-height: 0;
        }
        .expand-all-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: #409EFF;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
        }
        .expand-all-btn:hover {
            background: #337ecc;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
        }
        .subsection-toggle {
            cursor: pointer;
            user-select: none;
            display: flex;
            align-items: center;
            gap: 8px;
            color: #67C23A;
            margin-top: 20px;
            margin-bottom: 10px;
        }
        .subsection-toggle:hover {
            color: #529b2e;
        }
        .subsection-toggle-icon {
            transition: transform 0.3s ease;
            font-size: 12px;
        }
        .subsection-toggle-icon.collapsed {
            transform: rotate(-90deg);
        }
        .subsection-content {
            max-height: 500px;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        .subsection-content.collapsed {
            max-height: 0;
        }
        ul {
            padding-left: 20px;
        }
        li {
            margin-bottom: 5px;
        }
        .table-structure {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .permission-note {
            background-color: #fff7e6;
            border: 1px solid #ffd591;
            color: #d48806;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <button class="back-btn" onclick="goBack()">← 返回</button>
            <h1>� 下注记录管理使用说明</h1>
            <p>详细介绍下注记录管理页面的所有功能和使用方法</p>
        </div>

        <div class="content">
        <div class="tip">
            <strong>页面概述：</strong>下注记录管理页面是彩票系统的核心功能模块，用于管理和查看所有的投注记录。支持多级数据展示、对账功能、批量操作等高级功能。
        </div>
        


        <h2 class="section-toggle" onclick="toggleSection('search')">
            <span class="toggle-icon" id="search-icon">▼</span>
            一、搜索功能区域
        </h2>
        <div class="feature-section section-content collapsed" id="search-content">
            <h3 class="subsection-toggle" onclick="toggleSubsection('search-1-1')">
                <span class="subsection-toggle-icon collapsed" id="search-1-1-icon">▼</span>
                1.1 玩法筛选
            </h3>
            <div class="subsection-content collapsed" id="search-1-1-content">
                <div class="function-item">
                    <div class="function-title">功能说明</div>
                    <div class="function-desc">根据不同的彩票玩法类型筛选投注记录</div>
                    <div class="function-usage">使用方法：点击下拉框选择具体玩法，如"直选"、"组选"等</div>
                </div>
            </div>

            <h3 class="subsection-toggle" onclick="toggleSubsection('search-1-2')">
                <span class="subsection-toggle-icon collapsed" id="search-1-2-icon">▼</span>
                1.2 流水号筛选
            </h3>
            <div class="subsection-content collapsed" id="search-1-2-content">
                <div class="function-item">
                    <div class="function-title">功能说明</div>
                    <div class="function-desc">按流水号查询特定批次的投注记录，支持模糊搜索</div>
                    <div class="function-usage">使用方法：输入流水号或从下拉列表中选择，支持输入关键字快速筛选</div>
                </div>
            </div>

            <h3 class="subsection-toggle" onclick="toggleSubsection('search-1-3')">
                <span class="subsection-toggle-icon collapsed" id="search-1-3-icon">▼</span>
                1.3 用户筛选
            </h3>
            <div class="subsection-content collapsed" id="search-1-3-content">
                <div class="function-item">
                    <div class="function-title">玩家筛选</div>
                    <div class="function-desc">筛选特定玩家的投注记录</div>
                    <div class="function-usage">使用方法：从下拉列表中选择玩家姓名</div>
                </div>
            </div>

            <h3 class="subsection-toggle" onclick="toggleSubsection('search-1-4')">
                <span class="subsection-toggle-icon collapsed" id="search-1-4-icon">▼</span>
                1.4 中奖状态筛选
            </h3>
            <div class="subsection-content collapsed" id="search-1-4-content">
                <div class="function-item">
                    <div class="function-title">功能说明</div>
                    <div class="function-desc">筛选已中奖或未中奖的投注记录</div>
                    <div class="function-usage">使用方法：选择"已中奖"或"未中奖"进行筛选</div>
                </div>
            </div>

            <h3 class="subsection-toggle" onclick="toggleSubsection('search-1-5')">
                <span class="subsection-toggle-icon collapsed" id="search-1-5-icon">▼</span>
                1.5 识别框搜索
            </h3>
            <div class="subsection-content collapsed" id="search-1-5-content">
                <div class="function-item">
                    <div class="function-title">功能说明</div>
                    <div class="function-desc">通过识别内容进行精确或模糊搜索，支持多行文本输入</div>
                    <div class="function-usage">使用方法：在文本框中输入识别内容，系统会先进行精确匹配，无结果时自动进行包含匹配</div>
                </div>
                <div class="tip">
                    <strong>搜索提示：</strong>识别框搜索会自动展开所有匹配的搜索结果，方便快速查看
                </div>
            </div>

            <h3 class="subsection-toggle" onclick="toggleSubsection('search-1-6')">
                <span class="subsection-toggle-icon collapsed" id="search-1-6-icon">▼</span>
                1.6 搜索操作按钮
            </h3>
            <div class="subsection-content collapsed" id="search-1-6-content">
                <div class="function-item">
                    <div class="function-title">搜索按钮</div>
                    <div class="function-desc">执行搜索操作，根据设置的筛选条件查询数据</div>
                </div>
                <div class="function-item">
                    <div class="function-title">重置按钮</div>
                    <div class="function-desc">清空所有搜索条件，恢复到初始状态</div>
                </div>
            </div>
        </div>

        <h2 class="section-toggle" onclick="toggleSection('toolbar')">
            <span class="toggle-icon collapsed" id="toolbar-icon">▼</span>
            二、工具栏功能
        </h2>
        <div class="feature-section section-content collapsed" id="toolbar-content">
            <h3 class="subsection-toggle" onclick="toggleSubsection('toolbar-2-1')">
                <span class="subsection-toggle-icon collapsed" id="toolbar-2-1-icon">▼</span>
                2.1 数据操作按钮
            </h3>
            <div class="subsection-content collapsed" id="toolbar-2-1-content">
                <div class="function-item">
                    <div class="function-title">新增按钮</div>
                    <div class="function-desc">打开新增投注记录对话框，支持批量添加多个投注</div>
                    <div class="function-usage">使用方法：点击后弹出投注对话框，可以选择玩法、输入号码和金额</div>
                </div>

                <div class="function-item">
                    <div class="function-title">批量删除流水</div>
                    <div class="function-desc">删除选中的整个流水号及其所有投注记录</div>
                    <div class="function-usage">使用方法：勾选要删除的流水号，点击按钮确认删除</div>
                </div>
                <div class="warning">
                    <strong>注意：</strong>批量删除操作不可恢复，请谨慎操作！
                </div>

                <div class="function-item">
                    <div class="function-title">导出按钮</div>
                    <div class="function-desc">将当前筛选的数据导出为Excel文件</div>
                    <div class="function-usage">使用方法：点击后自动下载包含当前页面显示字段的Excel文件</div>
                </div>
            </div>

            <h3 class="subsection-toggle" onclick="toggleSubsection('toolbar-2-2')">
                <span class="subsection-toggle-icon collapsed" id="toolbar-2-2-icon">▼</span>
                2.2 展开控制按钮
            </h3>
            <div class="subsection-content collapsed" id="toolbar-2-2-content">
                <div class="function-item">
                    <div class="function-title">展开二级/收回二级</div>
                    <div class="function-desc">控制流水号分组的展开和收起状态</div>
                    <div class="function-usage">使用方法：点击可批量展开或收起所有流水号分组</div>
                </div>

                <div class="function-item">
                    <div class="function-title">展开三级/收回三级</div>
                    <div class="function-desc">控制投注记录详情的展开和收起状态</div>
                    <div class="function-usage">使用方法：点击可批量展开或收起所有投注记录的详细信息</div>
                </div>
                <div class="tip">
                    <strong>提示：</strong>在对账模式下，展开控制按钮会自动隐藏
                </div>
            </div>

            <h3 class="subsection-toggle" onclick="toggleSubsection('toolbar-2-3')">
                <span class="subsection-toggle-icon collapsed" id="toolbar-2-3-icon">▼</span>
                2.3 对账功能
            </h3>
            <div class="subsection-content collapsed" id="toolbar-2-3-content">
                <div class="function-item">
                    <div class="function-title">开始对账/退出对账</div>
                    <div class="function-desc">进入或退出对账模式，用于核对投注记录和金额</div>
                    <div class="function-usage">使用方法：点击进入对账模式后，数据会按正序排列并显示所有记录</div>
                </div>
            </div>
        </div>

        <h2 class="section-toggle" onclick="toggleSection('reconciliation')">
            <span class="toggle-icon collapsed" id="reconciliation-icon">▼</span>
            三、对账模式详解
        </h2>
        <div class="feature-section section-content collapsed" id="reconciliation-content">
            <h3 class="subsection-toggle" onclick="toggleSubsection('reconciliation-3-1')">
                <span class="subsection-toggle-icon collapsed" id="reconciliation-3-1-icon">▼</span>
                3.1 对账模式特点
            </h3>
            <div class="subsection-content collapsed" id="reconciliation-3-1-content">
                <ul>
                    <li>显示所有数据，不分页</li>
                    <li>数据按时间正序排列</li>
                    <li>隐藏展开控制按钮</li>
                    <li>显示对账状态统计</li>
                    <li>支持单个流水号对账标记</li>
                </ul>
            </div>

            <h3 class="subsection-toggle" onclick="toggleSubsection('reconciliation-3-2')">
                <span class="subsection-toggle-icon collapsed" id="reconciliation-3-2-icon">▼</span>
                3.2 对账操作方法
            </h3>
            <div class="subsection-content collapsed" id="reconciliation-3-2-content">
                <div class="function-item">
                    <div class="function-title">标记对账状态</div>
                    <div class="function-desc">点击表格中蓝色虚线框的<span class="highlight">"投注总额: ￥xxx.xx"</span>区域可切换该流水号的对账状态</div>
                    <div class="function-usage">操作提示：再次点击可取消对账标记</div>
                </div>
            </div>

            <h3 class="subsection-toggle" onclick="toggleSubsection('reconciliation-3-3')">
                <span class="subsection-toggle-icon collapsed" id="reconciliation-3-3-icon">▼</span>
                3.3 对账统计信息
            </h3>
            <div class="subsection-content collapsed" id="reconciliation-3-3-content">
                <div class="function-item">
                    <div class="function-title">对账进度</div>
                    <div class="function-desc">显示已对账流水号数量与总数量的比例</div>
                </div>
                <div class="function-item">
                    <div class="function-title">对账金额</div>
                    <div class="function-desc">显示已对账流水号的总金额与全部金额的对比</div>
                </div>

                <div class="success">
                    <strong>对账模式优势：</strong>状态会自动保存到本地存储，页面刷新后仍然保持对账状态
                </div>
            </div>
        </div>

        <h2 class="section-toggle" onclick="toggleSection('table')">
            <span class="toggle-icon collapsed" id="table-icon">▼</span>
            四、数据表格功能
        </h2>
        <div class="feature-section section-content collapsed" id="table-content">
            <h3 class="subsection-toggle" onclick="toggleSubsection('table-4-1')">
                <span class="subsection-toggle-icon collapsed" id="table-4-1-icon">▼</span>
                4.1 三级数据结构
            </h3>
            <div class="subsection-content collapsed" id="table-4-1-content">
                <div class="table-structure">
                    <strong>数据层级说明：</strong>
                    <ul>
                        <li><strong>第一级：</strong>流水号分组 - 显示流水号、总金额、总注数等汇总信息</li>
                        <li><strong>第二级：</strong>投注记录 - 显示具体的投注详情</li>
                        <li><strong>第三级：</strong>号码详情 - 显示投注的具体号码组合</li>
                    </ul>
                </div>
            </div>

            <h3 class="subsection-toggle" onclick="toggleSubsection('table-4-2')">
                <span class="subsection-toggle-icon collapsed" id="table-4-2-icon">▼</span>
                4.2 表格列说明
            </h3>
            <div class="subsection-content collapsed" id="table-4-2-content">
                <div class="function-item">
                    <div class="function-title">流水号列</div>
                    <div class="function-desc">显示投注的流水号，相同流水号的投注会分组显示</div>
                </div>
                <div class="function-item">
                    <div class="function-title">玩家列</div>
                    <div class="function-desc">显示投注玩家的姓名</div>
                </div>
                <div class="function-item">
                    <div class="function-title">玩法列</div>
                    <div class="function-desc">显示投注的彩票玩法类型</div>
                </div>
                <div class="function-item">
                    <div class="function-title">期号列</div>
                    <div class="function-desc">显示投注的彩票期号</div>
                </div>
                <div class="function-item">
                    <div class="function-title">投注号码列</div>
                    <div class="function-desc">显示投注的号码组合，支持展开查看详细号码</div>
                </div>
                <div class="function-item">
                    <div class="function-title">金额列</div>
                    <div class="function-desc">显示单注投注金额</div>
                </div>
                <div class="function-item">
                    <div class="function-title">注数列</div>
                    <div class="function-desc">显示投注的注数</div>
                </div>
                <div class="function-item">
                    <div class="function-title">总金额列</div>
                    <div class="function-desc">显示该投注的总金额（金额×注数）</div>
                </div>
                <div class="function-item">
                    <div class="function-title">中奖状态列</div>
                    <div class="function-desc">显示是否中奖，包含中奖金额信息</div>
                </div>
                <div class="function-item">
                    <div class="function-title">结算状态列</div>
                    <div class="function-desc">显示投注是否已结算，支持切换结算状态</div>
                </div>
                <div class="function-item">
                    <div class="function-title">操作列</div>
                    <div class="function-desc">提供删除等操作按钮</div>
                </div>
            </div>

            <h3 class="subsection-toggle" onclick="toggleSubsection('table-4-3')">
                <span class="subsection-toggle-icon collapsed" id="table-4-3-icon">▼</span>
                4.3 表格交互功能
            </h3>
            <div class="subsection-content collapsed" id="table-4-3-content">
                <div class="function-item">
                    <div class="function-title">展开/收起</div>
                    <div class="function-desc">点击展开图标可以查看下级详细信息</div>
                    <div class="function-usage">操作方法：点击行首的展开图标或直接点击行内容</div>
                </div>
                <div class="function-item">
                    <div class="function-title">多选功能</div>
                    <div class="function-desc">支持勾选多个流水号进行批量操作</div>
                    <div class="function-usage">操作方法：勾选复选框，可配合批量删除功能使用</div>
                </div>
                <div class="function-item">
                    <div class="function-title">排序功能</div>
                    <div class="function-desc">默认按时间倒序排列，对账模式下自动切换为正序</div>
                </div>
            </div>
        </div>

        <h2 class="section-toggle" onclick="toggleSection('permission')">
            <span class="toggle-icon collapsed" id="permission-icon">▼</span>
            五、权限控制
        </h2>
        <div class="feature-section section-content collapsed" id="permission-content">
            <h3 class="subsection-toggle" onclick="toggleSubsection('permission-5-1')">
                <span class="subsection-toggle-icon collapsed" id="permission-5-1-icon">▼</span>
                5.1 普通用户权限
            </h3>
            <div class="subsection-content collapsed" id="permission-5-1-content">
                <ul>
                    <li>查看自己的投注记录</li>
                    <li>新增投注记录</li>
                    <li>删除自己的投注记录</li>
                    <li>导出自己的数据</li>
                    <li>使用对账功能</li>
                </ul>
            </div>

        </div>

        <h2 class="section-toggle" onclick="toggleSection('floating')">
            <span class="toggle-icon collapsed" id="floating-icon">▼</span>
            六、浮动操作按钮
        </h2>
        <div class="feature-section section-content collapsed" id="floating-content">
            <h3 class="subsection-toggle" onclick="toggleSubsection('floating-6-1')">
                <span class="subsection-toggle-icon collapsed" id="floating-6-1-icon">▼</span>
                6.1 浮动按钮功能
            </h3>
            <div class="subsection-content collapsed" id="floating-6-1-content">
                <div class="function-item">
                    <div class="function-title">显示条件</div>
                    <div class="function-desc">当工具栏滚动到屏幕顶部以上时，右下角会显示浮动操作按钮</div>
                </div>
                <div class="function-item">
                    <div class="function-title">包含功能</div>
                    <div class="function-desc">包含返回顶部、展开/收起控制等关键功能</div>
                </div>
                <div class="function-item">
                    <div class="function-title">对账模式适配</div>
                    <div class="function-desc">在对账模式下会自动隐藏展开/收起相关按钮</div>
                </div>

                <div class="tip">
                    <strong>设计理念：</strong>浮动按钮确保用户在长页面滚动时仍能快速访问关键功能
                </div>
            </div>
        </div>

        <h2 class="section-toggle" onclick="toggleSection('tips')">
            <span class="toggle-icon collapsed" id="tips-icon">▼</span>
            七、使用技巧与注意事项
        </h2>
        <div class="feature-section section-content collapsed" id="tips-content">
            <h3 class="subsection-toggle" onclick="toggleSubsection('tips-7-1')">
                <span class="subsection-toggle-icon collapsed" id="tips-7-1-icon">▼</span>
                7.1 使用技巧
            </h3>
            <div class="subsection-content collapsed" id="tips-7-1-content">
                <ul>
                    <li>使用识别框搜索时，系统会自动展开匹配结果，便于快速查看</li>
                    <li>对账模式下的状态会持久化保存，可以分多次完成对账工作</li>
                    <li>批量操作前建议先使用筛选功能缩小操作范围</li>
                    <li>导出功能只包含页面显示的字段，确保数据的相关性</li>
                    <li>长时间操作建议定期保存，避免数据丢失</li>
                </ul>
            </div>

            <h3 class="subsection-toggle" onclick="toggleSubsection('tips-7-2')">
                <span class="subsection-toggle-icon collapsed" id="tips-7-2-icon">▼</span>
                7.2 注意事项
            </h3>
            <div class="subsection-content collapsed" id="tips-7-2-content">
                <div class="warning">
                    <ul>
                        <li>批量删除操作不可恢复，操作前请仔细确认</li>
                        <li>对账模式会加载所有数据，大量数据时可能影响页面性能</li>
                        <li>结算状态的修改会影响财务统计，请谨慎操作</li>
                        <li>导出大量数据时请耐心等待，避免重复点击</li>
                    </ul>
                </div>
            </div>

            <h3 class="subsection-toggle" onclick="toggleSubsection('tips-7-3')">
                <span class="subsection-toggle-icon collapsed" id="tips-7-3-icon">▼</span>
                7.3 故障排除
            </h3>
            <div class="subsection-content collapsed" id="tips-7-3-content">
                <div class="function-item">
                    <div class="function-title">页面加载缓慢</div>
                    <div class="function-desc">建议使用筛选条件减少数据量，或分批次查看</div>
                </div>
                <div class="function-item">
                    <div class="function-title">对账状态丢失</div>
                    <div class="function-desc">检查浏览器是否禁用了本地存储功能</div>
                </div>
                <div class="function-item">
                    <div class="function-title">导出失败</div>
                    <div class="function-desc">检查网络连接和权限设置，必要时联系管理员</div>
                </div>
            </div>
        </div>

        <div class="success">
            <strong>帮助提示：</strong>如果在使用过程中遇到问题，可以尝试刷新页面或联系系统管理员获取帮助。
        </div>
    </div>

    <button class="expand-all-btn" onclick="toggleAllSections()">
        <span id="expandAllText">📖 展开全部</span>
    </button>

    <script>
        let allExpanded = false;
        let allSubsectionsExpanded = false;

        // 切换单个章节
        function toggleSection(sectionId) {
            const content = document.getElementById(sectionId + '-content');
            const icon = document.getElementById(sectionId + '-icon');

            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                icon.classList.remove('collapsed');
            } else {
                content.classList.add('collapsed');
                icon.classList.add('collapsed');
            }
        }

        // 切换三级子章节
        function toggleSubsection(subsectionId) {
            const content = document.getElementById(subsectionId + '-content');
            const icon = document.getElementById(subsectionId + '-icon');

            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                icon.classList.remove('collapsed');
            } else {
                content.classList.add('collapsed');
                icon.classList.add('collapsed');
            }
        }

        // 切换全部章节和子章节
        function toggleAllSections() {
            const sections = ['search', 'toolbar', 'reconciliation', 'table', 'permission', 'floating', 'tips'];
            const subsections = [
                // 第一章节：搜索功能区域
                'search-1-1', 'search-1-2', 'search-1-3', 'search-1-4', 'search-1-5', 'search-1-6',
                // 第二章节：工具栏功能
                'toolbar-2-1', 'toolbar-2-2', 'toolbar-2-3',
                // 第三章节：对账模式详解
                'reconciliation-3-1', 'reconciliation-3-2', 'reconciliation-3-3',
                // 第四章节：数据表格功能
                'table-4-1', 'table-4-2', 'table-4-3',
                // 第五章节：权限控制
                'permission-5-1', 'permission-5-2',
                // 第六章节：浮动操作按钮
                'floating-6-1',
                // 第七章节：使用技巧与注意事项
                'tips-7-1', 'tips-7-2', 'tips-7-3'
            ];
            const toggleAllText = document.getElementById('toggleAllText');
            const expandAllText = document.getElementById('expandAllText');

            allExpanded = !allExpanded;
            allSubsectionsExpanded = allExpanded;

            // 切换主章节
            sections.forEach(sectionId => {
                const content = document.getElementById(sectionId + '-content');
                const icon = document.getElementById(sectionId + '-icon');

                if (allExpanded) {
                    content.classList.remove('collapsed');
                    icon.classList.remove('collapsed');
                } else {
                    content.classList.add('collapsed');
                    icon.classList.add('collapsed');
                }
            });

            // 切换子章节
            subsections.forEach(subsectionId => {
                const content = document.getElementById(subsectionId + '-content');
                const icon = document.getElementById(subsectionId + '-icon');

                if (content && icon) {
                    if (allSubsectionsExpanded) {
                        content.classList.remove('collapsed');
                        icon.classList.remove('collapsed');
                    } else {
                        content.classList.add('collapsed');
                        icon.classList.add('collapsed');
                    }
                }
            });

            if (allExpanded) {
                toggleAllText.textContent = '📕 收起全部';
                expandAllText.textContent = '📕 收起全部';
            } else {
                toggleAllText.textContent = '📖 展开全部';
                expandAllText.textContent = '📖 展开全部';
            }
        }

        // 根据URL参数自动展开指定内容
        function autoExpandFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            const expandTarget = urlParams.get('expand');

            if (expandTarget) {
                // 查找包含目标文本的元素
                const allElements = document.querySelectorAll('.function-title, .subsection-toggle, .section-toggle');

                for (let element of allElements) {
                    const text = element.textContent || element.innerText;
                    if (text.includes(expandTarget)) {
                        // 找到匹配的元素，展开其所在的章节和子章节
                        expandToTarget(element);
                        // 滚动到目标位置
                        setTimeout(() => {
                            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            // 高亮显示目标元素
                            highlightElement(element);
                        }, 300);
                        break;
                    }
                }
            }
        }

        // 展开到目标元素
        function expandToTarget(targetElement) {
            let current = targetElement;

            // 向上查找并展开所有父级章节
            while (current) {
                if (current.classList && current.classList.contains('section-content')) {
                    // 展开章节
                    const sectionId = current.id.replace('-content', '');
                    const icon = document.getElementById(sectionId + '-icon');
                    if (icon && icon.classList.contains('collapsed')) {
                        toggleSection(sectionId);
                    }
                }

                if (current.classList && current.classList.contains('subsection-content')) {
                    // 展开子章节
                    const subsectionId = current.id.replace('-content', '');
                    const icon = document.getElementById(subsectionId + '-icon');
                    if (icon && icon.classList.contains('collapsed')) {
                        toggleSubsection(subsectionId);
                    }
                }

                current = current.parentElement;
            }
        }

        // 高亮显示元素
        function highlightElement(element) {
            element.style.backgroundColor = '#fff3cd';
            element.style.border = '2px solid #ffc107';
            element.style.borderRadius = '4px';
            element.style.padding = '8px';
            element.style.transition = 'all 0.3s ease';

            // 3秒后移除高亮
            setTimeout(() => {
                element.style.backgroundColor = '';
                element.style.border = '';
                element.style.borderRadius = '';
                element.style.padding = '';
            }, 3000);
        }

        // 返回功能
        function goBack() {
            const fromHelp = sessionStorage.getItem('helpFromPage');
            if (fromHelp) {
                sessionStorage.removeItem('helpFromPage');
                window.location.href = fromHelp;
            } else {
                window.location.href = 'index.html';
            }
        }

        // 页面加载时处理URL参数
        document.addEventListener('DOMContentLoaded', function() {
            autoExpandFromUrl();
        });
    </script>
</body>
</html>
