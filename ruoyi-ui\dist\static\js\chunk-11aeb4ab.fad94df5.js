(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-11aeb4ab"],{"0cfc":function(e,t,a){"use strict";a.d(t,"d",(function(){return i})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){return o})),a.d(t,"e",(function(){return l})),a.d(t,"b",(function(){return s}));var n=a("b775");function i(e){return Object(n["a"])({url:"/game/type/list",method:"get",params:e})}function r(){return Object(n["a"])({url:"/game/type/all",method:"get"})}function o(e){return Object(n["a"])({url:"/game/type",method:"post",data:e})}function l(e){return Object(n["a"])({url:"/game/type",method:"put",data:e})}function s(e){return Object(n["a"])({url:"/game/type/"+e,method:"delete"})}},"17c6":function(e,t,a){"use strict";a("828f")},"28e5":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"类型ID",prop:"typeId"}},[a("el-input",{attrs:{placeholder:"请输入类型ID",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.typeId,callback:function(t){e.$set(e.queryParams,"typeId",t)},expression:"queryParams.typeId"}})],1),a("el-form-item",{attrs:{label:"玩法名称",prop:"typeName"}},[a("el-input",{attrs:{placeholder:"请输入玩法名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.typeName,callback:function(t){e.$set(e.queryParams,"typeName",t)},expression:"queryParams.typeName"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["game:type:add"],expression:"['game:type:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["game:type:edit"],expression:"['game:type:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["game:type:remove"],expression:"['game:type:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["game:type:export"],expression:"['game:type:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.typeList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),e._e(),a("el-table-column",{attrs:{label:"类型ID",align:"center",prop:"typeId"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tag",{staticStyle:{"font-size":"14px","font-weight":"700",height:"32px",width:"32px","line-height":"32px",padding:"0","min-width":"32px","border-radius":"50%",display:"inline-flex","align-items":"center","justify-content":"center"},attrs:{type:"primary",effect:"dark",size:"medium"}},[e._v(e._s(t.row.typeId))])]}}])}),a("el-table-column",{attrs:{label:"玩法名称",align:"center",prop:"typeName"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tag",{staticStyle:{"font-size":"14px","font-weight":"700",height:"36px","line-height":"36px",padding:"0 15px","min-width":"80px"},attrs:{type:"primary",effect:"dark",size:"medium"}},[e._v(e._s(t.row.typeName))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["game:type:edit"],expression:"['game:type:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["game:type:remove"],expression:"['game:type:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"类型ID",prop:"typeId"}},[a("el-input",{attrs:{placeholder:"请输入类型ID",disabled:null!=e.form.ID},model:{value:e.form.typeId,callback:function(t){e.$set(e.form,"typeId",t)},expression:"form.typeId"}})],1),a("el-form-item",{attrs:{label:"玩法名称",prop:"typeName"}},[a("el-input",{attrs:{placeholder:"请输入玩法名称"},model:{value:e.form.typeName,callback:function(t){e.$set(e.form,"typeName",t)},expression:"form.typeName"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},i=[],r=a("5530"),o=(a("d81d"),a("a9e3"),a("d3b7"),a("0643"),a("a573"),a("0cfc")),l={name:"Type",data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,typeList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:50,typeId:null,typeName:null},form:{},rules:{typeId:[{required:!0,message:"类型ID不能为空",trigger:"blur"},{type:"number",message:"类型ID必须为数字",trigger:"blur",transform:function(e){return Number(e)}}],typeName:[{required:!0,message:"玩法名称不能为空",trigger:"blur"},{min:2,max:20,message:"玩法名称长度必须在2到20个字符之间",trigger:"blur"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,Object(o["d"])(this.queryParams).then((function(t){e.typeList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={ID:null,typeId:null,typeName:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加玩法管理"},handleUpdate:function(e){this.reset(),console.log("修改行数据:",e),console.log("row.id:",e.id),console.log("row.ID:",e.ID),e.id?(this.form={ID:e.id,typeId:e.typeId,typeName:e.typeName},console.log("表单数据:",this.form),this.open=!0,this.title="修改玩法管理"):this.$modal.msgError("数据ID不能为空")},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){if(t)if(null!=e.form.ID){var a={id:e.form.ID,typeName:e.form.typeName};console.log("提交数据:",a),Object(o["e"])(a).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})).catch((function(t){console.error("修改失败:",t),e.$modal.msgError("修改失败："+t)}))}else Object(o["d"])({typeId:e.form.typeId}).then((function(t){t.rows&&t.rows.length>0?e.$modal.msgError("类型ID已存在，请重新输入"):Object(o["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()}))}))}))},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除玩法管理编号为"'+a+'"的数据项？').then((function(){return Object(o["b"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("game/type/export",Object(r["a"])({},this.queryParams),"type_".concat((new Date).getTime(),".xlsx"))}}},s=l,c=(a("17c6"),a("2877")),m=Object(c["a"])(s,n,i,!1,null,"69d1640f",null);t["default"]=m.exports},"828f":function(e,t,a){}}]);