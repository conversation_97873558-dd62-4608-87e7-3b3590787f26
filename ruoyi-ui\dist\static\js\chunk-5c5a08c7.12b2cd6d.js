(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5c5a08c7"],{"0901":function(t,s,e){"use strict";e.d(s,"e",(function(){return i})),e.d(s,"d",(function(){return n})),e.d(s,"a",(function(){return o})),e.d(s,"f",(function(){return c})),e.d(s,"c",(function(){return r})),e.d(s,"b",(function(){return l}));var a=e("b775");function i(t){return Object(a["a"])({url:"/game/customer/list",method:"get",params:t})}function n(t){return Object(a["a"])({url:"/game/customer/"+t,method:"get"})}function o(t){return Object(a["a"])({url:"/game/customer",method:"post",data:t})}function c(t){return Object(a["a"])({url:"/game/customer",method:"put",data:t})}function r(t){return Object(a["a"])({url:"/game/customer/"+t,method:"delete"})}function l(){return Object(a["a"])({url:"/game/customer/checkAndSetDefault",method:"get"})}},"0ccb":function(t,s,e){"use strict";var a=e("e330"),i=e("50c4"),n=e("577e"),o=e("1148"),c=e("1d80"),r=a(o),l=a("".slice),u=Math.ceil,d=function(t){return function(s,e,a){var o,d,m=n(c(s)),v=i(e),f=m.length,g=void 0===a?" ":n(a);return v<=f||""===g?m:(o=v-f,d=r(g,u(o/g.length)),d.length>o&&(d=l(d,0,o)),t?m+d:d+m)}};t.exports={start:d(!1),end:d(!0)}},"1e4b":function(t,s,e){"use strict";e.r(s);var a=function(){var t=this,s=t.$createElement,e=t._self._c||s;return e("div",{staticClass:"app-container home"},[t.hasError?e("el-alert",{staticStyle:{"margin-bottom":"20px"},attrs:{title:t.errorMessage,type:"warning",closable:!1,"show-icon":""}}):t._e(),t.isAdmin?t._e():e("el-row",{staticStyle:{"margin-bottom":"24px"},attrs:{gutter:20}},[e("el-col",{attrs:{span:24}},[e("div",{staticClass:"auth-status-card"},[e("div",{staticClass:"auth-header"},[e("div",{staticClass:"auth-icon"},[e("i",{staticClass:"el-icon-user"})]),e("div",{staticClass:"auth-title"},[e("h3",[t._v(t._s(t.userInfo.nickName||t.userInfo.userName||"用户"))]),e("span",{staticClass:"auth-subtitle"},[t._v("授权状态")])]),e("div",{staticClass:"auth-status"},[e("div",{staticClass:"status-badge",class:t.getAuthStatusClass()},[e("i",{class:t.getAuthStatusIcon()}),e("span",[t._v(t._s(t.getAuthStatusText(t.userInfo.authStatus)))])])])]),e("div",{staticClass:"auth-body"},[t.userInfo.authEndTime?e("div",{staticClass:"auth-info-grid"},[e("div",{staticClass:"info-item"},[e("div",{staticClass:"info-icon expire-icon"},[e("i",{staticClass:"el-icon-time"})]),e("div",{staticClass:"info-content"},[e("div",{staticClass:"info-label"},[t._v("到期时间")]),e("div",{staticClass:"info-value",class:t.getExpireTimeClass()},[t._v(" "+t._s(t.formatDate(t.userInfo.authEndTime))+" ")])])]),null!==t.getRemainingDays()?e("div",{staticClass:"info-item"},[e("div",{staticClass:"info-icon days-icon"},[e("i",{staticClass:"el-icon-calendar"})]),e("div",{staticClass:"info-content"},[e("div",{staticClass:"info-label"},[t._v("剩余时间")]),e("div",{staticClass:"info-value",class:t.getRemainingDaysClass()},[t._v(" "+t._s(t.getRemainingDaysText())+" ")])])]):t._e()]):e("div",{staticClass:"auth-info-single"},[e("div",{staticClass:"no-auth"},[e("i",{staticClass:"el-icon-warning"}),e("span",[t._v("未设置授权时间")])])])])])])],1),e("el-divider"),t.isAdmin?e("div",[e("el-row",{staticStyle:{"margin-bottom":"24px"},attrs:{gutter:24}},[e("el-col",{attrs:{xs:24,sm:24,md:12,lg:12}},[e("div",{staticClass:"admin-ticket-card"},[e("div",{staticClass:"card-header"},[e("div",{staticClass:"header-left"},[e("div",{staticClass:"header-icon"},[e("i",{staticClass:"el-icon-message"})]),e("div",{staticClass:"header-text"},[e("h3",[t._v("工单管理")]),e("span",{staticClass:"header-subtitle"},[t._v("客服工单统计")])])]),e("div",{staticClass:"header-actions"},[e("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-view"},on:{click:t.handleViewTickets}},[t._v(" 查看全部 ")])],1)]),e("div",{staticClass:"ticket-stats-grid"},[e("div",{staticClass:"ticket-stat-item pending",on:{click:function(s){return t.handleViewTicketsByStatus("0")}}},[e("div",{staticClass:"stat-icon"},[e("i",{staticClass:"el-icon-clock"})]),e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-number"},[t._v(t._s(t.ticketStats.pending||0))]),e("div",{staticClass:"stat-label"},[t._v("待处理")])])]),e("div",{staticClass:"ticket-stat-item processing",on:{click:function(s){return t.handleViewTicketsByStatus("1")}}},[e("div",{staticClass:"stat-icon"},[e("i",{staticClass:"el-icon-loading"})]),e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-number"},[t._v(t._s(t.ticketStats.processing||0))]),e("div",{staticClass:"stat-label"},[t._v("处理中")])])]),e("div",{staticClass:"ticket-stat-item urgent",on:{click:function(s){return t.handleViewTicketsByStatus("urgent")}}},[e("div",{staticClass:"stat-icon"},[e("i",{staticClass:"el-icon-warning"})]),e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-number"},[t._v(t._s(t.ticketStats.urgent||0))]),e("div",{staticClass:"stat-label"},[t._v("紧急工单")])])]),e("div",{staticClass:"ticket-stat-item total",on:{click:t.handleViewTickets}},[e("div",{staticClass:"stat-icon"},[e("i",{staticClass:"el-icon-tickets"})]),e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-number"},[t._v(t._s(t.ticketStats.total||0))]),e("div",{staticClass:"stat-label"},[t._v("总计")])])])])])]),e("el-col",{attrs:{xs:24,sm:24,md:12,lg:12}},[e("div",{staticClass:"admin-notice-card"},[e("div",{staticClass:"notice-header"},[e("div",{staticClass:"notice-icon"},[e("i",{staticClass:"el-icon-bell"})]),e("div",{staticClass:"notice-title"},[e("h3",[t._v(t._s(t.noticeInfo.noticeTitle||"系统公告"))]),e("span",{staticClass:"notice-subtitle"},[t._v("重要提醒")])]),e("div",{staticClass:"notice-actions"},[e("el-button",{staticClass:"edit-btn",attrs:{type:"primary",size:"mini",icon:"el-icon-edit"},on:{click:t.handleEditNotice}},[t._v(" 编辑公告 ")])],1)]),e("div",{staticClass:"notice-body"},[t.noticeInfo.noticeContent?e("div",{staticClass:"notice-content-text"},t._l(t.getNoticeLines(),(function(s,a){return e("div",{key:a,staticClass:"notice-line"},[e("div",{staticClass:"notice-dot",class:t.getNoticeDotClass(a)}),e("div",{staticClass:"notice-text"},[t._v(t._s(s))])])})),0):e("div",{staticClass:"notice-empty"},[e("i",{staticClass:"el-icon-info"}),e("span",[t._v("暂无公告内容")])])])])])],1),e("el-row",{attrs:{gutter:24}},[e("el-col",{attrs:{span:24}},[e("div",{staticClass:"admin-stats-section"},[e("div",{staticClass:"section-header"},[e("div",{staticClass:"header-left"},[e("div",{staticClass:"header-icon"},[e("i",{staticClass:"el-icon-data-analysis"})]),e("div",{staticClass:"header-text"},[e("h3",[t._v("用户数据统计")]),e("span",{staticClass:"header-subtitle"},[t._v("各用户玩家数据概览")])])]),e("div",{staticClass:"header-actions"},[e("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-refresh",loading:t.loadingUserStats},on:{click:t.refreshUserStats}},[t._v(" 刷新数据 ")])],1)]),e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loadingUserStats,expression:"loadingUserStats"}],staticClass:"user-stats-grid"},[t._l(t.userStatsList,(function(s){return e("div",{key:s.userId,staticClass:"user-stats-card"},[e("div",{staticClass:"user-card-header"},[e("div",{staticClass:"user-info"},[e("div",{staticClass:"user-avatar"},[e("i",{staticClass:"el-icon-user"})]),e("div",{staticClass:"user-details"},[e("div",{staticClass:"user-name"},[t._v(t._s(s.nickName||s.userName))]),e("div",{staticClass:"user-id"},[t._v("ID: "+t._s(s.userId)+" ("+t._s(s.userName)+")")])])]),e("div",{staticClass:"user-status"},[e("el-tag",{attrs:{type:"0"===s.status?"success":"danger",size:"mini"}},[t._v(" "+t._s("0"===s.status?"正常":"禁用")+" ")])],1)]),e("div",{staticClass:"user-total-stats"},[e("div",{staticClass:"total-stats-header"},[e("i",{staticClass:"el-icon-data-analysis"}),e("span",[t._v("用户总统计")])]),e("div",{staticClass:"total-stats-grid"},[e("div",{staticClass:"total-stat-item"},[e("div",{staticClass:"stat-value"},[t._v("￥"+t._s(t.formatNumber(s.totalBetAmount)))]),e("div",{staticClass:"stat-label"},[t._v("总下注")])]),e("div",{staticClass:"total-stat-item"},[e("div",{staticClass:"stat-value"},[t._v("￥"+t._s(t.formatNumber(s.totalWinAmount)))]),e("div",{staticClass:"stat-label"},[t._v("总中奖")])]),e("div",{staticClass:"total-stat-item"},[e("div",{staticClass:"stat-value",class:t.getProfitClass(s.totalProfit)},[t._v(" ￥"+t._s(t.formatNumber(s.totalProfit))+" ")]),e("div",{staticClass:"stat-label"},[t._v("总盈利")])])])]),e("div",{staticClass:"customers-stats"},[e("div",{staticClass:"customers-header"},[e("i",{staticClass:"el-icon-user-solid"}),e("span",[t._v("玩家统计 ("+t._s(s.customerCount)+"个)")])]),e("div",{staticClass:"customers-list"},t._l(s.customers,(function(s){return e("div",{key:s.customerId,staticClass:"customer-item"},[e("div",{staticClass:"customer-header"},[e("div",{staticClass:"customer-info"},[e("div",{staticClass:"customer-name"},[t._v(t._s(s.customerName||s.name||s.nickName||"未知玩家"))])]),e("div",{staticClass:"customer-status"})]),e("div",{staticClass:"customer-stats-grid"},[e("div",{staticClass:"customer-stat"},[e("div",{staticClass:"stat-value"},[t._v("￥"+t._s(t.formatNumber(s.totalBetAmount)))]),e("div",{staticClass:"stat-label"},[t._v("下注额")])]),e("div",{staticClass:"customer-stat"},[e("div",{staticClass:"stat-value"},[t._v("￥"+t._s(t.formatNumber(s.totalWinAmount)))]),e("div",{staticClass:"stat-label"},[t._v("中奖额")])]),e("div",{staticClass:"customer-stat"},[e("div",{staticClass:"stat-value",class:t.getProfitClass(s.totalProfit)},[t._v(" ￥"+t._s(t.formatNumber(s.totalProfit))+" ")]),e("div",{staticClass:"stat-label"},[t._v("盈利")])])])])})),0)])])})),0!==t.userStatsList.length||t.loadingUserStats?t._e():e("div",{staticClass:"no-data-card"},[e("div",{staticClass:"no-data-content"},[e("i",{staticClass:"el-icon-data-analysis"}),e("h4",[t._v("暂无用户数据")]),e("p",[t._v("当前没有用户统计数据")])])])],2)])])],1)],1):e("div",[e("el-row",{attrs:{gutter:24}},[e("el-col",{attrs:{xs:24,sm:24,md:12,lg:12}},[e("div",{staticClass:"stats-card"},[e("div",{staticClass:"stats-header"},[e("div",{staticClass:"stats-icon"},[e("i",{staticClass:"el-icon-data-line"})]),e("div",{staticClass:"stats-title"},[e("h3",[t._v("数据统计")]),e("span",{staticClass:"stats-subtitle"},[t._v("累计数据概览")])])]),e("div",{staticClass:"stats-grid"},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-icon bet-icon"},[e("i",{staticClass:"el-icon-money"})]),e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-value"},[t._v("￥"+t._s(t.formatNumber(t.totalStats.totalBetAmount)))]),e("div",{staticClass:"stat-label"},[t._v("下注总额")])])]),e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-icon count-icon"},[e("i",{staticClass:"el-icon-document"})]),e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.formatNumber(t.totalStats.totalBets)))]),e("div",{staticClass:"stat-label"},[t._v("下注数量")])])]),e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-icon win-icon"},[e("i",{staticClass:"el-icon-trophy"})]),e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-value"},[t._v("￥"+t._s(t.formatNumber(t.totalStats.totalWinAmount)))]),e("div",{staticClass:"stat-label"},[t._v("中奖金额")])])]),e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-icon profit-icon"},[e("i",{staticClass:"el-icon-coin"})]),e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-value profit-value",class:t.getProfitClass(t.totalStats.totalProfit)},[t._v(" ￥"+t._s(t.formatNumber(t.totalStats.totalProfit))+" ")]),e("div",{staticClass:"stat-label"},[t._v("盈利金额")])])])])])]),e("el-col",{attrs:{xs:24,sm:24,md:12,lg:12}},[e("div",{staticClass:"notice-card"},[e("div",{staticClass:"notice-header"},[e("div",{staticClass:"notice-icon"},[e("i",{staticClass:"el-icon-bell"})]),e("div",{staticClass:"notice-title"},[e("h3",[t._v(t._s(t.noticeInfo.noticeTitle||"系统公告"))]),e("span",{staticClass:"notice-subtitle"},[t._v("重要提醒")])])]),e("div",{staticClass:"notice-body"},[t.noticeInfo.noticeContent?e("div",{staticClass:"notice-content-text"},t._l(t.getNoticeLines(),(function(s,a){return e("div",{key:a,staticClass:"notice-line"},[e("div",{staticClass:"notice-dot",class:t.getNoticeDotClass(a)}),e("div",{staticClass:"notice-text"},[t._v(t._s(s))])])})),0):e("div",{staticClass:"notice-empty"},[e("i",{staticClass:"el-icon-info"}),e("span",[t._v("暂无公告内容")])])])])])],1),e("el-row",{staticStyle:{"margin-top":"24px"},attrs:{gutter:24}},[e("el-col",{attrs:{span:24}},[e("div",{staticClass:"user-players-section"},[e("div",{staticClass:"section-header"},[e("div",{staticClass:"header-left"},[e("div",{staticClass:"header-icon"},[e("i",{staticClass:"el-icon-user-solid"})]),e("div",{staticClass:"header-text"},[e("h3",[t._v("玩家数据统计")])])]),e("div",{staticClass:"header-actions"},[e("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-refresh",loading:t.loadingPlayerStats},on:{click:t.refreshPlayerStats}},[t._v(" 刷新数据 ")])],1)]),e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loadingPlayerStats,expression:"loadingPlayerStats"}],staticClass:"player-stats-grid"},[t._l(t.playerStatsList,(function(s){return e("div",{key:s.userId,staticClass:"player-stats-card"},[e("div",{staticClass:"player-card-header"},[e("div",{staticClass:"player-info"},[e("div",{staticClass:"player-avatar"},[e("i",{staticClass:"el-icon-user-solid"})]),e("div",{staticClass:"player-details"},[e("div",{staticClass:"player-name"},[t._v(t._s(s.name||"未知玩家"))]),e("div",{staticClass:"player-id"},[t._v("ID: "+t._s(s.userId))])])])]),e("div",{staticClass:"player-stats-content"},[e("div",{staticClass:"player-stat-item"},[e("div",{staticClass:"stat-icon bet-icon"},[e("i",{staticClass:"el-icon-money"})]),e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-value"},[t._v("￥"+t._s(t.formatNumber(s.totalBetAmount)))]),e("div",{staticClass:"stat-label"},[t._v("下注总额")])])]),e("div",{staticClass:"player-stat-item"},[e("div",{staticClass:"stat-icon win-icon"},[e("i",{staticClass:"el-icon-trophy"})]),e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-value"},[t._v("￥"+t._s(t.formatNumber(s.totalWinAmount)))]),e("div",{staticClass:"stat-label"},[t._v("中奖金额")])])]),e("div",{staticClass:"player-stat-item"},[e("div",{staticClass:"stat-icon profit-icon"},[e("i",{staticClass:"el-icon-coin"})]),e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-value",class:t.getProfitClass(s.totalProfit)},[t._v(" ￥"+t._s(t.formatNumber(s.totalProfit))+" ")]),e("div",{staticClass:"stat-label"},[t._v("盈利金额")])])])])])})),0!==t.playerStatsList.length||t.loadingPlayerStats?t._e():e("div",{staticClass:"no-data-card"},[e("div",{staticClass:"no-data-content"},[e("i",{staticClass:"el-icon-user-solid"}),e("h4",[t._v("暂无玩家数据")]),e("p",[t._v("当前没有玩家统计数据")])])])],2)])])],1)],1),e("el-dialog",{attrs:{title:"编辑首页公告",visible:t.noticeDialogVisible,width:"800px","append-to-body":"","close-on-click-modal":!1},on:{"update:visible":function(s){t.noticeDialogVisible=s}}},[e("el-form",{ref:"noticeForm",attrs:{model:t.noticeForm,rules:t.noticeRules,"label-width":"100px"}},[e("el-form-item",{attrs:{label:"公告标题",prop:"noticeTitle"}},[e("el-input",{attrs:{placeholder:"请输入公告标题",maxlength:"50","show-word-limit":""},model:{value:t.noticeForm.noticeTitle,callback:function(s){t.$set(t.noticeForm,"noticeTitle",s)},expression:"noticeForm.noticeTitle"}})],1),e("el-form-item",{attrs:{label:"公告内容",prop:"noticeContent"}},[e("el-input",{attrs:{type:"textarea",placeholder:"请输入公告内容，每行一条公告",rows:8,maxlength:"1000","show-word-limit":""},model:{value:t.noticeForm.noticeContent,callback:function(s){t.$set(t.noticeForm,"noticeContent",s)},expression:"noticeForm.noticeContent"}})],1),e("el-form-item",{attrs:{label:"公告状态",prop:"status"}},[e("el-radio-group",{model:{value:t.noticeForm.status,callback:function(s){t.$set(t.noticeForm,"status",s)},expression:"noticeForm.status"}},[e("el-radio",{attrs:{label:"0"}},[t._v("正常")]),e("el-radio",{attrs:{label:"1"}},[t._v("关闭")])],1)],1)],1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(s){t.noticeDialogVisible=!1}}},[t._v("取 消")]),e("el-button",{attrs:{type:"primary",loading:t.noticeSubmitting},on:{click:t.submitNoticeForm}},[t._v("确 定")])],1)],1),e("el-divider")],1)},i=[],n=e("3835"),o=e("b85c"),c=e("c14f"),r=e("1da1"),l=e("5530"),u=(e("99af"),e("4de4"),e("7db0"),e("d81d"),e("14d9"),e("fb6a"),e("4e82"),e("b0c0"),e("4ec9"),e("a9e3"),e("d3b7"),e("3ca3"),e("4d90"),e("498a"),e("0643"),e("2382"),e("fffc"),e("4e3e"),e("a573"),e("159b"),e("ddb0"),e("b775")),d=e("8b29"),m=e("58dc"),v=e("0901"),f={name:"Index",data:function(){return{userInfo:{userId:null,userName:"",nickName:"",authStatus:"",authEndTime:null,authStartTime:null,authDurationType:""},noticeInfo:{noticeId:null,noticeTitle:"",noticeContent:""},noticeDialogVisible:!1,noticeSubmitting:!1,noticeForm:{noticeId:null,noticeTitle:"",noticeContent:"",noticeType:"2",status:"0"},noticeRules:{noticeTitle:[{required:!0,message:"公告标题不能为空",trigger:"blur"},{max:50,message:"公告标题不能超过50个字符",trigger:"blur"}],noticeContent:[{required:!0,message:"公告内容不能为空",trigger:"blur"},{max:1e3,message:"公告内容不能超过1000个字符",trigger:"blur"}]},todayStats:{totalBetAmount:0,totalBets:0,totalWinAmount:0,totalProfit:0},monthStats:{totalBetAmount:0,totalBets:0,totalWinAmount:0,totalProfit:0},totalStats:{totalBetAmount:0,totalBets:0,totalWinAmount:0,totalProfit:0},ticketStats:{pending:0,processing:0,resolved:0,closed:0,total:0,todayNew:0,urgent:0},latestTickets:[],userStatsList:[],loadingUserStats:!1,playerStatsList:[],loadingPlayerStats:!1,hasError:!1,errorMessage:""}},created:function(){this.getUserInfo(),this.getNoticeInfo(),this.getTodayStats(),this.getMonthStats(),this.getTotalStats()},computed:{isAdmin:function(){return 1===this.userInfo.userId}},methods:{handleError:function(t){if(t.response){var s=t.response.status;403!==s&&404!==s||(this.hasError=!0,this.errorMessage="系统未激活，请先激活系统")}},getUserInfo:function(){var t=this;Object(u["a"])({url:"/getInfo",method:"get"}).then((function(s){200===s.code&&s.user&&(t.userInfo=s.user,1===t.userInfo.userId?(console.log("检测到超级管理员，开始获取工单数据和用户统计"),t.getTicketData(),t.getUserStats()):(console.log("检测到普通用户，开始获取玩家统计数据"),t.getPlayerStats()))})).catch((function(t){console.error("获取用户信息失败:",t)}))},getNoticeInfo:function(){var t=this;Object(d["c"])().then((function(s){200===s.code&&s.data&&(t.noticeInfo=s.data)})).catch((function(t){console.error("获取公告信息失败:",t)}))},getTodayStats:function(){var t=this;Object(u["a"])({url:"/game/draw/stats/today",method:"get"}).then((function(s){200===s.code&&(t.todayStats=s.data)})).catch((function(s){t.handleError(s)}))},getMonthStats:function(){var t=this;Object(u["a"])({url:"/game/draw/stats/month",method:"get"}).then((function(s){200===s.code&&(t.monthStats=s.data)})).catch((function(s){t.handleError(s)}))},getTotalStats:function(){var t=this;Object(u["a"])({url:"/game/draw/stats/total",method:"get"}).then((function(s){200===s.code?t.totalStats=s.data:console.error("累计统计API返回错误:",s)})).catch((function(s){console.error("累计统计API调用失败:",s),t.handleError(s)}))},getAuthStatusType:function(t){if(1===this.userInfo.userId)return"success";switch(t){case"1":return"success";case"2":return"danger";default:return"warning"}},getAuthStatusText:function(t){if(1===this.userInfo.userId)return"永久授权";switch(t){case"1":return"正常";case"2":return"已过期";default:return"未授权"}},formatDateTime:function(t){if(!t)return"";var s=new Date(t),e=s.getFullYear(),a=String(s.getMonth()+1).padStart(2,"0"),i=String(s.getDate()).padStart(2,"0"),n=String(s.getHours()).padStart(2,"0"),o=String(s.getMinutes()).padStart(2,"0"),c=String(s.getSeconds()).padStart(2,"0");return"".concat(e,"-").concat(a,"-").concat(i," ").concat(n,":").concat(o,":").concat(c)},formatDate:function(t){if(!t)return"";var s=new Date(t),e=s.getFullYear(),a=String(s.getMonth()+1).padStart(2,"0"),i=String(s.getDate()).padStart(2,"0");return"".concat(e,"-").concat(a,"-").concat(i)},formatNumber:function(t){return null===t||void 0===t?"0":Number(t).toLocaleString()},getProfitClass:function(t){return t>0?"profit-positive":t<0?"profit-negative":"profit-zero"},getAuthStatusClass:function(){if(1===this.userInfo.userId)return"status-permanent";switch(this.userInfo.authStatus){case"1":return"status-normal";case"2":return"status-expired";default:return"status-unauthorized"}},getAuthStatusIcon:function(){if(1===this.userInfo.userId)return"el-icon-star-on";switch(this.userInfo.authStatus){case"1":return"el-icon-circle-check";case"2":return"el-icon-circle-close";default:return"el-icon-warning"}},getRemainingDays:function(){if(!this.userInfo.authEndTime||1===this.userInfo.userId)return null;var t=new Date,s=new Date(this.userInfo.authEndTime),e=s-t,a=Math.ceil(e/864e5);return a},getRemainingDaysText:function(){var t=this.getRemainingDays();return null===t?"":t>0?"".concat(t," 天"):0===t?"今天到期":"已过期 ".concat(Math.abs(t)," 天")},getRemainingDaysClass:function(){var t=this.getRemainingDays();return null===t?"":t>7?"days-safe":t>3?"days-warning":t>0?"days-danger":"days-expired"},getExpireTimeClass:function(){var t=this.getRemainingDays();return null===t?"":t>7?"time-safe":t>3?"time-warning":t>0?"time-danger":"time-expired"},getNoticeLines:function(){return this.noticeInfo.noticeContent?this.noticeInfo.noticeContent.split("\n").filter((function(t){return t.trim()})):[]},getNoticeDotClass:function(t){var s=["warning","danger","info","success"];return s[t%s.length]},handleEditNotice:function(){this.noticeForm={noticeId:this.noticeInfo.noticeId,noticeTitle:this.noticeInfo.noticeTitle||"系统公告",noticeContent:this.noticeInfo.noticeContent||"",noticeType:"2",status:"0"},this.noticeDialogVisible=!0},submitNoticeForm:function(){var t=this;this.$refs.noticeForm.validate((function(s){if(s){t.noticeSubmitting=!0;var e,a=Object(l["a"])({},t.noticeForm);e=t.noticeForm.noticeId?Object(d["f"])(a):Object(d["a"])(a),e.then((function(){t.$modal.msgSuccess(t.noticeForm.noticeId?"修改成功":"新增成功"),t.noticeDialogVisible=!1,t.getNoticeInfo()})).catch((function(s){console.error("保存公告失败:",s),t.$modal.msgError("保存失败："+(s.message||s))})).finally((function(){t.noticeSubmitting=!1}))}}))},getTicketData:function(){var t=this;return Object(r["a"])(Object(c["a"])().m((function s(){var e,a,i;return Object(c["a"])().w((function(s){while(1)switch(s.n){case 0:if(t.isAdmin){s.n=1;break}return console.log("非管理员用户，跳过工单数据获取"),s.a(2);case 1:return console.log("开始获取工单数据..."),s.p=2,console.log("正在获取工单统计..."),s.n=3,Object(m["g"])();case 3:return e=s.v,console.log("工单统计响应:",e),200===e.code?(t.ticketStats=e.data,console.log("工单统计数据:",t.ticketStats)):console.error("工单统计API返回错误:",e),console.log("正在获取最新工单..."),s.n=4,Object(m["e"])(5);case 4:a=s.v,console.log("最新工单响应:",a),200===a.code?(t.latestTickets=a.data,console.log("最新工单数据:",t.latestTickets)):console.error("最新工单API返回错误:",a),s.n=6;break;case 5:s.p=5,i=s.v,console.error("获取工单数据失败:",i),i.response&&(console.error("错误响应:",i.response),console.error("错误状态:",i.response.status),console.error("错误数据:",i.response.data));case 6:return s.a(2)}}),s,null,[[2,5]])})))()},handleViewTickets:function(){this.$router.push("/system/ticket-admin")},handleViewTicketDetail:function(t){this.$router.push({path:"/system/ticket-admin",query:{ticketId:t.ticketId}})},handleViewTicketsByStatus:function(t){var s=t;"urgent"===t?this.$router.push({path:"/system/ticket-admin",query:{priority:"4"}}):this.$router.push({path:"/system/ticket-admin",query:{status:s}})},getTicketStatusType:function(t){var s={0:"info",1:"warning",2:"success",3:"danger"};return s[t]||"info"},getTicketStatusText:function(t){var s={0:"待处理",1:"处理中",2:"已解决",3:"已关闭"};return s[t]||"未知"},formatTicketTime:function(t){if(!t)return"";var s=new Date(t),e=new Date,a=e-s,i=Math.floor(a/6e4),n=Math.floor(a/36e5),o=Math.floor(a/864e5);return i<1?"刚刚":i<60?"".concat(i,"分钟前"):n<24?"".concat(n,"小时前"):o<7?"".concat(o,"天前"):this.parseTime(t,"{m}-{d} {h}:{i}")},getUserStats:function(){var t=this;return Object(r["a"])(Object(c["a"])().m((function s(){var e,a,i,r,d,m,v,f,g,C,h,p,b;return Object(c["a"])().w((function(s){while(1)switch(s.n){case 0:if(t.isAdmin){s.n=1;break}return console.log("非管理员用户，跳过用户统计数据获取"),s.a(2);case 1:return t.loadingUserStats=!0,console.log("开始获取用户统计数据..."),s.p=2,s.n=3,Object(u["a"])({url:"/system/user/list",method:"get",params:{pageNum:1,pageSize:1e3}});case 3:if(e=s.v,e&&e.rows){s.n=4;break}return t.userStatsList=[],s.a(2);case 4:return a=e.rows.filter((function(t){return"0"===t.status&&1!==t.userId})),console.log("所有用户:",a),console.log("开始获取玩家数据..."),s.n=5,Object(u["a"])({url:"/game/customer/list",method:"get",params:{pageNum:1,pageSize:1e5}});case 5:i=s.v,console.log("玩家API响应:",i),r=i&&i.rows?i.rows:[],console.log("所有玩家数量:",r.length),console.log("前3个玩家:",r.slice(0,3)),d=new Map,r.forEach((function(t){var s=parseInt(t.sysUserId),e=parseInt(t.userId),i=parseFloat(t.totalBetAmount)||0,n=parseFloat(t.totalWinAmount)||0;if(1!==s&&!(i<=0)){console.log("处理玩家: 用户".concat(s," 玩家").concat(e,"(").concat(t.name,") 下注").concat(i," 中奖").concat(n));var o=a.find((function(t){return parseInt(t.userId)===s}));if(o){d.has(s)||(d.set(s,Object(l["a"])(Object(l["a"])({},o),{},{totalBetAmount:0,totalWinAmount:0,totalProfit:0,customerCount:0,customers:[]})),console.log("初始化用户".concat(s,"(").concat(o.userName,")统计")));var c=d.get(s);c.totalBetAmount+=i,c.totalWinAmount+=n;var r={customerId:e,customerName:t.name,sysUserId:s,totalBetAmount:i,totalWinAmount:n,totalProfit:i-n};c.customers.push(r),c.customerCount+=1,console.log("用户".concat(s,"玩家").concat(e,"统计:"),r)}else console.log("找不到用户: sysUserId=".concat(s))}})),m=[],v=Object(o["a"])(d),s.p=6,v.s();case 7:if((f=v.n()).done){s.n=10;break}if(g=Object(n["a"])(f.value,2),C=g[0],h=g[1],0!==h.customerCount){s.n=8;break}return s.a(3,9);case 8:h.totalProfit=h.totalBetAmount-h.totalWinAmount,h.customers.sort((function(t,s){return s.totalBetAmount-t.totalBetAmount})),console.log("用户".concat(C,"(").concat(h.userName,") 最终统计:"),{totalBetAmount:h.totalBetAmount,totalBets:h.totalBets,totalWinAmount:h.totalWinAmount,totalProfit:h.totalProfit,customerCount:h.customerCount}),m.push(h);case 9:s.n=7;break;case 10:s.n=12;break;case 11:s.p=11,p=s.v,v.e(p);case 12:return s.p=12,v.f(),s.f(12);case 13:t.userStatsList=m.sort((function(t,s){return s.totalBetAmount-t.totalBetAmount})),console.log("用户统计数据获取完成:",t.userStatsList),s.n=15;break;case 14:s.p=14,b=s.v,console.error("获取用户统计数据失败:",b),t.userStatsList=[];case 15:return s.p=15,t.loadingUserStats=!1,s.f(15);case 16:return s.a(2)}}),s,null,[[6,11,12,13],[2,14,15,16]])})))()},refreshUserStats:function(){this.getUserStats()},getPlayerStats:function(){var t=this;return Object(r["a"])(Object(c["a"])().m((function s(){var e,a,i;return Object(c["a"])().w((function(s){while(1)switch(s.n){case 0:if(!t.isAdmin){s.n=1;break}return console.log("管理员用户，跳过玩家统计数据获取"),s.a(2);case 1:return t.loadingPlayerStats=!0,console.log("开始获取玩家统计数据..."),s.p=2,s.n=3,Object(v["e"])({pageNum:1,pageSize:1e3});case 3:if(e=s.v,console.log("玩家API响应:",e),e&&e.rows){s.n=4;break}return t.playerStatsList=[],s.a(2);case 4:a=e.rows.map((function(t){var s=parseFloat(t.totalBetAmount)||0,e=parseFloat(t.totalWinAmount)||0,a=s-e;return Object(l["a"])(Object(l["a"])({},t),{},{totalBetAmount:s,totalWinAmount:e,totalProfit:a})})),t.playerStatsList=a.sort((function(t,s){return s.totalBetAmount-t.totalBetAmount})),console.log("玩家统计数据获取完成:",t.playerStatsList),s.n=6;break;case 5:s.p=5,i=s.v,console.error("获取玩家统计数据失败:",i),t.playerStatsList=[];case 6:return s.p=6,t.loadingPlayerStats=!1,s.f(6);case 7:return s.a(2)}}),s,null,[[2,5,6,7]])})))()},refreshPlayerStats:function(){this.getPlayerStats()}}},g=f,C=(e("e256"),e("7cf9"),e("2877")),h=Object(C["a"])(g,a,i,!1,null,"2e44e8d2",null);s["default"]=h.exports},"4d90":function(t,s,e){"use strict";var a=e("23e7"),i=e("0ccb").start,n=e("9a0c");a({target:"String",proto:!0,forced:n},{padStart:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}})},"58dc":function(t,s,e){"use strict";e.d(s,"h",(function(){return i})),e.d(s,"f",(function(){return n})),e.d(s,"d",(function(){return o})),e.d(s,"c",(function(){return c})),e.d(s,"b",(function(){return r})),e.d(s,"a",(function(){return l})),e.d(s,"g",(function(){return u})),e.d(s,"e",(function(){return d}));var a=e("b775");function i(t){return Object(a["a"])({url:"/system/ticket/list",method:"get",params:t})}function n(t){return Object(a["a"])({url:"/system/ticket/"+t,method:"get"})}function o(t){return Object(a["a"])({url:"/system/ticket/"+t,method:"delete"})}function c(t,s){return Object(a["a"])({url:"/system/ticket/close/"+t,method:"put",data:s})}function r(t,s){return Object(a["a"])({url:"/system/ticket/assign/"+t,method:"put",data:s})}function l(t,s){return Object(a["a"])({url:"/system/ticket/reply/"+t,method:"post",data:s})}function u(){return Object(a["a"])({url:"/system/ticket/stats",method:"get"})}function d(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:5;return Object(a["a"])({url:"/system/ticket/latest",method:"get",params:{limit:t}})}},"60e6":function(t,s,e){},7603:function(t,s,e){},"7cf9":function(t,s,e){"use strict";e("60e6")},"8b29":function(t,s,e){"use strict";e.d(s,"e",(function(){return i})),e.d(s,"d",(function(){return n})),e.d(s,"a",(function(){return o})),e.d(s,"f",(function(){return c})),e.d(s,"b",(function(){return r})),e.d(s,"c",(function(){return l}));var a=e("b775");function i(t){return Object(a["a"])({url:"/system/notice/list",method:"get",params:t})}function n(t){return Object(a["a"])({url:"/system/notice/"+t,method:"get"})}function o(t){return Object(a["a"])({url:"/system/notice",method:"post",data:t})}function c(t){return Object(a["a"])({url:"/system/notice",method:"put",data:t})}function r(t){return Object(a["a"])({url:"/system/notice/"+t,method:"delete"})}function l(){return Object(a["a"])({url:"/system/notice/homepage",method:"get"})}},"9a0c":function(t,s,e){"use strict";var a=e("342f");t.exports=/Version\/10(?:\.\d+){1,2}(?: [\w./]+)?(?: Mobile\/\w+)? Safari\//.test(a)},e256:function(t,s,e){"use strict";e("7603")}}]);