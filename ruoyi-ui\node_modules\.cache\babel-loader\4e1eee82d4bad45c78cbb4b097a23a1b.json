{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetStatistics.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetStatistics.vue", "mtime": 1756001626956}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\babel.config.js", "mtime": 1750852368688}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750942928175}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750942928768}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJDOi9Vc2Vycy9BZG1pbmlzdHJhdG9yL0Rlc2t0b3AvZHVvM2QvcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfb2JqZWN0U3ByZWFkMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL2R1bzNkL3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL29iamVjdFNwcmVhZDIuanMiKSk7CnZhciBfc2xpY2VkVG9BcnJheTIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkM6L1VzZXJzL0FkbWluaXN0cmF0b3IvRGVza3RvcC9kdW8zZC9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9zbGljZWRUb0FycmF5LmpzIikpOwp2YXIgX3R5cGVvZjIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkM6L1VzZXJzL0FkbWluaXN0cmF0b3IvRGVza3RvcC9kdW8zZC9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy90eXBlb2YuanMiKSk7CnZhciBfdG9Db25zdW1hYmxlQXJyYXkyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJDOi9Vc2Vycy9BZG1pbmlzdHJhdG9yL0Rlc2t0b3AvZHVvM2QvcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvdG9Db25zdW1hYmxlQXJyYXkuanMiKSk7CnZhciBfcmVnZW5lcmF0b3IyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJDOi9Vc2Vycy9BZG1pbmlzdHJhdG9yL0Rlc2t0b3AvZHVvM2QvcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvcmVnZW5lcmF0b3IuanMiKSk7CnZhciBfYXN5bmNUb0dlbmVyYXRvcjIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkM6L1VzZXJzL0FkbWluaXN0cmF0b3IvRGVza3RvcC9kdW8zZC9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9hc3luY1RvR2VuZXJhdG9yLmpzIikpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuY29uY2F0LmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5maWx0ZXIuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmZpbmQuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmZyb20uanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmluY2x1ZGVzLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5qb2luLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5tYXAuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnB1c2guanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnNvcnQuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmZ1bmN0aW9uLm5hbWUuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm1hcC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMubnVtYmVyLnRvLWZpeGVkLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QuZW50cmllcy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LmtleXMuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC5leGVjLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAudGVzdC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc2V0LmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zZXQuZGlmZmVyZW5jZS52Mi5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc2V0LmludGVyc2VjdGlvbi52Mi5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc2V0LmlzLWRpc2pvaW50LWZyb20udjIuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnNldC5pcy1zdWJzZXQtb2YudjIuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnNldC5pcy1zdXBlcnNldC1vZi52Mi5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc2V0LnN5bW1ldHJpYy1kaWZmZXJlbmNlLnYyLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zZXQudW5pb24udjIuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5pbmNsdWRlcy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLml0ZXJhdG9yLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcucmVwbGFjZS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLnRyaW0uanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5jb25zdHJ1Y3Rvci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmZpbHRlci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmZpbmQuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5mb3ItZWFjaC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLm1hcC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5mb3ItZWFjaC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5pdGVyYXRvci5qcyIpOwp2YXIgX3JlcXVlc3QgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdXRpbHMvcmVxdWVzdCIpKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICdCZXRTdGF0aXN0aWNzJywKICBwcm9wczogewogICAgdmlzaWJsZTogewogICAgICB0eXBlOiBCb29sZWFuLAogICAgICBkZWZhdWx0OiBmYWxzZQogICAgfQogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBtZXRob2RSYW5raW5nQnlHcm91cDogW10sCiAgICAgIC8vIOaMieeOqeazleWIhue7hOeahOaOkuihjOamnAogICAgICBudW1iZXJSYW5raW5nQnlHcm91cDogW10sCiAgICAgIC8vIOaMieeOqeazleWIhue7hOeahOWPt+eggeaOkuihjOamnAogICAgICBnYW1lTWV0aG9kc0RhdGE6IFtdLAogICAgICAvLyDnjqnms5XmlbDmja4KICAgICAgLy8g566h55CG5ZGY5Yqf6IO955u45YWzCiAgICAgIHNlbGVjdGVkVXNlcklkOiBudWxsLAogICAgICAvLyDpgInkuK3nmoTnlKjmiLdJRAogICAgICB1c2VyTGlzdDogW10sCiAgICAgIC8vIOeUqOaIt+WIl+ihqAogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgLy8g5Yqg6L2954q25oCBCiAgICAgIC8vIOW9qeenjeebuOWFswogICAgICBhY3RpdmVMb3R0ZXJ5VHlwZTogJ2Z1Y2FpJywKICAgICAgLy8g5b2T5YmN6YCJ5Lit55qE5b2p56eN77yaZnVjYWko56aP5b2pM0QpIOaIliB0aWNhaSjkvZPlvalQMykKICAgICAgLy8g5YiG5b2p56eN55qE57uf6K6h5pWw5o2uCiAgICAgIGZ1Y2FpTWV0aG9kUmFua2luZzogW10sCiAgICAgIC8vIOemj+W9qeaKleazqOaOkuihjAogICAgICBmdWNhaU51bWJlclJhbmtpbmc6IFtdLAogICAgICAvLyDnpo/lvanng63pl6jkuInkvY3mlbDmjpLooYwKICAgICAgdGljYWlNZXRob2RSYW5raW5nOiBbXSwKICAgICAgLy8g5L2T5b2p5oqV5rOo5o6S6KGMCiAgICAgIHRpY2FpTnVtYmVyUmFua2luZzogW10sCiAgICAgIC8vIOS9k+W9qeeDremXqOS4ieS9jeaVsOaOkuihjAogICAgICAvLyDmkJzntKLlip/og73nm7jlhbMKICAgICAgc2VhcmNoTnVtYmVyOiAnJywKICAgICAgLy8g5pCc57Si55qE5Y+356CBCiAgICAgIGlzU2VhcmNoTW9kZTogZmFsc2UsCiAgICAgIC8vIOaYr+WQpuWkhOS6juaQnOe0ouaooeW8jwogICAgICBzZWFyY2hMb2FkaW5nOiBmYWxzZSwKICAgICAgLy8g5pCc57Si5Yqg6L2954q25oCBCiAgICAgIHNlYXJjaFJlc3VsdENvdW50OiAwLAogICAgICAvLyDmkJzntKLnu5PmnpzmlbDph48KICAgICAgLy8g5Y6f5aeL5a6M5pW05pWw5o2u77yI55So5LqO5pCc57Si6L+H5ruk77yJCiAgICAgIG9yaWdpbmFsRnVjYWlNZXRob2RSYW5raW5nOiBbXSwKICAgICAgb3JpZ2luYWxGdWNhaU51bWJlclJhbmtpbmc6IFtdLAogICAgICBvcmlnaW5hbFRpY2FpTWV0aG9kUmFua2luZzogW10sCiAgICAgIG9yaWdpbmFsVGljYWlOdW1iZXJSYW5raW5nOiBbXQogICAgfTsKICB9LAogIGNvbXB1dGVkOiB7CiAgICAvKiog5piv5ZCm5piv566h55CG5ZGYICovaXNBZG1pbjogZnVuY3Rpb24gaXNBZG1pbigpIHsKICAgICAgdmFyIHVzZXJJbmZvID0gdGhpcy4kc3RvcmUuZ2V0dGVycy51c2VySW5mbzsKICAgICAgdmFyIHJvbGVzID0gdGhpcy4kc3RvcmUuZ2V0dGVycy5yb2xlczsKCiAgICAgIC8vIOajgOafpeaYr+WQpuaYr+i2hee6p+euoeeQhuWRmO+8iOeUqOaIt0lE5Li6Me+8iQogICAgICBpZiAodXNlckluZm8gJiYgdXNlckluZm8udXNlcklkID09PSAxKSB7CiAgICAgICAgcmV0dXJuIHRydWU7CiAgICAgIH0KCiAgICAgIC8vIOajgOafpeaYr+WQpuacieeuoeeQhuWRmOinkuiJsgogICAgICBpZiAocm9sZXMgJiYgcm9sZXMubGVuZ3RoID4gMCkgewogICAgICAgIHJldHVybiByb2xlcy5pbmNsdWRlcygnYWRtaW4nKSB8fCByb2xlcy5pbmNsdWRlcygnM2RfYWRtaW4nKTsKICAgICAgfQogICAgICByZXR1cm4gZmFsc2U7CiAgICB9LAogICAgLyoqIOW9k+WJjeW9qeenjeeahOaKleazqOaOkuihjOaVsOaNriAqL2N1cnJlbnRNZXRob2RSYW5raW5nOiBmdW5jdGlvbiBjdXJyZW50TWV0aG9kUmFua2luZygpIHsKICAgICAgcmV0dXJuIHRoaXMuYWN0aXZlTG90dGVyeVR5cGUgPT09ICdmdWNhaScgPyB0aGlzLmZ1Y2FpTWV0aG9kUmFua2luZyA6IHRoaXMudGljYWlNZXRob2RSYW5raW5nOwogICAgfSwKICAgIC8qKiDlvZPliY3lvannp43nmoTng63pl6jkuInkvY3mlbDmjpLooYzmlbDmja4gKi9jdXJyZW50TnVtYmVyUmFua2luZzogZnVuY3Rpb24gY3VycmVudE51bWJlclJhbmtpbmcoKSB7CiAgICAgIHJldHVybiB0aGlzLmFjdGl2ZUxvdHRlcnlUeXBlID09PSAnZnVjYWknID8gdGhpcy5mdWNhaU51bWJlclJhbmtpbmcgOiB0aGlzLnRpY2FpTnVtYmVyUmFua2luZzsKICAgIH0sCiAgICAvKiog5pCc57Si57uT5p6c5qCH6aKYICovc2VhcmNoUmVzdWx0VGl0bGU6IGZ1bmN0aW9uIHNlYXJjaFJlc3VsdFRpdGxlKCkgewogICAgICByZXR1cm4gIlx1NjQxQ1x1N0QyMlx1NTNGN1x1NzgwMSBcIiIuY29uY2F0KHRoaXMuc2VhcmNoTnVtYmVyLCAiXCIgXHU3Njg0XHU3RUQzXHU2NzlDXHVGRjFBXHU1MTcxXHU2MjdFXHU1MjMwICIpLmNvbmNhdCh0aGlzLnNlYXJjaFJlc3VsdENvdW50LCAiIFx1NEUyQVx1NTMzOVx1OTE0RFx1OTg3OSIpOwogICAgfQogIH0sCiAgd2F0Y2g6IHsKICAgIHZpc2libGU6IGZ1bmN0aW9uIHZpc2libGUodmFsKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHZhbDsKICAgICAgaWYgKHZhbCkgewogICAgICAgIC8vIOi/kOihjOa1i+ivlQogICAgICAgIHRoaXMudGVzdFRocmVlRGlnaXRFeHRyYWN0aW9uKCk7CgogICAgICAgIC8vIOWmguaenOaYr+euoeeQhuWRmO+8jOWPquWKoOi9veeUqOaIt+WIl+ihqOWSjOeOqeazleaVsOaNru+8jOS4jeiHquWKqOiuoeeul+e7n+iuoQogICAgICAgIGlmICh0aGlzLmlzQWRtaW4pIHsKICAgICAgICAgIHRoaXMubG9hZFVzZXJMaXN0KCk7CiAgICAgICAgICB0aGlzLmxvYWRHYW1lTWV0aG9kcygpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAvLyDmma7pgJrnlKjmiLfoh6rliqjliqDovb3nu5/orqEKICAgICAgICAgIHRoaXMubG9hZEdhbWVNZXRob2RzKCkudGhlbigvKiNfX1BVUkVfXyovKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSgvKiNfX1BVUkVfXyovKDAsIF9yZWdlbmVyYXRvcjIuZGVmYXVsdCkoKS5tKGZ1bmN0aW9uIF9jYWxsZWUoKSB7CiAgICAgICAgICAgIHZhciBzeXNVc2VySWQ7CiAgICAgICAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yMi5kZWZhdWx0KSgpLncoZnVuY3Rpb24gKF9jb250ZXh0KSB7CiAgICAgICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQubikgewogICAgICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgICAgICAvLyDmma7pgJrnlKjmiLfku45zZXNzaW9uU3RvcmFnZeiOt+WPluiHquW3seeahOaVsOaNrgogICAgICAgICAgICAgICAgICBzeXNVc2VySWQgPSBzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKCdzeXNVc2VySWQnKTsKICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ1tCZXRTdGF0aXN0aWNzXSBzZXNzaW9uU3RvcmFnZeS4reeahHN5c1VzZXJJZDonLCBzeXNVc2VySWQpOwogICAgICAgICAgICAgICAgICBpZiAoc3lzVXNlcklkKSB7CiAgICAgICAgICAgICAgICAgICAgX3RoaXMuc2VsZWN0ZWRVc2VySWQgPSBwYXJzZUludChzeXNVc2VySWQpOwogICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdbQmV0U3RhdGlzdGljc10g6K6+572uc2VsZWN0ZWRVc2VySWQ6JywgX3RoaXMuc2VsZWN0ZWRVc2VySWQpOwogICAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICAgIGNvbnNvbGUud2FybignW0JldFN0YXRpc3RpY3NdIOaXoOazleS7jnNlc3Npb25TdG9yYWdl6I635Y+Wc3lzVXNlcklkJyk7CiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgX2NvbnRleHQubiA9IDE7CiAgICAgICAgICAgICAgICAgIHJldHVybiBfdGhpcy5jYWxjdWxhdGVTdGF0aXN0aWNzKCk7CiAgICAgICAgICAgICAgICBjYXNlIDE6CiAgICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dC5hKDIpOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSwgX2NhbGxlZSk7CiAgICAgICAgICB9KSkpOwogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgIGRpYWxvZ1Zpc2libGU6IGZ1bmN0aW9uIGRpYWxvZ1Zpc2libGUodmFsKSB7CiAgICAgIHRoaXMuJGVtaXQoJ3VwZGF0ZTp2aXNpYmxlJywgdmFsKTsKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDliqDovb3njqnms5XmlbDmja4gKi9sb2FkR2FtZU1ldGhvZHM6IGZ1bmN0aW9uIGxvYWRHYW1lTWV0aG9kcygpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKC8qI19fUFVSRV9fKi8oMCwgX3JlZ2VuZXJhdG9yMi5kZWZhdWx0KSgpLm0oZnVuY3Rpb24gX2NhbGxlZTIoKSB7CiAgICAgICAgdmFyIHJlc3BvbnNlLCBfdDsKICAgICAgICByZXR1cm4gKDAsIF9yZWdlbmVyYXRvcjIuZGVmYXVsdCkoKS53KGZ1bmN0aW9uIChfY29udGV4dDIpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0Mi5uKSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBfY29udGV4dDIucCA9IDA7CiAgICAgICAgICAgICAgX2NvbnRleHQyLm4gPSAxOwogICAgICAgICAgICAgIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgICAgICAgICAgICAgdXJsOiAnL2dhbWUvb2Rkcy9saXN0JywKICAgICAgICAgICAgICAgIG1ldGhvZDogJ2dldCcsCiAgICAgICAgICAgICAgICBwYXJhbXM6IHsKICAgICAgICAgICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgICAgICAgICAgcGFnZVNpemU6IDEwMDAKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgY2FzZSAxOgogICAgICAgICAgICAgIHJlc3BvbnNlID0gX2NvbnRleHQyLnY7CiAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlICYmIHJlc3BvbnNlLnJvd3MpIHsKICAgICAgICAgICAgICAgIF90aGlzMi5nYW1lTWV0aG9kc0RhdGEgPSByZXNwb25zZS5yb3dzOwogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICBfdGhpczIuZ2FtZU1ldGhvZHNEYXRhID0gW107CiAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oJ+eOqeazleaVsOaNruS4uuepuicpOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfY29udGV4dDIubiA9IDM7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICBfY29udGV4dDIucCA9IDI7CiAgICAgICAgICAgICAgX3QgPSBfY29udGV4dDIudjsKICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bnjqnms5XmlbDmja7lpLHotKU6JywgX3QpOwogICAgICAgICAgICAgIC8vIOWmguaenEFQSeWksei0pe+8jOS9v+eUqOWfuuacrOeahOeOqeazleaYoOWwhAogICAgICAgICAgICAgIF90aGlzMi5nYW1lTWV0aG9kc0RhdGEgPSBfdGhpczIuZ2V0QmFzaWNNZXRob2RzRGF0YSgpOwogICAgICAgICAgICBjYXNlIDM6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Mi5hKDIpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUyLCBudWxsLCBbWzAsIDJdXSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8qKiDojrflj5bln7rmnKznjqnms5XmlbDmja7vvIhBUEnlpLHotKXml7bnmoTlpIfnlKjmlrnmoYjvvIkgKi9nZXRCYXNpY01ldGhvZHNEYXRhOiBmdW5jdGlvbiBnZXRCYXNpY01ldGhvZHNEYXRhKCkgewogICAgICByZXR1cm4gW3sKICAgICAgICBtZXRob2RJZDogMSwKICAgICAgICBtZXRob2ROYW1lOiAn54us6IOGJwogICAgICB9LCB7CiAgICAgICAgbWV0aG9kSWQ6IDIsCiAgICAgICAgbWV0aG9kTmFtZTogJ+S4gOeggeWumuS9jScKICAgICAgfSwgewogICAgICAgIG1ldGhvZElkOiAzLAogICAgICAgIG1ldGhvZE5hbWU6ICfkuKTnoIHnu4TlkIgnCiAgICAgIH0sIHsKICAgICAgICBtZXRob2RJZDogNCwKICAgICAgICBtZXRob2ROYW1lOiAn5Lik56CB5a+55a2QJwogICAgICB9LCB7CiAgICAgICAgbWV0aG9kSWQ6IDUsCiAgICAgICAgbWV0aG9kTmFtZTogJ+S4ieeggeebtOmAiScKICAgICAgfSwgewogICAgICAgIG1ldGhvZElkOiA2LAogICAgICAgIG1ldGhvZE5hbWU6ICfkuInnoIHnu4TpgIknCiAgICAgIH0sIHsKICAgICAgICBtZXRob2RJZDogNywKICAgICAgICBtZXRob2ROYW1lOiAn5LiJ56CB57uE5LiJJwogICAgICB9LCB7CiAgICAgICAgbWV0aG9kSWQ6IDgsCiAgICAgICAgbWV0aG9kTmFtZTogJ+Wbm+eggee7hOWFrScKICAgICAgfSwgewogICAgICAgIG1ldGhvZElkOiA5LAogICAgICAgIG1ldGhvZE5hbWU6ICflm5vnoIHnu4TkuIknCiAgICAgIH0sIHsKICAgICAgICBtZXRob2RJZDogMTAsCiAgICAgICAgbWV0aG9kTmFtZTogJ+S6lOeggee7hOWFrScKICAgICAgfSwgewogICAgICAgIG1ldGhvZElkOiAxMSwKICAgICAgICBtZXRob2ROYW1lOiAn5LqU56CB57uE5LiJJwogICAgICB9LCB7CiAgICAgICAgbWV0aG9kSWQ6IDEyLAogICAgICAgIG1ldGhvZE5hbWU6ICflha3noIHnu4Tlha0nCiAgICAgIH0sIHsKICAgICAgICBtZXRob2RJZDogMTMsCiAgICAgICAgbWV0aG9kTmFtZTogJ+WFreeggee7hOS4iScKICAgICAgfSwgewogICAgICAgIG1ldGhvZElkOiAxNCwKICAgICAgICBtZXRob2ROYW1lOiAn5LiD56CB57uE5YWtJwogICAgICB9LCB7CiAgICAgICAgbWV0aG9kSWQ6IDE1LAogICAgICAgIG1ldGhvZE5hbWU6ICfkuIPnoIHnu4TkuIknCiAgICAgIH0sIHsKICAgICAgICBtZXRob2RJZDogMTYsCiAgICAgICAgbWV0aG9kTmFtZTogJ+WFq+eggee7hOWFrScKICAgICAgfSwgewogICAgICAgIG1ldGhvZElkOiAxNywKICAgICAgICBtZXRob2ROYW1lOiAn5YWr56CB57uE5LiJJwogICAgICB9LCB7CiAgICAgICAgbWV0aG9kSWQ6IDE4LAogICAgICAgIG1ldGhvZE5hbWU6ICfkuZ3noIHnu4Tlha0nCiAgICAgIH0sIHsKICAgICAgICBtZXRob2RJZDogMTksCiAgICAgICAgbWV0aG9kTmFtZTogJ+S5neeggee7hOS4iScKICAgICAgfSwgewogICAgICAgIG1ldGhvZElkOiAyMCwKICAgICAgICBtZXRob2ROYW1lOiAn5YyF5omT57uE5YWtJwogICAgICB9LCB7CiAgICAgICAgbWV0aG9kSWQ6IDIxLAogICAgICAgIG1ldGhvZE5hbWU6ICc35oiWMjDlkozlgLwnCiAgICAgIH0sIHsKICAgICAgICBtZXRob2RJZDogMjIsCiAgICAgICAgbWV0aG9kTmFtZTogJzjmiJYxOeWSjOWAvCcKICAgICAgfSwgewogICAgICAgIG1ldGhvZElkOiAyMywKICAgICAgICBtZXRob2ROYW1lOiAnOeaIljE45ZKM5YC8JwogICAgICB9LCB7CiAgICAgICAgbWV0aG9kSWQ6IDI0LAogICAgICAgIG1ldGhvZE5hbWU6ICcxMOaIljE35ZKM5YC8JwogICAgICB9LCB7CiAgICAgICAgbWV0aG9kSWQ6IDI1LAogICAgICAgIG1ldGhvZE5hbWU6ICcxMeaIljE25ZKM5YC8JwogICAgICB9LCB7CiAgICAgICAgbWV0aG9kSWQ6IDI2LAogICAgICAgIG1ldGhvZE5hbWU6ICcxMuaIljE15ZKM5YC8JwogICAgICB9LCB7CiAgICAgICAgbWV0aG9kSWQ6IDI3LAogICAgICAgIG1ldGhvZE5hbWU6ICcxM+aIljE05ZKM5YC8JwogICAgICB9LCB7CiAgICAgICAgbWV0aG9kSWQ6IDI4LAogICAgICAgIG1ldGhvZE5hbWU6ICfnm7TpgInlpI3lvI8nCiAgICAgIH0sIHsKICAgICAgICBtZXRob2RJZDogMjksCiAgICAgICAgbWV0aG9kTmFtZTogJ+WSjOWAvOWNleWPjCcKICAgICAgfSwgewogICAgICAgIG1ldGhvZElkOiAzMCwKICAgICAgICBtZXRob2ROYW1lOiAn5ZKM5YC85aSn5bCPJwogICAgICB9LCB7CiAgICAgICAgbWV0aG9kSWQ6IDMxLAogICAgICAgIG1ldGhvZE5hbWU6ICfljIXmiZPnu4TkuIknCiAgICAgIH0sIHsKICAgICAgICBtZXRob2RJZDogMTAwLAogICAgICAgIG1ldGhvZE5hbWU6ICfkuInnoIHpmLLlr7knCiAgICAgIH1dOwogICAgfSwKICAgIC8qKiDojrflj5bnjqnms5XlkI3np7AgKi9nZXRNZXRob2ROYW1lOiBmdW5jdGlvbiBnZXRNZXRob2ROYW1lKG1ldGhvZElkKSB7CiAgICAgIHZhciBtZXRob2QgPSB0aGlzLmdhbWVNZXRob2RzRGF0YS5maW5kKGZ1bmN0aW9uIChtKSB7CiAgICAgICAgcmV0dXJuIG0ubWV0aG9kSWQgPT09IG1ldGhvZElkOwogICAgICB9KTsKICAgICAgcmV0dXJuIG1ldGhvZCA/IG1ldGhvZC5tZXRob2ROYW1lIDogIlx1NzNBOVx1NkNENSIuY29uY2F0KG1ldGhvZElkKTsKICAgIH0sCiAgICAvKiog6I635Y+W5o6S5ZCN5qC35byP57G7ICovZ2V0UmFua0NsYXNzOiBmdW5jdGlvbiBnZXRSYW5rQ2xhc3MocmFuaykgewogICAgICBpZiAocmFuayA9PT0gMSkgcmV0dXJuICdyYW5rLWZpcnN0JzsKICAgICAgaWYgKHJhbmsgPT09IDIpIHJldHVybiAncmFuay1zZWNvbmQnOwogICAgICBpZiAocmFuayA9PT0gMykgcmV0dXJuICdyYW5rLXRoaXJkJzsKICAgICAgcmV0dXJuICdyYW5rLW90aGVyJzsKICAgIH0sCiAgICAvKiog6K6h566X57uf6K6h5pWw5o2uICovY2FsY3VsYXRlU3RhdGlzdGljczogZnVuY3Rpb24gY2FsY3VsYXRlU3RhdGlzdGljcygpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKC8qI19fUFVSRV9fKi8oMCwgX3JlZ2VuZXJhdG9yMi5kZWZhdWx0KSgpLm0oZnVuY3Rpb24gX2NhbGxlZTMoKSB7CiAgICAgICAgdmFyIGRhdGE7CiAgICAgICAgcmV0dXJuICgwLCBfcmVnZW5lcmF0b3IyLmRlZmF1bHQpKCkudyhmdW5jdGlvbiAoX2NvbnRleHQzKSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDMubikgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgX2NvbnRleHQzLm4gPSAxOwogICAgICAgICAgICAgIHJldHVybiBfdGhpczMuZ2V0UmVjb3JkRGF0YSgpOwogICAgICAgICAgICBjYXNlIDE6CiAgICAgICAgICAgICAgZGF0YSA9IF9jb250ZXh0My52OwogICAgICAgICAgICAgIGlmICghKCFkYXRhIHx8IGRhdGEubGVuZ3RoID09PSAwKSkgewogICAgICAgICAgICAgICAgX2NvbnRleHQzLm4gPSAyOwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIGlmIChfdGhpczMuaXNBZG1pbiAmJiAhX3RoaXMzLnNlbGVjdGVkVXNlcklkKSB7CiAgICAgICAgICAgICAgICBfdGhpczMuJG1lc3NhZ2Uud2FybmluZygn6K+36YCJ5oup6KaB5p+l55yL57uf6K6h55qE55So5oi3Jyk7CiAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIF90aGlzMy4kbWVzc2FnZS53YXJuaW5nKCfmmoLml6Dnu5/orqHmlbDmja7vvIzor7flhYjliLfmlrByZWNvcmTpobXpnaInKTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgX3RoaXMzLmNsZWFyQWxsUmFua2luZ0RhdGEoKTsKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQzLmEoMik7CiAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICAvLyDmjInlvannp43liIbliKvorqHnrpfnu5/orqHmlbDmja4KICAgICAgICAgICAgICBfdGhpczMuY2FsY3VsYXRlU3RhdGlzdGljc0J5TG90dGVyeVR5cGUoZGF0YSk7CgogICAgICAgICAgICAgIC8vIOiwg+ivleS/oeaBrwogICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfnu5/orqHorqHnrpflrozmiJA6JywgewogICAgICAgICAgICAgICAg56aP5b2p5oqV5rOo5o6S6KGMOiBfdGhpczMuZnVjYWlNZXRob2RSYW5raW5nLmxlbmd0aCwKICAgICAgICAgICAgICAgIOemj+W9qeeDremXqOS4ieS9jeaVsDogX3RoaXMzLmZ1Y2FpTnVtYmVyUmFua2luZy5sZW5ndGgsCiAgICAgICAgICAgICAgICDkvZPlvanmipXms6jmjpLooYw6IF90aGlzMy50aWNhaU1ldGhvZFJhbmtpbmcubGVuZ3RoLAogICAgICAgICAgICAgICAg5L2T5b2p54Ot6Zeo5LiJ5L2N5pWwOiBfdGhpczMudGljYWlOdW1iZXJSYW5raW5nLmxlbmd0aCwKICAgICAgICAgICAgICAgIOW9k+WJjemAieS4reW9qeenjTogX3RoaXMzLmFjdGl2ZUxvdHRlcnlUeXBlCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQzLmEoMik7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTMpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvKiog6I635Y+WcmVjb3Jk5pWw5o2uICovZ2V0UmVjb3JkRGF0YTogZnVuY3Rpb24gZ2V0UmVjb3JkRGF0YSgpIHsKICAgICAgdmFyIF90aGlzNCA9IHRoaXM7CiAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKC8qI19fUFVSRV9fKi8oMCwgX3JlZ2VuZXJhdG9yMi5kZWZhdWx0KSgpLm0oZnVuY3Rpb24gX2NhbGxlZTQoKSB7CiAgICAgICAgdmFyIHN5c1VzZXJJZCwgZGF0YSwgX3QyOwogICAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yMi5kZWZhdWx0KSgpLncoZnVuY3Rpb24gKF9jb250ZXh0NCkgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQ0Lm4pIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIF9jb250ZXh0NC5wID0gMDsKICAgICAgICAgICAgICBpZiAoIV90aGlzNC5zZWxlY3RlZFVzZXJJZCkgewogICAgICAgICAgICAgICAgX2NvbnRleHQ0Lm4gPSAyOwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF9jb250ZXh0NC5uID0gMTsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXM0LmZldGNoVXNlclJlY29yZERhdGEoX3RoaXM0LnNlbGVjdGVkVXNlcklkKTsKICAgICAgICAgICAgY2FzZSAxOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDQuYSgyLCBfY29udGV4dDQudik7CiAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICBpZiAoIV90aGlzNC5pc0FkbWluKSB7CiAgICAgICAgICAgICAgICBfY29udGV4dDQubiA9IDM7CiAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgX3RoaXM0LiRtZXNzYWdlLndhcm5pbmcoJ+ivt+mAieaLqeimgeafpeeci+e7n+iuoeeahOeUqOaItycpOwogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDQuYSgyLCBbXSk7CiAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICAvLyDmma7pgJrnlKjmiLfvvJrnm7TmjqXku45BUEnojrflj5blvZPliY3nlKjmiLfmlbDmja7vvIzku45zZXNzaW9uU3RvcmFnZeiOt+WPlueUqOaIt0lECiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ1tCZXRTdGF0aXN0aWNzXSDmma7pgJrnlKjmiLfvvIznm7TmjqXku45BUEnojrflj5bmlbDmja4nKTsKCiAgICAgICAgICAgICAgLy8g5LuOc2Vzc2lvblN0b3JhZ2Xojrflj5bnlKjmiLdJRAogICAgICAgICAgICAgIHN5c1VzZXJJZCA9IHNlc3Npb25TdG9yYWdlLmdldEl0ZW0oJ3N5c1VzZXJJZCcpOwogICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdbQmV0U3RhdGlzdGljc10g5LuOc2Vzc2lvblN0b3JhZ2Xojrflj5bnmoRzeXNVc2VySWQ6Jywgc3lzVXNlcklkKTsKICAgICAgICAgICAgICBpZiAoIXN5c1VzZXJJZCkgewogICAgICAgICAgICAgICAgX2NvbnRleHQ0Lm4gPSA1OwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdbQmV0U3RhdGlzdGljc10g5L2/55Soc3lzVXNlcklk6LCD55SoQVBJOicsIHN5c1VzZXJJZCk7CiAgICAgICAgICAgICAgX2NvbnRleHQ0Lm4gPSA0OwogICAgICAgICAgICAgIHJldHVybiBfdGhpczQuZmV0Y2hVc2VyUmVjb3JkRGF0YShwYXJzZUludChzeXNVc2VySWQpKTsKICAgICAgICAgICAgY2FzZSA0OgogICAgICAgICAgICAgIGRhdGEgPSBfY29udGV4dDQudjsKICAgICAgICAgICAgICBjb25zb2xlLmxvZygnW0JldFN0YXRpc3RpY3NdIEFQSeiOt+WPluWIsOeahOaVsOaNrjonLCBkYXRhKTsKICAgICAgICAgICAgICBpZiAoZGF0YSAmJiBkYXRhLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCJbQmV0U3RhdGlzdGljc10gXHU2MjEwXHU1MjlGXHU1MkEwXHU4RjdEXHU0RTg2ICIuY29uY2F0KGRhdGEubGVuZ3RoLCAiIFx1Njc2MVx1N0VERlx1OEJBMVx1NjU3MFx1NjM2RSIpKTsKICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgY29uc29sZS53YXJuKCdbQmV0U3RhdGlzdGljc10gQVBJ6L+U5Zue56m65pWw5o2uJyk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDQuYSgyLCBkYXRhKTsKICAgICAgICAgICAgY2FzZSA1OgogICAgICAgICAgICAgIGNvbnNvbGUud2FybignW0JldFN0YXRpc3RpY3NdIOaXoOazleS7jnNlc3Npb25TdG9yYWdl6I635Y+Wc3lzVXNlcklkJyk7CiAgICAgICAgICAgICAgX3RoaXM0LiRtZXNzYWdlLndhcm5pbmcoJ+aXoOazleiOt+WPlueUqOaIt+S/oeaBr++8jOivt+mHjeaWsOeZu+W9lScpOwogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDQuYSgyLCBbXSk7CiAgICAgICAgICAgIGNhc2UgNjoKICAgICAgICAgICAgICBfY29udGV4dDQubiA9IDg7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgNzoKICAgICAgICAgICAgICBfY29udGV4dDQucCA9IDc7CiAgICAgICAgICAgICAgX3QyID0gX2NvbnRleHQ0LnY7CiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign6Kej5p6Q57uf6K6h5pWw5o2u5aSx6LSlOicsIF90Mik7CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0NC5hKDIsIFtdKTsKICAgICAgICAgICAgY2FzZSA4OgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDQuYSgyKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlNCwgbnVsbCwgW1swLCA3XV0pOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvKiog5riF56m65omA5pyJ5o6S6KGM5pWw5o2uICovY2xlYXJBbGxSYW5raW5nRGF0YTogZnVuY3Rpb24gY2xlYXJBbGxSYW5raW5nRGF0YSgpIHsKICAgICAgdGhpcy5mdWNhaU1ldGhvZFJhbmtpbmcgPSBbXTsKICAgICAgdGhpcy5mdWNhaU51bWJlclJhbmtpbmcgPSBbXTsKICAgICAgdGhpcy50aWNhaU1ldGhvZFJhbmtpbmcgPSBbXTsKICAgICAgdGhpcy50aWNhaU51bWJlclJhbmtpbmcgPSBbXTsKICAgIH0sCiAgICAvKiog5oyJ5b2p56eN5YiG5Yir6K6h566X57uf6K6h5pWw5o2uICovY2FsY3VsYXRlU3RhdGlzdGljc0J5TG90dGVyeVR5cGU6IGZ1bmN0aW9uIGNhbGN1bGF0ZVN0YXRpc3RpY3NCeUxvdHRlcnlUeXBlKGRhdGEpIHsKICAgICAgdmFyIF90aGlzNSA9IHRoaXM7CiAgICAgIC8vIOiwg+ivle+8muafpeeci+aVsOaNrue7k+aehAogICAgICBpZiAoZGF0YS5sZW5ndGggPiAwKSB7CiAgICAgICAgY29uc29sZS5sb2coJ+aVsOaNruagt+acrDonLCBkYXRhWzBdKTsKICAgICAgICBjb25zb2xlLmxvZygn5Y+v6IO955qE5b2p56eN5a2X5q61OicsIHsKICAgICAgICAgIGxvdHRlcnlUeXBlOiBkYXRhWzBdLmxvdHRlcnlUeXBlLAogICAgICAgICAgZ2FtZVR5cGU6IGRhdGFbMF0uZ2FtZVR5cGUsCiAgICAgICAgICBsb3R0ZXJ5SWQ6IGRhdGFbMF0ubG90dGVyeUlkLAogICAgICAgICAgZ2FtZU5hbWU6IGRhdGFbMF0uZ2FtZU5hbWUsCiAgICAgICAgICBtZXRob2ROYW1lOiBkYXRhWzBdLm1ldGhvZE5hbWUKICAgICAgICB9KTsKCiAgICAgICAgLy8g57uf6K6h5omA5pyJ5Y+v6IO955qE5b2p56eN5qCH6K+GCiAgICAgICAgdmFyIGxvdHRlcnlUeXBlcyA9ICgwLCBfdG9Db25zdW1hYmxlQXJyYXkyLmRlZmF1bHQpKG5ldyBTZXQoZGF0YS5tYXAoZnVuY3Rpb24gKHJlY29yZCkgewogICAgICAgICAgcmV0dXJuIHJlY29yZC5sb3R0ZXJ5VHlwZTsKICAgICAgICB9KSkpOwogICAgICAgIHZhciBnYW1lVHlwZXMgPSAoMCwgX3RvQ29uc3VtYWJsZUFycmF5Mi5kZWZhdWx0KShuZXcgU2V0KGRhdGEubWFwKGZ1bmN0aW9uIChyZWNvcmQpIHsKICAgICAgICAgIHJldHVybiByZWNvcmQuZ2FtZVR5cGU7CiAgICAgICAgfSkpKTsKICAgICAgICB2YXIgbG90dGVyeUlkcyA9ICgwLCBfdG9Db25zdW1hYmxlQXJyYXkyLmRlZmF1bHQpKG5ldyBTZXQoZGF0YS5tYXAoZnVuY3Rpb24gKHJlY29yZCkgewogICAgICAgICAgcmV0dXJuIHJlY29yZC5sb3R0ZXJ5SWQ7CiAgICAgICAgfSkpKTsKICAgICAgICB2YXIgZ2FtZU5hbWVzID0gKDAsIF90b0NvbnN1bWFibGVBcnJheTIuZGVmYXVsdCkobmV3IFNldChkYXRhLm1hcChmdW5jdGlvbiAocmVjb3JkKSB7CiAgICAgICAgICByZXR1cm4gcmVjb3JkLmdhbWVOYW1lOwogICAgICAgIH0pKSk7CiAgICAgICAgY29uc29sZS5sb2coJ+aVsOaNruS4reeahOW9qeenjeagh+ivhjonLCB7CiAgICAgICAgICBsb3R0ZXJ5VHlwZXM6IGxvdHRlcnlUeXBlcywKICAgICAgICAgIGdhbWVUeXBlczogZ2FtZVR5cGVzLAogICAgICAgICAgbG90dGVyeUlkczogbG90dGVyeUlkcywKICAgICAgICAgIGdhbWVOYW1lczogZ2FtZU5hbWVzCiAgICAgICAgfSk7CiAgICAgIH0KCiAgICAgIC8vIOaMieW9qeenjeWIhue7hOaVsOaNru+8iOS9v+eUqGxvdHRlcnlJZOWtl+aute+8iQogICAgICB2YXIgZnVjYWlEYXRhID0gZGF0YS5maWx0ZXIoZnVuY3Rpb24gKHJlY29yZCkgewogICAgICAgIHJldHVybiBfdGhpczUuaXNGdWNhaUxvdHRlcnkocmVjb3JkLmxvdHRlcnlJZCk7CiAgICAgIH0pOwogICAgICB2YXIgdGljYWlEYXRhID0gZGF0YS5maWx0ZXIoZnVuY3Rpb24gKHJlY29yZCkgewogICAgICAgIHJldHVybiBfdGhpczUuaXNUaWNhaUxvdHRlcnkocmVjb3JkLmxvdHRlcnlJZCk7CiAgICAgIH0pOwogICAgICBjb25zb2xlLmxvZygiXHU3OThGXHU1RjY5M0RcdTY1NzBcdTYzNkU6ICIuY29uY2F0KGZ1Y2FpRGF0YS5sZW5ndGgsICJcdTY3NjEsIFx1NEY1M1x1NUY2OVAzXHU2NTcwXHU2MzZFOiAiKS5jb25jYXQodGljYWlEYXRhLmxlbmd0aCwgIlx1Njc2MSIpKTsKCiAgICAgIC8vIOWmguaenOayoeacieaYjuehrueahOW9qeenjeWMuuWIhu+8jOWwhuaJgOacieaVsOaNruW9kuexu+S4uuemj+W9qTNE77yI5YW85a655oCn5aSE55CG77yJCiAgICAgIGlmIChmdWNhaURhdGEubGVuZ3RoID09PSAwICYmIHRpY2FpRGF0YS5sZW5ndGggPT09IDAgJiYgZGF0YS5sZW5ndGggPiAwKSB7CiAgICAgICAgY29uc29sZS5sb2coJ+acquajgOa1i+WIsOW9qeenjeWtl+aute+8jOWwhuaJgOacieaVsOaNruW9kuexu+S4uuemj+W9qTNEJyk7CiAgICAgICAgdGhpcy5mdWNhaU1ldGhvZFJhbmtpbmcgPSB0aGlzLmNhbGN1bGF0ZU1ldGhvZFJhbmtpbmdCeUdyb3VwKGRhdGEpOwogICAgICAgIHRoaXMuZnVjYWlOdW1iZXJSYW5raW5nID0gdGhpcy5jYWxjdWxhdGVOdW1iZXJSYW5raW5nKGRhdGEpOwogICAgICAgIHRoaXMudGljYWlNZXRob2RSYW5raW5nID0gW107CiAgICAgICAgdGhpcy50aWNhaU51bWJlclJhbmtpbmcgPSBbXTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIC8vIOWIhuWIq+iuoeeul+emj+W9qeWSjOS9k+W9qeeahOe7n+iuoeaVsOaNrgogICAgICBpZiAoZnVjYWlEYXRhLmxlbmd0aCA+IDApIHsKICAgICAgICB0aGlzLm9yaWdpbmFsRnVjYWlNZXRob2RSYW5raW5nID0gdGhpcy5jYWxjdWxhdGVNZXRob2RSYW5raW5nQnlHcm91cChmdWNhaURhdGEpOwogICAgICAgIHRoaXMub3JpZ2luYWxGdWNhaU51bWJlclJhbmtpbmcgPSB0aGlzLmNhbGN1bGF0ZU51bWJlclJhbmtpbmcoZnVjYWlEYXRhKTsKICAgICAgICB0aGlzLmZ1Y2FpTWV0aG9kUmFua2luZyA9ICgwLCBfdG9Db25zdW1hYmxlQXJyYXkyLmRlZmF1bHQpKHRoaXMub3JpZ2luYWxGdWNhaU1ldGhvZFJhbmtpbmcpOwogICAgICAgIHRoaXMuZnVjYWlOdW1iZXJSYW5raW5nID0gKDAsIF90b0NvbnN1bWFibGVBcnJheTIuZGVmYXVsdCkodGhpcy5vcmlnaW5hbEZ1Y2FpTnVtYmVyUmFua2luZyk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5vcmlnaW5hbEZ1Y2FpTWV0aG9kUmFua2luZyA9IFtdOwogICAgICAgIHRoaXMub3JpZ2luYWxGdWNhaU51bWJlclJhbmtpbmcgPSBbXTsKICAgICAgICB0aGlzLmZ1Y2FpTWV0aG9kUmFua2luZyA9IFtdOwogICAgICAgIHRoaXMuZnVjYWlOdW1iZXJSYW5raW5nID0gW107CiAgICAgIH0KICAgICAgaWYgKHRpY2FpRGF0YS5sZW5ndGggPiAwKSB7CiAgICAgICAgdGhpcy5vcmlnaW5hbFRpY2FpTWV0aG9kUmFua2luZyA9IHRoaXMuY2FsY3VsYXRlTWV0aG9kUmFua2luZ0J5R3JvdXAodGljYWlEYXRhKTsKICAgICAgICB0aGlzLm9yaWdpbmFsVGljYWlOdW1iZXJSYW5raW5nID0gdGhpcy5jYWxjdWxhdGVOdW1iZXJSYW5raW5nKHRpY2FpRGF0YSk7CiAgICAgICAgdGhpcy50aWNhaU1ldGhvZFJhbmtpbmcgPSAoMCwgX3RvQ29uc3VtYWJsZUFycmF5Mi5kZWZhdWx0KSh0aGlzLm9yaWdpbmFsVGljYWlNZXRob2RSYW5raW5nKTsKICAgICAgICB0aGlzLnRpY2FpTnVtYmVyUmFua2luZyA9ICgwLCBfdG9Db25zdW1hYmxlQXJyYXkyLmRlZmF1bHQpKHRoaXMub3JpZ2luYWxUaWNhaU51bWJlclJhbmtpbmcpOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMub3JpZ2luYWxUaWNhaU1ldGhvZFJhbmtpbmcgPSBbXTsKICAgICAgICB0aGlzLm9yaWdpbmFsVGljYWlOdW1iZXJSYW5raW5nID0gW107CiAgICAgICAgdGhpcy50aWNhaU1ldGhvZFJhbmtpbmcgPSBbXTsKICAgICAgICB0aGlzLnRpY2FpTnVtYmVyUmFua2luZyA9IFtdOwogICAgICB9CiAgICB9LAogICAgLyoqIOWIpOaWreaYr+WQpuS4uuemj+W9qTNEICovaXNGdWNhaUxvdHRlcnk6IGZ1bmN0aW9uIGlzRnVjYWlMb3R0ZXJ5KGxvdHRlcnlJZCkgewogICAgICAvLyDnpo/lvakzROeahGxvdHRlcnlJZOaYrzEKICAgICAgcmV0dXJuIGxvdHRlcnlJZCA9PT0gMSB8fCBsb3R0ZXJ5SWQgPT09ICcxJzsKICAgIH0sCiAgICAvKiog5Yik5pat5piv5ZCm5Li65L2T5b2pUDMgKi9pc1RpY2FpTG90dGVyeTogZnVuY3Rpb24gaXNUaWNhaUxvdHRlcnkobG90dGVyeUlkKSB7CiAgICAgIC8vIOS9k+W9qVAz55qEbG90dGVyeUlk5pivMgogICAgICByZXR1cm4gbG90dGVyeUlkID09PSAyIHx8IGxvdHRlcnlJZCA9PT0gJzInOwogICAgfSwKICAgIC8qKiDmjInnjqnms5XliIbnu4TorqHnrpfmjpLooYzmppwgKi9jYWxjdWxhdGVNZXRob2RSYW5raW5nQnlHcm91cDogZnVuY3Rpb24gY2FsY3VsYXRlTWV0aG9kUmFua2luZ0J5R3JvdXAoZGF0YSkgewogICAgICB2YXIgX3RoaXM2ID0gdGhpczsKICAgICAgY29uc29sZS5sb2coJ1tjYWxjdWxhdGVNZXRob2RSYW5raW5nQnlHcm91cF0g5byA5aeL6K6h566X5ZCE546p5rOV5oqV5rOo5o6S6KGM5qacJyk7CgogICAgICAvLyDmjInnjqnms5XliIbnu4TvvIznm7jlkIzlj7fnoIHlkIjlubbntK/orqHph5Hpop0KICAgICAgdmFyIG1ldGhvZEdyb3VwcyA9IHt9OwogICAgICBkYXRhLmZvckVhY2goZnVuY3Rpb24gKHJlY29yZCkgewogICAgICAgIHZhciBtZXRob2RJZCA9IHJlY29yZC5tZXRob2RJZDsKICAgICAgICB2YXIgYW1vdW50ID0gcGFyc2VGbG9hdChyZWNvcmQubW9uZXkpIHx8IDA7CiAgICAgICAgaWYgKCFtZXRob2RHcm91cHNbbWV0aG9kSWRdKSB7CiAgICAgICAgICBtZXRob2RHcm91cHNbbWV0aG9kSWRdID0gbmV3IE1hcCgpOyAvLyDkvb/nlKhNYXDmnaXlrZjlgqjlj7fnoIHlkozlr7nlupTnmoTntK/orqHmlbDmja4KICAgICAgICB9CgogICAgICAgIC8vIOino+aekOaKleazqOWPt+eggQogICAgICAgIHRyeSB7CiAgICAgICAgICB2YXIgYmV0TnVtYmVycyA9IEpTT04ucGFyc2UocmVjb3JkLmJldE51bWJlcnMgfHwgJ3t9Jyk7CiAgICAgICAgICB2YXIgbnVtYmVycyA9IGJldE51bWJlcnMubnVtYmVycyB8fCBbXTsKCiAgICAgICAgICAvLyDkuLrmr4/kuKrlj7fnoIHntK/orqHph5Hpop0KICAgICAgICAgIG51bWJlcnMuZm9yRWFjaChmdW5jdGlvbiAobnVtYmVyT2JqKSB7CiAgICAgICAgICAgIHZhciBzaW5nbGVOdW1iZXJEaXNwbGF5ID0gJyc7CiAgICAgICAgICAgIGlmICgoMCwgX3R5cGVvZjIuZGVmYXVsdCkobnVtYmVyT2JqKSA9PT0gJ29iamVjdCcgJiYgbnVtYmVyT2JqICE9PSBudWxsKSB7CiAgICAgICAgICAgICAgLy8g5aSE55CGIHthOjEsIGI6MiwgYzozLCBkOjQsIGU6NX0g5qC85byPCiAgICAgICAgICAgICAgdmFyIGtleXMgPSBPYmplY3Qua2V5cyhudW1iZXJPYmopLnNvcnQoKTsKICAgICAgICAgICAgICB2YXIgdmFsdWVzID0ga2V5cy5tYXAoZnVuY3Rpb24gKGtleSkgewogICAgICAgICAgICAgICAgcmV0dXJuIFN0cmluZyhudW1iZXJPYmpba2V5XSk7CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgc2luZ2xlTnVtYmVyRGlzcGxheSA9IHZhbHVlcy5qb2luKCcnKTsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICBzaW5nbGVOdW1iZXJEaXNwbGF5ID0gU3RyaW5nKG51bWJlck9iaik7CiAgICAgICAgICAgIH0KCiAgICAgICAgICAgIC8vIOWmguaenOWPt+eggeW3suWtmOWcqO+8jOe0r+iuoemHkemineWSjOiusOW9leaVsAogICAgICAgICAgICBpZiAobWV0aG9kR3JvdXBzW21ldGhvZElkXS5oYXMoc2luZ2xlTnVtYmVyRGlzcGxheSkpIHsKICAgICAgICAgICAgICB2YXIgZXhpc3RpbmcgPSBtZXRob2RHcm91cHNbbWV0aG9kSWRdLmdldChzaW5nbGVOdW1iZXJEaXNwbGF5KTsKICAgICAgICAgICAgICBleGlzdGluZy50b3RhbEFtb3VudCArPSBhbW91bnQ7CiAgICAgICAgICAgICAgZXhpc3RpbmcucmVjb3JkQ291bnQgKz0gMTsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAvLyDmlrDlj7fnoIHvvIzliJvlu7rorrDlvZUKICAgICAgICAgICAgICBtZXRob2RHcm91cHNbbWV0aG9kSWRdLnNldChzaW5nbGVOdW1iZXJEaXNwbGF5LCB7CiAgICAgICAgICAgICAgICBiZXROdW1iZXJzOiBzaW5nbGVOdW1iZXJEaXNwbGF5LAogICAgICAgICAgICAgICAgdG90YWxBbW91bnQ6IGFtb3VudCwKICAgICAgICAgICAgICAgIHJlY29yZENvdW50OiAxCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgICBjb25zb2xlLndhcm4oJ+ino+aekOaKleazqOWPt+eggeWksei0pTonLCByZWNvcmQuYmV0TnVtYmVycywgZXJyb3IpOwogICAgICAgICAgLy8g5aaC5p6c6Kej5p6Q5aSx6LSl77yM5L2/55So5Y6f5aeL5pWw5o2uCiAgICAgICAgICB2YXIgZmFsbGJhY2tOdW1iZXJzID0gcmVjb3JkLmJldE51bWJlcnMgfHwgJ+acquefpeWPt+eggSc7CiAgICAgICAgICBpZiAobWV0aG9kR3JvdXBzW21ldGhvZElkXS5oYXMoZmFsbGJhY2tOdW1iZXJzKSkgewogICAgICAgICAgICB2YXIgZXhpc3RpbmcgPSBtZXRob2RHcm91cHNbbWV0aG9kSWRdLmdldChmYWxsYmFja051bWJlcnMpOwogICAgICAgICAgICBleGlzdGluZy50b3RhbEFtb3VudCArPSBhbW91bnQ7CiAgICAgICAgICAgIGV4aXN0aW5nLnJlY29yZENvdW50ICs9IDE7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBtZXRob2RHcm91cHNbbWV0aG9kSWRdLnNldChmYWxsYmFja051bWJlcnMsIHsKICAgICAgICAgICAgICBiZXROdW1iZXJzOiBmYWxsYmFja051bWJlcnMsCiAgICAgICAgICAgICAgdG90YWxBbW91bnQ6IGFtb3VudCwKICAgICAgICAgICAgICByZWNvcmRDb3VudDogMQogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pOwogICAgICBjb25zb2xlLmxvZygnW2NhbGN1bGF0ZU1ldGhvZFJhbmtpbmdCeUdyb3VwXSDlvIDlp4vnlJ/miJDmjpLooYzmppwnKTsKCiAgICAgIC8vIOS4uuavj+S4queOqeazleeUn+aIkOaOkuihjOamnOW5tui/lOWbngogICAgICByZXR1cm4gT2JqZWN0LmVudHJpZXMobWV0aG9kR3JvdXBzKS5tYXAoZnVuY3Rpb24gKF9yZWYyKSB7CiAgICAgICAgdmFyIF9yZWYzID0gKDAsIF9zbGljZWRUb0FycmF5Mi5kZWZhdWx0KShfcmVmMiwgMiksCiAgICAgICAgICBtZXRob2RJZCA9IF9yZWYzWzBdLAogICAgICAgICAgbnVtYmVyc01hcCA9IF9yZWYzWzFdOwogICAgICAgIC8vIOWwhk1hcOi9rOaNouS4uuaVsOe7hOW5tuaMieaAu+mHkeminemZjeW6j+aOkuW6jwogICAgICAgIHZhciByYW5raW5nID0gQXJyYXkuZnJvbShudW1iZXJzTWFwLnZhbHVlcygpKS5zb3J0KGZ1bmN0aW9uIChhLCBiKSB7CiAgICAgICAgICByZXR1cm4gYi50b3RhbEFtb3VudCAtIGEudG90YWxBbW91bnQ7CiAgICAgICAgfSkubWFwKGZ1bmN0aW9uIChpdGVtLCBpbmRleCkgewogICAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgcmFuazogaW5kZXggKyAxLAogICAgICAgICAgICBiZXROdW1iZXJzOiBpdGVtLmJldE51bWJlcnMsCiAgICAgICAgICAgIHRvdGFsQW1vdW50OiBpdGVtLnRvdGFsQW1vdW50LAogICAgICAgICAgICByZWNvcmRDb3VudDogaXRlbS5yZWNvcmRDb3VudAogICAgICAgICAgfTsKICAgICAgICB9KTsKICAgICAgICBjb25zb2xlLmxvZygiW2NhbGN1bGF0ZU1ldGhvZFJhbmtpbmdCeUdyb3VwXSBcdTczQTlcdTZDRDUiLmNvbmNhdChtZXRob2RJZCwgIigiKS5jb25jYXQoX3RoaXM2LmdldE1ldGhvZE5hbWUocGFyc2VJbnQobWV0aG9kSWQpKSwgIilcdUZGMUEiKS5jb25jYXQocmFua2luZy5sZW5ndGgsICJcdTRFMkFcdTRFMERcdTU0MENcdTUzRjdcdTc4MDEiKSk7CiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgIG1ldGhvZElkOiBwYXJzZUludChtZXRob2RJZCksCiAgICAgICAgICBtZXRob2ROYW1lOiBfdGhpczYuZ2V0TWV0aG9kTmFtZShwYXJzZUludChtZXRob2RJZCkpLAogICAgICAgICAgcmFua2luZzogcmFua2luZwogICAgICAgIH07CiAgICAgIH0pLmZpbHRlcihmdW5jdGlvbiAoZ3JvdXApIHsKICAgICAgICByZXR1cm4gZ3JvdXAucmFua2luZy5sZW5ndGggPiAwOwogICAgICB9KTsgLy8g5Y+q5pi+56S65pyJ5pWw5o2u55qE546p5rOVCiAgICB9LAogICAgLyoqIOagvOW8j+WMluaKleazqOWPt+eggeeUqOS6juaYvuekuiAqL2Zvcm1hdEJldE51bWJlcnNGb3JEaXNwbGF5OiBmdW5jdGlvbiBmb3JtYXRCZXROdW1iZXJzRm9yRGlzcGxheShiZXROdW1iZXJzS2V5KSB7CiAgICAgIHRyeSB7CiAgICAgICAgdmFyIG51bWJlcnMgPSBKU09OLnBhcnNlKGJldE51bWJlcnNLZXkpOwogICAgICAgIGlmIChBcnJheS5pc0FycmF5KG51bWJlcnMpICYmIG51bWJlcnMubGVuZ3RoID4gMCkgewogICAgICAgICAgcmV0dXJuIG51bWJlcnMubWFwKGZ1bmN0aW9uIChudW0pIHsKICAgICAgICAgICAgaWYgKCgwLCBfdHlwZW9mMi5kZWZhdWx0KShudW0pID09PSAnb2JqZWN0JyAmJiBudW0gIT09IG51bGwpIHsKICAgICAgICAgICAgICAvLyDlpITnkIYge2E6MSwgYjoyLCBjOjMsIGQ6NCwgZTo1fSDmoLzlvI8KICAgICAgICAgICAgICB2YXIga2V5cyA9IE9iamVjdC5rZXlzKG51bSkuc29ydCgpOwogICAgICAgICAgICAgIHZhciB2YWx1ZXMgPSBrZXlzLm1hcChmdW5jdGlvbiAoa2V5KSB7CiAgICAgICAgICAgICAgICByZXR1cm4gU3RyaW5nKG51bVtrZXldKTsKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICByZXR1cm4gdmFsdWVzLmpvaW4oJycpOwogICAgICAgICAgICB9CiAgICAgICAgICAgIHJldHVybiBTdHJpbmcobnVtKTsKICAgICAgICAgIH0pLmpvaW4oJywgJyk7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIC8vIOWmguaenOino+aekOWksei0pe+8jOebtOaOpei/lOWbnuWOn+Wni+Wtl+espuS4sgogICAgICB9CiAgICAgIHJldHVybiBiZXROdW1iZXJzS2V5Lmxlbmd0aCA+IDIwID8gYmV0TnVtYmVyc0tleS5zdWJzdHJpbmcoMCwgMjApICsgJy4uLicgOiBiZXROdW1iZXJzS2V5OwogICAgfSwKICAgIC8qKiDmjInnjqnms5XliIbnu4TorqHnrpflj7fnoIHmjpLooYwgLSDlj6rkvb/nlKhtb25leeWtl+autSAqL2NhbGN1bGF0ZU51bWJlclJhbmtpbmc6IGZ1bmN0aW9uIGNhbGN1bGF0ZU51bWJlclJhbmtpbmcoZGF0YSkgewogICAgICB2YXIgX3RoaXM3ID0gdGhpczsKICAgICAgLy8g5Yid5aeL5YyW6L+U5Zue5pWw57uECiAgICAgIHZhciBudW1iZXJSYW5raW5nQnlHcm91cCA9IFtdOwoKICAgICAgLy8g5oyJ546p5rOV5YiG57uE57uf6K6hCiAgICAgIHZhciBtZXRob2RHcm91cHMgPSB7fTsKICAgICAgZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChyZWNvcmQpIHsKICAgICAgICB0cnkgewogICAgICAgICAgdmFyIG1ldGhvZElkID0gcmVjb3JkLm1ldGhvZElkOwogICAgICAgICAgdmFyIG1vbmV5ID0gcGFyc2VGbG9hdChyZWNvcmQubW9uZXkgfHwgMCk7CiAgICAgICAgICB2YXIgYmV0TnVtYmVycyA9IEpTT04ucGFyc2UocmVjb3JkLmJldE51bWJlcnMgfHwgJ3t9Jyk7CiAgICAgICAgICB2YXIgbnVtYmVycyA9IGJldE51bWJlcnMubnVtYmVycyB8fCBbXTsKCiAgICAgICAgICAvLyDliJ3lp4vljJbnjqnms5XliIbnu4QKICAgICAgICAgIGlmICghbWV0aG9kR3JvdXBzW21ldGhvZElkXSkgewogICAgICAgICAgICBtZXRob2RHcm91cHNbbWV0aG9kSWRdID0gewogICAgICAgICAgICAgIG1ldGhvZFRvdGFsOiAwLAogICAgICAgICAgICAgIHRocmVlRGlnaXRNYXA6IG5ldyBNYXAoKQogICAgICAgICAgICB9OwogICAgICAgICAgfQogICAgICAgICAgbWV0aG9kR3JvdXBzW21ldGhvZElkXS5tZXRob2RUb3RhbCArPSBtb25leTsKCiAgICAgICAgICAvLyDlpITnkIbmr4/kuKrmipXms6jlj7fnoIEKICAgICAgICAgIG51bWJlcnMuZm9yRWFjaChmdW5jdGlvbiAobnVtYmVyT2JqKSB7CiAgICAgICAgICAgIHZhciB0aHJlZURpZ2l0cyA9IF90aGlzNy5leHRyYWN0VGhyZWVEaWdpdE51bWJlcnMobnVtYmVyT2JqKTsKCiAgICAgICAgICAgIC8vIOWvueW9k+WJjeWPt+eggeeahOS4ieS9jeaVsOe7hOWQiOWOu+mHje+8iOmBv+WFjeWQjOS4gOWPt+eggeWGhemHjeWkjee7hOWQiOWkmuasoee0r+iuoe+8iQogICAgICAgICAgICB2YXIgdW5pcXVlTm9ybWFsaXplZERpZ2l0cyA9IG5ldyBTZXQoKTsKICAgICAgICAgICAgdGhyZWVEaWdpdHMuZm9yRWFjaChmdW5jdGlvbiAoZGlnaXQpIHsKICAgICAgICAgICAgICAvLyDlvZLkuIDljJbkuInkvY3mlbDvvJrlsIbmlbDlrZfmjInku47lsI/liLDlpKfmjpLluo/kvZzkuLrnu5/kuIBrZXkKICAgICAgICAgICAgICB2YXIgbm9ybWFsaXplZERpZ2l0ID0gX3RoaXM3Lm5vcm1hbGl6ZVRocmVlRGlnaXRzKGRpZ2l0KTsKICAgICAgICAgICAgICB1bmlxdWVOb3JtYWxpemVkRGlnaXRzLmFkZChub3JtYWxpemVkRGlnaXQpOwogICAgICAgICAgICB9KTsKCiAgICAgICAgICAgIC8vIOavj+S4quW9kuS4gOWMluWQjueahOS4ieS9jeaVsOe0r+WKoOmHkeminQogICAgICAgICAgICB1bmlxdWVOb3JtYWxpemVkRGlnaXRzLmZvckVhY2goZnVuY3Rpb24gKG5vcm1hbGl6ZWREaWdpdCkgewogICAgICAgICAgICAgIHZhciBjdXJyZW50QW1vdW50ID0gbWV0aG9kR3JvdXBzW21ldGhvZElkXS50aHJlZURpZ2l0TWFwLmdldChub3JtYWxpemVkRGlnaXQpIHx8IDA7CiAgICAgICAgICAgICAgbWV0aG9kR3JvdXBzW21ldGhvZElkXS50aHJlZURpZ2l0TWFwLnNldChub3JtYWxpemVkRGlnaXQsIGN1cnJlbnRBbW91bnQgKyBtb25leSk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSk7CiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+ino+aekOWksei0pTonLCBlcnJvcik7CiAgICAgICAgfQogICAgICB9KTsKCiAgICAgIC8vIOeUn+aIkOaOkuihjOamnAoKICAgICAgdGhpcy5udW1iZXJSYW5raW5nQnlHcm91cCA9IFtdOwogICAgICBPYmplY3QuZW50cmllcyhtZXRob2RHcm91cHMpLmZvckVhY2goZnVuY3Rpb24gKF9yZWY0KSB7CiAgICAgICAgdmFyIF9yZWY1ID0gKDAsIF9zbGljZWRUb0FycmF5Mi5kZWZhdWx0KShfcmVmNCwgMiksCiAgICAgICAgICBtZXRob2RJZCA9IF9yZWY1WzBdLAogICAgICAgICAgZ3JvdXAgPSBfcmVmNVsxXTsKICAgICAgICAvLyDovazmjaJNYXDkuLrmlbDnu4TlubbmjpLluo/vvIzmmL7npLrmiYDmnInmlbDmja4KICAgICAgICB2YXIgc29ydGVkVGhyZWVEaWdpdHMgPSBBcnJheS5mcm9tKGdyb3VwLnRocmVlRGlnaXRNYXAuZW50cmllcygpKS5zb3J0KGZ1bmN0aW9uIChfcmVmNiwgX3JlZjcpIHsKICAgICAgICAgIHZhciBfcmVmOCA9ICgwLCBfc2xpY2VkVG9BcnJheTIuZGVmYXVsdCkoX3JlZjYsIDIpLAogICAgICAgICAgICBhID0gX3JlZjhbMV07CiAgICAgICAgICB2YXIgX3JlZjkgPSAoMCwgX3NsaWNlZFRvQXJyYXkyLmRlZmF1bHQpKF9yZWY3LCAyKSwKICAgICAgICAgICAgYiA9IF9yZWY5WzFdOwogICAgICAgICAgcmV0dXJuIGIgLSBhOwogICAgICAgIH0pOwogICAgICAgIHZhciByYW5raW5nID0gc29ydGVkVGhyZWVEaWdpdHMubWFwKGZ1bmN0aW9uIChfcmVmMCwgaW5kZXgpIHsKICAgICAgICAgIHZhciBfcmVmMSA9ICgwLCBfc2xpY2VkVG9BcnJheTIuZGVmYXVsdCkoX3JlZjAsIDIpLAogICAgICAgICAgICBudW1iZXIgPSBfcmVmMVswXSwKICAgICAgICAgICAgYW1vdW50ID0gX3JlZjFbMV07CiAgICAgICAgICByZXR1cm4gewogICAgICAgICAgICByYW5rOiBpbmRleCArIDEsCiAgICAgICAgICAgIG51bWJlcjogbnVtYmVyLAogICAgICAgICAgICBjb3VudDogMSwKICAgICAgICAgICAgdG90YWxBbW91bnQ6IGFtb3VudCwKICAgICAgICAgICAgcGVyY2VudGFnZTogZ3JvdXAubWV0aG9kVG90YWwgPiAwID8gKGFtb3VudCAvIGdyb3VwLm1ldGhvZFRvdGFsICogMTAwKS50b0ZpeGVkKDEpIDogJzAuMCcKICAgICAgICAgIH07CiAgICAgICAgfSk7CiAgICAgICAgaWYgKHJhbmtpbmcubGVuZ3RoID4gMCkgewogICAgICAgICAgbnVtYmVyUmFua2luZ0J5R3JvdXAucHVzaCh7CiAgICAgICAgICAgIG1ldGhvZElkOiBwYXJzZUludChtZXRob2RJZCksCiAgICAgICAgICAgIG1ldGhvZE5hbWU6IF90aGlzNy5nZXRNZXRob2ROYW1lKHBhcnNlSW50KG1ldGhvZElkKSksCiAgICAgICAgICAgIHJhbmtpbmc6IHJhbmtpbmcKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICAgIHJldHVybiBudW1iZXJSYW5raW5nQnlHcm91cDsKICAgIH0sCiAgICAvKiog5LuO5oqV5rOo5Y+356CB5Lit5o+Q5Y+W5omA5pyJ5Y+v6IO955qE5LiJ5L2N5pWw57uE5ZCIICovZXh0cmFjdFRocmVlRGlnaXROdW1iZXJzOiBmdW5jdGlvbiBleHRyYWN0VGhyZWVEaWdpdE51bWJlcnMobnVtYmVyT2JqKSB7CiAgICAgIHZhciBudW1iZXJzID0gW107CgogICAgICAvLyDlpITnkIbkuI3lkIznmoTlj7fnoIHmoLzlvI8KICAgICAgaWYgKHR5cGVvZiBudW1iZXJPYmogPT09ICdzdHJpbmcnKSB7CiAgICAgICAgLy8g55u05o6l5piv5a2X56ym5Liy5qC85byP55qE5Y+356CBCiAgICAgICAgdmFyIGNsZWFuU3RyID0gbnVtYmVyT2JqLnJlcGxhY2UoL1xEL2csICcnKTsgLy8g56e76Zmk6Z2e5pWw5a2X5a2X56ymCiAgICAgICAgdGhpcy5nZW5lcmF0ZVRocmVlRGlnaXRDb21iaW5hdGlvbnMoY2xlYW5TdHIuc3BsaXQoJycpLCBudW1iZXJzKTsKICAgICAgfSBlbHNlIGlmICgoMCwgX3R5cGVvZjIuZGVmYXVsdCkobnVtYmVyT2JqKSA9PT0gJ29iamVjdCcgJiYgbnVtYmVyT2JqICE9PSBudWxsKSB7CiAgICAgICAgLy8g5a+56LGh5qC85byPIHthOiAxLCBiOiAyLCBjOiAzLCBkOiA0LCBlOiA1fQogICAgICAgIHZhciBrZXlzID0gT2JqZWN0LmtleXMobnVtYmVyT2JqKS5zb3J0KCk7CiAgICAgICAgdmFyIGRpZ2l0cyA9IGtleXMubWFwKGZ1bmN0aW9uIChrZXkpIHsKICAgICAgICAgIHJldHVybiBTdHJpbmcobnVtYmVyT2JqW2tleV0pOwogICAgICAgIH0pOwoKICAgICAgICAvLyDnlJ/miJDmiYDmnInlj6/og73nmoTkuInkvY3mlbDnu4TlkIgKICAgICAgICB0aGlzLmdlbmVyYXRlVGhyZWVEaWdpdENvbWJpbmF0aW9ucyhkaWdpdHMsIG51bWJlcnMpOwogICAgICB9CiAgICAgIHJldHVybiBudW1iZXJzOwogICAgfSwKICAgIC8qKiDnlJ/miJDmiYDmnInlj6/og73nmoTkuInkvY3mlbDnu4TlkIggKi9nZW5lcmF0ZVRocmVlRGlnaXRDb21iaW5hdGlvbnM6IGZ1bmN0aW9uIGdlbmVyYXRlVGhyZWVEaWdpdENvbWJpbmF0aW9ucyhkaWdpdHMsIG51bWJlcnMpIHsKICAgICAgdmFyIG4gPSBkaWdpdHMubGVuZ3RoOwoKICAgICAgLy8g5aaC5p6c5pWw5a2X5bCR5LqOM+S4qu+8jOaXoOazlee7hOaIkOS4ieS9jeaVsAogICAgICBpZiAobiA8IDMpIHsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIC8vIOeUn+aIkOaJgOacieWPr+iDveeahOS4ieS9jeaVsOe7hOWQiO+8iEMobiwzKe+8iQogICAgICBmb3IgKHZhciBpID0gMDsgaSA8IG4gLSAyOyBpKyspIHsKICAgICAgICBmb3IgKHZhciBqID0gaSArIDE7IGogPCBuIC0gMTsgaisrKSB7CiAgICAgICAgICBmb3IgKHZhciBrID0gaiArIDE7IGsgPCBuOyBrKyspIHsKICAgICAgICAgICAgdmFyIHRocmVlRGlnaXQgPSBkaWdpdHNbaV0gKyBkaWdpdHNbal0gKyBkaWdpdHNba107CiAgICAgICAgICAgIGlmICgvXlxkezN9JC8udGVzdCh0aHJlZURpZ2l0KSkgewogICAgICAgICAgICAgIG51bWJlcnMucHVzaCh0aHJlZURpZ2l0KTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgIC8qKiDlvZLkuIDljJbkuInkvY3mlbDvvJrlsIbmlbDlrZfmjInku47lsI/liLDlpKfmjpLluo/vvIznu5/kuIDnm7jlkIzmlbDlrZfnmoTkuI3lkIzmjpLliJcgKi9ub3JtYWxpemVUaHJlZURpZ2l0czogZnVuY3Rpb24gbm9ybWFsaXplVGhyZWVEaWdpdHModGhyZWVEaWdpdCkgewogICAgICAvLyDlsIbkuInkvY3mlbDlrZfnrKbkuLLovazmjaLkuLrmlbDnu4TvvIzmjpLluo/lkI7ph43mlrDnu4TlkIgKICAgICAgcmV0dXJuIHRocmVlRGlnaXQuc3BsaXQoJycpLnNvcnQoKS5qb2luKCcnKTsKICAgIH0sCiAgICAvKiog5pCc57Si6L6T5YWl5aSE55CGICovaGFuZGxlU2VhcmNoSW5wdXQ6IGZ1bmN0aW9uIGhhbmRsZVNlYXJjaElucHV0KHZhbHVlKSB7CiAgICAgIC8vIOWPquWFgeiuuOi+k+WFpeaVsOWtlwogICAgICB0aGlzLnNlYXJjaE51bWJlciA9IHZhbHVlLnJlcGxhY2UoL1xEL2csICcnKTsKICAgIH0sCiAgICAvKiog5omn6KGM5pCc57SiICovcGVyZm9ybVNlYXJjaDogZnVuY3Rpb24gcGVyZm9ybVNlYXJjaCgpIHsKICAgICAgaWYgKCF0aGlzLnNlYXJjaE51bWJlci50cmltKCkpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+i+k+WFpeimgeaQnOe0oueahOWPt+eggScpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICB0aGlzLnNlYXJjaExvYWRpbmcgPSB0cnVlOwogICAgICB0aGlzLmlzU2VhcmNoTW9kZSA9IHRydWU7CiAgICAgIHRyeSB7CiAgICAgICAgLy8g5pCc57Si5b2T5YmN5b2p56eN55qE5pWw5o2uCiAgICAgICAgdmFyIHNlYXJjaFJlc3VsdHMgPSB0aGlzLnNlYXJjaEluUmFua2luZ0RhdGEodGhpcy5zZWFyY2hOdW1iZXIpOwoKICAgICAgICAvLyDmm7TmlrDmmL7npLrmlbDmja7kuLrmkJzntKLnu5PmnpwKICAgICAgICBpZiAodGhpcy5hY3RpdmVMb3R0ZXJ5VHlwZSA9PT0gJ2Z1Y2FpJykgewogICAgICAgICAgdGhpcy5mdWNhaU1ldGhvZFJhbmtpbmcgPSBzZWFyY2hSZXN1bHRzLm1ldGhvZFJhbmtpbmc7CiAgICAgICAgICB0aGlzLmZ1Y2FpTnVtYmVyUmFua2luZyA9IHNlYXJjaFJlc3VsdHMubnVtYmVyUmFua2luZzsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy50aWNhaU1ldGhvZFJhbmtpbmcgPSBzZWFyY2hSZXN1bHRzLm1ldGhvZFJhbmtpbmc7CiAgICAgICAgICB0aGlzLnRpY2FpTnVtYmVyUmFua2luZyA9IHNlYXJjaFJlc3VsdHMubnVtYmVyUmFua2luZzsKICAgICAgICB9CgogICAgICAgIC8vIOiuoeeul+aQnOe0oue7k+aenOaVsOmHjwogICAgICAgIHRoaXMuc2VhcmNoUmVzdWx0Q291bnQgPSB0aGlzLmNhbGN1bGF0ZVNlYXJjaFJlc3VsdENvdW50KHNlYXJjaFJlc3VsdHMpOwogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2VzcygiXHU2NDFDXHU3RDIyXHU1QjhDXHU2MjEwXHVGRjBDXHU2MjdFXHU1MjMwICIuY29uY2F0KHRoaXMuc2VhcmNoUmVzdWx0Q291bnQsICIgXHU0RTJBXHU1MzM5XHU5MTREXHU5ODc5IikpOwogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+aQnOe0ouWksei0pTonLCBlcnJvcik7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5pCc57Si5aSx6LSl77yM6K+36YeN6K+VJyk7CiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgdGhpcy5zZWFyY2hMb2FkaW5nID0gZmFsc2U7CiAgICAgIH0KICAgIH0sCiAgICAvKiog5riF6Zmk5pCc57SiICovY2xlYXJTZWFyY2g6IGZ1bmN0aW9uIGNsZWFyU2VhcmNoKCkgewogICAgICB0aGlzLnNlYXJjaE51bWJlciA9ICcnOwogICAgICB0aGlzLmlzU2VhcmNoTW9kZSA9IGZhbHNlOwogICAgICB0aGlzLnNlYXJjaFJlc3VsdENvdW50ID0gMDsKCiAgICAgIC8vIOaBouWkjeWOn+Wni+aVsOaNrgogICAgICB0aGlzLmZ1Y2FpTWV0aG9kUmFua2luZyA9ICgwLCBfdG9Db25zdW1hYmxlQXJyYXkyLmRlZmF1bHQpKHRoaXMub3JpZ2luYWxGdWNhaU1ldGhvZFJhbmtpbmcpOwogICAgICB0aGlzLmZ1Y2FpTnVtYmVyUmFua2luZyA9ICgwLCBfdG9Db25zdW1hYmxlQXJyYXkyLmRlZmF1bHQpKHRoaXMub3JpZ2luYWxGdWNhaU51bWJlclJhbmtpbmcpOwogICAgICB0aGlzLnRpY2FpTWV0aG9kUmFua2luZyA9ICgwLCBfdG9Db25zdW1hYmxlQXJyYXkyLmRlZmF1bHQpKHRoaXMub3JpZ2luYWxUaWNhaU1ldGhvZFJhbmtpbmcpOwogICAgICB0aGlzLnRpY2FpTnVtYmVyUmFua2luZyA9ICgwLCBfdG9Db25zdW1hYmxlQXJyYXkyLmRlZmF1bHQpKHRoaXMub3JpZ2luYWxUaWNhaU51bWJlclJhbmtpbmcpOwogICAgfSwKICAgIC8qKiDlnKjmjpLooYzmlbDmja7kuK3mkJzntKLljIXlkKvmjIflrprlj7fnoIHnmoTpobnnm64gKi9zZWFyY2hJblJhbmtpbmdEYXRhOiBmdW5jdGlvbiBzZWFyY2hJblJhbmtpbmdEYXRhKHNlYXJjaE51bWJlcikgewogICAgICB2YXIgX3RoaXM4ID0gdGhpczsKICAgICAgdmFyIG9yaWdpbmFsTWV0aG9kUmFua2luZyA9IHRoaXMuYWN0aXZlTG90dGVyeVR5cGUgPT09ICdmdWNhaScgPyB0aGlzLm9yaWdpbmFsRnVjYWlNZXRob2RSYW5raW5nIDogdGhpcy5vcmlnaW5hbFRpY2FpTWV0aG9kUmFua2luZzsKICAgICAgdmFyIG9yaWdpbmFsTnVtYmVyUmFua2luZyA9IHRoaXMuYWN0aXZlTG90dGVyeVR5cGUgPT09ICdmdWNhaScgPyB0aGlzLm9yaWdpbmFsRnVjYWlOdW1iZXJSYW5raW5nIDogdGhpcy5vcmlnaW5hbFRpY2FpTnVtYmVyUmFua2luZzsKCiAgICAgIC8vIOaQnOe0ouaKleazqOaOkuihjOamnAogICAgICB2YXIgZmlsdGVyZWRNZXRob2RSYW5raW5nID0gb3JpZ2luYWxNZXRob2RSYW5raW5nLm1hcChmdW5jdGlvbiAobWV0aG9kR3JvdXApIHsKICAgICAgICB2YXIgZmlsdGVyZWRSYW5raW5nID0gbWV0aG9kR3JvdXAucmFua2luZy5maWx0ZXIoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgIHJldHVybiBfdGhpczguaXNOdW1iZXJNYXRjaChpdGVtLmJldE51bWJlcnMsIHNlYXJjaE51bWJlcik7CiAgICAgICAgfSk7CiAgICAgICAgcmV0dXJuICgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSgoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoe30sIG1ldGhvZEdyb3VwKSwge30sIHsKICAgICAgICAgIHJhbmtpbmc6IGZpbHRlcmVkUmFua2luZwogICAgICAgIH0pOwogICAgICB9KS5maWx0ZXIoZnVuY3Rpb24gKG1ldGhvZEdyb3VwKSB7CiAgICAgICAgcmV0dXJuIG1ldGhvZEdyb3VwLnJhbmtpbmcubGVuZ3RoID4gMDsKICAgICAgfSk7CgogICAgICAvLyDmkJzntKLng63pl6jkuInkvY3mlbDmjpLooYzmppwKICAgICAgdmFyIGZpbHRlcmVkTnVtYmVyUmFua2luZyA9IG9yaWdpbmFsTnVtYmVyUmFua2luZy5tYXAoZnVuY3Rpb24gKG1ldGhvZEdyb3VwKSB7CiAgICAgICAgdmFyIGZpbHRlcmVkUmFua2luZyA9IG1ldGhvZEdyb3VwLnJhbmtpbmcuZmlsdGVyKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICByZXR1cm4gX3RoaXM4LmlzTnVtYmVyTWF0Y2goaXRlbS5udW1iZXIsIHNlYXJjaE51bWJlcik7CiAgICAgICAgfSk7CiAgICAgICAgcmV0dXJuICgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSgoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoe30sIG1ldGhvZEdyb3VwKSwge30sIHsKICAgICAgICAgIHJhbmtpbmc6IGZpbHRlcmVkUmFua2luZwogICAgICAgIH0pOwogICAgICB9KS5maWx0ZXIoZnVuY3Rpb24gKG1ldGhvZEdyb3VwKSB7CiAgICAgICAgcmV0dXJuIG1ldGhvZEdyb3VwLnJhbmtpbmcubGVuZ3RoID4gMDsKICAgICAgfSk7CiAgICAgIHJldHVybiB7CiAgICAgICAgbWV0aG9kUmFua2luZzogZmlsdGVyZWRNZXRob2RSYW5raW5nLAogICAgICAgIG51bWJlclJhbmtpbmc6IGZpbHRlcmVkTnVtYmVyUmFua2luZwogICAgICB9OwogICAgfSwKICAgIC8qKiDliLfmlrDmlbDmja4gKi9yZWZyZXNoRGF0YTogZnVuY3Rpb24gcmVmcmVzaERhdGEoKSB7CiAgICAgIHZhciBfdGhpczkgPSB0aGlzOwogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSgvKiNfX1BVUkVfXyovKDAsIF9yZWdlbmVyYXRvcjIuZGVmYXVsdCkoKS5tKGZ1bmN0aW9uIF9jYWxsZWU1KCkgewogICAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yMi5kZWZhdWx0KSgpLncoZnVuY3Rpb24gKF9jb250ZXh0NSkgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQ1Lm4pIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIF9jb250ZXh0NS5uID0gMTsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXM5LmNhbGN1bGF0ZVN0YXRpc3RpY3MoKTsKICAgICAgICAgICAgY2FzZSAxOgogICAgICAgICAgICAgIF90aGlzOS4kbWVzc2FnZS5zdWNjZXNzKCfmlbDmja7lt7LliLfmlrAnKTsKICAgICAgICAgICAgY2FzZSAyOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDUuYSgyKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlNSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8qKiDmtYvor5XkuInkvY3mlbDnu4TlkIjmj5Dlj5bpgLvovpEgKi90ZXN0VGhyZWVEaWdpdEV4dHJhY3Rpb246IGZ1bmN0aW9uIHRlc3RUaHJlZURpZ2l0RXh0cmFjdGlvbigpIHsKICAgICAgdmFyIF90aGlzMCA9IHRoaXM7CiAgICAgIGNvbnNvbGUubG9nKCc9PT0g5rWL6K+V5LiJ5L2N5pWw57uE5ZCI5o+Q5Y+W5ZKM5b2S5LiA5YyW6YC76L6RID09PScpOwoKICAgICAgLy8g5rWL6K+V5LiN5ZCM6ZW/5bqm55qE5Y+356CBCiAgICAgIHZhciB0ZXN0Q2FzZXMgPSBbewogICAgICAgIGE6IDEsCiAgICAgICAgYjogMiwKICAgICAgICBjOiAzCiAgICAgIH0sCiAgICAgIC8vIOS4ieS9jeaVsAogICAgICB7CiAgICAgICAgYTogMSwKICAgICAgICBiOiAyLAogICAgICAgIGM6IDMsCiAgICAgICAgZDogNAogICAgICB9LAogICAgICAvLyDlm5vkvY3mlbAKICAgICAgewogICAgICAgIGE6IDAsCiAgICAgICAgYjogMSwKICAgICAgICBjOiAyLAogICAgICAgIGQ6IDMsCiAgICAgICAgZTogNCwKICAgICAgICBmOiA1CiAgICAgIH0sCiAgICAgIC8vIOWFreS9jeaVsAogICAgICB7CiAgICAgICAgYTogMSwKICAgICAgICBiOiAyLAogICAgICAgIGM6IDMsCiAgICAgICAgZDogNCwKICAgICAgICBlOiA1CiAgICAgIH0gLy8g5LqU5L2N5pWwCiAgICAgIF07CiAgICAgIHRlc3RDYXNlcy5mb3JFYWNoKGZ1bmN0aW9uICh0ZXN0Q2FzZSwgaW5kZXgpIHsKICAgICAgICBjb25zb2xlLmxvZygiXG5cdTZENEJcdThCRDVcdTc1MjhcdTRGOEIgIi5jb25jYXQoaW5kZXggKyAxLCAiOiIpLCB0ZXN0Q2FzZSk7CiAgICAgICAgdmFyIHRocmVlRGlnaXRzID0gX3RoaXMwLmV4dHJhY3RUaHJlZURpZ2l0TnVtYmVycyh0ZXN0Q2FzZSk7CiAgICAgICAgY29uc29sZS5sb2coJ+aPkOWPlueahOS4ieS9jeaVsOe7hOWQiDonLCB0aHJlZURpZ2l0cyk7CgogICAgICAgIC8vIOa1i+ivleW9kuS4gOWMluaViOaenAogICAgICAgIHZhciBub3JtYWxpemVkU2V0ID0gbmV3IFNldCgpOwogICAgICAgIHRocmVlRGlnaXRzLmZvckVhY2goZnVuY3Rpb24gKGRpZ2l0KSB7CiAgICAgICAgICB2YXIgbm9ybWFsaXplZCA9IF90aGlzMC5ub3JtYWxpemVUaHJlZURpZ2l0cyhkaWdpdCk7CiAgICAgICAgICBub3JtYWxpemVkU2V0LmFkZChub3JtYWxpemVkKTsKICAgICAgICAgIGNvbnNvbGUubG9nKCIiLmNvbmNhdChkaWdpdCwgIiAtPiAiKS5jb25jYXQobm9ybWFsaXplZCkpOwogICAgICAgIH0pOwogICAgICAgIGNvbnNvbGUubG9nKCflvZLkuIDljJblkI7nmoTllK/kuIDnu4TlkIg6JywgQXJyYXkuZnJvbShub3JtYWxpemVkU2V0KSk7CiAgICAgIH0pOwogICAgICBjb25zb2xlLmxvZygnPT09IOa1i+ivleWujOaIkCA9PT0nKTsKICAgIH0sCiAgICAvKiog6K6h566X57uE5ZCI5pWwIEMobixyKSAqL2NhbGN1bGF0ZUNvbWJpbmF0aW9uczogZnVuY3Rpb24gY2FsY3VsYXRlQ29tYmluYXRpb25zKG4sIHIpIHsKICAgICAgaWYgKHIgPiBuKSByZXR1cm4gMDsKICAgICAgaWYgKHIgPT09IDAgfHwgciA9PT0gbikgcmV0dXJuIDE7CiAgICAgIHZhciByZXN1bHQgPSAxOwogICAgICBmb3IgKHZhciBpID0gMDsgaSA8IHI7IGkrKykgewogICAgICAgIHJlc3VsdCA9IHJlc3VsdCAqIChuIC0gaSkgLyAoaSArIDEpOwogICAgICB9CiAgICAgIHJldHVybiBNYXRoLnJvdW5kKHJlc3VsdCk7CiAgICB9LAogICAgLyoqIOWFs+mXreWvueivneahhiAqLwogICAgLyoqIOWKoOi9veeUqOaIt+WIl+ihqCAqLwogICAgbG9hZFVzZXJMaXN0OiBmdW5jdGlvbiBsb2FkVXNlckxpc3QoKSB7CiAgICAgIHZhciBfdGhpczEgPSB0aGlzOwogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSgvKiNfX1BVUkVfXyovKDAsIF9yZWdlbmVyYXRvcjIuZGVmYXVsdCkoKS5tKGZ1bmN0aW9uIF9jYWxsZWU2KCkgewogICAgICAgIHZhciB1c2VyUmVzcG9uc2UsIGFsbFVzZXJzLCByZWNvcmRSZXNwb25zZSwgdXNlclJlY29yZENvdW50cywgX3QzOwogICAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yMi5kZWZhdWx0KSgpLncoZnVuY3Rpb24gKF9jb250ZXh0NikgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQ2Lm4pIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIF9jb250ZXh0Ni5wID0gMDsKICAgICAgICAgICAgICBfY29udGV4dDYubiA9IDE7CiAgICAgICAgICAgICAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICAgICAgICAgICAgICB1cmw6ICcvc3lzdGVtL3VzZXIvbGlzdCcsCiAgICAgICAgICAgICAgICBtZXRob2Q6ICdnZXQnLAogICAgICAgICAgICAgICAgcGFyYW1zOiB7CiAgICAgICAgICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICAgICAgICAgIHBhZ2VTaXplOiAxMDAwCiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIGNhc2UgMToKICAgICAgICAgICAgICB1c2VyUmVzcG9uc2UgPSBfY29udGV4dDYudjsKICAgICAgICAgICAgICBpZiAoISghdXNlclJlc3BvbnNlIHx8ICF1c2VyUmVzcG9uc2Uucm93cykpIHsKICAgICAgICAgICAgICAgIF9jb250ZXh0Ni5uID0gMjsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfdGhpczEudXNlckxpc3QgPSBbXTsKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ2LmEoMik7CiAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICBhbGxVc2VycyA9IHVzZXJSZXNwb25zZS5yb3dzLmZpbHRlcihmdW5jdGlvbiAodXNlcikgewogICAgICAgICAgICAgICAgcmV0dXJuIHVzZXIuc3RhdHVzID09PSAnMCc7CiAgICAgICAgICAgICAgfSk7IC8vIOWPquiOt+WPluato+W4uOeKtuaAgeeahOeUqOaItwogICAgICAgICAgICAgIC8vIOiOt+WPluacieS4i+azqOiusOW9leeahOeUqOaIt0lE5YiX6KGoCiAgICAgICAgICAgICAgX2NvbnRleHQ2Lm4gPSAzOwogICAgICAgICAgICAgIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgICAgICAgICAgICAgdXJsOiAnL2dhbWUvcmVjb3JkL2xpc3QnLAogICAgICAgICAgICAgICAgbWV0aG9kOiAnZ2V0JywKICAgICAgICAgICAgICAgIHBhcmFtczogewogICAgICAgICAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgICAgICAgICBwYWdlU2l6ZTogMTAwMDAwIC8vIOiOt+WPluaJgOacieiusOW9leadpee7n+iuoeeUqOaItwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICBjYXNlIDM6CiAgICAgICAgICAgICAgcmVjb3JkUmVzcG9uc2UgPSBfY29udGV4dDYudjsKICAgICAgICAgICAgICBpZiAocmVjb3JkUmVzcG9uc2UgJiYgcmVjb3JkUmVzcG9uc2Uucm93cykgewogICAgICAgICAgICAgICAgLy8g57uf6K6h5q+P5Liq55So5oi355qE5LiL5rOo6K6w5b2V5pWw6YePCiAgICAgICAgICAgICAgICB1c2VyUmVjb3JkQ291bnRzID0ge307CiAgICAgICAgICAgICAgICByZWNvcmRSZXNwb25zZS5yb3dzLmZvckVhY2goZnVuY3Rpb24gKHJlY29yZCkgewogICAgICAgICAgICAgICAgICB2YXIgdXNlcklkID0gcmVjb3JkLnN5c1VzZXJJZDsKICAgICAgICAgICAgICAgICAgdXNlclJlY29yZENvdW50c1t1c2VySWRdID0gKHVzZXJSZWNvcmRDb3VudHNbdXNlcklkXSB8fCAwKSArIDE7CiAgICAgICAgICAgICAgICB9KTsKCiAgICAgICAgICAgICAgICAvLyDlj6rkv53nlZnmnInkuIvms6jorrDlvZXnmoTnlKjmiLfvvIzlubbmt7vliqDorrDlvZXmlbDph4/kv6Hmga8KICAgICAgICAgICAgICAgIF90aGlzMS51c2VyTGlzdCA9IGFsbFVzZXJzLmZpbHRlcihmdW5jdGlvbiAodXNlcikgewogICAgICAgICAgICAgICAgICByZXR1cm4gdXNlclJlY29yZENvdW50c1t1c2VyLnVzZXJJZF07CiAgICAgICAgICAgICAgICB9KS5tYXAoZnVuY3Rpb24gKHVzZXIpIHsKICAgICAgICAgICAgICAgICAgcmV0dXJuICgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSgoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoe30sIHVzZXIpLCB7fSwgewogICAgICAgICAgICAgICAgICAgIHJlY29yZENvdW50OiB1c2VyUmVjb3JkQ291bnRzW3VzZXIudXNlcklkXSB8fCAwCiAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgfSkuc29ydChmdW5jdGlvbiAoYSwgYikgewogICAgICAgICAgICAgICAgICByZXR1cm4gYi5yZWNvcmRDb3VudCAtIGEucmVjb3JkQ291bnQ7CiAgICAgICAgICAgICAgICB9KTsgLy8g5oyJ5LiL5rOo6K6w5b2V5pWw6YeP6ZmN5bqP5o6S5YiXCgogICAgICAgICAgICAgICAgY29uc29sZS5sb2coIlx1NTJBMFx1OEY3RFx1NzUyOFx1NjIzN1x1NTIxN1x1ODg2OFx1NUI4Q1x1NjIxMFx1RkYwQ1x1NTE3MSAiLmNvbmNhdChfdGhpczEudXNlckxpc3QubGVuZ3RoLCAiIFx1NEUyQVx1NjcwOVx1NEUwQlx1NkNFOFx1OEJCMFx1NUY1NVx1NzY4NFx1NzUyOFx1NjIzNyIpKTsKICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgX3RoaXMxLnVzZXJMaXN0ID0gW107CiAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oJ+acquiOt+WPluWIsOS4i+azqOiusOW9leaVsOaNricpOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfY29udGV4dDYubiA9IDU7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgNDoKICAgICAgICAgICAgICBfY29udGV4dDYucCA9IDQ7CiAgICAgICAgICAgICAgX3QzID0gX2NvbnRleHQ2LnY7CiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W55So5oi35YiX6KGo5aSx6LSlOicsIF90Myk7CiAgICAgICAgICAgICAgX3RoaXMxLiRtZXNzYWdlLmVycm9yKCfojrflj5bnlKjmiLfliJfooajlpLHotKUnKTsKICAgICAgICAgICAgICBfdGhpczEudXNlckxpc3QgPSBbXTsKICAgICAgICAgICAgY2FzZSA1OgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDYuYSgyKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlNiwgbnVsbCwgW1swLCA0XV0pOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvKiog6I635Y+W5oyH5a6a55So5oi355qE6K6w5b2V5pWw5o2uICovZmV0Y2hVc2VyUmVjb3JkRGF0YTogZnVuY3Rpb24gZmV0Y2hVc2VyUmVjb3JkRGF0YSh1c2VySWQpIHsKICAgICAgdmFyIF90aGlzMTAgPSB0aGlzOwogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSgvKiNfX1BVUkVfXyovKDAsIF9yZWdlbmVyYXRvcjIuZGVmYXVsdCkoKS5tKGZ1bmN0aW9uIF9jYWxsZWU3KCkgewogICAgICAgIHZhciByZXNwb25zZSwgX3Q0OwogICAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yMi5kZWZhdWx0KSgpLncoZnVuY3Rpb24gKF9jb250ZXh0NykgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQ3Lm4pIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIF9jb250ZXh0Ny5wID0gMDsKICAgICAgICAgICAgICBfdGhpczEwLmxvYWRpbmcgPSB0cnVlOwogICAgICAgICAgICAgIF9jb250ZXh0Ny5uID0gMTsKICAgICAgICAgICAgICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgICAgICAgICAgICAgIHVybDogJy9nYW1lL3JlY29yZC9saXN0JywKICAgICAgICAgICAgICAgIG1ldGhvZDogJ2dldCcsCiAgICAgICAgICAgICAgICBwYXJhbXM6IHsKICAgICAgICAgICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgICAgICAgICAgcGFnZVNpemU6IDEwMDAwMCwKICAgICAgICAgICAgICAgICAgc3lzVXNlcklkOiB1c2VySWQKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgY2FzZSAxOgogICAgICAgICAgICAgIHJlc3BvbnNlID0gX2NvbnRleHQ3LnY7CiAgICAgICAgICAgICAgaWYgKCEocmVzcG9uc2UgJiYgcmVzcG9uc2Uucm93cykpIHsKICAgICAgICAgICAgICAgIF9jb250ZXh0Ny5uID0gMjsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ3LmEoMiwgcmVzcG9uc2Uucm93cyk7CiAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ3LmEoMiwgW10pOwogICAgICAgICAgICBjYXNlIDM6CiAgICAgICAgICAgICAgX2NvbnRleHQ3Lm4gPSA1OwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDQ6CiAgICAgICAgICAgICAgX2NvbnRleHQ3LnAgPSA0OwogICAgICAgICAgICAgIF90NCA9IF9jb250ZXh0Ny52OwogICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlueUqOaIt+iusOW9leaVsOaNruWksei0pTonLCBfdDQpOwogICAgICAgICAgICAgIF90aGlzMTAuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPlueUqOaIt+iusOW9leaVsOaNruWksei0pScpOwogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDcuYSgyLCBbXSk7CiAgICAgICAgICAgIGNhc2UgNToKICAgICAgICAgICAgICBfY29udGV4dDcucCA9IDU7CiAgICAgICAgICAgICAgX3RoaXMxMC5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Ny5mKDUpOwogICAgICAgICAgICBjYXNlIDY6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Ny5hKDIpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWU3LCBudWxsLCBbWzAsIDQsIDUsIDZdXSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8qKiDnlKjmiLfpgInmi6nlj5jljJblpITnkIYgKi9oYW5kbGVVc2VyQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVVc2VyQ2hhbmdlKHVzZXJJZCkgewogICAgICB2YXIgX3RoaXMxMSA9IHRoaXM7CiAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKC8qI19fUFVSRV9fKi8oMCwgX3JlZ2VuZXJhdG9yMi5kZWZhdWx0KSgpLm0oZnVuY3Rpb24gX2NhbGxlZTgoKSB7CiAgICAgICAgcmV0dXJuICgwLCBfcmVnZW5lcmF0b3IyLmRlZmF1bHQpKCkudyhmdW5jdGlvbiAoX2NvbnRleHQ4KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDgubikgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgLy8g5riF56m65b2T5YmN57uf6K6h5pWw5o2uCiAgICAgICAgICAgICAgX3RoaXMxMS5tZXRob2RSYW5raW5nQnlHcm91cCA9IFtdOwogICAgICAgICAgICAgIF90aGlzMTEubnVtYmVyUmFua2luZ0J5R3JvdXAgPSBbXTsKICAgICAgICAgICAgICBpZiAoIXVzZXJJZCkgewogICAgICAgICAgICAgICAgX2NvbnRleHQ4Lm4gPSAyOwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF9jb250ZXh0OC5uID0gMTsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXMxMS5jYWxjdWxhdGVTdGF0aXN0aWNzKCk7CiAgICAgICAgICAgIGNhc2UgMToKICAgICAgICAgICAgICBfdGhpczExLiRtZXNzYWdlLnN1Y2Nlc3MoIlx1NURGMlx1NTJBMFx1OEY3RFx1NzUyOFx1NjIzNyAiLmNvbmNhdChfdGhpczExLmdldEN1cnJlbnRVc2VyTmFtZSgpLCAiIFx1NzY4NFx1N0VERlx1OEJBMVx1NjU3MFx1NjM2RSIpKTsKICAgICAgICAgICAgY2FzZSAyOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDguYSgyKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlOCk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8qKiDojrflj5blvZPliY3pgInkuK3nlKjmiLfnmoTlkI3np7AgKi9nZXRDdXJyZW50VXNlck5hbWU6IGZ1bmN0aW9uIGdldEN1cnJlbnRVc2VyTmFtZSgpIHsKICAgICAgdmFyIF90aGlzMTIgPSB0aGlzOwogICAgICBpZiAoIXRoaXMuc2VsZWN0ZWRVc2VySWQpIHJldHVybiAnJzsKICAgICAgdmFyIHVzZXIgPSB0aGlzLnVzZXJMaXN0LmZpbmQoZnVuY3Rpb24gKHUpIHsKICAgICAgICByZXR1cm4gdS51c2VySWQgPT09IF90aGlzMTIuc2VsZWN0ZWRVc2VySWQ7CiAgICAgIH0pOwogICAgICByZXR1cm4gdXNlciA/IHVzZXIubmlja05hbWUgfHwgdXNlci51c2VyTmFtZSA6ICcnOwogICAgfSwKICAgIC8qKiDlvannp43liIfmjaLlpITnkIYgKi9oYW5kbGVMb3R0ZXJ5VHlwZUNoYW5nZTogZnVuY3Rpb24gaGFuZGxlTG90dGVyeVR5cGVDaGFuZ2UodGFiKSB7CiAgICAgIGNvbnNvbGUubG9nKCJcdTUyMDdcdTYzNjJcdTUyMzBcdTVGNjlcdTc5Q0Q6ICIuY29uY2F0KHRhYi5uYW1lID09PSAnZnVjYWknID8gJ+emj+W9qTNEJyA6ICfkvZPlvalQMycpKTsKICAgICAgLy8g5YiH5o2i5b2p56eN5pe277yM6K6h566X5bGe5oCn5Lya6Ieq5Yqo5pu05paw5pi+56S655qE5pWw5o2uCiAgICB9LAogICAgLyoqIOWIt+aWsOe7n+iuoeaVsOaNriAqL3JlZnJlc2hTdGF0aXN0aWNzOiBmdW5jdGlvbiByZWZyZXNoU3RhdGlzdGljcygpIHsKICAgICAgdmFyIF90aGlzMTMgPSB0aGlzOwogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSgvKiNfX1BVUkVfXyovKDAsIF9yZWdlbmVyYXRvcjIuZGVmYXVsdCkoKS5tKGZ1bmN0aW9uIF9jYWxsZWU5KCkgewogICAgICAgIHZhciBfdDU7CiAgICAgICAgcmV0dXJuICgwLCBfcmVnZW5lcmF0b3IyLmRlZmF1bHQpKCkudyhmdW5jdGlvbiAoX2NvbnRleHQ5KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDkubikgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgaWYgKCEoX3RoaXMxMy5pc0FkbWluICYmICFfdGhpczEzLnNlbGVjdGVkVXNlcklkKSkgewogICAgICAgICAgICAgICAgX2NvbnRleHQ5Lm4gPSAxOwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF90aGlzMTMuJG1lc3NhZ2Uud2FybmluZygn6K+35YWI6YCJ5oup6KaB5p+l55yL57uf6K6h55qE55So5oi3Jyk7CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0OS5hKDIpOwogICAgICAgICAgICBjYXNlIDE6CiAgICAgICAgICAgICAgX2NvbnRleHQ5LnAgPSAxOwogICAgICAgICAgICAgIF9jb250ZXh0OS5uID0gMjsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXMxMy5sb2FkR2FtZU1ldGhvZHMoKTsKICAgICAgICAgICAgY2FzZSAyOgogICAgICAgICAgICAgIF9jb250ZXh0OS5uID0gMzsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXMxMy5jYWxjdWxhdGVTdGF0aXN0aWNzKCk7CiAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICBfdGhpczEzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+e7n+iuoeaVsOaNruW3suWIt+aWsCcpOwogICAgICAgICAgICAgIF9jb250ZXh0OS5uID0gNTsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSA0OgogICAgICAgICAgICAgIF9jb250ZXh0OS5wID0gNDsKICAgICAgICAgICAgICBfdDUgPSBfY29udGV4dDkudjsKICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfliLfmlrDnu5/orqHlpLHotKU6JywgX3Q1KTsKICAgICAgICAgICAgICBfdGhpczEzLiRtZXNzYWdlLmVycm9yKCfliLfmlrDnu5/orqHlpLHotKUnKTsKICAgICAgICAgICAgY2FzZSA1OgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDkuYSgyKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlOSwgbnVsbCwgW1sxLCA0XV0pOwogICAgICB9KSkoKTsKICAgIH0sCiAgICBjbG9zZTogZnVuY3Rpb24gY2xvc2UoKSB7CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlOwogICAgICAvLyDmuIXnqbrpgInmi6nnirbmgIEKICAgICAgdGhpcy5zZWxlY3RlZFVzZXJJZCA9IG51bGw7CiAgICAgIHRoaXMuYWN0aXZlTG90dGVyeVR5cGUgPSAnZnVjYWknOyAvLyDph43nva7kuLrnpo/lvakKICAgICAgLy8g5riF56m65omA5pyJ57uf6K6h5pWw5o2uCiAgICAgIHRoaXMuY2xlYXJBbGxSYW5raW5nRGF0YSgpOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "name", "props", "visible", "type", "Boolean", "default", "data", "dialogVisible", "methodRankingByGroup", "numberRankingByGroup", "gameMethodsData", "selectedUserId", "userList", "loading", "activeLotteryType", "fucaiMethodRanking", "fucaiNumberRanking", "ticaiMethodRanking", "ticaiNumberRanking", "searchNumber", "isSearchMode", "searchLoading", "searchResultCount", "originalFucaiMethodRanking", "originalFucaiNumberRanking", "originalTicaiMethodRanking", "originalTicaiNumberRanking", "computed", "isAdmin", "userInfo", "$store", "getters", "roles", "userId", "length", "includes", "currentMethodRanking", "currentNumberRanking", "searchResultTitle", "concat", "watch", "val", "_this", "testThreeDigitExtraction", "loadUserList", "loadGameMethods", "then", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "sysUserId", "w", "_context", "n", "sessionStorage", "getItem", "console", "log", "parseInt", "warn", "calculateStatistics", "a", "$emit", "methods", "_this2", "_callee2", "response", "_t", "_context2", "p", "request", "url", "method", "params", "pageNum", "pageSize", "v", "rows", "error", "getBasicMethodsData", "methodId", "methodName", "getMethodName", "find", "getRankClass", "rank", "_this3", "_callee3", "_context3", "getRecordData", "$message", "warning", "clearAllRankingData", "calculateStatisticsByLotteryType", "福彩投注排行", "福彩热门三位数", "体彩投注排行", "体彩热门三位数", "当前选中彩种", "_this4", "_callee4", "_t2", "_context4", "fetchUserRecordData", "_this5", "lotteryType", "gameType", "lotteryId", "gameName", "lotteryTypes", "_toConsumableArray2", "Set", "map", "record", "gameTypes", "lotteryIds", "gameNames", "fucaiData", "filter", "isFucaiLottery", "ticaiData", "isTicaiLottery", "calculateMethodRankingByGroup", "calculateNumberRanking", "_this6", "methodGroups", "for<PERSON>ach", "amount", "parseFloat", "money", "Map", "betNumbers", "JSON", "parse", "numbers", "numberObj", "singleNumberDisplay", "_typeof2", "keys", "Object", "sort", "values", "key", "String", "join", "has", "existing", "get", "totalAmount", "recordCount", "set", "fallback<PERSON><PERSON>bers", "entries", "_ref2", "_ref3", "_slicedToArray2", "numbersMap", "ranking", "Array", "from", "b", "item", "index", "group", "formatBetNumbersForDisplay", "betNumbersKey", "isArray", "num", "substring", "_this7", "methodTotal", "threeDigitMap", "threeDigits", "extractThreeDigitNumbers", "uniqueNormalizedDigits", "digit", "normalizedDigit", "normalizeThreeDigits", "add", "currentAmount", "_ref4", "_ref5", "sortedThreeDigits", "_ref6", "_ref7", "_ref8", "_ref9", "_ref0", "_ref1", "number", "count", "percentage", "toFixed", "push", "cleanStr", "replace", "generateThreeDigitCombinations", "split", "digits", "i", "j", "k", "threeDigit", "test", "handleSearchInput", "value", "performSearch", "trim", "searchResults", "searchInRankingData", "methodRanking", "numberRanking", "calculateSearchResultCount", "success", "clearSearch", "_this8", "originalMethodRanking", "originalNumberRanking", "filteredMethodRanking", "methodGroup", "filteredRanking", "isNumberMatch", "_objectSpread2", "filteredNumberRanking", "refreshData", "_this9", "_callee5", "_context5", "_this0", "testCases", "c", "d", "e", "f", "testCase", "normalizedSet", "normalized", "calculateCombinations", "r", "result", "Math", "round", "_this1", "_callee6", "userResponse", "allUsers", "recordResponse", "userRecordCounts", "_t3", "_context6", "user", "status", "_this10", "_callee7", "_t4", "_context7", "handleUserChange", "_this11", "_callee8", "_context8", "getCurrentUserName", "_this12", "u", "nick<PERSON><PERSON>", "userName", "handleLotteryTypeChange", "tab", "refreshStatistics", "_this13", "_callee9", "_t5", "_context9", "close"], "sources": ["src/views/game/record/components/BetStatistics.vue"], "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      title=\"下注统计\"\n      :visible.sync=\"dialogVisible\"\n      width=\"95%\"\n      append-to-body\n      style=\"margin-top: -50px;\"\n      :close-on-click-modal=\"false\"\n      class=\"statistics-dialog\">\n\n      <!-- 管理员用户选择器 -->\n      <div v-if=\"isAdmin\" class=\"admin-controls\" style=\"margin-bottom: 20px; padding: 15px; background: #f5f7fa; border-radius: 4px;\">\n        <div style=\"display: flex; align-items: center; gap: 15px;\">\n          <span style=\"font-weight: 500; color: #606266;\">选择用户:</span>\n          <el-select\n            v-model=\"selectedUserId\"\n            placeholder=\"选择用户查看统计\"\n            @change=\"handleUserChange\"\n            clearable\n            style=\"width: 250px;\">\n            <el-option\n              v-for=\"user in userList\"\n              :key=\"user.userId\"\n              :label=\"`${user.nickName || user.userName} (ID: ${user.userId}) - ${user.recordCount}条记录`\"\n              :value=\"user.userId\">\n            </el-option>\n          </el-select>\n          <el-button\n            type=\"primary\"\n            size=\"small\"\n            @click=\"refreshStatistics\"\n            :loading=\"loading\">\n            <i class=\"el-icon-refresh\"></i> 刷新统计\n          </el-button>\n          <span v-if=\"selectedUserId\" style=\"color: #67c23a; font-size: 12px;\">\n            当前查看: {{ getCurrentUserName() }}\n          </span>\n        </div>\n      </div>\n\n      <!-- 彩种选择标签页 - 所有用户都可见 -->\n      <div class=\"lottery-type-tabs-container\" style=\"margin-bottom: 20px; text-align: center;\">\n        <el-tabs v-model=\"activeLotteryType\" @tab-click=\"handleLotteryTypeChange\" type=\"card\">\n          <el-tab-pane name=\"fucai\">\n            <span slot=\"label\">\n              <i class=\"el-icon-medal-1\"></i>\n              福彩3D\n            </span>\n          </el-tab-pane>\n          <el-tab-pane name=\"ticai\">\n            <span slot=\"label\">\n              <i class=\"el-icon-trophy-1\"></i>\n              体彩排三\n            </span>\n          </el-tab-pane>\n        </el-tabs>\n      </div>\n\n      <!-- 号码搜索功能 -->\n      <div v-if=\"!isAdmin || (isAdmin && selectedUserId)\" class=\"search-container\" style=\"margin-bottom: 20px;\">\n        <div class=\"search-box\">\n          <el-input\n            v-model=\"searchNumber\"\n            placeholder=\"输入号码搜索相关投注（如：125）\"\n            prefix-icon=\"el-icon-search\"\n            clearable\n            @input=\"handleSearchInput\"\n            @clear=\"clearSearch\"\n            style=\"width: 300px; margin-right: 10px;\">\n          </el-input>\n          <el-button\n            type=\"primary\"\n            icon=\"el-icon-search\"\n            @click=\"performSearch\"\n            :loading=\"searchLoading\">\n            搜索\n          </el-button>\n          <el-button\n            v-if=\"isSearchMode\"\n            type=\"info\"\n            icon=\"el-icon-refresh-left\"\n            @click=\"clearSearch\">\n            显示全部\n          </el-button>\n        </div>\n\n        <!-- 搜索结果提示 -->\n        <div v-if=\"isSearchMode\" class=\"search-result-tip\" style=\"margin-top: 10px;\">\n          <el-alert\n            :title=\"searchResultTitle\"\n            type=\"info\"\n            :closable=\"false\"\n            show-icon>\n          </el-alert>\n        </div>\n      </div>\n\n      <!-- 管理员未选择用户时的提示 -->\n      <div v-if=\"isAdmin && !selectedUserId\" class=\"admin-no-selection\" style=\"text-align: center; padding: 60px 20px; color: #909399;\">\n        <i class=\"el-icon-user\" style=\"font-size: 48px; margin-bottom: 20px; display: block;\"></i>\n        <h3 style=\"margin: 0 0 10px 0; color: #606266;\">请选择要查看统计的用户</h3>\n        <p style=\"margin: 0; font-size: 14px;\">选择用户后将自动加载该用户的下注统计数据</p>\n      </div>\n\n      <!-- 统计内容区域 -->\n      <div v-if=\"!isAdmin || (isAdmin && selectedUserId)\" class=\"statistics-content\">\n\n        <!-- 当前彩种无数据时的提示 -->\n        <div v-if=\"currentMethodRanking.length === 0 && currentNumberRanking.length === 0\"\n             class=\"no-data-tip\"\n             style=\"text-align: center; padding: 60px 20px; color: #909399;\">\n          <i class=\"el-icon-data-analysis\" style=\"font-size: 48px; margin-bottom: 20px; display: block;\"></i>\n          <h3 style=\"margin: 0 0 10px 0; color: #606266;\">\n            {{ activeLotteryType === 'fucai' ? '福彩3D' : '体彩排三' }} 暂无统计数据\n          </h3>\n          <p style=\"margin: 0; font-size: 14px;\">\n            {{ activeLotteryType === 'fucai' ? '切换到体彩排三查看数据' : '切换到福彩3D查看数据' }}\n          </p>\n        </div>\n          <!-- 按玩法分组的热门三位数排行 -->\n        <div v-if=\"currentNumberRanking.length > 0\" class=\"numbers-rankings-section\">\n          <h3 class=\"main-section-title\">\n            <i class=\"el-icon-data-line\"></i>\n            各玩法热门三位数排行榜\n          </h3>\n\n          <!-- 使用Grid布局，一行显示五个玩法 -->\n          <div class=\"numbers-grid\">\n            <div\n              v-for=\"methodGroup in currentNumberRanking\"\n              :key=\"methodGroup.methodId\"\n              class=\"numbers-card\">\n\n              <div class=\"numbers-card-header\">\n                <span class=\"method-name\">{{ methodGroup.methodName }}</span>\n                <span class=\"method-count\">共{{ methodGroup.ranking.length }}项</span>\n              </div>\n\n              <div class=\"numbers-ranking-list\">\n                <div\n                  v-for=\"item in methodGroup.ranking\"\n                  :key=\"item.rank\"\n                  class=\"numbers-ranking-item\">\n                  <div class=\"rank-badge\" :class=\"getRankClass(item.rank)\">\n                    {{ item.rank }}\n                  </div>\n                  <div class=\"numbers-info\">\n                    <div class=\"hot-number\">{{ item.number }}</div>\n                    <div class=\"amount\">￥{{ item.totalAmount.toFixed(0) }}</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      <div class=\"statistics-container\">\n        <!-- 按玩法分组的投注金额排行 - 两列布局 -->\n        <div v-if=\"currentMethodRanking.length > 0\" class=\"method-rankings-section\">\n          <h3 class=\"main-section-title\">\n            <i class=\"el-icon-trophy\"></i>\n            各玩法投注排行榜\n          </h3>\n\n          <!-- 使用Grid布局，一行显示两个玩法 -->\n          <div class=\"method-grid\">\n            <div\n              v-for=\"methodGroup in currentMethodRanking\"\n              :key=\"methodGroup.methodId\"\n              class=\"method-card\">\n\n              <div class=\"method-card-header\">\n                <span class=\"method-name\">{{ methodGroup.methodName }}</span>\n                <span class=\"method-count\">共{{ methodGroup.ranking.length }}项</span>\n              </div>\n\n              <div class=\"ranking-list\">\n                <div\n                  v-for=\"item in methodGroup.ranking\"\n                  :key=\"item.rank\"\n                  class=\"ranking-item\">\n                  <div class=\"rank-badge\" :class=\"getRankClass(item.rank)\">\n                    {{ item.rank }}\n                  </div>\n                  <div class=\"bet-info\">\n                    <div class=\"bet-numbers\">{{ item.betNumbers }}</div>\n                    <div class=\"amount\">￥{{ item.totalAmount.toFixed(0) }}</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n\n      </div>\n      <!-- 统计内容区域结束 -->\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"refreshData\" type=\"primary\" icon=\"el-icon-refresh\">刷新数据</el-button>\n        <el-button @click=\"close\">关闭</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport request from '@/utils/request'\n\nexport default {\n  name: 'BetStatistics',\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      dialogVisible: false,\n      methodRankingByGroup: [], // 按玩法分组的排行榜\n      numberRankingByGroup: [], // 按玩法分组的号码排行榜\n      gameMethodsData: [], // 玩法数据\n      // 管理员功能相关\n      selectedUserId: null, // 选中的用户ID\n      userList: [], // 用户列表\n      loading: false, // 加载状态\n      // 彩种相关\n      activeLotteryType: 'fucai', // 当前选中的彩种：fucai(福彩3D) 或 ticai(体彩P3)\n      // 分彩种的统计数据\n      fucaiMethodRanking: [], // 福彩投注排行\n      fucaiNumberRanking: [], // 福彩热门三位数排行\n      ticaiMethodRanking: [], // 体彩投注排行\n      ticaiNumberRanking: [], // 体彩热门三位数排行\n      // 搜索功能相关\n      searchNumber: '', // 搜索的号码\n      isSearchMode: false, // 是否处于搜索模式\n      searchLoading: false, // 搜索加载状态\n      searchResultCount: 0, // 搜索结果数量\n      // 原始完整数据（用于搜索过滤）\n      originalFucaiMethodRanking: [],\n      originalFucaiNumberRanking: [],\n      originalTicaiMethodRanking: [],\n      originalTicaiNumberRanking: []\n    }\n  },\n  computed: {\n    /** 是否是管理员 */\n    isAdmin() {\n      const userInfo = this.$store.getters.userInfo;\n      const roles = this.$store.getters.roles;\n\n      // 检查是否是超级管理员（用户ID为1）\n      if (userInfo && userInfo.userId === 1) {\n        return true;\n      }\n\n      // 检查是否有管理员角色\n      if (roles && roles.length > 0) {\n        return roles.includes('admin') || roles.includes('3d_admin');\n      }\n\n      return false;\n    },\n\n    /** 当前彩种的投注排行数据 */\n    currentMethodRanking() {\n      return this.activeLotteryType === 'fucai' ? this.fucaiMethodRanking : this.ticaiMethodRanking;\n    },\n\n    /** 当前彩种的热门三位数排行数据 */\n    currentNumberRanking() {\n      return this.activeLotteryType === 'fucai' ? this.fucaiNumberRanking : this.ticaiNumberRanking;\n    },\n\n    /** 搜索结果标题 */\n    searchResultTitle() {\n      return `搜索号码 \"${this.searchNumber}\" 的结果：共找到 ${this.searchResultCount} 个匹配项`;\n    }\n  },\n  watch: {\n    visible(val) {\n      this.dialogVisible = val;\n      if (val) {\n        // 运行测试\n        this.testThreeDigitExtraction();\n\n        // 如果是管理员，只加载用户列表和玩法数据，不自动计算统计\n        if (this.isAdmin) {\n          this.loadUserList();\n          this.loadGameMethods();\n        } else {\n          // 普通用户自动加载统计\n          this.loadGameMethods().then(async () => {\n            // 普通用户从sessionStorage获取自己的数据\n            const sysUserId = sessionStorage.getItem('sysUserId');\n            console.log('[BetStatistics] sessionStorage中的sysUserId:', sysUserId);\n\n            if (sysUserId) {\n              this.selectedUserId = parseInt(sysUserId);\n              console.log('[BetStatistics] 设置selectedUserId:', this.selectedUserId);\n            } else {\n              console.warn('[BetStatistics] 无法从sessionStorage获取sysUserId');\n            }\n            await this.calculateStatistics();\n          });\n        }\n      }\n    },\n    dialogVisible(val) {\n      this.$emit('update:visible', val);\n    }\n  },\n  methods: {\n    /** 加载玩法数据 */\n    async loadGameMethods() {\n      try {\n        // 直接从API获取玩法数据\n        const response = await request({\n          url: '/game/odds/list',\n          method: 'get',\n          params: { pageNum: 1, pageSize: 1000 }\n        });\n\n        if (response && response.rows) {\n          this.gameMethodsData = response.rows;\n         \n        } else {\n          this.gameMethodsData = [];\n          console.warn('玩法数据为空');\n        }\n      } catch (error) {\n        console.error('获取玩法数据失败:', error);\n        // 如果API失败，使用基本的玩法映射\n        this.gameMethodsData = this.getBasicMethodsData();\n      }\n    },\n    /** 获取基本玩法数据（API失败时的备用方案） */\n    getBasicMethodsData() {\n      return [\n        { methodId: 1, methodName: '独胆' },\n        { methodId: 2, methodName: '一码定位' },\n        { methodId: 3, methodName: '两码组合' },\n        { methodId: 4, methodName: '两码对子' },\n        { methodId: 5, methodName: '三码直选' },\n        { methodId: 6, methodName: '三码组选' },\n        { methodId: 7, methodName: '三码组三' },\n        { methodId: 8, methodName: '四码组六' },\n        { methodId: 9, methodName: '四码组三' },\n        { methodId: 10, methodName: '五码组六' },\n        { methodId: 11, methodName: '五码组三' },\n        { methodId: 12, methodName: '六码组六' },\n        { methodId: 13, methodName: '六码组三' },\n        { methodId: 14, methodName: '七码组六' },\n        { methodId: 15, methodName: '七码组三' },\n        { methodId: 16, methodName: '八码组六' },\n        { methodId: 17, methodName: '八码组三' },\n        { methodId: 18, methodName: '九码组六' },\n        { methodId: 19, methodName: '九码组三' },\n        { methodId: 20, methodName: '包打组六' },\n        { methodId: 21, methodName: '7或20和值' },\n        { methodId: 22, methodName: '8或19和值' },\n        { methodId: 23, methodName: '9或18和值' },\n        { methodId: 24, methodName: '10或17和值' },\n        { methodId: 25, methodName: '11或16和值' },\n        { methodId: 26, methodName: '12或15和值' },\n        { methodId: 27, methodName: '13或14和值' },\n        { methodId: 28, methodName: '直选复式' },\n        { methodId: 29, methodName: '和值单双' },\n        { methodId: 30, methodName: '和值大小' },\n        { methodId: 31, methodName: '包打组三' },\n        { methodId: 100, methodName: '三码防对' }\n      ];\n    },\n    /** 获取玩法名称 */\n    getMethodName(methodId) {\n      const method = this.gameMethodsData.find(m => m.methodId === methodId);\n      return method ? method.methodName : `玩法${methodId}`;\n    },\n    /** 获取排名样式类 */\n    getRankClass(rank) {\n      if (rank === 1) return 'rank-first';\n      if (rank === 2) return 'rank-second';\n      if (rank === 3) return 'rank-third';\n      return 'rank-other';\n    },\n    /** 计算统计数据 */\n    async calculateStatistics() {\n      const data = await this.getRecordData();\n\n\n      if (!data || data.length === 0) {\n        if (this.isAdmin && !this.selectedUserId) {\n          this.$message.warning('请选择要查看统计的用户');\n        } else {\n          this.$message.warning('暂无统计数据，请先刷新record页面');\n        }\n        this.clearAllRankingData();\n        return;\n      }\n\n      // 按彩种分别计算统计数据\n      this.calculateStatisticsByLotteryType(data);\n\n      // 调试信息\n      console.log('统计计算完成:', {\n        福彩投注排行: this.fucaiMethodRanking.length,\n        福彩热门三位数: this.fucaiNumberRanking.length,\n        体彩投注排行: this.ticaiMethodRanking.length,\n        体彩热门三位数: this.ticaiNumberRanking.length,\n        当前选中彩种: this.activeLotteryType\n      });\n    },\n    /** 获取record数据 */\n    async getRecordData() {\n      try {\n        // 如果有选择的用户ID（管理员选择的用户或普通用户自己），直接从API获取数据\n        if (this.selectedUserId) {\n          return await this.fetchUserRecordData(this.selectedUserId);\n        }\n\n        // 如果是管理员但没有选择用户，提示选择用户\n        if (this.isAdmin) {\n          this.$message.warning('请选择要查看统计的用户');\n          return [];\n        }\n\n        // 普通用户：直接从API获取当前用户数据，从sessionStorage获取用户ID\n        console.log('[BetStatistics] 普通用户，直接从API获取数据');\n\n        // 从sessionStorage获取用户ID\n        const sysUserId = sessionStorage.getItem('sysUserId');\n        console.log('[BetStatistics] 从sessionStorage获取的sysUserId:', sysUserId);\n\n        if (sysUserId) {\n          console.log('[BetStatistics] 使用sysUserId调用API:', sysUserId);\n          const data = await this.fetchUserRecordData(parseInt(sysUserId));\n          console.log('[BetStatistics] API获取到的数据:', data);\n\n          if (data && data.length > 0) {\n            console.log(`[BetStatistics] 成功加载了 ${data.length} 条统计数据`);\n          } else {\n            console.warn('[BetStatistics] API返回空数据');\n          }\n\n          return data;\n        } else {\n          console.warn('[BetStatistics] 无法从sessionStorage获取sysUserId');\n          this.$message.warning('无法获取用户信息，请重新登录');\n          return [];\n        }\n      } catch (error) {\n        console.error('解析统计数据失败:', error);\n        return [];\n      }\n    },\n    /** 清空所有排行数据 */\n    clearAllRankingData() {\n      this.fucaiMethodRanking = [];\n      this.fucaiNumberRanking = [];\n      this.ticaiMethodRanking = [];\n      this.ticaiNumberRanking = [];\n    },\n\n    /** 按彩种分别计算统计数据 */\n    calculateStatisticsByLotteryType(data) {\n      // 调试：查看数据结构\n      if (data.length > 0) {\n        console.log('数据样本:', data[0]);\n        console.log('可能的彩种字段:', {\n          lotteryType: data[0].lotteryType,\n          gameType: data[0].gameType,\n          lotteryId: data[0].lotteryId,\n          gameName: data[0].gameName,\n          methodName: data[0].methodName\n        });\n\n        // 统计所有可能的彩种标识\n        const lotteryTypes = [...new Set(data.map(record => record.lotteryType))];\n        const gameTypes = [...new Set(data.map(record => record.gameType))];\n        const lotteryIds = [...new Set(data.map(record => record.lotteryId))];\n        const gameNames = [...new Set(data.map(record => record.gameName))];\n\n        console.log('数据中的彩种标识:', {\n          lotteryTypes,\n          gameTypes,\n          lotteryIds,\n          gameNames\n        });\n      }\n\n      // 按彩种分组数据（使用lotteryId字段）\n      const fucaiData = data.filter(record => this.isFucaiLottery(record.lotteryId));\n      const ticaiData = data.filter(record => this.isTicaiLottery(record.lotteryId));\n\n      console.log(`福彩3D数据: ${fucaiData.length}条, 体彩P3数据: ${ticaiData.length}条`);\n\n      // 如果没有明确的彩种区分，将所有数据归类为福彩3D（兼容性处理）\n      if (fucaiData.length === 0 && ticaiData.length === 0 && data.length > 0) {\n        console.log('未检测到彩种字段，将所有数据归类为福彩3D');\n        this.fucaiMethodRanking = this.calculateMethodRankingByGroup(data);\n        this.fucaiNumberRanking = this.calculateNumberRanking(data);\n        this.ticaiMethodRanking = [];\n        this.ticaiNumberRanking = [];\n        return;\n      }\n\n      // 分别计算福彩和体彩的统计数据\n      if (fucaiData.length > 0) {\n        this.originalFucaiMethodRanking = this.calculateMethodRankingByGroup(fucaiData);\n        this.originalFucaiNumberRanking = this.calculateNumberRanking(fucaiData);\n        this.fucaiMethodRanking = [...this.originalFucaiMethodRanking];\n        this.fucaiNumberRanking = [...this.originalFucaiNumberRanking];\n      } else {\n        this.originalFucaiMethodRanking = [];\n        this.originalFucaiNumberRanking = [];\n        this.fucaiMethodRanking = [];\n        this.fucaiNumberRanking = [];\n      }\n\n      if (ticaiData.length > 0) {\n        this.originalTicaiMethodRanking = this.calculateMethodRankingByGroup(ticaiData);\n        this.originalTicaiNumberRanking = this.calculateNumberRanking(ticaiData);\n        this.ticaiMethodRanking = [...this.originalTicaiMethodRanking];\n        this.ticaiNumberRanking = [...this.originalTicaiNumberRanking];\n      } else {\n        this.originalTicaiMethodRanking = [];\n        this.originalTicaiNumberRanking = [];\n        this.ticaiMethodRanking = [];\n        this.ticaiNumberRanking = [];\n      }\n    },\n\n    /** 判断是否为福彩3D */\n    isFucaiLottery(lotteryId) {\n      // 福彩3D的lotteryId是1\n      return lotteryId === 1 || lotteryId === '1';\n    },\n\n    /** 判断是否为体彩P3 */\n    isTicaiLottery(lotteryId) {\n      // 体彩P3的lotteryId是2\n      return lotteryId === 2 || lotteryId === '2';\n    },\n\n    /** 按玩法分组计算排行榜 */\n    calculateMethodRankingByGroup(data) {\n      console.log('[calculateMethodRankingByGroup] 开始计算各玩法投注排行榜');\n\n      // 按玩法分组，相同号码合并累计金额\n      const methodGroups = {};\n\n      data.forEach(record => {\n        const methodId = record.methodId;\n        const amount = parseFloat(record.money) || 0;\n\n        if (!methodGroups[methodId]) {\n          methodGroups[methodId] = new Map(); // 使用Map来存储号码和对应的累计数据\n        }\n\n        // 解析投注号码\n        try {\n          const betNumbers = JSON.parse(record.betNumbers || '{}');\n          const numbers = betNumbers.numbers || [];\n\n          // 为每个号码累计金额\n          numbers.forEach((numberObj) => {\n            let singleNumberDisplay = '';\n\n            if (typeof numberObj === 'object' && numberObj !== null) {\n              // 处理 {a:1, b:2, c:3, d:4, e:5} 格式\n              const keys = Object.keys(numberObj).sort();\n              const values = keys.map(key => String(numberObj[key]));\n              singleNumberDisplay = values.join('');\n            } else {\n              singleNumberDisplay = String(numberObj);\n            }\n\n            // 如果号码已存在，累计金额和记录数\n            if (methodGroups[methodId].has(singleNumberDisplay)) {\n              const existing = methodGroups[methodId].get(singleNumberDisplay);\n              existing.totalAmount += amount;\n              existing.recordCount += 1;\n            } else {\n              // 新号码，创建记录\n              methodGroups[methodId].set(singleNumberDisplay, {\n                betNumbers: singleNumberDisplay,\n                totalAmount: amount,\n                recordCount: 1\n              });\n            }\n          });\n        } catch (error) {\n          console.warn('解析投注号码失败:', record.betNumbers, error);\n          // 如果解析失败，使用原始数据\n          const fallbackNumbers = record.betNumbers || '未知号码';\n\n          if (methodGroups[methodId].has(fallbackNumbers)) {\n            const existing = methodGroups[methodId].get(fallbackNumbers);\n            existing.totalAmount += amount;\n            existing.recordCount += 1;\n          } else {\n            methodGroups[methodId].set(fallbackNumbers, {\n              betNumbers: fallbackNumbers,\n              totalAmount: amount,\n              recordCount: 1\n            });\n          }\n        }\n      });\n\n      console.log('[calculateMethodRankingByGroup] 开始生成排行榜');\n\n      // 为每个玩法生成排行榜并返回\n      return Object.entries(methodGroups).map(([methodId, numbersMap]) => {\n        // 将Map转换为数组并按总金额降序排序\n        const ranking = Array.from(numbersMap.values())\n          .sort((a, b) => b.totalAmount - a.totalAmount)\n          .map((item, index) => ({\n            rank: index + 1,\n            betNumbers: item.betNumbers,\n            totalAmount: item.totalAmount,\n            recordCount: item.recordCount\n          }));\n\n        console.log(`[calculateMethodRankingByGroup] 玩法${methodId}(${this.getMethodName(parseInt(methodId))})：${ranking.length}个不同号码`);\n\n        return {\n          methodId: parseInt(methodId),\n          methodName: this.getMethodName(parseInt(methodId)),\n          ranking\n        };\n      }).filter(group => group.ranking.length > 0); // 只显示有数据的玩法\n    },\n    /** 格式化投注号码用于显示 */\n    formatBetNumbersForDisplay(betNumbersKey) {\n      try {\n        const numbers = JSON.parse(betNumbersKey);\n        if (Array.isArray(numbers) && numbers.length > 0) {\n          return numbers.map(num => {\n            if (typeof num === 'object' && num !== null) {\n              // 处理 {a:1, b:2, c:3, d:4, e:5} 格式\n              const keys = Object.keys(num).sort();\n              const values = keys.map(key => String(num[key]));\n              return values.join('');\n            }\n            return String(num);\n          }).join(', ');\n        }\n      } catch (error) {\n        // 如果解析失败，直接返回原始字符串\n      }\n      return betNumbersKey.length > 20 ? betNumbersKey.substring(0, 20) + '...' : betNumbersKey;\n    },\n    /** 按玩法分组计算号码排行 - 只使用money字段 */\n    calculateNumberRanking(data) {\n      // 初始化返回数组\n      const numberRankingByGroup = [];\n\n      // 按玩法分组统计\n      const methodGroups = {};\n\n      data.forEach(record => {\n        try {\n          const methodId = record.methodId;\n          const money = parseFloat(record.money || 0);\n          const betNumbers = JSON.parse(record.betNumbers || '{}');\n          const numbers = betNumbers.numbers || [];\n\n          // 初始化玩法分组\n          if (!methodGroups[methodId]) {\n            methodGroups[methodId] = {\n              methodTotal: 0,\n              threeDigitMap: new Map()\n            };\n          }\n\n          methodGroups[methodId].methodTotal += money;\n\n          // 处理每个投注号码\n          numbers.forEach(numberObj => {\n            const threeDigits = this.extractThreeDigitNumbers(numberObj);\n\n            // 对当前号码的三位数组合去重（避免同一号码内重复组合多次累计）\n            const uniqueNormalizedDigits = new Set();\n\n            threeDigits.forEach(digit => {\n              // 归一化三位数：将数字按从小到大排序作为统一key\n              const normalizedDigit = this.normalizeThreeDigits(digit);\n              uniqueNormalizedDigits.add(normalizedDigit);\n            });\n\n            // 每个归一化后的三位数累加金额\n            uniqueNormalizedDigits.forEach(normalizedDigit => {\n              const currentAmount = methodGroups[methodId].threeDigitMap.get(normalizedDigit) || 0;\n              methodGroups[methodId].threeDigitMap.set(normalizedDigit, currentAmount + money);\n            });\n          });\n\n        } catch (error) {\n          console.error('解析失败:', error);\n        }\n      });\n\n\n\n      // 生成排行榜\n   \n      this.numberRankingByGroup = [];\n\n      Object.entries(methodGroups).forEach(([methodId, group]) => {\n        // 转换Map为数组并排序，显示所有数据\n        const sortedThreeDigits = Array.from(group.threeDigitMap.entries())\n          .sort(([,a], [,b]) => b - a);\n\n        const ranking = sortedThreeDigits.map(([number, amount], index) => ({\n          rank: index + 1,\n          number,\n          count: 1,\n          totalAmount: amount,\n          percentage: group.methodTotal > 0 ? ((amount / group.methodTotal) * 100).toFixed(1) : '0.0'\n        }));\n\n        if (ranking.length > 0) {\n          numberRankingByGroup.push({\n            methodId: parseInt(methodId),\n            methodName: this.getMethodName(parseInt(methodId)),\n            ranking\n          });\n        }\n      });\n\n      return numberRankingByGroup;\n    },\n    /** 从投注号码中提取所有可能的三位数组合 */\n    extractThreeDigitNumbers(numberObj) {\n      const numbers = [];\n\n      // 处理不同的号码格式\n      if (typeof numberObj === 'string') {\n        // 直接是字符串格式的号码\n        const cleanStr = numberObj.replace(/\\D/g, ''); // 移除非数字字符\n        this.generateThreeDigitCombinations(cleanStr.split(''), numbers);\n      } else if (typeof numberObj === 'object' && numberObj !== null) {\n        // 对象格式 {a: 1, b: 2, c: 3, d: 4, e: 5}\n        const keys = Object.keys(numberObj).sort();\n        const digits = keys.map(key => String(numberObj[key]));\n\n    \n\n        // 生成所有可能的三位数组合\n        this.generateThreeDigitCombinations(digits, numbers);\n      }\n\n      return numbers;\n    },\n    /** 生成所有可能的三位数组合 */\n    generateThreeDigitCombinations(digits, numbers) {\n      const n = digits.length;\n\n      // 如果数字少于3个，无法组成三位数\n      if (n < 3) {\n        return;\n      }\n\n      // 生成所有可能的三位数组合（C(n,3)）\n      for (let i = 0; i < n - 2; i++) {\n        for (let j = i + 1; j < n - 1; j++) {\n          for (let k = j + 1; k < n; k++) {\n            const threeDigit = digits[i] + digits[j] + digits[k];\n            if (/^\\d{3}$/.test(threeDigit)) {\n              numbers.push(threeDigit);\n            }\n          }\n        }\n      }\n    },\n\n    /** 归一化三位数：将数字按从小到大排序，统一相同数字的不同排列 */\n    normalizeThreeDigits(threeDigit) {\n      // 将三位数字符串转换为数组，排序后重新组合\n      return threeDigit.split('').sort().join('');\n    },\n\n    /** 搜索输入处理 */\n    handleSearchInput(value) {\n      // 只允许输入数字\n      this.searchNumber = value.replace(/\\D/g, '');\n    },\n\n    /** 执行搜索 */\n    performSearch() {\n      if (!this.searchNumber.trim()) {\n        this.$message.warning('请输入要搜索的号码');\n        return;\n      }\n\n      this.searchLoading = true;\n      this.isSearchMode = true;\n\n      try {\n        // 搜索当前彩种的数据\n        const searchResults = this.searchInRankingData(this.searchNumber);\n\n        // 更新显示数据为搜索结果\n        if (this.activeLotteryType === 'fucai') {\n          this.fucaiMethodRanking = searchResults.methodRanking;\n          this.fucaiNumberRanking = searchResults.numberRanking;\n        } else {\n          this.ticaiMethodRanking = searchResults.methodRanking;\n          this.ticaiNumberRanking = searchResults.numberRanking;\n        }\n\n        // 计算搜索结果数量\n        this.searchResultCount = this.calculateSearchResultCount(searchResults);\n\n        this.$message.success(`搜索完成，找到 ${this.searchResultCount} 个匹配项`);\n      } catch (error) {\n        console.error('搜索失败:', error);\n        this.$message.error('搜索失败，请重试');\n      } finally {\n        this.searchLoading = false;\n      }\n    },\n\n    /** 清除搜索 */\n    clearSearch() {\n      this.searchNumber = '';\n      this.isSearchMode = false;\n      this.searchResultCount = 0;\n\n      // 恢复原始数据\n      this.fucaiMethodRanking = [...this.originalFucaiMethodRanking];\n      this.fucaiNumberRanking = [...this.originalFucaiNumberRanking];\n      this.ticaiMethodRanking = [...this.originalTicaiMethodRanking];\n      this.ticaiNumberRanking = [...this.originalTicaiNumberRanking];\n    },\n\n    /** 在排行数据中搜索包含指定号码的项目 */\n    searchInRankingData(searchNumber) {\n      const originalMethodRanking = this.activeLotteryType === 'fucai'\n        ? this.originalFucaiMethodRanking\n        : this.originalTicaiMethodRanking;\n\n      const originalNumberRanking = this.activeLotteryType === 'fucai'\n        ? this.originalFucaiNumberRanking\n        : this.originalTicaiNumberRanking;\n\n      // 搜索投注排行榜\n      const filteredMethodRanking = originalMethodRanking.map(methodGroup => {\n        const filteredRanking = methodGroup.ranking.filter(item => {\n          return this.isNumberMatch(item.betNumbers, searchNumber);\n        });\n\n        return {\n          ...methodGroup,\n          ranking: filteredRanking\n        };\n      }).filter(methodGroup => methodGroup.ranking.length > 0);\n\n      // 搜索热门三位数排行榜\n      const filteredNumberRanking = originalNumberRanking.map(methodGroup => {\n        const filteredRanking = methodGroup.ranking.filter(item => {\n          return this.isNumberMatch(item.number, searchNumber);\n        });\n\n        return {\n          ...methodGroup,\n          ranking: filteredRanking\n        };\n      }).filter(methodGroup => methodGroup.ranking.length > 0);\n\n      return {\n        methodRanking: filteredMethodRanking,\n        numberRanking: filteredNumberRanking\n      };\n    },\n    /** 刷新数据 */\n    async refreshData() {\n      await this.calculateStatistics();\n      this.$message.success('数据已刷新');\n    },\n    /** 测试三位数组合提取逻辑 */\n    testThreeDigitExtraction() {\n      console.log('=== 测试三位数组合提取和归一化逻辑 ===');\n\n      // 测试不同长度的号码\n      const testCases = [\n        {a: 1, b: 2, c: 3},                    // 三位数\n        {a: 1, b: 2, c: 3, d: 4},             // 四位数\n        {a: 0, b: 1, c: 2, d: 3, e: 4, f: 5}, // 六位数\n        {a: 1, b: 2, c: 3, d: 4, e: 5},       // 五位数\n      ];\n\n      testCases.forEach((testCase, index) => {\n        console.log(`\\n测试用例 ${index + 1}:`, testCase);\n        const threeDigits = this.extractThreeDigitNumbers(testCase);\n        console.log('提取的三位数组合:', threeDigits);\n\n        // 测试归一化效果\n        const normalizedSet = new Set();\n        threeDigits.forEach(digit => {\n          const normalized = this.normalizeThreeDigits(digit);\n          normalizedSet.add(normalized);\n          console.log(`${digit} -> ${normalized}`);\n        });\n        console.log('归一化后的唯一组合:', Array.from(normalizedSet));\n      });\n\n      console.log('=== 测试完成 ===');\n    },\n    /** 计算组合数 C(n,r) */\n    calculateCombinations(n, r) {\n      if (r > n) return 0;\n      if (r === 0 || r === n) return 1;\n\n      let result = 1;\n      for (let i = 0; i < r; i++) {\n        result = result * (n - i) / (i + 1);\n      }\n      return Math.round(result);\n    },\n    /** 关闭对话框 */\n    /** 加载用户列表 */\n    async loadUserList() {\n      try {\n        // 先获取所有用户\n        const userResponse = await request({\n          url: '/system/user/list',\n          method: 'get',\n          params: { pageNum: 1, pageSize: 1000 }\n        });\n\n        if (!userResponse || !userResponse.rows) {\n          this.userList = [];\n          return;\n        }\n\n        const allUsers = userResponse.rows.filter(user => user.status === '0'); // 只获取正常状态的用户\n\n        // 获取有下注记录的用户ID列表\n        const recordResponse = await request({\n          url: '/game/record/list',\n          method: 'get',\n          params: {\n            pageNum: 1,\n            pageSize: 100000 // 获取所有记录来统计用户\n          }\n        });\n\n        if (recordResponse && recordResponse.rows) {\n          // 统计每个用户的下注记录数量\n          const userRecordCounts = {};\n          recordResponse.rows.forEach(record => {\n            const userId = record.sysUserId;\n            userRecordCounts[userId] = (userRecordCounts[userId] || 0) + 1;\n          });\n\n          // 只保留有下注记录的用户，并添加记录数量信息\n          this.userList = allUsers\n            .filter(user => userRecordCounts[user.userId])\n            .map(user => ({\n              ...user,\n              recordCount: userRecordCounts[user.userId] || 0\n            }))\n            .sort((a, b) => b.recordCount - a.recordCount); // 按下注记录数量降序排列\n\n          console.log(`加载用户列表完成，共 ${this.userList.length} 个有下注记录的用户`);\n        } else {\n          this.userList = [];\n          console.warn('未获取到下注记录数据');\n        }\n\n      } catch (error) {\n        console.error('获取用户列表失败:', error);\n        this.$message.error('获取用户列表失败');\n        this.userList = [];\n      }\n    },\n\n    /** 获取指定用户的记录数据 */\n    async fetchUserRecordData(userId) {\n      try {\n        this.loading = true;\n        const response = await request({\n          url: '/game/record/list',\n          method: 'get',\n          params: {\n            pageNum: 1,\n            pageSize: 100000,\n            sysUserId: userId\n          }\n        });\n\n        if (response && response.rows) {\n          return response.rows;\n        } else {\n          return [];\n        }\n      } catch (error) {\n        console.error('获取用户记录数据失败:', error);\n        this.$message.error('获取用户记录数据失败');\n        return [];\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    /** 用户选择变化处理 */\n    async handleUserChange(userId) {\n      // 清空当前统计数据\n      this.methodRankingByGroup = [];\n      this.numberRankingByGroup = [];\n\n      if (userId) {\n        // 直接加载选中用户的统计数据\n        await this.calculateStatistics();\n        this.$message.success(`已加载用户 ${this.getCurrentUserName()} 的统计数据`);\n      }\n    },\n\n    /** 获取当前选中用户的名称 */\n    getCurrentUserName() {\n      if (!this.selectedUserId) return '';\n      const user = this.userList.find(u => u.userId === this.selectedUserId);\n      return user ? (user.nickName || user.userName) : '';\n    },\n\n    /** 彩种切换处理 */\n    handleLotteryTypeChange(tab) {\n      console.log(`切换到彩种: ${tab.name === 'fucai' ? '福彩3D' : '体彩P3'}`);\n      // 切换彩种时，计算属性会自动更新显示的数据\n    },\n\n    /** 刷新统计数据 */\n    async refreshStatistics() {\n      if (this.isAdmin && !this.selectedUserId) {\n        this.$message.warning('请先选择要查看统计的用户');\n        return;\n      }\n\n      try {\n        await this.loadGameMethods();\n        await this.calculateStatistics();\n        this.$message.success('统计数据已刷新');\n      } catch (error) {\n        console.error('刷新统计失败:', error);\n        this.$message.error('刷新统计失败');\n      }\n    },\n\n    close() {\n      this.dialogVisible = false;\n      // 清空选择状态\n      this.selectedUserId = null;\n      this.activeLotteryType = 'fucai'; // 重置为福彩\n      // 清空所有统计数据\n      this.clearAllRankingData();\n    }\n  }\n}\n</script>\n\n<style scoped>\n/* 管理员控制区域样式 */\n.admin-controls {\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n}\n\n.admin-controls .el-select {\n  background: white;\n}\n\n/* 彩种标签页样式 */\n.lottery-type-tabs {\n  margin-left: 20px;\n  flex: 1;\n}\n\n.lottery-type-tabs-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header {\n  border-bottom: none;\n  margin: 0;\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__nav,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__nav {\n  border: none;\n  border-radius: 8px;\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n  padding: 4px;\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__item,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__item {\n  border: none;\n  background: transparent;\n  color: #606266;\n  font-weight: 500;\n  padding: 8px 16px;\n  margin-right: 4px;\n  border-radius: 6px;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__item:hover,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__item:hover {\n  color: #409eff;\n  background: rgba(64, 158, 255, 0.1);\n  transform: translateY(-1px);\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__item.is-active,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__item.is-active {\n  color: white;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  font-weight: 600;\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n  transform: translateY(-1px);\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__item.is-active::before,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__item.is-active::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, transparent 100%);\n  pointer-events: none;\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__item i,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__item i {\n  margin-right: 6px;\n  font-size: 16px;\n}\n\n.lottery-type-tabs .el-tabs__content,\n.lottery-type-tabs-container .el-tabs__content {\n  display: none; /* 隐藏内容区域，因为我们只用标签页切换 */\n}\n\n.statistics-dialog {\n  .el-dialog {\n    border-radius: 12px;\n  }\n  \n  .el-dialog__header {\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    color: white;\n    padding: 20px 24px;\n  }\n  \n  .el-dialog__title {\n    color: white;\n    font-weight: 600;\n    font-size: 18px;\n  }\n}\n\n.statistics-container {\n  padding: 10px 0;\n}\n\n.main-section-title {\n  display: flex;\n  align-items: center;\n  font-size: 16px;\n  font-weight: 600;\n  color: #2c3e50;\n  margin-bottom: 8px;\n  padding-bottom: 4px;\n  border-bottom: 1px solid #ecf0f1;\n}\n\n.main-section-title i {\n  margin-right: 4px;\n  color: #667eea;\n  font-size: 14px;\n}\n\n/* 玩法排行榜样式 */\n.method-rankings-section {\n  margin-bottom: 15px;\n}\n\n.method-grid {\n  display: grid;\n  grid-template-columns: repeat(5, 1fr);\n  gap: 6px;\n}\n\n@media (max-width: 1400px) {\n  .method-grid {\n    grid-template-columns: repeat(4, 1fr);\n  }\n}\n\n@media (max-width: 1100px) {\n  .method-grid {\n    grid-template-columns: repeat(3, 1fr);\n  }\n}\n\n@media (max-width: 800px) {\n  .method-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n\n@media (max-width: 500px) {\n  .method-grid {\n    grid-template-columns: 1fr;\n  }\n}\n\n.method-card {\n  background: white;\n  border-radius: 10px;\n  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.12);\n  overflow: hidden;\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n  min-width: 0; /* 防止内容溢出 */\n  min-height: 200px; /* 最小高度 */\n  max-height: 500px; /* 增加最大高度以适应更多数据 */\n}\n\n.method-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.18);\n}\n\n.method-card-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 8px 12px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.method-name {\n  font-size: 15px;\n  font-weight: 700;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.method-count {\n  font-size: 13px;\n  opacity: 0.9;\n}\n\n.ranking-list {\n  padding: 8px;\n  max-height: 420px; /* 增加列表高度以适应更多数据 */\n  overflow-y: auto; /* 添加垂直滚动条 */\n}\n\n/* 滚动条样式 */\n.ranking-list::-webkit-scrollbar {\n  width: 4px;\n}\n\n.ranking-list::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 2px;\n}\n\n.ranking-list::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 2px;\n}\n\n.ranking-list::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n/* 热门三位数排行榜滚动条样式 */\n.numbers-ranking-list::-webkit-scrollbar {\n  width: 4px;\n}\n\n.numbers-ranking-list::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 2px;\n}\n\n.numbers-ranking-list::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 2px;\n}\n\n.numbers-ranking-list::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n.ranking-item {\n  display: flex;\n  align-items: center;\n  padding: 4px 8px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.ranking-item:last-child {\n  border-bottom: none;\n}\n\n.rank-badge {\n  min-width: 18px;\n  height: 18px;\n  padding: 0 4px;\n  border-radius: 9px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  font-size: 10px;\n  color: white;\n  flex: 0 0 auto;\n  margin-right: 8px;\n  white-space: nowrap;\n}\n\n.rank-first {\n  background: linear-gradient(135deg, #ff6b6b, #ee5a24);\n}\n\n.rank-second {\n  background: linear-gradient(135deg, #feca57, #ff9ff3);\n}\n\n.rank-third {\n  background: linear-gradient(135deg, #48dbfb, #0abde3);\n}\n\n.rank-other {\n  background: linear-gradient(135deg, #a4b0be, #747d8c);\n}\n\n.bet-info {\n  display: flex;\n  align-items: center;\n  flex: 1;\n  min-width: 0;\n}\n\n.bet-numbers {\n  font-size: 15px;\n  font-weight: 900;\n  color: #ffffff;\n  font-family: 'Courier New', monospace;\n  background: linear-gradient(135deg, #667eea, #764ba2);\n  padding: 3px 8px;\n  border-radius: 5px;\n  text-align: center;\n  box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);\n  letter-spacing: 0.5px;\n  flex: 2;\n  margin-right: 8px;\n}\n\n.amount {\n  color: #e74c3c;\n  font-weight: 700;\n  font-size: 14px;\n  white-space: nowrap;\n  flex: 1;\n  text-align: right;\n}\n\n.record-count {\n  color: #95a5a6;\n  font-size: 11px;\n  text-align: right;\n  margin-top: 2px;\n}\n\n/* 热门三位数样式 */\n.numbers-section {\n  margin-bottom: 30px;\n}\n\n.ranking-table {\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.amount-text {\n  font-weight: 600;\n  color: #e74c3c;\n}\n\n.percentage-text {\n  font-weight: 500;\n  color: #3498db;\n}\n\n/* 热门三位数卡片样式 */\n.numbers-rankings-section {\n  margin-bottom: 15px;\n}\n\n.numbers-grid {\n  display: grid;\n  grid-template-columns: repeat(5, 1fr);\n  gap: 6px;\n}\n\n@media (max-width: 1400px) {\n  .numbers-grid {\n    grid-template-columns: repeat(4, 1fr);\n  }\n}\n\n@media (max-width: 1100px) {\n  .numbers-grid {\n    grid-template-columns: repeat(3, 1fr);\n  }\n}\n\n@media (max-width: 800px) {\n  .numbers-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n\n@media (max-width: 500px) {\n  .numbers-grid {\n    grid-template-columns: 1fr;\n  }\n}\n\n.numbers-card {\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n  min-width: 0;\n  min-height: 120px;\n  max-height: 400px; /* 增加最大高度以适应更多数据 */\n}\n\n.numbers-card:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.numbers-card-header {\n  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);\n  color: white;\n  padding: 4px 6px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.numbers-ranking-list {\n  padding: 4px;\n  max-height: 320px; /* 限制列表高度以适应更多数据 */\n  overflow-y: auto; /* 添加垂直滚动条 */\n}\n\n.numbers-ranking-item {\n  display: flex;\n  align-items: center;\n  padding: 2px 4px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.numbers-ranking-item:last-child {\n  border-bottom: none;\n}\n\n.numbers-info {\n  display: flex;\n  align-items: center;\n  flex: 1;\n  min-width: 0;\n}\n\n.numbers-info .hot-number {\n  font-family: 'Courier New', monospace;\n  font-weight: 900;\n  font-size: 13px;\n  color: #ffffff;\n  background: linear-gradient(135deg, #27ae60, #2ecc71);\n  padding: 2px 4px;\n  border-radius: 3px;\n  text-align: center;\n  flex: 2;\n  margin-right: 4px;\n}\n\n.numbers-info .amount {\n  color: #e74c3c;\n  font-weight: 700;\n  font-size: 13px;\n  white-space: nowrap;\n  flex: 1;\n  text-align: right;\n}\n\n\n\n.dialog-footer {\n  text-align: right;\n  padding-top: 20px;\n  border-top: 1px solid #ecf0f1;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgNA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,oBAAA;MAAA;MACAC,oBAAA;MAAA;MACAC,eAAA;MAAA;MACA;MACAC,cAAA;MAAA;MACAC,QAAA;MAAA;MACAC,OAAA;MAAA;MACA;MACAC,iBAAA;MAAA;MACA;MACAC,kBAAA;MAAA;MACAC,kBAAA;MAAA;MACAC,kBAAA;MAAA;MACAC,kBAAA;MAAA;MACA;MACAC,YAAA;MAAA;MACAC,YAAA;MAAA;MACAC,aAAA;MAAA;MACAC,iBAAA;MAAA;MACA;MACAC,0BAAA;MACAC,0BAAA;MACAC,0BAAA;MACAC,0BAAA;IACA;EACA;EACAC,QAAA;IACA,aACAC,OAAA,WAAAA,QAAA;MACA,IAAAC,QAAA,QAAAC,MAAA,CAAAC,OAAA,CAAAF,QAAA;MACA,IAAAG,KAAA,QAAAF,MAAA,CAAAC,OAAA,CAAAC,KAAA;;MAEA;MACA,IAAAH,QAAA,IAAAA,QAAA,CAAAI,MAAA;QACA;MACA;;MAEA;MACA,IAAAD,KAAA,IAAAA,KAAA,CAAAE,MAAA;QACA,OAAAF,KAAA,CAAAG,QAAA,aAAAH,KAAA,CAAAG,QAAA;MACA;MAEA;IACA;IAEA,kBACAC,oBAAA,WAAAA,qBAAA;MACA,YAAAtB,iBAAA,oBAAAC,kBAAA,QAAAE,kBAAA;IACA;IAEA,qBACAoB,oBAAA,WAAAA,qBAAA;MACA,YAAAvB,iBAAA,oBAAAE,kBAAA,QAAAE,kBAAA;IACA;IAEA,aACAoB,iBAAA,WAAAA,kBAAA;MACA,qCAAAC,MAAA,MAAApB,YAAA,oDAAAoB,MAAA,MAAAjB,iBAAA;IACA;EACA;EACAkB,KAAA;IACAtC,OAAA,WAAAA,QAAAuC,GAAA;MAAA,IAAAC,KAAA;MACA,KAAAnC,aAAA,GAAAkC,GAAA;MACA,IAAAA,GAAA;QACA;QACA,KAAAE,wBAAA;;QAEA;QACA,SAAAf,OAAA;UACA,KAAAgB,YAAA;UACA,KAAAC,eAAA;QACA;UACA;UACA,KAAAA,eAAA,GAAAC,IAAA,kBAAAC,kBAAA,CAAA1C,OAAA,mBAAA2C,aAAA,CAAA3C,OAAA,IAAA4C,CAAA,UAAAC,QAAA;YAAA,IAAAC,SAAA;YAAA,WAAAH,aAAA,CAAA3C,OAAA,IAAA+C,CAAA,WAAAC,QAAA;cAAA,kBAAAA,QAAA,CAAAC,CAAA;gBAAA;kBACA;kBACAH,SAAA,GAAAI,cAAA,CAAAC,OAAA;kBACAC,OAAA,CAAAC,GAAA,+CAAAP,SAAA;kBAEA,IAAAA,SAAA;oBACAT,KAAA,CAAA/B,cAAA,GAAAgD,QAAA,CAAAR,SAAA;oBACAM,OAAA,CAAAC,GAAA,sCAAAhB,KAAA,CAAA/B,cAAA;kBACA;oBACA8C,OAAA,CAAAG,IAAA;kBACA;kBAAAP,QAAA,CAAAC,CAAA;kBAAA,OACAZ,KAAA,CAAAmB,mBAAA;gBAAA;kBAAA,OAAAR,QAAA,CAAAS,CAAA;cAAA;YAAA,GAAAZ,OAAA;UAAA,CACA;QACA;MACA;IACA;IACA3C,aAAA,WAAAA,cAAAkC,GAAA;MACA,KAAAsB,KAAA,mBAAAtB,GAAA;IACA;EACA;EACAuB,OAAA;IACA,aACAnB,eAAA,WAAAA,gBAAA;MAAA,IAAAoB,MAAA;MAAA,WAAAlB,kBAAA,CAAA1C,OAAA,mBAAA2C,aAAA,CAAA3C,OAAA,IAAA4C,CAAA,UAAAiB,SAAA;QAAA,IAAAC,QAAA,EAAAC,EAAA;QAAA,WAAApB,aAAA,CAAA3C,OAAA,IAAA+C,CAAA,WAAAiB,SAAA;UAAA,kBAAAA,SAAA,CAAAf,CAAA;YAAA;cAAAe,SAAA,CAAAC,CAAA;cAAAD,SAAA,CAAAf,CAAA;cAAA,OAGA,IAAAiB,gBAAA;gBACAC,GAAA;gBACAC,MAAA;gBACAC,MAAA;kBAAAC,OAAA;kBAAAC,QAAA;gBAAA;cACA;YAAA;cAJAT,QAAA,GAAAE,SAAA,CAAAQ,CAAA;cAMA,IAAAV,QAAA,IAAAA,QAAA,CAAAW,IAAA;gBACAb,MAAA,CAAAvD,eAAA,GAAAyD,QAAA,CAAAW,IAAA;cAEA;gBACAb,MAAA,CAAAvD,eAAA;gBACA+C,OAAA,CAAAG,IAAA;cACA;cAAAS,SAAA,CAAAf,CAAA;cAAA;YAAA;cAAAe,SAAA,CAAAC,CAAA;cAAAF,EAAA,GAAAC,SAAA,CAAAQ,CAAA;cAEApB,OAAA,CAAAsB,KAAA,cAAAX,EAAA;cACA;cACAH,MAAA,CAAAvD,eAAA,GAAAuD,MAAA,CAAAe,mBAAA;YAAA;cAAA,OAAAX,SAAA,CAAAP,CAAA;UAAA;QAAA,GAAAI,QAAA;MAAA;IAEA;IACA,4BACAc,mBAAA,WAAAA,oBAAA;MACA,QACA;QAAAC,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,EACA;IACA;IACA,aACAC,aAAA,WAAAA,cAAAF,QAAA;MACA,IAAAR,MAAA,QAAA/D,eAAA,CAAA0E,IAAA,WAAAnC,CAAA;QAAA,OAAAA,CAAA,CAAAgC,QAAA,KAAAA,QAAA;MAAA;MACA,OAAAR,MAAA,GAAAA,MAAA,CAAAS,UAAA,kBAAA3C,MAAA,CAAA0C,QAAA;IACA;IACA,cACAI,YAAA,WAAAA,aAAAC,IAAA;MACA,IAAAA,IAAA;MACA,IAAAA,IAAA;MACA,IAAAA,IAAA;MACA;IACA;IACA,aACAzB,mBAAA,WAAAA,oBAAA;MAAA,IAAA0B,MAAA;MAAA,WAAAxC,kBAAA,CAAA1C,OAAA,mBAAA2C,aAAA,CAAA3C,OAAA,IAAA4C,CAAA,UAAAuC,SAAA;QAAA,IAAAlF,IAAA;QAAA,WAAA0C,aAAA,CAAA3C,OAAA,IAAA+C,CAAA,WAAAqC,SAAA;UAAA,kBAAAA,SAAA,CAAAnC,CAAA;YAAA;cAAAmC,SAAA,CAAAnC,CAAA;cAAA,OACAiC,MAAA,CAAAG,aAAA;YAAA;cAAApF,IAAA,GAAAmF,SAAA,CAAAZ,CAAA;cAAA,MAGA,CAAAvE,IAAA,IAAAA,IAAA,CAAA4B,MAAA;gBAAAuD,SAAA,CAAAnC,CAAA;gBAAA;cAAA;cACA,IAAAiC,MAAA,CAAA3D,OAAA,KAAA2D,MAAA,CAAA5E,cAAA;gBACA4E,MAAA,CAAAI,QAAA,CAAAC,OAAA;cACA;gBACAL,MAAA,CAAAI,QAAA,CAAAC,OAAA;cACA;cACAL,MAAA,CAAAM,mBAAA;cAAA,OAAAJ,SAAA,CAAA3B,CAAA;YAAA;cAIA;cACAyB,MAAA,CAAAO,gCAAA,CAAAxF,IAAA;;cAEA;cACAmD,OAAA,CAAAC,GAAA;gBACAqC,MAAA,EAAAR,MAAA,CAAAxE,kBAAA,CAAAmB,MAAA;gBACA8D,OAAA,EAAAT,MAAA,CAAAvE,kBAAA,CAAAkB,MAAA;gBACA+D,MAAA,EAAAV,MAAA,CAAAtE,kBAAA,CAAAiB,MAAA;gBACAgE,OAAA,EAAAX,MAAA,CAAArE,kBAAA,CAAAgB,MAAA;gBACAiE,MAAA,EAAAZ,MAAA,CAAAzE;cACA;YAAA;cAAA,OAAA2E,SAAA,CAAA3B,CAAA;UAAA;QAAA,GAAA0B,QAAA;MAAA;IACA;IACA,iBACAE,aAAA,WAAAA,cAAA;MAAA,IAAAU,MAAA;MAAA,WAAArD,kBAAA,CAAA1C,OAAA,mBAAA2C,aAAA,CAAA3C,OAAA,IAAA4C,CAAA,UAAAoD,SAAA;QAAA,IAAAlD,SAAA,EAAA7C,IAAA,EAAAgG,GAAA;QAAA,WAAAtD,aAAA,CAAA3C,OAAA,IAAA+C,CAAA,WAAAmD,SAAA;UAAA,kBAAAA,SAAA,CAAAjD,CAAA;YAAA;cAAAiD,SAAA,CAAAjC,CAAA;cAAA,KAGA8B,MAAA,CAAAzF,cAAA;gBAAA4F,SAAA,CAAAjD,CAAA;gBAAA;cAAA;cAAAiD,SAAA,CAAAjD,CAAA;cAAA,OACA8C,MAAA,CAAAI,mBAAA,CAAAJ,MAAA,CAAAzF,cAAA;YAAA;cAAA,OAAA4F,SAAA,CAAAzC,CAAA,IAAAyC,SAAA,CAAA1B,CAAA;YAAA;cAAA,KAIAuB,MAAA,CAAAxE,OAAA;gBAAA2E,SAAA,CAAAjD,CAAA;gBAAA;cAAA;cACA8C,MAAA,CAAAT,QAAA,CAAAC,OAAA;cAAA,OAAAW,SAAA,CAAAzC,CAAA,IACA;YAAA;cAGA;cACAL,OAAA,CAAAC,GAAA;;cAEA;cACAP,SAAA,GAAAI,cAAA,CAAAC,OAAA;cACAC,OAAA,CAAAC,GAAA,iDAAAP,SAAA;cAAA,KAEAA,SAAA;gBAAAoD,SAAA,CAAAjD,CAAA;gBAAA;cAAA;cACAG,OAAA,CAAAC,GAAA,sCAAAP,SAAA;cAAAoD,SAAA,CAAAjD,CAAA;cAAA,OACA8C,MAAA,CAAAI,mBAAA,CAAA7C,QAAA,CAAAR,SAAA;YAAA;cAAA7C,IAAA,GAAAiG,SAAA,CAAA1B,CAAA;cACApB,OAAA,CAAAC,GAAA,+BAAApD,IAAA;cAEA,IAAAA,IAAA,IAAAA,IAAA,CAAA4B,MAAA;gBACAuB,OAAA,CAAAC,GAAA,mDAAAnB,MAAA,CAAAjC,IAAA,CAAA4B,MAAA;cACA;gBACAuB,OAAA,CAAAG,IAAA;cACA;cAAA,OAAA2C,SAAA,CAAAzC,CAAA,IAEAxD,IAAA;YAAA;cAEAmD,OAAA,CAAAG,IAAA;cACAwC,MAAA,CAAAT,QAAA,CAAAC,OAAA;cAAA,OAAAW,SAAA,CAAAzC,CAAA,IACA;YAAA;cAAAyC,SAAA,CAAAjD,CAAA;cAAA;YAAA;cAAAiD,SAAA,CAAAjC,CAAA;cAAAgC,GAAA,GAAAC,SAAA,CAAA1B,CAAA;cAGApB,OAAA,CAAAsB,KAAA,cAAAuB,GAAA;cAAA,OAAAC,SAAA,CAAAzC,CAAA,IACA;YAAA;cAAA,OAAAyC,SAAA,CAAAzC,CAAA;UAAA;QAAA,GAAAuC,QAAA;MAAA;IAEA;IACA,eACAR,mBAAA,WAAAA,oBAAA;MACA,KAAA9E,kBAAA;MACA,KAAAC,kBAAA;MACA,KAAAC,kBAAA;MACA,KAAAC,kBAAA;IACA;IAEA,kBACA4E,gCAAA,WAAAA,iCAAAxF,IAAA;MAAA,IAAAmG,MAAA;MACA;MACA,IAAAnG,IAAA,CAAA4B,MAAA;QACAuB,OAAA,CAAAC,GAAA,UAAApD,IAAA;QACAmD,OAAA,CAAAC,GAAA;UACAgD,WAAA,EAAApG,IAAA,IAAAoG,WAAA;UACAC,QAAA,EAAArG,IAAA,IAAAqG,QAAA;UACAC,SAAA,EAAAtG,IAAA,IAAAsG,SAAA;UACAC,QAAA,EAAAvG,IAAA,IAAAuG,QAAA;UACA3B,UAAA,EAAA5E,IAAA,IAAA4E;QACA;;QAEA;QACA,IAAA4B,YAAA,OAAAC,mBAAA,CAAA1G,OAAA,MAAA2G,GAAA,CAAA1G,IAAA,CAAA2G,GAAA,WAAAC,MAAA;UAAA,OAAAA,MAAA,CAAAR,WAAA;QAAA;QACA,IAAAS,SAAA,OAAAJ,mBAAA,CAAA1G,OAAA,MAAA2G,GAAA,CAAA1G,IAAA,CAAA2G,GAAA,WAAAC,MAAA;UAAA,OAAAA,MAAA,CAAAP,QAAA;QAAA;QACA,IAAAS,UAAA,OAAAL,mBAAA,CAAA1G,OAAA,MAAA2G,GAAA,CAAA1G,IAAA,CAAA2G,GAAA,WAAAC,MAAA;UAAA,OAAAA,MAAA,CAAAN,SAAA;QAAA;QACA,IAAAS,SAAA,OAAAN,mBAAA,CAAA1G,OAAA,MAAA2G,GAAA,CAAA1G,IAAA,CAAA2G,GAAA,WAAAC,MAAA;UAAA,OAAAA,MAAA,CAAAL,QAAA;QAAA;QAEApD,OAAA,CAAAC,GAAA;UACAoD,YAAA,EAAAA,YAAA;UACAK,SAAA,EAAAA,SAAA;UACAC,UAAA,EAAAA,UAAA;UACAC,SAAA,EAAAA;QACA;MACA;;MAEA;MACA,IAAAC,SAAA,GAAAhH,IAAA,CAAAiH,MAAA,WAAAL,MAAA;QAAA,OAAAT,MAAA,CAAAe,cAAA,CAAAN,MAAA,CAAAN,SAAA;MAAA;MACA,IAAAa,SAAA,GAAAnH,IAAA,CAAAiH,MAAA,WAAAL,MAAA;QAAA,OAAAT,MAAA,CAAAiB,cAAA,CAAAR,MAAA,CAAAN,SAAA;MAAA;MAEAnD,OAAA,CAAAC,GAAA,gCAAAnB,MAAA,CAAA+E,SAAA,CAAApF,MAAA,0CAAAK,MAAA,CAAAkF,SAAA,CAAAvF,MAAA;;MAEA;MACA,IAAAoF,SAAA,CAAApF,MAAA,UAAAuF,SAAA,CAAAvF,MAAA,UAAA5B,IAAA,CAAA4B,MAAA;QACAuB,OAAA,CAAAC,GAAA;QACA,KAAA3C,kBAAA,QAAA4G,6BAAA,CAAArH,IAAA;QACA,KAAAU,kBAAA,QAAA4G,sBAAA,CAAAtH,IAAA;QACA,KAAAW,kBAAA;QACA,KAAAC,kBAAA;QACA;MACA;;MAEA;MACA,IAAAoG,SAAA,CAAApF,MAAA;QACA,KAAAX,0BAAA,QAAAoG,6BAAA,CAAAL,SAAA;QACA,KAAA9F,0BAAA,QAAAoG,sBAAA,CAAAN,SAAA;QACA,KAAAvG,kBAAA,OAAAgG,mBAAA,CAAA1G,OAAA,OAAAkB,0BAAA;QACA,KAAAP,kBAAA,OAAA+F,mBAAA,CAAA1G,OAAA,OAAAmB,0BAAA;MACA;QACA,KAAAD,0BAAA;QACA,KAAAC,0BAAA;QACA,KAAAT,kBAAA;QACA,KAAAC,kBAAA;MACA;MAEA,IAAAyG,SAAA,CAAAvF,MAAA;QACA,KAAAT,0BAAA,QAAAkG,6BAAA,CAAAF,SAAA;QACA,KAAA/F,0BAAA,QAAAkG,sBAAA,CAAAH,SAAA;QACA,KAAAxG,kBAAA,OAAA8F,mBAAA,CAAA1G,OAAA,OAAAoB,0BAAA;QACA,KAAAP,kBAAA,OAAA6F,mBAAA,CAAA1G,OAAA,OAAAqB,0BAAA;MACA;QACA,KAAAD,0BAAA;QACA,KAAAC,0BAAA;QACA,KAAAT,kBAAA;QACA,KAAAC,kBAAA;MACA;IACA;IAEA,gBACAsG,cAAA,WAAAA,eAAAZ,SAAA;MACA;MACA,OAAAA,SAAA,UAAAA,SAAA;IACA;IAEA,gBACAc,cAAA,WAAAA,eAAAd,SAAA;MACA;MACA,OAAAA,SAAA,UAAAA,SAAA;IACA;IAEA,iBACAe,6BAAA,WAAAA,8BAAArH,IAAA;MAAA,IAAAuH,MAAA;MACApE,OAAA,CAAAC,GAAA;;MAEA;MACA,IAAAoE,YAAA;MAEAxH,IAAA,CAAAyH,OAAA,WAAAb,MAAA;QACA,IAAAjC,QAAA,GAAAiC,MAAA,CAAAjC,QAAA;QACA,IAAA+C,MAAA,GAAAC,UAAA,CAAAf,MAAA,CAAAgB,KAAA;QAEA,KAAAJ,YAAA,CAAA7C,QAAA;UACA6C,YAAA,CAAA7C,QAAA,QAAAkD,GAAA;QACA;;QAEA;QACA;UACA,IAAAC,UAAA,GAAAC,IAAA,CAAAC,KAAA,CAAApB,MAAA,CAAAkB,UAAA;UACA,IAAAG,OAAA,GAAAH,UAAA,CAAAG,OAAA;;UAEA;UACAA,OAAA,CAAAR,OAAA,WAAAS,SAAA;YACA,IAAAC,mBAAA;YAEA,QAAAC,QAAA,CAAArI,OAAA,EAAAmI,SAAA,kBAAAA,SAAA;cACA;cACA,IAAAG,IAAA,GAAAC,MAAA,CAAAD,IAAA,CAAAH,SAAA,EAAAK,IAAA;cACA,IAAAC,MAAA,GAAAH,IAAA,CAAA1B,GAAA,WAAA8B,GAAA;gBAAA,OAAAC,MAAA,CAAAR,SAAA,CAAAO,GAAA;cAAA;cACAN,mBAAA,GAAAK,MAAA,CAAAG,IAAA;YACA;cACAR,mBAAA,GAAAO,MAAA,CAAAR,SAAA;YACA;;YAEA;YACA,IAAAV,YAAA,CAAA7C,QAAA,EAAAiE,GAAA,CAAAT,mBAAA;cACA,IAAAU,QAAA,GAAArB,YAAA,CAAA7C,QAAA,EAAAmE,GAAA,CAAAX,mBAAA;cACAU,QAAA,CAAAE,WAAA,IAAArB,MAAA;cACAmB,QAAA,CAAAG,WAAA;YACA;cACA;cACAxB,YAAA,CAAA7C,QAAA,EAAAsE,GAAA,CAAAd,mBAAA;gBACAL,UAAA,EAAAK,mBAAA;gBACAY,WAAA,EAAArB,MAAA;gBACAsB,WAAA;cACA;YACA;UACA;QACA,SAAAvE,KAAA;UACAtB,OAAA,CAAAG,IAAA,cAAAsD,MAAA,CAAAkB,UAAA,EAAArD,KAAA;UACA;UACA,IAAAyE,eAAA,GAAAtC,MAAA,CAAAkB,UAAA;UAEA,IAAAN,YAAA,CAAA7C,QAAA,EAAAiE,GAAA,CAAAM,eAAA;YACA,IAAAL,QAAA,GAAArB,YAAA,CAAA7C,QAAA,EAAAmE,GAAA,CAAAI,eAAA;YACAL,QAAA,CAAAE,WAAA,IAAArB,MAAA;YACAmB,QAAA,CAAAG,WAAA;UACA;YACAxB,YAAA,CAAA7C,QAAA,EAAAsE,GAAA,CAAAC,eAAA;cACApB,UAAA,EAAAoB,eAAA;cACAH,WAAA,EAAArB,MAAA;cACAsB,WAAA;YACA;UACA;QACA;MACA;MAEA7F,OAAA,CAAAC,GAAA;;MAEA;MACA,OAAAkF,MAAA,CAAAa,OAAA,CAAA3B,YAAA,EAAAb,GAAA,WAAAyC,KAAA;QAAA,IAAAC,KAAA,OAAAC,eAAA,CAAAvJ,OAAA,EAAAqJ,KAAA;UAAAzE,QAAA,GAAA0E,KAAA;UAAAE,UAAA,GAAAF,KAAA;QACA;QACA,IAAAG,OAAA,GAAAC,KAAA,CAAAC,IAAA,CAAAH,UAAA,CAAAf,MAAA,IACAD,IAAA,WAAA/E,CAAA,EAAAmG,CAAA;UAAA,OAAAA,CAAA,CAAAZ,WAAA,GAAAvF,CAAA,CAAAuF,WAAA;QAAA,GACApC,GAAA,WAAAiD,IAAA,EAAAC,KAAA;UAAA;YACA7E,IAAA,EAAA6E,KAAA;YACA/B,UAAA,EAAA8B,IAAA,CAAA9B,UAAA;YACAiB,WAAA,EAAAa,IAAA,CAAAb,WAAA;YACAC,WAAA,EAAAY,IAAA,CAAAZ;UACA;QAAA;QAEA7F,OAAA,CAAAC,GAAA,gDAAAnB,MAAA,CAAA0C,QAAA,OAAA1C,MAAA,CAAAsF,MAAA,CAAA1C,aAAA,CAAAxB,QAAA,CAAAsB,QAAA,eAAA1C,MAAA,CAAAuH,OAAA,CAAA5H,MAAA;QAEA;UACA+C,QAAA,EAAAtB,QAAA,CAAAsB,QAAA;UACAC,UAAA,EAAA2C,MAAA,CAAA1C,aAAA,CAAAxB,QAAA,CAAAsB,QAAA;UACA6E,OAAA,EAAAA;QACA;MACA,GAAAvC,MAAA,WAAA6C,KAAA;QAAA,OAAAA,KAAA,CAAAN,OAAA,CAAA5H,MAAA;MAAA;IACA;IACA,kBACAmI,0BAAA,WAAAA,2BAAAC,aAAA;MACA;QACA,IAAA/B,OAAA,GAAAF,IAAA,CAAAC,KAAA,CAAAgC,aAAA;QACA,IAAAP,KAAA,CAAAQ,OAAA,CAAAhC,OAAA,KAAAA,OAAA,CAAArG,MAAA;UACA,OAAAqG,OAAA,CAAAtB,GAAA,WAAAuD,GAAA;YACA,QAAA9B,QAAA,CAAArI,OAAA,EAAAmK,GAAA,kBAAAA,GAAA;cACA;cACA,IAAA7B,IAAA,GAAAC,MAAA,CAAAD,IAAA,CAAA6B,GAAA,EAAA3B,IAAA;cACA,IAAAC,MAAA,GAAAH,IAAA,CAAA1B,GAAA,WAAA8B,GAAA;gBAAA,OAAAC,MAAA,CAAAwB,GAAA,CAAAzB,GAAA;cAAA;cACA,OAAAD,MAAA,CAAAG,IAAA;YACA;YACA,OAAAD,MAAA,CAAAwB,GAAA;UACA,GAAAvB,IAAA;QACA;MACA,SAAAlE,KAAA;QACA;MAAA;MAEA,OAAAuF,aAAA,CAAApI,MAAA,QAAAoI,aAAA,CAAAG,SAAA,kBAAAH,aAAA;IACA;IACA,+BACA1C,sBAAA,WAAAA,uBAAAtH,IAAA;MAAA,IAAAoK,MAAA;MACA;MACA,IAAAjK,oBAAA;;MAEA;MACA,IAAAqH,YAAA;MAEAxH,IAAA,CAAAyH,OAAA,WAAAb,MAAA;QACA;UACA,IAAAjC,QAAA,GAAAiC,MAAA,CAAAjC,QAAA;UACA,IAAAiD,KAAA,GAAAD,UAAA,CAAAf,MAAA,CAAAgB,KAAA;UACA,IAAAE,UAAA,GAAAC,IAAA,CAAAC,KAAA,CAAApB,MAAA,CAAAkB,UAAA;UACA,IAAAG,OAAA,GAAAH,UAAA,CAAAG,OAAA;;UAEA;UACA,KAAAT,YAAA,CAAA7C,QAAA;YACA6C,YAAA,CAAA7C,QAAA;cACA0F,WAAA;cACAC,aAAA,MAAAzC,GAAA;YACA;UACA;UAEAL,YAAA,CAAA7C,QAAA,EAAA0F,WAAA,IAAAzC,KAAA;;UAEA;UACAK,OAAA,CAAAR,OAAA,WAAAS,SAAA;YACA,IAAAqC,WAAA,GAAAH,MAAA,CAAAI,wBAAA,CAAAtC,SAAA;;YAEA;YACA,IAAAuC,sBAAA,OAAA/D,GAAA;YAEA6D,WAAA,CAAA9C,OAAA,WAAAiD,KAAA;cACA;cACA,IAAAC,eAAA,GAAAP,MAAA,CAAAQ,oBAAA,CAAAF,KAAA;cACAD,sBAAA,CAAAI,GAAA,CAAAF,eAAA;YACA;;YAEA;YACAF,sBAAA,CAAAhD,OAAA,WAAAkD,eAAA;cACA,IAAAG,aAAA,GAAAtD,YAAA,CAAA7C,QAAA,EAAA2F,aAAA,CAAAxB,GAAA,CAAA6B,eAAA;cACAnD,YAAA,CAAA7C,QAAA,EAAA2F,aAAA,CAAArB,GAAA,CAAA0B,eAAA,EAAAG,aAAA,GAAAlD,KAAA;YACA;UACA;QAEA,SAAAnD,KAAA;UACAtB,OAAA,CAAAsB,KAAA,UAAAA,KAAA;QACA;MACA;;MAIA;;MAEA,KAAAtE,oBAAA;MAEAmI,MAAA,CAAAa,OAAA,CAAA3B,YAAA,EAAAC,OAAA,WAAAsD,KAAA;QAAA,IAAAC,KAAA,OAAA1B,eAAA,CAAAvJ,OAAA,EAAAgL,KAAA;UAAApG,QAAA,GAAAqG,KAAA;UAAAlB,KAAA,GAAAkB,KAAA;QACA;QACA,IAAAC,iBAAA,GAAAxB,KAAA,CAAAC,IAAA,CAAAI,KAAA,CAAAQ,aAAA,CAAAnB,OAAA,IACAZ,IAAA,WAAA2C,KAAA,EAAAC,KAAA;UAAA,IAAAC,KAAA,OAAA9B,eAAA,CAAAvJ,OAAA,EAAAmL,KAAA;YAAA1H,CAAA,GAAA4H,KAAA;UAAA,IAAAC,KAAA,OAAA/B,eAAA,CAAAvJ,OAAA,EAAAoL,KAAA;YAAAxB,CAAA,GAAA0B,KAAA;UAAA,OAAA1B,CAAA,GAAAnG,CAAA;QAAA;QAEA,IAAAgG,OAAA,GAAAyB,iBAAA,CAAAtE,GAAA,WAAA2E,KAAA,EAAAzB,KAAA;UAAA,IAAA0B,KAAA,OAAAjC,eAAA,CAAAvJ,OAAA,EAAAuL,KAAA;YAAAE,MAAA,GAAAD,KAAA;YAAA7D,MAAA,GAAA6D,KAAA;UAAA;YACAvG,IAAA,EAAA6E,KAAA;YACA2B,MAAA,EAAAA,MAAA;YACAC,KAAA;YACA1C,WAAA,EAAArB,MAAA;YACAgE,UAAA,EAAA5B,KAAA,CAAAO,WAAA,QAAA3C,MAAA,GAAAoC,KAAA,CAAAO,WAAA,QAAAsB,OAAA;UACA;QAAA;QAEA,IAAAnC,OAAA,CAAA5H,MAAA;UACAzB,oBAAA,CAAAyL,IAAA;YACAjH,QAAA,EAAAtB,QAAA,CAAAsB,QAAA;YACAC,UAAA,EAAAwF,MAAA,CAAAvF,aAAA,CAAAxB,QAAA,CAAAsB,QAAA;YACA6E,OAAA,EAAAA;UACA;QACA;MACA;MAEA,OAAArJ,oBAAA;IACA;IACA,yBACAqK,wBAAA,WAAAA,yBAAAtC,SAAA;MACA,IAAAD,OAAA;;MAEA;MACA,WAAAC,SAAA;QACA;QACA,IAAA2D,QAAA,GAAA3D,SAAA,CAAA4D,OAAA;QACA,KAAAC,8BAAA,CAAAF,QAAA,CAAAG,KAAA,MAAA/D,OAAA;MACA,eAAAG,QAAA,CAAArI,OAAA,EAAAmI,SAAA,kBAAAA,SAAA;QACA;QACA,IAAAG,IAAA,GAAAC,MAAA,CAAAD,IAAA,CAAAH,SAAA,EAAAK,IAAA;QACA,IAAA0D,MAAA,GAAA5D,IAAA,CAAA1B,GAAA,WAAA8B,GAAA;UAAA,OAAAC,MAAA,CAAAR,SAAA,CAAAO,GAAA;QAAA;;QAIA;QACA,KAAAsD,8BAAA,CAAAE,MAAA,EAAAhE,OAAA;MACA;MAEA,OAAAA,OAAA;IACA;IACA,mBACA8D,8BAAA,WAAAA,+BAAAE,MAAA,EAAAhE,OAAA;MACA,IAAAjF,CAAA,GAAAiJ,MAAA,CAAArK,MAAA;;MAEA;MACA,IAAAoB,CAAA;QACA;MACA;;MAEA;MACA,SAAAkJ,CAAA,MAAAA,CAAA,GAAAlJ,CAAA,MAAAkJ,CAAA;QACA,SAAAC,CAAA,GAAAD,CAAA,MAAAC,CAAA,GAAAnJ,CAAA,MAAAmJ,CAAA;UACA,SAAAC,CAAA,GAAAD,CAAA,MAAAC,CAAA,GAAApJ,CAAA,EAAAoJ,CAAA;YACA,IAAAC,UAAA,GAAAJ,MAAA,CAAAC,CAAA,IAAAD,MAAA,CAAAE,CAAA,IAAAF,MAAA,CAAAG,CAAA;YACA,cAAAE,IAAA,CAAAD,UAAA;cACApE,OAAA,CAAA2D,IAAA,CAAAS,UAAA;YACA;UACA;QACA;MACA;IACA;IAEA,oCACAzB,oBAAA,WAAAA,qBAAAyB,UAAA;MACA;MACA,OAAAA,UAAA,CAAAL,KAAA,KAAAzD,IAAA,GAAAI,IAAA;IACA;IAEA,aACA4D,iBAAA,WAAAA,kBAAAC,KAAA;MACA;MACA,KAAA3L,YAAA,GAAA2L,KAAA,CAAAV,OAAA;IACA;IAEA,WACAW,aAAA,WAAAA,cAAA;MACA,UAAA5L,YAAA,CAAA6L,IAAA;QACA,KAAArH,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,KAAAvE,aAAA;MACA,KAAAD,YAAA;MAEA;QACA;QACA,IAAA6L,aAAA,QAAAC,mBAAA,MAAA/L,YAAA;;QAEA;QACA,SAAAL,iBAAA;UACA,KAAAC,kBAAA,GAAAkM,aAAA,CAAAE,aAAA;UACA,KAAAnM,kBAAA,GAAAiM,aAAA,CAAAG,aAAA;QACA;UACA,KAAAnM,kBAAA,GAAAgM,aAAA,CAAAE,aAAA;UACA,KAAAjM,kBAAA,GAAA+L,aAAA,CAAAG,aAAA;QACA;;QAEA;QACA,KAAA9L,iBAAA,QAAA+L,0BAAA,CAAAJ,aAAA;QAEA,KAAAtH,QAAA,CAAA2H,OAAA,+CAAA/K,MAAA,MAAAjB,iBAAA;MACA,SAAAyD,KAAA;QACAtB,OAAA,CAAAsB,KAAA,UAAAA,KAAA;QACA,KAAAY,QAAA,CAAAZ,KAAA;MACA;QACA,KAAA1D,aAAA;MACA;IACA;IAEA,WACAkM,WAAA,WAAAA,YAAA;MACA,KAAApM,YAAA;MACA,KAAAC,YAAA;MACA,KAAAE,iBAAA;;MAEA;MACA,KAAAP,kBAAA,OAAAgG,mBAAA,CAAA1G,OAAA,OAAAkB,0BAAA;MACA,KAAAP,kBAAA,OAAA+F,mBAAA,CAAA1G,OAAA,OAAAmB,0BAAA;MACA,KAAAP,kBAAA,OAAA8F,mBAAA,CAAA1G,OAAA,OAAAoB,0BAAA;MACA,KAAAP,kBAAA,OAAA6F,mBAAA,CAAA1G,OAAA,OAAAqB,0BAAA;IACA;IAEA,wBACAwL,mBAAA,WAAAA,oBAAA/L,YAAA;MAAA,IAAAqM,MAAA;MACA,IAAAC,qBAAA,QAAA3M,iBAAA,eACA,KAAAS,0BAAA,GACA,KAAAE,0BAAA;MAEA,IAAAiM,qBAAA,QAAA5M,iBAAA,eACA,KAAAU,0BAAA,GACA,KAAAE,0BAAA;;MAEA;MACA,IAAAiM,qBAAA,GAAAF,qBAAA,CAAAxG,GAAA,WAAA2G,WAAA;QACA,IAAAC,eAAA,GAAAD,WAAA,CAAA9D,OAAA,CAAAvC,MAAA,WAAA2C,IAAA;UACA,OAAAsD,MAAA,CAAAM,aAAA,CAAA5D,IAAA,CAAA9B,UAAA,EAAAjH,YAAA;QACA;QAEA,WAAA4M,cAAA,CAAA1N,OAAA,MAAA0N,cAAA,CAAA1N,OAAA,MACAuN,WAAA;UACA9D,OAAA,EAAA+D;QAAA;MAEA,GAAAtG,MAAA,WAAAqG,WAAA;QAAA,OAAAA,WAAA,CAAA9D,OAAA,CAAA5H,MAAA;MAAA;;MAEA;MACA,IAAA8L,qBAAA,GAAAN,qBAAA,CAAAzG,GAAA,WAAA2G,WAAA;QACA,IAAAC,eAAA,GAAAD,WAAA,CAAA9D,OAAA,CAAAvC,MAAA,WAAA2C,IAAA;UACA,OAAAsD,MAAA,CAAAM,aAAA,CAAA5D,IAAA,CAAA4B,MAAA,EAAA3K,YAAA;QACA;QAEA,WAAA4M,cAAA,CAAA1N,OAAA,MAAA0N,cAAA,CAAA1N,OAAA,MACAuN,WAAA;UACA9D,OAAA,EAAA+D;QAAA;MAEA,GAAAtG,MAAA,WAAAqG,WAAA;QAAA,OAAAA,WAAA,CAAA9D,OAAA,CAAA5H,MAAA;MAAA;MAEA;QACAiL,aAAA,EAAAQ,qBAAA;QACAP,aAAA,EAAAY;MACA;IACA;IACA,WACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MAAA,WAAAnL,kBAAA,CAAA1C,OAAA,mBAAA2C,aAAA,CAAA3C,OAAA,IAAA4C,CAAA,UAAAkL,SAAA;QAAA,WAAAnL,aAAA,CAAA3C,OAAA,IAAA+C,CAAA,WAAAgL,SAAA;UAAA,kBAAAA,SAAA,CAAA9K,CAAA;YAAA;cAAA8K,SAAA,CAAA9K,CAAA;cAAA,OACA4K,MAAA,CAAArK,mBAAA;YAAA;cACAqK,MAAA,CAAAvI,QAAA,CAAA2H,OAAA;YAAA;cAAA,OAAAc,SAAA,CAAAtK,CAAA;UAAA;QAAA,GAAAqK,QAAA;MAAA;IACA;IACA,kBACAxL,wBAAA,WAAAA,yBAAA;MAAA,IAAA0L,MAAA;MACA5K,OAAA,CAAAC,GAAA;;MAEA;MACA,IAAA4K,SAAA,IACA;QAAAxK,CAAA;QAAAmG,CAAA;QAAAsE,CAAA;MAAA;MAAA;MACA;QAAAzK,CAAA;QAAAmG,CAAA;QAAAsE,CAAA;QAAAC,CAAA;MAAA;MAAA;MACA;QAAA1K,CAAA;QAAAmG,CAAA;QAAAsE,CAAA;QAAAC,CAAA;QAAAC,CAAA;QAAAC,CAAA;MAAA;MAAA;MACA;QAAA5K,CAAA;QAAAmG,CAAA;QAAAsE,CAAA;QAAAC,CAAA;QAAAC,CAAA;MAAA;MAAA,CACA;MAEAH,SAAA,CAAAvG,OAAA,WAAA4G,QAAA,EAAAxE,KAAA;QACA1G,OAAA,CAAAC,GAAA,+BAAAnB,MAAA,CAAA4H,KAAA,YAAAwE,QAAA;QACA,IAAA9D,WAAA,GAAAwD,MAAA,CAAAvD,wBAAA,CAAA6D,QAAA;QACAlL,OAAA,CAAAC,GAAA,cAAAmH,WAAA;;QAEA;QACA,IAAA+D,aAAA,OAAA5H,GAAA;QACA6D,WAAA,CAAA9C,OAAA,WAAAiD,KAAA;UACA,IAAA6D,UAAA,GAAAR,MAAA,CAAAnD,oBAAA,CAAAF,KAAA;UACA4D,aAAA,CAAAzD,GAAA,CAAA0D,UAAA;UACApL,OAAA,CAAAC,GAAA,IAAAnB,MAAA,CAAAyI,KAAA,UAAAzI,MAAA,CAAAsM,UAAA;QACA;QACApL,OAAA,CAAAC,GAAA,eAAAqG,KAAA,CAAAC,IAAA,CAAA4E,aAAA;MACA;MAEAnL,OAAA,CAAAC,GAAA;IACA;IACA,mBACAoL,qBAAA,WAAAA,sBAAAxL,CAAA,EAAAyL,CAAA;MACA,IAAAA,CAAA,GAAAzL,CAAA;MACA,IAAAyL,CAAA,UAAAA,CAAA,KAAAzL,CAAA;MAEA,IAAA0L,MAAA;MACA,SAAAxC,CAAA,MAAAA,CAAA,GAAAuC,CAAA,EAAAvC,CAAA;QACAwC,MAAA,GAAAA,MAAA,IAAA1L,CAAA,GAAAkJ,CAAA,KAAAA,CAAA;MACA;MACA,OAAAyC,IAAA,CAAAC,KAAA,CAAAF,MAAA;IACA;IACA;IACA;IACApM,YAAA,WAAAA,aAAA;MAAA,IAAAuM,MAAA;MAAA,WAAApM,kBAAA,CAAA1C,OAAA,mBAAA2C,aAAA,CAAA3C,OAAA,IAAA4C,CAAA,UAAAmM,SAAA;QAAA,IAAAC,YAAA,EAAAC,QAAA,EAAAC,cAAA,EAAAC,gBAAA,EAAAC,GAAA;QAAA,WAAAzM,aAAA,CAAA3C,OAAA,IAAA+C,CAAA,WAAAsM,SAAA;UAAA,kBAAAA,SAAA,CAAApM,CAAA;YAAA;cAAAoM,SAAA,CAAApL,CAAA;cAAAoL,SAAA,CAAApM,CAAA;cAAA,OAGA,IAAAiB,gBAAA;gBACAC,GAAA;gBACAC,MAAA;gBACAC,MAAA;kBAAAC,OAAA;kBAAAC,QAAA;gBAAA;cACA;YAAA;cAJAyK,YAAA,GAAAK,SAAA,CAAA7K,CAAA;cAAA,MAMA,CAAAwK,YAAA,KAAAA,YAAA,CAAAvK,IAAA;gBAAA4K,SAAA,CAAApM,CAAA;gBAAA;cAAA;cACA6L,MAAA,CAAAvO,QAAA;cAAA,OAAA8O,SAAA,CAAA5L,CAAA;YAAA;cAIAwL,QAAA,GAAAD,YAAA,CAAAvK,IAAA,CAAAyC,MAAA,WAAAoI,IAAA;gBAAA,OAAAA,IAAA,CAAAC,MAAA;cAAA;cAEA;cAAAF,SAAA,CAAApM,CAAA;cAAA,OACA,IAAAiB,gBAAA;gBACAC,GAAA;gBACAC,MAAA;gBACAC,MAAA;kBACAC,OAAA;kBACAC,QAAA;gBACA;cACA;YAAA;cAPA2K,cAAA,GAAAG,SAAA,CAAA7K,CAAA;cASA,IAAA0K,cAAA,IAAAA,cAAA,CAAAzK,IAAA;gBACA;gBACA0K,gBAAA;gBACAD,cAAA,CAAAzK,IAAA,CAAAiD,OAAA,WAAAb,MAAA;kBACA,IAAAjF,MAAA,GAAAiF,MAAA,CAAA/D,SAAA;kBACAqM,gBAAA,CAAAvN,MAAA,KAAAuN,gBAAA,CAAAvN,MAAA;gBACA;;gBAEA;gBACAkN,MAAA,CAAAvO,QAAA,GAAA0O,QAAA,CACA/H,MAAA,WAAAoI,IAAA;kBAAA,OAAAH,gBAAA,CAAAG,IAAA,CAAA1N,MAAA;gBAAA,GACAgF,GAAA,WAAA0I,IAAA;kBAAA,WAAA5B,cAAA,CAAA1N,OAAA,MAAA0N,cAAA,CAAA1N,OAAA,MACAsP,IAAA;oBACArG,WAAA,EAAAkG,gBAAA,CAAAG,IAAA,CAAA1N,MAAA;kBAAA;gBAAA,CACA,EACA4G,IAAA,WAAA/E,CAAA,EAAAmG,CAAA;kBAAA,OAAAA,CAAA,CAAAX,WAAA,GAAAxF,CAAA,CAAAwF,WAAA;gBAAA;;gBAEA7F,OAAA,CAAAC,GAAA,iEAAAnB,MAAA,CAAA4M,MAAA,CAAAvO,QAAA,CAAAsB,MAAA;cACA;gBACAiN,MAAA,CAAAvO,QAAA;gBACA6C,OAAA,CAAAG,IAAA;cACA;cAAA8L,SAAA,CAAApM,CAAA;cAAA;YAAA;cAAAoM,SAAA,CAAApL,CAAA;cAAAmL,GAAA,GAAAC,SAAA,CAAA7K,CAAA;cAGApB,OAAA,CAAAsB,KAAA,cAAA0K,GAAA;cACAN,MAAA,CAAAxJ,QAAA,CAAAZ,KAAA;cACAoK,MAAA,CAAAvO,QAAA;YAAA;cAAA,OAAA8O,SAAA,CAAA5L,CAAA;UAAA;QAAA,GAAAsL,QAAA;MAAA;IAEA;IAEA,kBACA5I,mBAAA,WAAAA,oBAAAvE,MAAA;MAAA,IAAA4N,OAAA;MAAA,WAAA9M,kBAAA,CAAA1C,OAAA,mBAAA2C,aAAA,CAAA3C,OAAA,IAAA4C,CAAA,UAAA6M,SAAA;QAAA,IAAA3L,QAAA,EAAA4L,GAAA;QAAA,WAAA/M,aAAA,CAAA3C,OAAA,IAAA+C,CAAA,WAAA4M,SAAA;UAAA,kBAAAA,SAAA,CAAA1M,CAAA;YAAA;cAAA0M,SAAA,CAAA1L,CAAA;cAEAuL,OAAA,CAAAhP,OAAA;cAAAmP,SAAA,CAAA1M,CAAA;cAAA,OACA,IAAAiB,gBAAA;gBACAC,GAAA;gBACAC,MAAA;gBACAC,MAAA;kBACAC,OAAA;kBACAC,QAAA;kBACAzB,SAAA,EAAAlB;gBACA;cACA;YAAA;cARAkC,QAAA,GAAA6L,SAAA,CAAAnL,CAAA;cAAA,MAUAV,QAAA,IAAAA,QAAA,CAAAW,IAAA;gBAAAkL,SAAA,CAAA1M,CAAA;gBAAA;cAAA;cAAA,OAAA0M,SAAA,CAAAlM,CAAA,IACAK,QAAA,CAAAW,IAAA;YAAA;cAAA,OAAAkL,SAAA,CAAAlM,CAAA,IAEA;YAAA;cAAAkM,SAAA,CAAA1M,CAAA;cAAA;YAAA;cAAA0M,SAAA,CAAA1L,CAAA;cAAAyL,GAAA,GAAAC,SAAA,CAAAnL,CAAA;cAGApB,OAAA,CAAAsB,KAAA,gBAAAgL,GAAA;cACAF,OAAA,CAAAlK,QAAA,CAAAZ,KAAA;cAAA,OAAAiL,SAAA,CAAAlM,CAAA,IACA;YAAA;cAAAkM,SAAA,CAAA1L,CAAA;cAEAuL,OAAA,CAAAhP,OAAA;cAAA,OAAAmP,SAAA,CAAAtB,CAAA;YAAA;cAAA,OAAAsB,SAAA,CAAAlM,CAAA;UAAA;QAAA,GAAAgM,QAAA;MAAA;IAEA;IAEA,eACAG,gBAAA,WAAAA,iBAAAhO,MAAA;MAAA,IAAAiO,OAAA;MAAA,WAAAnN,kBAAA,CAAA1C,OAAA,mBAAA2C,aAAA,CAAA3C,OAAA,IAAA4C,CAAA,UAAAkN,SAAA;QAAA,WAAAnN,aAAA,CAAA3C,OAAA,IAAA+C,CAAA,WAAAgN,SAAA;UAAA,kBAAAA,SAAA,CAAA9M,CAAA;YAAA;cACA;cACA4M,OAAA,CAAA1P,oBAAA;cACA0P,OAAA,CAAAzP,oBAAA;cAAA,KAEAwB,MAAA;gBAAAmO,SAAA,CAAA9M,CAAA;gBAAA;cAAA;cAAA8M,SAAA,CAAA9M,CAAA;cAAA,OAEA4M,OAAA,CAAArM,mBAAA;YAAA;cACAqM,OAAA,CAAAvK,QAAA,CAAA2H,OAAA,mCAAA/K,MAAA,CAAA2N,OAAA,CAAAG,kBAAA;YAAA;cAAA,OAAAD,SAAA,CAAAtM,CAAA;UAAA;QAAA,GAAAqM,QAAA;MAAA;IAEA;IAEA,kBACAE,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,OAAA;MACA,UAAA3P,cAAA;MACA,IAAAgP,IAAA,QAAA/O,QAAA,CAAAwE,IAAA,WAAAmL,CAAA;QAAA,OAAAA,CAAA,CAAAtO,MAAA,KAAAqO,OAAA,CAAA3P,cAAA;MAAA;MACA,OAAAgP,IAAA,GAAAA,IAAA,CAAAa,QAAA,IAAAb,IAAA,CAAAc,QAAA;IACA;IAEA,aACAC,uBAAA,WAAAA,wBAAAC,GAAA;MACAlN,OAAA,CAAAC,GAAA,oCAAAnB,MAAA,CAAAoO,GAAA,CAAA3Q,IAAA;MACA;IACA;IAEA,aACA4Q,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,OAAA;MAAA,WAAA9N,kBAAA,CAAA1C,OAAA,mBAAA2C,aAAA,CAAA3C,OAAA,IAAA4C,CAAA,UAAA6N,SAAA;QAAA,IAAAC,GAAA;QAAA,WAAA/N,aAAA,CAAA3C,OAAA,IAAA+C,CAAA,WAAA4N,SAAA;UAAA,kBAAAA,SAAA,CAAA1N,CAAA;YAAA;cAAA,MACAuN,OAAA,CAAAjP,OAAA,KAAAiP,OAAA,CAAAlQ,cAAA;gBAAAqQ,SAAA,CAAA1N,CAAA;gBAAA;cAAA;cACAuN,OAAA,CAAAlL,QAAA,CAAAC,OAAA;cAAA,OAAAoL,SAAA,CAAAlN,CAAA;YAAA;cAAAkN,SAAA,CAAA1M,CAAA;cAAA0M,SAAA,CAAA1N,CAAA;cAAA,OAKAuN,OAAA,CAAAhO,eAAA;YAAA;cAAAmO,SAAA,CAAA1N,CAAA;cAAA,OACAuN,OAAA,CAAAhN,mBAAA;YAAA;cACAgN,OAAA,CAAAlL,QAAA,CAAA2H,OAAA;cAAA0D,SAAA,CAAA1N,CAAA;cAAA;YAAA;cAAA0N,SAAA,CAAA1M,CAAA;cAAAyM,GAAA,GAAAC,SAAA,CAAAnM,CAAA;cAEApB,OAAA,CAAAsB,KAAA,YAAAgM,GAAA;cACAF,OAAA,CAAAlL,QAAA,CAAAZ,KAAA;YAAA;cAAA,OAAAiM,SAAA,CAAAlN,CAAA;UAAA;QAAA,GAAAgN,QAAA;MAAA;IAEA;IAEAG,KAAA,WAAAA,MAAA;MACA,KAAA1Q,aAAA;MACA;MACA,KAAAI,cAAA;MACA,KAAAG,iBAAA;MACA;MACA,KAAA+E,mBAAA;IACA;EACA;AACA", "ignoreList": []}]}