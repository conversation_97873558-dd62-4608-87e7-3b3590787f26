{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetStatistics.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetStatistics.vue", "mtime": 1756000107447}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\babel.config.js", "mtime": 1750852368688}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750942928175}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750942928768}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "name", "props", "visible", "type", "Boolean", "default", "data", "dialogVisible", "methodRankingByGroup", "numberRankingByGroup", "gameMethodsData", "selectedUserId", "userList", "loading", "activeLotteryType", "fucaiMethodRanking", "fucaiNumberRanking", "ticaiMethodRanking", "ticaiNumberRanking", "computed", "isAdmin", "userInfo", "$store", "getters", "roles", "userId", "length", "includes", "currentMethodRanking", "currentNumberRanking", "watch", "val", "_this", "testThreeDigitExtraction", "loadUserList", "loadGameMethods", "then", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "sysUserId", "w", "_context", "n", "sessionStorage", "getItem", "console", "log", "parseInt", "warn", "calculateStatistics", "a", "$emit", "methods", "_this2", "_callee2", "response", "_t", "_context2", "p", "request", "url", "method", "params", "pageNum", "pageSize", "v", "rows", "error", "getBasicMethodsData", "methodId", "methodName", "getMethodName", "find", "concat", "getRankClass", "rank", "_this3", "_callee3", "_context3", "getRecordData", "$message", "warning", "clearAllRankingData", "calculateStatisticsByLotteryType", "福彩投注排行", "福彩热门三位数", "体彩投注排行", "体彩热门三位数", "当前选中彩种", "_this4", "_callee4", "_t2", "_context4", "fetchUserRecordData", "_this5", "lotteryType", "gameType", "lotteryId", "gameName", "lotteryTypes", "_toConsumableArray2", "Set", "map", "record", "gameTypes", "lotteryIds", "gameNames", "fucaiData", "filter", "isFucaiLottery", "ticaiData", "isTicaiLottery", "calculateMethodRankingByGroup", "calculateNumberRanking", "_this6", "methodGroups", "for<PERSON>ach", "amount", "parseFloat", "money", "Map", "betNumbers", "JSON", "parse", "numbers", "numberObj", "singleNumberDisplay", "_typeof2", "keys", "Object", "sort", "values", "key", "String", "join", "has", "existing", "get", "totalAmount", "recordCount", "set", "fallback<PERSON><PERSON>bers", "entries", "_ref2", "_ref3", "_slicedToArray2", "numbersMap", "ranking", "Array", "from", "b", "item", "index", "group", "formatBetNumbersForDisplay", "betNumbersKey", "isArray", "num", "substring", "_this7", "methodTotal", "threeDigitMap", "threeDigits", "extractThreeDigitNumbers", "uniqueNormalizedDigits", "digit", "normalizedDigit", "normalizeThreeDigits", "add", "currentAmount", "_ref4", "_ref5", "sortedThreeDigits", "_ref6", "_ref7", "_ref8", "_ref9", "_ref0", "_ref1", "number", "count", "percentage", "toFixed", "push", "cleanStr", "replace", "generateThreeDigitCombinations", "split", "digits", "i", "j", "k", "threeDigit", "test", "refreshData", "_this8", "_callee5", "_context5", "success", "_this9", "testCases", "c", "d", "e", "f", "testCase", "normalizedSet", "normalized", "calculateCombinations", "r", "result", "Math", "round", "_this0", "_callee6", "userResponse", "allUsers", "recordResponse", "userRecordCounts", "_t3", "_context6", "user", "status", "_objectSpread2", "_this1", "_callee7", "_t4", "_context7", "handleUserChange", "_this10", "_callee8", "_context8", "getCurrentUserName", "_this11", "u", "nick<PERSON><PERSON>", "userName", "handleLotteryTypeChange", "tab", "refreshStatistics", "_this12", "_callee9", "_t5", "_context9", "close"], "sources": ["src/views/game/record/components/BetStatistics.vue"], "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      title=\"下注统计\"\n      :visible.sync=\"dialogVisible\"\n      width=\"95%\"\n      append-to-body\n      style=\"margin-top: -50px;\"\n      :close-on-click-modal=\"false\"\n      class=\"statistics-dialog\">\n\n      <!-- 管理员用户选择器 -->\n      <div v-if=\"isAdmin\" class=\"admin-controls\" style=\"margin-bottom: 20px; padding: 15px; background: #f5f7fa; border-radius: 4px;\">\n        <div style=\"display: flex; align-items: center; gap: 15px;\">\n          <span style=\"font-weight: 500; color: #606266;\">选择用户:</span>\n          <el-select\n            v-model=\"selectedUserId\"\n            placeholder=\"选择用户查看统计\"\n            @change=\"handleUserChange\"\n            clearable\n            style=\"width: 250px;\">\n            <el-option\n              v-for=\"user in userList\"\n              :key=\"user.userId\"\n              :label=\"`${user.nickName || user.userName} (ID: ${user.userId}) - ${user.recordCount}条记录`\"\n              :value=\"user.userId\">\n            </el-option>\n          </el-select>\n          <el-button\n            type=\"primary\"\n            size=\"small\"\n            @click=\"refreshStatistics\"\n            :loading=\"loading\">\n            <i class=\"el-icon-refresh\"></i> 刷新统计\n          </el-button>\n          <span v-if=\"selectedUserId\" style=\"color: #67c23a; font-size: 12px;\">\n            当前查看: {{ getCurrentUserName() }}\n          </span>\n        </div>\n      </div>\n\n      <!-- 彩种选择标签页 - 所有用户都可见 -->\n      <div class=\"lottery-type-tabs-container\" style=\"margin-bottom: 20px; text-align: center;\">\n        <el-tabs v-model=\"activeLotteryType\" @tab-click=\"handleLotteryTypeChange\" type=\"card\">\n          <el-tab-pane name=\"fucai\">\n            <span slot=\"label\">\n              <i class=\"el-icon-medal-1\"></i>\n              福彩3D\n            </span>\n          </el-tab-pane>\n          <el-tab-pane name=\"ticai\">\n            <span slot=\"label\">\n              <i class=\"el-icon-trophy-1\"></i>\n              体彩排三\n            </span>\n          </el-tab-pane>\n        </el-tabs>\n      </div>\n\n      <!-- 管理员未选择用户时的提示 -->\n      <div v-if=\"isAdmin && !selectedUserId\" class=\"admin-no-selection\" style=\"text-align: center; padding: 60px 20px; color: #909399;\">\n        <i class=\"el-icon-user\" style=\"font-size: 48px; margin-bottom: 20px; display: block;\"></i>\n        <h3 style=\"margin: 0 0 10px 0; color: #606266;\">请选择要查看统计的用户</h3>\n        <p style=\"margin: 0; font-size: 14px;\">选择用户后将自动加载该用户的下注统计数据</p>\n      </div>\n\n      <!-- 统计内容区域 -->\n      <div v-if=\"!isAdmin || (isAdmin && selectedUserId)\" class=\"statistics-content\">\n\n        <!-- 当前彩种无数据时的提示 -->\n        <div v-if=\"currentMethodRanking.length === 0 && currentNumberRanking.length === 0\"\n             class=\"no-data-tip\"\n             style=\"text-align: center; padding: 60px 20px; color: #909399;\">\n          <i class=\"el-icon-data-analysis\" style=\"font-size: 48px; margin-bottom: 20px; display: block;\"></i>\n          <h3 style=\"margin: 0 0 10px 0; color: #606266;\">\n            {{ activeLotteryType === 'fucai' ? '福彩3D' : '体彩排三' }} 暂无统计数据\n          </h3>\n          <p style=\"margin: 0; font-size: 14px;\">\n            {{ activeLotteryType === 'fucai' ? '切换到体彩排三查看数据' : '切换到福彩3D查看数据' }}\n          </p>\n        </div>\n          <!-- 按玩法分组的热门三位数排行 -->\n        <div v-if=\"currentNumberRanking.length > 0\" class=\"numbers-rankings-section\">\n          <h3 class=\"main-section-title\">\n            <i class=\"el-icon-data-line\"></i>\n            各玩法热门三位数排行榜\n          </h3>\n\n          <!-- 使用Grid布局，一行显示五个玩法 -->\n          <div class=\"numbers-grid\">\n            <div\n              v-for=\"methodGroup in currentNumberRanking\"\n              :key=\"methodGroup.methodId\"\n              class=\"numbers-card\">\n\n              <div class=\"numbers-card-header\">\n                <span class=\"method-name\">{{ methodGroup.methodName }}</span>\n                <span class=\"method-count\">共{{ methodGroup.ranking.length }}项</span>\n              </div>\n\n              <div class=\"numbers-ranking-list\">\n                <div\n                  v-for=\"item in methodGroup.ranking\"\n                  :key=\"item.rank\"\n                  class=\"numbers-ranking-item\">\n                  <div class=\"rank-badge\" :class=\"getRankClass(item.rank)\">\n                    {{ item.rank }}\n                  </div>\n                  <div class=\"numbers-info\">\n                    <div class=\"hot-number\">{{ item.number }}</div>\n                    <div class=\"amount\">￥{{ item.totalAmount.toFixed(0) }}</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      <div class=\"statistics-container\">\n        <!-- 按玩法分组的投注金额排行 - 两列布局 -->\n        <div v-if=\"currentMethodRanking.length > 0\" class=\"method-rankings-section\">\n          <h3 class=\"main-section-title\">\n            <i class=\"el-icon-trophy\"></i>\n            各玩法投注排行榜\n          </h3>\n\n          <!-- 使用Grid布局，一行显示两个玩法 -->\n          <div class=\"method-grid\">\n            <div\n              v-for=\"methodGroup in currentMethodRanking\"\n              :key=\"methodGroup.methodId\"\n              class=\"method-card\">\n\n              <div class=\"method-card-header\">\n                <span class=\"method-name\">{{ methodGroup.methodName }}</span>\n                <span class=\"method-count\">共{{ methodGroup.ranking.length }}项</span>\n              </div>\n\n              <div class=\"ranking-list\">\n                <div\n                  v-for=\"item in methodGroup.ranking\"\n                  :key=\"item.rank\"\n                  class=\"ranking-item\">\n                  <div class=\"rank-badge\" :class=\"getRankClass(item.rank)\">\n                    {{ item.rank }}\n                  </div>\n                  <div class=\"bet-info\">\n                    <div class=\"bet-numbers\">{{ item.betNumbers }}</div>\n                    <div class=\"amount\">￥{{ item.totalAmount.toFixed(0) }}</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n\n      </div>\n      <!-- 统计内容区域结束 -->\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"refreshData\" type=\"primary\" icon=\"el-icon-refresh\">刷新数据</el-button>\n        <el-button @click=\"close\">关闭</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport request from '@/utils/request'\n\nexport default {\n  name: 'BetStatistics',\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      dialogVisible: false,\n      methodRankingByGroup: [], // 按玩法分组的排行榜\n      numberRankingByGroup: [], // 按玩法分组的号码排行榜\n      gameMethodsData: [], // 玩法数据\n      // 管理员功能相关\n      selectedUserId: null, // 选中的用户ID\n      userList: [], // 用户列表\n      loading: false, // 加载状态\n      // 彩种相关\n      activeLotteryType: 'fucai', // 当前选中的彩种：fucai(福彩3D) 或 ticai(体彩P3)\n      // 分彩种的统计数据\n      fucaiMethodRanking: [], // 福彩投注排行\n      fucaiNumberRanking: [], // 福彩热门三位数排行\n      ticaiMethodRanking: [], // 体彩投注排行\n      ticaiNumberRanking: [] // 体彩热门三位数排行\n    }\n  },\n  computed: {\n    /** 是否是管理员 */\n    isAdmin() {\n      const userInfo = this.$store.getters.userInfo;\n      const roles = this.$store.getters.roles;\n\n      // 检查是否是超级管理员（用户ID为1）\n      if (userInfo && userInfo.userId === 1) {\n        return true;\n      }\n\n      // 检查是否有管理员角色\n      if (roles && roles.length > 0) {\n        return roles.includes('admin') || roles.includes('3d_admin');\n      }\n\n      return false;\n    },\n\n    /** 当前彩种的投注排行数据 */\n    currentMethodRanking() {\n      return this.activeLotteryType === 'fucai' ? this.fucaiMethodRanking : this.ticaiMethodRanking;\n    },\n\n    /** 当前彩种的热门三位数排行数据 */\n    currentNumberRanking() {\n      return this.activeLotteryType === 'fucai' ? this.fucaiNumberRanking : this.ticaiNumberRanking;\n    }\n  },\n  watch: {\n    visible(val) {\n      this.dialogVisible = val;\n      if (val) {\n        // 运行测试\n        this.testThreeDigitExtraction();\n\n        // 如果是管理员，只加载用户列表和玩法数据，不自动计算统计\n        if (this.isAdmin) {\n          this.loadUserList();\n          this.loadGameMethods();\n        } else {\n          // 普通用户自动加载统计\n          this.loadGameMethods().then(async () => {\n            // 普通用户从sessionStorage获取自己的数据\n            const sysUserId = sessionStorage.getItem('sysUserId');\n            console.log('[BetStatistics] sessionStorage中的sysUserId:', sysUserId);\n\n            if (sysUserId) {\n              this.selectedUserId = parseInt(sysUserId);\n              console.log('[BetStatistics] 设置selectedUserId:', this.selectedUserId);\n            } else {\n              console.warn('[BetStatistics] 无法从sessionStorage获取sysUserId');\n            }\n            await this.calculateStatistics();\n          });\n        }\n      }\n    },\n    dialogVisible(val) {\n      this.$emit('update:visible', val);\n    }\n  },\n  methods: {\n    /** 加载玩法数据 */\n    async loadGameMethods() {\n      try {\n        // 直接从API获取玩法数据\n        const response = await request({\n          url: '/game/odds/list',\n          method: 'get',\n          params: { pageNum: 1, pageSize: 1000 }\n        });\n\n        if (response && response.rows) {\n          this.gameMethodsData = response.rows;\n         \n        } else {\n          this.gameMethodsData = [];\n          console.warn('玩法数据为空');\n        }\n      } catch (error) {\n        console.error('获取玩法数据失败:', error);\n        // 如果API失败，使用基本的玩法映射\n        this.gameMethodsData = this.getBasicMethodsData();\n      }\n    },\n    /** 获取基本玩法数据（API失败时的备用方案） */\n    getBasicMethodsData() {\n      return [\n        { methodId: 1, methodName: '独胆' },\n        { methodId: 2, methodName: '一码定位' },\n        { methodId: 3, methodName: '两码组合' },\n        { methodId: 4, methodName: '两码对子' },\n        { methodId: 5, methodName: '三码直选' },\n        { methodId: 6, methodName: '三码组选' },\n        { methodId: 7, methodName: '三码组三' },\n        { methodId: 8, methodName: '四码组六' },\n        { methodId: 9, methodName: '四码组三' },\n        { methodId: 10, methodName: '五码组六' },\n        { methodId: 11, methodName: '五码组三' },\n        { methodId: 12, methodName: '六码组六' },\n        { methodId: 13, methodName: '六码组三' },\n        { methodId: 14, methodName: '七码组六' },\n        { methodId: 15, methodName: '七码组三' },\n        { methodId: 16, methodName: '八码组六' },\n        { methodId: 17, methodName: '八码组三' },\n        { methodId: 18, methodName: '九码组六' },\n        { methodId: 19, methodName: '九码组三' },\n        { methodId: 20, methodName: '包打组六' },\n        { methodId: 21, methodName: '7或20和值' },\n        { methodId: 22, methodName: '8或19和值' },\n        { methodId: 23, methodName: '9或18和值' },\n        { methodId: 24, methodName: '10或17和值' },\n        { methodId: 25, methodName: '11或16和值' },\n        { methodId: 26, methodName: '12或15和值' },\n        { methodId: 27, methodName: '13或14和值' },\n        { methodId: 28, methodName: '直选复式' },\n        { methodId: 29, methodName: '和值单双' },\n        { methodId: 30, methodName: '和值大小' },\n        { methodId: 31, methodName: '包打组三' },\n        { methodId: 100, methodName: '三码防对' }\n      ];\n    },\n    /** 获取玩法名称 */\n    getMethodName(methodId) {\n      const method = this.gameMethodsData.find(m => m.methodId === methodId);\n      return method ? method.methodName : `玩法${methodId}`;\n    },\n    /** 获取排名样式类 */\n    getRankClass(rank) {\n      if (rank === 1) return 'rank-first';\n      if (rank === 2) return 'rank-second';\n      if (rank === 3) return 'rank-third';\n      return 'rank-other';\n    },\n    /** 计算统计数据 */\n    async calculateStatistics() {\n      const data = await this.getRecordData();\n\n\n      if (!data || data.length === 0) {\n        if (this.isAdmin && !this.selectedUserId) {\n          this.$message.warning('请选择要查看统计的用户');\n        } else {\n          this.$message.warning('暂无统计数据，请先刷新record页面');\n        }\n        this.clearAllRankingData();\n        return;\n      }\n\n      // 按彩种分别计算统计数据\n      this.calculateStatisticsByLotteryType(data);\n\n      // 调试信息\n      console.log('统计计算完成:', {\n        福彩投注排行: this.fucaiMethodRanking.length,\n        福彩热门三位数: this.fucaiNumberRanking.length,\n        体彩投注排行: this.ticaiMethodRanking.length,\n        体彩热门三位数: this.ticaiNumberRanking.length,\n        当前选中彩种: this.activeLotteryType\n      });\n    },\n    /** 获取record数据 */\n    async getRecordData() {\n      try {\n        // 如果有选择的用户ID（管理员选择的用户或普通用户自己），直接从API获取数据\n        if (this.selectedUserId) {\n          return await this.fetchUserRecordData(this.selectedUserId);\n        }\n\n        // 如果是管理员但没有选择用户，提示选择用户\n        if (this.isAdmin) {\n          this.$message.warning('请选择要查看统计的用户');\n          return [];\n        }\n\n        // 普通用户：直接从API获取当前用户数据，从sessionStorage获取用户ID\n        console.log('[BetStatistics] 普通用户，直接从API获取数据');\n\n        // 从sessionStorage获取用户ID\n        const sysUserId = sessionStorage.getItem('sysUserId');\n        console.log('[BetStatistics] 从sessionStorage获取的sysUserId:', sysUserId);\n\n        if (sysUserId) {\n          console.log('[BetStatistics] 使用sysUserId调用API:', sysUserId);\n          const data = await this.fetchUserRecordData(parseInt(sysUserId));\n          console.log('[BetStatistics] API获取到的数据:', data);\n\n          if (data && data.length > 0) {\n            console.log(`[BetStatistics] 成功加载了 ${data.length} 条统计数据`);\n          } else {\n            console.warn('[BetStatistics] API返回空数据');\n          }\n\n          return data;\n        } else {\n          console.warn('[BetStatistics] 无法从sessionStorage获取sysUserId');\n          this.$message.warning('无法获取用户信息，请重新登录');\n          return [];\n        }\n      } catch (error) {\n        console.error('解析统计数据失败:', error);\n        return [];\n      }\n    },\n    /** 清空所有排行数据 */\n    clearAllRankingData() {\n      this.fucaiMethodRanking = [];\n      this.fucaiNumberRanking = [];\n      this.ticaiMethodRanking = [];\n      this.ticaiNumberRanking = [];\n    },\n\n    /** 按彩种分别计算统计数据 */\n    calculateStatisticsByLotteryType(data) {\n      // 调试：查看数据结构\n      if (data.length > 0) {\n        console.log('数据样本:', data[0]);\n        console.log('可能的彩种字段:', {\n          lotteryType: data[0].lotteryType,\n          gameType: data[0].gameType,\n          lotteryId: data[0].lotteryId,\n          gameName: data[0].gameName,\n          methodName: data[0].methodName\n        });\n\n        // 统计所有可能的彩种标识\n        const lotteryTypes = [...new Set(data.map(record => record.lotteryType))];\n        const gameTypes = [...new Set(data.map(record => record.gameType))];\n        const lotteryIds = [...new Set(data.map(record => record.lotteryId))];\n        const gameNames = [...new Set(data.map(record => record.gameName))];\n\n        console.log('数据中的彩种标识:', {\n          lotteryTypes,\n          gameTypes,\n          lotteryIds,\n          gameNames\n        });\n      }\n\n      // 按彩种分组数据（使用lotteryId字段）\n      const fucaiData = data.filter(record => this.isFucaiLottery(record.lotteryId));\n      const ticaiData = data.filter(record => this.isTicaiLottery(record.lotteryId));\n\n      console.log(`福彩3D数据: ${fucaiData.length}条, 体彩P3数据: ${ticaiData.length}条`);\n\n      // 如果没有明确的彩种区分，将所有数据归类为福彩3D（兼容性处理）\n      if (fucaiData.length === 0 && ticaiData.length === 0 && data.length > 0) {\n        console.log('未检测到彩种字段，将所有数据归类为福彩3D');\n        this.fucaiMethodRanking = this.calculateMethodRankingByGroup(data);\n        this.fucaiNumberRanking = this.calculateNumberRanking(data);\n        this.ticaiMethodRanking = [];\n        this.ticaiNumberRanking = [];\n        return;\n      }\n\n      // 分别计算福彩和体彩的统计数据\n      if (fucaiData.length > 0) {\n        this.fucaiMethodRanking = this.calculateMethodRankingByGroup(fucaiData);\n        this.fucaiNumberRanking = this.calculateNumberRanking(fucaiData);\n      } else {\n        this.fucaiMethodRanking = [];\n        this.fucaiNumberRanking = [];\n      }\n\n      if (ticaiData.length > 0) {\n        this.ticaiMethodRanking = this.calculateMethodRankingByGroup(ticaiData);\n        this.ticaiNumberRanking = this.calculateNumberRanking(ticaiData);\n      } else {\n        this.ticaiMethodRanking = [];\n        this.ticaiNumberRanking = [];\n      }\n    },\n\n    /** 判断是否为福彩3D */\n    isFucaiLottery(lotteryId) {\n      // 福彩3D的lotteryId是1\n      return lotteryId === 1 || lotteryId === '1';\n    },\n\n    /** 判断是否为体彩P3 */\n    isTicaiLottery(lotteryId) {\n      // 体彩P3的lotteryId是2\n      return lotteryId === 2 || lotteryId === '2';\n    },\n\n    /** 按玩法分组计算排行榜 */\n    calculateMethodRankingByGroup(data) {\n      console.log('[calculateMethodRankingByGroup] 开始计算各玩法投注排行榜');\n\n      // 按玩法分组，相同号码合并累计金额\n      const methodGroups = {};\n\n      data.forEach(record => {\n        const methodId = record.methodId;\n        const amount = parseFloat(record.money) || 0;\n\n        if (!methodGroups[methodId]) {\n          methodGroups[methodId] = new Map(); // 使用Map来存储号码和对应的累计数据\n        }\n\n        // 解析投注号码\n        try {\n          const betNumbers = JSON.parse(record.betNumbers || '{}');\n          const numbers = betNumbers.numbers || [];\n\n          // 为每个号码累计金额\n          numbers.forEach((numberObj) => {\n            let singleNumberDisplay = '';\n\n            if (typeof numberObj === 'object' && numberObj !== null) {\n              // 处理 {a:1, b:2, c:3, d:4, e:5} 格式\n              const keys = Object.keys(numberObj).sort();\n              const values = keys.map(key => String(numberObj[key]));\n              singleNumberDisplay = values.join('');\n            } else {\n              singleNumberDisplay = String(numberObj);\n            }\n\n            // 如果号码已存在，累计金额和记录数\n            if (methodGroups[methodId].has(singleNumberDisplay)) {\n              const existing = methodGroups[methodId].get(singleNumberDisplay);\n              existing.totalAmount += amount;\n              existing.recordCount += 1;\n            } else {\n              // 新号码，创建记录\n              methodGroups[methodId].set(singleNumberDisplay, {\n                betNumbers: singleNumberDisplay,\n                totalAmount: amount,\n                recordCount: 1\n              });\n            }\n          });\n        } catch (error) {\n          console.warn('解析投注号码失败:', record.betNumbers, error);\n          // 如果解析失败，使用原始数据\n          const fallbackNumbers = record.betNumbers || '未知号码';\n\n          if (methodGroups[methodId].has(fallbackNumbers)) {\n            const existing = methodGroups[methodId].get(fallbackNumbers);\n            existing.totalAmount += amount;\n            existing.recordCount += 1;\n          } else {\n            methodGroups[methodId].set(fallbackNumbers, {\n              betNumbers: fallbackNumbers,\n              totalAmount: amount,\n              recordCount: 1\n            });\n          }\n        }\n      });\n\n      console.log('[calculateMethodRankingByGroup] 开始生成排行榜');\n\n      // 为每个玩法生成排行榜并返回\n      return Object.entries(methodGroups).map(([methodId, numbersMap]) => {\n        // 将Map转换为数组并按总金额降序排序\n        const ranking = Array.from(numbersMap.values())\n          .sort((a, b) => b.totalAmount - a.totalAmount)\n          .map((item, index) => ({\n            rank: index + 1,\n            betNumbers: item.betNumbers,\n            totalAmount: item.totalAmount,\n            recordCount: item.recordCount\n          }));\n\n        console.log(`[calculateMethodRankingByGroup] 玩法${methodId}(${this.getMethodName(parseInt(methodId))})：${ranking.length}个不同号码`);\n\n        return {\n          methodId: parseInt(methodId),\n          methodName: this.getMethodName(parseInt(methodId)),\n          ranking\n        };\n      }).filter(group => group.ranking.length > 0); // 只显示有数据的玩法\n    },\n    /** 格式化投注号码用于显示 */\n    formatBetNumbersForDisplay(betNumbersKey) {\n      try {\n        const numbers = JSON.parse(betNumbersKey);\n        if (Array.isArray(numbers) && numbers.length > 0) {\n          return numbers.map(num => {\n            if (typeof num === 'object' && num !== null) {\n              // 处理 {a:1, b:2, c:3, d:4, e:5} 格式\n              const keys = Object.keys(num).sort();\n              const values = keys.map(key => String(num[key]));\n              return values.join('');\n            }\n            return String(num);\n          }).join(', ');\n        }\n      } catch (error) {\n        // 如果解析失败，直接返回原始字符串\n      }\n      return betNumbersKey.length > 20 ? betNumbersKey.substring(0, 20) + '...' : betNumbersKey;\n    },\n    /** 按玩法分组计算号码排行 - 只使用money字段 */\n    calculateNumberRanking(data) {\n      // 初始化返回数组\n      const numberRankingByGroup = [];\n\n      // 按玩法分组统计\n      const methodGroups = {};\n\n      data.forEach(record => {\n        try {\n          const methodId = record.methodId;\n          const money = parseFloat(record.money || 0);\n          const betNumbers = JSON.parse(record.betNumbers || '{}');\n          const numbers = betNumbers.numbers || [];\n\n          // 初始化玩法分组\n          if (!methodGroups[methodId]) {\n            methodGroups[methodId] = {\n              methodTotal: 0,\n              threeDigitMap: new Map()\n            };\n          }\n\n          methodGroups[methodId].methodTotal += money;\n\n          // 处理每个投注号码\n          numbers.forEach(numberObj => {\n            const threeDigits = this.extractThreeDigitNumbers(numberObj);\n\n            // 对当前号码的三位数组合去重（避免同一号码内重复组合多次累计）\n            const uniqueNormalizedDigits = new Set();\n\n            threeDigits.forEach(digit => {\n              // 归一化三位数：将数字按从小到大排序作为统一key\n              const normalizedDigit = this.normalizeThreeDigits(digit);\n              uniqueNormalizedDigits.add(normalizedDigit);\n            });\n\n            // 每个归一化后的三位数累加金额\n            uniqueNormalizedDigits.forEach(normalizedDigit => {\n              const currentAmount = methodGroups[methodId].threeDigitMap.get(normalizedDigit) || 0;\n              methodGroups[methodId].threeDigitMap.set(normalizedDigit, currentAmount + money);\n            });\n          });\n\n        } catch (error) {\n          console.error('解析失败:', error);\n        }\n      });\n\n\n\n      // 生成排行榜\n   \n      this.numberRankingByGroup = [];\n\n      Object.entries(methodGroups).forEach(([methodId, group]) => {\n        // 转换Map为数组并排序，显示所有数据\n        const sortedThreeDigits = Array.from(group.threeDigitMap.entries())\n          .sort(([,a], [,b]) => b - a);\n\n        const ranking = sortedThreeDigits.map(([number, amount], index) => ({\n          rank: index + 1,\n          number,\n          count: 1,\n          totalAmount: amount,\n          percentage: group.methodTotal > 0 ? ((amount / group.methodTotal) * 100).toFixed(1) : '0.0'\n        }));\n\n        if (ranking.length > 0) {\n          numberRankingByGroup.push({\n            methodId: parseInt(methodId),\n            methodName: this.getMethodName(parseInt(methodId)),\n            ranking\n          });\n        }\n      });\n\n      return numberRankingByGroup;\n    },\n    /** 从投注号码中提取所有可能的三位数组合 */\n    extractThreeDigitNumbers(numberObj) {\n      const numbers = [];\n\n      // 处理不同的号码格式\n      if (typeof numberObj === 'string') {\n        // 直接是字符串格式的号码\n        const cleanStr = numberObj.replace(/\\D/g, ''); // 移除非数字字符\n        this.generateThreeDigitCombinations(cleanStr.split(''), numbers);\n      } else if (typeof numberObj === 'object' && numberObj !== null) {\n        // 对象格式 {a: 1, b: 2, c: 3, d: 4, e: 5}\n        const keys = Object.keys(numberObj).sort();\n        const digits = keys.map(key => String(numberObj[key]));\n\n    \n\n        // 生成所有可能的三位数组合\n        this.generateThreeDigitCombinations(digits, numbers);\n      }\n\n      return numbers;\n    },\n    /** 生成所有可能的三位数组合 */\n    generateThreeDigitCombinations(digits, numbers) {\n      const n = digits.length;\n\n      // 如果数字少于3个，无法组成三位数\n      if (n < 3) {\n        return;\n      }\n\n      // 生成所有可能的三位数组合（C(n,3)）\n      for (let i = 0; i < n - 2; i++) {\n        for (let j = i + 1; j < n - 1; j++) {\n          for (let k = j + 1; k < n; k++) {\n            const threeDigit = digits[i] + digits[j] + digits[k];\n            if (/^\\d{3}$/.test(threeDigit)) {\n              numbers.push(threeDigit);\n            }\n          }\n        }\n      }\n    },\n\n    /** 归一化三位数：将数字按从小到大排序，统一相同数字的不同排列 */\n    normalizeThreeDigits(threeDigit) {\n      // 将三位数字符串转换为数组，排序后重新组合\n      return threeDigit.split('').sort().join('');\n    },\n    /** 刷新数据 */\n    async refreshData() {\n      await this.calculateStatistics();\n      this.$message.success('数据已刷新');\n    },\n    /** 测试三位数组合提取逻辑 */\n    testThreeDigitExtraction() {\n      console.log('=== 测试三位数组合提取和归一化逻辑 ===');\n\n      // 测试不同长度的号码\n      const testCases = [\n        {a: 1, b: 2, c: 3},                    // 三位数\n        {a: 1, b: 2, c: 3, d: 4},             // 四位数\n        {a: 0, b: 1, c: 2, d: 3, e: 4, f: 5}, // 六位数\n        {a: 1, b: 2, c: 3, d: 4, e: 5},       // 五位数\n      ];\n\n      testCases.forEach((testCase, index) => {\n        console.log(`\\n测试用例 ${index + 1}:`, testCase);\n        const threeDigits = this.extractThreeDigitNumbers(testCase);\n        console.log('提取的三位数组合:', threeDigits);\n\n        // 测试归一化效果\n        const normalizedSet = new Set();\n        threeDigits.forEach(digit => {\n          const normalized = this.normalizeThreeDigits(digit);\n          normalizedSet.add(normalized);\n          console.log(`${digit} -> ${normalized}`);\n        });\n        console.log('归一化后的唯一组合:', Array.from(normalizedSet));\n      });\n\n      console.log('=== 测试完成 ===');\n    },\n    /** 计算组合数 C(n,r) */\n    calculateCombinations(n, r) {\n      if (r > n) return 0;\n      if (r === 0 || r === n) return 1;\n\n      let result = 1;\n      for (let i = 0; i < r; i++) {\n        result = result * (n - i) / (i + 1);\n      }\n      return Math.round(result);\n    },\n    /** 关闭对话框 */\n    /** 加载用户列表 */\n    async loadUserList() {\n      try {\n        // 先获取所有用户\n        const userResponse = await request({\n          url: '/system/user/list',\n          method: 'get',\n          params: { pageNum: 1, pageSize: 1000 }\n        });\n\n        if (!userResponse || !userResponse.rows) {\n          this.userList = [];\n          return;\n        }\n\n        const allUsers = userResponse.rows.filter(user => user.status === '0'); // 只获取正常状态的用户\n\n        // 获取有下注记录的用户ID列表\n        const recordResponse = await request({\n          url: '/game/record/list',\n          method: 'get',\n          params: {\n            pageNum: 1,\n            pageSize: 100000 // 获取所有记录来统计用户\n          }\n        });\n\n        if (recordResponse && recordResponse.rows) {\n          // 统计每个用户的下注记录数量\n          const userRecordCounts = {};\n          recordResponse.rows.forEach(record => {\n            const userId = record.sysUserId;\n            userRecordCounts[userId] = (userRecordCounts[userId] || 0) + 1;\n          });\n\n          // 只保留有下注记录的用户，并添加记录数量信息\n          this.userList = allUsers\n            .filter(user => userRecordCounts[user.userId])\n            .map(user => ({\n              ...user,\n              recordCount: userRecordCounts[user.userId] || 0\n            }))\n            .sort((a, b) => b.recordCount - a.recordCount); // 按下注记录数量降序排列\n\n          console.log(`加载用户列表完成，共 ${this.userList.length} 个有下注记录的用户`);\n        } else {\n          this.userList = [];\n          console.warn('未获取到下注记录数据');\n        }\n\n      } catch (error) {\n        console.error('获取用户列表失败:', error);\n        this.$message.error('获取用户列表失败');\n        this.userList = [];\n      }\n    },\n\n    /** 获取指定用户的记录数据 */\n    async fetchUserRecordData(userId) {\n      try {\n        this.loading = true;\n        const response = await request({\n          url: '/game/record/list',\n          method: 'get',\n          params: {\n            pageNum: 1,\n            pageSize: 100000,\n            sysUserId: userId\n          }\n        });\n\n        if (response && response.rows) {\n          return response.rows;\n        } else {\n          return [];\n        }\n      } catch (error) {\n        console.error('获取用户记录数据失败:', error);\n        this.$message.error('获取用户记录数据失败');\n        return [];\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    /** 用户选择变化处理 */\n    async handleUserChange(userId) {\n      // 清空当前统计数据\n      this.methodRankingByGroup = [];\n      this.numberRankingByGroup = [];\n\n      if (userId) {\n        // 直接加载选中用户的统计数据\n        await this.calculateStatistics();\n        this.$message.success(`已加载用户 ${this.getCurrentUserName()} 的统计数据`);\n      }\n    },\n\n    /** 获取当前选中用户的名称 */\n    getCurrentUserName() {\n      if (!this.selectedUserId) return '';\n      const user = this.userList.find(u => u.userId === this.selectedUserId);\n      return user ? (user.nickName || user.userName) : '';\n    },\n\n    /** 彩种切换处理 */\n    handleLotteryTypeChange(tab) {\n      console.log(`切换到彩种: ${tab.name === 'fucai' ? '福彩3D' : '体彩P3'}`);\n      // 切换彩种时，计算属性会自动更新显示的数据\n    },\n\n    /** 刷新统计数据 */\n    async refreshStatistics() {\n      if (this.isAdmin && !this.selectedUserId) {\n        this.$message.warning('请先选择要查看统计的用户');\n        return;\n      }\n\n      try {\n        await this.loadGameMethods();\n        await this.calculateStatistics();\n        this.$message.success('统计数据已刷新');\n      } catch (error) {\n        console.error('刷新统计失败:', error);\n        this.$message.error('刷新统计失败');\n      }\n    },\n\n    close() {\n      this.dialogVisible = false;\n      // 清空选择状态\n      this.selectedUserId = null;\n      this.activeLotteryType = 'fucai'; // 重置为福彩\n      // 清空所有统计数据\n      this.clearAllRankingData();\n    }\n  }\n}\n</script>\n\n<style scoped>\n/* 管理员控制区域样式 */\n.admin-controls {\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n}\n\n.admin-controls .el-select {\n  background: white;\n}\n\n/* 彩种标签页样式 */\n.lottery-type-tabs {\n  margin-left: 20px;\n  flex: 1;\n}\n\n.lottery-type-tabs-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header {\n  border-bottom: none;\n  margin: 0;\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__nav,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__nav {\n  border: none;\n  border-radius: 8px;\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n  padding: 4px;\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__item,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__item {\n  border: none;\n  background: transparent;\n  color: #606266;\n  font-weight: 500;\n  padding: 8px 16px;\n  margin-right: 4px;\n  border-radius: 6px;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__item:hover,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__item:hover {\n  color: #409eff;\n  background: rgba(64, 158, 255, 0.1);\n  transform: translateY(-1px);\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__item.is-active,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__item.is-active {\n  color: white;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  font-weight: 600;\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n  transform: translateY(-1px);\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__item.is-active::before,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__item.is-active::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, transparent 100%);\n  pointer-events: none;\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__item i,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__item i {\n  margin-right: 6px;\n  font-size: 16px;\n}\n\n.lottery-type-tabs .el-tabs__content,\n.lottery-type-tabs-container .el-tabs__content {\n  display: none; /* 隐藏内容区域，因为我们只用标签页切换 */\n}\n\n.statistics-dialog {\n  .el-dialog {\n    border-radius: 12px;\n  }\n  \n  .el-dialog__header {\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    color: white;\n    padding: 20px 24px;\n  }\n  \n  .el-dialog__title {\n    color: white;\n    font-weight: 600;\n    font-size: 18px;\n  }\n}\n\n.statistics-container {\n  padding: 10px 0;\n}\n\n.main-section-title {\n  display: flex;\n  align-items: center;\n  font-size: 16px;\n  font-weight: 600;\n  color: #2c3e50;\n  margin-bottom: 8px;\n  padding-bottom: 4px;\n  border-bottom: 1px solid #ecf0f1;\n}\n\n.main-section-title i {\n  margin-right: 4px;\n  color: #667eea;\n  font-size: 14px;\n}\n\n/* 玩法排行榜样式 */\n.method-rankings-section {\n  margin-bottom: 15px;\n}\n\n.method-grid {\n  display: grid;\n  grid-template-columns: repeat(5, 1fr);\n  gap: 6px;\n}\n\n@media (max-width: 1400px) {\n  .method-grid {\n    grid-template-columns: repeat(4, 1fr);\n  }\n}\n\n@media (max-width: 1100px) {\n  .method-grid {\n    grid-template-columns: repeat(3, 1fr);\n  }\n}\n\n@media (max-width: 800px) {\n  .method-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n\n@media (max-width: 500px) {\n  .method-grid {\n    grid-template-columns: 1fr;\n  }\n}\n\n.method-card {\n  background: white;\n  border-radius: 10px;\n  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.12);\n  overflow: hidden;\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n  min-width: 0; /* 防止内容溢出 */\n  min-height: 200px; /* 最小高度 */\n  max-height: 500px; /* 增加最大高度以适应更多数据 */\n}\n\n.method-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.18);\n}\n\n.method-card-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 8px 12px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.method-name {\n  font-size: 15px;\n  font-weight: 700;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.method-count {\n  font-size: 13px;\n  opacity: 0.9;\n}\n\n.ranking-list {\n  padding: 8px;\n  max-height: 420px; /* 增加列表高度以适应更多数据 */\n  overflow-y: auto; /* 添加垂直滚动条 */\n}\n\n/* 滚动条样式 */\n.ranking-list::-webkit-scrollbar {\n  width: 4px;\n}\n\n.ranking-list::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 2px;\n}\n\n.ranking-list::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 2px;\n}\n\n.ranking-list::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n/* 热门三位数排行榜滚动条样式 */\n.numbers-ranking-list::-webkit-scrollbar {\n  width: 4px;\n}\n\n.numbers-ranking-list::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 2px;\n}\n\n.numbers-ranking-list::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 2px;\n}\n\n.numbers-ranking-list::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n.ranking-item {\n  display: flex;\n  align-items: center;\n  padding: 4px 8px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.ranking-item:last-child {\n  border-bottom: none;\n}\n\n.rank-badge {\n  min-width: 18px;\n  height: 18px;\n  padding: 0 4px;\n  border-radius: 9px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  font-size: 10px;\n  color: white;\n  flex: 0 0 auto;\n  margin-right: 8px;\n  white-space: nowrap;\n}\n\n.rank-first {\n  background: linear-gradient(135deg, #ff6b6b, #ee5a24);\n}\n\n.rank-second {\n  background: linear-gradient(135deg, #feca57, #ff9ff3);\n}\n\n.rank-third {\n  background: linear-gradient(135deg, #48dbfb, #0abde3);\n}\n\n.rank-other {\n  background: linear-gradient(135deg, #a4b0be, #747d8c);\n}\n\n.bet-info {\n  display: flex;\n  align-items: center;\n  flex: 1;\n  min-width: 0;\n}\n\n.bet-numbers {\n  font-size: 15px;\n  font-weight: 900;\n  color: #ffffff;\n  font-family: 'Courier New', monospace;\n  background: linear-gradient(135deg, #667eea, #764ba2);\n  padding: 3px 8px;\n  border-radius: 5px;\n  text-align: center;\n  box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);\n  letter-spacing: 0.5px;\n  flex: 2;\n  margin-right: 8px;\n}\n\n.amount {\n  color: #e74c3c;\n  font-weight: 700;\n  font-size: 14px;\n  white-space: nowrap;\n  flex: 1;\n  text-align: right;\n}\n\n.record-count {\n  color: #95a5a6;\n  font-size: 11px;\n  text-align: right;\n  margin-top: 2px;\n}\n\n/* 热门三位数样式 */\n.numbers-section {\n  margin-bottom: 30px;\n}\n\n.ranking-table {\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.amount-text {\n  font-weight: 600;\n  color: #e74c3c;\n}\n\n.percentage-text {\n  font-weight: 500;\n  color: #3498db;\n}\n\n/* 热门三位数卡片样式 */\n.numbers-rankings-section {\n  margin-bottom: 15px;\n}\n\n.numbers-grid {\n  display: grid;\n  grid-template-columns: repeat(5, 1fr);\n  gap: 6px;\n}\n\n@media (max-width: 1400px) {\n  .numbers-grid {\n    grid-template-columns: repeat(4, 1fr);\n  }\n}\n\n@media (max-width: 1100px) {\n  .numbers-grid {\n    grid-template-columns: repeat(3, 1fr);\n  }\n}\n\n@media (max-width: 800px) {\n  .numbers-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n\n@media (max-width: 500px) {\n  .numbers-grid {\n    grid-template-columns: 1fr;\n  }\n}\n\n.numbers-card {\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n  min-width: 0;\n  min-height: 120px;\n  max-height: 400px; /* 增加最大高度以适应更多数据 */\n}\n\n.numbers-card:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.numbers-card-header {\n  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);\n  color: white;\n  padding: 4px 6px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.numbers-ranking-list {\n  padding: 4px;\n  max-height: 320px; /* 限制列表高度以适应更多数据 */\n  overflow-y: auto; /* 添加垂直滚动条 */\n}\n\n.numbers-ranking-item {\n  display: flex;\n  align-items: center;\n  padding: 2px 4px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.numbers-ranking-item:last-child {\n  border-bottom: none;\n}\n\n.numbers-info {\n  display: flex;\n  align-items: center;\n  flex: 1;\n  min-width: 0;\n}\n\n.numbers-info .hot-number {\n  font-family: 'Courier New', monospace;\n  font-weight: 900;\n  font-size: 13px;\n  color: #ffffff;\n  background: linear-gradient(135deg, #27ae60, #2ecc71);\n  padding: 2px 4px;\n  border-radius: 3px;\n  text-align: center;\n  flex: 2;\n  margin-right: 4px;\n}\n\n.numbers-info .amount {\n  color: #e74c3c;\n  font-weight: 700;\n  font-size: 13px;\n  white-space: nowrap;\n  flex: 1;\n  text-align: right;\n}\n\n\n\n.dialog-footer {\n  text-align: right;\n  padding-top: 20px;\n  border-top: 1px solid #ecf0f1;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyKA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,oBAAA;MAAA;MACAC,oBAAA;MAAA;MACAC,eAAA;MAAA;MACA;MACAC,cAAA;MAAA;MACAC,QAAA;MAAA;MACAC,OAAA;MAAA;MACA;MACAC,iBAAA;MAAA;MACA;MACAC,kBAAA;MAAA;MACAC,kBAAA;MAAA;MACAC,kBAAA;MAAA;MACAC,kBAAA;IACA;EACA;EACAC,QAAA;IACA,aACAC,OAAA,WAAAA,QAAA;MACA,IAAAC,QAAA,QAAAC,MAAA,CAAAC,OAAA,CAAAF,QAAA;MACA,IAAAG,KAAA,QAAAF,MAAA,CAAAC,OAAA,CAAAC,KAAA;;MAEA;MACA,IAAAH,QAAA,IAAAA,QAAA,CAAAI,MAAA;QACA;MACA;;MAEA;MACA,IAAAD,KAAA,IAAAA,KAAA,CAAAE,MAAA;QACA,OAAAF,KAAA,CAAAG,QAAA,aAAAH,KAAA,CAAAG,QAAA;MACA;MAEA;IACA;IAEA,kBACAC,oBAAA,WAAAA,qBAAA;MACA,YAAAd,iBAAA,oBAAAC,kBAAA,QAAAE,kBAAA;IACA;IAEA,qBACAY,oBAAA,WAAAA,qBAAA;MACA,YAAAf,iBAAA,oBAAAE,kBAAA,QAAAE,kBAAA;IACA;EACA;EACAY,KAAA;IACA5B,OAAA,WAAAA,QAAA6B,GAAA;MAAA,IAAAC,KAAA;MACA,KAAAzB,aAAA,GAAAwB,GAAA;MACA,IAAAA,GAAA;QACA;QACA,KAAAE,wBAAA;;QAEA;QACA,SAAAb,OAAA;UACA,KAAAc,YAAA;UACA,KAAAC,eAAA;QACA;UACA;UACA,KAAAA,eAAA,GAAAC,IAAA,kBAAAC,kBAAA,CAAAhC,OAAA,mBAAAiC,aAAA,CAAAjC,OAAA,IAAAkC,CAAA,UAAAC,QAAA;YAAA,IAAAC,SAAA;YAAA,WAAAH,aAAA,CAAAjC,OAAA,IAAAqC,CAAA,WAAAC,QAAA;cAAA,kBAAAA,QAAA,CAAAC,CAAA;gBAAA;kBACA;kBACAH,SAAA,GAAAI,cAAA,CAAAC,OAAA;kBACAC,OAAA,CAAAC,GAAA,+CAAAP,SAAA;kBAEA,IAAAA,SAAA;oBACAT,KAAA,CAAArB,cAAA,GAAAsC,QAAA,CAAAR,SAAA;oBACAM,OAAA,CAAAC,GAAA,sCAAAhB,KAAA,CAAArB,cAAA;kBACA;oBACAoC,OAAA,CAAAG,IAAA;kBACA;kBAAAP,QAAA,CAAAC,CAAA;kBAAA,OACAZ,KAAA,CAAAmB,mBAAA;gBAAA;kBAAA,OAAAR,QAAA,CAAAS,CAAA;cAAA;YAAA,GAAAZ,OAAA;UAAA,CACA;QACA;MACA;IACA;IACAjC,aAAA,WAAAA,cAAAwB,GAAA;MACA,KAAAsB,KAAA,mBAAAtB,GAAA;IACA;EACA;EACAuB,OAAA;IACA,aACAnB,eAAA,WAAAA,gBAAA;MAAA,IAAAoB,MAAA;MAAA,WAAAlB,kBAAA,CAAAhC,OAAA,mBAAAiC,aAAA,CAAAjC,OAAA,IAAAkC,CAAA,UAAAiB,SAAA;QAAA,IAAAC,QAAA,EAAAC,EAAA;QAAA,WAAApB,aAAA,CAAAjC,OAAA,IAAAqC,CAAA,WAAAiB,SAAA;UAAA,kBAAAA,SAAA,CAAAf,CAAA;YAAA;cAAAe,SAAA,CAAAC,CAAA;cAAAD,SAAA,CAAAf,CAAA;cAAA,OAGA,IAAAiB,gBAAA;gBACAC,GAAA;gBACAC,MAAA;gBACAC,MAAA;kBAAAC,OAAA;kBAAAC,QAAA;gBAAA;cACA;YAAA;cAJAT,QAAA,GAAAE,SAAA,CAAAQ,CAAA;cAMA,IAAAV,QAAA,IAAAA,QAAA,CAAAW,IAAA;gBACAb,MAAA,CAAA7C,eAAA,GAAA+C,QAAA,CAAAW,IAAA;cAEA;gBACAb,MAAA,CAAA7C,eAAA;gBACAqC,OAAA,CAAAG,IAAA;cACA;cAAAS,SAAA,CAAAf,CAAA;cAAA;YAAA;cAAAe,SAAA,CAAAC,CAAA;cAAAF,EAAA,GAAAC,SAAA,CAAAQ,CAAA;cAEApB,OAAA,CAAAsB,KAAA,cAAAX,EAAA;cACA;cACAH,MAAA,CAAA7C,eAAA,GAAA6C,MAAA,CAAAe,mBAAA;YAAA;cAAA,OAAAX,SAAA,CAAAP,CAAA;UAAA;QAAA,GAAAI,QAAA;MAAA;IAEA;IACA,4BACAc,mBAAA,WAAAA,oBAAA;MACA,QACA;QAAAC,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,QAAA;QAAAC,UAAA;MAAA,EACA;IACA;IACA,aACAC,aAAA,WAAAA,cAAAF,QAAA;MACA,IAAAR,MAAA,QAAArD,eAAA,CAAAgE,IAAA,WAAAnC,CAAA;QAAA,OAAAA,CAAA,CAAAgC,QAAA,KAAAA,QAAA;MAAA;MACA,OAAAR,MAAA,GAAAA,MAAA,CAAAS,UAAA,kBAAAG,MAAA,CAAAJ,QAAA;IACA;IACA,cACAK,YAAA,WAAAA,aAAAC,IAAA;MACA,IAAAA,IAAA;MACA,IAAAA,IAAA;MACA,IAAAA,IAAA;MACA;IACA;IACA,aACA1B,mBAAA,WAAAA,oBAAA;MAAA,IAAA2B,MAAA;MAAA,WAAAzC,kBAAA,CAAAhC,OAAA,mBAAAiC,aAAA,CAAAjC,OAAA,IAAAkC,CAAA,UAAAwC,SAAA;QAAA,IAAAzE,IAAA;QAAA,WAAAgC,aAAA,CAAAjC,OAAA,IAAAqC,CAAA,WAAAsC,SAAA;UAAA,kBAAAA,SAAA,CAAApC,CAAA;YAAA;cAAAoC,SAAA,CAAApC,CAAA;cAAA,OACAkC,MAAA,CAAAG,aAAA;YAAA;cAAA3E,IAAA,GAAA0E,SAAA,CAAAb,CAAA;cAAA,MAGA,CAAA7D,IAAA,IAAAA,IAAA,CAAAoB,MAAA;gBAAAsD,SAAA,CAAApC,CAAA;gBAAA;cAAA;cACA,IAAAkC,MAAA,CAAA1D,OAAA,KAAA0D,MAAA,CAAAnE,cAAA;gBACAmE,MAAA,CAAAI,QAAA,CAAAC,OAAA;cACA;gBACAL,MAAA,CAAAI,QAAA,CAAAC,OAAA;cACA;cACAL,MAAA,CAAAM,mBAAA;cAAA,OAAAJ,SAAA,CAAA5B,CAAA;YAAA;cAIA;cACA0B,MAAA,CAAAO,gCAAA,CAAA/E,IAAA;;cAEA;cACAyC,OAAA,CAAAC,GAAA;gBACAsC,MAAA,EAAAR,MAAA,CAAA/D,kBAAA,CAAAW,MAAA;gBACA6D,OAAA,EAAAT,MAAA,CAAA9D,kBAAA,CAAAU,MAAA;gBACA8D,MAAA,EAAAV,MAAA,CAAA7D,kBAAA,CAAAS,MAAA;gBACA+D,OAAA,EAAAX,MAAA,CAAA5D,kBAAA,CAAAQ,MAAA;gBACAgE,MAAA,EAAAZ,MAAA,CAAAhE;cACA;YAAA;cAAA,OAAAkE,SAAA,CAAA5B,CAAA;UAAA;QAAA,GAAA2B,QAAA;MAAA;IACA;IACA,iBACAE,aAAA,WAAAA,cAAA;MAAA,IAAAU,MAAA;MAAA,WAAAtD,kBAAA,CAAAhC,OAAA,mBAAAiC,aAAA,CAAAjC,OAAA,IAAAkC,CAAA,UAAAqD,SAAA;QAAA,IAAAnD,SAAA,EAAAnC,IAAA,EAAAuF,GAAA;QAAA,WAAAvD,aAAA,CAAAjC,OAAA,IAAAqC,CAAA,WAAAoD,SAAA;UAAA,kBAAAA,SAAA,CAAAlD,CAAA;YAAA;cAAAkD,SAAA,CAAAlC,CAAA;cAAA,KAGA+B,MAAA,CAAAhF,cAAA;gBAAAmF,SAAA,CAAAlD,CAAA;gBAAA;cAAA;cAAAkD,SAAA,CAAAlD,CAAA;cAAA,OACA+C,MAAA,CAAAI,mBAAA,CAAAJ,MAAA,CAAAhF,cAAA;YAAA;cAAA,OAAAmF,SAAA,CAAA1C,CAAA,IAAA0C,SAAA,CAAA3B,CAAA;YAAA;cAAA,KAIAwB,MAAA,CAAAvE,OAAA;gBAAA0E,SAAA,CAAAlD,CAAA;gBAAA;cAAA;cACA+C,MAAA,CAAAT,QAAA,CAAAC,OAAA;cAAA,OAAAW,SAAA,CAAA1C,CAAA,IACA;YAAA;cAGA;cACAL,OAAA,CAAAC,GAAA;;cAEA;cACAP,SAAA,GAAAI,cAAA,CAAAC,OAAA;cACAC,OAAA,CAAAC,GAAA,iDAAAP,SAAA;cAAA,KAEAA,SAAA;gBAAAqD,SAAA,CAAAlD,CAAA;gBAAA;cAAA;cACAG,OAAA,CAAAC,GAAA,sCAAAP,SAAA;cAAAqD,SAAA,CAAAlD,CAAA;cAAA,OACA+C,MAAA,CAAAI,mBAAA,CAAA9C,QAAA,CAAAR,SAAA;YAAA;cAAAnC,IAAA,GAAAwF,SAAA,CAAA3B,CAAA;cACApB,OAAA,CAAAC,GAAA,+BAAA1C,IAAA;cAEA,IAAAA,IAAA,IAAAA,IAAA,CAAAoB,MAAA;gBACAqB,OAAA,CAAAC,GAAA,mDAAA2B,MAAA,CAAArE,IAAA,CAAAoB,MAAA;cACA;gBACAqB,OAAA,CAAAG,IAAA;cACA;cAAA,OAAA4C,SAAA,CAAA1C,CAAA,IAEA9C,IAAA;YAAA;cAEAyC,OAAA,CAAAG,IAAA;cACAyC,MAAA,CAAAT,QAAA,CAAAC,OAAA;cAAA,OAAAW,SAAA,CAAA1C,CAAA,IACA;YAAA;cAAA0C,SAAA,CAAAlD,CAAA;cAAA;YAAA;cAAAkD,SAAA,CAAAlC,CAAA;cAAAiC,GAAA,GAAAC,SAAA,CAAA3B,CAAA;cAGApB,OAAA,CAAAsB,KAAA,cAAAwB,GAAA;cAAA,OAAAC,SAAA,CAAA1C,CAAA,IACA;YAAA;cAAA,OAAA0C,SAAA,CAAA1C,CAAA;UAAA;QAAA,GAAAwC,QAAA;MAAA;IAEA;IACA,eACAR,mBAAA,WAAAA,oBAAA;MACA,KAAArE,kBAAA;MACA,KAAAC,kBAAA;MACA,KAAAC,kBAAA;MACA,KAAAC,kBAAA;IACA;IAEA,kBACAmE,gCAAA,WAAAA,iCAAA/E,IAAA;MAAA,IAAA0F,MAAA;MACA;MACA,IAAA1F,IAAA,CAAAoB,MAAA;QACAqB,OAAA,CAAAC,GAAA,UAAA1C,IAAA;QACAyC,OAAA,CAAAC,GAAA;UACAiD,WAAA,EAAA3F,IAAA,IAAA2F,WAAA;UACAC,QAAA,EAAA5F,IAAA,IAAA4F,QAAA;UACAC,SAAA,EAAA7F,IAAA,IAAA6F,SAAA;UACAC,QAAA,EAAA9F,IAAA,IAAA8F,QAAA;UACA5B,UAAA,EAAAlE,IAAA,IAAAkE;QACA;;QAEA;QACA,IAAA6B,YAAA,OAAAC,mBAAA,CAAAjG,OAAA,MAAAkG,GAAA,CAAAjG,IAAA,CAAAkG,GAAA,WAAAC,MAAA;UAAA,OAAAA,MAAA,CAAAR,WAAA;QAAA;QACA,IAAAS,SAAA,OAAAJ,mBAAA,CAAAjG,OAAA,MAAAkG,GAAA,CAAAjG,IAAA,CAAAkG,GAAA,WAAAC,MAAA;UAAA,OAAAA,MAAA,CAAAP,QAAA;QAAA;QACA,IAAAS,UAAA,OAAAL,mBAAA,CAAAjG,OAAA,MAAAkG,GAAA,CAAAjG,IAAA,CAAAkG,GAAA,WAAAC,MAAA;UAAA,OAAAA,MAAA,CAAAN,SAAA;QAAA;QACA,IAAAS,SAAA,OAAAN,mBAAA,CAAAjG,OAAA,MAAAkG,GAAA,CAAAjG,IAAA,CAAAkG,GAAA,WAAAC,MAAA;UAAA,OAAAA,MAAA,CAAAL,QAAA;QAAA;QAEArD,OAAA,CAAAC,GAAA;UACAqD,YAAA,EAAAA,YAAA;UACAK,SAAA,EAAAA,SAAA;UACAC,UAAA,EAAAA,UAAA;UACAC,SAAA,EAAAA;QACA;MACA;;MAEA;MACA,IAAAC,SAAA,GAAAvG,IAAA,CAAAwG,MAAA,WAAAL,MAAA;QAAA,OAAAT,MAAA,CAAAe,cAAA,CAAAN,MAAA,CAAAN,SAAA;MAAA;MACA,IAAAa,SAAA,GAAA1G,IAAA,CAAAwG,MAAA,WAAAL,MAAA;QAAA,OAAAT,MAAA,CAAAiB,cAAA,CAAAR,MAAA,CAAAN,SAAA;MAAA;MAEApD,OAAA,CAAAC,GAAA,gCAAA2B,MAAA,CAAAkC,SAAA,CAAAnF,MAAA,0CAAAiD,MAAA,CAAAqC,SAAA,CAAAtF,MAAA;;MAEA;MACA,IAAAmF,SAAA,CAAAnF,MAAA,UAAAsF,SAAA,CAAAtF,MAAA,UAAApB,IAAA,CAAAoB,MAAA;QACAqB,OAAA,CAAAC,GAAA;QACA,KAAAjC,kBAAA,QAAAmG,6BAAA,CAAA5G,IAAA;QACA,KAAAU,kBAAA,QAAAmG,sBAAA,CAAA7G,IAAA;QACA,KAAAW,kBAAA;QACA,KAAAC,kBAAA;QACA;MACA;;MAEA;MACA,IAAA2F,SAAA,CAAAnF,MAAA;QACA,KAAAX,kBAAA,QAAAmG,6BAAA,CAAAL,SAAA;QACA,KAAA7F,kBAAA,QAAAmG,sBAAA,CAAAN,SAAA;MACA;QACA,KAAA9F,kBAAA;QACA,KAAAC,kBAAA;MACA;MAEA,IAAAgG,SAAA,CAAAtF,MAAA;QACA,KAAAT,kBAAA,QAAAiG,6BAAA,CAAAF,SAAA;QACA,KAAA9F,kBAAA,QAAAiG,sBAAA,CAAAH,SAAA;MACA;QACA,KAAA/F,kBAAA;QACA,KAAAC,kBAAA;MACA;IACA;IAEA,gBACA6F,cAAA,WAAAA,eAAAZ,SAAA;MACA;MACA,OAAAA,SAAA,UAAAA,SAAA;IACA;IAEA,gBACAc,cAAA,WAAAA,eAAAd,SAAA;MACA;MACA,OAAAA,SAAA,UAAAA,SAAA;IACA;IAEA,iBACAe,6BAAA,WAAAA,8BAAA5G,IAAA;MAAA,IAAA8G,MAAA;MACArE,OAAA,CAAAC,GAAA;;MAEA;MACA,IAAAqE,YAAA;MAEA/G,IAAA,CAAAgH,OAAA,WAAAb,MAAA;QACA,IAAAlC,QAAA,GAAAkC,MAAA,CAAAlC,QAAA;QACA,IAAAgD,MAAA,GAAAC,UAAA,CAAAf,MAAA,CAAAgB,KAAA;QAEA,KAAAJ,YAAA,CAAA9C,QAAA;UACA8C,YAAA,CAAA9C,QAAA,QAAAmD,GAAA;QACA;;QAEA;QACA;UACA,IAAAC,UAAA,GAAAC,IAAA,CAAAC,KAAA,CAAApB,MAAA,CAAAkB,UAAA;UACA,IAAAG,OAAA,GAAAH,UAAA,CAAAG,OAAA;;UAEA;UACAA,OAAA,CAAAR,OAAA,WAAAS,SAAA;YACA,IAAAC,mBAAA;YAEA,QAAAC,QAAA,CAAA5H,OAAA,EAAA0H,SAAA,kBAAAA,SAAA;cACA;cACA,IAAAG,IAAA,GAAAC,MAAA,CAAAD,IAAA,CAAAH,SAAA,EAAAK,IAAA;cACA,IAAAC,MAAA,GAAAH,IAAA,CAAA1B,GAAA,WAAA8B,GAAA;gBAAA,OAAAC,MAAA,CAAAR,SAAA,CAAAO,GAAA;cAAA;cACAN,mBAAA,GAAAK,MAAA,CAAAG,IAAA;YACA;cACAR,mBAAA,GAAAO,MAAA,CAAAR,SAAA;YACA;;YAEA;YACA,IAAAV,YAAA,CAAA9C,QAAA,EAAAkE,GAAA,CAAAT,mBAAA;cACA,IAAAU,QAAA,GAAArB,YAAA,CAAA9C,QAAA,EAAAoE,GAAA,CAAAX,mBAAA;cACAU,QAAA,CAAAE,WAAA,IAAArB,MAAA;cACAmB,QAAA,CAAAG,WAAA;YACA;cACA;cACAxB,YAAA,CAAA9C,QAAA,EAAAuE,GAAA,CAAAd,mBAAA;gBACAL,UAAA,EAAAK,mBAAA;gBACAY,WAAA,EAAArB,MAAA;gBACAsB,WAAA;cACA;YACA;UACA;QACA,SAAAxE,KAAA;UACAtB,OAAA,CAAAG,IAAA,cAAAuD,MAAA,CAAAkB,UAAA,EAAAtD,KAAA;UACA;UACA,IAAA0E,eAAA,GAAAtC,MAAA,CAAAkB,UAAA;UAEA,IAAAN,YAAA,CAAA9C,QAAA,EAAAkE,GAAA,CAAAM,eAAA;YACA,IAAAL,QAAA,GAAArB,YAAA,CAAA9C,QAAA,EAAAoE,GAAA,CAAAI,eAAA;YACAL,QAAA,CAAAE,WAAA,IAAArB,MAAA;YACAmB,QAAA,CAAAG,WAAA;UACA;YACAxB,YAAA,CAAA9C,QAAA,EAAAuE,GAAA,CAAAC,eAAA;cACApB,UAAA,EAAAoB,eAAA;cACAH,WAAA,EAAArB,MAAA;cACAsB,WAAA;YACA;UACA;QACA;MACA;MAEA9F,OAAA,CAAAC,GAAA;;MAEA;MACA,OAAAmF,MAAA,CAAAa,OAAA,CAAA3B,YAAA,EAAAb,GAAA,WAAAyC,KAAA;QAAA,IAAAC,KAAA,OAAAC,eAAA,CAAA9I,OAAA,EAAA4I,KAAA;UAAA1E,QAAA,GAAA2E,KAAA;UAAAE,UAAA,GAAAF,KAAA;QACA;QACA,IAAAG,OAAA,GAAAC,KAAA,CAAAC,IAAA,CAAAH,UAAA,CAAAf,MAAA,IACAD,IAAA,WAAAhF,CAAA,EAAAoG,CAAA;UAAA,OAAAA,CAAA,CAAAZ,WAAA,GAAAxF,CAAA,CAAAwF,WAAA;QAAA,GACApC,GAAA,WAAAiD,IAAA,EAAAC,KAAA;UAAA;YACA7E,IAAA,EAAA6E,KAAA;YACA/B,UAAA,EAAA8B,IAAA,CAAA9B,UAAA;YACAiB,WAAA,EAAAa,IAAA,CAAAb,WAAA;YACAC,WAAA,EAAAY,IAAA,CAAAZ;UACA;QAAA;QAEA9F,OAAA,CAAAC,GAAA,gDAAA2B,MAAA,CAAAJ,QAAA,OAAAI,MAAA,CAAAyC,MAAA,CAAA3C,aAAA,CAAAxB,QAAA,CAAAsB,QAAA,eAAAI,MAAA,CAAA0E,OAAA,CAAA3H,MAAA;QAEA;UACA6C,QAAA,EAAAtB,QAAA,CAAAsB,QAAA;UACAC,UAAA,EAAA4C,MAAA,CAAA3C,aAAA,CAAAxB,QAAA,CAAAsB,QAAA;UACA8E,OAAA,EAAAA;QACA;MACA,GAAAvC,MAAA,WAAA6C,KAAA;QAAA,OAAAA,KAAA,CAAAN,OAAA,CAAA3H,MAAA;MAAA;IACA;IACA,kBACAkI,0BAAA,WAAAA,2BAAAC,aAAA;MACA;QACA,IAAA/B,OAAA,GAAAF,IAAA,CAAAC,KAAA,CAAAgC,aAAA;QACA,IAAAP,KAAA,CAAAQ,OAAA,CAAAhC,OAAA,KAAAA,OAAA,CAAApG,MAAA;UACA,OAAAoG,OAAA,CAAAtB,GAAA,WAAAuD,GAAA;YACA,QAAA9B,QAAA,CAAA5H,OAAA,EAAA0J,GAAA,kBAAAA,GAAA;cACA;cACA,IAAA7B,IAAA,GAAAC,MAAA,CAAAD,IAAA,CAAA6B,GAAA,EAAA3B,IAAA;cACA,IAAAC,MAAA,GAAAH,IAAA,CAAA1B,GAAA,WAAA8B,GAAA;gBAAA,OAAAC,MAAA,CAAAwB,GAAA,CAAAzB,GAAA;cAAA;cACA,OAAAD,MAAA,CAAAG,IAAA;YACA;YACA,OAAAD,MAAA,CAAAwB,GAAA;UACA,GAAAvB,IAAA;QACA;MACA,SAAAnE,KAAA;QACA;MAAA;MAEA,OAAAwF,aAAA,CAAAnI,MAAA,QAAAmI,aAAA,CAAAG,SAAA,kBAAAH,aAAA;IACA;IACA,+BACA1C,sBAAA,WAAAA,uBAAA7G,IAAA;MAAA,IAAA2J,MAAA;MACA;MACA,IAAAxJ,oBAAA;;MAEA;MACA,IAAA4G,YAAA;MAEA/G,IAAA,CAAAgH,OAAA,WAAAb,MAAA;QACA;UACA,IAAAlC,QAAA,GAAAkC,MAAA,CAAAlC,QAAA;UACA,IAAAkD,KAAA,GAAAD,UAAA,CAAAf,MAAA,CAAAgB,KAAA;UACA,IAAAE,UAAA,GAAAC,IAAA,CAAAC,KAAA,CAAApB,MAAA,CAAAkB,UAAA;UACA,IAAAG,OAAA,GAAAH,UAAA,CAAAG,OAAA;;UAEA;UACA,KAAAT,YAAA,CAAA9C,QAAA;YACA8C,YAAA,CAAA9C,QAAA;cACA2F,WAAA;cACAC,aAAA,MAAAzC,GAAA;YACA;UACA;UAEAL,YAAA,CAAA9C,QAAA,EAAA2F,WAAA,IAAAzC,KAAA;;UAEA;UACAK,OAAA,CAAAR,OAAA,WAAAS,SAAA;YACA,IAAAqC,WAAA,GAAAH,MAAA,CAAAI,wBAAA,CAAAtC,SAAA;;YAEA;YACA,IAAAuC,sBAAA,OAAA/D,GAAA;YAEA6D,WAAA,CAAA9C,OAAA,WAAAiD,KAAA;cACA;cACA,IAAAC,eAAA,GAAAP,MAAA,CAAAQ,oBAAA,CAAAF,KAAA;cACAD,sBAAA,CAAAI,GAAA,CAAAF,eAAA;YACA;;YAEA;YACAF,sBAAA,CAAAhD,OAAA,WAAAkD,eAAA;cACA,IAAAG,aAAA,GAAAtD,YAAA,CAAA9C,QAAA,EAAA4F,aAAA,CAAAxB,GAAA,CAAA6B,eAAA;cACAnD,YAAA,CAAA9C,QAAA,EAAA4F,aAAA,CAAArB,GAAA,CAAA0B,eAAA,EAAAG,aAAA,GAAAlD,KAAA;YACA;UACA;QAEA,SAAApD,KAAA;UACAtB,OAAA,CAAAsB,KAAA,UAAAA,KAAA;QACA;MACA;;MAIA;;MAEA,KAAA5D,oBAAA;MAEA0H,MAAA,CAAAa,OAAA,CAAA3B,YAAA,EAAAC,OAAA,WAAAsD,KAAA;QAAA,IAAAC,KAAA,OAAA1B,eAAA,CAAA9I,OAAA,EAAAuK,KAAA;UAAArG,QAAA,GAAAsG,KAAA;UAAAlB,KAAA,GAAAkB,KAAA;QACA;QACA,IAAAC,iBAAA,GAAAxB,KAAA,CAAAC,IAAA,CAAAI,KAAA,CAAAQ,aAAA,CAAAnB,OAAA,IACAZ,IAAA,WAAA2C,KAAA,EAAAC,KAAA;UAAA,IAAAC,KAAA,OAAA9B,eAAA,CAAA9I,OAAA,EAAA0K,KAAA;YAAA3H,CAAA,GAAA6H,KAAA;UAAA,IAAAC,KAAA,OAAA/B,eAAA,CAAA9I,OAAA,EAAA2K,KAAA;YAAAxB,CAAA,GAAA0B,KAAA;UAAA,OAAA1B,CAAA,GAAApG,CAAA;QAAA;QAEA,IAAAiG,OAAA,GAAAyB,iBAAA,CAAAtE,GAAA,WAAA2E,KAAA,EAAAzB,KAAA;UAAA,IAAA0B,KAAA,OAAAjC,eAAA,CAAA9I,OAAA,EAAA8K,KAAA;YAAAE,MAAA,GAAAD,KAAA;YAAA7D,MAAA,GAAA6D,KAAA;UAAA;YACAvG,IAAA,EAAA6E,KAAA;YACA2B,MAAA,EAAAA,MAAA;YACAC,KAAA;YACA1C,WAAA,EAAArB,MAAA;YACAgE,UAAA,EAAA5B,KAAA,CAAAO,WAAA,QAAA3C,MAAA,GAAAoC,KAAA,CAAAO,WAAA,QAAAsB,OAAA;UACA;QAAA;QAEA,IAAAnC,OAAA,CAAA3H,MAAA;UACAjB,oBAAA,CAAAgL,IAAA;YACAlH,QAAA,EAAAtB,QAAA,CAAAsB,QAAA;YACAC,UAAA,EAAAyF,MAAA,CAAAxF,aAAA,CAAAxB,QAAA,CAAAsB,QAAA;YACA8E,OAAA,EAAAA;UACA;QACA;MACA;MAEA,OAAA5I,oBAAA;IACA;IACA,yBACA4J,wBAAA,WAAAA,yBAAAtC,SAAA;MACA,IAAAD,OAAA;;MAEA;MACA,WAAAC,SAAA;QACA;QACA,IAAA2D,QAAA,GAAA3D,SAAA,CAAA4D,OAAA;QACA,KAAAC,8BAAA,CAAAF,QAAA,CAAAG,KAAA,MAAA/D,OAAA;MACA,eAAAG,QAAA,CAAA5H,OAAA,EAAA0H,SAAA,kBAAAA,SAAA;QACA;QACA,IAAAG,IAAA,GAAAC,MAAA,CAAAD,IAAA,CAAAH,SAAA,EAAAK,IAAA;QACA,IAAA0D,MAAA,GAAA5D,IAAA,CAAA1B,GAAA,WAAA8B,GAAA;UAAA,OAAAC,MAAA,CAAAR,SAAA,CAAAO,GAAA;QAAA;;QAIA;QACA,KAAAsD,8BAAA,CAAAE,MAAA,EAAAhE,OAAA;MACA;MAEA,OAAAA,OAAA;IACA;IACA,mBACA8D,8BAAA,WAAAA,+BAAAE,MAAA,EAAAhE,OAAA;MACA,IAAAlF,CAAA,GAAAkJ,MAAA,CAAApK,MAAA;;MAEA;MACA,IAAAkB,CAAA;QACA;MACA;;MAEA;MACA,SAAAmJ,CAAA,MAAAA,CAAA,GAAAnJ,CAAA,MAAAmJ,CAAA;QACA,SAAAC,CAAA,GAAAD,CAAA,MAAAC,CAAA,GAAApJ,CAAA,MAAAoJ,CAAA;UACA,SAAAC,CAAA,GAAAD,CAAA,MAAAC,CAAA,GAAArJ,CAAA,EAAAqJ,CAAA;YACA,IAAAC,UAAA,GAAAJ,MAAA,CAAAC,CAAA,IAAAD,MAAA,CAAAE,CAAA,IAAAF,MAAA,CAAAG,CAAA;YACA,cAAAE,IAAA,CAAAD,UAAA;cACApE,OAAA,CAAA2D,IAAA,CAAAS,UAAA;YACA;UACA;QACA;MACA;IACA;IAEA,oCACAzB,oBAAA,WAAAA,qBAAAyB,UAAA;MACA;MACA,OAAAA,UAAA,CAAAL,KAAA,KAAAzD,IAAA,GAAAI,IAAA;IACA;IACA,WACA4D,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MAAA,WAAAhK,kBAAA,CAAAhC,OAAA,mBAAAiC,aAAA,CAAAjC,OAAA,IAAAkC,CAAA,UAAA+J,SAAA;QAAA,WAAAhK,aAAA,CAAAjC,OAAA,IAAAqC,CAAA,WAAA6J,SAAA;UAAA,kBAAAA,SAAA,CAAA3J,CAAA;YAAA;cAAA2J,SAAA,CAAA3J,CAAA;cAAA,OACAyJ,MAAA,CAAAlJ,mBAAA;YAAA;cACAkJ,MAAA,CAAAnH,QAAA,CAAAsH,OAAA;YAAA;cAAA,OAAAD,SAAA,CAAAnJ,CAAA;UAAA;QAAA,GAAAkJ,QAAA;MAAA;IACA;IACA,kBACArK,wBAAA,WAAAA,yBAAA;MAAA,IAAAwK,MAAA;MACA1J,OAAA,CAAAC,GAAA;;MAEA;MACA,IAAA0J,SAAA,IACA;QAAAtJ,CAAA;QAAAoG,CAAA;QAAAmD,CAAA;MAAA;MAAA;MACA;QAAAvJ,CAAA;QAAAoG,CAAA;QAAAmD,CAAA;QAAAC,CAAA;MAAA;MAAA;MACA;QAAAxJ,CAAA;QAAAoG,CAAA;QAAAmD,CAAA;QAAAC,CAAA;QAAAC,CAAA;QAAAC,CAAA;MAAA;MAAA;MACA;QAAA1J,CAAA;QAAAoG,CAAA;QAAAmD,CAAA;QAAAC,CAAA;QAAAC,CAAA;MAAA;MAAA,CACA;MAEAH,SAAA,CAAApF,OAAA,WAAAyF,QAAA,EAAArD,KAAA;QACA3G,OAAA,CAAAC,GAAA,+BAAA2B,MAAA,CAAA+E,KAAA,YAAAqD,QAAA;QACA,IAAA3C,WAAA,GAAAqC,MAAA,CAAApC,wBAAA,CAAA0C,QAAA;QACAhK,OAAA,CAAAC,GAAA,cAAAoH,WAAA;;QAEA;QACA,IAAA4C,aAAA,OAAAzG,GAAA;QACA6D,WAAA,CAAA9C,OAAA,WAAAiD,KAAA;UACA,IAAA0C,UAAA,GAAAR,MAAA,CAAAhC,oBAAA,CAAAF,KAAA;UACAyC,aAAA,CAAAtC,GAAA,CAAAuC,UAAA;UACAlK,OAAA,CAAAC,GAAA,IAAA2B,MAAA,CAAA4F,KAAA,UAAA5F,MAAA,CAAAsI,UAAA;QACA;QACAlK,OAAA,CAAAC,GAAA,eAAAsG,KAAA,CAAAC,IAAA,CAAAyD,aAAA;MACA;MAEAjK,OAAA,CAAAC,GAAA;IACA;IACA,mBACAkK,qBAAA,WAAAA,sBAAAtK,CAAA,EAAAuK,CAAA;MACA,IAAAA,CAAA,GAAAvK,CAAA;MACA,IAAAuK,CAAA,UAAAA,CAAA,KAAAvK,CAAA;MAEA,IAAAwK,MAAA;MACA,SAAArB,CAAA,MAAAA,CAAA,GAAAoB,CAAA,EAAApB,CAAA;QACAqB,MAAA,GAAAA,MAAA,IAAAxK,CAAA,GAAAmJ,CAAA,KAAAA,CAAA;MACA;MACA,OAAAsB,IAAA,CAAAC,KAAA,CAAAF,MAAA;IACA;IACA;IACA;IACAlL,YAAA,WAAAA,aAAA;MAAA,IAAAqL,MAAA;MAAA,WAAAlL,kBAAA,CAAAhC,OAAA,mBAAAiC,aAAA,CAAAjC,OAAA,IAAAkC,CAAA,UAAAiL,SAAA;QAAA,IAAAC,YAAA,EAAAC,QAAA,EAAAC,cAAA,EAAAC,gBAAA,EAAAC,GAAA;QAAA,WAAAvL,aAAA,CAAAjC,OAAA,IAAAqC,CAAA,WAAAoL,SAAA;UAAA,kBAAAA,SAAA,CAAAlL,CAAA;YAAA;cAAAkL,SAAA,CAAAlK,CAAA;cAAAkK,SAAA,CAAAlL,CAAA;cAAA,OAGA,IAAAiB,gBAAA;gBACAC,GAAA;gBACAC,MAAA;gBACAC,MAAA;kBAAAC,OAAA;kBAAAC,QAAA;gBAAA;cACA;YAAA;cAJAuJ,YAAA,GAAAK,SAAA,CAAA3J,CAAA;cAAA,MAMA,CAAAsJ,YAAA,KAAAA,YAAA,CAAArJ,IAAA;gBAAA0J,SAAA,CAAAlL,CAAA;gBAAA;cAAA;cACA2K,MAAA,CAAA3M,QAAA;cAAA,OAAAkN,SAAA,CAAA1K,CAAA;YAAA;cAIAsK,QAAA,GAAAD,YAAA,CAAArJ,IAAA,CAAA0C,MAAA,WAAAiH,IAAA;gBAAA,OAAAA,IAAA,CAAAC,MAAA;cAAA;cAEA;cAAAF,SAAA,CAAAlL,CAAA;cAAA,OACA,IAAAiB,gBAAA;gBACAC,GAAA;gBACAC,MAAA;gBACAC,MAAA;kBACAC,OAAA;kBACAC,QAAA;gBACA;cACA;YAAA;cAPAyJ,cAAA,GAAAG,SAAA,CAAA3J,CAAA;cASA,IAAAwJ,cAAA,IAAAA,cAAA,CAAAvJ,IAAA;gBACA;gBACAwJ,gBAAA;gBACAD,cAAA,CAAAvJ,IAAA,CAAAkD,OAAA,WAAAb,MAAA;kBACA,IAAAhF,MAAA,GAAAgF,MAAA,CAAAhE,SAAA;kBACAmL,gBAAA,CAAAnM,MAAA,KAAAmM,gBAAA,CAAAnM,MAAA;gBACA;;gBAEA;gBACA8L,MAAA,CAAA3M,QAAA,GAAA8M,QAAA,CACA5G,MAAA,WAAAiH,IAAA;kBAAA,OAAAH,gBAAA,CAAAG,IAAA,CAAAtM,MAAA;gBAAA,GACA+E,GAAA,WAAAuH,IAAA;kBAAA,WAAAE,cAAA,CAAA5N,OAAA,MAAA4N,cAAA,CAAA5N,OAAA,MACA0N,IAAA;oBACAlF,WAAA,EAAA+E,gBAAA,CAAAG,IAAA,CAAAtM,MAAA;kBAAA;gBAAA,CACA,EACA2G,IAAA,WAAAhF,CAAA,EAAAoG,CAAA;kBAAA,OAAAA,CAAA,CAAAX,WAAA,GAAAzF,CAAA,CAAAyF,WAAA;gBAAA;;gBAEA9F,OAAA,CAAAC,GAAA,iEAAA2B,MAAA,CAAA4I,MAAA,CAAA3M,QAAA,CAAAc,MAAA;cACA;gBACA6L,MAAA,CAAA3M,QAAA;gBACAmC,OAAA,CAAAG,IAAA;cACA;cAAA4K,SAAA,CAAAlL,CAAA;cAAA;YAAA;cAAAkL,SAAA,CAAAlK,CAAA;cAAAiK,GAAA,GAAAC,SAAA,CAAA3J,CAAA;cAGApB,OAAA,CAAAsB,KAAA,cAAAwJ,GAAA;cACAN,MAAA,CAAArI,QAAA,CAAAb,KAAA;cACAkJ,MAAA,CAAA3M,QAAA;YAAA;cAAA,OAAAkN,SAAA,CAAA1K,CAAA;UAAA;QAAA,GAAAoK,QAAA;MAAA;IAEA;IAEA,kBACAzH,mBAAA,WAAAA,oBAAAtE,MAAA;MAAA,IAAAyM,MAAA;MAAA,WAAA7L,kBAAA,CAAAhC,OAAA,mBAAAiC,aAAA,CAAAjC,OAAA,IAAAkC,CAAA,UAAA4L,SAAA;QAAA,IAAA1K,QAAA,EAAA2K,GAAA;QAAA,WAAA9L,aAAA,CAAAjC,OAAA,IAAAqC,CAAA,WAAA2L,SAAA;UAAA,kBAAAA,SAAA,CAAAzL,CAAA;YAAA;cAAAyL,SAAA,CAAAzK,CAAA;cAEAsK,MAAA,CAAArN,OAAA;cAAAwN,SAAA,CAAAzL,CAAA;cAAA,OACA,IAAAiB,gBAAA;gBACAC,GAAA;gBACAC,MAAA;gBACAC,MAAA;kBACAC,OAAA;kBACAC,QAAA;kBACAzB,SAAA,EAAAhB;gBACA;cACA;YAAA;cARAgC,QAAA,GAAA4K,SAAA,CAAAlK,CAAA;cAAA,MAUAV,QAAA,IAAAA,QAAA,CAAAW,IAAA;gBAAAiK,SAAA,CAAAzL,CAAA;gBAAA;cAAA;cAAA,OAAAyL,SAAA,CAAAjL,CAAA,IACAK,QAAA,CAAAW,IAAA;YAAA;cAAA,OAAAiK,SAAA,CAAAjL,CAAA,IAEA;YAAA;cAAAiL,SAAA,CAAAzL,CAAA;cAAA;YAAA;cAAAyL,SAAA,CAAAzK,CAAA;cAAAwK,GAAA,GAAAC,SAAA,CAAAlK,CAAA;cAGApB,OAAA,CAAAsB,KAAA,gBAAA+J,GAAA;cACAF,MAAA,CAAAhJ,QAAA,CAAAb,KAAA;cAAA,OAAAgK,SAAA,CAAAjL,CAAA,IACA;YAAA;cAAAiL,SAAA,CAAAzK,CAAA;cAEAsK,MAAA,CAAArN,OAAA;cAAA,OAAAwN,SAAA,CAAAvB,CAAA;YAAA;cAAA,OAAAuB,SAAA,CAAAjL,CAAA;UAAA;QAAA,GAAA+K,QAAA;MAAA;IAEA;IAEA,eACAG,gBAAA,WAAAA,iBAAA7M,MAAA;MAAA,IAAA8M,OAAA;MAAA,WAAAlM,kBAAA,CAAAhC,OAAA,mBAAAiC,aAAA,CAAAjC,OAAA,IAAAkC,CAAA,UAAAiM,SAAA;QAAA,WAAAlM,aAAA,CAAAjC,OAAA,IAAAqC,CAAA,WAAA+L,SAAA;UAAA,kBAAAA,SAAA,CAAA7L,CAAA;YAAA;cACA;cACA2L,OAAA,CAAA/N,oBAAA;cACA+N,OAAA,CAAA9N,oBAAA;cAAA,KAEAgB,MAAA;gBAAAgN,SAAA,CAAA7L,CAAA;gBAAA;cAAA;cAAA6L,SAAA,CAAA7L,CAAA;cAAA,OAEA2L,OAAA,CAAApL,mBAAA;YAAA;cACAoL,OAAA,CAAArJ,QAAA,CAAAsH,OAAA,mCAAA7H,MAAA,CAAA4J,OAAA,CAAAG,kBAAA;YAAA;cAAA,OAAAD,SAAA,CAAArL,CAAA;UAAA;QAAA,GAAAoL,QAAA;MAAA;IAEA;IAEA,kBACAE,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,OAAA;MACA,UAAAhO,cAAA;MACA,IAAAoN,IAAA,QAAAnN,QAAA,CAAA8D,IAAA,WAAAkK,CAAA;QAAA,OAAAA,CAAA,CAAAnN,MAAA,KAAAkN,OAAA,CAAAhO,cAAA;MAAA;MACA,OAAAoN,IAAA,GAAAA,IAAA,CAAAc,QAAA,IAAAd,IAAA,CAAAe,QAAA;IACA;IAEA,aACAC,uBAAA,WAAAA,wBAAAC,GAAA;MACAjM,OAAA,CAAAC,GAAA,oCAAA2B,MAAA,CAAAqK,GAAA,CAAAhP,IAAA;MACA;IACA;IAEA,aACAiP,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,OAAA;MAAA,WAAA7M,kBAAA,CAAAhC,OAAA,mBAAAiC,aAAA,CAAAjC,OAAA,IAAAkC,CAAA,UAAA4M,SAAA;QAAA,IAAAC,GAAA;QAAA,WAAA9M,aAAA,CAAAjC,OAAA,IAAAqC,CAAA,WAAA2M,SAAA;UAAA,kBAAAA,SAAA,CAAAzM,CAAA;YAAA;cAAA,MACAsM,OAAA,CAAA9N,OAAA,KAAA8N,OAAA,CAAAvO,cAAA;gBAAA0O,SAAA,CAAAzM,CAAA;gBAAA;cAAA;cACAsM,OAAA,CAAAhK,QAAA,CAAAC,OAAA;cAAA,OAAAkK,SAAA,CAAAjM,CAAA;YAAA;cAAAiM,SAAA,CAAAzL,CAAA;cAAAyL,SAAA,CAAAzM,CAAA;cAAA,OAKAsM,OAAA,CAAA/M,eAAA;YAAA;cAAAkN,SAAA,CAAAzM,CAAA;cAAA,OACAsM,OAAA,CAAA/L,mBAAA;YAAA;cACA+L,OAAA,CAAAhK,QAAA,CAAAsH,OAAA;cAAA6C,SAAA,CAAAzM,CAAA;cAAA;YAAA;cAAAyM,SAAA,CAAAzL,CAAA;cAAAwL,GAAA,GAAAC,SAAA,CAAAlL,CAAA;cAEApB,OAAA,CAAAsB,KAAA,YAAA+K,GAAA;cACAF,OAAA,CAAAhK,QAAA,CAAAb,KAAA;YAAA;cAAA,OAAAgL,SAAA,CAAAjM,CAAA;UAAA;QAAA,GAAA+L,QAAA;MAAA;IAEA;IAEAG,KAAA,WAAAA,MAAA;MACA,KAAA/O,aAAA;MACA;MACA,KAAAI,cAAA;MACA,KAAAG,iBAAA;MACA;MACA,KAAAsE,mBAAA;IACA;EACA;AACA", "ignoreList": []}]}