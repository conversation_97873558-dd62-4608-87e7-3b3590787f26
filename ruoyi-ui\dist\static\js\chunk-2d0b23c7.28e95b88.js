(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0b23c7"],{"22f7":function(e,n,t){"use strict";t.r(n),t.d(n,"EIGHT_CODE_PATTERNS",(function(){return A}));var r=t("2909"),d=t("3835"),a=t("b85c"),o=(t("99af"),t("4de4"),t("caad"),t("a15b"),t("d81d"),t("14d9"),t("4e82"),t("4ec9"),t("b64b"),t("d3b7"),t("4d63"),t("c607"),t("ac1f"),t("2c3e"),t("00b4"),t("25f0"),t("8a79"),t("2532"),t("3ca3"),t("466d"),t("5319"),t("1276"),t("498a"),t("0643"),t("76d6"),t("2382"),t("4e3e"),t("a573"),t("9a9a"),t("159b"),t("ddb0"),t("f9d6"));function u(e){return"string"===typeof e&&e.endsWith("$")?e.replace(/\$$/,"[，,./*-=。·、s]*$"):e instanceof RegExp&&e.source.endsWith("$")?new RegExp(e.source.replace(/\$$/,"[，,./*-=。·、s]*$")):e}var A={BAMA_TRANSFORM:{pattern:/^(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?(\d{8})[\-—~～@#￥%…&!、\\/*,，.＝=各·\s]*?(转|套|拖)(?:各)?(\d+)(?:\+(\d+))?(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?$/,handler:function(e,n){var r=e[2],d=e[4],a=e[5],o=e[6],u=t("f9d6").generateTransformNumbers(r),A=[];return d?A.push({methodId:t("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:u.join(","),money:d}):a&&o&&(A.push({methodId:t("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:u.join(","),money:a}),A.push({methodId:t("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:u.join(","),money:o})),A}},BAMA_ZUSAN:{pattern:u(/^(\d{8})[\-—~～@#￥%…&!、\\/*,，一－.＝=·\s]*组三?[\-—~～@#￥%…&!、\\/*,，。：:，一—－.＝=·\s]*([\d]+)?(元|块|米)?[\-—~～@#￥%…&!、。，一—－\\/*,，.＝=·\s]*$/),handler:function(e,n){return 8!==e[1].length?null:[{methodId:o["METHOD_ID"].BAMA_ZUSAN,betNumbers:e[1],money:e[2]||""}]}},BAMA_FANGDUI:{pattern:u(/^(\d{8})防对?(?:-?(\d+))?$/),handler:function(e,n){return 8!==e[1].length?null:[{methodId:o["METHOD_ID"].BAMA_ZUSAN,betNumbers:e[1],money:e[2]||""}]}},MULTI_BAMA:{pattern:u(/^(\d{8}(?:[^0-9]+\d{8})*)(?:各([一二三四五六七八九十百千万\d]+))?(?:防([一二三四五六七八九十百千万\d]+))?$/),handler:function(e,n){var r=t("f9d6"),d=r.chineseToNumber,a=n.split(o["BASE_PATTERNS"].SEPARATORS).filter(Boolean).filter((function(e){return 8===e.length})),u=e[2]?d(e[2]):"",A=e[3]?d(e[3]):"",l=[];return u&&l.push({methodId:o["METHOD_ID"].BAMA_ZULIU,betNumbers:a.join(","),money:u}),A&&l.push({methodId:o["METHOD_ID"].BAMA_ZUSAN,betNumbers:a[a.length-1],money:A}),console.log("八码 handler 返回:",l),l}},BAMA_ZULIU_ZUSAN_MULTI_MONEY_SUPER:{pattern:u(/^(\d{8}(?:[－\-—~～@#￥%…&!、\\/*,一－。：:＝=·\s+吊赶打各]+\d{8})*)[\-—~～@#￥%…&!、\\/*,一－。＝=：:·\s+吊赶打各]+(\d+)[\-—~～@#￥%…&!、\\/*,一－。：:＝=·\s+吊赶打各]*[+](\d+)(?:元)?$/),handler:function(e,n){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],d=t("f9d6"),a=d.SUPER_SEPARATORS,o=e[1].split(a).filter(Boolean),u=e[2],A=e[3];return r&&(/^\d{8}$/.test(u)||/^\d{8}$/.test(A))?null:[{methodId:t("f9d6").METHOD_ID.BAMA_ZULIU,betNumbers:o.join(","),money:u},{methodId:t("f9d6").METHOD_ID.BAMA_ZUSAN,betNumbers:o.join(","),money:A}]}},BAMA_ZULIU_ZU_SUPER:{pattern:u(/^(\d{8})组(\d+)$/),handler:function(e,n){var r=e[1],d=e[2];return[{methodId:t("f9d6").METHOD_ID.BAMA_ZULIU,betNumbers:r,money:d}]}},BAMA_ZULIU_MULTI_SUPER:{pattern:u(/^(\d{8}(?:[\-—~～@#￥%…&!、\\/*,：:一－。，.＝=·\s+吊赶打各]+\d{8})*)[\-—~～@#￥%…&!、\\/*,一－。，.＝=：:·\s+吊赶打各]+(\d+)$/),handler:function(e,n){var r=t("f9d6"),d=r.SUPER_SEPARATORS,a=e[1].split(d).filter(Boolean),o=e[2];return[{methodId:t("f9d6").METHOD_ID.BAMA_ZULIU,betNumbers:a.join(","),money:o}]}},BAMA_ZUSAN_ZU_SUPER:{pattern:u(/^(\d{8})组三(\d+)$/),handler:function(e,n){var r=e[1],d=e[2];return[{methodId:t("f9d6").METHOD_ID.BAMA_ZUSAN,betNumbers:r,money:d}]}},BAMA_ZHIZU_MULTI_SUPER:{pattern:u(/^(\d{8}(?:[\-—~～@#￥%…&!、\\/*,一：:－。，.＝=·\s+吊赶打各]+\d{8})*)[\-—~～@#￥%…&!、\\/*,一：:－。，.＝=·\s+吊赶打各]*(直组|组直|值组|组值)[\s]*各?(\d+)(?:元)?$/),handler:function(e,n){var r=t("f9d6"),d=r.SUPER_SEPARATORS,a=e[1].split(d).filter(Boolean),o=e[3];return[{methodId:t("f9d6").METHOD_ID.BAMA_ZULIU,betNumbers:a.join(","),money:o},{methodId:t("f9d6").METHOD_ID.BAMA_ZUSAN,betNumbers:a.join(","),money:o}]}},BAMA_ZULIU_FANG:{pattern:u(/^(\d{8})-(\d+)防(\d+)$/),handler:function(e,n){var t=e[1],r=e[2],d=e[3];return[{methodId:o["METHOD_ID"].BAMA_ZULIU,betNumbers:t,money:r},{methodId:o["METHOD_ID"].BAMA_ZUSAN,betNumbers:t,money:d}]}},BAMA_MULTI_ZHIZU_EACH_SUPER:{pattern:u(/^(直组|组直|值组|组值)各([0-9]+)[\s\-—~～@# ￥%…&!、\\/*,一－。，.：:＝=·吊赶打各\n]+([\d\s\-—~～@#￥%…&!、\\/*,一－：:。，.＝=·吊赶打各]+)$/i),handler:function(e,n){var r=t("f9d6"),d=r.SUPER_SEPARATORS,a=e[2],o=e[3].replace(/\n/g," "),u=o.split(d).map((function(e){return e.trim()})).filter((function(e){return/^\d{8}$/.test(e)}));return[{methodId:t("f9d6").METHOD_ID.BAMA_ZULIU,betNumbers:u.join(","),money:a},{methodId:t("f9d6").METHOD_ID.BAMA_ZUSAN,betNumbers:u.join(","),money:a}]}},BAMA_MULTI_EACH_SUPER:{pattern:u(/^(\d{8}(?:[\-~～@#￥%…&!、\\/*,一－。，.＝=：:·\s+吊赶打各]+\d{8})*)各(\d+)$/),handler:function(e,n){var r=t("f9d6"),d=r.SUPER_SEPARATORS,a=e[1].split(d).filter(Boolean);if(!a.length||a.some((function(e){return 8!==e.length})))return null;var o=e[2];return[{methodId:t("f9d6").METHOD_ID.BAMA_ZULIU,betNumbers:a.join(","),money:o}]}},BAMA_SLASH_TWO_MONEY:{pattern:/^(?!.*[\r\n])(\d{8})[\-—~～@#￥：:%…&!、\\/*,，.。\u4e00\uff0d\uff1d=· 　\t]+(\d+)[+]+(\d+)$/,handler:function(e,n){var r=e[1],d=e[2],a=e[3];return[{methodId:t("f9d6").METHOD_ID.BAMA_ZULIU,betNumbers:r,money:d},{methodId:t("f9d6").METHOD_ID.BAMA_ZUSAN,betNumbers:r,money:a}]}},BAMA_MULTI_SUPER_SLASH_TWO_MONEY:{pattern:u(/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、：:一－。＝=·吊赶打各]+)\/(\d+)\/(\d+)$/),handler:function(e,n){var r=t("f9d6"),d=r.SUPER_SEPARATORS,a=e[1].split(d).map((function(e){return e.trim()})).filter((function(e){return 8===e.length}));if(!a.length)return[];var o=e[2],u=e[3];return[{methodId:t("f9d6").METHOD_ID.BAMA_ZULIU,betNumbers:a.join(","),money:o},{methodId:t("f9d6").METHOD_ID.BAMA_ZUSAN,betNumbers:a.join(","),money:u}]}},BAMA_UNIVERSAL_SUPER:{pattern:u(/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、：:一－。＝=·吊赶打各]+)[\-~～@#￥%…&!、\\/*,一－。＝=·：:\s+吊赶打各]+([0-9]+)(元|米|块)?$/),handler:function(e,n){var r=t("f9d6"),d=r.SUPER_SEPARATORS,a=r.METHOD_ID,o=e[1].split(d).map((function(e){return e.trim()})).filter(Boolean),u=e[2];return 1===o.length&&8===o[0].length?[{methodId:a.BAMA_ZULIU,betNumbers:o[0],money:u}]:o.length>=2&&o.every((function(e){return 8===e.length}))?[{methodId:a.BAMA_ZULIU,betNumbers:o.join(","),money:u}]:null}},BAMA_EACH_PLUS_SUPER:{pattern:u(/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、一－。：:＝=·吊赶打各]+)各([0-9]+)\+([0-9]+)$/),handler:function(e,n){var r=t("f9d6"),d=r.SUPER_SEPARATORS,a=r.METHOD_ID,o=e[1].split(d).map((function(e){return e.trim()})).filter(Boolean),u=e[2],A=e[3];return 1===o.length&&8===o[0].length?[{methodId:a.BAMA_ZULIU,betNumbers:o[0],money:u},{methodId:a.BAMA_ZUSAN,betNumbers:o[0],money:A}]:o.length>=2&&o.every((function(e){return 8===e.length}))?[{methodId:a.BAMA_ZULIU,betNumbers:o.join(","),money:u},{methodId:a.BAMA_ZUSAN,betNumbers:o.join(","),money:A}]:null}},BAMA_ZULIU_MONEY:{pattern:u(/^([0-9]{8,})\s+(\d+)(元|块|米)?$/),handler:function(e,n){var r=e[1],d=e[2];return r&&8===r.length?[{methodId:t("f9d6").METHOD_ID.BAMA_ZULIU,betNumbers:r,money:d}]:null}},BAMA_DA_MONEY:{pattern:u(/^([0-9]{8})打(\d+)(元|块|米)?$/),handler:function(e,n){return[{methodId:t("f9d6").METHOD_ID.BAMA_ZULIU,betNumbers:e[1],money:e[2]}]}},BAMA_MULTI_WITH_MONEY_SLASH_PLUS:{pattern:u(/^(\d{8}(?:[\s,，\-]+?\d{8})*)\/\s*(\d+)\+(\d+)$/),handler:function(e,n){var r=e[1].split(/[\s,，\-]+/).filter(Boolean),d=e[2],a=e[3];return[{methodId:t("f9d6").METHOD_ID.BAMA_ZULIU,betNumbers:r.join(","),money:d},{methodId:t("f9d6").METHOD_ID.BAMA_ZUSAN,betNumbers:r.join(","),money:a}]}},BAMA_MULTI_LINE_MONEY_PLUS:{pattern:/^((?:\d{8}[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+\d+\+\d+\s*\n?)+)$/m,handler:function(e,n){var r,o=t("f9d6"),u=o.METHOD_ID,A=n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),l=new Map,_=!0,s=Object(a["a"])(A);try{for(s.s();!(r=s.n()).done;){var i=r.value,m=i.match(/^(\d{8})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+(\d+)\+(\d+)$/);if(!m){_=!1;break}var f=m[1];if(8!==f.length){_=!1;break}var h=i.split(/[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+/)[0];if(8!==h.length||!/^\d{8}$/.test(h)){_=!1;break}var I="".concat(m[2],"+").concat(m[3]);l.has(I)||l.set(I,[]),l.get(I).push(f)}}catch(B){s.e(B)}finally{s.f()}if(!_||0===l.size)return null;var U,c=[],M=Object(a["a"])(l.entries());try{for(M.s();!(U=M.n()).done;){var b=Object(d["a"])(U.value,2),N=b[0],p=b[1],E=N.split("+"),O=Object(d["a"])(E,2),D=O[0],S=O[1];c.push({methodId:u.BAMA_ZULIU,betNumbers:p.join(","),money:D}),c.push({methodId:u.BAMA_ZUSAN,betNumbers:p.join(","),money:S})}}catch(B){M.e(B)}finally{M.f()}return c.length?c:null}},BAMA_MULTI_LINE_MONEY_PLUS_CN:{pattern:u(/^((?:\d{8}[，,、]?[一二三四五六七八九十百千万\d]+加[一二三四五六七八九十百千万\d]+\s*\n?)+)$/m),handler:function(e,n){var r,o=t("f9d6"),u=o.METHOD_ID,A=o.chineseToNumber,l=n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),_=new Map,s=Object(a["a"])(l);try{for(s.s();!(r=s.n()).done;){var i=r.value,m=i.match(/^(\d{8})[，,、]?([一二三四五六七八九十百千万\d]+)加([一二三四五六七八九十百千万\d]+)$/);if(m){var f=m[1],h=A(m[2]),I=A(m[3]),U="".concat(h,"+").concat(I);_.has(U)||_.set(U,[]),_.get(U).push(f)}}}catch(L){s.e(L)}finally{s.f()}var c,M=[],b=Object(a["a"])(_.entries());try{for(b.s();!(c=b.n()).done;){var N=Object(d["a"])(c.value,2),p=N[0],E=N[1],O=p.split("+"),D=Object(d["a"])(O,2),S=D[0],B=D[1];M.push({methodId:u.BAMA_ZULIU,betNumbers:E.join(","),money:S}),M.push({methodId:u.BAMA_ZUSAN,betNumbers:E.join(","),money:B})}}catch(L){b.e(L)}finally{b.f()}return M.length?M:null}},BAMA_MULTI_EACH_FANGDUI_SLASH:{pattern:u(/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、一－。＝=·吊赶打各]+)[/\-~～@#￥%…&!、\\/*,一－。＝=·吊赶打各]+各防对([一二三四五六七八九十百千万\d]+)$/),handler:function(e,n){var r=t("f9d6"),d=r.SUPER_SEPARATORS,a=r.METHOD_ID,o=r.chineseToNumber,u=e[1].split(d).map((function(e){return e.trim()})).filter((function(e){return 8===e.length})),A=o(e[2]);return u.length?[{methodId:a.BAMA_ZUSAN,betNumbers:u.join(","),money:A}]:null}},BAMA_MULTI_EACH_FANGDUI:{pattern:u(/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、一－。＝=·吊赶打各]+)各防对([一二三四五六七八九十百千万\d]+)$/),handler:function(e,n){var r=t("f9d6"),d=r.SUPER_SEPARATORS,a=r.METHOD_ID,o=r.chineseToNumber,u=e[1].split(d).map((function(e){return e.trim()})).filter((function(e){return 8===e.length})),A=o(e[2]);return u.length?[{methodId:a.BAMA_ZUSAN,betNumbers:u.join(","),money:A}]:null}},BAMA_SLASH_MONEY:{pattern:u(/^\s*(\d{8})\s*\/\s*([一二三四五六七八九十百千万\d]+)\s*$/),handler:function(e,n){var r=t("f9d6"),d=r.METHOD_ID,a=r.chineseToNumber,o=e[1],u=a(e[2]);return[{methodId:d.BAMA_ZULIU,betNumbers:o,money:u}]}},BAMA_ZULIU_ZUSAN_MONEY_PAIR:{pattern:/^(\d{8}[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*)组六[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*(\d+)[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*组三[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*(\d+)$/,handler:function(e,n){var t=e[1],r=e[2],d=e[3];return[{methodId:o["METHOD_ID"].BAMA_ZULIU,betNumbers:t,money:r},{methodId:o["METHOD_ID"].BAMA_ZUSAN,betNumbers:t,money:d}]}},BAMA_ZULIU:{pattern:/^(\d{8})组六?(\d+)[，,./\*\-\=。一－。·、\s]*$/,handler:function(e,n){var t=e[1];if(8!==t.length)return null;var r=e[2]||"";return[{methodId:o["METHOD_ID"].BAMA_ZULIU,betNumbers:t,money:r}]}},BAMA_ZULIU_ZUSAN_COMBO:{pattern:/^(\d{8}(?:[+＋\\-—~～@#￥%…&!、：:\\/*,，。一—－~.＝=·吊赶打各\s]+\d{8})*)[+＋\-—~～@#￥%…&!、：:\\/*,，。一—－~.＝=·吊赶打各]+(\d+)[+]+(\d+)$/,handler:function(e,n){var r=t("f9d6"),d=(r.SUPER_SEPARATORS,e[1].split(/[。.．、,，\s]+/).map((function(e){return e.trim()})).filter(Boolean)),a=e[2],o=e[3];return d.length&&d.every((function(e){return 8===e.length}))?[{methodId:t("f9d6").METHOD_ID.BAMA_ZULIU,betNumbers:d.join(","),money:a},{methodId:t("f9d6").METHOD_ID.BAMA_ZUSAN,betNumbers:d.join(","),money:o}]:null}},BAMA_SINGLE_LINE_ZULIU_ZUSAN_PLUS:{pattern:/^(\d{8})\s+(\d+)[+＋](\d+)$/,handler:function(e,n){return[{methodId:t("f9d6").METHOD_ID.BAMA_ZULIU,betNumbers:e[1],money:e[2]},{methodId:t("f9d6").METHOD_ID.BAMA_ZUSAN,betNumbers:e[1],money:e[3]}]}},BAMA_MULTI_LINE_ZULIU_ZUSAN_PLUS:{pattern:/^([\s\S]+)$/m,handler:function(e,n){var r,o=t("f9d6"),u=o.SUPER_SEPARATORS,A=n.split(/\r?\n/).map((function(e){return e.trim()})).filter(Boolean),l=new Map,_=new Map,s=Object(a["a"])(A);try{for(s.s();!(r=s.n()).done;){var i=r.value,m=i.match(new RegExp("^(\\d{8})".concat(u.source,"(\\d+)\\+(\\d+)$")));if(m){var f=m[1],h=m[2],I=m[3],U=h;l.has(U)||l.set(U,[]),l.get(U).push(f);var c=I;_.has(c)||_.set(c,[]),_.get(c).push(f)}else{var M=i.match(new RegExp("^(\\d{8})".concat(u.source,"(\\d+)$")));if(M){var b=M[1],N=M[2];l.has(N)||l.set(N,[]),l.get(N).push(b)}}}}catch(R){s.e(R)}finally{s.f()}var p,E=[],O=Object(a["a"])(l.entries());try{for(O.s();!(p=O.n()).done;){var D=Object(d["a"])(p.value,2),S=D[0],B=D[1];E.push({methodId:t("f9d6").METHOD_ID.BAMA_ZULIU,betNumbers:B.join(","),money:S})}}catch(R){O.e(R)}finally{O.f()}var L,T=Object(a["a"])(_.entries());try{for(T.s();!(L=T.n()).done;){var v=Object(d["a"])(L.value,2),y=v[0],Z=v[1];E.push({methodId:t("f9d6").METHOD_ID.BAMA_ZUSAN,betNumbers:Z.join(","),money:y})}}catch(R){T.e(R)}finally{T.f()}return E.length?E:null}},BAMA_MULTI_LINE_ZULIU_PLUS:{pattern:/^((?:\d{8}[+＋\-—~～@#￥%…&!、：:\\/*,，。一—－~.＝=·吊赶打各]+]\d+\s*\n?)+)$/m,handler:function(e,n){var r,o=t("f9d6"),u=o.METHOD_ID,A=n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),l=new Map,_=[],s=Object(a["a"])(A);try{for(s.s();!(r=s.n()).done;){var i=r.value,m=i.match(/^(\d{8})[+＋\-—~～@#￥%…&!、：:\\/*,，。一—－~.＝=·吊赶打各]+](\d+)$/);if(m){var f=m[1],h=m[2];l.has(h)||l.set(h,[]),l.get(h).push(f)}else _.push(i)}}catch(p){s.e(p)}finally{s.f()}var I,U=[],c=Object(a["a"])(l.entries());try{for(c.s();!(I=c.n()).done;){var M=Object(d["a"])(I.value,2),b=M[0],N=M[1];U.push({methodId:u.BAMA_ZULIU,betNumbers:N.join(","),money:b})}}catch(p){c.e(p)}finally{c.f()}return U}},BAMA_ZULIU_FANG_ZUSAN_PLUS:{pattern:/^(\d{8})[\-—~～@#￥%…&!、\\/*,一－。＝=·\s+吊赶打各]*(\d+)[\-—~～@#￥%…&!、\\/*,一－。＝=·\s+吊赶打各]*(防对|防)(\d+)$/,handler:function(e,n){return[{methodId:t("f9d6").METHOD_ID.BAMA_ZULIU,betNumbers:e[1],money:e[2]},{methodId:t("f9d6").METHOD_ID.BAMA_ZUSAN,betNumbers:e[1],money:e[3]}]}},BAMA_MULTI_LINE_ZULIU_FANG_ZUSAN_GROUP:{pattern:/^([\s\S]+)$/m,handler:function(e,n){var r,o=n.split(/[\r\n]+/).map((function(e){return e.trim()})).filter(Boolean),u=new Map,A=Object(a["a"])(o);try{for(A.s();!(r=A.n()).done;){var l=r.value,_=l.match(/^(\d{8})-?(\d+)防(\d+)$/);if(_){var s=_[1],i=_[2],m=_[3],f="".concat(i,"|").concat(m);u.has(f)||u.set(f,[]),u.get(f).push(s)}}}catch(D){A.e(D)}finally{A.f()}var h,I=[],U=Object(a["a"])(u.entries());try{for(U.s();!(h=U.n()).done;){var c=Object(d["a"])(h.value,2),M=c[0],b=c[1],N=M.split("|"),p=Object(d["a"])(N,2),E=p[0],O=p[1];I.push({methodId:t("f9d6").METHOD_ID.BAMA_ZULIU,betNumbers:b.join(","),money:E}),I.push({methodId:t("f9d6").METHOD_ID.BAMA_ZUSAN,betNumbers:b.join(","),money:O})}}catch(D){U.e(D)}finally{U.f()}return I.length?I:null}},BAMA_MULTI_TRANSFORM_EACH:{pattern:/^(\d{7}(?:[\-~～@#￥%…&!、\\/*,一－。＝=·\s+吊赶打各]+\d{7})*)(转|转各|套|套各)(\d+)\+(\d+)$/,handler:function(e,n){var r,d=t("f9d6"),o=d.SUPER_SEPARATORS,u=d.METHOD_ID,A=d.generateTransformNumbers,l=e[1].split(o).filter(Boolean),_=e[3],s=e[4],i=[],m=Object(a["a"])(l);try{for(m.s();!(r=m.n()).done;){var f=r.value,h=A(f);i.push({methodId:u.BAMA_ZULIU,betNumbers:h.join(","),money:_}),i.push({methodId:u.BAMA_ZUSAN,betNumbers:h.join(","),money:s})}}catch(I){m.e(I)}finally{m.f()}return i}},BAMA_MULTI_LINE_TRANSFORM:{pattern:/^((?:(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?\d{8}转\d+(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?\s*\n?)+)$/m,handler:function(e,n){var r,o=t("f9d6"),u=o.METHOD_ID,A=n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),l=new Map,_=Object(a["a"])(A);try{for(_.s();!(r=_.n()).done;){var s=r.value,i=s.match(/^(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?(\d{8})转(\d+)(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?$/);if(i){var m=i[2],f=i[3];l.has(f)||l.set(f,[]),l.get(f).push(m)}}}catch(N){_.e(N)}finally{_.f()}var h,I=[],U=Object(a["a"])(l.entries());try{for(U.s();!(h=U.n()).done;){var c=Object(d["a"])(h.value,2),M=c[0],b=c[1];I.push({methodId:u.JIUMA_ZULIU,betNumbers:b.join(","),money:M})}}catch(N){U.e(N)}finally{U.f()}return I.length?I:null}},BAMA_MULTI_COLON_EACH_SUPER:{pattern:/^(\d{8}(?::|：\d{8})*)各(\d+)$/,handler:function(e,n){var r=n.split(/:|：/).map((function(e){return e.trim()})).filter((function(e){return 8===e.length})),d=e[2];return r.length?[{methodId:t("f9d6").METHOD_ID.BAMA_ZULIU,betNumbers:r.join(","),money:d}]:null}},BAMA_MULTI_YIGE_EACH_PLUS:{pattern:/^([\d一]+)一([\d一]+)一?各?(\d+)\+(\d+)$/,handler:function(e,n){var r=n.split("一").map((function(e){return e.replace(/[^\d]/g,"")})).filter((function(e){return 8===e.length})),d=e[3],a=e[4];return r.length?[{methodId:t("f9d6").METHOD_ID.BAMA_ZULIU,betNumbers:r.join(","),money:d},{methodId:t("f9d6").METHOD_ID.BAMA_ZUSAN,betNumbers:r.join(","),money:a}]:null}},BAMA_DIAN_XMA_ZULIU_FANG_ZUSAN:{pattern:/^(\d{8})[。.．、,，\s]+8码(\d+)防(\d+)$/,handler:function(e,n){return[{methodId:t("f9d6").METHOD_ID.BAMA_ZULIU,betNumbers:e[1],money:e[2]},{methodId:t("f9d6").METHOD_ID.BAMA_ZUSAN,betNumbers:e[1],money:e[3]}]}},BAMA_MULTI_LINE_ZULIU_MONEY_GROUP:{pattern:/^((?:\d{8}\s+\d+\s*\n?)+)$/m,handler:function(e,n){var r,o=t("f9d6"),u=o.METHOD_ID,A=n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),l=new Map,_=Object(a["a"])(A);try{for(_.s();!(r=_.n()).done;){var s=r.value,i=s.match(/^(\d{8})\s+(\d+)$/);if(i){var m=i[1],f=i[2];l.has(f)||l.set(f,[]),l.get(f).push(m)}}}catch(N){_.e(N)}finally{_.f()}var h,I=[],U=Object(a["a"])(l.entries());try{for(U.s();!(h=U.n()).done;){var c=Object(d["a"])(h.value,2),M=c[0],b=c[1];I.push({methodId:u.BAMA_ZULIU,betNumbers:b.join(","),money:M})}}catch(N){U.e(N)}finally{U.f()}return I.length?I:null}},BAMA_MULTI_NUMBERS_EACH_FANG:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、一－。＝=·吊赶打]+)各(\d+)(防|防对|放对)(\d+)$/,handler:function(e,n){var r=t("f9d6"),d=r.SUPER_SEPARATORS,a=r.METHOD_ID,o=e[1].split(d).map((function(e){return e.trim()})).filter((function(e){return 8===e.length}));if(!o.length)return null;var u=e[2],A=e[4];return[{methodId:a.BAMA_ZULIU,betNumbers:o.join(","),money:u},{methodId:a.BAMA_ZUSAN,betNumbers:o.join(","),money:A}]}},BAMA_YI_YI_ONE_MONEY:{pattern:/^(\d{8})一(\d{8})一个(\d+)(元|块|米)?$/,handler:function(e,n){var r=e[1],d=e[2],a=e[3];return 8!==r.length||8!==d.length?null:[{methodId:t("f9d6").METHOD_ID.BAMA_ZULIU,betNumbers:r+","+d,money:a}]}},BAMA_GE_MONEY:{pattern:/^(\d{8})[，,、\s]*个(\d+)(元|块|米)?$/,handler:function(e,n){var r=e[1],d=e[2];return 8!==r.length?null:[{methodId:t("f9d6").METHOD_ID.BAMA_ZULIU,betNumbers:r,money:d}]}},MULTI_LINE_BAMA_MONEY_GROUP:{pattern:/^((?:\d{8}[\-—~～@#￥%…&!、\\/*,一－。.＝=·吊赶打各\s]+\d+\s*\n?)+)$/m,handler:function(e,n){var r,o=t("f9d6"),u=o.METHOD_ID,A=n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),l=new Map,_=Object(a["a"])(A);try{for(_.s();!(r=_.n()).done;){var s=r.value,i=s.match(/^(\d{8})[\-—~～@#￥%…&!、\\/*,一－。.＝=·吊赶打各\s]+([0-9]+)$/);if(i){var m=i[1],f=i[2];l.has(f)||l.set(f,[]),l.get(f).push(m)}}}catch(N){_.e(N)}finally{_.f()}var h,I=[],U=Object(a["a"])(l.entries());try{for(U.s();!(h=U.n()).done;){var c=Object(d["a"])(h.value,2),M=c[0],b=c[1];I.push({methodId:u.BAMA_ZULIU,betNumbers:b.join(","),money:M})}}catch(N){U.e(N)}finally{U.f()}return I.length?I:null}},BAMA_MULTI_LINE_ZULIU_MONEY_SUPER:{pattern:/^([\s\S]+)$/m,handler:function(e,n){var r,o=t("f9d6"),u=o.METHOD_ID,A=n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),l=new Map,_=/^(\d{8})[\-—~～@#￥%…&!、一－。\\/*,，.＝=·\s]+(\d+)(元|米|块)?([，,./\\*\-\=。·、\s]*)?$/,s=[],i=Object(a["a"])(A);try{for(i.s();!(r=i.n()).done;){var m=r.value,f=m.match(_);if(!f)return console.log('【BAMA_MULTI_LINE_ZULIU_MONEY_SUPER】行 "'.concat(m,'" 不匹配八码格式，返回null')),null;s.push({line:m,match:f})}}catch(S){i.e(S)}finally{i.f()}console.log("【BAMA_MULTI_LINE_ZULIU_MONEY_SUPER】所有 ".concat(s.length," 行都是八码格式，继续处理"));for(var h=0,I=s;h<I.length;h++){var U=I[h].match,c=U[1],M=U[2];l.has(M)||l.set(M,[]),l.get(M).push(c)}var b,N=[],p=Object(a["a"])(l.entries());try{for(p.s();!(b=p.n()).done;){var E=Object(d["a"])(b.value,2),O=E[0],D=E[1];N.push({methodId:u.BAMA_ZULIU,betNumbers:D.join(","),money:O})}}catch(S){p.e(S)}finally{p.f()}return console.log("【BAMA_MULTI_LINE_ZULIU_MONEY_SUPER】处理完成，返回 ".concat(N.length," 个结果组")),N.length?N:null}},BAMA_MULTI_FANGDUI_SUPER:{pattern:/^([0-9]{8}(?:[\-—–―－﹣﹘⸺⸻~～@#￥%…&!、\\/*,，.一－。＝=·\s+吊赶打各]+[0-9]{8})+)[\-—–―－﹣﹘⸺⸻~～@#￥%…&!、\\/*,，.一－。＝=·\s+吊赶打各]*(防对|防|组三)([0-9]+)$/,handler:function(e,n){var r=t("f9d6"),d=r.SUPER_SEPARATORS,a=r.METHOD_ID,o=e[1].split(d).map((function(e){return e.trim()})).filter((function(e){return 8===e.length})),u=e[3];return o.length<2?null:[{methodId:a.BAMA_ZUSAN,betNumbers:o.join(","),money:u}]}},BAMA_MULTI_LINE_ZULIU_ZUSAN_KEYWORD:{pattern:/^([0-9]{8}(组六|组三)\d+(元|米|块)?[，,./\*\-\=。一－。·、\s]*\n?)+$/m,handler:function(e,n){var t,r=n.split(/\r?\n/).map((function(e){return e.trim()})).filter(Boolean),u=new Map,A=Object(a["a"])(r);try{for(A.s();!(t=A.n()).done;){var l=t.value,_=l.match(/^([0-9]{8})(组六|组三)(\d+)(元|米|块)?[，,./\*\-\=。一－。·、\s]*$/);if(_&&8===_[1].length){var s=_[1],i=_[2],m=_[3],f=i+"_"+m;u.has(f)||u.set(f,[]),u.get(f).push(s)}}}catch(D){A.e(D)}finally{A.f()}var h,I=[],U=Object(a["a"])(u.entries());try{for(U.s();!(h=U.n()).done;){var c=Object(d["a"])(h.value,2),M=c[0],b=c[1],N=M.split("_"),p=Object(d["a"])(N,2),E=p[0],O=p[1];I.push({methodId:"组六"===E?o["METHOD_ID"].BAMA_ZULIU:o["METHOD_ID"].BAMA_ZUSAN,betNumbers:b.join(","),money:O})}}catch(D){U.e(D)}finally{U.f()}return I.length?I:null}},BAMA_MONEY_UNIT_FANGDUI_SUPER:{pattern:/^(\d{8})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:元|米|块)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(防|防对|组三)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*$/,handler:function(e,n){var t=e[1],r=e[2];e[3];return 8!==t.length?null:[{methodId:o["METHOD_ID"].BAMA_ZUSAN,betNumbers:t,money:r}]}},BAMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER:{pattern:/^([\s\S]+)$/m,handler:function(e,n){var r=/(福彩|福|3D|3d)/.test(n),o=/(体彩|体|排|排三)/.test(n);if(r||o)return null;var u,A=n.split(/\r?\n/).map((function(e){return e.trim()})).filter(Boolean),l=new Map,_=new Map,s=/^([0-9]{8})[\-—~～@#￥%…&!、\\/*,一－–.。＝=·\s\/]+(\d+)\++(\d+)[元米块]*$/,i=/^([0-9]{8})[\-—~～@#￥%…&!、\\/*,一－–.。＝=·\s\/]+(\d+)[元米块]*$/,m=[],f=Object(a["a"])(A);try{for(f.s();!(u=f.n()).done;){var h=u.value,I=h.match(s);if(I)m.push({line:h,type:"zuliuZusan",match:I});else{if(I=h.match(i),!I)return console.log('【BAMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】行 "'.concat(h,'" 不匹配八码格式，返回null')),null;m.push({line:h,type:"zuliu",match:I})}}}catch(P){f.e(P)}finally{f.f()}console.log("【BAMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】所有 ".concat(m.length," 行都是八码格式，继续处理"));for(var U=0,c=m;U<c.length;U++){var M=c[U],b=M.match,N=M.type;if("zuliuZusan"===N){var p=b[1],E=b[2],O=b[3];l.has(E)||l.set(E,[]),l.get(E).push(p),_.has(O)||_.set(O,[]),_.get(O).push(p)}else if("zuliu"===N){var D=b[1],S=b[2];l.has(S)||l.set(S,[]),l.get(S).push(D)}}var B,L=[],T=Object(a["a"])(l.entries());try{for(T.s();!(B=T.n()).done;){var v=Object(d["a"])(B.value,2),y=v[0],Z=v[1];L.push({methodId:t("f9d6").METHOD_ID.BAMA_ZULIU,betNumbers:Z.join(","),money:y})}}catch(P){T.e(P)}finally{T.f()}var R,j=Object(a["a"])(_.entries());try{for(j.s();!(R=j.n()).done;){var g=Object(d["a"])(R.value,2),H=g[0],$=g[1];L.push({methodId:t("f9d6").METHOD_ID.BAMA_ZUSAN,betNumbers:$.join(","),money:H})}}catch(P){j.e(P)}finally{j.f()}return console.log("【BAMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】处理完成，返回 ".concat(L.length," 个结果组")),L.length?L:null}},QIMA_TO_BAIMA_MULTI_TRANSFORM:{pattern:/^((?:\d{7}[\-—~～@#￥%…&!、\\/*,，.＝=·\s]+){1,}\d{7})(各)?(转|套)[\s]*([0-9]+)\+([0-9]+)$/,handler:function(e,n){var d=t("f9d6"),a=d.SUPER_SEPARATORS,o=e[1],u=e[4],A=e[5],l=o.split(a).filter((function(e){return 7===e.length})),_=[];return l.forEach((function(e){var n=e.split(""),t="0123456789".split("").filter((function(e){return!n.includes(e)})),d=t.map((function(n){return e+n})).sort(),a=d.filter((function(e){return!_.includes(e)}));_.push.apply(_,Object(r["a"])(a))})),[{methodId:t("f9d6").METHOD_ID.BAMA_ZULIU,betNumbers:_.join(","),money:u},{methodId:t("f9d6").METHOD_ID.BAMA_ZUSAN,betNumbers:_.join(","),money:A}]}}};Object.keys(A).forEach((function(e){var n=A[e].pattern;"string"===typeof n&&n.endsWith("$")?A[e].pattern=n.replace(/\$$/,"[，,./*-=。·、s]*$"):n instanceof RegExp&&n.source.endsWith("$")&&(A[e].pattern=new RegExp(n.source.replace(/\$$/,"[，,./*-=。·、s]*$")))}))}}]);