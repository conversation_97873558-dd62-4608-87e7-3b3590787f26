(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7910d0ad"],{"1e8b":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"user-info-container"},[e._m(0),r("el-form",{ref:"form",staticClass:"user-info-form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[r("el-row",{attrs:{gutter:24}},[r("el-col",{attrs:{lg:12,md:24}},[r("el-form-item",{attrs:{label:"用户昵称",prop:"nickName"}},[r("div",{staticClass:"input-wrapper"},[r("el-input",{staticClass:"form-input",attrs:{maxlength:"30",placeholder:"请输入用户昵称","prefix-icon":"el-icon-user"},model:{value:e.form.nickName,callback:function(t){e.$set(e.form,"nickName",t)},expression:"form.nickName"}})],1)])],1),r("el-col",{attrs:{lg:12,md:24}},[r("el-form-item",{attrs:{label:"手机号码",prop:"phonenumber"}},[r("div",{staticClass:"input-wrapper"},[r("el-input",{staticClass:"form-input",attrs:{maxlength:"11",placeholder:"请输入手机号码","prefix-icon":"el-icon-phone"},model:{value:e.form.phonenumber,callback:function(t){e.$set(e.form,"phonenumber",t)},expression:"form.phonenumber"}})],1)])],1)],1),r("el-row",{attrs:{gutter:24}},[r("el-col",{attrs:{lg:12,md:24}},[r("el-form-item",{attrs:{label:"邮箱地址",prop:"email"}},[r("div",{staticClass:"input-wrapper"},[r("el-input",{staticClass:"form-input",attrs:{maxlength:"50",placeholder:"请输入邮箱地址","prefix-icon":"el-icon-message"},model:{value:e.form.email,callback:function(t){e.$set(e.form,"email",t)},expression:"form.email"}})],1)])],1),r("el-col",{attrs:{lg:12,md:24}},[r("el-form-item",{attrs:{label:"性别"}},[r("div",{staticClass:"radio-wrapper"},[r("el-radio-group",{staticClass:"gender-radio",model:{value:e.form.sex,callback:function(t){e.$set(e.form,"sex",t)},expression:"form.sex"}},[r("el-radio",{staticClass:"gender-option",attrs:{label:"0"}},[r("i",{staticClass:"el-icon-male"}),e._v(" 男 ")]),r("el-radio",{staticClass:"gender-option",attrs:{label:"1"}},[r("i",{staticClass:"el-icon-female"}),e._v(" 女 ")])],1)],1)])],1)],1),r("div",{staticClass:"form-actions"},[r("el-button",{staticClass:"save-btn",attrs:{type:"primary"},on:{click:e.submit}},[r("i",{staticClass:"el-icon-check"}),e._v(" 保存修改 ")]),r("el-button",{staticClass:"cancel-btn",on:{click:e.close}},[r("i",{staticClass:"el-icon-close"}),e._v(" 取消 ")])],1)],1)],1)},s=[function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"form-header"},[r("h4",[e._v("基本信息")]),r("p",[e._v("更新您的个人基本信息")])])}],l=r("c0c7"),i={props:{user:{type:Object}},data:function(){return{form:{},rules:{nickName:[{required:!0,message:"用户昵称不能为空",trigger:"blur"}],email:[{required:!0,message:"邮箱地址不能为空",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:["blur","change"]}],phonenumber:[{required:!0,message:"手机号码不能为空",trigger:"blur"},{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"请输入正确的手机号码",trigger:"blur"}]}}},watch:{user:{handler:function(e){e&&(this.form={nickName:e.nickName,phonenumber:e.phonenumber,email:e.email,sex:e.sex})},immediate:!0}},methods:{submit:function(){var e=this;this.$refs["form"].validate((function(t){t&&Object(l["updateUserProfile"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.user.phonenumber=e.form.phonenumber,e.user.email=e.form.email,e.$emit("refresh")}))}))},close:function(){this.$tab.closePage()}}},o=i,n=(r("fd1a"),r("2877")),c=Object(n["a"])(o,a,s,!1,null,"02063764",null);t["default"]=c.exports},"30d7":function(e,t,r){},fd1a:function(e,t,r){"use strict";r("30d7")}}]);