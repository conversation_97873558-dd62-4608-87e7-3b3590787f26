{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetStatistics.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetStatistics.vue", "mtime": 1756001841596}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\babel.config.js", "mtime": 1750852368688}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750942928175}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750942928768}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}