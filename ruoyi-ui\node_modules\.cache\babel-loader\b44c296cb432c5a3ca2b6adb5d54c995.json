{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetStatistics.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetStatistics.vue", "mtime": 1756000107447}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\babel.config.js", "mtime": 1750852368688}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750942928175}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750942928768}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}