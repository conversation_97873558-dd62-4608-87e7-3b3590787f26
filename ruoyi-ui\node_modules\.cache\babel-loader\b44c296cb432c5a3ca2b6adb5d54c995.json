{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetStatistics.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetStatistics.vue", "mtime": 1756001680527}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\babel.config.js", "mtime": 1750852368688}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750942928175}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750942928768}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}