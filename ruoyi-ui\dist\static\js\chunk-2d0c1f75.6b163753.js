(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0c1f75"],{"47fb":function(e,n,t){"use strict";t.r(n),t.d(n,"FIVE_CODE_PATTERNS",(function(){return U}));var r=t("2909"),d=t("3835"),a=t("b85c"),o=(t("99af"),t("4de4"),t("a15b"),t("d81d"),t("14d9"),t("4ec9"),t("b64b"),t("d3b7"),t("4d63"),t("c607"),t("ac1f"),t("2c3e"),t("00b4"),t("25f0"),t("8a79"),t("3ca3"),t("466d"),t("5319"),t("1276"),t("498a"),t("0643"),t("76d6"),t("2382"),t("4e3e"),t("a573"),t("9a9a"),t("159b"),t("ddb0"),t("f9d6")),U={WUMA_TRANSFORM:{pattern:/^(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?(\d{5})[\-—~～@#￥%…&!、\\/*,，.＝=各·\s]*?(转|套|拖)(?:各)?(\d+)(?:\+(\d+))?(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?$/,handler:function(e,n){var r=e[2],d=e[4],a=e[5],o=e[6],U=t("f9d6").generateTransformNumbers(r),l=[];return d?l.push({methodId:t("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:U.join(","),money:d}):a&&o&&(l.push({methodId:t("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:U.join(","),money:a}),l.push({methodId:t("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:U.join(","),money:o})),l}},WUMA_ZULIU_ZULIU_KEYWORD:{pattern:/^([0-9]{5})组六?(\d+)(元|米|块)?[，,./\*\-\=。一－。·、\s]*$/,handler:function(e,n){var t=e[1];if(5!==t.length)return null;var r=e[2]||"";return[{methodId:o["METHOD_ID"].WUMA_ZULIU,betNumbers:t,money:r}]}},WUMA_ZULIU:{pattern:/^(\d{5})(?!组三)(组六)?[\-—~～@#￥%…&!、\\/*,，.＝=· \t]*(\d+)?$/,handler:function(e,n){var t=e[1],r=(e[2],e[3]||""),d=[];return r&&d.push({methodId:o["METHOD_ID"].WUMA_ZULIU,betNumbers:t,money:r}),console.log("五码 handler 返回:",d),d}},WUMA_ZUSAN:{pattern:/^(\d{5})[\-—~～@#￥%…&!、\\/*,，一－.＝=·\s]*组三[\-—~～@#￥%…&!、\\/*。，一－,，.＝=·\s]*(\d+)(元|块|米)?[\-—~～@#￥%…&!、。，一－\\/*,，.＝=·\s]*$/,handler:function(e,n){return 5!==e[1].length?null:[{methodId:o["METHOD_ID"].WUMA_ZUSAN,betNumbers:e[1],money:e[2]||""}]}},WUMA_FANGDUI:{pattern:/^(\d{5})防对?(?:-?(\d+))?$/,handler:function(e,n){return 5!==e[1].length?null:[{methodId:o["METHOD_ID"].WUMA_ZUSAN,betNumbers:e[1],money:e[2]||""}]}},MULTI_WUMA:{pattern:/^(\d{5}(?:[^0-9]+\d{5})*)(?:各([一二三四五六七八九十百千万\d]+))?(?:防([一二三四五六七八九十百千万\d]+))?$/,handler:function(e,n){var r=t("f9d6"),d=r.chineseToNumber,a=n.split(o["BASE_PATTERNS"].SEPARATORS).filter(Boolean).filter((function(e){return 5===e.length}));if(!a.length||!a.every((function(e){return 5===e.length})))return null;var U=e[2]?d(e[2]):"",l=e[3]?d(e[3]):"",u=[];return U&&u.push({methodId:o["METHOD_ID"].WUMA_ZULIU,betNumbers:a.join(","),money:U}),l&&u.push({methodId:o["METHOD_ID"].WUMA_ZUSAN,betNumbers:a[a.length-1],money:l}),u.length?u:null}},WUMA_COMBO:{pattern:/^(\d{5}(?:\/\d{5})*?)\/(\d+)\+(\d+)$/,handler:function(e,n){var t=n.split("/"),r=t.pop().split("+")[0],d=r,a=t.pop().split("+")[1],U=[];return t.forEach((function(e){5===e.length&&(U.push({methodId:o["METHOD_ID"].WUMA_ZULIU,betNumbers:e,money:d}),U.push({methodId:o["METHOD_ID"].WUMA_ZUSAN,betNumbers:e,money:a}))})),console.log("五码 handler 返回:",U),U}},WUMA_ZULIU_ZUSAN_MULTI_MONEY_SUPER:{pattern:/^(\d{5}(?:[－\-—~～@#￥%…&!、.\\/*,一－。，＝=·\s+吊赶打各]+\d{5})*)[\-—~～@#￥%…&!、\\/*,一－.。，＝=·\s+吊赶打各]+(\d+)[\-—~～@#￥%…&!、\\/*,一－。，.＝=·\s+吊赶打各]*[\+\-\\/*、.]+[\-—~～@#￥%…&!、\\/*,一－。＝.，=·\s+吊赶打各]*(\d+)(?:元)?$/,handler:function(e,n){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],d=t("f9d6"),a=d.SUPER_SEPARATORS,o=e[1].split(a).filter(Boolean),U=e[2],l=e[3];return r&&(/^\d{5}$/.test(U)||/^\d{5}$/.test(l))?null:[{methodId:t("f9d6").METHOD_ID.WUMA_ZULIU,betNumbers:o.join(","),money:U},{methodId:t("f9d6").METHOD_ID.WUMA_ZUSAN,betNumbers:o.join(","),money:l}]}},WUMA_ZULIU_ZU_SUPER:{pattern:/^(\d{5})组(\d+)$/,handler:function(e,n){var r=e[1],d=e[2];return[{methodId:t("f9d6").METHOD_ID.WUMA_ZULIU,betNumbers:r,money:d}]}},WUMA_ZULIU_MULTI_SUPER:{pattern:/^(\d{5}(?:[\-—~～@#￥%…&!、\\/*,一－。＝=·\s+吊赶打各]+\d{5})*)[\-—~～@#￥%…&!、\\/*,一－。＝=·\s+吊赶打各]+(\d+)$/,handler:function(e,n){var r=t("f9d6"),d=r.SUPER_SEPARATORS,a=e[1].split(d).filter(Boolean),o=e[2];return[{methodId:t("f9d6").METHOD_ID.WUMA_ZULIU,betNumbers:a.join(","),money:o}]}},WUMA_ZUSAN_ZU_SUPER:{pattern:/^(\d{5})组三(\d+)$/,handler:function(e,n){var r=e[1],d=e[2];return[{methodId:t("f9d6").METHOD_ID.WUMA_ZUSAN,betNumbers:r,money:d}]}},WUMA_ZHIZU_MULTI_SUPER:{pattern:/^(\d{5}(?:[\-—~～@#￥%…&!、\\/*,一－。＝=·\s+吊赶打各]+\d{5})*)[\-—~～@#￥%…&!、\\/*,一－。＝=·\s+吊赶打各]*(直组|组直|值组|组值)[\s]*各?(\d+)(?:元)?$/,handler:function(e,n){var r=t("f9d6"),d=r.SUPER_SEPARATORS,a=e[1].split(d).filter(Boolean),o=e[3];return[{methodId:t("f9d6").METHOD_ID.WUMA_ZULIU,betNumbers:a.join(","),money:o},{methodId:t("f9d6").METHOD_ID.WUMA_ZUSAN,betNumbers:a.join(","),money:o}]}},WUMA_ZULIU_FANG:{pattern:/^(\d{5})-(\d+)防(\d+)$/,handler:function(e,n){var t=e[1],r=e[2],d=e[3];return[{methodId:o["METHOD_ID"].WUMA_ZULIU,betNumbers:t,money:r},{methodId:o["METHOD_ID"].WUMA_ZUSAN,betNumbers:t,money:d}]}},WUMA_MULTI_ZULIU_ZUSAN_SLASH_SUPER:{pattern:/^([\d、,，.。\\/*\s\n\r\-—~～@#￥%…&!、一－。＝=·吊赶打各]+)\/(\d+)\+(\d+)$/,handler:function(e,n){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],d=t("f9d6"),o=d.SUPER_SEPARATORS,U=n.split(/[\r\n]+/).map((function(e){return e.trim()})).filter(Boolean);if(U.length>1){var l,u=/^\d{5}\/\d+\+\d+$/,_=Object(a["a"])(U);try{for(_.s();!(l=_.n()).done;){var s=l.value;if(!u.test(s))return null}}catch(f){_.e(f)}finally{_.f()}}var i=e[1].split(o).filter((function(e){return 5===e.length})),m=e[2],h=e[3];return r&&(/^\d{5}$/.test(m)||/^\d{5}$/.test(h))?null:[{methodId:t("f9d6").METHOD_ID.WUMA_ZULIU,betNumbers:i.join(","),money:m},{methodId:t("f9d6").METHOD_ID.WUMA_ZUSAN,betNumbers:i.join(","),money:h}]}},WUMA_MULTI_ZHIZU_EACH_SUPER:{pattern:/^(直组|组直|值组|组值)各([0-9]+)[\s\-—~～@#￥%…&!、\\/*,一－。＝=·吊赶打各\n]+([\d\s\-—~～@#￥%…&!、\\/*,一－。＝=·吊赶打各]+)$/i,handler:function(e,n){var r=t("f9d6"),d=r.SUPER_SEPARATORS,a=e[2],o=e[3].replace(/\n/g," "),U=o.split(d).map((function(e){return e.trim()})).filter((function(e){return/^\d{5}$/.test(e)}));return[{methodId:t("f9d6").METHOD_ID.WUMA_ZULIU,betNumbers:U.join(","),money:a},{methodId:t("f9d6").METHOD_ID.WUMA_ZUSAN,betNumbers:U.join(","),money:a}]}},WUMA_MULTI_EACH_SUPER:{pattern:/^(\d{5}(?:[\-~～@#￥%…&!、\\/*,一－。＝=·\s+吊赶打各]+\d{5})*)各(\d+)$/,handler:function(e,n){var r=t("f9d6"),d=r.SUPER_SEPARATORS,a=e[1].split(d).filter(Boolean);if(!a.length||a.some((function(e){return 5!==e.length})))return[];var o=e[2];return[{methodId:t("f9d6").METHOD_ID.WUMA_ZULIU,betNumbers:a.join(","),money:o}]}},WUMA_UNIVERSAL_SUPER:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、一—－。＝=·吊赶打各]+)[\-~～@#￥%…&!、\\/*,一—－。＝=·\s+吊赶打各]+([0-9]+)(元|米|块)?$/,handler:function(e,n){var r=t("f9d6"),d=r.SUPER_SEPARATORS,a=r.METHOD_ID,o=e[1].split(d).map((function(e){return e.trim()})).filter(Boolean),U=e[2];return 1===o.length&&5===o[0].length?[{methodId:a.WUMA_ZULIU,betNumbers:o[0],money:U}]:o.length>=2&&o.every((function(e){return 5===e.length}))?[{methodId:a.WUMA_ZULIU,betNumbers:o.join(","),money:U}]:null}},WUMA_EACH_PLUS_SUPER:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、一－。＝=·吊赶打各]+)各([0-9]+)\+([0-9]+)$/,handler:function(e,n){var r=t("f9d6"),d=r.SUPER_SEPARATORS,a=r.METHOD_ID,o=e[1].split(d).map((function(e){return e.trim()})).filter(Boolean),U=e[2],l=e[3];return 1===o.length&&5===o[0].length?[{methodId:a.WUMA_ZULIU,betNumbers:o[0],money:U},{methodId:a.WUMA_ZUSAN,betNumbers:o[0],money:l}]:o.length>=2&&o.every((function(e){return 5===e.length}))?[{methodId:a.WUMA_ZULIU,betNumbers:o.join(","),money:U},{methodId:a.WUMA_ZUSAN,betNumbers:o.join(","),money:l}]:null}},WUMA_WHOLE_MONEY:{pattern:/^([0-9]{5,})\s+(\d+)(元|块|米)?$/,handler:function(e,n){var r=e[1],d=e[2];return r&&5===r.length?[{methodId:t("f9d6").METHOD_ID.WUMA_ZULIU,betNumbers:r,money:d}]:null}},WUMA_DA_MONEY:{pattern:/^([0-9]{5})打(\d+)(元|块|米)?$/,handler:function(e,n){return[{methodId:t("f9d6").METHOD_ID.WUMA_ZULIU,betNumbers:e[1],money:e[2]}]}},WUMA_MULTI_WITH_MONEY_SLASH_PLUS:{pattern:/^(\d{5}(?:[\s,，\-]+?\d{5})*)\/\s*(\d+)\+(\d+)$/,handler:function(e,n){var r=e[1].split(/[\s,，\-]+/).filter(Boolean),d=e[2],a=e[3];return[{methodId:t("f9d6").METHOD_ID.WUMA_ZULIU,betNumbers:r.join(","),money:d},{methodId:t("f9d6").METHOD_ID.WUMA_ZUSAN,betNumbers:r.join(","),money:a}]}},WUMA_MULTI_LINE_MONEY_PLUS:{pattern:/^((?:\d{5}[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+\d+\+\d+\s*\n?)+)$/m,handler:function(e,n){var r,o=t("f9d6"),U=o.METHOD_ID,l=(o.SUPER_SEPARATORS,n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean)),u=new Map,_=!0,s=Object(a["a"])(l);try{for(s.s();!(r=s.n()).done;){var i=r.value,m=i.match(/^(\d{5})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+(\d+)\+(\d+)$/);if(!m){_=!1;break}var h=m[1];if(5!==h.length){_=!1;break}var f=i.split(/[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+/)[0];if(5!==f.length||!/^\d{5}$/.test(f)){_=!1;break}var I="".concat(m[2],"+").concat(m[3]);u.has(I)||u.set(I,[]),u.get(I).push(h)}}catch(L){s.e(L)}finally{s.f()}if(!_||0===u.size)return null;var M,c=[],A=Object(a["a"])(u.entries());try{for(A.s();!(M=A.n()).done;){var b=Object(d["a"])(M.value,2),N=b[0],p=b[1],E=N.split("+"),O=Object(d["a"])(E,2),D=O[0],S=O[1];c.push({methodId:U.WUMA_ZULIU,betNumbers:p.join(","),money:D}),c.push({methodId:U.WUMA_ZUSAN,betNumbers:p.join(","),money:S})}}catch(L){A.e(L)}finally{A.f()}return c.length?c:null}},WUMA_MULTI_LINE_MONEY_PLUS_CN:{pattern:/^((?:\d{5}[，,、]?[一二三四五六七八九十百千万\d]+加[一二三四五六七八九十百千万\d]+\s*\n?)+)$/m,handler:function(e,n){var r,o=t("f9d6"),U=o.METHOD_ID,l=o.chineseToNumber,u=n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),_=new Map,s=Object(a["a"])(u);try{for(s.s();!(r=s.n()).done;){var i=r.value,m=i.match(/^(\d{5})[，,、]?([一二三四五六七八九十百千万\d]+)加([一二三四五六七八九十百千万\d]+)$/);if(m){var h=m[1],f=l(m[2]),I=l(m[3]),M="".concat(f,"+").concat(I);_.has(M)||_.set(M,[]),_.get(M).push(h)}}}catch(v){s.e(v)}finally{s.f()}var c,A=[],b=Object(a["a"])(_.entries());try{for(b.s();!(c=b.n()).done;){var N=Object(d["a"])(c.value,2),p=N[0],E=N[1],O=p.split("+"),D=Object(d["a"])(O,2),S=D[0],L=D[1];A.push({methodId:U.WUMA_ZULIU,betNumbers:E.join(","),money:S}),A.push({methodId:U.WUMA_ZUSAN,betNumbers:E.join(","),money:L})}}catch(v){b.e(v)}finally{b.f()}return A.length?A:null}},WUMA_MULTI_EACH_FANGDUI_SLASH:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、一－。＝=·吊赶打各]+)[/\-~～@#￥%…&!、\\/*,一－。＝=·吊赶打各]+各防对([一二三四五六七八九十百千万\d]+)$/,handler:function(e,n){var r=t("f9d6"),d=r.SUPER_SEPARATORS,a=r.METHOD_ID,o=r.chineseToNumber,U=e[1].split(d).map((function(e){return e.trim()})).filter((function(e){return 5===e.length})),l=o(e[2]);return U.length?[{methodId:a.WUMA_ZUSAN,betNumbers:U.join(","),money:l}]:null}},WUMA_MULTI_EACH_FANGDUI:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、一－。＝=·吊赶打各]+)各防对([一二三四五六七八九十百千万\d]+)$/,handler:function(e,n){var r=t("f9d6"),d=r.SUPER_SEPARATORS,a=r.METHOD_ID,o=r.chineseToNumber,U=e[1].split(d).map((function(e){return e.trim()})).filter((function(e){return 5===e.length})),l=o(e[2]);return U.length?[{methodId:a.WUMA_ZUSAN,betNumbers:U.join(","),money:l}]:null}},WUMA_SLASH_MONEY:{pattern:/^\s*(\d{5})\s*\/\s*([一二三四五六七八九十百千万\d]+)\s*$/,handler:function(e,n){var r=t("f9d6"),d=r.METHOD_ID,a=r.chineseToNumber,o=e[1],U=a(e[2]);return[{methodId:d.WUMA_ZULIU,betNumbers:o,money:U}]}},WUMA_ZULIU_ZUSAN_MONEY_PAIR:{pattern:/^(\d{5}[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*)组六[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*(\d+)[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*组三[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*(\d+)$/,handler:function(e,n){var r=e[1].replace(/[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]/g,""),d=e[2],a=e[3];return[{methodId:t("f9d6").METHOD_ID.WUMA_ZULIU,betNumbers:r,money:d},{methodId:t("f9d6").METHOD_ID.WUMA_ZUSAN,betNumbers:r,money:a}]}},WUMA_ZULIU_ZUSAN_COMBO:{pattern:/^(\d{5}(?:[。.．、,，\s]+\d{5})*)\/(\d+)\+(\d+)$/,handler:function(e,n){var r=t("f9d6"),d=(r.SUPER_SEPARATORS,e[1].split(/[。.．、,，\s]+/).map((function(e){return e.trim()})).filter(Boolean)),a=e[2],o=e[3];return d.length&&d.every((function(e){return 5===e.length}))?[{methodId:t("f9d6").METHOD_ID.WUMA_ZULIU,betNumbers:d.join(","),money:a},{methodId:t("f9d6").METHOD_ID.WUMA_ZUSAN,betNumbers:d.join(","),money:o}]:null}},WUMA_SINGLE_LINE_ZULIU_ZUSAN_PLUS:{pattern:/^(\d{5})\s+(\d+)[+＋](\d+)$/,handler:function(e,n){return[{methodId:t("f9d6").METHOD_ID.WUMA_ZULIU,betNumbers:e[1],money:e[2]},{methodId:t("f9d6").METHOD_ID.WUMA_ZUSAN,betNumbers:e[1],money:e[3]}]}},WUMA_MULTI_LINE_ZULIU_PLUS:{pattern:/^(\d{5})[+＋\\-—~～@#￥%…&!、\\/*,，.＝=·吊赶打各]+(\d+)$/,handler:function(e,n){var r,o=t("f9d6"),U=o.METHOD_ID,l=n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),u=new Map,_=[],s=Object(a["a"])(l);try{for(s.s();!(r=s.n()).done;){var i=r.value,m=i.match(/^(\d{5})[+＋\\-—~～@#￥%…&!、\\/*,，.＝=·吊赶打各]+(\d+)$/);if(m){var h=m[1],f=m[2];u.has(f)||u.set(f,[]),u.get(f).push(h)}else _.push(i)}}catch(p){s.e(p)}finally{s.f()}var I,M=[],c=Object(a["a"])(u.entries());try{for(c.s();!(I=c.n()).done;){var A=Object(d["a"])(I.value,2),b=A[0],N=A[1];M.push({methodId:U.WUMA_ZULIU,betNumbers:N.join(","),money:b})}}catch(p){c.e(p)}finally{c.f()}return M}},WUMA_ZULIU_FANG_ZUSAN_PLUS:{pattern:/^(\d{5})[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s+吊赶打各]*(\d+)[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s+吊赶打各]*(防对|防)(\d+)$/,handler:function(e,n){return[{methodId:t("f9d6").METHOD_ID.WUMA_ZULIU,betNumbers:e[1],money:e[2]},{methodId:t("f9d6").METHOD_ID.WUMA_ZUSAN,betNumbers:e[1],money:e[4]}]}},WUMA_MULTI_LINE_ZULIU_FANG_ZUSAN_GROUP:{pattern:/^([\s\S]+)$/m,handler:function(e,n){var r,o=n.split(/[\r\n]+/).map((function(e){return e.trim()})).filter(Boolean),U=new Map,l=Object(a["a"])(o);try{for(l.s();!(r=l.n()).done;){var u=r.value,_=u.match(/^(\d{5})-?(\d+)防(\d+)$/);if(_){var s=_[1],i=_[2],m=_[3],h="".concat(i,"|").concat(m);U.has(h)||U.set(h,[]),U.get(h).push(s)}}}catch(D){l.e(D)}finally{l.f()}var f,I=[],M=Object(a["a"])(U.entries());try{for(M.s();!(f=M.n()).done;){var c=Object(d["a"])(f.value,2),A=c[0],b=c[1],N=A.split("|"),p=Object(d["a"])(N,2),E=p[0],O=p[1];I.push({methodId:t("f9d6").METHOD_ID.WUMA_ZULIU,betNumbers:b.join(","),money:E}),I.push({methodId:t("f9d6").METHOD_ID.WUMA_ZUSAN,betNumbers:b.join(","),money:O})}}catch(D){M.e(D)}finally{M.f()}return I.length?I:null}},WUMA_MULTI_TRANSFORM_EACH:{pattern:/^(\d{5}(?:[\-~～@#￥%…&!、\\/*一－。,，.＝=·\s+吊赶打各]+\d{5})*)(转|转各|套|套各)(\d+)\+(\d+)$/,handler:function(e,n){var r,d=t("f9d6"),o=d.SUPER_SEPARATORS,U=d.METHOD_ID,l=d.generateTransformNumbers,u=e[1].split(o).filter(Boolean),_=e[3],s=e[4],i=[],m=Object(a["a"])(u);try{for(m.s();!(r=m.n()).done;){var h=r.value,f=l(h);i.push({methodId:U.LIUMA_ZULIU,betNumbers:f.join(","),money:_}),i.push({methodId:U.LIUMA_ZUSAN,betNumbers:f.join(","),money:s})}}catch(I){m.e(I)}finally{m.f()}return i}},WUMA_MULTI_LINE_TRANSFORM:{pattern:/^((?:(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?\d{5}转\d+(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?\s*\n?)+)$/m,handler:function(e,n){var o,U=t("f9d6"),l=U.generateTransformNumbers,u=U.METHOD_ID,_=n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),s=new Map,i=Object(a["a"])(_);try{for(i.s();!(o=i.n()).done;){var m=o.value,h=m.match(/^(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?(\d{5})转(\d+)(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?$/);if(h){var f,I=h[2],M=h[3];s.has(M)||s.set(M,[]),(f=s.get(M)).push.apply(f,Object(r["a"])(l(I)))}}}catch(O){i.e(O)}finally{i.f()}var c,A=[],b=Object(a["a"])(s.entries());try{for(b.s();!(c=b.n()).done;){var N=Object(d["a"])(c.value,2),p=N[0],E=N[1];A.push({methodId:u.LIUMA_ZULIU,betNumbers:E.join(","),money:p})}}catch(O){b.e(O)}finally{b.f()}return A.length?A:null}},WUMA_MULTI_COLON_EACH_SUPER:{pattern:/^(\d{5}(?::|：\d{5})*)各(\d+)$/,handler:function(e,n){var r=n.split(/:|：/).map((function(e){return e.trim()})).filter((function(e){return 5===e.length})),d=e[2];return r.length?[{methodId:t("f9d6").METHOD_ID.WUMA_ZULIU,betNumbers:r.join(","),money:d}]:null}},WUMA_MULTI_YIGE_EACH_PLUS:{pattern:/^([\d一]+)一([\d一]+)一?各?(\d+)\+(\d+)$/,handler:function(e,n){var r=n.split("一").map((function(e){return e.replace(/[^\d]/g,"")})).filter((function(e){return 5===e.length})),d=e[3],a=e[4];return r.length?[{methodId:t("f9d6").METHOD_ID.WUMA_ZULIU,betNumbers:r.join(","),money:d},{methodId:t("f9d6").METHOD_ID.WUMA_ZUSAN,betNumbers:r.join(","),money:a}]:null}},WUMA_DIAN_XMA_ZULIU_FANG_ZUSAN:{pattern:/^(\d{5})[。.．、,，\s]+5码(\d+)防(\d+)$/,handler:function(e,n){return[{methodId:t("f9d6").METHOD_ID.WUMA_ZULIU,betNumbers:e[1],money:e[2]},{methodId:t("f9d6").METHOD_ID.WUMA_ZUSAN,betNumbers:e[1],money:e[3]}]}},WUMA_MULTI_LINE_ZULIU_MONEY_GROUP:{pattern:/^((?:\d{5}\s+\d+\s*\n?)+)$/m,handler:function(e,n){var r,o=t("f9d6"),U=o.METHOD_ID,l=n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),u=new Map,_=Object(a["a"])(l);try{for(_.s();!(r=_.n()).done;){var s=r.value,i=s.match(/^(\d{5})\s+(\d+)$/);if(i){var m=i[1],h=i[2];u.has(h)||u.set(h,[]),u.get(h).push(m)}}}catch(N){_.e(N)}finally{_.f()}var f,I=[],M=Object(a["a"])(u.entries());try{for(M.s();!(f=M.n()).done;){var c=Object(d["a"])(f.value,2),A=c[0],b=c[1];I.push({methodId:U.WUMA_ZULIU,betNumbers:b.join(","),money:A})}}catch(N){M.e(N)}finally{M.f()}return I.length?I:null}},WUMA_MULTI_NUMBERS_EACH_FANG:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、一－。＝=·吊赶打各]+)各(\d+)(防|防对|放对)(\d+)$/,handler:function(e,n){var r=t("f9d6"),d=r.SUPER_SEPARATORS,a=r.METHOD_ID,o=e[1].split(d).map((function(e){return e.trim()})).filter((function(e){return 5===e.length}));if(!o.length)return null;var U=e[2],l=e[4];return[{methodId:a.WUMA_ZULIU,betNumbers:o.join(","),money:U},{methodId:a.WUMA_ZUSAN,betNumbers:o.join(","),money:l}]}},WUMA_YI_YI_ONE_MONEY:{pattern:/^(\d{5})一(\d{2})一个(\d+)(元|块|米)?$/,handler:function(e,n){var r=e[1],d=e[2],a=e[3];return 5!==r.length||5!==d.length?null:[{methodId:t("f9d6").METHOD_ID.WUMA_ZULIU,betNumbers:r+","+d,money:a}]}},WUMA_GE_MONEY:{pattern:/^(\d{5})[，,、\s]*个(\d+)(元|块|米)?$/,handler:function(e,n){var r=e[1],d=e[2];return 5!==r.length?null:[{methodId:t("f9d6").METHOD_ID.WUMA_ZULIU,betNumbers:r,money:d}]}},MULTI_LINE_WUMA_MONEY_GROUP:{pattern:/^((?:\d{5}[\-—~～@#￥%…&!、\\/*,一－。.＝=·吊赶打各\s]+\d+\s*\n?)+)$/m,handler:function(e,n){var r,o=t("f9d6"),U=o.METHOD_ID,l=n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),u=new Map,_=Object(a["a"])(l);try{for(_.s();!(r=_.n()).done;){var s=r.value,i=s.match(/^(\d{5})[\-—~～@#￥%…&!、\\/*,一－。.＝=·吊赶打各\s]+([0-9]+)$/);if(i){var m=i[1],h=i[2];u.has(h)||u.set(h,[]),u.get(h).push(m)}}}catch(N){_.e(N)}finally{_.f()}var f,I=[],M=Object(a["a"])(u.entries());try{for(M.s();!(f=M.n()).done;){var c=Object(d["a"])(f.value,2),A=c[0],b=c[1];I.push({methodId:U.WUMA_ZULIU,betNumbers:b.join(","),money:A})}}catch(N){M.e(N)}finally{M.f()}return I.length?I:null}},WUMA_MULTI_LINE_ZULIU_MONEY_SUPER:{pattern:/^([\s\S]+)$/m,handler:function(e,n){var r,o=t("f9d6"),U=o.METHOD_ID,l=n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),u=new Map,_=/^(\d{5})[\-—~～@#￥%…&!、一－。\\/*,，.＝=· 　\t]+(\d+)(元|米|块)?([，,./\\*\-\=。·、 　\t]*)?$/,s=[],i=Object(a["a"])(l);try{for(i.s();!(r=i.n()).done;){var m=r.value,h=m.match(_);if(!h)return console.log('【WUMA_MULTI_LINE_ZULIU_MONEY_SUPER】行 "'.concat(m,'" 不匹配五码格式，返回null')),null;if(h[1].length===h[2].length)return console.log('【WUMA_MULTI_LINE_ZULIU_MONEY_SUPER】行 "'.concat(m,'" 号码和金额长度相同，可能误匹配，返回null')),null;s.push({line:m,match:h})}}catch(g){i.e(g)}finally{i.f()}console.log("【WUMA_MULTI_LINE_ZULIU_MONEY_SUPER】所有 ".concat(s.length," 行都是五码格式，继续处理"));for(var f=0,I=s;f<I.length;f++){var M=I[f].match;if(5===M[1].length){var c=M[1],A=M[2],b=M[3]||"",N=A+b;u.has(N)||u.set(N,[]),u.get(N).push(c)}}var p,E=[],O=Object(a["a"])(u.entries());try{for(O.s();!(p=O.n()).done;){var D=Object(d["a"])(p.value,2),S=D[0],L=D[1];if(L.length>0){var v,T=S.length>0?[S.replace(/(元|米|块)$/,""),(null===(v=S.match(/(元|米|块)$/))||void 0===v?void 0:v[0])||""]:["",""],y=Object(d["a"])(T,2),W=y[0],Z=y[1];E.push({methodId:U.WUMA_ZULIU,betNumbers:L.join(","),money:W,unit:Z||void 0})}}}catch(g){O.e(g)}finally{O.f()}return console.log("【WUMA_MULTI_LINE_ZULIU_MONEY_SUPER】处理完成，返回 ".concat(E.length," 个结果组")),E}},WUMA_MULTI_FANGDUI_SUPER:{pattern:/^([0-9]{5}(?:[\-—–―－﹣﹘⸺⸻~～@#￥%…&!、\\/*,，.一－。＝=·\s+吊赶打各]+[0-9]{5})+)[\-—–―－﹣﹘⸺⸻~～@#￥%…&!、\\/*,，.一－。＝=·\s+吊赶打各]*(防对|防|组三)([0-9]+)$/,handler:function(e,n){var r=t("f9d6"),d=r.SUPER_SEPARATORS,a=r.METHOD_ID,o=e[1].split(d).map((function(e){return e.trim()})).filter((function(e){return 5===e.length})),U=e[3];return o.length<2?null:[{methodId:a.WUMA_ZUSAN,betNumbers:o.join(","),money:U}]}},WUMA_MONEY_UNIT_FANGDUI_SUPER:{pattern:/^(\d{5})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:元|米|块)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(防|防对|组三)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*$/,handler:function(e,n){var t=e[1],r=e[2];e[3];return 5!==t.length?null:[{methodId:o["METHOD_ID"].WUMA_ZUSAN,betNumbers:t,money:r}]}},WUMA_MULTI_LINE_ZULIU_ZUSAN_KEYWORD:{pattern:/^([0-9]{5}(组六|组三)\d+(元|米|块)?[，,./\*\-\=。一－。·、\s]*\n?)+$/m,handler:function(e,n){var t,r=n.split(/\r?\n/).map((function(e){return e.trim()})).filter(Boolean),U=new Map,l=Object(a["a"])(r);try{for(l.s();!(t=l.n()).done;){var u=t.value,_=u.match(/^([0-9]{5})(组六|组三)(\d+)(元|米|块)?[，,./\*\-\=。一－。·、\s]*$/);if(_&&5===_[1].length){var s=_[1],i=_[2],m=_[3],h=i+"_"+m;U.has(h)||U.set(h,[]),U.get(h).push(s)}}}catch(D){l.e(D)}finally{l.f()}var f,I=[],M=Object(a["a"])(U.entries());try{for(M.s();!(f=M.n()).done;){var c=Object(d["a"])(f.value,2),A=c[0],b=c[1],N=A.split("_"),p=Object(d["a"])(N,2),E=p[0],O=p[1];I.push({methodId:"组六"===E?o["METHOD_ID"].WUMA_ZULIU:o["METHOD_ID"].WUMA_ZUSAN,betNumbers:b.join(","),money:O})}}catch(D){M.e(D)}finally{M.f()}return I.length?I:null}},WUMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER:{pattern:/^([\s\S]+)$/m,handler:function(e,n){var r=/(福彩|福|3D|3d)/.test(n),o=/(体彩|体|排|排三)/.test(n);if(r||o)return null;var U,l=n.split(/\r?\n/).map((function(e){return e.trim()})).filter(Boolean),u=new Map,_=new Map,s=/^([0-9]{5})[\-—~～@#￥%…&!、\\/*,一－–.。＝=·\s\/]+(\d+)\++(\d+)[元米块]*$/,i=/^([0-9]{5})[\-—~～@#￥%…&!、\\/*,一－–.。＝=·\s\/]+(\d+)[元米块]*$/,m=[],h=Object(a["a"])(l);try{for(h.s();!(U=h.n()).done;){var f=U.value,I=f.match(s);if(I)m.push({line:f,type:"zuliuZusan",match:I});else{if(I=f.match(i),!I)return console.log('【WUMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】行 "'.concat(f,'" 不匹配五码格式，返回null')),null;m.push({line:f,type:"zuliu",match:I})}}}catch(P){h.e(P)}finally{h.f()}console.log("【WUMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】所有 ".concat(m.length," 行都是五码格式，继续处理"));for(var M=0,c=m;M<c.length;M++){var A=c[M],b=A.match,N=A.type;if("zuliuZusan"===N){var p=b[1],E=b[2],O=b[3];u.has(E)||u.set(E,[]),u.get(E).push(p),_.has(O)||_.set(O,[]),_.get(O).push(p)}else if("zuliu"===N){var D=b[1],S=b[2];u.has(S)||u.set(S,[]),u.get(S).push(D)}}var L,v=[],T=Object(a["a"])(u.entries());try{for(T.s();!(L=T.n()).done;){var y=Object(d["a"])(L.value,2),W=y[0],Z=y[1];v.push({methodId:t("f9d6").METHOD_ID.WUMA_ZULIU,betNumbers:Z.join(","),money:W})}}catch(P){T.e(P)}finally{T.f()}var g,R=Object(a["a"])(_.entries());try{for(R.s();!(g=R.n()).done;){var j=Object(d["a"])(g.value,2),H=j[0],$=j[1];v.push({methodId:t("f9d6").METHOD_ID.WUMA_ZUSAN,betNumbers:$.join(","),money:H})}}catch(P){R.e(P)}finally{R.f()}return console.log("【WUMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】处理完成，返回 ".concat(v.length," 个结果组")),v.length?v:null}}};Object.keys(U).forEach((function(e){var n=U[e].pattern;"string"===typeof n&&n.endsWith("$")?U[e].pattern=n.replace(/\$$/,"[，,./*-=。·、s]*$"):n instanceof RegExp&&n.source.endsWith("$")&&(U[e].pattern=new RegExp(n.source.replace(/\$$/,"[，,./*-=。·、s]*$")))}))}}]);