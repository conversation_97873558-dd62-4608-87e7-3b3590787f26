(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-240d66cb"],{9532:function(s,t,e){"use strict";e("b16f")},b16f:function(s,t,e){},ee46:function(s,t,e){"use strict";e.r(t);var r=function(){var s=this,t=s.$createElement,e=s._self._c||t;return e("div",{staticClass:"reset-pwd-container"},[s._m(0),s._m(1),e("el-form",{ref:"form",staticClass:"reset-pwd-form",attrs:{model:s.user,rules:s.rules,"label-width":"100px"}},[e("el-form-item",{attrs:{label:"当前密码",prop:"oldPassword"}},[e("div",{staticClass:"input-wrapper"},[e("el-input",{staticClass:"form-input",attrs:{placeholder:"请输入当前密码",type:"password","show-password":"","prefix-icon":"el-icon-lock"},model:{value:s.user.oldPassword,callback:function(t){s.$set(s.user,"oldPassword",t)},expression:"user.oldPassword"}})],1)]),e("el-form-item",{attrs:{label:"新密码",prop:"newPassword"}},[e("div",{staticClass:"input-wrapper"},[e("el-input",{staticClass:"form-input",attrs:{placeholder:"请输入新密码",type:"password","show-password":"","prefix-icon":"el-icon-key"},on:{input:s.checkPasswordStrength},model:{value:s.user.newPassword,callback:function(t){s.$set(s.user,"newPassword",t)},expression:"user.newPassword"}})],1),s.user.newPassword?e("div",{staticClass:"password-strength"},[e("div",{staticClass:"strength-label"},[s._v("密码强度：")]),e("div",{staticClass:"strength-bar"},[e("div",{staticClass:"strength-fill",class:s.passwordStrength.class,style:{width:s.passwordStrength.width}})]),e("span",{staticClass:"strength-text",class:s.passwordStrength.class},[s._v(" "+s._s(s.passwordStrength.text)+" ")])]):s._e()]),e("el-form-item",{attrs:{label:"确认密码",prop:"confirmPassword"}},[e("div",{staticClass:"input-wrapper"},[e("el-input",{staticClass:"form-input",attrs:{placeholder:"请再次输入新密码",type:"password","show-password":"","prefix-icon":"el-icon-check"},model:{value:s.user.confirmPassword,callback:function(t){s.$set(s.user,"confirmPassword",t)},expression:"user.confirmPassword"}})],1)]),e("div",{staticClass:"form-actions"},[e("el-button",{staticClass:"save-btn",attrs:{type:"primary"},on:{click:s.submit}},[e("i",{staticClass:"el-icon-check"}),s._v(" 修改密码 ")]),e("el-button",{staticClass:"cancel-btn",on:{click:s.close}},[e("i",{staticClass:"el-icon-close"}),s._v(" 取消 ")])],1)],1)],1)},a=[function(){var s=this,t=s.$createElement,e=s._self._c||t;return e("div",{staticClass:"form-header"},[e("h4",[s._v("修改密码")]),e("p",[s._v("为了账户安全，请定期更换密码")])])},function(){var s=this,t=s.$createElement,e=s._self._c||t;return e("div",{staticClass:"security-tips"},[e("div",{staticClass:"tip-item"},[e("i",{staticClass:"el-icon-info"}),e("span",[s._v("密码长度为6-20个字符")])]),e("div",{staticClass:"tip-item"},[e("i",{staticClass:"el-icon-info"}),e("span",[s._v("不能包含特殊字符：< > \" ' \\ |")])])])}],i=(e("d9e2"),e("14d9"),e("ac1f"),e("00b4"),e("c0c7")),o={data:function(){var s=this,t=function(t,e,r){s.user.newPassword!==e?r(new Error("两次输入的密码不一致")):r()};return{user:{oldPassword:void 0,newPassword:void 0,confirmPassword:void 0},passwordStrength:{width:"0%",class:"",text:""},rules:{oldPassword:[{required:!0,message:"旧密码不能为空",trigger:"blur"}],newPassword:[{required:!0,message:"新密码不能为空",trigger:"blur"},{min:6,max:20,message:"长度在 6 到 20 个字符",trigger:"blur"},{pattern:/^[^<>"'|\\]+$/,message:"不能包含非法字符：< > \" ' \\ |",trigger:"blur"}],confirmPassword:[{required:!0,message:"确认密码不能为空",trigger:"blur"},{required:!0,validator:t,trigger:"blur"}]}}},methods:{submit:function(){var s=this;this.$refs["form"].validate((function(t){t&&Object(i["updateUserPwd"])(s.user.oldPassword,s.user.newPassword).then((function(t){s.$modal.msgSuccess("密码修改成功"),s.resetForm()})).catch((function(t){s.$modal.msgError("密码修改失败："+(t.msg||t.message||"未知错误"))}))}))},close:function(){this.resetForm(),this.$tab.closePage()},resetForm:function(){var s=this;this.user={oldPassword:void 0,newPassword:void 0,confirmPassword:void 0},this.passwordStrength={width:"0%",class:"",text:""},this.$nextTick((function(){s.$refs.form&&s.$refs.form.clearValidate()}))},checkPasswordStrength:function(){var s=this.user.newPassword;if(s){var t=0,e=[];s.length>=8?t+=25:e.push("至少8位"),/\d/.test(s)?t+=25:e.push("包含数字"),/[a-z]/.test(s)?t+=25:e.push("包含小写字母"),/[A-Z]/.test(s)||/[!@#$%^&*(),.?":{}|<>]/.test(s)?t+=25:e.push("包含大写字母或特殊字符"),this.passwordStrength=t<=25?{width:"25%",class:"weak",text:"弱"}:t<=50?{width:"50%",class:"fair",text:"一般"}:t<=75?{width:"75%",class:"good",text:"良好"}:{width:"100%",class:"strong",text:"强"}}else this.passwordStrength={width:"0%",class:"",text:""}}}},n=o,l=(e("9532"),e("2877")),c=Object(l["a"])(n,r,a,!1,null,"da14463c",null);t["default"]=c.exports}}]);