{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetStatistics.vue?vue&type=template&id=29e3fc36&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetStatistics.vue", "mtime": 1756003139597}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750942930085}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750942928768}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}