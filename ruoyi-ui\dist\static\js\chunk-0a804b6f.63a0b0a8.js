(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0a804b6f"],{7766:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["game:method:add"],expression:"['game:method:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["game:method:edit"],expression:"['game:method:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["game:method:remove"],expression:"['game:method:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["game:method:export"],expression:"['game:method:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.methodList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"玩法名称",align:"center",prop:"methodName"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tag",{staticStyle:{"font-size":"14px","font-weight":"700",height:"36px","line-height":"36px",padding:"0 15px","min-width":"80px"},attrs:{type:"primary",effect:"dark",size:"medium"}},[e._v(e._s(t.row.methodName))])]}}])}),a("el-table-column",{attrs:{label:"玩法赔率",align:"center",prop:"odds"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tag",{staticStyle:{"font-size":"14px","font-weight":"700",height:"36px","line-height":"36px",padding:"0 15px","min-width":"80px"},attrs:{type:"danger",effect:"dark",size:"medium"}},[e._v("￥"+e._s((t.row.odds||0).toFixed(2)))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["game:method:edit"],expression:"['game:method:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["game:method:remove"],expression:"['game:method:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":"","close-on-click-modal":!1},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"玩法名称",prop:"methodName"}},[a("el-input",{attrs:{placeholder:"请输入玩法名称"},model:{value:e.form.methodName,callback:function(t){e.$set(e.form,"methodName",t)},expression:"form.methodName"}})],1),a("el-form-item",{attrs:{label:"玩法赔率",prop:"odds"}},[a("el-input",{attrs:{placeholder:"请输入玩法赔率"},model:{value:e.form.odds,callback:function(t){e.$set(e.form,"odds",t)},expression:"form.odds"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},o=[],n=a("5530"),s=(a("d81d"),a("d3b7"),a("0643"),a("a573"),a("2449")),r={name:"Method",data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,methodList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:50,lotteryId:1,methodName:null,odds:null},form:{lotteryId:1},rules:{lotteryId:[{required:!0,message:"彩种名称不能为空",trigger:"blur"}],methodName:[{required:!0,message:"玩法名称不能为空",trigger:"blur"}],odds:[{required:!0,message:"玩法赔率不能为空",trigger:"blur"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,Object(s["e"])(this.queryParams).then((function(t){e.methodList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={methodId:null,lotteryId:null,methodName:null,odds:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.methodId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加赔率管理"},handleUpdate:function(e){var t=this;this.reset();var a=e.methodId||this.ids;Object(s["d"])(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改赔率管理"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.methodId?Object(s["f"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(s["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.methodId||this.ids;this.$modal.confirm('是否确认删除赔率管理编号为"'+a+'"的数据项？').then((function(){return Object(s["b"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("game/method/export",Object(n["a"])({},this.queryParams),"method_".concat((new Date).getTime(),".xlsx"))}}},l=r,d=(a("8cde"),a("2877")),m=Object(d["a"])(l,i,o,!1,null,"7b04a637",null);t["default"]=m.exports},"85a6":function(e,t,a){},"8cde":function(e,t,a){"use strict";a("85a6")}}]);