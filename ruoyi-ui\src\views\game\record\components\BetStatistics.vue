<template>
  <div>
    <el-dialog
      title="下注统计"
      :visible.sync="dialogVisible"
      width="95%"
      append-to-body
      style="margin-top: -50px;"
      :close-on-click-modal="false"
      class="statistics-dialog">

      <!-- 管理员用户选择器 -->
      <div v-if="isAdmin" class="admin-controls" style="margin-bottom: 20px; padding: 15px; background: #f5f7fa; border-radius: 4px;">
        <div style="display: flex; align-items: center; gap: 15px;">
          <span style="font-weight: 500; color: #606266;">选择用户:</span>
          <el-select
            v-model="selectedUserId"
            placeholder="选择用户查看统计"
            @change="handleUserChange"
            clearable
            style="width: 250px;">
            <el-option
              v-for="user in userList"
              :key="user.userId"
              :label="`${user.nickName || user.userName} (ID: ${user.userId}) - ${user.recordCount}条记录`"
              :value="user.userId">
            </el-option>
          </el-select>
          <el-button
            type="primary"
            size="small"
            @click="refreshStatistics"
            :loading="loading">
            <i class="el-icon-refresh"></i> 刷新统计
          </el-button>
          <span v-if="selectedUserId" style="color: #67c23a; font-size: 12px;">
            当前查看: {{ getCurrentUserName() }}
          </span>
        </div>
      </div>

      <!-- 彩种选择标签页 - 所有用户都可见 -->
      <div class="lottery-type-tabs-container" style="margin-bottom: 20px; text-align: center;">
        <el-tabs v-model="activeLotteryType" @tab-click="handleLotteryTypeChange" type="card">
          <el-tab-pane name="fucai">
            <span slot="label">
              <i class="el-icon-medal-1"></i>
              福彩3D
            </span>
          </el-tab-pane>
          <el-tab-pane name="ticai">
            <span slot="label">
              <i class="el-icon-trophy-1"></i>
              体彩排三
            </span>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 号码搜索功能 -->
      <div v-if="!isAdmin || (isAdmin && selectedUserId)" class="search-container" style="margin-bottom: 20px;">
        <div class="search-box">
          <el-input
            v-model="searchNumber"
            placeholder="输入号码搜索相关投注（如：125）"
            prefix-icon="el-icon-search"
            clearable
            @input="handleSearchInput"
            @clear="clearSearch"
            style="width: 300px; margin-right: 10px;">
          </el-input>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click="performSearch"
            :loading="searchLoading">
            搜索
          </el-button>
          <el-button
            v-if="isSearchMode"
            type="info"
            icon="el-icon-refresh-left"
            @click="clearSearch">
            显示全部
          </el-button>
        </div>

        <!-- 搜索结果提示 -->
        <div v-if="isSearchMode" class="search-result-tip" style="margin-top: 10px;">
          <el-alert
            :title="searchResultTitle"
            type="info"
            :closable="false"
            show-icon>
          </el-alert>
        </div>
      </div>

      <!-- 管理员未选择用户时的提示 -->
      <div v-if="isAdmin && !selectedUserId" class="admin-no-selection" style="text-align: center; padding: 60px 20px; color: #909399;">
        <i class="el-icon-user" style="font-size: 48px; margin-bottom: 20px; display: block;"></i>
        <h3 style="margin: 0 0 10px 0; color: #606266;">请选择要查看统计的用户</h3>
        <p style="margin: 0; font-size: 14px;">选择用户后将自动加载该用户的下注统计数据</p>
      </div>

      <!-- 统计内容区域 -->
      <div v-if="!isAdmin || (isAdmin && selectedUserId)" class="statistics-content">

        <!-- 当前彩种无数据时的提示 -->
        <div v-if="currentMethodRanking.length === 0 && currentNumberRanking.length === 0"
             class="no-data-tip"
             style="text-align: center; padding: 60px 20px; color: #909399;">
          <i class="el-icon-data-analysis" style="font-size: 48px; margin-bottom: 20px; display: block;"></i>
          <h3 style="margin: 0 0 10px 0; color: #606266;">
            {{ activeLotteryType === 'fucai' ? '福彩3D' : '体彩排三' }} 暂无统计数据
          </h3>
          <p style="margin: 0; font-size: 14px;">
            {{ activeLotteryType === 'fucai' ? '切换到体彩排三查看数据' : '切换到福彩3D查看数据' }}
          </p>
        </div>
          <!-- 按玩法分组的热门三位数排行 -->
        <div v-if="currentNumberRanking.length > 0" class="numbers-rankings-section">
          <h3 class="main-section-title">
            <i class="el-icon-data-line"></i>
            各玩法热门三位数排行榜
          </h3>

          <!-- 使用Grid布局，一行显示五个玩法 -->
          <div class="numbers-grid">
            <div
              v-for="methodGroup in currentNumberRanking"
              :key="methodGroup.methodId"
              class="numbers-card">

              <div class="numbers-card-header">
                <span class="method-name">{{ methodGroup.methodName }}</span>
                <span class="method-count">共{{ methodGroup.ranking.length }}项</span>
              </div>

              <div class="numbers-ranking-list">
                <div
                  v-for="item in methodGroup.ranking"
                  :key="item.rank"
                  class="numbers-ranking-item">
                  <div class="rank-badge" :class="getRankClass(item.rank)">
                    {{ item.rank }}
                  </div>
                  <div class="numbers-info">
                    <div class="hot-number">{{ item.number }}</div>
                    <div class="amount">￥{{ item.totalAmount.toFixed(0) }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      <div class="statistics-container">
        <!-- 按玩法分组的投注金额排行 - 两列布局 -->
        <div v-if="currentMethodRanking.length > 0" class="method-rankings-section">
          <h3 class="main-section-title">
            <i class="el-icon-trophy"></i>
            各玩法投注排行榜
          </h3>

          <!-- 使用Grid布局，一行显示两个玩法 -->
          <div class="method-grid">
            <div
              v-for="methodGroup in currentMethodRanking"
              :key="methodGroup.methodId"
              class="method-card">

              <div class="method-card-header">
                <span class="method-name">{{ methodGroup.methodName }}</span>
                <span class="method-count">共{{ methodGroup.ranking.length }}项</span>
              </div>

              <div class="ranking-list">
                <div
                  v-for="item in methodGroup.ranking"
                  :key="item.rank"
                  class="ranking-item">
                  <div class="rank-badge" :class="getRankClass(item.rank)">
                    {{ item.rank }}
                  </div>
                  <div class="bet-info">
                    <div class="bet-numbers">{{ item.betNumbers }}</div>
                    <div class="amount">￥{{ item.totalAmount.toFixed(0) }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>


      </div>
      <!-- 统计内容区域结束 -->
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="refreshData" type="primary" icon="el-icon-refresh">刷新数据</el-button>
        <el-button @click="close">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import request from '@/utils/request'

export default {
  name: 'BetStatistics',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,
      methodRankingByGroup: [], // 按玩法分组的排行榜
      numberRankingByGroup: [], // 按玩法分组的号码排行榜
      gameMethodsData: [], // 玩法数据
      // 管理员功能相关
      selectedUserId: null, // 选中的用户ID
      userList: [], // 用户列表
      loading: false, // 加载状态
      // 彩种相关
      activeLotteryType: 'fucai', // 当前选中的彩种：fucai(福彩3D) 或 ticai(体彩P3)
      // 分彩种的统计数据
      fucaiMethodRanking: [], // 福彩投注排行
      fucaiNumberRanking: [], // 福彩热门三位数排行
      ticaiMethodRanking: [], // 体彩投注排行
      ticaiNumberRanking: [], // 体彩热门三位数排行
      // 搜索功能相关
      searchNumber: '', // 搜索的号码
      isSearchMode: false, // 是否处于搜索模式
      searchLoading: false, // 搜索加载状态
      searchResultCount: 0, // 搜索结果数量
      // 原始完整数据（用于搜索过滤）
      originalFucaiMethodRanking: [],
      originalFucaiNumberRanking: [],
      originalTicaiMethodRanking: [],
      originalTicaiNumberRanking: []
    }
  },
  computed: {
    /** 是否是管理员 */
    isAdmin() {
      const userInfo = this.$store.getters.userInfo;
      const roles = this.$store.getters.roles;

      // 检查是否是超级管理员（用户ID为1）
      if (userInfo && userInfo.userId === 1) {
        return true;
      }

      // 检查是否有管理员角色
      if (roles && roles.length > 0) {
        return roles.includes('admin') || roles.includes('3d_admin');
      }

      return false;
    },

    /** 当前彩种的投注排行数据 */
    currentMethodRanking() {
      return this.activeLotteryType === 'fucai' ? this.fucaiMethodRanking : this.ticaiMethodRanking;
    },

    /** 当前彩种的热门三位数排行数据 */
    currentNumberRanking() {
      return this.activeLotteryType === 'fucai' ? this.fucaiNumberRanking : this.ticaiNumberRanking;
    },

    /** 搜索结果标题 */
    searchResultTitle() {
      return `搜索号码 "${this.searchNumber}" 的结果：共找到 ${this.searchResultCount} 个匹配项`;
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val) {
        // 运行测试
        this.testThreeDigitExtraction();

        // 如果是管理员，只加载用户列表和玩法数据，不自动计算统计
        if (this.isAdmin) {
          this.loadUserList();
          this.loadGameMethods();
        } else {
          // 普通用户自动加载统计
          this.loadGameMethods().then(async () => {
            // 普通用户从sessionStorage获取自己的数据
            const sysUserId = sessionStorage.getItem('sysUserId');
            console.log('[BetStatistics] sessionStorage中的sysUserId:', sysUserId);

            if (sysUserId) {
              this.selectedUserId = parseInt(sysUserId);
              console.log('[BetStatistics] 设置selectedUserId:', this.selectedUserId);
            } else {
              console.warn('[BetStatistics] 无法从sessionStorage获取sysUserId');
            }
            await this.calculateStatistics();
          });
        }
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val);
    }
  },
  methods: {
    /** 加载玩法数据 */
    async loadGameMethods() {
      try {
        // 直接从API获取玩法数据
        const response = await request({
          url: '/game/odds/list',
          method: 'get',
          params: { pageNum: 1, pageSize: 1000 }
        });

        if (response && response.rows) {
          this.gameMethodsData = response.rows;
         
        } else {
          this.gameMethodsData = [];
          console.warn('玩法数据为空');
        }
      } catch (error) {
        console.error('获取玩法数据失败:', error);
        // 如果API失败，使用基本的玩法映射
        this.gameMethodsData = this.getBasicMethodsData();
      }
    },
    /** 获取基本玩法数据（API失败时的备用方案） */
    getBasicMethodsData() {
      return [
        { methodId: 1, methodName: '独胆' },
        { methodId: 2, methodName: '一码定位' },
        { methodId: 3, methodName: '两码组合' },
        { methodId: 4, methodName: '两码对子' },
        { methodId: 5, methodName: '三码直选' },
        { methodId: 6, methodName: '三码组选' },
        { methodId: 7, methodName: '三码组三' },
        { methodId: 8, methodName: '四码组六' },
        { methodId: 9, methodName: '四码组三' },
        { methodId: 10, methodName: '五码组六' },
        { methodId: 11, methodName: '五码组三' },
        { methodId: 12, methodName: '六码组六' },
        { methodId: 13, methodName: '六码组三' },
        { methodId: 14, methodName: '七码组六' },
        { methodId: 15, methodName: '七码组三' },
        { methodId: 16, methodName: '八码组六' },
        { methodId: 17, methodName: '八码组三' },
        { methodId: 18, methodName: '九码组六' },
        { methodId: 19, methodName: '九码组三' },
        { methodId: 20, methodName: '包打组六' },
        { methodId: 21, methodName: '7或20和值' },
        { methodId: 22, methodName: '8或19和值' },
        { methodId: 23, methodName: '9或18和值' },
        { methodId: 24, methodName: '10或17和值' },
        { methodId: 25, methodName: '11或16和值' },
        { methodId: 26, methodName: '12或15和值' },
        { methodId: 27, methodName: '13或14和值' },
        { methodId: 28, methodName: '直选复式' },
        { methodId: 29, methodName: '和值单双' },
        { methodId: 30, methodName: '和值大小' },
        { methodId: 31, methodName: '包打组三' },
        { methodId: 100, methodName: '三码防对' }
      ];
    },
    /** 获取玩法名称 */
    getMethodName(methodId) {
      const method = this.gameMethodsData.find(m => m.methodId === methodId);
      return method ? method.methodName : `玩法${methodId}`;
    },
    /** 获取排名样式类 */
    getRankClass(rank) {
      if (rank === 1) return 'rank-first';
      if (rank === 2) return 'rank-second';
      if (rank === 3) return 'rank-third';
      return 'rank-other';
    },
    /** 计算统计数据 */
    async calculateStatistics() {
      const data = await this.getRecordData();


      if (!data || data.length === 0) {
        if (this.isAdmin && !this.selectedUserId) {
          this.$message.warning('请选择要查看统计的用户');
        } else {
          this.$message.warning('暂无统计数据，请先刷新record页面');
        }
        this.clearAllRankingData();
        return;
      }

      // 按彩种分别计算统计数据
      this.calculateStatisticsByLotteryType(data);

      // 调试信息
      console.log('统计计算完成:', {
        福彩投注排行: this.fucaiMethodRanking.length,
        福彩热门三位数: this.fucaiNumberRanking.length,
        体彩投注排行: this.ticaiMethodRanking.length,
        体彩热门三位数: this.ticaiNumberRanking.length,
        当前选中彩种: this.activeLotteryType
      });
    },
    /** 获取record数据 */
    async getRecordData() {
      try {
        // 如果有选择的用户ID（管理员选择的用户或普通用户自己），直接从API获取数据
        if (this.selectedUserId) {
          return await this.fetchUserRecordData(this.selectedUserId);
        }

        // 如果是管理员但没有选择用户，提示选择用户
        if (this.isAdmin) {
          this.$message.warning('请选择要查看统计的用户');
          return [];
        }

        // 普通用户：直接从API获取当前用户数据，从sessionStorage获取用户ID
        console.log('[BetStatistics] 普通用户，直接从API获取数据');

        // 从sessionStorage获取用户ID
        const sysUserId = sessionStorage.getItem('sysUserId');
        console.log('[BetStatistics] 从sessionStorage获取的sysUserId:', sysUserId);

        if (sysUserId) {
          console.log('[BetStatistics] 使用sysUserId调用API:', sysUserId);
          const data = await this.fetchUserRecordData(parseInt(sysUserId));
          console.log('[BetStatistics] API获取到的数据:', data);

          if (data && data.length > 0) {
            console.log(`[BetStatistics] 成功加载了 ${data.length} 条统计数据`);
          } else {
            console.warn('[BetStatistics] API返回空数据');
          }

          return data;
        } else {
          console.warn('[BetStatistics] 无法从sessionStorage获取sysUserId');
          this.$message.warning('无法获取用户信息，请重新登录');
          return [];
        }
      } catch (error) {
        console.error('解析统计数据失败:', error);
        return [];
      }
    },
    /** 清空所有排行数据 */
    clearAllRankingData() {
      this.fucaiMethodRanking = [];
      this.fucaiNumberRanking = [];
      this.ticaiMethodRanking = [];
      this.ticaiNumberRanking = [];
      // 清空原始数据
      this.originalFucaiMethodRanking = [];
      this.originalFucaiNumberRanking = [];
      this.originalTicaiMethodRanking = [];
      this.originalTicaiNumberRanking = [];
      // 重置搜索状态
      this.clearSearch();
    },

    /** 按彩种分别计算统计数据 */
    calculateStatisticsByLotteryType(data) {
      // 调试：查看数据结构
      if (data.length > 0) {
        console.log('数据样本:', data[0]);
        console.log('可能的彩种字段:', {
          lotteryType: data[0].lotteryType,
          gameType: data[0].gameType,
          lotteryId: data[0].lotteryId,
          gameName: data[0].gameName,
          methodName: data[0].methodName
        });

        // 统计所有可能的彩种标识
        const lotteryTypes = [...new Set(data.map(record => record.lotteryType))];
        const gameTypes = [...new Set(data.map(record => record.gameType))];
        const lotteryIds = [...new Set(data.map(record => record.lotteryId))];
        const gameNames = [...new Set(data.map(record => record.gameName))];

        console.log('数据中的彩种标识:', {
          lotteryTypes,
          gameTypes,
          lotteryIds,
          gameNames
        });
      }

      // 按彩种分组数据（使用lotteryId字段）
      const fucaiData = data.filter(record => this.isFucaiLottery(record.lotteryId));
      const ticaiData = data.filter(record => this.isTicaiLottery(record.lotteryId));

      console.log(`福彩3D数据: ${fucaiData.length}条, 体彩P3数据: ${ticaiData.length}条`);

      // 如果没有明确的彩种区分，将所有数据归类为福彩3D（兼容性处理）
      if (fucaiData.length === 0 && ticaiData.length === 0 && data.length > 0) {
        console.log('未检测到彩种字段，将所有数据归类为福彩3D');
        this.fucaiMethodRanking = this.calculateMethodRankingByGroup(data);
        this.fucaiNumberRanking = this.calculateNumberRanking(data);
        this.ticaiMethodRanking = [];
        this.ticaiNumberRanking = [];
        return;
      }

      // 分别计算福彩和体彩的统计数据
      if (fucaiData.length > 0) {
        this.originalFucaiMethodRanking = this.calculateMethodRankingByGroup(fucaiData);
        this.originalFucaiNumberRanking = this.calculateNumberRanking(fucaiData);
        this.fucaiMethodRanking = [...this.originalFucaiMethodRanking];
        this.fucaiNumberRanking = [...this.originalFucaiNumberRanking];
      } else {
        this.originalFucaiMethodRanking = [];
        this.originalFucaiNumberRanking = [];
        this.fucaiMethodRanking = [];
        this.fucaiNumberRanking = [];
      }

      if (ticaiData.length > 0) {
        this.originalTicaiMethodRanking = this.calculateMethodRankingByGroup(ticaiData);
        this.originalTicaiNumberRanking = this.calculateNumberRanking(ticaiData);
        this.ticaiMethodRanking = [...this.originalTicaiMethodRanking];
        this.ticaiNumberRanking = [...this.originalTicaiNumberRanking];
      } else {
        this.originalTicaiMethodRanking = [];
        this.originalTicaiNumberRanking = [];
        this.ticaiMethodRanking = [];
        this.ticaiNumberRanking = [];
      }
    },

    /** 判断是否为福彩3D */
    isFucaiLottery(lotteryId) {
      // 福彩3D的lotteryId是1
      return lotteryId === 1 || lotteryId === '1';
    },

    /** 判断是否为体彩P3 */
    isTicaiLottery(lotteryId) {
      // 体彩P3的lotteryId是2
      return lotteryId === 2 || lotteryId === '2';
    },

    /** 按玩法分组计算排行榜 */
    calculateMethodRankingByGroup(data) {
      console.log('[calculateMethodRankingByGroup] 开始计算各玩法投注排行榜');

      // 按玩法分组，相同号码合并累计金额
      const methodGroups = {};

      data.forEach(record => {
        const methodId = record.methodId;
        const amount = parseFloat(record.money) || 0;

        if (!methodGroups[methodId]) {
          methodGroups[methodId] = new Map(); // 使用Map来存储号码和对应的累计数据
        }

        // 解析投注号码
        try {
          const betNumbers = JSON.parse(record.betNumbers || '{}');
          const numbers = betNumbers.numbers || [];

          // 为每个号码累计金额
          numbers.forEach((numberObj) => {
            let singleNumberDisplay = '';

            if (typeof numberObj === 'object' && numberObj !== null) {
              // 处理 {a:1, b:2, c:3, d:4, e:5} 格式
              const keys = Object.keys(numberObj).sort();
              const values = keys.map(key => String(numberObj[key]));
              singleNumberDisplay = values.join('');
            } else {
              singleNumberDisplay = String(numberObj);
            }

            // 如果号码已存在，累计金额和记录数
            if (methodGroups[methodId].has(singleNumberDisplay)) {
              const existing = methodGroups[methodId].get(singleNumberDisplay);
              existing.totalAmount += amount;
              existing.recordCount += 1;
            } else {
              // 新号码，创建记录
              methodGroups[methodId].set(singleNumberDisplay, {
                betNumbers: singleNumberDisplay,
                totalAmount: amount,
                recordCount: 1
              });
            }
          });
        } catch (error) {
          console.warn('解析投注号码失败:', record.betNumbers, error);
          // 如果解析失败，使用原始数据
          const fallbackNumbers = record.betNumbers || '未知号码';

          if (methodGroups[methodId].has(fallbackNumbers)) {
            const existing = methodGroups[methodId].get(fallbackNumbers);
            existing.totalAmount += amount;
            existing.recordCount += 1;
          } else {
            methodGroups[methodId].set(fallbackNumbers, {
              betNumbers: fallbackNumbers,
              totalAmount: amount,
              recordCount: 1
            });
          }
        }
      });

      console.log('[calculateMethodRankingByGroup] 开始生成排行榜');

      // 为每个玩法生成排行榜并返回
      return Object.entries(methodGroups).map(([methodId, numbersMap]) => {
        // 将Map转换为数组并按总金额降序排序
        const ranking = Array.from(numbersMap.values())
          .sort((a, b) => b.totalAmount - a.totalAmount)
          .map((item, index) => ({
            rank: index + 1,
            betNumbers: item.betNumbers,
            totalAmount: item.totalAmount,
            recordCount: item.recordCount
          }));

        console.log(`[calculateMethodRankingByGroup] 玩法${methodId}(${this.getMethodName(parseInt(methodId))})：${ranking.length}个不同号码`);

        return {
          methodId: parseInt(methodId),
          methodName: this.getMethodName(parseInt(methodId)),
          ranking
        };
      }).filter(group => group.ranking.length > 0); // 只显示有数据的玩法
    },
    /** 格式化投注号码用于显示 */
    formatBetNumbersForDisplay(betNumbersKey) {
      try {
        const numbers = JSON.parse(betNumbersKey);
        if (Array.isArray(numbers) && numbers.length > 0) {
          return numbers.map(num => {
            if (typeof num === 'object' && num !== null) {
              // 处理 {a:1, b:2, c:3, d:4, e:5} 格式
              const keys = Object.keys(num).sort();
              const values = keys.map(key => String(num[key]));
              return values.join('');
            }
            return String(num);
          }).join(', ');
        }
      } catch (error) {
        // 如果解析失败，直接返回原始字符串
      }
      return betNumbersKey.length > 20 ? betNumbersKey.substring(0, 20) + '...' : betNumbersKey;
    },
    /** 按玩法分组计算号码排行 - 只使用money字段 */
    calculateNumberRanking(data) {
      // 初始化返回数组
      const numberRankingByGroup = [];

      // 按玩法分组统计
      const methodGroups = {};

      data.forEach(record => {
        try {
          const methodId = record.methodId;
          const money = parseFloat(record.money || 0);
          const betNumbers = JSON.parse(record.betNumbers || '{}');
          const numbers = betNumbers.numbers || [];

          // 初始化玩法分组
          if (!methodGroups[methodId]) {
            methodGroups[methodId] = {
              methodTotal: 0,
              threeDigitMap: new Map()
            };
          }

          methodGroups[methodId].methodTotal += money;

          // 处理每个投注号码
          numbers.forEach(numberObj => {
            const threeDigits = this.extractThreeDigitNumbers(numberObj);

            // 对当前号码的三位数组合去重（避免同一号码内重复组合多次累计）
            const uniqueNormalizedDigits = new Set();

            threeDigits.forEach(digit => {
              // 归一化三位数：将数字按从小到大排序作为统一key
              const normalizedDigit = this.normalizeThreeDigits(digit);
              uniqueNormalizedDigits.add(normalizedDigit);
            });

            // 每个归一化后的三位数累加金额
            uniqueNormalizedDigits.forEach(normalizedDigit => {
              const currentAmount = methodGroups[methodId].threeDigitMap.get(normalizedDigit) || 0;
              methodGroups[methodId].threeDigitMap.set(normalizedDigit, currentAmount + money);
            });
          });

        } catch (error) {
          console.error('解析失败:', error);
        }
      });



      // 生成排行榜
   
      this.numberRankingByGroup = [];

      Object.entries(methodGroups).forEach(([methodId, group]) => {
        // 转换Map为数组并排序，显示所有数据
        const sortedThreeDigits = Array.from(group.threeDigitMap.entries())
          .sort(([,a], [,b]) => b - a);

        const ranking = sortedThreeDigits.map(([number, amount], index) => ({
          rank: index + 1,
          number,
          count: 1,
          totalAmount: amount,
          percentage: group.methodTotal > 0 ? ((amount / group.methodTotal) * 100).toFixed(1) : '0.0'
        }));

        if (ranking.length > 0) {
          numberRankingByGroup.push({
            methodId: parseInt(methodId),
            methodName: this.getMethodName(parseInt(methodId)),
            ranking
          });
        }
      });

      return numberRankingByGroup;
    },
    /** 从投注号码中提取所有可能的三位数组合 */
    extractThreeDigitNumbers(numberObj) {
      const numbers = [];

      // 处理不同的号码格式
      if (typeof numberObj === 'string') {
        // 直接是字符串格式的号码
        const cleanStr = numberObj.replace(/\D/g, ''); // 移除非数字字符
        this.generateThreeDigitCombinations(cleanStr.split(''), numbers);
      } else if (typeof numberObj === 'object' && numberObj !== null) {
        // 对象格式 {a: 1, b: 2, c: 3, d: 4, e: 5}
        const keys = Object.keys(numberObj).sort();
        const digits = keys.map(key => String(numberObj[key]));

    

        // 生成所有可能的三位数组合
        this.generateThreeDigitCombinations(digits, numbers);
      }

      return numbers;
    },
    /** 生成所有可能的三位数组合 */
    generateThreeDigitCombinations(digits, numbers) {
      const n = digits.length;

      // 如果数字少于3个，无法组成三位数
      if (n < 3) {
        return;
      }

      // 生成所有可能的三位数组合（C(n,3)）
      for (let i = 0; i < n - 2; i++) {
        for (let j = i + 1; j < n - 1; j++) {
          for (let k = j + 1; k < n; k++) {
            const threeDigit = digits[i] + digits[j] + digits[k];
            if (/^\d{3}$/.test(threeDigit)) {
              numbers.push(threeDigit);
            }
          }
        }
      }
    },

    /** 归一化三位数：将数字按从小到大排序，统一相同数字的不同排列 */
    normalizeThreeDigits(threeDigit) {
      // 将三位数字符串转换为数组，排序后重新组合
      return threeDigit.split('').sort().join('');
    },

    /** 搜索输入处理 */
    handleSearchInput(value) {
      // 只允许输入数字
      this.searchNumber = value.replace(/\D/g, '');
    },

    /** 执行搜索 */
    performSearch() {
      if (!this.searchNumber.trim()) {
        this.$message.warning('请输入要搜索的号码');
        return;
      }

      this.searchLoading = true;
      this.isSearchMode = true;

      try {
        // 搜索当前彩种的数据
        const searchResults = this.searchInRankingData(this.searchNumber);

        // 更新显示数据为搜索结果
        if (this.activeLotteryType === 'fucai') {
          this.fucaiMethodRanking = searchResults.methodRanking;
          this.fucaiNumberRanking = searchResults.numberRanking;
        } else {
          this.ticaiMethodRanking = searchResults.methodRanking;
          this.ticaiNumberRanking = searchResults.numberRanking;
        }

        // 计算搜索结果数量
        this.searchResultCount = this.calculateSearchResultCount(searchResults);

        this.$message.success(`搜索完成，找到 ${this.searchResultCount} 个匹配项`);
      } catch (error) {
        console.error('搜索失败:', error);
        this.$message.error('搜索失败，请重试');
      } finally {
        this.searchLoading = false;
      }
    },

    /** 清除搜索 */
    clearSearch() {
      this.searchNumber = '';
      this.isSearchMode = false;
      this.searchResultCount = 0;

      // 恢复原始数据
      this.fucaiMethodRanking = [...this.originalFucaiMethodRanking];
      this.fucaiNumberRanking = [...this.originalFucaiNumberRanking];
      this.ticaiMethodRanking = [...this.originalTicaiMethodRanking];
      this.ticaiNumberRanking = [...this.originalTicaiNumberRanking];
    },

    /** 在排行数据中搜索包含指定号码的项目 */
    searchInRankingData(searchNumber) {
      const originalMethodRanking = this.activeLotteryType === 'fucai'
        ? this.originalFucaiMethodRanking
        : this.originalTicaiMethodRanking;

      const originalNumberRanking = this.activeLotteryType === 'fucai'
        ? this.originalFucaiNumberRanking
        : this.originalTicaiNumberRanking;

      // 搜索投注排行榜
      const filteredMethodRanking = originalMethodRanking.map(methodGroup => {
        const filteredRanking = methodGroup.ranking.filter(item => {
          return this.isNumberMatch(item.betNumbers, searchNumber);
        });

        return {
          ...methodGroup,
          ranking: filteredRanking
        };
      }).filter(methodGroup => methodGroup.ranking.length > 0);

      // 搜索热门三位数排行榜
      const filteredNumberRanking = originalNumberRanking.map(methodGroup => {
        const filteredRanking = methodGroup.ranking.filter(item => {
          return this.isNumberMatch(item.number, searchNumber);
        });

        return {
          ...methodGroup,
          ranking: filteredRanking
        };
      }).filter(methodGroup => methodGroup.ranking.length > 0);

      return {
        methodRanking: filteredMethodRanking,
        numberRanking: filteredNumberRanking
      };
    },

    /** 判断号码是否匹配搜索条件 */
    isNumberMatch(targetNumber, searchNumber) {
      if (!targetNumber || !searchNumber) return false;

      const target = String(targetNumber);
      const search = String(searchNumber);

      // 如果搜索号码长度为1，检查是否包含该数字
      if (search.length === 1) {
        return target.includes(search);
      }

      // 如果搜索号码长度为2，检查是否包含这两个数字的组合
      if (search.length === 2) {
        const [digit1, digit2] = search.split('');
        return target.includes(digit1) && target.includes(digit2) &&
               (target.includes(search) || target.includes(digit2 + digit1));
      }

      // 如果搜索号码长度为3或更多，检查是否包含所有数字
      if (search.length >= 3) {
        const searchDigits = search.split('');

        // 检查目标号码是否包含搜索号码的所有数字
        const targetDigits = target.split('');
        const hasAllDigits = searchDigits.every(digit => targetDigits.includes(digit));

        if (!hasAllDigits) return false;

        // 对于三位数的特殊处理：检查是否为相同的数字组合（归一化后比较）
        if (search.length === 3 && target.length === 3) {
          const normalizedSearch = this.normalizeThreeDigits(search);
          const normalizedTarget = this.normalizeThreeDigits(target);
          return normalizedSearch === normalizedTarget;
        }

        // 对于更长的号码，检查是否包含搜索号码的所有数字
        return hasAllDigits;
      }

      return false;
    },

    /** 计算搜索结果数量 */
    calculateSearchResultCount(searchResults) {
      let count = 0;

      // 统计投注排行榜匹配项
      searchResults.methodRanking.forEach(methodGroup => {
        count += methodGroup.ranking.length;
      });

      // 统计热门三位数排行榜匹配项
      searchResults.numberRanking.forEach(methodGroup => {
        count += methodGroup.ranking.length;
      });

      return count;
    },
    /** 刷新数据 */
    async refreshData() {
      await this.calculateStatistics();
      this.$message.success('数据已刷新');
    },
    /** 测试三位数组合提取逻辑 */
    testThreeDigitExtraction() {
      console.log('=== 测试三位数组合提取和归一化逻辑 ===');

      // 测试不同长度的号码
      const testCases = [
        {a: 1, b: 2, c: 3},                    // 三位数
        {a: 1, b: 2, c: 3, d: 4},             // 四位数
        {a: 0, b: 1, c: 2, d: 3, e: 4, f: 5}, // 六位数
        {a: 1, b: 2, c: 3, d: 4, e: 5},       // 五位数
      ];

      testCases.forEach((testCase, index) => {
        console.log(`\n测试用例 ${index + 1}:`, testCase);
        const threeDigits = this.extractThreeDigitNumbers(testCase);
        console.log('提取的三位数组合:', threeDigits);

        // 测试归一化效果
        const normalizedSet = new Set();
        threeDigits.forEach(digit => {
          const normalized = this.normalizeThreeDigits(digit);
          normalizedSet.add(normalized);
          console.log(`${digit} -> ${normalized}`);
        });
        console.log('归一化后的唯一组合:', Array.from(normalizedSet));
      });

      console.log('=== 测试完成 ===');
    },
    /** 计算组合数 C(n,r) */
    calculateCombinations(n, r) {
      if (r > n) return 0;
      if (r === 0 || r === n) return 1;

      let result = 1;
      for (let i = 0; i < r; i++) {
        result = result * (n - i) / (i + 1);
      }
      return Math.round(result);
    },
    /** 关闭对话框 */
    /** 加载用户列表 */
    async loadUserList() {
      try {
        // 先获取所有用户
        const userResponse = await request({
          url: '/system/user/list',
          method: 'get',
          params: { pageNum: 1, pageSize: 1000 }
        });

        if (!userResponse || !userResponse.rows) {
          this.userList = [];
          return;
        }

        const allUsers = userResponse.rows.filter(user => user.status === '0'); // 只获取正常状态的用户

        // 获取有下注记录的用户ID列表
        const recordResponse = await request({
          url: '/game/record/list',
          method: 'get',
          params: {
            pageNum: 1,
            pageSize: 100000 // 获取所有记录来统计用户
          }
        });

        if (recordResponse && recordResponse.rows) {
          // 统计每个用户的下注记录数量
          const userRecordCounts = {};
          recordResponse.rows.forEach(record => {
            const userId = record.sysUserId;
            userRecordCounts[userId] = (userRecordCounts[userId] || 0) + 1;
          });

          // 只保留有下注记录的用户，并添加记录数量信息
          this.userList = allUsers
            .filter(user => userRecordCounts[user.userId])
            .map(user => ({
              ...user,
              recordCount: userRecordCounts[user.userId] || 0
            }))
            .sort((a, b) => b.recordCount - a.recordCount); // 按下注记录数量降序排列

          console.log(`加载用户列表完成，共 ${this.userList.length} 个有下注记录的用户`);
        } else {
          this.userList = [];
          console.warn('未获取到下注记录数据');
        }

      } catch (error) {
        console.error('获取用户列表失败:', error);
        this.$message.error('获取用户列表失败');
        this.userList = [];
      }
    },

    /** 获取指定用户的记录数据 */
    async fetchUserRecordData(userId) {
      try {
        this.loading = true;
        const response = await request({
          url: '/game/record/list',
          method: 'get',
          params: {
            pageNum: 1,
            pageSize: 100000,
            sysUserId: userId
          }
        });

        if (response && response.rows) {
          return response.rows;
        } else {
          return [];
        }
      } catch (error) {
        console.error('获取用户记录数据失败:', error);
        this.$message.error('获取用户记录数据失败');
        return [];
      } finally {
        this.loading = false;
      }
    },

    /** 用户选择变化处理 */
    async handleUserChange(userId) {
      // 清空当前统计数据
      this.methodRankingByGroup = [];
      this.numberRankingByGroup = [];

      if (userId) {
        // 直接加载选中用户的统计数据
        await this.calculateStatistics();
        this.$message.success(`已加载用户 ${this.getCurrentUserName()} 的统计数据`);
      }
    },

    /** 获取当前选中用户的名称 */
    getCurrentUserName() {
      if (!this.selectedUserId) return '';
      const user = this.userList.find(u => u.userId === this.selectedUserId);
      return user ? (user.nickName || user.userName) : '';
    },

    /** 彩种切换处理 */
    handleLotteryTypeChange(tab) {
      console.log(`切换到彩种: ${tab.name === 'fucai' ? '福彩3D' : '体彩P3'}`);
      // 切换彩种时，计算属性会自动更新显示的数据
    },

    /** 刷新统计数据 */
    async refreshStatistics() {
      if (this.isAdmin && !this.selectedUserId) {
        this.$message.warning('请先选择要查看统计的用户');
        return;
      }

      try {
        await this.loadGameMethods();
        await this.calculateStatistics();
        this.$message.success('统计数据已刷新');
      } catch (error) {
        console.error('刷新统计失败:', error);
        this.$message.error('刷新统计失败');
      }
    },

    close() {
      this.dialogVisible = false;
      // 清空选择状态
      this.selectedUserId = null;
      this.activeLotteryType = 'fucai'; // 重置为福彩
      // 清空所有统计数据
      this.clearAllRankingData();
    }
  }
}
</script>

<style scoped>
/* 管理员控制区域样式 */
.admin-controls {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.admin-controls .el-select {
  background: white;
}

/* 彩种标签页样式 */
.lottery-type-tabs {
  margin-left: 20px;
  flex: 1;
}

.lottery-type-tabs-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.lottery-type-tabs .el-tabs--card > .el-tabs__header,
.lottery-type-tabs-container .el-tabs--card > .el-tabs__header {
  border-bottom: none;
  margin: 0;
}

.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__nav,
.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__nav {
  border: none;
  border-radius: 8px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 4px;
}

.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__item,
.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__item {
  border: none;
  background: transparent;
  color: #606266;
  font-weight: 500;
  padding: 8px 16px;
  margin-right: 4px;
  border-radius: 6px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__item:hover,
.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__item:hover {
  color: #409eff;
  background: rgba(64, 158, 255, 0.1);
  transform: translateY(-1px);
}

.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__item.is-active,
.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transform: translateY(-1px);
}

.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__item.is-active::before,
.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__item.is-active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, transparent 100%);
  pointer-events: none;
}

.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__item i,
.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__item i {
  margin-right: 6px;
  font-size: 16px;
}

.lottery-type-tabs .el-tabs__content,
.lottery-type-tabs-container .el-tabs__content {
  display: none; /* 隐藏内容区域，因为我们只用标签页切换 */
}

.statistics-dialog {
  .el-dialog {
    border-radius: 12px;
  }
  
  .el-dialog__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 24px;
  }
  
  .el-dialog__title {
    color: white;
    font-weight: 600;
    font-size: 18px;
  }
}

.statistics-container {
  padding: 10px 0;
}

.main-section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
  padding-bottom: 4px;
  border-bottom: 1px solid #ecf0f1;
}

.main-section-title i {
  margin-right: 4px;
  color: #667eea;
  font-size: 14px;
}

/* 玩法排行榜样式 */
.method-rankings-section {
  margin-bottom: 15px;
}

.method-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 6px;
}

@media (max-width: 1400px) {
  .method-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 1100px) {
  .method-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 800px) {
  .method-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 500px) {
  .method-grid {
    grid-template-columns: 1fr;
  }
}

.method-card {
  background: white;
  border-radius: 10px;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.12);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  min-width: 0; /* 防止内容溢出 */
  min-height: 200px; /* 最小高度 */
  max-height: 500px; /* 增加最大高度以适应更多数据 */
}

.method-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.18);
}

.method-card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.method-name {
  font-size: 15px;
  font-weight: 700;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.method-count {
  font-size: 13px;
  opacity: 0.9;
}

.ranking-list {
  padding: 8px;
  max-height: 420px; /* 增加列表高度以适应更多数据 */
  overflow-y: auto; /* 添加垂直滚动条 */
}

/* 滚动条样式 */
.ranking-list::-webkit-scrollbar {
  width: 4px;
}

.ranking-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.ranking-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.ranking-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 热门三位数排行榜滚动条样式 */
.numbers-ranking-list::-webkit-scrollbar {
  width: 4px;
}

.numbers-ranking-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.numbers-ranking-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.numbers-ranking-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-bottom: 1px solid #f0f0f0;
}

.ranking-item:last-child {
  border-bottom: none;
}

.rank-badge {
  min-width: 18px;
  height: 18px;
  padding: 0 4px;
  border-radius: 9px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 10px;
  color: white;
  flex: 0 0 auto;
  margin-right: 8px;
  white-space: nowrap;
}

.rank-first {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
}

.rank-second {
  background: linear-gradient(135deg, #feca57, #ff9ff3);
}

.rank-third {
  background: linear-gradient(135deg, #48dbfb, #0abde3);
}

.rank-other {
  background: linear-gradient(135deg, #a4b0be, #747d8c);
}

.bet-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.bet-numbers {
  font-size: 15px;
  font-weight: 900;
  color: #ffffff;
  font-family: 'Courier New', monospace;
  background: linear-gradient(135deg, #667eea, #764ba2);
  padding: 3px 8px;
  border-radius: 5px;
  text-align: center;
  box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);
  letter-spacing: 0.5px;
  flex: 2;
  margin-right: 8px;
}

.amount {
  color: #e74c3c;
  font-weight: 700;
  font-size: 14px;
  white-space: nowrap;
  flex: 1;
  text-align: right;
}

.record-count {
  color: #95a5a6;
  font-size: 11px;
  text-align: right;
  margin-top: 2px;
}

/* 热门三位数样式 */
.numbers-section {
  margin-bottom: 30px;
}

.ranking-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.amount-text {
  font-weight: 600;
  color: #e74c3c;
}

.percentage-text {
  font-weight: 500;
  color: #3498db;
}

/* 热门三位数卡片样式 */
.numbers-rankings-section {
  margin-bottom: 15px;
}

.numbers-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 6px;
}

@media (max-width: 1400px) {
  .numbers-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 1100px) {
  .numbers-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 800px) {
  .numbers-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 500px) {
  .numbers-grid {
    grid-template-columns: 1fr;
  }
}

.numbers-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  min-width: 0;
  min-height: 120px;
  max-height: 400px; /* 增加最大高度以适应更多数据 */
}

.numbers-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.numbers-card-header {
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  color: white;
  padding: 4px 6px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.numbers-ranking-list {
  padding: 4px;
  max-height: 320px; /* 限制列表高度以适应更多数据 */
  overflow-y: auto; /* 添加垂直滚动条 */
}

.numbers-ranking-item {
  display: flex;
  align-items: center;
  padding: 2px 4px;
  border-bottom: 1px solid #f0f0f0;
}

.numbers-ranking-item:last-child {
  border-bottom: none;
}

.numbers-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.numbers-info .hot-number {
  font-family: 'Courier New', monospace;
  font-weight: 900;
  font-size: 13px;
  color: #ffffff;
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  padding: 2px 4px;
  border-radius: 3px;
  text-align: center;
  flex: 2;
  margin-right: 4px;
}

.numbers-info .amount {
  color: #e74c3c;
  font-weight: 700;
  font-size: 13px;
  white-space: nowrap;
  flex: 1;
  text-align: right;
}



.dialog-footer {
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #ecf0f1;
}

/* 搜索功能样式 */
.search-container {
  padding: 15px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.search-box {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 10px;
}

.search-box .el-input {
  max-width: 300px;
  flex: 1;
  min-width: 200px;
}

.search-box .el-button {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.search-box .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.search-result-tip {
  text-align: center;
}

.search-result-tip .el-alert {
  border-radius: 6px;
}

.search-result-tip .el-alert--info {
  background-color: rgba(64, 158, 255, 0.1);
  border-color: rgba(64, 158, 255, 0.2);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .search-box {
    flex-direction: column;
    align-items: stretch;
  }

  .search-box .el-input {
    max-width: none;
    width: 100%;
  }

  .search-box .el-button {
    width: 100%;
    margin-top: 5px;
  }
}
</style>
