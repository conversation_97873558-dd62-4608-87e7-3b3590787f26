(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7dd7cb19","chunk-8adb838a","chunk-0e3c7648"],{"0068":function(e,t,n){"use strict";n.d(t,"d",(function(){return a})),n.d(t,"a",(function(){return o})),n.d(t,"e",(function(){return i})),n.d(t,"b",(function(){return s})),n.d(t,"c",(function(){return l}));var r=n("b775");function a(e){return Object(r["a"])({url:"/game/qihao/list",method:"get",params:e})}function o(e){return Object(r["a"])({url:"/game/qihao",method:"post",data:e})}function i(e){return Object(r["a"])({url:"/game/qihao",method:"put",data:e})}function s(e){return Object(r["a"])({url:"/game/qihao/"+e,method:"delete"})}function l(e){return Object(r["a"])({url:"/game/qihao/current/"+e,method:"get"})}},"01e0":function(e,t,n){},"0481":function(e,t,n){"use strict";var r=n("23e7"),a=n("a2bf6"),o=n("7b0b"),i=n("07fa"),s=n("5926"),l=n("65f0");r({target:"Array",proto:!0},{flat:function(){var e=arguments.length?arguments[0]:void 0,t=o(this),n=i(t),r=l(t,0);return r.length=a(r,t,t,n,0,void 0===e?1:s(e)),r}})},"07ac":function(e,t,n){"use strict";var r=n("23e7"),a=n("6f53").values;r({target:"Object",stat:!0},{values:function(e){return a(e)}})},"08c8":function(e,t,n){"use strict";n.d(t,"d",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){return i})),n.d(t,"e",(function(){return s})),n.d(t,"b",(function(){return l}));var r=n("b775");function a(e){return Object(r["a"])({url:"/game/odds/list",method:"get",params:e})}function o(e){return Object(r["a"])({url:"/game/odds/"+e,method:"get"})}function i(e){return Object(r["a"])({url:"/game/odds",method:"post",data:e})}function s(e){return Object(r["a"])({url:"/game/odds",method:"put",data:e})}function l(e){return Object(r["a"])({url:"/game/odds/"+e,method:"delete"})}},"0901":function(e,t,n){"use strict";n.d(t,"e",(function(){return a})),n.d(t,"d",(function(){return o})),n.d(t,"a",(function(){return i})),n.d(t,"f",(function(){return s})),n.d(t,"c",(function(){return l})),n.d(t,"b",(function(){return u}));var r=n("b775");function a(e){return Object(r["a"])({url:"/game/customer/list",method:"get",params:e})}function o(e){return Object(r["a"])({url:"/game/customer/"+e,method:"get"})}function i(e){return Object(r["a"])({url:"/game/customer",method:"post",data:e})}function s(e){return Object(r["a"])({url:"/game/customer",method:"put",data:e})}function l(e){return Object(r["a"])({url:"/game/customer/"+e,method:"delete"})}function u(){return Object(r["a"])({url:"/game/customer/checkAndSetDefault",method:"get"})}},"0ccb":function(e,t,n){"use strict";var r=n("e330"),a=n("50c4"),o=n("577e"),i=n("1148"),s=n("1d80"),l=r(i),u=r("".slice),c=Math.ceil,d=function(e){return function(t,n,r){var i,d,m=o(s(t)),h=a(n),f=m.length,I=void 0===r?" ":o(r);return h<=f||""===I?m:(i=h-f,d=l(I,c(i/I.length)),d.length>i&&(d=u(d,0,i)),e?m+d:d+m)}};e.exports={start:d(!1),end:d(!0)}},"0fad":function(e,t,n){"use strict";n.d(t,"f",(function(){return a})),n.d(t,"d",(function(){return o})),n.d(t,"a",(function(){return i})),n.d(t,"h",(function(){return s})),n.d(t,"c",(function(){return l})),n.d(t,"g",(function(){return u})),n.d(t,"b",(function(){return c})),n.d(t,"e",(function(){return d}));var r=n("b775");function a(e){return Object(r["a"])({url:"/game/draw/list",method:"get",params:e})}function o(e){return Object(r["a"])({url:"/game/draw/"+e,method:"get"})}function i(e){return Object(r["a"])({url:"/game/draw",method:"post",data:e})}function s(e){return Object(r["a"])({url:"/game/draw",method:"put",data:e})}function l(e){return Object(r["a"])({url:"/game/draw/"+e,method:"delete"})}function u(){return Object(r["a"])({url:"/game/draw/kaijiangjiesuan",method:"post"})}function c(e){return Object(r["a"])({url:"/game/draw/cancelPay/"+e,method:"put"})}function d(){return Object(r["a"])({url:"/game/draw/insertNextDraw",method:"post"})}},"2e8d":function(e,t,n){"use strict";n.r(t),n.d(t,"SEVEN_CODE_PATTERNS",(function(){return s}));var r=n("2909"),a=n("3835"),o=n("b85c"),i=(n("99af"),n("4de4"),n("caad"),n("a15b"),n("d81d"),n("14d9"),n("4e82"),n("4ec9"),n("b64b"),n("d3b7"),n("4d63"),n("c607"),n("ac1f"),n("2c3e"),n("00b4"),n("25f0"),n("8a79"),n("2532"),n("3ca3"),n("466d"),n("5319"),n("1276"),n("498a"),n("0643"),n("76d6"),n("2382"),n("4e3e"),n("a573"),n("9a9a"),n("159b"),n("ddb0"),n("f9d6")),s={QIMA_TRANSFORM:{pattern:/^(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?(\d{7})[\-—~～@#￥%…&!、\\/*,，.＝=各·\s]*?(转|套|拖)(?:各)?(\d+)(?:\+(\d+))?(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?$/,handler:function(e,t){var r=e[2],a=e[4],o=e[5],i=e[6],s=n("f9d6").generateTransformNumbers(r),l=[];return a?l.push({methodId:n("f9d6").METHOD_ID.BAMA_ZULIU,betNumbers:s.join(","),money:a}):o&&i&&(l.push({methodId:n("f9d6").METHOD_ID.BAMA_ZULIU,betNumbers:s.join(","),money:o}),l.push({methodId:n("f9d6").METHOD_ID.BAMA_ZUSAN,betNumbers:s.join(","),money:i})),l}},QIMA_ZUSAN:{pattern:/^(\d{7})[\-—~～@#￥%…&!、\\/*,，一－.＝=·\s]*组三?[\-—~～@#￥%…&!、\\/*,，。，一—－.＝=·\s]*([\d]+)?(元|块|米)?[\-—~～@#￥%…&!、。，一—－\\/*,，.＝=·\s]*$/,handler:function(e,t){return 7!==e[1].length?null:[{methodId:i["METHOD_ID"].QIMA_ZUSAN,betNumbers:e[1],money:e[2]||""}]}},QIMA_FANGDUI:{pattern:/^(\d{7})[\-—~～@#￥%…&!、\\/*,，一－.＝=·\s]*防对?([+＋\-—~～@#￥%…&!、：:\\/*,，。一—－~.＝=·吊赶打各]?(\d+))?$/,handler:function(e,t){return 7!==e[1].length?null:[{methodId:i["METHOD_ID"].QIMA_ZUSAN,betNumbers:e[1],money:e[2]||""}]}},MULTI_QIMA:{pattern:/^(\d{7}(?:[^0-9]+\d{7})*)([+＋\-—~～@#￥%…&!、：:\\/*,，。一—－~.＝=·吊赶打各]([一二三四五六七八九十百千万\d]+))?(?:防([一二三四五六七八九十百千万\d]+))?$/,handler:function(e,t){var r=n("f9d6"),a=r.chineseToNumber,o=t.split(i["BASE_PATTERNS"].SEPARATORS).filter(Boolean).filter((function(e){return 7===e.length})),s=e[2]?a(e[2]):"",l=e[3]?a(e[3]):"",u=[];return s&&u.push({methodId:i["METHOD_ID"].QIMA_ZULIU,betNumbers:o.join(","),money:s}),l&&u.push({methodId:i["METHOD_ID"].QIMA_ZUSAN,betNumbers:o[o.length-1],money:l}),console.log("七码 handler 返回:",u),u}},QIMA_ZULIU_ZUSAN_MULTI_MONEY_SUPER:{pattern:/^[－\-—~～@#￥%…&!、\\/*,一－。＝=·\s+吊赶打各]*(\d{7}(?:[－\-—~～@#￥%…&!、\\/*,一－。＝=·\s+吊赶打各]+\d{7})*)[\-—~～@#￥%…&!、\\/*,一－。＝=·\s+吊赶打各]+(\d+)[\-—~～@#￥%…&!、\\/*,一－。＝=·\s+吊赶打各]*[\+\-\\/*、.]+[\-—~～@#￥%…&!、\\/*,一－。＝=·\s+吊赶打各]*(\d+)(?:元)?$/,handler:function(e,t){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=n("f9d6"),o=a.SUPER_SEPARATORS,i=e[1].split(o).filter(Boolean),s=e[2],l=e[3];return r&&(/^\d{7}$/.test(s)||/^\d{7}$/.test(l))?null:[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:i.join(","),money:s},{methodId:n("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:i.join(","),money:l}]}},QIMA_ZULIU_ZU_SUPER:{pattern:/^(\d{7})组(\d+)$/,handler:function(e,t){var r=e[1],a=e[2];return[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:r,money:a}]}},QIMA_ZULIU_MULTI_SUPER:{pattern:/^(\d{7}(?:[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各]+\d{7})*)[\-—~～@一－。#￥%…&!、\\/*,，.＝=·\s+吊赶打各]+(\d+)$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=e[1].split(a).filter(Boolean),i=e[2];return[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:o.join(","),money:i}]}},QIMA_ZUSAN_ZU_SUPER:{pattern:/^(\d{7})组三(\d+)$/,handler:function(e,t){var r=e[1],a=e[2];return[{methodId:n("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:r,money:a}]}},QIMA_ZHIZU_MULTI_SUPER:{pattern:/^(\d{7}(?:[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各]+\d{7})*)[\-—~～@#￥%…&!、\\/*,，.＝=一－。·\s+吊赶打各]*(直组|组直|值组|组值)[\s]*各?(\d+)(?:元)?$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=e[1].split(a).filter(Boolean),i=e[3];return[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:o.join(","),money:i},{methodId:n("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:o.join(","),money:i}]}},QIMA_ZULIU_FANG:{pattern:/^(\d{7})[+＋\-—~～@#￥%…&!、：:\\/*,，。一—－~.＝=·吊赶打各](\d+)[+防](\d+)$/,handler:function(e,t){var n=e[1],r=e[2],a=e[3];return[{methodId:i["METHOD_ID"].QIMA_ZULIU,betNumbers:n,money:r},{methodId:i["METHOD_ID"].QIMA_ZUSAN,betNumbers:n,money:a}]}},QIMA_MULTI_ZHIZU_EACH_SUPER:{pattern:/^(直组|组直|值组|组值)各([0-9]+)[\s\-—~～@#￥%…&!、一－。 \\/*,，.＝=·吊赶打各\n]+([\d\s\-—~～@#￥%…&!一－。、\\/*,，.＝=·吊赶打各]+)$/i,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=e[2],i=e[3].replace(/\n/g," "),s=i.split(a).map((function(e){return e.trim()})).filter((function(e){return/^\d{7}$/.test(e)}));return[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:s.join(","),money:o},{methodId:n("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:s.join(","),money:o}]}},QIMA_MULTI_EACH_SUPER:{pattern:/^(\d{7}(?:[\-~～@#￥%…&!一－。、\\/*,，.＝=·\s+吊赶打各]+\d{7})*)各(\d+)$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=e[1].split(a).filter(Boolean);if(!o.length||o.some((function(e){return 7!==e.length})))return null;var i=e[2];return[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:o.join(","),money:i}]}},QIMA_SLASH_TWO_MONEY:{pattern:/^(?!.*[\r\n])(\d{7})[\-—~～@#￥%…&!、\\/*,，.。\u4e00\uff0d\uff1d=· 　\t]+(\d+)[+]+(\d+)$/,handler:function(e,t){var r=e[1],a=e[2],o=e[3];return[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:r,money:a},{methodId:n("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:r,money:o}]}},QIMA_MULTI_SUPER_SLASH_TWO_MONEY:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、＝=·吊赶打各]+)\/(\d+)\/(\d+)$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=e[1].split(a).map((function(e){return e.trim()})).filter((function(e){return 7===e.length}));if(!o.length)return[];var i=e[2],s=e[3];return[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:o.join(","),money:i},{methodId:n("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:o.join(","),money:s}]}},QIMA_UNIVERSAL_SUPER:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥一－—。%…&!、＝=·吊赶打各]+)[\-~～@#￥%…&!一－—。、\\/*,，.＝=·\s+吊赶打各]+([0-9]+)(元|米|块)?$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=e[1].split(a).map((function(e){return e.trim()})).filter(Boolean),s=e[2];return 1===i.length&&7===i[0].length?[{methodId:o.QIMA_ZULIU,betNumbers:i[0],money:s}]:i.length>=2&&i.every((function(e){return 7===e.length}))?[{methodId:o.QIMA_ZULIU,betNumbers:i.join(","),money:s}]:null}},QIMA_EACH_PLUS_SUPER:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥一－。%…&!、＝=·吊赶打各]+)各([0-9]+)\+([0-9]+)$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=e[1].split(a).map((function(e){return e.trim()})).filter(Boolean),s=e[2],l=e[3];return 1===i.length&&7===i[0].length?[{methodId:o.QIMA_ZULIU,betNumbers:i[0],money:s},{methodId:o.QIMA_ZUSAN,betNumbers:i[0],money:l}]:i.length>=2&&i.every((function(e){return 7===e.length}))?[{methodId:o.QIMA_ZULIU,betNumbers:i.join(","),money:s},{methodId:o.QIMA_ZUSAN,betNumbers:i.join(","),money:l}]:null}},QIMA_ZULIU_MONEY:{pattern:/^([0-9]{7,})\s+(\d+)(元|块|米)?$/,handler:function(e,t){var r=e[1],a=e[2];return r&&7===r.length?[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:r,money:a}]:null}},QIMA_DA_MONEY:{pattern:/^([0-9]{7})打(\d+)(元|块|米)?$/,handler:function(e,t){return[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:e[1],money:e[2]}]}},QIMA_MULTI_WITH_MONEY_SLASH_PLUS:{pattern:/^(\d{7}(?:[\s,，\-]+?\d{7})*)\/\s*(\d+)\+(\d+)$/,handler:function(e,t){var r=e[1].split(/[\s,，\-]+/).filter(Boolean),a=e[2],o=e[3];return[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:r.join(","),money:a},{methodId:n("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:r.join(","),money:o}]}},QIMA_MULTI_LINE_MONEY_PLUS:{pattern:/^((?:\d{7}[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+\d+\+\d+\s*\n?)+)$/m,handler:function(e,t){var r,i=n("f9d6"),s=i.METHOD_ID,l=t.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),u=new Map,c=!0,d=Object(o["a"])(l);try{for(d.s();!(r=d.n()).done;){var m=r.value,h=m.match(/^(\d{7})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+(\d+)\+(\d+)$/);if(!h){c=!1;break}var f=h[1];if(7!==f.length){c=!1;break}var I=m.split(/[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+/)[0];if(7!==I.length||!/^\d{7}$/.test(I)){c=!1;break}var b="".concat(h[2],"+").concat(h[3]);u.has(b)||u.set(b,[]),u.get(b).push(f)}}catch(D){d.e(D)}finally{d.f()}if(!c||0===u.size)return null;var p,_=[],g=Object(o["a"])(u.entries());try{for(g.s();!(p=g.n()).done;){var N=Object(a["a"])(p.value,2),U=N[0],v=N[1],A=U.split("+"),y=Object(a["a"])(A,2),M=y[0],E=y[1];_.push({methodId:s.QIMA_ZULIU,betNumbers:v.join(","),money:M}),_.push({methodId:s.QIMA_ZUSAN,betNumbers:v.join(","),money:E})}}catch(D){g.e(D)}finally{g.f()}return _.length?_:null}},QIMA_MULTI_LINE_MONEY_PLUS_CN:{pattern:/^((?:\d{7}[，,、]?[一二三四五六七八九十百千万\d]+加[一二三四五六七八九十百千万\d]+\s*\n?)+)$/m,handler:function(e,t){var r,i=n("f9d6"),s=i.METHOD_ID,l=i.chineseToNumber,u=t.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),c=new Map,d=Object(o["a"])(u);try{for(d.s();!(r=d.n()).done;){var m=r.value,h=m.match(/^(\d{7})[，,、]?([一二三四五六七八九十百千万\d]+)加([一二三四五六七八九十百千万\d]+)$/);if(h){var f=h[1],I=l(h[2]),b=l(h[3]),p="".concat(I,"+").concat(b);c.has(p)||c.set(p,[]),c.get(p).push(f)}}}catch(O){d.e(O)}finally{d.f()}var _,g=[],N=Object(o["a"])(c.entries());try{for(N.s();!(_=N.n()).done;){var U=Object(a["a"])(_.value,2),v=U[0],A=U[1],y=v.split("+"),M=Object(a["a"])(y,2),E=M[0],D=M[1];g.push({methodId:s.QIMA_ZULIU,betNumbers:A.join(","),money:E}),g.push({methodId:s.QIMA_ZUSAN,betNumbers:A.join(","),money:D})}}catch(O){N.e(O)}finally{N.f()}return g.length?g:null}},QIMA_MULTI_EACH_FANGDUI_SLASH:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥一－。%…&!、＝=·吊赶打各]+)[/\-~～@#￥%…&!、\\/*一－。,，.＝=·吊赶打各]+各防对([一二三四五六七八九十百千万\d]+)$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=r.chineseToNumber,s=e[1].split(a).map((function(e){return e.trim()})).filter((function(e){return 7===e.length})),l=i(e[2]);return s.length?[{methodId:o.QIMA_ZUSAN,betNumbers:s.join(","),money:l}]:null}},QIMA_MULTI_EACH_FANGDUI:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!一－。、＝=·吊赶打各]+)各防对([一二三四五六七八九十百千万\d]+)$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=r.chineseToNumber,s=e[1].split(a).map((function(e){return e.trim()})).filter((function(e){return 7===e.length})),l=i(e[2]);return s.length?[{methodId:o.QIMA_ZUSAN,betNumbers:s.join(","),money:l}]:null}},QIMA_SLASH_MONEY:{pattern:/^\s*(\d{7})\s*\/\s*([一二三四五六七八九十百千万\d]+)\s*$/,handler:function(e,t){var r=n("f9d6"),a=r.METHOD_ID,o=r.chineseToNumber,i=e[1],s=o(e[2]);return[{methodId:a.QIMA_ZULIU,betNumbers:i,money:s}]}},QIMA_ZULIU_ZUSAN_MONEY_PAIR:{pattern:/^(\d{7}[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*)组六[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*(\d+)[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*组三[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*(\d+)$/,handler:function(e,t){var n=e[1],r=e[2],a=e[3];return[{methodId:i["METHOD_ID"].QIMA_ZULIU,betNumbers:n,money:r},{methodId:i["METHOD_ID"].QIMA_ZUSAN,betNumbers:n,money:a}]}},QIMA_ZULIU:{pattern:/^(\d{7})组六?(\d+)[，,./\*\-\=一－。。·、\s]*$/,handler:function(e,t){var n=e[1];if(7!==n.length)return null;var r=e[2]||"";return[{methodId:i["METHOD_ID"].QIMA_ZULIU,betNumbers:n,money:r}]}},QIMA_ZULIU_ZUSAN_COMBO:{pattern:/^(\d{7}(?:[。.．、,，\s]+\d{7})*)\/(\d+)\+(\d+)$/,handler:function(e,t){var r=n("f9d6"),a=(r.SUPER_SEPARATORS,e[1].split(/[。.．、,，\s]+/).map((function(e){return e.trim()})).filter(Boolean)),o=e[2],i=e[3];return a.length&&a.every((function(e){return 7===e.length}))?[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:a.join(","),money:o},{methodId:n("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:a.join(","),money:i}]:null}},QIMA_SINGLE_LINE_ZULIU_ZUSAN_PLUS:{pattern:/^(\d{7})\s+(\d+)[+＋](\d+)$/,handler:function(e,t){return[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:e[1],money:e[2]},{methodId:n("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:e[1],money:e[3]}]}},QIMA_MULTI_LINE_ZULIU_MONEY_GROUP:{pattern:/^((?:\d{7}\s+\d+\s*\n?)+)$/m,handler:function(e,t){var r,i=n("f9d6"),s=i.METHOD_ID,l=t.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),u=new Map,c=Object(o["a"])(l);try{for(c.s();!(r=c.n()).done;){var d=r.value,m=d.match(/^(\d{7})\s+(\d+)$/);if(m){var h=m[1],f=m[2];u.has(f)||u.set(f,[]),u.get(f).push(h)}}}catch(U){c.e(U)}finally{c.f()}var I,b=[],p=Object(o["a"])(u.entries());try{for(p.s();!(I=p.n()).done;){var _=Object(a["a"])(I.value,2),g=_[0],N=_[1];b.push({methodId:s.QIMA_ZULIU,betNumbers:N.join(","),money:g})}}catch(U){p.e(U)}finally{p.f()}return b.length?b:null}},QIMA_ZULIU_FANG_ZUSAN_PLUS:{pattern:/^(\d{7})[\-—~～@#￥%…&!、\\/*,，.＝一－。=·\s+吊赶打各]*(\d+)[\-—~～@#￥%…&!、一－。\\/*,，.＝=·\s+吊赶打各]*(防对|防)(\d+)$/,handler:function(e,t){return[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:e[1],money:e[2]},{methodId:n("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:e[1],money:e[3]}]}},QIMA_MULTI_LINE_ZULIU_FANG_ZUSAN_GROUP:{pattern:/^([\s\S]+)$/m,handler:function(e,t){var r,i=t.split(/[\r\n]+/).map((function(e){return e.trim()})).filter(Boolean),s=new Map,l=Object(o["a"])(i);try{for(l.s();!(r=l.n()).done;){var u=r.value,c=u.match(/^(\d{7})-?(\d+)防(\d+)$/);if(c){var d=c[1],m=c[2],h=c[3],f="".concat(m,"|").concat(h);s.has(f)||s.set(f,[]),s.get(f).push(d)}}}catch(M){l.e(M)}finally{l.f()}var I,b=[],p=Object(o["a"])(s.entries());try{for(p.s();!(I=p.n()).done;){var _=Object(a["a"])(I.value,2),g=_[0],N=_[1],U=g.split("|"),v=Object(a["a"])(U,2),A=v[0],y=v[1];b.push({methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:N.join(","),money:A}),b.push({methodId:n("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:N.join(","),money:y})}}catch(M){p.e(M)}finally{p.f()}return b.length?b:null}},QIMA_MULTI_TRANSFORM_EACH:{pattern:/^(\d{6}(?:[\-~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各]+\d{6})*)(转|转各|套|套各)(\d+)\+(\d+)$/,handler:function(e,t){var r,a=n("f9d6"),i=a.SUPER_SEPARATORS,s=a.METHOD_ID,l=a.generateTransformNumbers,u=e[1].split(i).filter(Boolean),c=e[3],d=e[4],m=[],h=Object(o["a"])(u);try{for(h.s();!(r=h.n()).done;){var f=r.value,I=l(f);m.push({methodId:s.BAMA_ZULIU,betNumbers:I.join(","),money:c}),m.push({methodId:s.BAMA_ZUSAN,betNumbers:I.join(","),money:d})}}catch(b){h.e(b)}finally{h.f()}return m}},QIMA_MULTI_LINE_TRANSFORM:{pattern:/^((?:(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?\d{7}转\d+(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?\s*\n?)+)$/m,handler:function(e,t){var r,i=n("f9d6"),s=i.METHOD_ID,l=t.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),u=new Map,c=Object(o["a"])(l);try{for(c.s();!(r=c.n()).done;){var d=r.value,m=d.match(/^(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?(\d{7})转(\d+)(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?$/);if(m){var h=m[2],f=m[3];u.has(f)||u.set(f,[]),u.get(f).push(h)}}}catch(U){c.e(U)}finally{c.f()}var I,b=[],p=Object(o["a"])(u.entries());try{for(p.s();!(I=p.n()).done;){var _=Object(a["a"])(I.value,2),g=_[0],N=_[1];b.push({methodId:s.BAMA_ZULIU,betNumbers:N.join(","),money:g})}}catch(U){p.e(U)}finally{p.f()}return b.length?b:null}},QIMA_MULTI_COLON_EACH_SUPER:{pattern:/^(\d{7}(?::|：\d{7})*)各(\d+)$/,handler:function(e,t){var r=t.split(/:|：/).map((function(e){return e.trim()})).filter((function(e){return 7===e.length})),a=e[2];return r.length?[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:r.join(","),money:a}]:null}},QIMA_MULTI_YIGE_EACH_PLUS:{pattern:/^([\d一]+)一([\d一]+)一?各?(\d+)\+(\d+)$/,handler:function(e,t){var r=t.split("一").map((function(e){return e.replace(/[^\d]/g,"")})).filter((function(e){return 7===e.length})),a=e[3],o=e[4];return r.length?[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:r.join(","),money:a},{methodId:n("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:r.join(","),money:o}]:null}},QIMA_DIAN_XMA_ZULIU_FANG_ZUSAN:{pattern:/^(\d{7})[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]+7码(\d+)防(\d+)$/,handler:function(e,t){return[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:e[1],money:e[2]},{methodId:n("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:e[1],money:e[3]}]}},QIMA_MULTI_NUMBERS_EACH_FANG:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥一－。%…&!、＝=·吊赶打各]+)各(\d+)(防|防对|放对)(\d+)$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=e[1].split(a).map((function(e){return e.trim()})).filter((function(e){return 7===e.length}));if(!i.length)return null;var s=e[2],l=e[4];return[{methodId:o.QIMA_ZULIU,betNumbers:i.join(","),money:s},{methodId:o.QIMA_ZUSAN,betNumbers:i.join(","),money:l}]}},QIMA_YI_YI_ONE_MONEY:{pattern:/^(\d{7})一(\d{7})一个(\d+)(元|块|米)?$/,handler:function(e,t){var r=e[1],a=e[2],o=e[3];return 7!==r.length||7!==a.length?null:[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:r+","+a,money:o}]}},QIMA_GE_MONEY:{pattern:/^(\d{7})[，,、\s]*个(\d+)(元|块|米)?$/,handler:function(e,t){var r=e[1],a=e[2];return 7!==r.length?null:[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:r,money:a}]}},MULTI_LINE_QIMA_MONEY_GROUP:{pattern:/^((?:\d{7}[\-—~～@#￥%…&!、\\/*,一－。.＝=·吊赶打各\s]+\d+\s*\n?)+)$/m,handler:function(e,t){var r,i=n("f9d6"),s=i.METHOD_ID,l=t.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),u=new Map,c=Object(o["a"])(l);try{for(c.s();!(r=c.n()).done;){var d=r.value,m=d.match(/^(\d{7})[\-—~～@#￥%…&!、\\/*,一－。.＝=·吊赶打各\s]+([0-9]+)$/);if(m){var h=m[1],f=m[2];u.has(f)||u.set(f,[]),u.get(f).push(h)}}}catch(U){c.e(U)}finally{c.f()}var I,b=[],p=Object(o["a"])(u.entries());try{for(p.s();!(I=p.n()).done;){var _=Object(a["a"])(I.value,2),g=_[0],N=_[1];b.push({methodId:s.QIMA_ZULIU,betNumbers:N.join(","),money:g})}}catch(U){p.e(U)}finally{p.f()}return b.length?b:null}},QIMA_MULTI_LINE_ZULIU_MONEY_SUPER:{pattern:/^([\s\S]+)$/m,handler:function(e,t){var r,i=n("f9d6"),s=i.METHOD_ID,l=t.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),u=new Map,c=/^(\d{7})[\-—~～@#￥%…&!、一－。\\/*,，.＝=·\s]+(\d+)(元|米|块)?([，,./\\*\-\=。·、\s]*)?$/,d=[],m=Object(o["a"])(l);try{for(m.s();!(r=m.n()).done;){var h=r.value,f=h.match(c);if(!f)return console.log('【QIMA_MULTI_LINE_ZULIU_MONEY_SUPER】行 "'.concat(h,'" 不匹配七码格式，返回null')),null;if(f[1].length===f[2].length)return console.log('【QIMA_MULTI_LINE_ZULIU_MONEY_SUPER】行 "'.concat(h,'" 号码和金额长度相同，可能误匹配，返回null')),null;d.push({line:h,match:f})}}catch(E){m.e(E)}finally{m.f()}console.log("【QIMA_MULTI_LINE_ZULIU_MONEY_SUPER】所有 ".concat(d.length," 行都是七码格式，继续处理"));for(var I=0,b=d;I<b.length;I++){var p=b[I].match,_=p[1],g=p[2];u.has(g)||u.set(g,[]),u.get(g).push(_)}var N,U=[],v=Object(o["a"])(u.entries());try{for(v.s();!(N=v.n()).done;){var A=Object(a["a"])(N.value,2),y=A[0],M=A[1];U.push({methodId:s.QIMA_ZULIU,betNumbers:M.join(","),money:y})}}catch(E){v.e(E)}finally{v.f()}return console.log("【QIMA_MULTI_LINE_ZULIU_MONEY_SUPER】处理完成，返回 ".concat(U.length," 个结果组")),U.length?U:null}},QIMA_MULTI_FANGDUI_SUPER:{pattern:/^([0-9]{7}(?:[\-—–―－﹣﹘⸺⸻~～@#￥%…&!、\\/*,，.一－。＝=·\s+吊赶打各]+[0-9]{7})+)[\-—–―－﹣﹘⸺⸻~～@#￥%…&!、\\/*,，.一－。＝=·\s+吊赶打各]*(防对|防|组三)([0-9]+)$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=e[1].split(a).map((function(e){return e.trim()})).filter((function(e){return 7===e.length})),s=e[3];return i.length<2?null:[{methodId:o.QIMA_ZUSAN,betNumbers:i.join(","),money:s}]}},QIMA_MULTI_LINE_ZULIU_ZUSAN_KEYWORD:{pattern:/^([0-9]{7}(组六|组三)\d+(元|米|块)?[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各\s]*\n?)+$/m,handler:function(e,t){var n,r=t.split(/\r?\n/).map((function(e){return e.trim()})).filter(Boolean),s=new Map,l=Object(o["a"])(r);try{for(l.s();!(n=l.n()).done;){var u=n.value,c=u.match(/^([0-9]{7})(组六|组三)(\d+)(元|米|块)?[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各\s]*$/);if(c&&7===c[1].length){var d=c[1],m=c[2],h=c[3],f=m+"_"+h;s.has(f)||s.set(f,[]),s.get(f).push(d)}}}catch(M){l.e(M)}finally{l.f()}var I,b=[],p=Object(o["a"])(s.entries());try{for(p.s();!(I=p.n()).done;){var _=Object(a["a"])(I.value,2),g=_[0],N=_[1],U=g.split("_"),v=Object(a["a"])(U,2),A=v[0],y=v[1];b.push({methodId:"组六"===A?i["METHOD_ID"].QIMA_ZULIU:i["METHOD_ID"].QIMA_ZUSAN,betNumbers:N.join(","),money:y})}}catch(M){p.e(M)}finally{p.f()}return b.length?b:null}},QIMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER:{pattern:/^([\s\S]+)$/m,handler:function(e,t){var r=/(福彩|福|3D|3d)/.test(t),i=/(体彩|体|排|排三)/.test(t);if(r||i)return null;var s,l=t.split(/\r?\n/).map((function(e){return e.trim()})).filter(Boolean),u=new Map,c=new Map,d=/^([0-9]{7})[\-—~～@#￥%…&!、\\/*,一－–.。＝=·\s\/]+(\d+)\++(\d+)[元米块]*$/,m=/^([0-9]{7})[\-—~～@#￥%…&!、\\/*,一－–.。＝=·\s\/]+(\d+)[元米块]*$/,h=[],f=Object(o["a"])(l);try{for(f.s();!(s=f.n()).done;){var I=s.value,b=I.match(d);if(b)h.push({line:I,type:"zuliuZusan",match:b});else{if(b=I.match(m),!b)return console.log('【QIMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】行 "'.concat(I,'" 不匹配七码格式，返回null')),null;h.push({line:I,type:"zuliu",match:b})}}}catch(P){f.e(P)}finally{f.f()}console.log("【QIMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】所有 ".concat(h.length," 行都是七码格式，继续处理"));for(var p=0,_=h;p<_.length;p++){var g=_[p],N=g.match,U=g.type;if("zuliuZusan"===U){var v=N[1],A=N[2],y=N[3];u.has(A)||u.set(A,[]),u.get(A).push(v),c.has(y)||c.set(y,[]),c.get(y).push(v)}else if("zuliu"===U){var M=N[1],E=N[2];u.has(E)||u.set(E,[]),u.get(E).push(M)}}var D,O=[],S=Object(o["a"])(u.entries());try{for(S.s();!(D=S.n()).done;){var T=Object(a["a"])(D.value,2),L=T[0],w=T[1];O.push({methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:w.join(","),money:L})}}catch(P){S.e(P)}finally{S.f()}var j,R=Object(o["a"])(c.entries());try{for(R.s();!(j=R.n()).done;){var $=Object(a["a"])(j.value,2),Z=$[0],H=$[1];O.push({methodId:n("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:H.join(","),money:Z})}}catch(P){R.e(P)}finally{R.f()}return console.log("【QIMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】处理完成，返回 ".concat(O.length," 个结果组")),O.length?O:null}},QIMA_MONEY_UNIT_FANGDUI_SUPER:{pattern:/^(\d{7})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:元|米|块)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(防|防对|组三)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*$/,handler:function(e,t){var n=e[1],r=e[2];e[3];return 7!==n.length?null:[{methodId:i["METHOD_ID"].QIMA_ZUSAN,betNumbers:n,money:r}]}},LIUMA_TO_QIMA_MULTI_TRANSFORM:{pattern:/^((?:\d{6}[\-—~～@#￥%…&!、\\/*,，.＝=·\s]+){1,}\d{6})(各)?(转|套)[\s]*([0-9]+)\+([0-9]+)$/,handler:function(e,t){var a=n("f9d6"),o=a.SUPER_SEPARATORS,i=e[1],s=e[4],l=e[5],u=i.split(o).filter((function(e){return 6===e.length})),c=[];return u.forEach((function(e){var t=e.split(""),n="0123456789".split("").filter((function(e){return!t.includes(e)})),a=n.map((function(t){return e+t})).sort(),o=a.filter((function(e){return!c.includes(e)}));c.push.apply(c,Object(r["a"])(o))})),[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:c.join(","),money:s},{methodId:n("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:c.join(","),money:l}]}}};Object.keys(s).forEach((function(e){var t=s[e].pattern;"string"===typeof t&&t.endsWith("$")?s[e].pattern=t.replace(/\$$/,"[，,./*-=。·、s]*$"):t instanceof RegExp&&t.source.endsWith("$")&&(s[e].pattern=new RegExp(t.source.replace(/\$$/,"[，,./*-=。·、s]*$")))}))},3506:function(e,t,n){"use strict";n.r(t),n.d(t,"NINE_CODE_PATTERNS",(function(){return i}));var r=n("3835"),a=n("b85c"),o=(n("99af"),n("4de4"),n("a15b"),n("d81d"),n("14d9"),n("4ec9"),n("b64b"),n("d3b7"),n("4d63"),n("c607"),n("ac1f"),n("2c3e"),n("00b4"),n("25f0"),n("8a79"),n("3ca3"),n("466d"),n("5319"),n("1276"),n("498a"),n("0643"),n("76d6"),n("2382"),n("4e3e"),n("a573"),n("9a9a"),n("159b"),n("ddb0"),n("f9d6")),i={JIUMA_TRANSFORM:{pattern:/^(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?(\d{9})(转|套|拖)(?:各)?(\d+)(?:\+(\d+))?(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?$/,handler:function(e,t){var n=e[2],r=e[3],a=e[4],i=[];return a?(i.push({methodId:o["METHOD_ID"].JIUMA_ZULIU,betNumbers:n,money:r}),i.push({methodId:o["METHOD_ID"].JIUMA_ZUSAN,betNumbers:n,money:a})):i.push({methodId:o["METHOD_ID"].JIUMA_ZULIU,betNumbers:n,money:r}),console.log("九码 handler 返回:",i),i}},JIUMA_ZUSAN:{pattern:/^(\d{9})[\-—~～@#￥%…&!、\\/*,，一－.＝=·\s]*组三?[\-—~～@#￥%…&!、\\/*,，。，—一－.＝=·\s]*([\d]+)?(元|块|米)?[\-—~～@#￥%…&!、。，—一－\\/*,，.＝=·\s]*$/,handler:function(e,t){return 9!==e[1].length?null:[{methodId:o["METHOD_ID"].JIUMA_ZUSAN,betNumbers:e[1],money:e[2]||""}]}},JIUMA_FANGDUI:{pattern:/^(\d{9})防对?(?:-?(\d+))?$/,handler:function(e,t){return 9!==e[1].length?null:[{methodId:o["METHOD_ID"].JIUMA_ZUSAN,betNumbers:e[1],money:e[2]||""}]}},MULTI_JIUMA:{pattern:/^(\d{9}(?:[^0-9]+\d{9})*)(?:各([一二三四五六七八九十百千万\d]+))?(?:防([一二三四五六七八九十百千万\d]+))?$/,handler:function(e,t){var r=n("f9d6"),a=r.chineseToNumber,i=t.split(o["BASE_PATTERNS"].SEPARATORS).filter(Boolean).filter((function(e){return 9===e.length}));if(!i.length||!i.every((function(e){return 9===e.length})))return null;var s=e[2]?a(e[2]):"",l=e[3]?a(e[3]):"",u=[];return s&&u.push({methodId:o["METHOD_ID"].JIUMA_ZULIU,betNumbers:i.join(","),money:s}),l&&u.push({methodId:o["METHOD_ID"].JIUMA_ZUSAN,betNumbers:i[i.length-1],money:l}),u.length?u:null}},JIUMA_ZULIU_ZUSAN_MULTI_MONEY_SUPER:{pattern:/^[－\-—~～@#￥%…&!、\\/*,一－。＝=·\s+吊赶打各]*(\d{9}(?:[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]+\d{9})*)[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]+(\d+)[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]*[+](\d+)(?:元)?$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=e[1].split(a).filter(Boolean),i=e[2],s=e[3];return[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:o.join(","),money:i},{methodId:n("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:o.join(","),money:s}]}},JIUMA_ZULIU_ZU_SUPER:{pattern:/^(\d{9})组(\d+)$/,handler:function(e,t){var r=e[1],a=e[2];return[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:r,money:a}]}},JIUMA_ZULIU_MULTI_SUPER:{pattern:/^(\d{9}(?:[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]+\d{9})*)[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]+(\d+)$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=e[1].split(a).filter(Boolean),i=e[2];return[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:o.join(","),money:i}]}},JIUMA_ZUSAN_ZU_SUPER:{pattern:/^(\d{9})组三(\d+)$/,handler:function(e,t){var r=e[1],a=e[2];return[{methodId:n("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:r,money:a}]}},JIUMA_ZHIZU_MULTI_SUPER:{pattern:/^(\d{9}(?:[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]+\d{9})*)[\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]*(直组|组直|值组|组值)[\s]*各?(\d+)(?:元)?$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=e[1].split(a).filter(Boolean),i=e[3];return[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:o.join(","),money:i},{methodId:n("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:o.join(","),money:i}]}},JIUMA_ZULIU_FANG:{pattern:/^(\d{9})-(\d+)防(\d+)$/,handler:function(e,t){var n=e[1],r=e[2],a=e[3];return[{methodId:o["METHOD_ID"].JIUMA_ZULIU,betNumbers:n,money:r},{methodId:o["METHOD_ID"].JIUMA_ZUSAN,betNumbers:n,money:a}]}},JIUMA_MULTI_ZHIZU_EACH_SUPER:{pattern:/^(直组|组直|值组|组值)各([0-9]+)[\s\+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各\n]+([\d\s\+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]+)$/i,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=e[2],i=e[3].replace(/\n/g," "),s=i.split(a).map((function(e){return e.trim()})).filter((function(e){return/^\d{9}$/.test(e)}));return[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:s.join(","),money:o},{methodId:n("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:s.join(","),money:o}]}},JIUMA_MULTI_EACH_SUPER:{pattern:/^(\d{9}(?:[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]+\d{9})*)各(\d+)$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=e[1].split(a).filter(Boolean);if(!o.length||o.some((function(e){return 9!==e.length})))return[];var i=e[2];return[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:o.join(","),money:i}]}},JIUMA_SLASH_TWO_MONEY:{pattern:/^(?!.*[\r\n])(\d{9})[\-—~～@#￥%…&!、\\/*,，.。\u4e00\uff0d\uff1d=· 　\t]+(\d+)[+]+(\d+)$/,handler:function(e,t){var r=e[1],a=e[2],o=e[3];return[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:r,money:a},{methodId:n("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:r,money:o}]}},JIUMA_MULTI_SUPER_SLASH_TWO_MONEY:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、＝一－。=·吊赶打各]+)\/(\d+)\/(\d+)$/,handler:function(e,t){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=n("f9d6"),o=a.SUPER_SEPARATORS,i=e[1].split(o).map((function(e){return e.trim()})).filter((function(e){return 9===e.length}));if(!i.length)return[];var s=e[2],l=e[3];return r&&(/^\d{9}$/.test(s)||/^\d{9}$/.test(l))?null:[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:i.join(","),money:s},{methodId:n("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:i.join(","),money:l}]}},JIUMA_UNIVERSAL_SUPER:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～一－—。@#￥%…&!、＝=·吊赶打各]+)[\-~～@#￥%…&!、\\/*,，一－—。.＝=·\s+吊赶打各]+([0-9]+)(元|米|块)?$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=e[1].split(a).map((function(e){return e.trim()})).filter(Boolean),s=e[2];return 1===i.length&&9===i[0].length?[{methodId:o.JIUMA_ZULIU,betNumbers:i[0],money:s}]:i.length>=2&&i.every((function(e){return 9===e.length}))?[{methodId:o.JIUMA_ZULIU,betNumbers:i.join(","),money:s}]:null}},JIUMA_EACH_PLUS_SUPER:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!一－。、＝=·吊赶打各]+)各([0-9]+)\+([0-9]+)$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=e[1].split(a).map((function(e){return e.trim()})).filter(Boolean),s=e[2],l=e[3];return 1===i.length&&9===i[0].length?[{methodId:o.JIUMA_ZULIU,betNumbers:i[0],money:s},{methodId:o.JIUMA_ZUSAN,betNumbers:i[0],money:l}]:i.length>=2&&i.every((function(e){return 9===e.length}))?[{methodId:o.JIUMA_ZULIU,betNumbers:i.join(","),money:s},{methodId:o.JIUMA_ZUSAN,betNumbers:i.join(","),money:l}]:null}},JIUMA_UNIVERSAL_SUPER_NEW:{pattern:/^([0-9]{9,})[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]+(\d+)(元|块|米)?$/,handler:function(e,t){var r=e[1],a=e[2];return r&&9===r.length?[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:r,money:a}]:null}},JIUMA_DA_MONEY:{pattern:/^([0-9]{9})打(\d+)(元|块|米)?$/,handler:function(e,t){return[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:e[1],money:e[2]}]}},JIUMA_MULTI_WITH_MONEY_SLASH_PLUS:{pattern:/^(\d{9}(?:[\s,，\-]+?\d{9})*)\/\s*(\d+)\+(\d+)$/,handler:function(e,t){var r=e[1].split(/[\s,，\-]+/).filter(Boolean),a=e[2],o=e[3];return[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:r.join(","),money:a},{methodId:n("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:r.join(","),money:o}]}},JIUMA_MULTI_LINE_MONEY_PLUS:{pattern:/^((?:\d{9}[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+\d+\+\d+\s*\n?)+)$/m,handler:function(e,t){var o,i=n("f9d6"),s=i.METHOD_ID,l=t.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),u=new Map,c=!0,d=Object(a["a"])(l);try{for(d.s();!(o=d.n()).done;){var m=o.value,h=m.match(/^(\d{9})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+(\d+)\+(\d+)$/);if(!h){c=!1;break}var f=h[1];if(9!==f.length){c=!1;break}var I=m.split(/[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+/)[0];if(9!==I.length||!/^\d{9}$/.test(I)){c=!1;break}var b="".concat(h[2],"+").concat(h[3]);u.has(b)||u.set(b,[]),u.get(b).push(f)}}catch(D){d.e(D)}finally{d.f()}if(!c||0===u.size)return null;var p,_=[],g=Object(a["a"])(u.entries());try{for(g.s();!(p=g.n()).done;){var N=Object(r["a"])(p.value,2),U=N[0],v=N[1],A=U.split("+"),y=Object(r["a"])(A,2),M=y[0],E=y[1];_.push({methodId:s.JIUMA_ZULIU,betNumbers:v.join(","),money:M}),_.push({methodId:s.JIUMA_ZUSAN,betNumbers:v.join(","),money:E})}}catch(D){g.e(D)}finally{g.f()}return _.length?_:null}},JIUMA_MULTI_LINE_MONEY_PLUS_CN:{pattern:/^((?:\d{9}[，,、]?[一二三四五六七八九十百千万\d]+加[一二三四五六七八九十百千万\d]+\s*\n?)+)$/m,handler:function(e,t){var o,i=n("f9d6"),s=i.METHOD_ID,l=i.chineseToNumber,u=t.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),c=new Map,d=Object(a["a"])(u);try{for(d.s();!(o=d.n()).done;){var m=o.value,h=m.match(/^(\d{9})[，,、]?([一二三四五六七八九十百千万\d]+)加([一二三四五六七八九十百千万\d]+)$/);if(h){var f=h[1],I=l(h[2]),b=l(h[3]),p="".concat(I,"+").concat(b);c.has(p)||c.set(p,[]),c.get(p).push(f)}}}catch(O){d.e(O)}finally{d.f()}var _,g=[],N=Object(a["a"])(c.entries());try{for(N.s();!(_=N.n()).done;){var U=Object(r["a"])(_.value,2),v=U[0],A=U[1],y=v.split("+"),M=Object(r["a"])(y,2),E=M[0],D=M[1];g.push({methodId:s.JIUMA_ZULIU,betNumbers:A.join(","),money:E}),g.push({methodId:s.JIUMA_ZUSAN,betNumbers:A.join(","),money:D})}}catch(O){N.e(O)}finally{N.f()}return g.length?g:null}},JIUMA_MULTI_EACH_FANGDUI_SLASH:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、一－。＝=·吊赶打各]+)[/\-~～@#￥%…&!、\\/*,，.一－。＝=·吊赶打各]+各防对([一二三四五六七八九十百千万\d]+)$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=r.chineseToNumber,s=e[1].split(a).map((function(e){return e.trim()})).filter((function(e){return 9===e.length})),l=i(e[2]);return s.length?[{methodId:o.JIUMA_ZUSAN,betNumbers:s.join(","),money:l}]:null}},JIUMA_MULTI_EACH_FANGDUI:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&一－。!、＝=·吊赶打各]+)各防对([一二三四五六七八九十百千万\d]+)$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=r.chineseToNumber,s=e[1].split(a).map((function(e){return e.trim()})).filter((function(e){return 9===e.length})),l=i(e[2]);return s.length?[{methodId:o.JIUMA_ZUSAN,betNumbers:s.join(","),money:l}]:null}},JIUMA_SLASH_MONEY:{pattern:/^\s*(\d{9})\s*\/\s*([一二三四五六七八九十百千万\d]+)\s*$/,handler:function(e,t){var r=n("f9d6"),a=r.METHOD_ID,o=r.chineseToNumber,i=e[1],s=o(e[2]);return[{methodId:a.JIUMA_ZULIU,betNumbers:i,money:s}]}},JIUMA_ZULIU_ZUSAN_MONEY_PAIR:{pattern:/^(\d{9}[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*)组六[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*(\d+)[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*组三[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*(\d+)$/,handler:function(e,t){var n=e[1],r=e[2],a=e[3];return[{methodId:o["METHOD_ID"].JIUMA_ZULIU,betNumbers:n,money:r},{methodId:o["METHOD_ID"].JIUMA_ZUSAN,betNumbers:n,money:a}]}},JIUMA_ZULIU:{pattern:/^(\d{9})组六?(\d+)[，,./\*\-一－。\=。·、\s]*$/,handler:function(e,t){var n=e[1];if(9!==n.length)return null;var r=e[2]||"";return[{methodId:o["METHOD_ID"].JIUMA_ZULIU,betNumbers:n,money:r}]}},JIUMA_ZULIU_ZUSAN_COMBO:{pattern:/^(\d{9}(?:[+＋\\-—~～@#￥%…&!、\\/*,，。一—~.＝=·吊赶打各\s]+\d{9})*)\/(\d+)\+(\d+)$/,handler:function(e,t){var r=n("f9d6"),a=(r.SUPER_SEPARATORS,e[1].split(/[。.．、,，\s]+/).map((function(e){return e.trim()})).filter(Boolean)),o=e[2],i=e[3];return a.length&&a.every((function(e){return 9===e.length}))?[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:a.join(","),money:o},{methodId:n("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:a.join(","),money:i}]:null}},JIUMA_SINGLE_LINE_ZULIU_ZUSAN_PLUS:{pattern:/^(\d{9})\s+(\d+)[+＋\\-—~～@#￥%…&!、\\/*,，。一—~.＝=·吊赶打各](\d+)$/,handler:function(e,t){return[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:e[1],money:e[2]},{methodId:n("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:e[1],money:e[3]}]}},JIUMA_MULTI_LINE_ZULIU_ZUSAN_PLUS:{pattern:/^([\s\S]+)$/m,handler:function(e,t){var o,i=n("f9d6"),s=i.SUPER_SEPARATORS,l=t.split(/\r?\n/).map((function(e){return e.trim()})).filter(Boolean),u=new Map,c=new Map,d=Object(a["a"])(l);try{for(d.s();!(o=d.n()).done;){var m=o.value,h=m.match(new RegExp("^(\\d{9})".concat(s.source,"(\\d+)\\+(\\d+)$")));if(h){var f=h[1],I=h[2],b=h[3],p=I;u.has(p)||u.set(p,[]),u.get(p).push(f);var _=b;c.has(_)||c.set(_,[]),c.get(_).push(f)}else{var g=m.match(new RegExp("^(\\d{9})".concat(s.source,"(\\d+)$")));if(g){var N=g[1],U=g[2];u.has(U)||u.set(U,[]),u.get(U).push(N)}}}}catch(j){d.e(j)}finally{d.f()}var v,A=[],y=Object(a["a"])(u.entries());try{for(y.s();!(v=y.n()).done;){var M=Object(r["a"])(v.value,2),E=M[0],D=M[1];A.push({methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:D.join(","),money:E})}}catch(j){y.e(j)}finally{y.f()}var O,S=Object(a["a"])(c.entries());try{for(S.s();!(O=S.n()).done;){var T=Object(r["a"])(O.value,2),L=T[0],w=T[1];A.push({methodId:n("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:w.join(","),money:L})}}catch(j){S.e(j)}finally{S.f()}return A.length?A:null}},JIUMA_MULTI_LINE_ZULIU_PLUS:{pattern:/^((?:\d{9}[+＋\\-—~～@#￥%…&!、\\/*,，。一—~.＝=·吊赶打各]\d+\s*\n?)+)$/m,handler:function(e,t){var o,i=n("f9d6"),s=i.METHOD_ID,l=t.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),u=new Map,c=[],d=Object(a["a"])(l);try{for(d.s();!(o=d.n()).done;){var m=o.value,h=m.match(/^(\d{9})[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]+(\d+)$/);if(h){var f=h[1],I=h[2];u.has(I)||u.set(I,[]),u.get(I).push(f)}else c.push(m)}}catch(v){d.e(v)}finally{d.f()}var b,p=[],_=Object(a["a"])(u.entries());try{for(_.s();!(b=_.n()).done;){var g=Object(r["a"])(b.value,2),N=g[0],U=g[1];p.push({methodId:s.JIUMA_ZULIU,betNumbers:U.join(","),money:N})}}catch(v){_.e(v)}finally{_.f()}return p}},JIUMA_ZULIU_FANG_ZUSAN_PLUS:{pattern:/^(\d{9})[\-—~～@#￥%…&!、\\/*,，.一－。＝=·吊赶打各]*(\d+)[\-—~～@#￥%…&!、\\/*,，.一－。＝=·吊赶打各]*(防对|防)(\d+)$/,handler:function(e,t){return[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:e[1],money:e[2]},{methodId:n("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:e[1],money:e[3]}]}},JIUMA_MULTI_LINE_ZULIU_FANG_ZUSAN_GROUP:{pattern:/^([\s\S]+)$/m,handler:function(e,t){var o,i=t.split(/[\r\n]+/).map((function(e){return e.trim()})).filter(Boolean),s=new Map,l=Object(a["a"])(i);try{for(l.s();!(o=l.n()).done;){var u=o.value,c=u.match(/^(\d{9})-?(\d+)防(\d+)$/);if(c){var d=c[1],m=c[2],h=c[3],f="".concat(m,"|").concat(h);s.has(f)||s.set(f,[]),s.get(f).push(d)}}}catch(M){l.e(M)}finally{l.f()}var I,b=[],p=Object(a["a"])(s.entries());try{for(p.s();!(I=p.n()).done;){var _=Object(r["a"])(I.value,2),g=_[0],N=_[1],U=g.split("|"),v=Object(r["a"])(U,2),A=v[0],y=v[1];b.push({methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:N.join(","),money:A}),b.push({methodId:n("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:N.join(","),money:y})}}catch(M){p.e(M)}finally{p.f()}return b.length?b:null}},JIUMA_MULTI_TRANSFORM_EACH:{pattern:/^(\d{8}(?:[\-~～@#￥%…&!、\\/*,一－。，.＝=·\s+吊赶打各]+\d{8})*)(转|套|拖)各(\d+)\+(\d+)$/,handler:function(e,t){var r,o=n("f9d6"),i=o.SUPER_SEPARATORS,s=o.METHOD_ID,l=o.generateTransformNumbers,u=e[1].split(i).filter(Boolean),c=e[3],d=e[4],m=[],h=Object(a["a"])(u);try{for(h.s();!(r=h.n()).done;){var f=r.value,I=l(f);m.push({methodId:s.JIUMA_ZULIU,betNumbers:I.join(","),money:c}),m.push({methodId:s.JIUMA_ZUSAN,betNumbers:I.join(","),money:d})}}catch(b){h.e(b)}finally{h.f()}return m}},JIUMA_MULTI_LINE_TRANSFORM:{pattern:/^((?:(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?\d{9}(转|套|拖)\d+(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?\s*\n?)+)$/m,handler:function(e,t){var o,i=n("f9d6"),s=i.METHOD_ID,l=t.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),u=new Map,c=Object(a["a"])(l);try{for(c.s();!(o=c.n()).done;){var d=o.value,m=d.match(/^(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?(\d{9})(转|套|拖)(\d+)(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?$/);if(m){var h=m[2],f=m[3];u.has(f)||u.set(f,[]),u.get(f).push(h)}}}catch(U){c.e(U)}finally{c.f()}var I,b=[],p=Object(a["a"])(u.entries());try{for(p.s();!(I=p.n()).done;){var _=Object(r["a"])(I.value,2),g=_[0],N=_[1];b.push({methodId:s.JIUMA_ZULIU,betNumbers:N.join(","),money:g})}}catch(U){p.e(U)}finally{p.f()}return b.length?b:null}},JIUMA_MULTI_COLON_EACH_SUPER:{pattern:/^(\d{9}(?::|：\d{9})*)各(\d+)$/,handler:function(e,t){var r=t.split(/:|：/).map((function(e){return e.trim()})).filter((function(e){return 9===e.length})),a=e[2];return r.length?[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:r.join(","),money:a}]:null}},JIUMA_MULTI_YIGE_EACH_PLUS:{pattern:/^([\d一]+)一([\d一]+)一?各?(\d+)\+(\d+)$/,handler:function(e,t){var r=t.split("一").map((function(e){return e.replace(/[^\d]/g,"")})).filter((function(e){return 9===e.length})),a=e[3],o=e[4];return r.length?[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:r.join(","),money:a},{methodId:n("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:r.join(","),money:o}]:null}},JIUMA_DIAN_XMA_ZULIU_FANG_ZUSAN:{pattern:/^(\d{9})[。.．、,，\s]+9码(\d+)防(\d+)$/,handler:function(e,t){return[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:e[1],money:e[2]},{methodId:n("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:e[1],money:e[3]}]}},JIUMA_MULTI_LINE_ZULIU_MONEY_GROUP:{pattern:/^((?:\d{9}\s+\d+\s*\n?)+)$/m,handler:function(e,t){var o,i=n("f9d6"),s=i.METHOD_ID,l=t.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),u=new Map,c=Object(a["a"])(l);try{for(c.s();!(o=c.n()).done;){var d=o.value,m=d.match(/^(\d{9})\s+(\d+)$/);if(m){var h=m[1],f=m[2];u.has(f)||u.set(f,[]),u.get(f).push(h)}}}catch(U){c.e(U)}finally{c.f()}var I,b=[],p=Object(a["a"])(u.entries());try{for(p.s();!(I=p.n()).done;){var _=Object(r["a"])(I.value,2),g=_[0],N=_[1];b.push({methodId:s.JIUMA_ZULIU,betNumbers:N.join(","),money:g})}}catch(U){p.e(U)}finally{p.f()}return b.length?b:null}},JIUMA_MULTI_NUMBERS_EACH_FANG:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、一－。＝=·吊赶打各]+)各(\d+)(防|防对|放对)(\d+)$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=e[1].split(a).map((function(e){return e.trim()})).filter((function(e){return 9===e.length}));if(!i.length)return null;var s=e[2],l=e[4];return[{methodId:o.JIUMA_ZULIU,betNumbers:i.join(","),money:s},{methodId:o.JIUMA_ZUSAN,betNumbers:i.join(","),money:l}]}},JIUMA_YI_YI_ONE_MONEY:{pattern:/^(\d{9})一(\d{9})一个(\d+)(元|块|米)?$/,handler:function(e,t){var r=e[1],a=e[2],o=e[3];return 9!==r.length||9!==a.length?null:[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:r+","+a,money:o}]}},JIUMA_GE_MONEY:{pattern:/^(\d{9})[，,、\s]*个(\d+)(元|块|米)?$/,handler:function(e,t){var r=e[1],a=e[2];return 9!==r.length?null:[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:r,money:a}]}},MULTI_LINE_JIUMA_MONEY_GROUP:{pattern:/^((?:\d{9}[\-—~～@#￥%…&!、\\/*,一－。.＝=·吊赶打各\s]+\d+\s*\n?)+)$/m,handler:function(e,t){var o,i=n("f9d6"),s=i.METHOD_ID,l=t.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),u=new Map,c=Object(a["a"])(l);try{for(c.s();!(o=c.n()).done;){var d=o.value,m=d.match(/^(\d{9})[\-—~～@#￥%…&!、\\/*,一－。.＝=·吊赶打各\s]+([0-9]+)$/);if(m){var h=m[1],f=m[2];u.has(f)||u.set(f,[]),u.get(f).push(h)}}}catch(U){c.e(U)}finally{c.f()}var I,b=[],p=Object(a["a"])(u.entries());try{for(p.s();!(I=p.n()).done;){var _=Object(r["a"])(I.value,2),g=_[0],N=_[1];b.push({methodId:s.JIUMA_ZULIU,betNumbers:N.join(","),money:g})}}catch(U){p.e(U)}finally{p.f()}return b.length?b:null}},JIUMA_MULTI_LINE_ZULIU_MONEY_SUPER:{pattern:/^([\s\S]+)$/m,handler:function(e,t){var o,i=n("f9d6"),s=i.METHOD_ID,l=t.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),u=new Map,c=/^(\d{9})[\-—~～@#￥%…&!、一－。\\/*,，.＝=·\s]+(\d+)(元|米|块)?([，,./\\*\-\=。·、\s]*)?$/,d=[],m=Object(a["a"])(l);try{for(m.s();!(o=m.n()).done;){var h=o.value,f=h.match(c);if(!f)return console.log('【JIUMA_MULTI_LINE_ZULIU_MONEY_SUPER】行 "'.concat(h,'" 不匹配九码格式，返回null')),null;if(f[1].length===f[2].length)return console.log('【JIUMA_MULTI_LINE_ZULIU_MONEY_SUPER】行 "'.concat(h,'" 号码和金额长度相同，可能误匹配，返回null')),null;d.push({line:h,match:f})}}catch(E){m.e(E)}finally{m.f()}console.log("【JIUMA_MULTI_LINE_ZULIU_MONEY_SUPER】所有 ".concat(d.length," 行都是九码格式，继续处理"));for(var I=0,b=d;I<b.length;I++){var p=b[I].match,_=p[1],g=p[2];u.has(g)||u.set(g,[]),u.get(g).push(_)}var N,U=[],v=Object(a["a"])(u.entries());try{for(v.s();!(N=v.n()).done;){var A=Object(r["a"])(N.value,2),y=A[0],M=A[1];U.push({methodId:s.JIUMA_ZULIU,betNumbers:M.join(","),money:y})}}catch(E){v.e(E)}finally{v.f()}return console.log("【JIUMA_MULTI_LINE_ZULIU_MONEY_SUPER】处理完成，返回 ".concat(U.length," 个结果组")),U.length?U:null}},JIUMA_MULTI_FANGDUI_SUPER:{pattern:/^([0-9]{9}(?:[\-—–―－﹣﹘⸺⸻~～@#￥%…&!、\\/*,，.一－。＝=·\s+吊赶打各]+[0-9]{9})+)[\-—–―－﹣﹘⸺⸻~～@#￥%…&!、\\/*,，.一－。＝=·\s+吊赶打各]*(防对|防|组三)([0-9]+)$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=e[1].split(a).map((function(e){return e.trim()})).filter((function(e){return 9===e.length})),s=e[3];return i.length<2?null:[{methodId:o.JIUMA_ZUSAN,betNumbers:i.join(","),money:s}]}},JIUMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER:{pattern:/^([\s\S]+)$/m,handler:function(e,t){var o=/(福彩|福|3D|3d)/.test(t),i=/(体彩|体|排|排三)/.test(t);if(o||i)return null;var s,l=t.split(/\r?\n/).map((function(e){return e.trim()})).filter(Boolean),u=new Map,c=new Map,d=/^([0-9]{9})[\-—~～@#￥%…&!、\\/*,一－–.。＝=·\s\/]+(\d+)\++(\d+)[元米块]*$/,m=/^([0-9]{9})[\-—~～@#￥%…&!、\\/*,一－–.。＝=·\s\/]+(\d+)[元米块]*$/,h=[],f=Object(a["a"])(l);try{for(f.s();!(s=f.n()).done;){var I=s.value,b=I.match(d);if(b)h.push({line:I,type:"zuliuZusan",match:b});else{if(b=I.match(m),!b)return console.log('【JIUMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】行 "'.concat(I,'" 不匹配九码格式，返回null')),null;h.push({line:I,type:"zuliu",match:b})}}}catch(P){f.e(P)}finally{f.f()}console.log("【JIUMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】所有 ".concat(h.length," 行都是九码格式，继续处理"));for(var p=0,_=h;p<_.length;p++){var g=_[p],N=g.match,U=g.type;if("zuliuZusan"===U){var v=N[1],A=N[2],y=N[3];u.has(A)||u.set(A,[]),u.get(A).push(v),c.has(y)||c.set(y,[]),c.get(y).push(v)}else if("zuliu"===U){var M=N[1],E=N[2];u.has(E)||u.set(E,[]),u.get(E).push(M)}}var D,O=[],S=Object(a["a"])(u.entries());try{for(S.s();!(D=S.n()).done;){var T=Object(r["a"])(D.value,2),L=T[0],w=T[1];O.push({methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:w.join(","),money:L})}}catch(P){S.e(P)}finally{S.f()}var j,R=Object(a["a"])(c.entries());try{for(R.s();!(j=R.n()).done;){var $=Object(r["a"])(j.value,2),Z=$[0],H=$[1];O.push({methodId:n("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:H.join(","),money:Z})}}catch(P){R.e(P)}finally{R.f()}return console.log("【JIUMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】处理完成，返回 ".concat(O.length," 个结果组")),O.length?O:null}},JIUMA_MONEY_UNIT_FANGDUI_SUPER:{pattern:/^(\d{9})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:元|米|块)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(防|防对|组三)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*$/,handler:function(e,t){var n=e[1],r=e[2];e[3];return 9!==n.length?null:[{methodId:o["METHOD_ID"].JIUMA_ZUSAN,betNumbers:n,money:r}]}},JIUMA_MULTI_LINE_ZULIU_ZUSAN_KEYWORD:{pattern:/^([0-9]{9}(组六|组三)\d+(元|米|块)?[，,./\*\-\=。一－。·、\s]*\n?)+$/m,handler:function(e,t){var n,i=t.split(/\r?\n/).map((function(e){return e.trim()})).filter(Boolean),s=new Map,l=Object(a["a"])(i);try{for(l.s();!(n=l.n()).done;){var u=n.value,c=u.match(/^([0-9]{9})(组六|组三)(\d+)(元|米|块)?[，,./\*\-\=。一－。·、\s]*$/);if(c&&9===c[1].length){var d=c[1],m=c[2],h=c[3],f=m+"_"+h;s.has(f)||s.set(f,[]),s.get(f).push(d)}}}catch(M){l.e(M)}finally{l.f()}var I,b=[],p=Object(a["a"])(s.entries());try{for(p.s();!(I=p.n()).done;){var _=Object(r["a"])(I.value,2),g=_[0],N=_[1],U=g.split("_"),v=Object(r["a"])(U,2),A=v[0],y=v[1];b.push({methodId:"组六"===A?o["METHOD_ID"].JIUMA_ZULIU:o["METHOD_ID"].JIUMA_ZUSAN,betNumbers:N.join(","),money:y})}}catch(M){p.e(M)}finally{p.f()}return b.length?b:null}}};Object.keys(i).forEach((function(e){var t=i[e].pattern;"string"===typeof t&&t.endsWith("$")?i[e].pattern=t.replace(/\$$/,"[，,./*-=。·、s]*$"):t instanceof RegExp&&t.source.endsWith("$")&&(i[e].pattern=new RegExp(t.source.replace(/\$$/,"[，,./*-=。·、s]*$")))}))},"35d4":function(e,t,n){"use strict";n.d(t,"d",(function(){return a})),n.d(t,"e",(function(){return o})),n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return s})),n.d(t,"c",(function(){return l}));var r=n("b775");function a(e){return Object(r["a"])({url:"/game/record/list",method:"get",params:e})}function o(e){return Object(r["a"])({url:"/game/record",method:"put",data:e})}function i(e){return Object(r["a"])({url:"/game/record/"+e,method:"delete"})}function s(e){return Object(r["a"])({url:"/game/record/serial/"+e,method:"delete"})}function l(){return Object(r["a"])({url:"/game/record/generateSerialNumber",method:"get"})}},4069:function(e,t,n){"use strict";var r=n("44d2");r("flat")},"414a":function(e,t,n){"use strict";n("01e0")},"4ac9":function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("div",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],staticClass:"search-container"},[n("el-form",{ref:"queryForm",staticClass:"search-form",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"80px"}},[n("el-form-item",{attrs:{label:"玩法",prop:"methodId"}},[n("el-select",{staticStyle:{width:"150px"},attrs:{placeholder:"请选择玩法",clearable:"","prefix-icon":"el-icon-star-on"},on:{change:e.handleQuery},model:{value:e.queryParams.methodId,callback:function(t){e.$set(e.queryParams,"methodId",t)},expression:"queryParams.methodId"}},e._l(e.gameMethodsData,(function(e){return n("el-option",{key:"method_"+(e.sysUserId||"default")+"_"+e.methodId,attrs:{label:e.methodName,value:e.methodId}})})),1)],1),n("el-form-item",{attrs:{label:"流水号",prop:"serialNumber"}},[n("el-select",{staticStyle:{width:"150px"},attrs:{placeholder:"请选择流水号",clearable:"",filterable:"","prefix-icon":"el-icon-document"},on:{change:e.handleQuery},model:{value:e.queryParams.serialNumber,callback:function(t){e.$set(e.queryParams,"serialNumber",t)},expression:"queryParams.serialNumber"}},e._l(e.serialNumberList,(function(e,t){return n("el-option",{key:"serial_"+t+"_"+e,attrs:{label:e,value:e}})})),1)],1),e.isAdmin?n("el-form-item",{attrs:{label:"用户ID",prop:"sysUserId"}},[n("el-select",{staticStyle:{width:"150px"},attrs:{placeholder:"请选择用户ID",clearable:"",filterable:"","prefix-icon":"el-icon-user-solid"},on:{change:e.handleUserIdChange},model:{value:e.queryParams.sysUserId,callback:function(t){e.$set(e.queryParams,"sysUserId",t)},expression:"queryParams.sysUserId"}},e._l(e.allUserList,(function(e){return n("el-option",{key:"admin_user_"+e.userId,attrs:{label:e.userId+" - "+e.userName,value:e.userId}})})),1)],1):e._e(),n("el-form-item",{attrs:{label:"玩家",prop:"userId"}},[n("el-select",{staticStyle:{width:"150px"},attrs:{placeholder:"请选择玩家",clearable:"","prefix-icon":"el-icon-user"},on:{change:e.handlePlayerChange},model:{value:e.queryParams.userId,callback:function(t){e.$set(e.queryParams,"userId",t)},expression:"queryParams.userId"}},e._l(e.userList,(function(e){return n("el-option",{key:"player_"+(e.sysUserId||"default")+"_"+e.userId,attrs:{label:e.name,value:e.userId,"data-sys-user-id":e.sysUserId}})})),1)],1),n("el-form-item",{attrs:{label:"是否中奖",prop:"shifouzj"}},[n("el-select",{staticStyle:{width:"120px"},attrs:{placeholder:"请选择",clearable:"","prefix-icon":"el-icon-trophy"},on:{change:e.handleQuery},model:{value:e.queryParams.shifouzj,callback:function(t){e.$set(e.queryParams,"shifouzj",t)},expression:"queryParams.shifouzj"}},[n("el-option",{attrs:{label:"已中奖",value:1}}),n("el-option",{attrs:{label:"未中奖",value:0}})],1)],1),n("el-form-item",{attrs:{label:"识别框",prop:"shibie"}},[n("el-input",{staticStyle:{width:"350px"},attrs:{placeholder:"请输入识别内容",clearable:"",type:"textarea","prefix-icon":"el-icon-search"},model:{value:e.queryParams.shibie,callback:function(t){e.$set(e.queryParams,"shibie",t)},expression:"queryParams.shibie"}})],1),n("el-form-item",[n("el-button",{staticClass:"search-btn",attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleQuery}},[e._v("搜索")]),n("el-button",{staticClass:"reset-btn",attrs:{icon:"el-icon-refresh"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),n("div",{staticClass:"toolbar-container"},[n("div",{staticClass:"toolbar-content"},[n("div",{staticClass:"toolbar-left"},[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["game:record:add"],expression:"['game:record:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"small"},on:{click:e.handleAdd}},[e._v("新增")]),n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["game:record:remove"],expression:"['game:record:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"small",disabled:e.multipleSerial},on:{click:e.handleBatchDeleteSerial}},[e._v("批量删除流水")]),n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["game:record:export"],expression:"['game:record:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"small"},on:{click:e.handleExport}},[e._v("导出")]),n("el-button",{attrs:{type:"info",plain:"",icon:e.isExpandAll?"el-icon-s-fold":"el-icon-s-unfold",size:"small"},on:{click:e.toggleExpandSecondLevel}},[e._v(" "+e._s(e.isExpandAll?"收回二级":"展开二级")+" ")]),n("el-button",{attrs:{type:"warning",plain:"",icon:e.isExpandThirdLevel?"el-icon-s-fold":"el-icon-s-unfold",size:"small"},on:{click:e.toggleExpandThirdLevel}},[e._v(" "+e._s(e.isExpandThirdLevel?"收回三级":"展开三级")+" ")]),n("el-button",{staticClass:"reconciliation-btn",attrs:{type:"primary",plain:"",icon:e.isReconciliationMode?"el-icon-document-checked":"el-icon-document",size:"small",loading:e.reconciliationLoading},on:{click:e.toggleReconciliationMode}},[e._v(" "+e._s(e.isReconciliationMode?"退出对账":"开始对账")+" ")])],1),n("div",{staticClass:"toolbar-right"},[n("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1)])]),e.isReconciliationMode?n("div",{ref:"reconciliationNotice",staticClass:"reconciliation-notice",class:{"reconciliation-fixed":e.isReconciliationFixed}},[n("div",{staticClass:"reconciliation-alert"},[e._m(0),n("div",{staticClass:"reconciliation-content"},[e._m(1),n("div",{staticClass:"reconciliation-stats"},[n("span",{staticClass:"stats-text"},[e._v(" 已对账流水号数量： "),n("span",{staticClass:"reconciled-count"},[e._v(e._s(e.reconciledSerialNumbers.length))]),e._v(" / "),n("span",{staticClass:"total-count"},[e._v(e._s(e.totalSerialGroupsCount))])]),n("span",{staticClass:"stats-text amount-stats"},[e._v(" 已对账流水号金额： "),n("span",{staticClass:"reconciled-amount"},[e._v("￥"+e._s(e.reconciledSerialAmount.toFixed(2)))]),e._v(" / "),n("span",{staticClass:"total-amount"},[e._v("￥"+e._s(e.totalSerialAmount.toFixed(2)))])]),e.isReconciliationFixed?n("el-button",{staticClass:"exit-reconciliation-btn",attrs:{type:"danger",size:"mini",icon:"el-icon-close"},on:{click:e.exitReconciliationMode}},[e._v(" 退出对账 ")]):e._e()],1)])])]):e._e(),n("div",{staticClass:"table-container"},[n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"recordTable",staticStyle:{width:"100%"},attrs:{data:e.recordList,size:"mini","cell-style":{padding:"4px 0"},stripe:"","row-style":{height:"40px"},"header-cell-style":{background:"#f5f7fa",color:"#031024FF",fontWeight:"bold"},"row-class-name":e.tableRowClassName,"row-key":"uniqueKey"},on:{"selection-change":e.handleSelectionChange,"expand-change":e.handleExpandChange,"row-click":e.handleRowClick}},[n("el-table-column",{attrs:{type:"expand",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isSerialGroup?n("div",{staticClass:"second-level-container"},[n("el-table",{ref:"innerTable_"+t.row.sysUserId+"_"+t.row.serialNumber,staticStyle:{width:"100%"},attrs:{data:e.getSafeTableData(t.row.betRecords),size:"small","cell-style":{padding:"4px 0"},stripe:"","row-style":{height:"40px"},"header-cell-style":{background:"#f5f7fa",color:"#031024FF",fontWeight:"bold"},"row-key":"safeId"},on:{"selection-change":e.handleInnerSelectionChange,"row-click":e.handleInnerRowClick}},[n("el-table-column",{attrs:{type:"expand"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-form",{staticClass:"table-expand",attrs:{"label-position":"left",inline:""}},[n("el-form-item",{attrs:{label:"识别框内容"}},[n("span",{staticStyle:{"font-size":"16px","font-weight":"bold"}},[e._v(e._s(t.row.shibie||"无识别内容"))])]),n("el-form-item",{attrs:{label:"下注号码详情"}},[t.row.methodId>=44&&t.row.methodId<=59&&t.row.betNumbers?n("div",[n("div",{staticStyle:{display:"flex","align-items":"center",gap:"12px"}},[n("el-tag",{staticStyle:{"font-size":"16px","font-weight":"bold"},attrs:{type:"danger",effect:"dark",size:"medium"}},[e._v(" 胆码: "+e._s(JSON.parse(t.row.betNumbers).numbers[0].danma)+" ")]),n("el-tag",{staticStyle:{"font-size":"16px","font-weight":"bold"},attrs:{effect:"dark",size:"medium"}},[e._v(" 拖码: "+e._s(JSON.parse(t.row.betNumbers).numbers[0].tuoma)+" ")]),n("span",{staticStyle:{color:"#909399","font-size":"14px"}},[e._v(" ("+e._s(e.getMethodName(t.row.methodId))+") ")])],1)]):t.row.methodId>=60&&t.row.methodId<=69&&t.row.betNumbers?n("div",[n("div",{staticStyle:{display:"flex","align-items":"center",gap:"12px"}},[n("el-tag",{staticStyle:{"font-size":"16px","font-weight":"bold"},attrs:{effect:"dark",size:"medium"}},[e._v(" "+e._s(e.getKuaduDisplay(t.row))+" ")]),n("span",{staticStyle:{color:"#909399","font-size":"14px"}},[e._v(" ("+e._s(e.getMethodName(t.row.methodId))+") ")])],1)]):t.row.betNumbers?n("div",{staticStyle:{display:"flex","flex-wrap":"wrap",gap:"1px",padding:"2px"}},e._l(JSON.parse(t.row.betNumbers).numbers,(function(r,a){return n("div",{key:"bet_"+t.row.betId+"_"+a},[n("el-tag",{staticStyle:{"font-size":"14px","font-weight":"bold",margin:"0",padding:"0 4px"},attrs:{type:"primary",effect:"dark",size:"mini"}},[1===Object.keys(r).length?[e._v(" "+e._s(r.a)+" ")]:2===Object.keys(r).length?[e._v(" "+e._s(r.a)+"."+e._s(r.b)+" ")]:[e._v(" "+e._s(Object.values(r).filter((function(e){return"string"===typeof e||"number"===typeof e})).join("."))+" ")]],2)],1)})),0):n("span",{staticStyle:{"font-size":"12px","font-weight":"bold"}},[e._v(e._s(t.row.betNumbers))])]),t.row.winningNumbers&&""!==t.row.winningNumbers?n("el-form-item",{attrs:{label:"中奖号码详情"}},[n("div",["string"===typeof t.row.winningNumbers?[n("div",{staticStyle:{display:"flex","flex-wrap":"wrap",gap:"1px",padding:"2px"}},e._l(JSON.parse(t.row.winningNumbers).winning,(function(r,a){return n("div",{key:"win_"+t.row.betId+"_"+a},[n("el-tag",{staticStyle:{"font-size":"14px","font-weight":"bold",margin:"0",padding:"0 4px"},attrs:{type:"primary",effect:"dark",size:"mini"}},[1===Object.keys(r).length?[e._v(" "+e._s(r.a)+" ")]:2===Object.keys(r).length?[e._v(" "+e._s(r.a)+"."+e._s(r.b)+" ")]:[e._v(" "+e._s(Object.values(r).join("."))+" ")]],2)],1)})),0)]:[n("span",{staticStyle:{"font-size":"12px","font-weight":"bold"}},[e._v(e._s(t.row.winningNumbers))])]],2)]):e._e()],1)]}}],null,!0)}),n("el-table-column",{attrs:{type:"selection",width:"42",align:"center"}}),n("el-table-column",{attrs:{label:"彩种",align:"center",prop:"lotteryId",width:"45"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.lotteryId?n("el-button",{attrs:{type:"danger",size:"mini",circle:""}},[e._v("福")]):2===t.row.lotteryId?n("el-button",{attrs:{type:"primary",size:"mini",circle:""}},[e._v("体")]):n("span",[e._v(e._s(t.row.lotteryId))])]}}],null,!0)}),n("el-table-column",{attrs:{label:"本期期号",align:"center",prop:"issueNumber",width:"75"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",{staticStyle:{"font-size":"12px","font-weight":"bold"}},[e._v(e._s(t.row.issueNumber))])]}}],null,!0)}),n("el-table-column",{attrs:{label:"玩法名称",align:"center",prop:"methodId",width:"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",{staticStyle:{"font-size":"14px","font-weight":"800",color:"#FF2F2FFF"}},[e._v(e._s(e.getMethodName(t.row.methodId)))])]}}],null,!0)}),n("el-table-column",{attrs:{label:"玩家名称",align:"center",prop:"userId",width:"75"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",{staticStyle:{"font-size":"12px","font-weight":"bold"}},[e._v(e._s(e.getUserName(t.row.userId)))])]}}],null,!0)}),n("el-table-column",{attrs:{label:"下注号码",align:"center",prop:"betNumbers","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.betNumbers?n("div",[t.row.methodId>=44&&t.row.methodId<=59?n("div",{staticStyle:{"white-space":"nowrap"}},[JSON.parse(t.row.betNumbers).numbers.length>0?[n("el-tag",{staticStyle:{margin:"1px","font-weight":"bold"},attrs:{type:"danger",effect:"dark",size:"mini"}},[e._v(" 胆"+e._s(JSON.parse(t.row.betNumbers).numbers[0].danma)+" ")]),n("el-tag",{staticStyle:{margin:"1px","font-weight":"bold"},attrs:{size:"mini",effect:"dark"}},[e._v(" 拖"+e._s(JSON.parse(t.row.betNumbers).numbers[0].tuoma)+" ")])]:e._e()],2):t.row.methodId>=60&&t.row.methodId<=69?n("div",{staticStyle:{"white-space":"nowrap"}},[t.row.betNumbers?[n("el-tag",{staticStyle:{margin:"1px","font-weight":"bold"},attrs:{effect:"dark",size:"mini"}},[e._v(" "+e._s(e.getKuaduDisplay(t.row))+" ")])]:e._e()],2):n("div",{staticStyle:{"white-space":"nowrap",overflow:"hidden","text-overflow":"ellipsis"}},[e._l(JSON.parse(t.row.betNumbers).numbers.slice(0,3),(function(r,a){return n("div",{key:"table_bet_"+t.row.betId+"_"+a,staticStyle:{display:"inline-block"}},[n("el-tag",{staticStyle:{"font-size":"14px",margin:"0 1px","font-weight":"bold",padding:"0 4px"},attrs:{type:"primary",effect:"dark",size:"mini"}},[1===Object.keys(r).length?[e._v(" "+e._s(r.a)+" ")]:2===Object.keys(r).length?[e._v(" "+e._s(r.a)+"."+e._s(r.b)+" ")]:[e._v(" "+e._s(Object.values(r).filter((function(e){return"string"===typeof e||"number"===typeof e})).join("."))+" ")]],2)],1)})),JSON.parse(t.row.betNumbers).numbers.length>3?n("span",{staticStyle:{"font-size":"12px",color:"#909399"}},[e._v("...")]):e._e()],2)]):n("span",{staticStyle:{"font-size":"12px","font-weight":"bold"}},[e._v(e._s(t.row.betNumbers))])]}}],null,!0)}),n("el-table-column",{attrs:{label:"中奖号码",align:"center",prop:"winningNumbers","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.winningNumbers&&"null"!==t.row.winningNumbers?n("div",["string"===typeof t.row.winningNumbers?[n("div",{staticStyle:{"white-space":"nowrap",overflow:"hidden","text-overflow":"ellipsis"}},[e._l(JSON.parse(t.row.winningNumbers).winning.slice(0,3),(function(r,a){return n("div",{key:"table_win_"+t.row.betId+"_"+a,staticStyle:{display:"inline-block"}},[n("el-tag",{staticStyle:{"font-size":"12px",margin:"0 1px",padding:"0 4px"},attrs:{type:"primary",effect:"dark",size:"mini"}},[1===Object.keys(r).length?[e._v(" "+e._s(r.a)+" ")]:2===Object.keys(r).length?[e._v(" "+e._s(r.a)+"."+e._s(r.b)+" ")]:[e._v(" "+e._s(Object.values(r).join("."))+" ")]],2)],1)})),JSON.parse(t.row.winningNumbers).winning.length>3?n("span",{staticStyle:{"font-size":"12px",color:"#909399"}},[e._v("...")]):e._e()],2)]:[n("span",{staticStyle:{"font-size":"12px","font-weight":"bold"}},[e._v(e._s(t.row.winningNumbers))])]],2):e._e()]}}],null,!0)}),n("el-table-column",{attrs:{label:"下注时间",align:"center",prop:"betTime",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",{staticStyle:{"font-size":"12px","font-weight":"bold"}},[e._v(e._s(e.parseTime(t.row.betTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}],null,!0)}),n("el-table-column",{attrs:{label:"定位",align:"center",prop:"dingwei",width:"45"},scopedSlots:e._u([{key:"default",fn:function(t){return[100===t.row.dingwei?n("el-tag",{staticStyle:{"font-size":"12px",padding:"0 4px"},attrs:{type:"danger",effect:"dark",size:"mini"}},[e._v("百")]):101===t.row.dingwei?n("el-tag",{staticStyle:{"font-size":"12px",padding:"0 4px"},attrs:{type:"warning",effect:"dark",size:"mini"}},[e._v("十")]):102===t.row.dingwei?n("el-tag",{staticStyle:{"font-size":"12px",padding:"0 4px"},attrs:{type:"success",effect:"dark",size:"mini"}},[e._v("个")]):n("span",{staticStyle:{"font-size":"12px","font-weight":"bold"}},[e._v(e._s(t.row.dingwei))])]}}],null,!0)}),n("el-table-column",{attrs:{label:"单双",align:"center",prop:"danshuang",width:"45"},scopedSlots:e._u([{key:"default",fn:function(t){return[200===t.row.danshuang?n("el-tag",{staticStyle:{"font-size":"12px",padding:"0 4px"},attrs:{type:"danger",effect:"dark",size:"mini"}},[e._v("单")]):201===t.row.danshuang?n("el-tag",{staticStyle:{"font-size":"12px",padding:"0 4px"},attrs:{type:"primary",effect:"dark",size:"mini"}},[e._v("双")]):n("span",{staticStyle:{"font-size":"12px","font-weight":"bold"}},[e._v(e._s(t.row.danshuang))])]}}],null,!0)}),n("el-table-column",{attrs:{label:"大小",align:"center",prop:"daxiao",width:"45"},scopedSlots:e._u([{key:"default",fn:function(t){return[300===t.row.daxiao?n("el-tag",{staticStyle:{"font-size":"12px",padding:"0 4px"},attrs:{type:"danger",effect:"dark",size:"mini"}},[e._v("大")]):301===t.row.daxiao?n("el-tag",{staticStyle:{"font-size":"12px",padding:"0 4px"},attrs:{type:"primary",effect:"dark",size:"mini"}},[e._v("小")]):n("span",{staticStyle:{"font-size":"12px","font-weight":"bold"}},[e._v(e._s(t.row.daxiao))])]}}],null,!0)}),n("el-table-column",{attrs:{label:"和值",align:"center",prop:"hezhi",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[null!==t.row.hezhi&&void 0!==t.row.hezhi?n("el-tag",{staticStyle:{"font-size":"12px",padding:"0 4px"},attrs:{type:"primary",effect:"dark",size:"mini"}},[e._v("和"+e._s(t.row.hezhi))]):e._e()]}}],null,!0)}),n("el-table-column",{attrs:{label:"中奖",align:"center",prop:"shifouzj",width:"45"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.shifouzj?n("el-tag",{staticStyle:{"font-size":"12px",padding:"0 4px"},attrs:{type:"success",effect:"dark",size:"mini"}},[e._v("是")]):n("el-tag",{staticStyle:{"font-size":"12px",padding:"0 4px"},attrs:{type:"info",effect:"dark",size:"mini"}},[e._v("否")])]}}],null,!0)}),n("el-table-column",{attrs:{label:"结算",align:"center",prop:"jiesuan",width:"45"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.jiesuan?n("el-tag",{staticStyle:{"font-size":"12px",padding:"0 4px"},attrs:{type:"success",effect:"dark",size:"mini"}},[e._v("是")]):n("el-tag",{staticStyle:{"font-size":"12px",padding:"0 4px"},attrs:{type:"info",effect:"dark",size:"mini"}},[e._v("否")])]}}],null,!0)}),n("el-table-column",{attrs:{label:"下注金额",align:"center",prop:"money",width:"85"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",{staticStyle:{"font-size":"14px","font-weight":"bold"}},[e._v(" ￥"+e._s(t.row.money?t.row.money.toFixed(2):"0.00")+" ")])]}}],null,!0)}),n("el-table-column",{attrs:{label:"下注总额",align:"center",prop:"totalAmount",width:"85"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",{staticStyle:{"font-size":"14px","font-weight":"bold"}},[e._v(" ￥"+e._s(t.row.totalAmount?t.row.totalAmount.toFixed(2):"0.00")+" ")])]}}],null,!0)}),n("el-table-column",{attrs:{label:"总注",align:"center",prop:"totalBets",width:"85"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",{staticStyle:{"font-size":"14px","font-weight":"bold"}},[e._v(" "+e._s(t.row.totalBets)+" 注 ")])]}}],null,!0)}),n("el-table-column",{attrs:{label:"盈利金额",align:"center",prop:"winAmount",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",{style:{fontSize:"14px",fontWeight:"bold",color:t.row.winAmount&&0!==t.row.winAmount?"#F56C6C":""}},[e._v(" ￥"+e._s(t.row.winAmount?t.row.winAmount.toFixed(2):"0.00")+" ")])]}}],null,!0)}),n("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"75"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["game:record:edit"],expression:"['game:record:edit']"}],staticStyle:{"font-size":"12px"},attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(n){return e.handleUpdate(t.row)}}},[e._v("修改")]),n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["game:record:remove"],expression:"['game:record:remove']"}],staticStyle:{"font-size":"12px"},attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(n){return e.handleDelete(t.row)}}},[e._v("删除")])]}}],null,!0)})],1)],1):n("el-form",{staticClass:"table-expand",attrs:{"label-position":"left",inline:""}},[n("el-form-item",{attrs:{label:"识别框内容"}},[n("span",{staticStyle:{"font-size":"16px","font-weight":"bold"}},[e._v(e._s(t.row.shibie||"无识别内容"))])]),n("el-form-item",{attrs:{label:"下注号码详情"}},[t.row.methodId>=44&&t.row.methodId<=59&&t.row.betNumbers?n("div",[n("div",{staticStyle:{display:"flex","align-items":"center",gap:"12px"}},[n("el-tag",{staticStyle:{"font-size":"16px","font-weight":"bold"},attrs:{type:"danger",effect:"dark",size:"medium"}},[e._v(" 胆码: "+e._s(JSON.parse(t.row.betNumbers).numbers[0].danma)+" ")]),n("el-tag",{staticStyle:{"font-size":"16px","font-weight":"bold"},attrs:{effect:"dark",size:"medium"}},[e._v(" 拖码: "+e._s(JSON.parse(t.row.betNumbers).numbers[0].tuoma)+" ")]),n("span",{staticStyle:{color:"#909399","font-size":"14px"}},[e._v(" ("+e._s(e.getMethodName(t.row.methodId))+") ")])],1)]):t.row.methodId>=60&&t.row.methodId<=69&&t.row.betNumbers?n("div",[n("div",{staticStyle:{display:"flex","align-items":"center",gap:"12px"}},[n("el-tag",{staticStyle:{"font-size":"16px","font-weight":"bold"},attrs:{type:"warning",effect:"dark",size:"medium"}},[e._v(" "+e._s(e.getKuaduDisplay(t.row))+" ")]),n("span",{staticStyle:{color:"#909399","font-size":"14px"}},[e._v(" ("+e._s(e.getMethodName(t.row.methodId))+") ")])],1)]):t.row.betNumbers?n("div",{staticStyle:{display:"flex","flex-wrap":"wrap",gap:"1px",padding:"2px"}},e._l(JSON.parse(t.row.betNumbers).numbers,(function(r,a){return n("div",{key:"expand_bet_"+t.row.betId+"_"+a},[n("el-tag",{staticStyle:{"font-size":"14px","font-weight":"bold",margin:"0",padding:"0 4px"},attrs:{type:"primary",effect:"dark",size:"mini"}},[1===Object.keys(r).length?[e._v(" "+e._s(r.a)+" ")]:2===Object.keys(r).length?[e._v(" "+e._s(r.a)+"."+e._s(r.b)+" ")]:[e._v(" "+e._s(Object.values(r).filter((function(e){return"string"===typeof e||"number"===typeof e})).join("."))+" ")]],2)],1)})),0):n("span",{staticStyle:{"font-size":"12px","font-weight":"bold"}},[e._v(e._s(t.row.betNumbers))])]),t.row.winningNumbers&&""!==t.row.winningNumbers?n("el-form-item",{attrs:{label:"中奖号码详情"}},[n("div",["string"===typeof t.row.winningNumbers?[n("div",{staticStyle:{display:"flex","flex-wrap":"wrap",gap:"1px",padding:"2px"}},e._l(JSON.parse(t.row.winningNumbers).winning,(function(r,a){return n("div",{key:"expand_win_"+t.row.betId+"_"+a},[n("el-tag",{staticStyle:{"font-size":"14px","font-weight":"bold",margin:"0",padding:"0 4px"},attrs:{type:"primary",effect:"dark",size:"mini"}},[1===Object.keys(r).length?[e._v(" "+e._s(r.a)+" ")]:2===Object.keys(r).length?[e._v(" "+e._s(r.a)+"."+e._s(r.b)+" ")]:[e._v(" "+e._s(Object.values(r).join("."))+" ")]],2)],1)})),0)]:[n("span",{staticStyle:{"font-size":"12px","font-weight":"bold"}},[e._v(e._s(t.row.winningNumbers))])]],2)]):e._e()],1)]}}])}),n("el-table-column",{attrs:{type:"selection",width:"55",selectable:e.checkSelectable}}),n("el-table-column",{attrs:{prop:"serialNumber"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isSerialGroup?n("div",{staticClass:"serial-group-info"},[n("div",{staticClass:"serial-header"},[n("i",{staticClass:"el-icon-document"}),n("span",{staticClass:"serial-number"},[e._v("流水号: "+e._s(t.row.serialNumber))])])]):e._e()]}}])}),n("el-table-column",{attrs:{prop:"lottery"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isSerialGroup?n("div",{staticClass:"lottery-info-display"},[n("span",{staticClass:"lottery-label"},[e._v("彩种:")]),n("div",{staticClass:"lottery-buttons"},[1===t.row.lottery?n("el-button",{staticClass:"lottery-btn",attrs:{type:"danger",size:"small",circle:""}},[e._v("福")]):2===t.row.lottery?n("el-button",{staticClass:"lottery-btn",attrs:{type:"primary",size:"small",circle:""}},[e._v("体")]):3===t.row.lottery?[n("el-button",{staticClass:"lottery-btn",staticStyle:{"margin-right":"4px"},attrs:{type:"danger",size:"small",circle:""}},[e._v("福")]),n("el-button",{staticClass:"lottery-btn",attrs:{type:"primary",size:"small",circle:""}},[e._v("体")])]:n("span",{staticClass:"lottery-unknown"},[e._v(e._s(t.row.lottery))])],2)]):e._e()]}}])}),n("el-table-column",{attrs:{prop:"userId"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isSerialGroup?n("div",{staticClass:"user-info-display"},[n("i",{staticClass:"el-icon-user"}),n("div",{staticClass:"user-details"},[e.isAdmin?n("div",{staticClass:"admin-user-info"},[n("div",{staticClass:"sys-user-info"},[n("span",{staticClass:"label"},[e._v("用户:")]),n("span",{staticClass:"sys-user-name"},[e._v(e._s(t.row.sysUserId))])]),n("div",{staticClass:"player-info"},[n("span",{staticClass:"label"},[e._v("玩家:")]),n("span",{staticClass:"user-name"},[e._v(e._s(e.getUserName(t.row.userId)))])])]):n("div",{staticClass:"normal-user-info"},[n("span",{staticClass:"user-name"},[e._v("玩家："+e._s(e.getUserName(t.row.userId)))])])])]):e._e()]}}])}),n("el-table-column",{attrs:{prop:"serialWinMoney","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isSerialGroup?n("div",{staticClass:"amount-display win-amount-display"},[n("i",{staticClass:"el-icon-trophy"}),n("span",{staticClass:"amount-label"},[e._v("中奖总额:")]),n("span",{staticClass:"amount-value win-amount"},[e._v("￥"+e._s(t.row.serialWinMoney?t.row.serialWinMoney.toFixed(2):"0.00"))])]):e._e()]}}])}),n("el-table-column",{attrs:{prop:"serialBetMoney","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isSerialGroup?n("div",{class:["amount-display","bet-amount-display",{"reconciliation-clickable":e.isReconciliationMode&&!e.isSerialReconciled(t.row.serialNumber),"reconciliation-completed":e.isReconciliationMode&&e.isSerialReconciled(t.row.serialNumber)}],attrs:{title:e.isReconciliationMode?e.isSerialReconciled(t.row.serialNumber)?"已对账 - 点击取消对账":"点击标记为已对账":""}},[n("i",{staticClass:"el-icon-coin"}),n("span",{staticClass:"amount-label"},[e._v("投注总额:")]),n("span",{staticClass:"amount-value bet-amount"},[e._v("￥"+e._s(t.row.serialBetMoney?t.row.serialBetMoney.toFixed(2):"0.00"))]),e.isReconciliationMode&&e.isSerialReconciled(t.row.serialNumber)?n("i",{staticClass:"el-icon-check reconciliation-check-icon"}):e._e(),e.isReconciliationMode?n("span",{staticClass:"reconciliation-status-badge",class:{reconciled:e.isSerialReconciled(t.row.serialNumber)}},[e._v(" "+e._s(e.isSerialReconciled(t.row.serialNumber)?"已对账":"待对账")+" ")]):e._e()]):e._e()]}}])}),n("el-table-column",{attrs:{prop:"totalBets"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isSerialGroup?n("div",{staticClass:"count-display"},[n("i",{staticClass:"el-icon-data-line"}),n("span",{staticClass:"count-label"},[e._v("总注数:")]),n("span",{staticClass:"count-value"},[e._v(e._s(t.row.totalBets||0)+" 注")])]):e._e()]}}])}),n("el-table-column",{attrs:{label:"操作",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isSerialGroup?n("div",[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["game:record:remove"],expression:"['game:record:remove']"}],staticStyle:{color:"#F56C6C","font-size":"12px"},attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(n){return e.handleDeleteSerial(t.row)}}},[e._v(" 删除流水 ")])],1):e._e()]}}])})],1)],1),n("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0&&!e.isReconciliationMode,expression:"total > 0 && !isReconciliationMode"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),n("bet-dialog",{attrs:{visible:e.open,title:e.title,row:e.selectedRow},on:{"update:visible":function(t){e.open=t},success:e.handleSuccess,cancel:e.handleCancel}}),n("div",{directives:[{name:"show",rawName:"v-show",value:e.showFloatingButtons,expression:"showFloatingButtons"}],staticClass:"floating-buttons"},[n("div",{staticClass:"floating-btn-wrapper"},[n("el-tooltip",{attrs:{content:"开奖结算",placement:"left"}},[n("el-button",{staticClass:"floating-btn settlement-btn",attrs:{type:"success",icon:"el-icon-check",circle:"",size:"small"},on:{click:e.handleDrawSettlement}})],1)],1),n("div",{staticClass:"floating-btn-wrapper"},[n("el-tooltip",{attrs:{content:"撤销开奖",placement:"left"}},[n("el-button",{staticClass:"floating-btn cancel-btn",attrs:{type:"warning",icon:"el-icon-refresh-left",circle:"",size:"small"},on:{click:e.handleCancelDrawSettlement}})],1)],1),e.isReconciliationMode?e._e():n("div",{staticClass:"floating-btn-wrapper"},[n("el-tooltip",{attrs:{content:e.isExpandAll?"收回二级":"展开二级",placement:"left"}},[n("el-button",{staticClass:"floating-btn expand-btn",attrs:{type:"primary",icon:e.isExpandAll?"el-icon-s-fold":"el-icon-s-unfold",circle:"",size:"small"},on:{click:e.toggleExpandSecondLevel}})],1)],1),n("div",{staticClass:"floating-btn-wrapper"},[n("el-tooltip",{attrs:{content:"回到顶部",placement:"left"}},[n("el-button",{staticClass:"floating-btn top-btn",attrs:{type:"info",icon:"el-icon-top",circle:"",size:"small"},on:{click:e.scrollToTop}})],1)],1)])],1)},a=[function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"reconciliation-header"},[n("i",{staticClass:"el-icon-info"}),n("span",{staticClass:"reconciliation-title"},[e._v("对账模式")])])},function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("p",{staticClass:"reconciliation-instruction"},[n("strong",{staticStyle:{color:"#ff4d4f"}},[e._v("操作提示：")]),e._v("点击表格中蓝色虚线框的 "),n("span",{staticClass:"highlight-amount"},[e._v('"投注总额: ￥xxx.xx"')]),e._v(" 区域可切换该流水号的对账状态！再次点击可取消对账标记。 ")])}],o=n("ade3"),i=n("2909"),s=n("c14f"),l=n("1da1"),u=n("3835"),c=n("5530"),d=(n("d9e2"),n("99af"),n("4de4"),n("7db0"),n("caad"),n("a15b"),n("d81d"),n("14d9"),n("13d5"),n("4e82"),n("a434"),n("b0c0"),n("e9c4"),n("a9e3"),n("b680"),n("b64b"),n("d3b7"),n("07ac"),n("25f0"),n("6062"),n("1e70"),n("79a4"),n("c1a1"),n("8b00"),n("a4e7"),n("1e5a"),n("72c3"),n("2532"),n("3ca3"),n("4d90"),n("498a"),n("0643"),n("2382"),n("fffc"),n("4e3e"),n("a573"),n("9d4a"),n("9a9a"),n("159b"),n("ddb0"),n("35d4")),m=n("0901"),h=n("08c8"),f=n("f97d"),I=n("0fad"),b=(n("c38a"),n("832b")),p=(n("6455"),n("2f62")),_=n("b775"),g={name:"Record",components:{BetDialog:b["default"]},data:function(){return{loading:!1,exportLoading:!1,ids:[],single:!0,multiple:!0,selectedSerialNumbers:[],multipleSerial:!0,showSearch:!0,total:0,recordList:[],isAdmin:!1,title:"",open:!1,userList:[],allUserList:[],gameMethodsData:[],serialNumberList:[],queryParams:{pageNum:1,pageSize:50,userId:null,sysUserId:null,serialNumber:null,methodId:null,issueNumber:null,betNumbers:null,shibie:null,jiesuan:null},form:{lotteryId:1,issueNumber:"",methodId:null,userId:null,betNumbers:null,isDrawn:0,winningNumbers:null,betTime:null,drawTime:null,totalAmount:0,money:0,dingwei:null,danshuang:null,daxiao:null,hezhi:null,shifouzj:0,jiesuan:0,winAmount:0,shibie:null,totalBets:null,betGroups:[]},rules:{lotteryId:[{required:!0,message:"彩种名称不能为空",trigger:"change"}],issueNumber:[{required:!0,message:"本期期号不能为空",trigger:"blur"}],methodId:[{required:!0,message:"玩法名称不能为空",trigger:"blur"}],userId:[{required:!0,message:"玩家名称不能为空",trigger:"blur"}],money:[{required:!0,message:"下注金额不能为空",trigger:"blur"}]},formatterOpen:!1,formatterForm:{type:1,lotteryId:1,input:"",output:""},pendingRequests:null,isExpandAll:!1,isExpandThirdLevel:!1,isGlobalExpand:!1,isDescOrder:null===localStorage.getItem("gameRecordSortOrder")||"true"===localStorage.getItem("gameRecordSortOrder"),isButtonOperation:!1,selectedRow:null,isReconciliationMode:!1,reconciliationLoading:!1,reconciledSerialNumbers:[],originalQueryParams:null,isReconciliationFixed:!1,reconciliationOriginalTop:0,scrollDebounceTimer:null,reconciliationStorageKey:"gameRecord_reconciliation_",reconciliationDateKey:"",showFloatingButtons:!1,toolbarElement:null}},computed:Object(c["a"])(Object(c["a"])({},Object(p["b"])(["roles"])),{},{showStatisticsButton:function(){var e=this.$store.getters.userInfo;return e&&"1"===e.showStatistics},totalSerialGroupsCount:function(){return this.recordList&&0!==this.recordList.length?this.recordList.filter((function(e){return e.isSerialGroup})).length:0},totalSerialAmount:function(){return this.recordList&&0!==this.recordList.length?this.recordList.filter((function(e){return e.isSerialGroup})).reduce((function(e,t){var n=parseFloat(t.serialBetMoney)||0;return e+n}),0):0},reconciledSerialAmount:function(){var e=this;return this.recordList&&0!==this.recordList.length&&this.reconciledSerialNumbers.length?this.recordList.filter((function(t){return t.isSerialGroup&&e.reconciledSerialNumbers.includes(t.serialNumber)})).reduce((function(e,t){var n=parseFloat(t.serialBetMoney)||0;return e+n}),0):0}}),created:function(){var e=this;this.initReconciliationStorageKey(),this.initSortOrder(),this.restoreReconciliationState(),this.getMethodList().then((function(){e.getList(),e.getCustomerList(),e.getSerialNumberList()})),this.$nextTick((function(){e.checkAdminRole()}))},mounted:function(){window.addEventListener("scroll",this.handleScroll),window.addEventListener("clearAllData",this.handleClearAllData),this.fetchStatisticsData()},beforeDestroy:function(){window.removeEventListener("scroll",this.handleScroll),window.removeEventListener("clearAllData",this.handleClearAllData),this.scrollDebounceTimer&&(clearTimeout(this.scrollDebounceTimer),this.scrollDebounceTimer=null),this.isReconciliationMode&&this.saveReconciliationState()},watch:{},methods:Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])({getList:function(){var e,t=this;this.loading=!0,e=this.isReconciliationMode?Object(c["a"])(Object(c["a"])({},this.queryParams),{},{pageNum:1,pageSize:1e4}):Object(c["a"])({},this.queryParams),this.fetchStatisticsData();var n=e.shibie&&""!==e.shibie.trim();n&&(e.shibie=e.shibie.trim()),Promise.all([this.getSerialData(),Object(d["d"])(e)]).then((function(r){var a=Object(u["a"])(r,2),o=a[0],i=a[1],s=i.rows;if(n&&s.length>0){var l=e.shibie,c=s.filter((function(e){return e.shibie&&e.shibie.trim()===l}));s=0===c.length?s.filter((function(e){return e.shibie&&e.shibie.includes(l)})):c,i.total=s.length}t.buildThreeLevelData(o,s),t.total=i.total,t.isReconciliationMode?t.sortRecordListForReconciliation():t.sortRecordList(),n?t.$nextTick((function(){t.isExpandAll=!0,t.expandAllLevels()})):(t.isReconciliationMode,t.setDefaultExpandState()),t.loading=!1})).catch((function(e){console.error("获取数据失败:",e),t.loading=!1}))},fetchStatisticsData:function(){return Object(l["a"])(Object(s["a"])().m((function e(){var t,n,r,a;return Object(s["a"])().w((function(e){while(1)switch(e.n){case 0:return e.p=0,t=sessionStorage.getItem("sysUserId"),n={pageNum:1,pageSize:1e5},t&&(n.sysUserId=t),e.n=1,Object(d["d"])(n);case 1:r=e.v,r&&r.rows?(sessionStorage.setItem("recordStatisticsData",JSON.stringify(r.rows)),r.total>r.rows.length&&console.warn("数据可能不完整！API返回total: ".concat(r.total,", 实际获取: ").concat(r.rows.length))):console.warn("API响应中没有rows数据"),e.n=3;break;case 2:e.p=2,a=e.v,console.error("获取统计数据失败:",a);case 3:return e.a(2)}}),e,null,[[0,2]])})))()},getCustomerList:function(){var e=this,t=this.isAdmin?"adminUserList":"userList";try{var n=sessionStorage.getItem(t);if(n){var r=JSON.parse(n);if(Array.isArray(r)&&r.length>0)return void(this.userList=r)}}catch(o){console.warn("从session获取玩家列表失败:",o)}var a=this.isAdmin?{pageNum:1,pageSize:1e4}:{};Object(m["e"])(a).then((function(n){e.userList=n.rows||[];try{sessionStorage.setItem(t,JSON.stringify(e.userList))}catch(o){console.warn("保存玩家列表到session失败:",o)}})).catch((function(t){console.error("获取玩家列表失败:",t),e.userList=[]}))},updatePlayerListForUser:function(e){var t=this;this.isAdmin&&Object(m["e"])({sysUserId:e}).then((function(n){t.userList=n.rows||[],console.log("更新用户".concat(e,"的玩家列表，共").concat(t.userList.length,"个玩家"))})).catch((function(e){console.error("获取指定用户玩家列表失败:",e),t.userList=[]}))},getAllUserList:function(){var e=this;try{var t=sessionStorage.getItem("allUserList");if(t){var r=JSON.parse(t);if(Array.isArray(r)&&r.length>0)return void(this.allUserList=r)}}catch(a){console.warn("从session获取所有用户列表失败:",a)}Promise.resolve().then(n.bind(null,"c0c7")).then((function(t){t.listUser({pageNum:1,pageSize:1e3}).then((function(t){e.allUserList=t.rows||[];try{sessionStorage.setItem("allUserList",JSON.stringify(e.allUserList))}catch(a){console.warn("保存所有用户列表到session失败:",a)}})).catch((function(e){console.error("获取所有用户列表失败:",e)}))}))},getMethodList:function(){var e=this;try{var t=sessionStorage.getItem("gameMethodsData");if(t){var n=JSON.parse(t);if(Array.isArray(n)&&n.length>0)return this.gameMethodsData=n,this.$forceUpdate(),Promise.resolve(this.gameMethodsData)}}catch(a){console.warn("从session获取玩法数据失败:",a)}console.log("Session中无数据，请求接口获取玩法数据");var r={pageNum:1,pageSize:1e3};return Object(h["d"])(r).then((function(t){e.gameMethodsData=t.rows||[],console.log("从接口获取玩法数据，总数:",e.gameMethodsData.length);try{sessionStorage.setItem("gameMethodsData",JSON.stringify(e.gameMethodsData))}catch(a){console.warn("保存玩法数据到session失败:",a)}return e.$forceUpdate(),e.gameMethodsData})).catch((function(t){return console.error("获取玩法列表失败:",t),e.gameMethodsData=[],[]}))},getSerialNumberList:function(){var e=this;Object(f["b"])({pageNum:1,pageSize:1e3}).then((function(t){t&&t.rows&&t.rows.length>0?e.serialNumberList=Object(i["a"])(new Set(t.rows.map((function(e){return e.serialNumbers||e.serial_numbers})))).filter((function(e){return null!=e})).sort((function(e,t){return t-e})):e.getSerialNumberFromRecord()})).catch((function(t){console.error("获取流水号列表失败，尝试从下注记录获取:",t),e.getSerialNumberFromRecord()}))},getSerialNumberFromRecord:function(){var e=this;Object(d["d"])({pageNum:1,pageSize:1e3}).then((function(t){t&&t.rows&&t.rows.length>0&&(e.serialNumberList=Object(i["a"])(new Set(t.rows.map((function(e){return e.serialNumber})))).filter((function(e){return null!=e})).sort((function(e,t){return t-e})))})).catch((function(e){console.error("从下注记录获取流水号失败:",e)}))},handleUserIdChange:function(e){e?(this.queryParams.userId=null,this.updatePlayerListForUser(e),console.log("管理员选择用户ID: ".concat(e))):(this.queryParams.userId=null,this.getCustomerList()),this.handleQuery()},handlePlayerChange:function(e){var t=this;if(console.log("=== 玩家选择变化调试 ==="),console.log("选中的playerId:",e),console.log("当前userList:",this.userList),console.log("是否管理员:",this.isAdmin),e){var n=this.userList.find((function(t){return t.userId===e}));console.log("找到的玩家:",n),n?this.isAdmin&&(this.queryParams.sysUserId=n.sysUserId,console.log("管理员选择玩家: ".concat(n.name," (playerId: ").concat(e,", sysUserId: ").concat(n.sysUserId,")")),console.log("设置后的查询参数:",this.queryParams)):console.error("未找到对应的玩家信息！")}else{if(this.isAdmin){var r=this.queryParams.sysUserId&&this.allUserList.some((function(e){return e.userId===t.queryParams.sysUserId}));r||(this.queryParams.sysUserId=null)}console.log("清空玩家选择后的查询参数:",this.queryParams)}console.log("=== 玩家选择变化调试结束 ==="),this.handleQuery()},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.queryParams.sysUserId=null,this.queryParams.userId=null,this.isAdmin&&this.getCustomerList(),this.resetExpandState(),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.betId})).filter((function(e){return e})),this.single=1!==e.length,this.multiple=!e.length,this.selectedSerialNumbers=e.filter((function(e){return e.isSerialGroup&&e.serialNumber})).map((function(e){return e.serialNumber})),this.multipleSerial=0===this.selectedSerialNumbers.length},handleAdd:function(){this.title="新增下注管理",this.selectedRow=null,this.open=!0},handleCancel:function(){this.open=!1},handleSuccess:function(){var e=this;this.getList(),this.$nextTick((function(){e.setDefaultExpandState()}))},handleUpdate:function(e){this.title="修改下注管理",this.selectedRow={betId:e.betId,lotteryId:e.lotteryId,issueNumber:e.issueNumber,methodId:e.methodId,userId:e.userId,betNumbers:e.betNumbers,money:e.money,danshuang:e.danshuang,daxiao:e.daxiao,dingwei:e.dingwei,hezhi:e.hezhi,totalAmount:e.totalAmount,totalBets:e.totalBets,shibie:e.shibie,jiesuan:e.jiesuan,winAmount:e.winAmount,shifouzj:e.shifouzj,winningNumbers:e.winningNumbers,betTime:e.betTime,serialNumber:e.serialNumber},this.open=!0},handleDelete:function(e){var t=this,n=e.betId?[e.betId]:this.ids;this.$modal.confirm('是否确认删除下注管理编号为"'+n+'"的数据项？').then((function(){return Object(d["a"])(n)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleDeleteSerial:function(e){var t=this,n=e.serialNumber,r=e.totalRecords||0;this.$modal.confirm('是否确认删除流水号"'.concat(n,'"的所有投注记录？\n共').concat(r,"条记录将被删除，此操作不可恢复！"),"警告",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"}).then((function(){return t.deleteSerialRecords(n)})).then((function(){t.getList(),t.$modal.msgSuccess('流水号"'.concat(n,'"的所有投注记录删除成功'))})).catch((function(){}))},handleBatchDeleteSerial:function(){var e=this;if(0!==this.selectedSerialNumbers.length){var t=this.selectedSerialNumbers,n=t.length;this.$modal.confirm("是否确认批量删除选中的".concat(n,"个流水号的所有投注记录？\n流水号：").concat(t.join(", "),"\n此操作不可恢复！"),"批量删除警告",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"}).then((function(){return e.batchDeleteSerialRecords(t)})).then((function(){e.getList(),e.$modal.msgSuccess("成功删除".concat(n,"个流水号的所有投注记录"))})).catch((function(){}))}else this.$modal.msgWarning("请选择要删除的流水号")},checkSelectable:function(e){return!0===e.isSerialGroup},deleteSerialRecords:function(e){return Object(l["a"])(Object(s["a"])().m((function t(){var n,r;return Object(s["a"])().w((function(t){while(1)switch(t.n){case 0:return t.p=0,t.n=1,Object(d["b"])(e);case 1:return n=t.v,t.a(2,n);case 2:throw t.p=2,r=t.v,console.error("删除流水号记录失败:",r),r;case 3:return t.a(2)}}),t,null,[[0,2]])})))()},batchDeleteSerialRecords:function(e){return Object(l["a"])(Object(s["a"])().m((function t(){var n,r,a;return Object(s["a"])().w((function(t){while(1)switch(t.n){case 0:return t.p=0,n=e.map((function(e){return Object(d["b"])(e)})),t.n=1,Promise.all(n);case 1:return r=t.v,t.a(2,r);case 2:throw t.p=2,a=t.v,console.error("批量删除流水号记录失败:",a),a;case 3:return t.a(2)}}),t,null,[[0,2]])})))()},handleExport:function(){var e=this;this.$modal.confirm("是否确认导出下注管理数据？").then((function(){e.exportLoading=!0;try{var t=e.formatCurrentDataForExport();e.exportToExcel(t)}catch(n){console.error("导出失败:",n),e.$modal.msgError("导出失败: "+n.message)}finally{e.exportLoading=!1}})).catch((function(){}))},handleExportFormatted:function(){var e=this;this.$modal.confirm("是否确认导出所有下注管理数据项？").then((function(){e.exportLoading=!0;var t=Object(c["a"])(Object(c["a"])({},e.queryParams),{},{pageNum:1,pageSize:1e4});Object(d["d"])(t).then((function(t){var n=t.rows;if(n&&0!==n.length)try{var r=e.formatDataForExport(n);e.exportToExcel(r)}catch(a){console.error("格式化数据失败:",a),e.$modal.msgError("数据格式化失败: "+a.message)}else e.$modal.msgWarning("没有数据可以导出")})).catch((function(t){console.error("获取数据失败:",t),e.$modal.msgError("获取数据失败: "+(t.message||"未知错误"))})).finally((function(){e.exportLoading=!1}))})).catch((function(){}))},tableRowClassName:function(e){var t=e.row,n=e.rowIndex,r="";return r=n%2===0?"even-row":"odd-row",this.isReconciliationMode&&t.isSerialGroup&&this.isSerialReconciled(t.serialNumber)&&(r+=" reconciled-row"),r},toggleExpandSecondLevel:function(){var e=this;this.isButtonOperation=!0,this.isExpandAll=!this.isExpandAll,this.$nextTick((function(){e.expandSecondLevel()}))},toggleExpandThirdLevel:function(){var e=this;this.isButtonOperation=!0,this.isExpandThirdLevel=!this.isExpandThirdLevel,this.isExpandThirdLevel?(this.isExpandAll=!0,this.$nextTick((function(){e.expandAllLevels()}))):(this.isExpandAll=!1,this.$nextTick((function(){e.collapseAllLevels()})))},toggleSortOrder:function(){this.isDescOrder=!this.isDescOrder,localStorage.setItem("gameRecordSortOrder",this.isDescOrder.toString()),this.sortRecordList()},initSortOrder:function(){var e=localStorage.getItem("gameRecordSortOrder");null===e&&(localStorage.setItem("gameRecordSortOrder","true"),this.isDescOrder=!0)},initReconciliationStorageKey:function(){var e=new Date,t=e.getFullYear()+"-"+String(e.getMonth()+1).padStart(2,"0")+"-"+String(e.getDate()).padStart(2,"0");this.reconciliationDateKey=this.reconciliationStorageKey+t},saveReconciliationState:function(){var e={isReconciliationMode:this.isReconciliationMode,reconciledSerialNumbers:this.reconciledSerialNumbers,originalQueryParams:this.originalQueryParams,timestamp:Date.now()};localStorage.setItem(this.reconciliationDateKey,JSON.stringify(e))},restoreReconciliationState:function(){try{var e=localStorage.getItem(this.reconciliationDateKey);if(e){var t=JSON.parse(e);this.isReconciliationMode=t.isReconciliationMode||!1,this.reconciledSerialNumbers=t.reconciledSerialNumbers||[],this.originalQueryParams=t.originalQueryParams||null,this.isReconciliationMode&&(this.isDescOrder=!1)}}catch(n){console.error("恢复对账状态失败:",n),this.clearReconciliationStorage()}},clearReconciliationStorage:function(){localStorage.removeItem(this.reconciliationDateKey)},toggleReconciliationMode:function(){this.reconciliationLoading=!0,this.isReconciliationMode?this.exitReconciliationMode():this.enterReconciliationMode()},enterReconciliationMode:function(){var e=this;this.originalQueryParams=Object(c["a"])({},this.queryParams),this.isReconciliationMode||(this.reconciledSerialNumbers=[]),this.isReconciliationFixed=!1,this.reconciliationOriginalTop=0,this.scrollDebounceTimer&&(clearTimeout(this.scrollDebounceTimer),this.scrollDebounceTimer=null),this.isDescOrder=!1;var t=Object(c["a"])(Object(c["a"])({},this.queryParams),{},{pageNum:1,pageSize:1e4});this.loading=!0,Promise.all([this.getSerialData(),Object(d["d"])(t)]).then((function(t){var n=Object(u["a"])(t,2),r=n[0],a=n[1];e.buildThreeLevelData(r,a.rows),e.sortRecordListForReconciliation(),e.isReconciliationMode=!0,e.saveReconciliationState(),e.setDefaultExpandState(),e.loading=!1,e.reconciliationLoading=!1,e.$modal.msgSuccess("已进入对账模式，显示所有数据并按正序排列")})).catch((function(t){console.error("进入对账模式失败:",t),e.loading=!1,e.reconciliationLoading=!1,e.$modal.msgError("进入对账模式失败")}))},exitReconciliationMode:function(){this.originalQueryParams&&(this.queryParams=Object(c["a"])({},this.originalQueryParams)),this.reconciledSerialNumbers=[],this.isReconciliationFixed=!1,this.reconciliationOriginalTop=0,this.scrollDebounceTimer&&(clearTimeout(this.scrollDebounceTimer),this.scrollDebounceTimer=null),this.isDescOrder=null===localStorage.getItem("gameRecordSortOrder")||"true"===localStorage.getItem("gameRecordSortOrder"),this.isReconciliationMode=!1,this.clearReconciliationStorage(),this.getList(),this.reconciliationLoading=!1,this.$modal.msgSuccess("已退出对账模式")},sortRecordListForReconciliation:function(){this.recordList&&0!==this.recordList.length&&(this.recordList.sort((function(e,t){if(e.isSerialGroup&&t.isSerialGroup){var n=e.serialNumber||0,r=t.serialNumber||0;return n-r}return 0})),this.recordList.forEach((function(e){e.isSerialGroup&&e.records&&e.records.length>0&&e.records.sort((function(e,t){var n=e.betId||0,r=t.betId||0;return n-r}))})))},toggleSerialReconciliation:function(e){var t=this.reconciledSerialNumbers.indexOf(e);t>-1?(this.reconciledSerialNumbers.splice(t,1),this.$message.success("流水号 ".concat(e," 已取消对账标记"))):(this.reconciledSerialNumbers.push(e),this.$message.success("流水号 ".concat(e," 已标记为对账完成"))),this.saveReconciliationState(),this.$forceUpdate()},markSerialAsReconciled:function(e){this.reconciledSerialNumbers.includes(e)||(this.reconciledSerialNumbers.push(e),this.saveReconciliationState())},isSerialReconciled:function(e){return this.reconciledSerialNumbers.includes(e)},handleScroll:function(){var e=this;if(this.isReconciliationMode&&this.$refs.reconciliationNotice){var t=this.$refs.reconciliationNotice,n=t.getBoundingClientRect(),r=window.pageYOffset||document.documentElement.scrollTop;0!==this.reconciliationOriginalTop||this.isReconciliationFixed||(this.reconciliationOriginalTop=r+n.top),this.scrollDebounceTimer||(this.scrollDebounceTimer=setTimeout((function(){n.top<=0&&!e.isReconciliationFixed?e.isReconciliationFixed=!0:r<=e.reconciliationOriginalTop-20&&e.isReconciliationFixed&&(e.isReconciliationFixed=!1),e.scrollDebounceTimer=null}),16))}this.handleFloatingButtonsVisibility()},handleFloatingButtonsVisibility:function(){if(this.toolbarElement||(this.toolbarElement=document.querySelector(".toolbar-container")),this.toolbarElement){var e=this.toolbarElement.getBoundingClientRect();this.showFloatingButtons=e.bottom<0}},handleClearAllData:function(){this.isReconciliationMode&&(this.exitReconciliationMode(),this.$message.info("检测到一键清空操作，已自动退出对账模式"))},sortRecordList:function(){var e=this;this.recordList&&0!==this.recordList.length&&(this.recordList.sort((function(t,n){if(t.isSerialGroup&&n.isSerialGroup){var r=t.serialNumber||0,a=n.serialNumber||0;return e.isDescOrder?a-r:r-a}return 0})),this.recordList.forEach((function(t){t.isSerialGroup&&t.records&&t.records.length>0&&t.records.sort((function(t,n){var r=t.betId||0,a=n.betId||0;return e.isDescOrder?a-r:r-a}))})))},handleExpandChange:function(e,t){},handleRowClick:function(e,t,n){t&&"selection"===t.type||(this.isReconciliationMode&&e.isSerialGroup&&e.serialNumber&&t&&"serialBetMoney"===t.property?this.toggleSerialReconciliation(e.serialNumber):this.$refs.recordTable&&this.$refs.recordTable.toggleRowExpansion(e))},handleInnerRowClick:function(e,t,n){if(!t||"selection"!==t.type){var r=n.target;while(r&&!r.classList.contains("el-table"))r=r.parentElement;if(r){var a=r.__vue__;a&&a.toggleRowExpansion&&a.toggleRowExpansion(e)}}},resetExpandState:function(){this.isExpandAll=!1,this.isExpandThirdLevel=!1,this.isGlobalExpand=!1,this.isButtonOperation=!1},getMethodName:function(e){if(!this.gameMethodsData||!this.gameMethodsData.length)return e;var t=this.gameMethodsData.find((function(t){return Number(t.methodId)===Number(e)}));return t?t.methodName:e},getUserName:function(e){var t=this.userList.find((function(t){return t.userId===e}));return t?t.name:e},getSysUserName:function(e){if(!this.allUserList||!Array.isArray(this.allUserList))return"用户".concat(e);var t=this.allUserList.find((function(t){return t.userId===e}));return t&&(t.realName||t.userName)||"用户".concat(e)},checkAdminRole:function(){this.isAdmin=this.roles&&this.roles.includes("admin"),this.isAdmin&&this.getAllUserList()},handleStatusChange:function(e){var t=this,n=1===e.jiesuan?"结算":"取消结算";this.$modal.confirm('确认要"'+n+'""'+e.betId+'"的下注记录吗？').then((function(){return Object(d["e"])(e)})).then((function(){t.getList(),t.$modal.msgSuccess(n+"成功")})).catch((function(){e.jiesuan=1===e.jiesuan?0:1}))},getSerialData:function(){return Object(l["a"])(Object(s["a"])().m((function e(){var t,n;return Object(s["a"])().w((function(e){while(1)switch(e.n){case 0:return e.p=0,e.n=1,Object(f["b"])({pageNum:1,pageSize:1e3});case 1:return t=e.v,e.a(2,t.rows||[]);case 2:return e.p=2,n=e.v,console.error("获取流水号数据失败:",n),e.a(2,[])}}),e,null,[[0,2]])})))()},buildThreeLevelData:function(e,t){var n={};t.forEach((function(e){var t=e.serialNumber,r=e.sysUserId;if(t&&r){var a="".concat(t,"_").concat(r);n[a]||(n[a]=[]),n[a].push(e)}})),this.recordList=e.filter((function(e){var t=e.serialNumbers||e.serial_numbers,r=e.sysUserId,a="".concat(t,"_").concat(r);return n[a]})).map((function(e){var t=e.serialNumbers||e.serial_numbers,r=e.sysUserId,a="".concat(t,"_").concat(r),o=n[a]||[],i=o.reduce((function(e,t){return e+(t.totalAmount||0)}),0),s=o.reduce((function(e,t){return e+(t.winAmount||0)}),0),l=o.length,u=o.reduce((function(e,t){return e+(t.totalBets||0)}),0);return{isSerialGroup:!0,serialNumber:t,userId:e.userId,sysUserId:e.sysUserId,serialBetMoney:e.serialBetMoney,serialWinMoney:e.serialWinMoney,lottery:e.lottery,totalBets:e.totalBets,uniqueKey:"serial_".concat(e.sysUserId,"_").concat(t),totalBetAmount:i,totalWinAmount:s,totalRecords:l,calculatedTotalBets:u,betRecords:o}})).sort((function(e,t){return t.serialNumber-e.serialNumber}))},expandSecondLevel:function(){var e=this;this.$nextTick((function(){e.$refs.recordTable&&e.recordList.forEach((function(t){e.$refs.recordTable.toggleRowExpansion(t,e.isExpandAll)}))}))},expandThirdLevel:function(){var e=this;this.isExpandThirdLevel?(this.isExpandAll=!0,this.$nextTick((function(){e.$refs.recordTable&&e.recordList.forEach((function(t){e.$refs.recordTable.toggleRowExpansion(t,!0)})),setTimeout((function(){e.recordList.forEach((function(t){var n=e.$refs["innerTable_".concat(t.sysUserId,"_").concat(t.serialNumber)];n&&n[0]&&t.betRecords&&t.betRecords.forEach((function(e){n[0].toggleRowExpansion(e,!0)}))}))}),200)}))):this.$nextTick((function(){setTimeout((function(){e.recordList.forEach((function(t){var n=e.$refs["innerTable_".concat(t.sysUserId,"_").concat(t.serialNumber)];n&&n[0]&&t.betRecords&&t.betRecords.forEach((function(e){n[0].toggleRowExpansion(e,!1)}))}))}),100)}))},setDefaultExpandState:function(){var e=this;this.resetExpandState(),this.$nextTick((function(){e.recordList&&e.recordList.length>0&&e.$refs.recordTable&&(e.recordList.forEach((function(t){e.$refs.recordTable.toggleRowExpansion(t,!1)})),setTimeout((function(){e.$refs.recordTable.toggleRowExpansion(e.recordList[0],!0)}),100))}))},getRowKey:function(e){if(e.isSerialGroup){var t=e.sysUserId||"unknown_user",n=e.serialNumber||"unknown_".concat(Math.random().toString(36).substr(2,9)),r="serial_".concat(t,"_").concat(n);return r}var a=e.sysUserId||"unknown_user",o=e.betId||e.id||"unknown_".concat(Math.random().toString(36).substr(2,9)),i="bet_".concat(a,"_").concat(o);return i},handleInnerSelectionChange:function(e){},getThirdLevelRowKey:function(e){var t=e.betId||e.id||Math.random().toString(36).substr(2,9);return"bet_".concat(t)},handleThirdLevelExpandChange:function(e,t){},expandAllLevels:function(){var e=this;this.$refs.recordTable&&this.recordList.forEach((function(t){e.$refs.recordTable.toggleRowExpansion(t,!0)})),setTimeout((function(){e.recordList.forEach((function(t){var n=e.$refs["innerTable_".concat(t.sysUserId,"_").concat(t.serialNumber)],r=Array.isArray(n)?n[0]:n;if(r&&t.betRecords){var a=e.getSafeTableData(t.betRecords);a.forEach((function(e,t){r.toggleRowExpansion(e,!0)}))}}))}),300)},collapseAllLevels:function(){var e=this;this.recordList.forEach((function(t){var n=e.$refs["innerTable_".concat(t.sysUserId,"_").concat(t.serialNumber)],r=Array.isArray(n)?n[0]:n;if(r&&t.betRecords){var a=e.getSafeTableData(t.betRecords);a.forEach((function(e){r.toggleRowExpansion(e,!1)}))}})),setTimeout((function(){e.$refs.recordTable&&e.recordList.forEach((function(t){e.$refs.recordTable.toggleRowExpansion(t,!1)}))}),200)},getSafeTableData:function(e){return e&&Array.isArray(e)?e.map((function(e,t){var n=e.sysUserId||"unknown_user",r=e.betId||e.id||"record_".concat(t,"_").concat(Math.random().toString(36).substr(2,9)),a="".concat(n,"_").concat(r);return Object(c["a"])(Object(c["a"])({},e),{},{safeId:a})})):[]},formatCurrentDataForExport:function(){var e=this,t=[];return this.recordList.forEach((function(n){if(n.isSerialGroup){var r=function(e){if(!e&&0!==e)return"0.00";var t=parseFloat(e);return t.toFixed(2)};t.push({"数据类型":"流水号汇总","流水号":n.serialNumber,"下注用户":e.getUserName(n.userId),"彩种":e.getLotteryName(n.lottery),"投注总额":r(n.serialBetMoney),"中奖总额":r(n.serialWinMoney),"总注数":n.totalBets||0,"记录数":n.betRecords?n.betRecords.length:0,"玩法名称":"","期号":"","下注号码":"","中奖号码":"","下注金额":"","中奖金额":"","结算状态":"","中奖状态":"","号码识别":"","下注时间":""}),n.betRecords&&n.betRecords.length>0&&n.betRecords.forEach((function(n){var a="";if(n.betNumbers)try{var o=JSON.parse(n.betNumbers);a=o.numbers&&Array.isArray(o.numbers)?o.numbers.map((function(e){return Object.values(e).join(".")})).join(", "):n.betNumbers}catch(c){a=n.betNumbers||""}var i="";if(n.winningNumbers)try{if("string"===typeof n.winningNumbers){var s=JSON.parse(n.winningNumbers);s.winning&&Array.isArray(s.winning)&&(i=s.winning.map((function(e){return void 0===e.b&&void 0===e.c?e.a:void 0===e.c?"".concat(e.a,".").concat(e.b):"".concat(e.a,".").concat(e.b,".").concat(e.c)})).join(", "))}else i=n.winningNumbers}catch(c){i=n.winningNumbers||""}var l=function(e){return 1===e?"已结算":"未结算"},u=function(e){return 1===e?"已中奖":0===e?"未中奖":"待开奖"};t.push({"数据类型":"下注记录","流水号":n.serialNumber,"下注用户":e.getUserName(n.userId),"彩种":e.getLotteryName(n.lotteryId),"玩法名称":e.getMethodName(n.methodId),"期号":n.issueNumber,"下注号码":a,"中奖号码":i,"下注金额":r(n.money),"中奖金额":r(n.winAmount),"结算状态":l(n.jiesuan),"中奖状态":u(n.shifouzj),"号码识别":n.shibie||"","下注时间":n.betTime?e.parseTime(n.betTime,"{y}-{m}-{d} {h}:{i}:{s}"):"","投注总额":"","中奖总额":"","总注数":"","记录数":""})})),t.push({"数据类型":"","流水号":"","下注用户":"","彩种":"","玩法名称":"","期号":"","下注号码":"","中奖号码":"","下注金额":"","中奖金额":"","结算状态":"","中奖状态":"","号码识别":"","下注时间":"","投注总额":"","中奖总额":"","总注数":"","记录数":""})}})),t},formatDataForExport:function(e){var t=this;return e.map((function(e){var n="";if(e.betNumbers)try{var r=JSON.parse(e.betNumbers);n=r.numbers&&Array.isArray(r.numbers)?r.numbers.map((function(e){return Object.values(e).join(".")})).join(", "):e.betNumbers}catch(u){n=e.betNumbers||""}var a="";if(e.winningNumbers)try{if("string"===typeof e.winningNumbers){var o=JSON.parse(e.winningNumbers);o.winning&&Array.isArray(o.winning)&&(a=o.winning.map((function(e){return void 0===e.b&&void 0===e.c?e.a:void 0===e.c?"".concat(e.a,".").concat(e.b):"".concat(e.a,".").concat(e.b,".").concat(e.c)})).join(", "))}else a=e.winningNumbers}catch(u){a=e.winningNumbers||""}var i=function(e){if(!e&&0!==e)return"0.00";var t=parseFloat(e);return t.toFixed(2)},s=function(e){return 1===e?"已结算":"未结算"},l=function(e){return 1===e?"已中奖":0===e?"未中奖":"待开奖"};return{"下注用户":t.getUserName(e.userId),"彩种":t.getLotteryName(e.lotteryId),"玩法名称":t.getMethodName(e.methodId),"期号":e.issueNumber,"下注号码":n,"中奖号码":a,"下注金额":i(e.money),"中奖金额":i(e.winAmount),"流水号":e.serialNumber,"结算状态":s(e.jiesuan),"中奖状态":l(e.shifouzj),"号码识别":e.shibie||"","下注时间":e.betTime?t.parseTime(e.betTime,"{y}-{m}-{d} {h}:{i}:{s}"):""}}))},exportToExcel:function(e){var t=this;Promise.all([n.e("chunk-70ca55ea"),n.e("chunk-2d0de310")]).then(n.bind(null,"8530")).then((function(n){try{var r=["数据类型","流水号","下注用户","彩种","投注总额","中奖总额","总注数","记录数","玩法名称","期号","下注号码","中奖号码","下注金额","中奖金额","结算状态","中奖状态","号码识别","下注时间"],a=["数据类型","流水号","下注用户","彩种","投注总额","中奖总额","总注数","记录数","玩法名称","期号","下注号码","中奖号码","下注金额","中奖金额","结算状态","中奖状态","号码识别","下注时间"],o=e.map((function(e){return a.map((function(t){var n=e[t]||"";return n}))})),i="下注管理_".concat(t.parseTime(new Date,"{y}{m}{d}_{h}{i}{s}"));n.export_json_to_excel({header:r,data:o,filename:i,autoWidth:!0,bookType:"xlsx"}),t.$modal.msgSuccess("导出成功")}catch(s){console.error("Excel导出过程中出错:",s),t.$modal.msgError("Excel导出失败: "+s.message)}})).catch((function(e){console.error("Excel工具加载失败:",e),t.$modal.msgError("Excel工具加载失败: "+e.message)}))},getLotteryName:function(e){try{var t={1:"福彩3D",2:"体彩排三"};return t[e]||"彩种".concat(e)}catch(n){return console.error("获取彩种名称失败:",n),"彩种".concat(e)}}},"getMethodName",(function(e){try{if(!this.gameMethodsData||!Array.isArray(this.gameMethodsData))return"玩法".concat(e);var t=Number(e),n=this.gameMethodsData.find((function(e){return Number(e.methodId)===t}));return n?n.methodName:"玩法".concat(e)}catch(r){return console.error("获取玩法名称失败:",r),"玩法".concat(e)}})),"getKuaduDisplay",(function(e){var t=e.methodId-60;return"跨度".concat(t)})),"handleExportSimple",(function(){this.download("game/record/export",Object(c["a"])({},this.queryParams),"record_".concat((new Date).getTime(),".xlsx"))})),"handleDrawSettlement",(function(){var e=this;this.$modal.confirm("是否确认进行开奖结算？").then((function(){Object(I["g"])().then((function(t){e.$modal.msgSuccess("开奖结算成功"),e.getList()})).catch((function(t){e.$modal.msgError("开奖结算失败："+t)}))})).catch((function(){}))})),"handleCancelDrawSettlement",(function(){var e=this;this.$modal.confirm("是否确认撤销所有彩种的结算？此操作将撤销所有已结算的开奖记录！","撤销结算确认",{confirmButtonText:"确定撤销",cancelButtonText:"取消",type:"warning"}).then((function(){return _["a"].get("/game/draw/list",{params:{pageNum:1,pageSize:1e3,jiesuan:1}})})).then((function(e){if(200===e.code&&e.rows&&e.rows.length>0){var t=e.rows.map((function(e){return e.drawId}));return _["a"].put("/game/draw/cancelPay/"+t.join(","))}throw new Error("没有找到已结算的记录")})).then((function(t){if(200!==t.code)throw new Error(t.msg||"撤销结算失败");e.$modal.msgSuccess("撤销结算成功"),e.getList()})).catch((function(t){t&&"cancel"!==t&&e.$modal.msgError("撤销结算失败："+(t.message||t))}))})),"toggleExpandSecondLevel",(function(){var e=this;this.isButtonOperation=!0,this.isExpandAll=!this.isExpandAll,this.isExpandAll?this.$nextTick((function(){e.expandSecondLevel()})):this.$nextTick((function(){e.$refs.recordTable&&e.recordList.forEach((function(t){e.$refs.recordTable.toggleRowExpansion(t,!1)}))}))})),"scrollToTop",(function(){window.scrollTo({top:0,behavior:"smooth"})}))},N=g,U=(n("d7b7"),n("2877")),v=Object(U["a"])(N,r,a,!1,null,"708ba832",null);t["default"]=v.exports},"4d90":function(e,t,n){"use strict";var r=n("23e7"),a=n("0ccb").start,o=n("9a0c");r({target:"String",proto:!0,forced:o},{padStart:function(e){return a(this,e,arguments.length>1?arguments[1]:void 0)}})},"5c5d":function(e,t,n){"use strict";n.r(t),n.d(t,"TWO_CODE_PATTERNS",(function(){return i}));var r=n("3835"),a=n("b85c"),o=(n("99af"),n("4de4"),n("a630"),n("a15b"),n("d81d"),n("14d9"),n("4ec9"),n("a9e3"),n("b64b"),n("d3b7"),n("4d63"),n("c607"),n("ac1f"),n("2c3e"),n("00b4"),n("25f0"),n("8a79"),n("3ca3"),n("466d"),n("5319"),n("1276"),n("498a"),n("0643"),n("76d6"),n("2382"),n("4e3e"),n("a573"),n("9a9a"),n("159b"),n("ddb0"),n("f9d6")),i={LIANGMA_ZUHE:{pattern:/^(\d{2})(?:组合|组)[\-—~～@#￥%…&!、\\/*,，.＝=·吊赶打各\s]*(\d+)?$/,handler:function(e,t){if(2!==e[1].length)return null;var n=e[1],r=e[2];return n[0]===n[1]?null:[{methodId:o["METHOD_ID"].LIANGMA_ZUHE,betNumbers:n,money:r}]}},LIANGMA_DUIZI:{pattern:/^(\d{2})[\-—~～@#￥%…&!、\\/*,，.＝=·吊赶打各\s]*对子[\-—~～@#￥%…&!、\\/*,，.＝=·吊赶打各\s]*(\d+)?(元|米|块)?$/,handler:function(e,t){if(2!==e[1].length)return null;var n=e[1],r=e[2];return n[0]!==n[1]?null:[{methodId:o["METHOD_ID"].LIANGMA_DUIZI,betNumbers:n,money:r}]}},LIANGMA_ZUSAN:{pattern:/^(\d{2})[\-—~～@#￥%…&!、\\/*,，一－.＝=·\s]*组三[\-—~～@#￥%…&!、\\/*,，.＝=·吊赶打各\s]*(\d+)?(元|米|块)?$/,handler:function(e,t){if(2!==e[1].length)return null;var n=e[1],r=e[2],a=e[3];if(n[0]!==n[1])return null;var i={methodId:o["METHOD_ID"].LIANGMA_DUIZI,betNumbers:n,money:r};return a&&(i.unit=a),[i]}},LIANGMA_ZUHE_DUIZI:{pattern:/^(\d{2})(?:[^0-9]+\d{2})*-(\d+)\+(\d+)$/,handler:function(e,t){var n=t.split(o["BASE_PATTERNS"].SEPARATORS).filter(Boolean).filter((function(e){return 2===e.length})),r=t.split(o["BASE_PATTERNS"].SEPARATORS).filter(Boolean);if(!r.length||r.some((function(e){return 2!==e.length})))return null;var a=e[2],i=e[3];return[{methodId:3,betNumbers:n.join(","),money:a},{methodId:4,betNumbers:n.join(","),money:i}]}},MULTI_LIANGMA:{pattern:/^(\d{2}(?:[^0-9]+\d{2})*)(?:各([一二三四五六七八九十百千万\d]+))?(?:防([一二三四五六七八九十百千万\d]+))?$/,handler:function(e,t){var r=n("f9d6"),a=r.chineseToNumber,i=t.split(o["BASE_PATTERNS"].SEPARATORS).filter(Boolean).filter((function(e){return 2===e.length}));if(!i.length)return null;var s=e[2]?a(e[2]):"",l=e[3]?a(e[3]):"",u=[];return s&&u.push({methodId:o["METHOD_ID"].LIANGMA_ZUHE,betNumbers:i.join(","),money:s}),l&&u.push({methodId:o["METHOD_ID"].LIANGMA_DUIZI,betNumbers:i[i.length-1],money:l}),console.log("两码 handler 返回:",u),u}},LIANGMA_ZUHE_ZU_SUPER:{pattern:/^(\d{2})组(\d+)$/,handler:function(e,t){var r=e[1];if(2!==r.length)return null;var a=e[2];return[{methodId:n("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:r,money:a}]}},LIANGMA_ZUHE_MULTI_SUPER:{pattern:/^(\d{2}(?:[\-~～@#￥%…&!、\\/*一－。,，.＝=· \t+吊赶打各]+\d{2})*)[\-~～@#￥%…&!、\\/*一－。,，.＝=· \t+吊赶打各]+(\d+)(元|块|米)?$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=e[1].split(a).filter(Boolean);if(!o.length||o.some((function(e){return 2!==e.length})))return null;var i=e[2],s=[],l=[];o.forEach((function(e){e[0]===e[1]?l.push(e):s.push(e)}));var u=[];return s.length&&u.push({methodId:n("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:s.join(","),money:i}),l.length&&u.push({methodId:n("f9d6").METHOD_ID.LIANGMA_DUIZI,betNumbers:l.join(","),money:i}),u.length?u:null}},LIANGMA_DUIZI_DUI_SUPER:{pattern:/^(\d{2})对子(\d+)$/,handler:function(e,t){var r=e[1];if(2!==r.length)return null;var a=e[2];return[{methodId:n("f9d6").METHOD_ID.LIANGMA_DUIZI,betNumbers:r,money:a}]}},LIANGMA_SINGLE_WITH_MONEY_SUPER:{pattern:/^(\d{2})[\-~～@#￥%…&!、\\/*,，一－。.＝=·\s+吊赶打各]+(\d{1,4})$/,handler:function(e,t){var r=e[1];if(2!==r.length)return null;var a=e[2];return r[0]===r[1]?[{methodId:n("f9d6").METHOD_ID.LIANGMA_DUIZI,betNumbers:r,money:a}]:[{methodId:n("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:r,money:a}]}},LIANGMA_MULTI_WITH_MONEY_SLASH_SUPER:{pattern:/^(\d{2}(?:[\-~～@#￥%…&!、\\/*,，.＝=一－。·\s+吊赶打各]+\d{2})*)\/(\d{1,4})$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=e[1].split(a).filter(Boolean);if(!o.length||o.some((function(e){return 2!==e.length})))return null;var i=e[2],s=[],l=[];o.forEach((function(e){e[0]===e[1]?l.push(e):s.push(e)}));var u=[];return s.length&&u.push({methodId:n("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:s.join(","),money:i}),l.length&&u.push({methodId:n("f9d6").METHOD_ID.LIANGMA_DUIZI,betNumbers:l.join(","),money:i}),u.length?u:null}},LIANGMA_MULTI_WITH_DOUBLE_MONEY_SLASH_SUPER:{pattern:/^(\d{2}(?:[\-~～@#￥%…&!、\\/*,，一－。.＝=·\s+吊赶打各]+\d{2})*)\/(\d+)-(\d+)$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=e[1].split(a).filter(Boolean);if(!o.length||o.some((function(e){return 2!==e.length})))return null;var i=o,s=e[2],l=e[3];return[{methodId:n("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:i.join(","),money:s},{methodId:n("f9d6").METHOD_ID.LIANGMA_DUIZI,betNumbers:i.join(","),money:l}]}},LIANGMA_ZUHE_FANG:{pattern:/^(\d{2})-(\d+)防(\d+)$/,handler:function(e,t){var r=e[1];if(2!==r.length)return null;var a=e[2],o=e[3];return[{methodId:n("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:r,money:a},{methodId:n("f9d6").METHOD_ID.LIANGMA_DUIZI,betNumbers:r,money:o}]}},LIANGMA_MULTI_ZUHE_DUIZI_SLASH_SUPER:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、＝一－。=·吊赶打各]+)(?:\/|各|\s|吊|赶|打)(\d+)\+(\d+)$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=e[1].split(a).filter(Boolean);if(!o.length||o.some((function(e){return 2!==e.length})))return null;var i=o,s=e[2],l=e[3];return[{methodId:n("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:i.join(","),money:s},{methodId:n("f9d6").METHOD_ID.LIANGMA_DUIZI,betNumbers:i.join(","),money:l}]}},LIANGMA_MULTI_EACH_SUPER:{pattern:/^(\d{2}(?:[\-~～@#￥%…&!、\\/*,，.＝=一－。·\s+吊赶打各]+\d{2})*)各(\d+)(元|块|米)?$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=e[1].split(a).filter(Boolean);if(!o.length||o.some((function(e){return 2!==e.length})))return null;var i=e[2];return[{methodId:n("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:o.join(","),money:i}]}},LIANGMA_MULTI_SUPER_SLASH_TWO_MONEY:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、一－。＝=·吊赶打各]+)\/(\d+)\/(\d+)$/,handler:function(e,t){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=n("f9d6"),o=a.SUPER_SEPARATORS,i=e[1].split(o).map((function(e){return e.trim()})).filter((function(e){return 2===e.length}));if(!i.length)return[];var s=e[2],l=e[3];return r&&(/^\d{2}$/.test(s)||/^\d{2}$/.test(l))?null:[{methodId:n("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:i.join(","),money:s},{methodId:n("f9d6").METHOD_ID.LIANGMA_DUIZI,betNumbers:i.join(","),money:l}]}},LIANGMA_MULTI_WITH_MONEY_UNIT_SUPER:{pattern:/^([\s\S]+)$/m,handler:function(e,t){var o,i=t.split(/\n+/).map((function(e){return e.trim()})).filter(Boolean),s=new Map,l=0,u=Object(a["a"])(i);try{for(u.s();!(o=u.n()).done;){var c=o.value,d=c.match(/(\d{2})[\s,，.。\-—~～@#￥%…&!、\\/*]*\/?[\s,，.。\-—~～@#￥%…&!、\\/*]*(\d+)[\s,，.。\-—~～@#￥%…&!、\\/*]*(元|块|米)/);if(d){l++;var m=d[1],h=d[2];s.has(h)||s.set(h,[]),s.get(h).push(m)}}}catch(f){u.e(f)}finally{u.f()}return l<2?null:s.size?Array.from(s.entries()).map((function(e){var t=Object(r["a"])(e,2),a=t[0],o=t[1];return{methodId:n("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:o.join(","),money:a}})):null}},LIANGMA_UNIVERSAL_SUPER:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!一－—。、＝=·吊赶打各]+)[\-~～@#￥%…&!、\\/*,，一－—。.＝=·吊赶打各]+([0-9]+)(元|米|块)?$/,handler:function(e,t){var o=n("f9d6"),i=o.SUPER_SEPARATORS,s=o.METHOD_ID,l=t.split(/\n|\r/).map((function(e){return e.trim()})).filter(Boolean),u=new Map;l.forEach((function(e){var t=e.match(/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!一－—。、＝=·吊赶打各]+)[\-~～@#￥%…&!、\\/*,，一－—。.＝=·吊赶打各]+([0-9]+)(元|米|块)?$/);if(t){var n=t[1].split(i).map((function(e){return e.trim()})).filter((function(e){return 2===e.length})),r=t[2];n.forEach((function(e){var t=null;t=e[0]===e[1]?s.LIANGMA_DUIZI:s.LIANGMA_ZUHE;var n=t+"_"+r;u.has(n)||u.set(n,[]),u.get(n).push(e)}))}}));var c,d=[],m=Object(a["a"])(u.entries());try{for(m.s();!(c=m.n()).done;){var h=Object(r["a"])(c.value,2),f=h[0],I=h[1],b=f.split("_"),p=Object(r["a"])(b,2),_=p[0],g=p[1];d.push({methodId:Number(_),betNumbers:I.join(","),money:g})}}catch(N){m.e(N)}finally{m.f()}return d.length?d:null}},LIANGMA_EACH_PLUS_SUPER:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!一－。、＝=·吊赶打各]+)各([0-9]+)\+([0-9]+)$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=e[1].split(a).map((function(e){return e.trim()})).filter(Boolean),s=e[2],l=e[3];return 1===i.length&&2===i[0].length?[{methodId:o.LIANGMA_ZUHE,betNumbers:i[0],money:s},{methodId:o.LIANGMA_DUIZI,betNumbers:i[0],money:l}]:i.length>=2&&i.every((function(e){return 2===e.length}))?[{methodId:o.LIANGMA_ZUHE,betNumbers:i.join(","),money:s},{methodId:o.LIANGMA_DUIZI,betNumbers:i.join(","),money:l}]:null}},LIANGMA_UNIVERSAL_SUPER_NEW:{pattern:/^([0-9]{2,})\s+(\d+)(元|块|米)?$/,handler:function(e,t){var r=e[1],a=e[2];return r&&2===r.length?[{methodId:n("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:r,money:a}]:null}},LIANGMA_DA_MONEY:{pattern:/^([0-9]{2})打(\d+)(元|块|米)?$/,handler:function(e,t){return[{methodId:n("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:e[1],money:e[2]}]}},LIANGMA_MULTI_WITH_MONEY_SLASH_PLUS:{pattern:/^(\d{2}(?:[\s,，\-]+?\d{2})*)\/\s*(\d+)\+(\d+)$/,handler:function(e,t){var r=e[1].split(/[\s,，\-]+/).filter(Boolean),a=e[2],o=e[3];return[{methodId:n("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:r.join(","),money:a},{methodId:n("f9d6").METHOD_ID.LIANGMA_DUIZI,betNumbers:r.join(","),money:o}]}},LIANGMA_MULTI_LINE_MONEY_PLUS_CN:{pattern:/^((?:\d{2}[，,、]?[一二三四五六七八九十百千万\d]+加[一二三四五六七八九十百千万\d]+\s*\n?)+)$/m,handler:function(e,t){var o,i=n("f9d6"),s=i.METHOD_ID,l=i.chineseToNumber,u=t.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),c=new Map,d=[],m=Object(a["a"])(u);try{for(m.s();!(o=m.n()).done;){var h=o.value,f=h.match(/^(\d{2})[，,、]?([一二三四五六七八九十百千万\d]+)加([一二三四五六七八九十百千万\d]+)$/);if(f){var I=f[1],b=l(f[2]),p=l(f[3]),_="".concat(b,"+").concat(p);c.has(_)||c.set(_,[]),c.get(_).push(I)}else d.push(h)}}catch(S){m.e(S)}finally{m.f()}var g,N=[],U=Object(a["a"])(c.entries());try{for(U.s();!(g=U.n()).done;){var v=Object(r["a"])(g.value,2),A=v[0],y=v[1],M=A.split("+"),E=Object(r["a"])(M,2),D=E[0],O=E[1];N.push({methodId:s.LIANGMA_ZUHE,betNumbers:y.join(","),money:D}),N.push({methodId:s.LIANGMA_DUIZI,betNumbers:y.join(","),money:O})}}catch(S){U.e(S)}finally{U.f()}return N.length?N:null}},DUIZI_SLASH_MONEY:{pattern:/^对(\d{2})\/(\d+)$/,handler:function(e,t){return[{methodId:o["METHOD_ID"].LIANGMA_DUIZI,betNumbers:e[1],money:e[2]}]}},ERMA_YI_YI_ONE_MONEY:{pattern:/^(\d{2})一(\d{2})一个(\d+)(元|块|米)?$/,handler:function(e,t){var r=e[1],a=e[2],o=e[3];return[{methodId:n("f9d6").METHOD_ID.ERMA_COMBO,betNumbers:r+","+a,money:o}]}},LIANGMA_MULTI_SINGLE_WITH_MONEY_FEN_SUPER:{pattern:/^([\s\S]+)$/m,handler:function(e,t){if(!/分\s*$/.test(t))return null;t=t.replace(/分\s*$/m,"").replace(/[\r\n]+/g," ").trim();var a,o=/(\d{2})[\-—~～@#￥%…&!、\\/*,，.＝=·吊赶打各\s]+(\d{2,3})(?=[\-—~～@#￥%…&!、\\/*,，.＝=·吊赶打各\s]|$)/g,i=new Map,s=[],l=t;while(null!==(a=o.exec(t)))s.push({num:a[1],money:a[2],raw:a[0]}),l=l.replace(a[0],"@@"),i.has(a[2])||i.set(a[2],[]),i.get(a[2]).push(a[1]);if(0===s.length)return null;if(!/^\d{2}$/.test(s[0].num))return null;if(l.replace(/[@\-—~～@#￥%…&!、\\/*,，.＝=·吊赶打各\s]/g,"").length>0)return null;var u=n("f9d6").METHOD_ID;return Array.from(i.entries()).map((function(e){var t=Object(r["a"])(e,2),n=t[0],a=t[1];return{methodId:u.LIANGMA_MULTI_SINGLE_WITH_MONEY_FEN_SUPER||3,betNumbers:a.join(","),money:n}}))}}};Object.keys(i).forEach((function(e){var t=i[e].pattern;"string"===typeof t&&t.endsWith("$")?i[e].pattern=t.replace(/\$$/,"[，,./*-=。·、s]*$"):t instanceof RegExp&&t.source.endsWith("$")&&(i[e].pattern=new RegExp(t.source.replace(/\$$/,"[，,./*-=。·、s]*$")))}))},"5f85":function(e,t,n){"use strict";n.r(t),n.d(t,"SIX_CODE_PATTERNS",(function(){return s}));var r=n("2909"),a=n("3835"),o=n("b85c"),i=(n("99af"),n("4de4"),n("caad"),n("a15b"),n("d81d"),n("14d9"),n("fb6a"),n("4e82"),n("4ec9"),n("b64b"),n("d3b7"),n("4d63"),n("c607"),n("ac1f"),n("2c3e"),n("00b4"),n("25f0"),n("8a79"),n("2532"),n("3ca3"),n("466d"),n("5319"),n("1276"),n("498a"),n("0643"),n("76d6"),n("2382"),n("4e3e"),n("a573"),n("9a9a"),n("159b"),n("ddb0"),n("f9d6")),s={LIUMA_TRANSFORM:{pattern:/^(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?(\d{6})[\-—~～@#￥%…&!、\\/*,，.＝=各·\s]*?(转|套|拖)(?:各)?(\d+)(?:\+(\d+))?(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?$/,handler:function(e,t){var r=e[2],a=e[4],o=e[5],i=e[6],s=n("f9d6").generateTransformNumbers(r),l=[];return a?l.push({methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:s.join(","),money:a}):o&&i&&(l.push({methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:s.join(","),money:o}),l.push({methodId:n("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:s.join(","),money:i})),l}},LIUMA_ZUSAN:{pattern:/^(\d{6})[\-—~～@#￥%…&!、\\/*,，。，一－.＝=·\s]*组三?[\-—~～@#￥%…&!、\\/*,，。，一－.＝=·\s]*([\d]+)?(元|块|米)?[\-—~～@#￥%…&!、。，一－\\/*,，.＝=·\s]*$/,handler:function(e,t){return 6!==e[1].length?null:[{methodId:i["METHOD_ID"].LIUMA_ZUSAN,betNumbers:e[1],money:e[2]||""}]}},LIUMA_FANGDUI:{pattern:/^(\d{6})[\-—~～@#￥%…&!、\\/*,，一－.＝=·\s]*防对?(?:-?(\d+))?$/,handler:function(e,t){return 6!==e[1].length?null:[{methodId:i["METHOD_ID"].LIUMA_ZUSAN,betNumbers:e[1],money:e[2]||""}]}},LIUMA_SIMPLE:{pattern:/^(\d{6})\/(\d+)\/?$/,handler:function(e,t){var n=e[1],r=e[2];return[{methodId:i["METHOD_ID"].LIUMA_ZULIU,betNumbers:n,money:r}]}},MULTI_LIUMA:{pattern:/^(\d{6}(?:[^0-9\n]+\d{6})*)(?:各([一二三四五六七八九十百千万\d]+))?(?:防([一二三四五六七八九十百千万\d]+))?$/,handler:function(e,t){var r=n("f9d6"),a=r.chineseToNumber,o=t.split(i["BASE_PATTERNS"].SEPARATORS).filter(Boolean).filter((function(e){return 6===e.length}));if(!o.length||!o.every((function(e){return 6===e.length})))return null;var s=e[2]?a(e[2]):"",l=e[3]?a(e[3]):"",u=[];return s&&u.push({methodId:i["METHOD_ID"].LIUMA_ZULIU,betNumbers:o.join(","),money:s}),l&&u.push({methodId:i["METHOD_ID"].LIUMA_ZUSAN,betNumbers:o[o.length-1],money:l}),u.length?u:null}},LIUMA_ZULIU_ZUSAN_MULTI_MONEY_SUPER:{pattern:/^(\d{6}(?:[－\-—~～@#￥%…&!、\\/*,一－。.＝=·+吊赶打各]+\d{6})*)[\-—~～@#￥%…&!、\\/*,一－.。＝=·+吊赶打各]+(\d+)[\-—~～@#￥%…&!、\\/*,一－.。＝=·+吊赶打各]*[\+\-\\/*、.]+[\-—~～@#￥%…&!、\\/*,一－.。＝=·+吊赶打各]*(\d+)(?:元)?$/,handler:function(e,t){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=n("f9d6"),o=a.SUPER_SEPARATORS,i=e[1].split(o).filter(Boolean),s=e[2],l=e[3];return r&&(/^\d{6}$/.test(s)||/^\d{6}$/.test(l))?null:[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:i.join(","),money:s},{methodId:n("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:i.join(","),money:l}]}},LIUMA_ZULIU_ZU_SUPER:{pattern:/^(\d{6})组(\d+)$/,handler:function(e,t){var r=e[1],a=e[2];return[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:r,money:a}]}},LIUMA_ZULIU_MULTI_SUPER:{pattern:/^(\d{6}(?:[\-—~～@#￥%…&一－。+!、\\/*,，.＝=·\s\n吊赶打各]+\d{6})*)[\-—~～@#￥%…&一－。!、\\/*,，.＝=·\s+吊赶打各]+各(\d+)(元)?(共计\d+(元)?)?$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=e[1].split(a).filter(Boolean),i=e[2];return[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:o.join(","),money:i}]}},LIUMA_ZUSAN_ZU_SUPER:{pattern:/^(\d{6})[\-—~～@#￥%…&!、\\/*,，一－.＝=·\s]*组三[\-—~～@#￥%…&!、\\/*,，一－.＝=·\s]*(\d+)$/,handler:function(e,t){var r=e[1],a=e[2];return[{methodId:n("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:r,money:a}]}},LIUMA_ZHIZU_MULTI_SUPER:{pattern:/^(\d{6}(?:[\-—~～@#￥%…&!、\\/一－。*,，.＝=·\s+吊赶打各]+\d{6})*)[\-—~～@#￥%…&!、一－。\\/*,，.＝=·\s+吊赶打各]*(直组|组直|值组|组值)[\s]*各?(\d+)(?:元)?$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=e[1].split(a).filter(Boolean),i=e[3];return[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:o.join(","),money:i},{methodId:n("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:o.join(","),money:i}]}},LIUMA_ZULIU_FANG:{pattern:/^(\d{6})[+＋\-—~～@#￥%…&!、：:\\/*,，。一—－~.＝=·吊赶打各](\d+)防(\d+)$/,handler:function(e,t){var n=e[1],r=e[2],a=e[3];return[{methodId:i["METHOD_ID"].LIUMA_ZULIU,betNumbers:n,money:r},{methodId:i["METHOD_ID"].LIUMA_ZUSAN,betNumbers:n,money:a}]}},LIUMA_MULTI_ZHIZU_EACH_SUPER:{pattern:/^(直组|组直|值组|组值)各([0-9]+)[\s\-—~～@#￥%…&!一－。、\\/*,，.＝=·吊赶打各\n]+([\d\s\-—~～@#￥%…&!一－。、\\/*,，.＝=·吊赶打各]+)$/i,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=e[2],i=e[3].replace(/\n/g," "),s=i.split(a).map((function(e){return e.trim()})).filter((function(e){return/^\d{6}$/.test(e)}));return[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:s.join(","),money:o},{methodId:n("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:s.join(","),money:o}]}},LIUMA_MULTI_GAN_FANGDUI_SUPER:{pattern:/^([0-9]{6}(?:[\-—~～@#￥%…&!、\\/*,，.一－。＝=·+吊赶打各 ]+[0-9]{6})*)[\-—~～@#￥%…&!、一－。\\/*,，.＝=·+吊赶打各 ]+赶([0-9]+)[\-—~～@#￥%…&!、一－。\\/*,，.＝=·+吊赶打各 ]*防对([0-9]+)$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=e[1].split(a).filter(Boolean);console.log("格式化后号码:",o.join(","));var i=e[2],s=e[3];return[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:o.join(","),money:i},{methodId:n("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:o.join(","),money:s}]}},LIUMA_MULTI_EACH_SUPER:{pattern:/^(\d{6}(?:[\-~～@#￥%…&!、\\/*,一－。，.＝=·\+吊赶打各 ]+\d{6})*)[+＋\-—~～@#￥%…&!、：:\\/*,，。一—－~.＝=·吊赶打各 ](\d+)$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=e[1].split(a).filter(Boolean);if(!o.length||o.some((function(e){return 6!==e.length})))return[];var i=e[2];return[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:o.join(","),money:i}]}},LIUMA_SLASH_TWO_MONEY:{pattern:/^(?!.*[\r\n])(\d{6})[\-—~～@#￥%…&!、\\/*,，.。\u4e00\uff0d\uff1d=· 　\t]+(\d{!6}+)[\-—~～@#￥%…&!、\\/*,，.。\u4e00\uff0d\uff1d=· 　\t]+(\d+)$/,handler:function(e,t){var r=e[1],a=e[2],o=e[3];return[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:r,money:a},{methodId:n("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:r,money:o}]}},LIUMA_UNIVERSAL_SUPER:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!一－—。、＝=·吊赶打各]+)[\-~～@#￥%…&!、\\/*,，.一－。—＝=·\s+吊赶打各]+([0-9]+)(元|米|块)?$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=e[1].split(a).map((function(e){return e.trim()})).filter(Boolean),s=e[2];return 1===i.length&&6===i[0].length?[{methodId:o.LIUMA_ZULIU,betNumbers:i[0],money:s}]:i.length>=2&&i.every((function(e){return 6===e.length}))?[{methodId:o.LIUMA_ZULIU,betNumbers:i.join(","),money:s}]:null}},LIUMA_EACH_PLUS_SUPER:{pattern:/^([\d、,，.。\\/*\-~～@#￥一－。%…&!、＝=·吊赶打各]+)各([0-9]+)\+([0-9]+)$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=e[1].split(a).map((function(e){return e.trim()})).filter(Boolean),s=e[2],l=e[3];return 1===i.length&&6===i[0].length?[{methodId:o.LIUMA_ZULIU,betNumbers:i[0],money:s},{methodId:o.LIUMA_ZUSAN,betNumbers:i[0],money:l}]:i.length>=2&&i.every((function(e){return 6===e.length}))?[{methodId:o.LIUMA_ZULIU,betNumbers:i.join(","),money:s},{methodId:o.LIUMA_ZUSAN,betNumbers:i.join(","),money:l}]:null}},LIUMA_UNIVERSAL_SUPER_NEW:{pattern:/^([0-9]{6,})\s+(\d+)(元|块|米)?$/,handler:function(e,t){var r=e[1],a=e[2];return r&&6===r.length?[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:r,money:a}]:null}},LIUMA_DA_MONEY:{pattern:/^([0-9]{6})打(\d+)(元|块|米)?$/,handler:function(e,t){return[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:e[1],money:e[2]}]}},LIUMA_MULTI_WITH_MONEY_SLASH_PLUS:{pattern:/^(\d{6}(?:[\s,，、\-]+?\d{6})*)(?:\/|各|\s|吊|赶|打|-|\+|、)(\d+)\+(\d+)$/,handler:function(e,t){var r=e[1].split(/[\s,，\-]+/).filter(Boolean),a=e[2],o=e[3];return[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:r.join(","),money:a},{methodId:n("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:r.join(","),money:o}]}},LIUMA_MULTI_LINE_MONEY_PLUS:{pattern:/^((?:\d{6}[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+\d+\+\d+\s*\n?)+)$/m,handler:function(e,t){var r,i=n("f9d6"),s=i.METHOD_ID,l=(i.SUPER_SEPARATORS,t.split(/\n/).map((function(e){return e.trim()})).filter(Boolean)),u=new Map,c=!0,d=Object(o["a"])(l);try{for(d.s();!(r=d.n()).done;){var m=r.value,h=m.match(/^(\d{6})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+(\d+)\+(\d+)$/);if(!h){c=!1;break}var f=h[1];if(6!==f.length){c=!1;break}var I=m.split(/[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+/)[0];if(6!==I.length||!/^\d{6}$/.test(I)){c=!1;break}var b="".concat(h[2],"+").concat(h[3]);u.has(b)||u.set(b,[]),u.get(b).push(f)}}catch(D){d.e(D)}finally{d.f()}if(!c||0===u.size)return null;var p,_=[],g=Object(o["a"])(u.entries());try{for(g.s();!(p=g.n()).done;){var N=Object(a["a"])(p.value,2),U=N[0],v=N[1],A=U.split("+"),y=Object(a["a"])(A,2),M=y[0],E=y[1];_.push({methodId:s.LIUMA_ZULIU,betNumbers:v.join(","),money:M}),_.push({methodId:s.LIUMA_ZUSAN,betNumbers:v.join(","),money:E})}}catch(D){g.e(D)}finally{g.f()}return _.length?_:null}},LIUMA_MULTI_LINE_MONEY_PLUS_CN:{pattern:/^((?:\d{6}[，,、]?[一二三四五六七八九十百千万\d]+加[一二三四五六七八九十百千万\d]+\s*\n?)+)$/m,handler:function(e,t){var r,i=n("f9d6"),s=i.METHOD_ID,l=i.chineseToNumber,u=t.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),c=new Map,d=Object(o["a"])(u);try{for(d.s();!(r=d.n()).done;){var m=r.value,h=m.match(/^(\d{6})[，,、]?([一二三四五六七八九十百千万\d]+)加([一二三四五六七八九十百千万\d]+)$/);if(h){var f=h[1],I=l(h[2]),b=l(h[3]),p="".concat(I,"+").concat(b);c.has(p)||c.set(p,[]),c.get(p).push(f)}}}catch(O){d.e(O)}finally{d.f()}var _,g=[],N=Object(o["a"])(c.entries());try{for(N.s();!(_=N.n()).done;){var U=Object(a["a"])(_.value,2),v=U[0],A=U[1],y=v.split("+"),M=Object(a["a"])(y,2),E=M[0],D=M[1];g.push({methodId:s.LIUMA_ZULIU,betNumbers:A.join(","),money:E}),g.push({methodId:s.LIUMA_ZUSAN,betNumbers:A.join(","),money:D})}}catch(O){N.e(O)}finally{N.f()}return g.length?g:null}},LIUMA_MULTI_EACH_FANGDUI_SLASH:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%一－。…&!、＝=·吊赶打各]+)[/\-~～@#￥%…&!、一－。\\/*,，.＝=·吊赶打各]+各防对([一二三四五六七八九十百千万\d]+)$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=r.chineseToNumber,s=e[1].split(a).map((function(e){return e.trim()})).filter((function(e){return 6===e.length})),l=i(e[2]);return s.length?[{methodId:o.LIUMA_ZUSAN,betNumbers:s.join(","),money:l}]:null}},LIUMA_MULTI_EACH_FANGDUI:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥一－。%…&!、＝=·吊赶打各]+)各防对([一二三四五六七八九十百千万\d]+)$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=r.chineseToNumber,s=e[1].split(a).map((function(e){return e.trim()})).filter((function(e){return 6===e.length})),l=i(e[2]);return s.length?[{methodId:o.LIUMA_ZUSAN,betNumbers:s.join(","),money:l}]:null}},LIUMA_SLASH_MONEY:{pattern:/^\s*(\d{6})\s*\/\s*([一二三四五六七八九十百千万\d]+)\s*$/,handler:function(e,t){var r=n("f9d6"),a=r.METHOD_ID,o=r.chineseToNumber,i=e[1],s=o(e[2]);return[{methodId:a.LIUMA_ZULIU,betNumbers:i,money:s}]}},LIUMA_ZULIU_ZUSAN_MONEY_PAIR:{pattern:/^(\d{6}[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*)组六[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*(\d+)[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*组三[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*(\d+)$/,handler:function(e,t){var n=e[1],r=e[2],a=e[3];return[{methodId:i["METHOD_ID"].LIUMA_ZULIU,betNumbers:n,money:r},{methodId:i["METHOD_ID"].LIUMA_ZUSAN,betNumbers:n,money:a}]}},LIUMA_ZULIU:{pattern:/^(\d{6})组六?(\d+)[，,./\*\-\=。·、\s]*$/,handler:function(e,t){var n=e[1];if(6!==n.length)return null;var r=e[2]||"";return[{methodId:i["METHOD_ID"].LIUMA_ZULIU,betNumbers:n,money:r}]}},LIUMA_ZULIU_ZUSAN_COMBO:{pattern:/^(\d{6}(?:[。.．、,，\s]+\d{6})*)\/(\d+)\+(\d+)$/,handler:function(e,t){var r=n("f9d6"),a=(r.SUPER_SEPARATORS,e[1].split(/[。.．、,，\s]+/).map((function(e){return e.trim()})).filter(Boolean)),o=e[2],i=e[3];return a.length&&a.every((function(e){return 6===e.length}))?[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:a.join(","),money:o},{methodId:n("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:a.join(","),money:i}]:null}},LIUMA_SINGLE_LINE_ZULIU_ZUSAN_PLUS:{pattern:/^(\d{6})\s+(\d+)[+＋](\d+)$/,handler:function(e,t){return[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:e[1],money:e[2]},{methodId:n("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:e[1],money:e[3]}]}},LIUMA_MULTI_LINE_ZULIU_PLUS:{pattern:/^((?:\d{6}[+＋]\d+\s*\n?)+)$/m,handler:function(e,t){var r,i=n("f9d6"),s=i.METHOD_ID,l=n("b2f0"),u=(l.getBestPatternResult,t.split(/\n/).map((function(e){return e.trim()})).filter(Boolean)),c=new Map,d=[],m=Object(o["a"])(u);try{for(m.s();!(r=m.n()).done;){var h=r.value,f=h.match(/^(\d{6})[+＋](\d+)$/);if(f){var I=f[1],b=f[2];c.has(b)||c.set(b,[]),c.get(b).push(I)}else d.push(h)}}catch(A){m.e(A)}finally{m.f()}var p,_=[],g=Object(o["a"])(c.entries());try{for(g.s();!(p=g.n()).done;){var N=Object(a["a"])(p.value,2),U=N[0],v=N[1];_.push({methodId:s.LIUMA_ZULIU,betNumbers:v.join(","),money:U})}}catch(A){g.e(A)}finally{g.f()}return _}},LIUMA_ZULIU_FANG_ZUSAN_PLUS:{pattern:/^(\d{6})[\-—~～@#￥%…&!、一－。\\/*,，.＝=·\s+吊赶打各]*(\d+)[\-—~～@#￥%…&!、一－。\\/*,，.＝=·\s+吊赶打各]*(防对|防)(\d+)$/,handler:function(e,t){return[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:e[1],money:e[2]},{methodId:n("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:e[1],money:e[4]}]}},LIUMA_MULTI_LINE_ZULIU_FANG_ZUSAN_GROUP:{pattern:/^([\s\S]+)$/m,handler:function(e,t){var r,i=t.split(/[\r\n]+/).map((function(e){return e.trim()})).filter(Boolean),s=new Map,l=Object(o["a"])(i);try{for(l.s();!(r=l.n()).done;){var u=r.value,c=u.match(/^(\d{6})-?(\d+)防(\d+)$/);if(c){var d=c[1],m=c[2],h=c[3],f="".concat(m,"|").concat(h);s.has(f)||s.set(f,[]),s.get(f).push(d)}}}catch(M){l.e(M)}finally{l.f()}var I,b=[],p=Object(o["a"])(s.entries());try{for(p.s();!(I=p.n()).done;){var _=Object(a["a"])(I.value,2),g=_[0],N=_[1],U=g.split("|"),v=Object(a["a"])(U,2),A=v[0],y=v[1];b.push({methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:N.join(","),money:A}),b.push({methodId:n("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:N.join(","),money:y})}}catch(M){p.e(M)}finally{p.f()}return b.length?b:null}},LIUMA_MULTI_LINE_WHOLE_MONEY:{pattern:/^([0-9]{6})\+(\d+)$/m,handler:function(e,t){var r,a=t.split(/[\r\n]+/).map((function(e){return e.trim()})).filter(Boolean),i=[],s=Object(o["a"])(a);try{for(s.s();!(r=s.n()).done;){var l=r.value,u=l.match(/^([0-9]{6})\+(\d+)$/);u&&i.push({methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:u[1],money:u[2]})}}catch(c){s.e(c)}finally{s.f()}return i.length?i:null}},LIUMA_MULTI_TRANSFORM_EACH:{pattern:/^(\d{6}(?:[\-~～@#￥%…&!、\\/*一－。,，.＝=·\s+吊赶打各]+\d{6})*)(转|转各|套|套各)(\d+)\+(\d+)$/,handler:function(e,t){var r,a=n("f9d6"),i=a.SUPER_SEPARATORS,s=a.METHOD_ID,l=a.generateTransformNumbers,u=e[1].split(i).filter(Boolean),c=e[3],d=e[4],m=[],h=Object(o["a"])(u);try{for(h.s();!(r=h.n()).done;){var f=r.value,I=l(f);m.push({methodId:s.QIMA_ZULIU,betNumbers:I.join(","),money:c}),m.push({methodId:s.QIMA_ZUSAN,betNumbers:I.join(","),money:d})}}catch(b){h.e(b)}finally{h.f()}return m}},LIUMA_MULTI_LINE_TRANSFORM:{pattern:/^((?:(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?\d{6}转\d+(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?\s*\n?)+)$/m,handler:function(e,t){var r,i=n("f9d6"),s=i.METHOD_ID,l=t.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),u=new Map,c=Object(o["a"])(l);try{for(c.s();!(r=c.n()).done;){var d=r.value,m=d.match(/^(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?(\d{6})转(\d+)(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?$/);if(m){var h=m[2],f=m[3];u.has(f)||u.set(f,[]),u.get(f).push(h)}}}catch(U){c.e(U)}finally{c.f()}var I,b=[],p=Object(o["a"])(u.entries());try{for(p.s();!(I=p.n()).done;){var _=Object(a["a"])(I.value,2),g=_[0],N=_[1];b.push({methodId:s.QIMA_ZULIU,betNumbers:N.join(","),money:g})}}catch(U){p.e(U)}finally{p.f()}return b.length?b:null}},LIUMA_MULTI_COLON_EACH_SUPER:{pattern:/^(\d{6}(?::|：\d{6})*)各(\d+)$/,handler:function(e,t){var r=t.split(/:|：/).map((function(e){return e.trim()})).filter((function(e){return 6===e.length})),a=e[2];return r.length?[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:r.join(","),money:a}]:null}},LIUMA_MULTI_YIGE_EACH_PLUS:{pattern:/^([\d一]+)一([\d一]+)一?各?(\d+)\+(\d+)$/,handler:function(e,t){var r=t.split("一").map((function(e){return e.replace(/[^\d]/g,"")})).filter((function(e){return 6===e.length})),a=e[3],o=e[4];return r.length?[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:r.join(","),money:a},{methodId:n("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:r.join(","),money:o}]:null}},LIUMA_DIAN_XMA_ZULIU_FANG_ZUSAN:{pattern:/^(\d{6})[。.．、,，\s]+6码(\d+)防(\d+)$/,handler:function(e,t){return[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:e[1],money:e[2]},{methodId:n("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:e[1],money:e[3]}]}},LIUMA_MULTI_LINE_ZULIU_MONEY_GROUP:{pattern:/^((?:\d{6}\s+\d+\s*\n?)+)$/m,handler:function(e,t){var r,i=n("f9d6"),s=i.METHOD_ID,l=t.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),u=Object(o["a"])(l);try{for(u.s();!(r=u.n()).done;){var c=r.value,d=c.match(/^(\d+)\s+(\d+)$/);if(d&&d[1].length===d[2].length)return null}}catch(M){u.e(M)}finally{u.f()}var m,h=new Map,f=Object(o["a"])(l);try{for(f.s();!(m=f.n()).done;){var I=m.value,b=I.match(/^(\d{6})\s+(\d+)$/);if(b){var p=b[1],_=b[2];h.has(_)||h.set(_,[]),h.get(_).push(p)}}}catch(M){f.e(M)}finally{f.f()}var g,N=[],U=Object(o["a"])(h.entries());try{for(U.s();!(g=U.n()).done;){var v=Object(a["a"])(g.value,2),A=v[0],y=v[1];N.push({methodId:s.LIUMA_ZULIU,betNumbers:y.join(","),money:A})}}catch(M){U.e(M)}finally{U.f()}return N.length?N:null}},LIUMA_MULTI_NUMBERS_EACH_FANG:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～一－。@#￥%…&!、＝=·吊赶打各]+)各(\d+)(防|防对|放对)(\d+)$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=e[1].split(a).map((function(e){return e.trim()})).filter((function(e){return 6===e.length}));if(!i.length)return null;var s=e[2],l=e[4];return[{methodId:o.LIUMA_ZULIU,betNumbers:i.join(","),money:s},{methodId:o.LIUMA_ZUSAN,betNumbers:i.join(","),money:l}]}},LIUMA_YI_YI_ONE_MONEY:{pattern:/^(\d{6})一(\d{6})一个(\d+)(元|块|米)?$/,handler:function(e,t){var r=e[1],a=e[2],o=e[3];return 6!==r.length||6!==a.length?null:[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:r+","+a,money:o}]}},LIUMA_GE_MONEY:{pattern:/^(\d{6})[，,、\s]*个(\d+)(元|块|米)?$/,handler:function(e,t){var r=e[1],a=e[2];return 6!==r.length?null:[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:r,money:a}]}},LIUMA_MULTI_LINE_ZULIU_MONEY_SUPER:{pattern:/^([\s\S]+)$/m,handler:function(e,t){var r,i=n("f9d6"),s=i.METHOD_ID,l=t.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),u=new Map,c=/^(\d{6})[\-—~～@#￥%…&!、一－。\\/*,，.＝=· 　\t]+(\d+)(元|米|块)?([，,./\\*\-\=。·、 　\t]*)?$/,d=[],m=Object(o["a"])(l);try{for(m.s();!(r=m.n()).done;){var h=r.value,f=h.match(c);if(!f)return console.log('【LIUMA_MULTI_LINE_ZULIU_MONEY_SUPER】行 "'.concat(h,'" 不匹配六码格式，返回null')),null;if(f[1].length===f[2].length)return console.log('【LIUMA_MULTI_LINE_ZULIU_MONEY_SUPER】行 "'.concat(h,'" 号码和金额长度相同，可能误匹配，返回null')),null;d.push({line:h,match:f})}}catch(j){m.e(j)}finally{m.f()}console.log("【LIUMA_MULTI_LINE_ZULIU_MONEY_SUPER】所有 ".concat(d.length," 行都是六码格式，继续处理"));for(var I=0,b=d;I<b.length;I++){var p=b[I].match;if(6===p[1].length){var _=p[1],g=p[2],N=p[3]||"",U=g+N;u.has(U)||u.set(U,[]),u.get(U).push(_)}}var v,A=[],y=Object(o["a"])(u.entries());try{for(y.s();!(v=y.n()).done;){var M=Object(a["a"])(v.value,2),E=M[0],D=M[1];if(D.length>0){var O,S=E.length>0?[E.replace(/(元|米|块)$/,""),(null===(O=E.match(/(元|米|块)$/))||void 0===O?void 0:O[0])||""]:["",""],T=Object(a["a"])(S,2),L=T[0],w=T[1];A.push({methodId:s.LIUMA_ZULIU,betNumbers:D.join(","),money:L,unit:w||void 0})}}}catch(j){y.e(j)}finally{y.f()}return console.log("【LIUMA_MULTI_LINE_ZULIU_MONEY_SUPER】处理完成，返回 ".concat(A.length," 个结果组")),A.length>0?A:null}},LIUMA_MULTI_FANGDUI_SUPER:{pattern:/^([0-9]{6}(?:[\-—–―－﹣﹘⸺⸻~～@#￥%…&!、\\/*,，.一－。＝=·\s+吊赶打各]+[0-9]{6})+)[\-—–―－﹣﹘⸺⸻~～@#￥%…&!、\\/*,，.一－。＝=·\s+吊赶打各]*(防对|防|组三)([0-9]+)$/,handler:function(e,t){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=e[1].split(a).map((function(e){return e.trim()})).filter((function(e){return 6===e.length})),s=e[3];return console.log(e[3]),i.length<2?null:[{methodId:o.LIUMA_ZUSAN,betNumbers:i.join(","),money:s}]}},LIUMA_MULTI_LINE_ZULIU_ZUSAN_KEYWORD:{pattern:/^([0-9]{6}(组六|组三)\d+(元|米|块)?[，,./\*\-\=。一－。·、\s]*\n?)+(总\d+(元|米|块)?[，,./\*\-\=。一－。·、\s]*)?$/m,handler:function(e,t){var n,r=t.split(/\r?\n/).map((function(e){return e.trim()})).filter(Boolean).filter((function(e){return!/^总\d+(元|米|块)?$/.test(e)})),s=new Map,l=Object(o["a"])(r);try{for(l.s();!(n=l.n()).done;){var u=n.value,c=u.match(/^([0-9]{6})(组六|组三)(\d+)(元|米|块)?[，,./\*\-\=。一－。·、\s]*$/);if(c&&6===c[1].length){var d=c[1],m=c[2],h=c[3],f=m+"_"+h;s.has(f)||s.set(f,[]),s.get(f).push(d)}}}catch(M){l.e(M)}finally{l.f()}var I,b=[],p=Object(o["a"])(s.entries());try{for(p.s();!(I=p.n()).done;){var _=Object(a["a"])(I.value,2),g=_[0],N=_[1],U=g.split("_"),v=Object(a["a"])(U,2),A=v[0],y=v[1];b.push({methodId:"组六"===A?i["METHOD_ID"].LIUMA_ZULIU:i["METHOD_ID"].LIUMA_ZUSAN,betNumbers:N.join(","),money:y})}}catch(M){p.e(M)}finally{p.f()}return b.length?b:null}},LIUMA_MULTI_LINE_NUMBERS_PLUS_MONEY:{pattern:/^([\s\S]+)$/m,handler:function(e,t){var r=t.split(/\n/).map((function(e){return e.trim()})).filter(Boolean);if(r.length<2)return null;var a=r.slice(0,-1),o="",i="",s=r[r.length-1],l=s.match(/^(\d{6})?\s*各(\d+)[+＋](\d+)$/);if(l)l[1]&&a.push(l[1]),o=l[2],i=l[3];else{var u=s.match(/^(\d+)[+＋](\d+)$/);if(!u)return null;o=u[1],i=u[2]}return a.length&&a.every((function(e){return/^\d{6}$/.test(e)}))?[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:a.join(","),money:o},{methodId:n("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:a.join(","),money:i}]:null}},LIUMA_MULTI_LINE_TWO_NUMBERS_CAIZHONG_EACH_SUPER_V2:{pattern:/^((?:\d{6}\s+\d{6}\s*\n?)+)((?:.*?(福彩|福|3D|3d|体彩|体|排|排三)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*各\d+\s*\n?)+)$/im,handler:function(e,t){var r,a=e[1].split(/\n/).map((function(e){return e.trim()})).filter(Boolean),i=[],s=Object(o["a"])(a);try{for(s.s();!(r=s.n()).done;){var l=r.value,u=l.match(/(\d{6})\s+(\d{6})/);u&&i.push(u[1],u[2])}}catch(f){s.e(f)}finally{s.f()}if(!i.length)return null;var c,d=[],m=/(福彩|福|3D|3d|体彩|体|排|排三)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*各(\d+)/g;while(null!==(c=m.exec(e[2]))){var h=1;/体彩|体|排|排三/.test(c[1])&&(h=2),d.push({lotteryId:h,money:c[2]})}return d.length?d.map((function(e){return{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:i.join(","),money:e.money,lotteryId:e.lotteryId}})):null}},LIUMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER:{pattern:/^([\s\S]+)$/m,handler:function(e,t){var r=/(福彩|福|3D|3d)/.test(t),i=/(体彩|体|排|排三)/.test(t);if(r||i)return null;var s,l=t.split(/\r?\n/).map((function(e){return e.trim()})).filter(Boolean),u=new Map,c=new Map,d=/^([0-9]{6})[\-—~～@#￥%…&!、\\/*,一－–.。＝=·\s\/]+(\d+)\++(\d+)[元米块]*$/,m=/^([0-9]{6})[\-—~～@#￥%…&!、\\/*,一－–.。＝=·\s\/]+(\d+)[元米块]*$/,h=[],f=Object(o["a"])(l);try{for(f.s();!(s=f.n()).done;){var I=s.value,b=I.match(d);if(b)h.push({line:I,type:"zuliuZusan",match:b});else{if(b=I.match(m),!b)return console.log('【LIUMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】行 "'.concat(I,'" 不匹配六码格式，返回null')),null;h.push({line:I,type:"zuliu",match:b})}}}catch(P){f.e(P)}finally{f.f()}console.log("【LIUMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】所有 ".concat(h.length," 行都是六码格式，继续处理"));for(var p=0,_=h;p<_.length;p++){var g=_[p],N=g.match,U=g.type;if("zuliuZusan"===U){var v=N[1],A=N[2],y=N[3];u.has(A)||u.set(A,[]),u.get(A).push(v),c.has(y)||c.set(y,[]),c.get(y).push(v)}else if("zuliu"===U){var M=N[1],E=N[2];u.has(E)||u.set(E,[]),u.get(E).push(M)}}var D,O=[],S=Object(o["a"])(u.entries());try{for(S.s();!(D=S.n()).done;){var T=Object(a["a"])(D.value,2),L=T[0],w=T[1];O.push({methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:w.join(","),money:L})}}catch(P){S.e(P)}finally{S.f()}var j,R=Object(o["a"])(c.entries());try{for(R.s();!(j=R.n()).done;){var $=Object(a["a"])(j.value,2),Z=$[0],H=$[1];O.push({methodId:n("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:H.join(","),money:Z})}}catch(P){R.e(P)}finally{R.f()}return console.log("【LIUMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】处理完成，返回 ".concat(O.length," 个结果组")),O.length?O:null}},LIUMA_MONEY_UNIT_FANGDUI_SUPER:{pattern:/^(\d{6})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:元|米|块)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(防|防对|组三)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*$/,handler:function(e,t){var n=e[1],r=e[2];e[3];return 6!==n.length?null:[{methodId:i["METHOD_ID"].LIUMA_ZUSAN,betNumbers:n,money:r}]}},WUMA_TO_LIUMA_MULTI_TRANSFORM:{pattern:/^((?:\d{5}[\-—~～@#￥%…&!、\\/*,，.＝=·\s]+){1,}\d{5})(各)?(转|套)[\s]*([0-9]+)\+([0-9]+)$/,handler:function(e,t){var a=n("f9d6"),o=a.SUPER_SEPARATORS,i=e[1],s=e[4],l=e[5],u=i.split(o).filter((function(e){return 5===e.length})),c=[];return u.forEach((function(e){var t=e.split(""),n="0123456789".split("").filter((function(e){return!t.includes(e)})),a=n.map((function(t){return e+t})).sort(),o=a.filter((function(e){return!c.includes(e)}));c.push.apply(c,Object(r["a"])(o))})),[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:c.join(","),money:s},{methodId:n("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:c.join(","),money:l}]}}};Object.keys(s).forEach((function(e){var t=s[e].pattern;"string"===typeof t&&t.endsWith("$")?s[e].pattern=t.replace(/\$$/,"[，,./*-=。·、s]*$"):t instanceof RegExp&&t.source.endsWith("$")&&(s[e].pattern=new RegExp(t.source.replace(/\$$/,"[，,./*-=。·、s]*$")))}))},7883:function(e,t,n){"use strict";n("db21")},"7b57":function(e,t,n){"use strict";n.r(t),n.d(t,"ALL_PATTERNS",(function(){return g})),n.d(t,"mergeGroups",(function(){return N})),n.d(t,"parseBetLine",(function(){return U})),n.d(t,"parseBetContent",(function(){return v}));var r=n("2909"),a=n("b85c"),o=n("5530"),i=(n("99af"),n("4de4"),n("0481"),n("a15b"),n("d81d"),n("14d9"),n("4069"),n("d3b7"),n("07ac"),n("ac1f"),n("5319"),n("1276"),n("498a"),n("0643"),n("2382"),n("4e3e"),n("a573"),n("159b"),n("f9d6"));n.d(t,"BASE_PATTERNS",(function(){return i["BASE_PATTERNS"]}));var s=n("ce3b");n.d(t,"SINGLE_CODE_PATTERNS",(function(){return s["SINGLE_CODE_PATTERNS"]}));var l=n("5c5d");n.d(t,"TWO_CODE_PATTERNS",(function(){return l["TWO_CODE_PATTERNS"]}));var u=n("9608");n.d(t,"THREE_CODE_PATTERNS",(function(){return u["THREE_CODE_PATTERNS"]}));var c=n("172b");n.d(t,"FOUR_CODE_PATTERNS",(function(){return c["FOUR_CODE_PATTERNS"]}));var d=n("47fb");n.d(t,"FIVE_CODE_PATTERNS",(function(){return d["FIVE_CODE_PATTERNS"]}));var m=n("5f85");n.d(t,"SIX_CODE_PATTERNS",(function(){return m["SIX_CODE_PATTERNS"]}));var h=n("2e8d");n.d(t,"SEVEN_CODE_PATTERNS",(function(){return h["SEVEN_CODE_PATTERNS"]}));var f=n("22f7");n.d(t,"EIGHT_CODE_PATTERNS",(function(){return f["EIGHT_CODE_PATTERNS"]}));var I=n("3506");n.d(t,"NINE_CODE_PATTERNS",(function(){return I["NINE_CODE_PATTERNS"]}));var b=n("f398"),p=n("8de0"),_=n("98b6");n.d(t,"cleanNumberString",(function(){return i["cleanNumberString"]})),n.d(t,"parseMoney",(function(){return i["parseMoney"]})),n.d(t,"checkLotteryType",(function(){return i["checkLotteryType"]})),n.d(t,"parseLotteryType",(function(){return i["parseLotteryType"]}));var g=Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])({},_["KUADU_PATTERNS"]),p["DANTUO_PATTERNS"]),s["SINGLE_CODE_PATTERNS"]),l["TWO_CODE_PATTERNS"]),u["THREE_CODE_PATTERNS"]),c["FOUR_CODE_PATTERNS"]),d["FIVE_CODE_PATTERNS"]),m["SIX_CODE_PATTERNS"]),h["SEVEN_CODE_PATTERNS"]),f["EIGHT_CODE_PATTERNS"]),I["NINE_CODE_PATTERNS"]),b["SINGLE_DOUBLE_PATTERNS"]);function N(e){var t={};return e.forEach((function(e){var n="".concat(e.methodId||"","_").concat(e.money||"");t[n]?t[n].betNumbers.push(e.betNumbers):t[n]=Object(o["a"])(Object(o["a"])({},e),{},{betNumbers:[e.betNumbers],methodId:e.methodId,danshuang:e.danshuang,daxiao:e.daxiao,dingwei:e.dingwei,hezhi:e.hezhi,money:e.money||""})})),Object.values(t).map((function(e){var t=e.betNumbers.filter(Boolean).map((function(e){return e.split(/[^0-9]+/)})).flat();return Object(o["a"])(Object(o["a"])({},e),{},{betNumbers:t.join(","),methodId:e.methodId,money:e.money||""})}))}function U(e){if(e=e.trim(),!e)return null;var t=Object(i["parseLotteryType"])(e);e=e.replace(/(福|福彩|3D|福3D|排|排三|体|体彩|福排|福体)/g,"").trim();for(var n=0,r=Object.values(g);n<r.length;n++){var a=r[n],o=a.pattern.exec(e);if(o){var s=a.handler(o,e);if(s)return s.forEach((function(e){e.lotteryId||(e.lotteryId=t?t.id:1)})),s}}return null}function v(e){var t,n=e.split(/[\n\r]+/).filter(Boolean),o=[],i=Object(a["a"])(n);try{for(i.s();!(t=i.n()).done;){var s=t.value,l=U(s);l&&o.push.apply(o,Object(r["a"])(l))}}catch(u){i.e(u)}finally{i.f()}return o}},"832b":function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("el-dialog",{staticClass:"modern-bet-dialog",staticStyle:{height:"108%",top:"-55px"},attrs:{title:e.dialogTitle,visible:e.dialogVisible,width:"600px","append-to-body":"","close-on-click-modal":!1},on:{"update:visible":function(t){e.dialogVisible=t}}},[n("el-form",{ref:"form",staticClass:"modern-bet-form",attrs:{model:e.formData,rules:e.rules,"label-width":"80px"}},[n("div",{staticClass:"info-card"},[n("div",{staticClass:"info-row"},[n("div",{staticClass:"info-group"},[n("div",{staticClass:"info-label"},[n("i",{staticClass:"el-icon-s-operation"}),n("span",[e._v("福体开关")])]),n("el-switch",{staticClass:"lottery-switch",attrs:{"active-value":"all","inactive-value":"tc","active-text":"全部彩种","inactive-text":"仅体彩",size:"small"},on:{change:e.onFutiSwitchChange},model:{value:e.futiSwitch,callback:function(t){e.futiSwitch=t},expression:"futiSwitch"}})],1)]),n("div",{staticClass:"info-row"},[n("div",{staticClass:"info-group"},[n("div",{staticClass:"info-label"},[n("i",{staticClass:"el-icon-user"}),n("span",[e._v("当前用户")])]),n("el-input",{staticClass:"compact-input",staticStyle:{width:"100px"},attrs:{disabled:"",placeholder:"未获取"},model:{value:e.currentUser.name,callback:function(t){e.$set(e.currentUser,"name",t)},expression:"currentUser.name"}})],1),n("div",{staticClass:"info-group"},[n("div",{staticClass:"info-label"},[n("i",{staticClass:"el-icon-switch-button"}),n("span",[e._v("切换用户")])]),n("el-select",{staticClass:"compact-select",staticStyle:{width:"120px"},attrs:{placeholder:"请选择用户"},model:{value:e.selectedUserId,callback:function(t){e.selectedUserId=t},expression:"selectedUserId"}},e._l(e.userList,(function(e){return n("el-option",{key:e.userId,attrs:{label:e.name,value:e.userId}})})),1),e.row?e._e():n("el-button",{staticClass:"compact-btn",attrs:{type:"primary",disabled:!e.selectedUserId},on:{click:e.switchUser}},[e._v("切换")])],1)]),n("div",{staticClass:"info-row"},[n("div",{staticClass:"info-group"},[n("div",{staticClass:"info-label"},[n("i",{staticClass:"el-icon-document"}),n("span",[e._v("流水号")])]),n("el-input",{staticClass:"compact-input",staticStyle:{width:"120px"},attrs:{placeholder:"流水号"},model:{value:e.serialNumber,callback:function(t){e.serialNumber=e._n(t)},expression:"serialNumber"}}),n("el-button",{staticClass:"compact-btn",attrs:{type:"success",loading:e.generateLoading},on:{click:e.generateNewSerialNumber}},[n("i",{staticClass:"el-icon-refresh"}),e._v(" 生成新流水号 ")]),n("el-button",{staticClass:"compact-btn",attrs:{type:"primary"},on:{click:e.showFormatDialog}},[n("i",{staticClass:"el-icon-s-operation"}),e._v(" 格式转换工具 ")]),e.showStatisticsButton?n("el-button",{staticClass:"compact-btn",attrs:{type:"success"},on:{click:e.showStatistics}},[n("i",{staticClass:"el-icon-data-analysis"}),e._v(" 下注统计 ")]):e._e()],1)]),n("div",{staticStyle:{display:"none"}},[n("el-input",{model:{value:e.formData.fc3dIssueNumber,callback:function(t){e.$set(e.formData,"fc3dIssueNumber",t)},expression:"formData.fc3dIssueNumber"}}),n("el-input",{model:{value:e.formData.tcIssueNumber,callback:function(t){e.$set(e.formData,"tcIssueNumber",t)},expression:"formData.tcIssueNumber"}})],1)]),n("div",{staticClass:"recognition-section"},[n("el-form-item",{attrs:{label:"号码识别",prop:"shibie"}},[n("div",{staticClass:"recognition-wrapper"},[n("div",{staticClass:"recognition-header"},[n("i",{staticClass:"el-icon-view"}),e.isNumberCountExceeded?e.isFirstNumberThreeDigits(e.formData.shibie)?n("span",{staticStyle:{color:"#67c23a"}},[e._v("首组为三位数，不限制组数 ("+e._s(e.numberCount)+"组)")]):n("span",{staticStyle:{color:"#f56c6c"}},[e._v("单次识别不可超过30组，你现在是"+e._s(e.numberCount)+"组")]):n("span",[e._v("智能识别")]),n("el-button",{attrs:{size:"mini",type:"primary"},on:{click:e.showBetFormatDialog}},[e._v("玩法格式")])],1),n("el-input",{class:["recognition-textarea",{"number-count-exceeded":e.isNumberCountExceeded}],attrs:{type:"textarea",rows:4,placeholder:"请将号码输入此处，识别后自动生成下注号码",size:"small",resize:"vertical"},on:{input:e.handleNumberRecognition},model:{value:e.formData.shibie,callback:function(t){e.$set(e.formData,"shibie",t)},expression:"formData.shibie"}})],1)])],1),n("div",{staticClass:"bet-groups-list-wrapper"},[n("div",{staticClass:"bet-groups-list"},e._l(e.formData.betGroups,(function(t,r){return n("div",{key:r,class:["bet-group-card",{"bet-group-even":r%2===1}]},[n("div",{staticStyle:{display:"flex","align-items":"center",gap:"8px","margin-bottom":"8px"}},[n("el-form-item",{staticStyle:{flex:"1","margin-bottom":"0"},attrs:{label:"玩法名称",prop:"betGroups."+r+".methodId"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",remote:"","remote-method":e.filterMethod,loading:e.loadingGameMethods,clearable:"",placeholder:"请选择玩法名称",size:"small"},on:{change:function(n){return e.onMethodChange(t)}},model:{value:t.methodId,callback:function(n){e.$set(t,"methodId",n)},expression:"group.methodId"}},e._l(e.filteredGameMethods,(function(t){return n("el-option",{key:t.methodId,attrs:{label:t.methodName,value:t.methodId}},[e._v(" "+e._s(t.methodName)+" ")])})),1)],1),n("el-form-item",{staticStyle:{width:"200px","margin-bottom":"0"},attrs:{label:"金额",prop:"betGroups."+r+".money"}},[n("el-input",{attrs:{placeholder:"请输入金额",size:"small"},on:{input:e.updateTotals},model:{value:t.money,callback:function(n){e.$set(t,"money",n)},expression:"group.money"}})],1),n("el-button",{attrs:{type:"danger",icon:"el-icon-delete",circle:"",size:"mini"},on:{click:function(t){return e.removeBetGroup(r)}}})],1),29===t.methodId||30===t.methodId||2===t.methodId||[21,22,23,24,25,26,27,37,38,39,40,41,42,43].includes(t.methodId)?n("div",{staticStyle:{"margin-left":"80px","margin-bottom":"8px"}},[29===t.methodId?[n("div",{staticClass:"option-group"},[n("span",{staticClass:"option-label",staticStyle:{"font-size":"16px","font-weight":"600"}},[e._v("单双：")]),n("el-radio-group",{attrs:{size:"medium"},model:{value:t.danshuang,callback:function(n){e.$set(t,"danshuang",n)},expression:"group.danshuang"}},[n("el-radio-button",{attrs:{label:200}},[e._v("单")]),n("el-radio-button",{attrs:{label:201}},[e._v("双")])],1)],1)]:30===t.methodId?[n("div",{staticClass:"option-group"},[n("span",{staticClass:"option-label",staticStyle:{"font-size":"16px","font-weight":"600"}},[e._v("大小：")]),n("el-radio-group",{attrs:{size:"medium"},model:{value:t.daxiao,callback:function(n){e.$set(t,"daxiao",n)},expression:"group.daxiao"}},[n("el-radio-button",{attrs:{label:300}},[e._v("大")]),n("el-radio-button",{attrs:{label:301}},[e._v("小")])],1)],1)]:2===t.methodId?[n("div",{staticClass:"option-group"},[n("span",{staticClass:"option-label",staticStyle:{"font-size":"16px","font-weight":"600"}},[e._v("定位：")]),n("el-radio-group",{attrs:{size:"medium"},model:{value:t.dingwei,callback:function(n){e.$set(t,"dingwei",n)},expression:"group.dingwei"}},[n("el-radio-button",{attrs:{label:100}},[e._v("百位")]),n("el-radio-button",{attrs:{label:101}},[e._v("十位")]),n("el-radio-button",{attrs:{label:102}},[e._v("个位")])],1)],1)]:[21,22,23,24,25,26,27,37,38,39,40,41,42,43].includes(t.methodId)?[n("div",{staticClass:"option-group"},[n("span",{staticClass:"option-label",staticStyle:{"font-size":"16px","font-weight":"600"}},[e._v("和值：")]),n("el-radio-group",{attrs:{size:"medium"},model:{value:t.hezhi,callback:function(n){e.$set(t,"hezhi",n)},expression:"group.hezhi"}},e._l(e.getHezhiOptions(t.methodId),(function(t){return n("el-radio-button",{key:t,attrs:{label:t}},[e._v("和值"+e._s(t))])})),1)],1)]:e._e()],2):e._e(),e.isSpecialMethod(t.methodId)?e._e():n("el-form-item",{staticStyle:{"margin-bottom":"8px"},attrs:{label:"下注号码",prop:"betGroups."+r+".betNumbers"}},[n("el-input",{attrs:{value:e.getDisplayBetNumbers(t),type:"textarea",rows:3,placeholder:"请输入下注号码"},on:{input:function(t){return e.onBetNumbersInput(t,r)}}})],1),n("el-form-item",{staticStyle:{"margin-bottom":"8px"},attrs:{label:"彩种类型"}},[n("div",{staticStyle:{display:"flex","align-items":"center"}},[n("el-radio-group",{staticStyle:{"margin-right":"16px","flex-shrink":"0"},attrs:{size:"small",fill:2===t.lotteryId?"#1890ff":"#ff4949"},model:{value:t.lotteryId,callback:function(n){e.$set(t,"lotteryId",n)},expression:"group.lotteryId"}},[n("el-radio-button",{staticStyle:{"margin-right":"6px"},attrs:{label:1,"data-lottery":"fc3d"}},[e._v("福彩3D")]),n("el-radio-button",{staticStyle:{"margin-right":"0"},attrs:{label:2,"data-lottery":"tc"}},[e._v("体彩排三")])],1),n("div",{staticClass:"group-summary"},[n("div",{staticClass:"summary-item amount"},[n("div",{staticClass:"summary-icon"},[n("i",{staticClass:"el-icon-wallet"})]),n("div",{staticClass:"summary-content"},[n("div",{staticClass:"summary-label"},[e._v("总额")]),n("div",{staticClass:"summary-value"},[e._v("￥"+e._s(e.getGroupAmount(t)))])])]),n("div",{staticClass:"summary-item bets"},[n("div",{staticClass:"summary-icon"},[n("i",{staticClass:"el-icon-tickets"})]),n("div",{staticClass:"summary-content"},[n("div",{staticClass:"summary-label"},[e._v("投注数")]),n("div",{staticClass:"summary-value"},[e._v(e._s(e.getGroupBets(t))+"注")])])])])],1)])],1)})),0),n("button",{staticClass:"bet-group-add-btn",attrs:{title:"添加投注组合"},on:{click:e.addBetGroup}})]),n("el-row",{attrs:{gutter:20}},[n("el-col",{attrs:{span:12}},[n("el-statistic",{attrs:{title:"下注总额",value:e.totalAmount,size:"small"}},[n("template",{slot:"prefix"},[e._v("￥")])],2)],1),n("el-col",{attrs:{span:12}},[n("el-statistic",{attrs:{title:"总投注数",value:e.totalBets,size:"small"}},[n("template",{slot:"suffix"},[e._v("注")])],2)],1)],1),"修改下注管理"===e.dialogTitle?n("el-form-item",{attrs:{label:"是否结算",prop:"jiesuan"}},[n("el-switch",{attrs:{"active-value":1,"inactive-value":0,"active-text":"已结算","inactive-text":"未结算",size:"small"},model:{value:e.formData.jiesuan,callback:function(t){e.$set(e.formData,"jiesuan",t)},expression:"formData.jiesuan"}})],1):e._e()],1),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"center",width:"100%"}},[n("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.submitForm}},[e._v("确 定")]),n("el-button",{attrs:{size:"small"},on:{click:e.close}},[e._v("取 消")])],1)])],1),n("el-dialog",{staticClass:"format-dialog",attrs:{title:"格式转换工具",visible:e.formatDialogVisible,width:"650px","append-to-body":"","close-on-click-modal":!1},on:{"update:visible":function(t){e.formatDialogVisible=t}}},[n("div",{staticClass:"format-dialog-content"},[n("el-tabs",{staticClass:"format-tabs",attrs:{type:"card"},model:{value:e.formatTab,callback:function(t){e.formatTab=t},expression:"formatTab"}},[n("el-tab-pane",{attrs:{name:"unified"}},[n("span",{attrs:{slot:"label"},slot:"label"},[n("i",{staticClass:"el-icon-edit"}),e._v(" 字符转换 ")]),n("div",{staticClass:"tab-content"},[n("div",{staticClass:"input-section"},[n("div",{staticClass:"section-header"},[n("i",{staticClass:"el-icon-upload2"}),n("span",[e._v("输入内容")])]),n("el-input",{staticClass:"format-input",attrs:{type:"textarea",rows:5,placeholder:"智能识别并转换：汉字金额(如二十)、逗号、句号、冒号、下划线、加号等转为空格&#10;示例：123,456。。789：：：012___345➕678二十元"},on:{input:e.handleUnifiedFormatInput},model:{value:e.unifiedFormatInput,callback:function(t){e.unifiedFormatInput=t},expression:"unifiedFormatInput"}})],1),n("div",{staticClass:"arrow-section"},[n("i",{staticClass:"el-icon-bottom"})]),n("div",{staticClass:"output-section"},[n("div",{staticClass:"section-header"},[n("i",{staticClass:"el-icon-download"}),n("span",[e._v("转换结果")])]),n("el-input",{staticClass:"format-output",attrs:{type:"textarea",rows:5,value:e.unifiedFormatOutput,readonly:"",placeholder:"转换结果将在这里显示..."}})],1),n("div",{staticClass:"button-section"},[n("el-button",{staticClass:"action-button",attrs:{type:"primary",icon:"el-icon-check",disabled:!e.unifiedFormatOutput},on:{click:e.insertUnifiedFormatResult}},[e._v(" 插入到号码识别 ")]),n("el-button",{staticClass:"cancel-button",attrs:{icon:"el-icon-close"},on:{click:function(t){e.formatDialogVisible=!1}}},[e._v(" 关闭 ")])],1)])]),n("el-tab-pane",{attrs:{name:"chain"}},[n("span",{attrs:{slot:"label"},slot:"label"},[n("i",{staticClass:"el-icon-s-grid"}),e._v(" 链子分类 ")]),n("div",{staticClass:"tab-content"},[n("div",{staticClass:"input-section"},[n("div",{staticClass:"section-header"},[n("i",{staticClass:"el-icon-upload2"}),n("span",[e._v("输入内容")])]),n("el-input",{staticClass:"format-input",attrs:{type:"textarea",rows:5,placeholder:"按号码长度自动分类到不同行\n示例：123-1234-568-56789-503-9012-12345678"},on:{input:e.handleChainFormatInput},model:{value:e.chainFormatInput,callback:function(t){e.chainFormatInput=t},expression:"chainFormatInput"}})],1),n("div",{staticClass:"arrow-section"},[n("i",{staticClass:"el-icon-bottom"})]),n("div",{staticClass:"output-section"},[n("div",{staticClass:"section-header"},[n("i",{staticClass:"el-icon-download"}),n("span",[e._v("分类结果")])]),n("el-input",{staticClass:"format-output",attrs:{type:"textarea",rows:6,value:e.chainFormatOutput,readonly:"",placeholder:"分类结果将在这里显示..."}})],1),n("div",{staticClass:"button-section"},[n("el-button",{staticClass:"action-button",attrs:{type:"primary",icon:"el-icon-check",disabled:!e.chainFormatOutput},on:{click:e.insertChainFormatResult}},[e._v(" 插入到号码识别 ")]),n("el-button",{staticClass:"cancel-button",attrs:{icon:"el-icon-close"},on:{click:function(t){e.formatDialogVisible=!1}}},[e._v(" 关闭 ")])],1)])]),n("el-tab-pane",{attrs:{name:"amount"}},[n("span",{attrs:{slot:"label"},slot:"label"},[n("i",{staticClass:"el-icon-money"}),e._v(" 金额整合 ")]),n("div",{staticClass:"tab-content"},[n("div",{staticClass:"input-section"},[n("div",{staticClass:"section-header"},[n("i",{staticClass:"el-icon-upload2"}),n("span",[e._v("输入内容")])]),n("el-input",{staticClass:"format-input",attrs:{type:"textarea",rows:6,placeholder:"将相同金额且相同位数的号码合并到一行\n  支持每行一个或一行内空格分隔多个\n  示例：57-50  59-50  79-50  88-100\n  结果： 57-59-79-50\n        88-100"},on:{input:e.handleAmountFormatInput},model:{value:e.amountFormatInput,callback:function(t){e.amountFormatInput=t},expression:"amountFormatInput"}})],1),n("div",{staticClass:"arrow-section"},[n("i",{staticClass:"el-icon-bottom"})]),n("div",{staticClass:"output-section"},[n("div",{staticClass:"section-header"},[n("i",{staticClass:"el-icon-download"}),n("span",[e._v("整合结果")])]),n("el-input",{staticClass:"format-output",attrs:{type:"textarea",rows:6,value:e.amountFormatOutput,readonly:"",placeholder:"整合结果将在这里显示..."}})],1),n("div",{staticClass:"button-section"},[n("el-button",{staticClass:"action-button",attrs:{type:"primary",icon:"el-icon-check",disabled:!e.amountFormatOutput},on:{click:e.insertAmountFormatResult}},[e._v(" 插入到号码识别 ")]),n("el-button",{staticClass:"cancel-button",attrs:{icon:"el-icon-close"},on:{click:function(t){e.formatDialogVisible=!1}}},[e._v(" 关闭 ")])],1)])]),n("el-tab-pane",{attrs:{name:"remove"}},[n("span",{attrs:{slot:"label"},slot:"label"},[n("i",{staticClass:"el-icon-delete"}),e._v(" 去除分隔符 ")]),n("div",{staticClass:"tab-content"},[n("div",{staticClass:"input-section"},[n("div",{staticClass:"section-header"},[n("i",{staticClass:"el-icon-upload2"}),n("span",[e._v("输入内容")])]),n("el-input",{staticClass:"format-input",attrs:{type:"textarea",rows:6,placeholder:"去除所有分隔符和英文字母，只保留汉字、数字和换行\n示例：123,456。。789：：：abc___def➕ghi二十元ABC\n结果：123456789二十元"},on:{input:e.handleRemoveFormatInput},model:{value:e.removeFormatInput,callback:function(t){e.removeFormatInput=t},expression:"removeFormatInput"}})],1),n("div",{staticClass:"arrow-section"},[n("i",{staticClass:"el-icon-bottom"})]),n("div",{staticClass:"output-section"},[n("div",{staticClass:"section-header"},[n("i",{staticClass:"el-icon-download"}),n("span",[e._v("清理结果")])]),n("el-input",{staticClass:"format-output",attrs:{type:"textarea",rows:6,value:e.removeFormatOutput,readonly:"",placeholder:"清理结果将在这里显示..."}})],1),n("div",{staticClass:"button-section"},[n("el-button",{staticClass:"action-button",attrs:{type:"primary",icon:"el-icon-check",disabled:!e.removeFormatOutput},on:{click:e.insertRemoveFormatResult}},[e._v(" 插入到号码识别 ")]),n("el-button",{staticClass:"cancel-button",attrs:{icon:"el-icon-close"},on:{click:function(t){e.formatDialogVisible=!1}}},[e._v(" 关闭 ")])],1)])])],1)],1)]),n("bet-statistics",{attrs:{visible:e.statisticsVisible},on:{"update:visible":function(t){e.statisticsVisible=t}}}),n("bet-format-dialog",{ref:"betFormatDialog"})],1)},a=[],o=n("ade3"),i=n("b85c"),s=n("3835"),l=n("5530"),u=n("c14f"),c=n("1da1"),d=(n("99af"),n("4de4"),n("c740"),n("caad"),n("a15b"),n("d81d"),n("14d9"),n("13d5"),n("fb6a"),n("4e82"),n("a434"),n("e9c4"),n("a9e3"),n("b64b"),n("d3b7"),n("07ac"),n("ac1f"),n("00b4"),n("25f0"),n("2532"),n("3ca3"),n("466d"),n("5319"),n("1276"),n("2ca0"),n("498a"),n("0643"),n("2382"),n("4e3e"),n("a573"),n("9d4a"),n("9a9a"),n("159b"),n("ddb0"),n("b2f0")),m=n("b775"),h=n("f97d"),f=n("35d4"),I=n("0068"),b=n("0901"),p=n("c0c7"),_=n("6455"),g=n("9b7b"),N={name:"BetDialog",components:{BetStatistics:_["default"],BetFormatDialog:g["default"]},props:{visible:{type:Boolean,default:!1},title:{type:String,default:"新增下注"},row:{type:Object,default:null}},data:function(){return{dialogVisible:!1,dialogTitle:"",totalAmount:0,totalBets:0,pendingRequests:[],gameMethodsData:[],filteredGameMethods:[],loadingGameMethods:!1,formData:{fc3dIssueNumber:"",tcIssueNumber:"",shibie:"",betGroups:[{methodId:void 0,money:"",betNumbers:"",danshuang:null,daxiao:null,dingwei:null,hezhi:null,lotteryId:1}]},numberCount:0,isNumberCountExceeded:!1,rules:{betGroups:{methodId:[{required:!0,message:"请选择玩法",trigger:"change"}],money:[{required:!0,message:"请输入金额",trigger:"blur"}]}},localForm:{fc3dIssueNumber:"",tcIssueNumber:"",shibie:"",betGroups:[]},currentUser:{name:""},userList:[],selectedUserId:null,futiSwitch:sessionStorage.getItem("futiSwitch")||"all",formatDialogVisible:!1,formatInput:"",formatOutput:"",formatTab:"unified",colonFormatInput:"",colonFormatOutput:"",moneyFormatInput:"",moneyFormatOutput:"",underscoreFormatInput:"",underscoreFormatOutput:"",unifiedFormatInput:"",unifiedFormatOutput:"",chainFormatInput:"",chainFormatOutput:"",amountFormatInput:"",amountFormatOutput:"",removeFormatInput:"",removeFormatOutput:"",serialNumber:1,maxSerialNumber:1,generateLoading:!1,originalUserId:null,originalSerialNumber:null,statisticsVisible:!1,hasStatisticsPermission:!1}},computed:{showStatisticsButton:function(){return this.hasStatisticsPermission}},watch:{visible:function(e){if(this.dialogVisible=e,e){if(this.checkPlayerStatus(),this.getAllUsers(),this.checkStatisticsPermission(),this.row&&this.row.userId)this.selectedUserId=this.row.userId;else{var t=sessionStorage.getItem("User_Id");this.selectedUserId=t?Number(t):null}this.initSerialNumber(),this.row&&this.initFormData(),this.onFutiSwitchChange(this.futiSwitch)}},dialogVisible:function(e){this.$emit("update:visible",e),e||this.resetForm()},selectedUserId:{handler:function(e,t){this.row&&this.row.betId&&null!==this.originalUserId&&void 0!==t&&this.handleUserIdChange(e,t)},immediate:!1},formatDialogVisible:function(e){e||(this.formatInput="",this.formatOutput="",this.colonFormatInput="",this.colonFormatOutput="",this.moneyFormatInput="",this.moneyFormatOutput="",this.underscoreFormatInput="",this.underscoreFormatOutput="",this.unifiedFormatInput="",this.unifiedFormatOutput="",this.chainFormatInput="",this.chainFormatOutput="",this.amountFormatInput="",this.amountFormatOutput="",this.removeFormatInput="",this.removeFormatOutput="")}},created:function(){this.getGameMethods()},methods:{showBetFormatDialog:function(){this.$refs.betFormatDialog.show()},checkPlayerStatus:function(){var e=this;return Object(c["a"])(Object(u["a"])().m((function t(){var n,r;return Object(u["a"])().w((function(t){while(1)switch(t.n){case 0:return t.p=0,t.n=1,Object(b["b"])();case 1:if(n=t.v,500!==n.code||!n.needCreatePlayer){t.n=2;break}return e.$confirm(n.msg+" 是否立即前往创建？","提示",{confirmButtonText:"前往创建",cancelButtonText:"取消",type:"warning"}).then((function(){e.dialogVisible=!1,e.$emit("update:visible",!1),e.$router.push("/game/customer")})).catch((function(){e.dialogVisible=!1,e.$emit("update:visible",!1)})),t.a(2);case 2:200===n.code&&n.hasPlayers&&(n.defaultUserId,e.initializeDialog()),t.n=4;break;case 3:t.p=3,r=t.v,console.error("检查玩家状态失败:",r),e.$message.error("检查玩家状态失败，请重试"),e.dialogVisible=!1,e.$emit("update:visible",!1);case 4:return t.a(2)}}),t,null,[[0,3]])})))()},initializeDialog:function(){this.getCurrentIssueNumber(),this.getGameMethods(),this.getCurrentUser()},getGameMethods:function(){var e=this;return this.loadingGameMethods=!0,Object(m["a"])({url:"/game/method/list",method:"get",params:{pageNum:1,pageSize:100}}).then((function(t){var n=[];if(200===t.code)if(Array.isArray(t.data))n=t.data;else if(t.rows&&Array.isArray(t.rows))n=t.rows;else if("string"===typeof t.data)try{var r=JSON.parse(t.data);n=Array.isArray(r)?r:[]}catch(a){n=[]}e.gameMethodsData=n,e.filteredGameMethods=n,e.loadingGameMethods=!1})).catch((function(){e.gameMethodsData=[],e.filteredGameMethods=[],e.loadingGameMethods=!1}))},show:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"新增下注";this.dialogTitle=e,this.dialogVisible=!0},close:function(){this.dialogVisible=!1,this.$emit("update:visible",!1),this.$emit("cancel")},resetForm:function(){this.formData={fc3dIssueNumber:"",tcIssueNumber:"",shibie:"",betGroups:[{methodId:void 0,money:"",betNumbers:"",danshuang:null,daxiao:null,dingwei:null,hezhi:null,lotteryId:1}]},this.totalAmount=0,this.totalBets=0,this.numberCount=0,this.isNumberCountExceeded=!1},addBetGroup:function(){var e=this,t={methodId:void 0,money:"",betNumbers:"",danshuang:null,daxiao:null,dingwei:null,hezhi:null,lotteryId:"tc"===this.futiSwitch?2:1};this.formData.betGroups||(this.formData.betGroups=[]),this.formData.betGroups.push(Object(l["a"])({},t)),this.$nextTick((function(){e.updateTotals()}))},removeBetGroup:function(e){var t=this;this.formData.betGroups.length<=1?this.formData.betGroups=[{methodId:void 0,money:"",betNumbers:"",danshuang:null,daxiao:null,dingwei:null,hezhi:null,lotteryId:1}]:this.formData.betGroups.splice(e,1),this.$nextTick((function(){t.updateTotals(),t.$forceUpdate()}))},updateTotals:function(){var e=this;this.totalAmount=this.formData.betGroups.reduce((function(t,n){return t+Number(e.getGroupAmount(n))}),0),this.totalBets=this.formData.betGroups.reduce((function(t,n){return t+Number(e.getGroupBets(n))}),0)},getHezhiOptions:function(e){var t={21:[7,20],22:[8,19],23:[9,18],24:[10,17],25:[11,16],26:[12,15],27:[13,14],37:[0,27],38:[1,26],39:[2,25],40:[3,24],41:[4,23],42:[5,22],43:[6,21]};return t[e]||[]},onMethodChange:function(e){var t=this.formData.betGroups.findIndex((function(t){return t===e}));if(t>-1){var n=Object(l["a"])(Object(l["a"])({},e),{},{danshuang:null,daxiao:null,dingwei:null,hezhi:null});if(29===n.methodId)n.danshuang=200;else if(30===n.methodId)n.daxiao=300;else if(2===n.methodId)n.dingwei=100;else if([21,22,23,24,25,26,27,37,38,39,40,41,42,43].includes(n.methodId)){var r=this.getHezhiOptions(n.methodId);r&&r.length>0&&(n.hezhi=r[0])}this.$set(this.formData.betGroups,t,n),this.$forceUpdate()}},getCurrentIssueNumber:function(){var e=this,t=this.$loading({lock:!0,text:"正在获取期号...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)",customClass:"bet-dialog-loading"});Promise.all([Object(I["c"])(1).catch((function(e){return console.error("获取福彩3D期号失败:",e),{code:500,msg:"获取失败"}})),Object(I["c"])(2).catch((function(e){return console.error("获取体彩排三期号失败:",e),{code:500,msg:"获取失败"}}))]).then((function(t){var n=Object(s["a"])(t,2),r=n[0],a=n[1];200===r.code?e.formData.fc3dIssueNumber=r.msg:(console.error("获取福彩3D期号失败:",r),e.$message.error("获取福彩3D期号失败")),200===a.code?e.formData.tcIssueNumber=a.msg:(console.error("获取体彩排三期号失败:",a),e.$message.error("获取体彩排三期号失败"))})).finally((function(){t.close()}))},calculateNumberCount:function(e){if(!e||!e.trim())return 0;var t=e.trim(),n=/\d{2,9}/g,r=t.match(n);if(!r)return 0;var a,o=t.split(/[\r\n]+/).filter((function(e){return e.trim()})),s=0,l=Object(i["a"])(o);try{for(l.s();!(a=l.n()).done;){var u=a.value,c=u.trim();if(c){var d=c.match(n);if(d){var m=/[元块米钱赶组直吊放防各]/,h=m.test(c),f=/[+＋\/]/.test(c);s+=h||f?Math.max(0,d.length-1):d.length}}}}catch(I){l.e(I)}finally{l.f()}return s},smartPreprocessInput:function(e){if(!e)return e;var t=e.split(/[—\-\n\r]+/).map((function(e){return e.trim()})).filter(Boolean);if(t.length>1){var n=/(赶|组|直|吊|放|防|元|各)[^\d]*\d+.*$/,r=t[t.length-1];if(n.test(r))return t.slice(0,-1).join("—")+"—"+r}return e},isFirstNumberThreeDigits:function(e){if(!e||!e.trim())return!1;var t=e.trim(),n=t.match(/\d{2,9}/);return!!n&&3===n[0].length},handleNumberRecognition:function(e){var t=this;this.numberCount=this.calculateNumberCount(e);var n=this.isFirstNumberThreeDigits(e);if(this.isNumberCountExceeded=!n&&this.numberCount>30,e){if(!this.isNumberCountExceeded){var r=Object(d["handleNumberRecognition"])(e);if(!r||!r.groups||!r.groups.some((function(e){return e.methodId}))){var a,o=e.split(/[\r\n]+/).map((function(e){return e.trim()})).filter(Boolean),s=[],u=Object(i["a"])(o);try{for(u.s();!(a=u.n()).done;){var c=a.value,m=Object(d["handleNumberRecognition"])(c);m&&m.groups&&m.groups.length>0&&(s=s.concat(m.groups))}}catch(I){u.e(I)}finally{u.f()}r={groups:s}}if(r){if(r.groups&&r.groups.length>0)this.$set(this.formData,"betGroups",[]),r.groups.forEach((function(e){var n={methodId:e.methodId,danshuang:e.danshuang,daxiao:e.daxiao,dingwei:e.dingwei,hezhi:e.hezhi,betNumbers:e.betNumbers,money:null!=e.money?String(e.money):"",lotteryId:"tc"===t.futiSwitch?2:e.lotteryId||1};t.formData.betGroups.push(n)}));else{var h={methodId:r.methodId,danshuang:r.danshuang,daxiao:r.daxiao,dingwei:r.dingwei,hezhi:r.hezhi,betNumbers:r.betNumbers,money:null!=r.money?String(r.money):"",lotteryId:"tc"===this.futiSwitch?2:r.lotteryId||1};this.$set(this.formData,"betGroups",[h])}var f=this.formData.betGroups.map((function(e){return Object(l["a"])(Object(l["a"])({},e),{},{danshuang:e.danshuang||null,daxiao:e.daxiao||null,dingwei:e.dingwei||null,hezhi:e.hezhi||null,betNumbers:e.betNumbers||"",money:e.money||null,lotteryId:e.lotteryId||1})}));this.$set(this.localForm,"betGroups",f),this.$nextTick((function(){t.updateTotals(),t.formData.betGroups.forEach((function(e,n){var r=Object(l["a"])({},e);t.$set(t.formData.betGroups,n,r)})),t.$forceUpdate()}))}}}else this.formData.betGroups=[{methodId:void 0,money:"",betNumbers:"",danshuang:null,daxiao:null,dingwei:null,hezhi:null,lotteryId:"tc"===this.futiSwitch?2:1}]},formatBetNumbersToDisplay:function(e){try{if(!e)return"";if(!e.startsWith("{"))return e;var t="string"===typeof e?JSON.parse(e):e;return t.numbers&&Array.isArray(t.numbers)?t.numbers.map((function(e){if(void 0!==e.kuadu)return"跨度".concat(e.kuadu);if(void 0!==e.danma&&void 0!==e.tuoma)return"胆".concat(e.danma,"拖").concat(e.tuoma);var t=Object.values(e);return t.join("")})).join(","):e}catch(n){return console.error("格式化下注号码失败:",n,"jsonStr:",e),e}},getDisplayBetNumbers:function(e){if(!e.betNumbers)return"";if(e.methodId>=44&&e.methodId<=59||e.methodId>=60&&e.methodId<=69)try{return e.betNumbers.startsWith("{")&&e.betNumbers.includes("numbers")?this.formatBetNumbersToDisplay(e.betNumbers):e.betNumbers}catch(t){return console.error("格式化显示号码失败:",t),e.betNumbers}return e.betNumbers},onBetNumbersInput:function(e,t){var n=this.formData.betGroups[t];n.methodId>=44&&n.methodId<=59||n.methodId>=60&&n.methodId,this.$set(this.formData.betGroups,t,Object(l["a"])(Object(l["a"])({},n),{},{betNumbers:e})),this.updateTotals()},initFormData:function(){var e=this;try{this.originalUserId=this.row.userId,this.originalSerialNumber=this.row.serialNumber,this.formData.fc3dIssueNumber=this.row.issueNumber,this.formData.tcIssueNumber=this.row.issueNumber;var t=this.formatBetNumbersToDisplay(this.row.betNumbers),n={methodId:this.row.methodId,money:null!=this.row.money?String(this.row.money):"",betNumbers:t,danshuang:this.row.danshuang,daxiao:this.row.daxiao,dingwei:this.row.dingwei,hezhi:this.row.hezhi,lotteryId:this.row.lotteryId};this.formData.betGroups=[n],this.row.shibie&&(this.formData.shibie=this.row.shibie),this.row.serialNumber&&(this.serialNumber=this.row.serialNumber),this.$nextTick((function(){e.updateTotals(),e.$forceUpdate()}))}catch(r){console.error("初始化表单数据失败:",r)}},formatNumbersForRequest:function(e,t){if(!e)return{numbers:[]};if(t>=44&&t<=59)return this.formatDantuoNumbers(e,t);if(t>=60&&t<=69)return this.formatKuaduNumbers(e,t);var n=e.split(",").filter((function(e){return e.trim()})),r={1:function(e){return{a:parseInt(e)}},2:function(e){return{a:parseInt(e)}},33:function(e){return{a:parseInt(e)}},34:function(e){return{a:e}},35:function(e){return{a:e}},36:function(e){return{a:e}},3:function(e){var t=e.split("").map((function(e){return parseInt(e)}));return{a:t[0],b:t[1]}},4:function(e){var t=e.split("").map((function(e){return parseInt(e)}));return{a:t[0],b:t[1]}},5:function(e){var t=e.split("").map((function(e){return parseInt(e)}));return{a:t[0],b:t[1],c:t[2]}},6:function(e){var t=e.split("").map((function(e){return parseInt(e)}));return{a:t[0],b:t[1],c:t[2]}},7:function(e){var t=e.split("").map((function(e){return parseInt(e)}));return{a:t[0],b:t[1],c:t[2]}},100:function(e){var t=e.split("").map((function(e){return parseInt(e)}));return{a:t[0],b:t[1],c:t[2]}},8:function(e){var t=e.split("").map((function(e){return parseInt(e)}));return{a:t[0],b:t[1],c:t[2],d:t[3]}},9:function(e){var t=e.split("").map((function(e){return parseInt(e)}));return{a:t[0],b:t[1],c:t[2],d:t[3]}},10:function(e){var t=e.split("").map((function(e){return parseInt(e)}));return{a:t[0],b:t[1],c:t[2],d:t[3],e:t[4]}},11:function(e){var t=e.split("").map((function(e){return parseInt(e)}));return{a:t[0],b:t[1],c:t[2],d:t[3],e:t[4]}},12:function(e){var t=e.split("").map((function(e){return parseInt(e)}));return{a:t[0],b:t[1],c:t[2],d:t[3],e:t[4],f:t[5]}},13:function(e){var t=e.split("").map((function(e){return parseInt(e)}));return{a:t[0],b:t[1],c:t[2],d:t[3],e:t[4],f:t[5]}},14:function(e){var t=e.split("").map((function(e){return parseInt(e)}));return{a:t[0],b:t[1],c:t[2],d:t[3],e:t[4],f:t[5],g:t[6]}},15:function(e){var t=e.split("").map((function(e){return parseInt(e)}));return{a:t[0],b:t[1],c:t[2],d:t[3],e:t[4],f:t[5],g:t[6]}},16:function(e){var t=e.split("").map((function(e){return parseInt(e)}));return{a:t[0],b:t[1],c:t[2],d:t[3],e:t[4],f:t[5],g:t[6],h:t[7]}},17:function(e){var t=e.split("").map((function(e){return parseInt(e)}));return{a:t[0],b:t[1],c:t[2],d:t[3],e:t[4],f:t[5],g:t[6],h:t[7]}},18:function(e){var t=e.split("").map((function(e){return parseInt(e)}));return{a:t[0],b:t[1],c:t[2],d:t[3],e:t[4],f:t[5],g:t[6],h:t[7],i:t[8]}},19:function(e){var t=e.split("").map((function(e){return parseInt(e)}));return{a:t[0],b:t[1],c:t[2],d:t[3],e:t[4],f:t[5],g:t[6],h:t[7],i:t[8]}},20:function(){return{}},31:function(){return{}},21:function(){return{}},22:function(){return{}},23:function(){return{}},24:function(){return{}},25:function(){return{}},26:function(){return{}},27:function(){return{}},37:function(){return{}},38:function(){return{}},39:function(){return{}},40:function(){return{}},41:function(){return{}},42:function(){return{}},43:function(){return{}},28:function(){return{}},29:function(){return{}},30:function(){return{}}},a=r[t];return a?{numbers:n.map((function(e){return a(e.trim())}))}:{numbers:[]}},submitForm:function(){var e=this,t=this.validateMoney();t.valid?this.$refs["form"].validate((function(t){if(t){var n=e.$loading({lock:!0,text:"正在提交下注...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),r=e.formData.betGroups.map((function(t,n){var r=e.formatNumbersForRequest(t.betNumbers,t.methodId),a=sessionStorage.getItem("sysUserId"),o={lotteryId:t.lotteryId,issueNumber:1===t.lotteryId?e.formData.fc3dIssueNumber:e.formData.tcIssueNumber,methodId:t.methodId,userId:Number(e.selectedUserId),sysUserId:a?Number(a):null,betNumbers:JSON.stringify(r),money:t.money,danshuang:t.danshuang,daxiao:t.daxiao,dingwei:t.dingwei,hezhi:t.hezhi,totalAmount:e.getGroupAmount(t),totalBets:e.getGroupBets(t),shibie:e.formData.shibie,betTime:e.formatDate(new Date),serialNumber:e.serialNumber};return e.row&&0===n&&(o.betId=e.row.betId),o})),a=e.row?"/game/record":"/game/record/batch",o=e.row?"put":"post",i=e.row?r[0]:r;Object(m["a"])({url:a,method:o,data:i}).then((function(t){if(200===t.code)e.$modal.msgSuccess("操作成功"),e.$emit("success"),e.formData.shibie="",e.formData.betGroups=[{methodId:void 0,money:"",betNumbers:"",danshuang:null,daxiao:null,dingwei:null,hezhi:null,lotteryId:1}],e.updateTotals(),e.row&&e.close();else if(t.data&&Array.isArray(t.data.failedIndices)){var n=e.formData.betGroups.filter((function(e,n){return t.data.failedIndices.includes(n)}));e.$set(e.formData,"betGroups",n),e.$modal.msgError("".concat(t.data.failedIndices.length,"个下注失败，请重试"))}else t._errorHandled||e.$modal.msgError(t.msg||"操作失败，请重试")})).catch((function(t){console.error("网络请求失败:",t),e.$modal.msgError("网络连接失败，请检查网络后重试")})).finally((function(){n.close(),e.updateTotals()}))}})):this.$alert(t.message,"错误",{confirmButtonText:"确定",type:"error",dangerouslyUseHTMLString:!0})},getGroupBets:function(e){return e.methodId>=44&&e.methodId<=59||e.methodId>=60&&e.methodId<=69?1:this.isSpecialMethod(e.methodId)?[21,22,23,24,25,26,27,37,38,39,40,41,42,43].includes(e.methodId)?e.hezhi?1:0:[29,30].includes(e.methodId)?e.danshuang||e.daxiao?1:0:[20,31].includes(e.methodId)?1:0:e.betNumbers?e.betNumbers.split(",").filter((function(e){return e.trim()})).length:0},getGroupAmount:function(e){var t=this.getGroupBets(e),n=Number(e.money)||0;return t*n},isSpecialMethod:function(e){return[20,21,22,23,24,25,26,27,29,30,31,37,38,39,40,41,42,43].includes(e)},formatDantuoNumbers:function(e,t){var n=e.split("拖");if(2!==n.length)return{numbers:[]};var r=n[0].replace(/[^0-9]/g,""),a=n[1].replace(/[^0-9,，\s]/g,"").split(/[,，\s]+/).filter((function(e){return e&&e!==r}));return{numbers:[{danma:r,tuoma:a.join(",")}]}},formatKuaduNumbers:function(e,t){if(console.log("formatKuaduNumbers 输入:",e,"methodId:",t),e.startsWith("{"))try{var n=JSON.parse(e);return console.log("已是JSON格式:",n),n}catch(i){console.error("JSON解析失败:",i)}var r=null,a=e.match(/跨度(\d)/);if(a?r=a[1]:(a=e.match(/跨(\d)[\s\-]*\d+/),a?r=a[1]:t>=60&&t<=69&&(r=(t-60).toString())),console.log("解析出的跨度值:",r),null===r)return console.warn("无法解析跨度值，返回空数组"),{numbers:[]};var o={numbers:[{kuadu:r}]};return console.log("formatKuaduNumbers 输出:",o),o},updateGroupValue:function(e,t,n){var r=this.formData.betGroups.findIndex((function(t){return t===e}));if(r>-1){var a=Object(l["a"])(Object(l["a"])({},this.formData.betGroups[r]),{},Object(o["a"])({},t,n));this.$set(this.formData.betGroups,r,a),this.$forceUpdate()}},formatDate:function(e){var t=function(e){return e<10?"0".concat(e):e},n=e.getFullYear(),r=t(e.getMonth()+1),a=t(e.getDate()),o=t(e.getHours()),i=t(e.getMinutes()),s=t(e.getSeconds());return"".concat(n,"-").concat(r,"-").concat(a," ").concat(o,":").concat(i,":").concat(s)},getCurrentUser:function(){var e=sessionStorage.getItem("User_Id");e?(this.selectedUserId=Number(e),this.loadUserById(e)):this.initializeFirstUser()},loadUserById:function(e){var t=this;Object(m["a"])({url:"/game/customer/".concat(e),method:"get"}).then((function(e){200===e.code&&e.data?t.currentUser=e.data:t.currentUser={name:"未知用户"}})).catch((function(e){console.error("获取用户信息失败:",e),t.currentUser={name:"未知用户"}}))},initializeFirstUser:function(){var e=this;Object(m["a"])({url:"/game/customer/list",method:"get",params:{pageNum:1,pageSize:1e3}}).then((function(t){if(200===t.code&&t.rows&&t.rows.length>0){var n=t.rows[0];e.selectedUserId=n.userId,e.currentUser=n,sessionStorage.setItem("User_Id",n.userId)}else console.warn("当前用户没有玩家数据"),e.currentUser={name:"无玩家数据"},e.selectedUserId=null})).catch((function(t){console.error("获取玩家列表失败:",t),e.currentUser={name:"获取失败"},e.selectedUserId=null}))},getAllUsers:function(){var e=this;Object(m["a"])({url:"/game/customer/list",method:"get",params:{pageNum:1,pageSize:1e3}}).then((function(t){200===t.code&&t.rows?e.userList=t.rows:e.userList=[]}))},switchUser:function(){this.selectedUserId&&(sessionStorage.setItem("User_Id",this.selectedUserId),this.getCurrentUser(),this.$message.success("切换用户成功"))},onFutiSwitchChange:function(e){sessionStorage.setItem("futiSwitch",e),"tc"===e?this.formData&&this.formData.betGroups&&this.formData.betGroups.forEach((function(e){e.lotteryId=2,null!=e.money&&"string"!==typeof e.money&&(e.money=String(e.money))})):this.formData&&this.formData.betGroups&&this.formData.betGroups.forEach((function(e){null!=e.money&&"string"!==typeof e.money&&(e.money=String(e.money))})),this.$forceUpdate()},filterMethod:function(e){this.filteredGameMethods=e?this.gameMethodsData.filter((function(t){return t.methodName&&-1!==t.methodName.indexOf(e)||"3"===e&&t.methodName&&-1!==t.methodName.indexOf("三")})):this.gameMethodsData},showFormatDialog:function(){this.formatDialogVisible=!0,this.formatInput="",this.formatOutput="",this.formatTab="unified"},showStatistics:function(){this.statisticsVisible=!0},validateMoney:function(){for(var e=0;e<this.formData.betGroups.length;e++){var t=this.formData.betGroups[e],n=t.money,r=e+1,a=null!=n?String(n):"";if(!a||""===a.trim())return{valid:!1,message:'第<span style="color: #ff4757; font-weight: bold; font-size: 16px;">'.concat(r,'</span>组下注金额不能为<span style="color: #ff4757; font-weight: bold; font-size: 16px;">空</span>')};var o=Number(a.trim());if(isNaN(o)||o<=0)return{valid:!1,message:'第<span style="color: #ff4757; font-weight: bold; font-size: 16px;">'.concat(r,'</span>组下注金额不能为<span style="color: #ff4757; font-weight: bold; font-size: 16px;">"').concat(a,'"</span>，请修改')}}return{valid:!0,message:""}},checkStatisticsPermission:function(){var e=this;return Object(c["a"])(Object(u["a"])().m((function t(){var n,r;return Object(u["a"])().w((function(t){while(1)switch(t.n){case 0:return t.p=0,t.n=1,Object(p["getStatisticsPermission"])();case 1:n=t.v,n&&200===n.code&&void 0!==n.data?e.hasStatisticsPermission="1"===n.data:e.hasStatisticsPermission=!1,t.n=3;break;case 2:t.p=2,r=t.v,console.error("获取用户权限失败:",r),e.hasStatisticsPermission=!1;case 3:return t.a(2)}}),t,null,[[0,2]])})))()},handleFormatInput:function(){var e=this,t=(this.formatInput||"").replace(/[。．.]/g,"-");t=t.replace(/-+/g,"-");var n=t.split("\n").map((function(t){return e.replaceChineseNumber(t)}));n=this.autoConvertChineseMoney(n),this.formatOutput=n.filter((function(e){return e})).join("\n")},insertFormatResult:function(){this.formData.shibie=this.formatOutput,this.formatDialogVisible=!1,this.handleNumberRecognition(this.formatOutput)},handleColonFormatInput:function(){var e=this,t=(this.colonFormatInput||"").replace(/[：:]/g,"-");t=t.replace(/-+/g,"-");var n=t.split("\n").map((function(t){return e.replaceChineseNumber(t)}));n=this.autoConvertChineseMoney(n),this.colonFormatOutput=n.filter((function(e){return e})).join("\n")},insertColonFormatResult:function(){this.formData.shibie=this.colonFormatOutput,this.formatDialogVisible=!1,this.handleNumberRecognition(this.colonFormatOutput)},handleMoneyFormatInput:function(){var e=this,t=(this.moneyFormatInput||"").split("\n").map((function(t){return e.replaceChineseNumber(t)})),n=/([一二三四五六七八九十百千万两零]+)(元|块|米)?/g;t=t.map((function(t){var r,a;while(null!==(a=n.exec(t)))r=a;if(r){var o=e.chineseToNumber(r[1]),i=r[2]||"",s=r.index,l=s+r[0].length,u=t.slice(0,s);return/[\/\s,，.。-]+$/.test(u)||(u+="/"),u+o+i+t.slice(l)}return t})),this.moneyFormatOutput=t.filter((function(e){return e})).join("\n")},insertMoneyFormatResult:function(){this.formData.shibie=this.moneyFormatOutput,this.formatDialogVisible=!1,this.handleNumberRecognition(this.moneyFormatOutput)},handleUnifiedFormatInput:function(){var e=this.unifiedFormatInput||"",t=e.split("\n").map((function(e){return e.trim()})).filter(Boolean);t=this.autoConvertChineseMoney(t),e=t.join("\n"),e=this.replaceChineseNumber(e),e=e.replace(/[,，]+/g," "),e=e.replace(/[。．.]+/g," "),e=e.replace(/[：:]+/g," "),e=e.replace(/_+/g," "),e=e.replace(/[➕＋﹢✚✛✜✙❇️❌❎]+/g," "),e=e.replace(/\s+/g," "),this.unifiedFormatOutput=e.split("\n").map((function(e){return e.trim()})).filter(Boolean).join("\n")},insertUnifiedFormatResult:function(){this.formData.shibie=this.unifiedFormatOutput,this.formatDialogVisible=!1,this.handleNumberRecognition(this.unifiedFormatOutput)},handleChainFormatInput:function(){var e=this.chainFormatInput||"";e=this.replaceChineseNumber(e);var t=/\d+/g,n=e.match(t)||[],r={},a={};n.forEach((function(e,t){var n=e.length;r[n]||(r[n]=[],a[n]=t),r[n].push(e)}));var o=Object.keys(r).sort((function(e,t){return a[e]-a[t]})),i=[];o.forEach((function(e){var t=r[e];if(t.length>0){var n=t.join(" ");i.push(n)}})),this.chainFormatOutput=i.join("\n")},insertChainFormatResult:function(){this.formData.shibie=this.chainFormatOutput,this.formatDialogVisible=!1,this.handleNumberRecognition(this.chainFormatOutput)},handleAmountFormatInput:function(){var e=this,t=this.amountFormatInput||"",n=t.split("\n").filter((function(e){return e.trim()})),r={},a=[];n.forEach((function(t){var n=t.trim();if(n){var o=n.split(/\s+/).filter((function(e){return e.trim()}));1===o.length?e.processAmountFormatItem(o[0],r,a):o.forEach((function(t){e.processAmountFormatItem(t,r,a)}))}}));var o=[];a.forEach((function(e){var t=r[e];if(t&&t.numbers.length>0){var n=t.numbers.join("-")+"-"+t.amount;o.push(n)}})),this.amountFormatOutput=o.join("\n")},processAmountFormatItem:function(e,t,n){var r=e.trim();if(r){var a=r.lastIndexOf("-");if(-1!==a){var o=r.substring(0,a).trim(),i=r.substring(a+1).trim();if(o&&i){var s=o.length,l="".concat(i,"_").concat(s);t[l]||(t[l]={amount:i,length:s,numbers:[]},n.push(l)),t[l].numbers.push(o)}}}},insertAmountFormatResult:function(){this.formData.shibie=this.amountFormatOutput,this.formatDialogVisible=!1,this.handleNumberRecognition(this.amountFormatOutput)},handleRemoveFormatInput:function(){var e=this.removeFormatInput||"",t=e.replace(/[^\u4e00-\u9fff\d\n\r]/g,"");this.removeFormatOutput=t.split("\n").map((function(e){return e.trim()})).filter((function(e,t,n){return e||t>0&&n[t-1]})).join("\n").replace(/\n{3,}/g,"\n\n")},insertRemoveFormatResult:function(){this.formData.shibie=this.removeFormatOutput,this.formatDialogVisible=!1,this.handleNumberRecognition(this.removeFormatOutput)},handleUnderscoreFormatInput:function(){var e=this,t=(this.underscoreFormatInput||"").replace(/_+/g," "),n=t.split("\n").map((function(t){return e.replaceChineseNumber(t)}));n=this.autoConvertChineseMoney(n),this.underscoreFormatOutput=n.filter((function(e){return e})).join("\n")},insertUnderscoreFormatResult:function(){this.formData.shibie=this.underscoreFormatOutput,this.formatDialogVisible=!1,this.handleNumberRecognition(this.underscoreFormatOutput)},replaceChineseNumber:function(e){var t=this,n=/[零一二两三四五六七八九十百千万]+/g;return e.replace(n,(function(e){if(1===e.length&&!/[十百千万]/.test(e)){var n={"零":0,"一":1,"二":2,"两":2,"三":3,"四":4,"五":5,"六":6,"七":7,"八":8,"九":9};return n[e]||e}return t.chineseToNumber(e)}))},chineseToNumber:function(e){if(!e)return 0;if(/^\d+$/.test(e))return parseInt(e,10);for(var t=e.replace(/元|块|米/g,""),n={"零":0,"一":1,"二":2,"两":2,"三":3,"四":4,"五":5,"六":6,"七":7,"八":8,"九":9},r={"十":10,"百":100,"千":1e3,"万":1e4},a=0,o=0,i=!1,s=0;s<t.length;s++){var l=t[s];if(n.hasOwnProperty(l))o=n[l],i=!0;else if(r.hasOwnProperty(l)){var u=r[l];"十"!==l||i||(o=1),1e4===u?(a=(a+o)*u,o=0):(a+=o*u,o=0),i=!1}}return a+=o,a},autoConvertChineseMoney:function(e){var t=this;"string"===typeof e&&(e=e.split(/\n/));var n=/([零一二三四五六七八九十百千万两]+)(元|块|米)?/g;return e.map((function(e){var r,a;n.lastIndex=0;while(null!==(a=n.exec(e)))/[一二三四五六七八九十百千万两零]/.test(a[1])&&(r=a);if(r){var o=t.chineseToNumber(r[1]),i=r[2]||"",s=r.index,l=s+r[0].length,u=e.slice(0,s);return u&&!/[\s\/,，.。-]$/.test(u)&&(u+=" "),u+o+i+e.slice(l)}return e}))},initSerialNumber:function(){var e=this;if(this.row&&this.row.serialNumber)return this.serialNumber=this.row.serialNumber,this.originalSerialNumber=this.row.serialNumber,void Object(h["a"])().then((function(t){e.maxSerialNumber=t?Number(t):1})).catch((function(){e.maxSerialNumber=1}));Object(h["a"])().then((function(t){null==t?(e.maxSerialNumber=1,e.serialNumber=1):(e.maxSerialNumber=Number(t),e.serialNumber=Number(t))})).catch((function(){e.maxSerialNumber=1,e.serialNumber=1}))},increaseSerialNumber:function(){var e=Number(this.maxSerialNumber)+1;this.serialNumber<e&&(this.serialNumber=e)},generateNewSerialNumber:function(){var e=this;this.generateLoading=!0,Object(f["c"])().then((function(t){200===t.code?(e.serialNumber=t.serialNumber,e.maxSerialNumber=t.serialNumber,e.$message.success("生成新流水号: ".concat(t.serialNumber))):e.$message.error("生成流水号失败: "+t.msg)})).catch((function(t){console.error("生成流水号失败:",t),e.$message.error("生成流水号失败")})).finally((function(){e.generateLoading=!1}))},handleUserIdChange:function(e,t){e===this.originalUserId?(this.serialNumber=this.originalSerialNumber,this.$message.info("已恢复原流水号: ".concat(this.originalSerialNumber))):this.autoGenerateSerialNumber()},autoGenerateSerialNumber:function(){var e=this;Object(f["c"])().then((function(t){200===t.code?(e.serialNumber=t.serialNumber,e.maxSerialNumber=t.serialNumber,e.$message.info("用户变更，自动生成新流水号: ".concat(t.serialNumber))):e.$message.error("自动生成流水号失败: "+t.msg)})).catch((function(t){console.error("自动生成流水号失败:",t),e.$message.error("自动生成流水号失败")}))}}},U=N,v=(n("414a"),n("7883"),n("2877")),A=Object(v["a"])(U,r,a,!1,null,"5f13ee76",null);t["default"]=A.exports},"8de0":function(e,t,n){"use strict";n.r(t),n.d(t,"DANTUO_PATTERNS",(function(){return a}));n("99af"),n("4de4"),n("a15b"),n("14d9"),n("d3b7"),n("ac1f"),n("00b4"),n("5319"),n("1276"),n("0643"),n("2382");var r=n("f9d6"),a={DANTUO_DEFAULT_ZULIU:{pattern:/^(?:胆|胆码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:拖|拖码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9,，\s]+?)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?$/,handler:function(e,t){var n=e[1],a=e[2],o=e[3],i=a.replace(/[\s,，]+/g,"").split("").filter((function(e){return e&&e!==n}));if(i.length<2||i.length>9)return console.warn("胆拖默认组六拖码数量不合法: ".concat(i.length,"，应为2-9个")),null;var s=Object(r["getDantuoZuliuMethodId"])(i.length);return[{methodId:s,betNumbers:"胆".concat(n,"拖").concat(i.join(",")),money:o}]}},DANTUO_EXPLICIT_ZUSAN:{pattern:/^(?:胆|胆码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:拖|拖码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9,，\s]+?)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?组三$/,handler:function(e,t){var n=e[1],a=e[2],o=e[3],i=a.replace(/[\s,，]+/g,"").split("").filter((function(e){return e&&e!==n}));if(i.length<2||i.length>9)return console.warn("胆拖组三拖码数量不合法: ".concat(i.length,"，应为2-9个")),null;var s=Object(r["getDantuoZusanMethodId"])(i.length);return[{methodId:s,betNumbers:"胆".concat(n,"拖").concat(i.join(",")),money:o}]}},DANTUO_EXPLICIT_ZULIU:{pattern:/^(?:胆|胆码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:拖|拖码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9,，\s]+?)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?组六$/,handler:function(e,t){var n=e[1],a=e[2],o=e[3],i=a.replace(/[\s,，]+/g,"").split("").filter((function(e){return e&&e!==n}));if(i.length<2||i.length>9)return console.warn("胆拖组六拖码数量不合法: ".concat(i.length,"，应为2-9个")),null;var s=Object(r["getDantuoZuliuMethodId"])(i.length);return[{methodId:s,betNumbers:"胆".concat(n,"拖").concat(i.join(",")),money:o}]}},SIMPLE_DANTUO_DEFAULT_ZULIU:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:拖|拖码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9,，\s]+?)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?$/,handler:function(e,t){var n=e[1],a=e[2],o=e[3],i=a.replace(/[\s,，]+/g,"").split("").filter((function(e){return e&&e!==n}));if(i.length<2||i.length>9)return console.warn("简化胆拖默认组六拖码数量不合法: ".concat(i.length,"，应为2-9个")),null;var s=Object(r["getDantuoZuliuMethodId"])(i.length);return[{methodId:s,betNumbers:"胆".concat(n,"拖").concat(i.join(",")),money:o}]}},SIMPLE_DANTUO_ZUSAN:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:拖|拖码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9,，\s]+?)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?组三$/,handler:function(e,t){var n=e[1],a=e[2],o=e[3],i=a.replace(/[\s,，]+/g,"").split("").filter((function(e){return e&&e!==n}));if(i.length<2||i.length>9)return console.warn("简化胆拖组三拖码数量不合法: ".concat(i.length,"，应为2-9个")),null;var s=Object(r["getDantuoZusanMethodId"])(i.length);return[{methodId:s,betNumbers:"胆".concat(n,"拖").concat(i.join(",")),money:o}]}},SIMPLE_DANTUO_ZULIU:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:拖|拖码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9,，\s]+?)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?组六$/,handler:function(e,t){var n=e[1],a=e[2],o=e[3],i=a.replace(/[\s,，]+/g,"").split("").filter((function(e){return e&&e!==n}));if(i.length<2||i.length>9)return console.warn("简化胆拖组六拖码数量不合法: ".concat(i.length,"，应为2-9个")),null;var s=Object(r["getDantuoZuliuMethodId"])(i.length);return[{methodId:s,betNumbers:"胆".concat(n,"拖").concat(i.join(",")),money:o}]}},DANTUO_ZUSAN:{pattern:/^胆(\d)拖([\d,，\s]+)组三[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*(\d+)(?:元|块|米)?$/,handler:function(e,t){var n=e[1],a=e[2],o=e[3],i=a.split(/[,，\s]+/).filter((function(e){return e&&e!==n}));if(i.length<2||i.length>9)return console.warn("胆拖组三拖码数量不合法: ".concat(i.length,"，应为2-9个")),null;var s=Object(r["getDantuoZusanMethodId"])(i.length);return[{methodId:s,betNumbers:"胆".concat(n,"拖").concat(i.join(",")),money:o}]}},DANTUO_ZULIU:{pattern:/^胆(\d)拖([\d,，\s]+)组六[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*(\d+)(?:元|块|米)?$/,handler:function(e,t){var n=e[1],a=e[2],o=e[3],i=a.split(/[,，\s]+/).filter((function(e){return e&&e!==n}));if(i.length<2||i.length>9)return console.warn("胆拖组六拖码数量不合法: ".concat(i.length,"，应为2-9个")),null;var s=Object(r["getDantuoZuliuMethodId"])(i.length);return[{methodId:s,betNumbers:"胆".concat(n,"拖").concat(i.join(",")),money:o}]}},DANTUO_BOTH:{pattern:/^胆(\d)拖([\d,，\s]+)(?:组三组六|组六组三)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*各?(\d+)(?:元|块|米)?$/,handler:function(e,t){var n=e[1],a=e[2],o=e[3],i=a.split(/[,，\s]+/).filter((function(e){return e&&e!==n}));if(i.length<2||i.length>9)return console.warn("胆拖组三组六拖码数量不合法: ".concat(i.length,"，应为2-9个")),null;var s=Object(r["getDantuoZusanMethodId"])(i.length),l=Object(r["getDantuoZuliuMethodId"])(i.length);return[{methodId:s,betNumbers:"胆".concat(n,"拖").concat(i.join(",")),money:o},{methodId:l,betNumbers:"胆".concat(n,"拖").concat(i.join(",")),money:o}]}},DANTUO_DEFAULT_BOTH:{pattern:/^胆(\d)拖([\d,，\s]+)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*各?(\d+)(?:元|块|米)?$/,handler:function(e,t){if(/组三|组六/.test(t))return null;var n=e[1],a=e[2],o=e[3],i=a.split(/[,，\s]+/).filter((function(e){return e&&e!==n}));if(i.length<2||i.length>9)return console.warn("胆拖默认拖码数量不合法: ".concat(i.length,"，应为2-9个")),null;var s=Object(r["getDantuoZusanMethodId"])(i.length),l=Object(r["getDantuoZuliuMethodId"])(i.length);return[{methodId:s,betNumbers:"胆".concat(n,"拖").concat(i.join(",")),money:o},{methodId:l,betNumbers:"胆".concat(n,"拖").concat(i.join(",")),money:o}]}},DANTUO_WITH_LOTTERY_PREFIX:{pattern:/^(?:福彩?|体彩?|排三?|3D)?胆(\d)拖([\d,，\s]+)(?:组三|组六)?[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*(\d+)(?:元|块|米)?$/,handler:function(e,t){var n=e[1],a=e[2],o=e[3],i=a.split(/[,，\s]+/).filter((function(e){return e&&e!==n}));if(i.length<2||i.length>9)return console.warn("胆拖带前缀拖码数量不合法: ".concat(i.length,"，应为2-9个")),null;var s=1;/体彩|排三/.test(t)&&(s=2);var l=[];if(/组三/.test(t)){var u=Object(r["getDantuoZusanMethodId"])(i.length);l.push({methodId:u,betNumbers:"胆".concat(n,"拖").concat(i.join(",")),money:o,lotteryId:s})}else if(/组六/.test(t)){var c=Object(r["getDantuoZuliuMethodId"])(i.length);l.push({methodId:c,betNumbers:"胆".concat(n,"拖").concat(i.join(",")),money:o,lotteryId:s})}else{var d=Object(r["getDantuoZusanMethodId"])(i.length),m=Object(r["getDantuoZuliuMethodId"])(i.length);l.push({methodId:d,betNumbers:"胆".concat(n,"拖").concat(i.join(",")),money:o,lotteryId:s}),l.push({methodId:m,betNumbers:"胆".concat(n,"拖").concat(i.join(",")),money:o,lotteryId:s})}return l}},DANTUO_SEPARATED_NUMBERS_TYPE_BEFORE_MONEY:{pattern:/^(?:胆|胆码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:拖|拖码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*((?:\d[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*){2,9})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(组三|组六)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?$/,handler:function(e){var t,n=e[1],a=e[2],o=e[3],i=e[4],s=a.replace(/[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]/g,"").split("").filter((function(e){return e&&e!==n}));return s.length<2||s.length>9?(console.warn("胆拖分隔数字拖码数量不合法: ".concat(s.length,"，应为2-9个")),null):(t="组三"===o?Object(r["getDantuoZusanMethodId"])(s.length):Object(r["getDantuoZuliuMethodId"])(s.length),[{methodId:t,betNumbers:"胆".concat(n,"拖").concat(s.join(",")),money:i}])}},DANTUO_SEPARATED_NUMBERS_TYPE_AFTER_MONEY:{pattern:/^(?:胆|胆码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:拖|拖码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*((?:\d[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*){2,9})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+(组三|组六)$/,handler:function(e){var t,n=e[1],a=e[2],o=e[3],i=e[4],s=a.replace(/[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]/g,"").split("").filter((function(e){return e&&e!==n}));return s.length<2||s.length>9?(console.warn("胆拖分隔数字拖码数量不合法: ".concat(s.length,"，应为2-9个")),null):(t="组三"===i?Object(r["getDantuoZusanMethodId"])(s.length):Object(r["getDantuoZuliuMethodId"])(s.length),[{methodId:t,betNumbers:"胆".concat(n,"拖").concat(s.join(",")),money:o}])}},DANTUO_ZULIU_ZUSAN_COMBO:{pattern:/^(?:胆|胆码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:拖|拖码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9,，\s]+?)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*\+[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?$/,handler:function(e){var t=e[1],n=e[2],a=e[3],o=e[4],i=n.replace(/[\s,，]+/g,"").split("").filter((function(e){return e&&e!==t}));if(i.length<2||i.length>9)return console.warn("胆拖组合格式拖码数量不合法: ".concat(i.length,"，应为2-9个")),null;var s=Object(r["getDantuoZusanMethodId"])(i.length),l=Object(r["getDantuoZuliuMethodId"])(i.length);return[{methodId:l,betNumbers:"胆".concat(t,"拖").concat(i.join(",")),money:a},{methodId:s,betNumbers:"胆".concat(t,"拖").concat(i.join(",")),money:o}]}},SIMPLE_DANTUO_ZULIU_ZUSAN_COMBO:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:拖|拖码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9,，\s]+?)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*\+[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?$/,handler:function(e){var t=e[1],n=e[2],a=e[3],o=e[4],i=n.replace(/[\s,，]+/g,"").split("").filter((function(e){return e&&e!==t}));if(i.length<2||i.length>9)return console.warn("简化胆拖组合格式拖码数量不合法: ".concat(i.length,"，应为2-9个")),null;var s=Object(r["getDantuoZusanMethodId"])(i.length),l=Object(r["getDantuoZuliuMethodId"])(i.length);return[{methodId:l,betNumbers:"胆".concat(t,"拖").concat(i.join(",")),money:a},{methodId:s,betNumbers:"胆".concat(t,"拖").concat(i.join(",")),money:o}]}}}},"98b6":function(e,t,n){"use strict";n.r(t),n.d(t,"KUADU_PATTERNS",(function(){return i})),n.d(t,"isKuaduMethod",(function(){return s})),n.d(t,"getKuaduMethodId",(function(){return l})),n.d(t,"getKuaduValue",(function(){return u}));var r=n("b85c"),a=(n("d81d"),n("14d9"),n("e9c4"),n("b64b"),n("d3b7"),n("4d63"),n("c607"),n("ac1f"),n("2c3e"),n("25f0"),n("8a79"),n("5319"),n("0643"),n("4e3e"),n("a573"),n("159b"),n("f9d6")),o={0:a["METHOD_ID"].KUADU_0,1:a["METHOD_ID"].KUADU_1,2:a["METHOD_ID"].KUADU_2,3:a["METHOD_ID"].KUADU_3,4:a["METHOD_ID"].KUADU_4,5:a["METHOD_ID"].KUADU_5,6:a["METHOD_ID"].KUADU_6,7:a["METHOD_ID"].KUADU_7,8:a["METHOD_ID"].KUADU_8,9:a["METHOD_ID"].KUADU_9},i={KUADU_VALUE_FIRST:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9])[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:跨|跨度)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*$/,handler:function(e){var t=parseInt(e[1]),n=e[2];return t<0||t>9?(console.warn("跨度值超出范围: ".concat(t,"，应为0-9")),null):[{methodId:o[t],betNumbers:JSON.stringify({numbers:[{kuadu:t.toString()}]}),money:n}]},priority:95},KUADU_VALUE_MIDDLE:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:跨|跨度)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9])[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*$/,handler:function(e){var t=parseInt(e[1]),n=e[2];return t<0||t>9?(console.warn("跨度值超出范围: ".concat(t,"，应为0-9")),null):[{methodId:o[t],betNumbers:JSON.stringify({numbers:[{kuadu:t.toString()}]}),money:n}]},priority:94},KUADU_VALUE_FIRST_KEYWORD_LAST:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9])[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:跨|跨度)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*$/,handler:function(e){var t=parseInt(e[1]),n=e[2];return t<0||t>9?(console.warn("跨度值超出范围: ".concat(t,"，应为0-9")),null):[{methodId:o[t],betNumbers:JSON.stringify({numbers:[{kuadu:t.toString()}]}),money:n}]},priority:93},MULTI_KUADU_VALUE_FIRST:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9]{2,})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:跨|跨度)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*$/,handler:function(e){var t,n=e[1],a=e[2],i=n.split("").map((function(e){return parseInt(e)})),s=[],l=Object(r["a"])(i);try{for(l.s();!(t=l.n()).done;){var u=t.value;u<0||u>9?console.warn("跨度值超出范围: ".concat(u,"，应为0-9")):s.push({methodId:o[u],betNumbers:"跨度".concat(u),money:a})}}catch(c){l.e(c)}finally{l.f()}return s.length>0?s:null},priority:96},MULTI_KUADU_VALUE_MIDDLE:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:跨|跨度)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9]{2,})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*$/,handler:function(e){var t,n=e[1],a=e[2],i=n.split("").map((function(e){return parseInt(e)})),s=[],l=Object(r["a"])(i);try{for(l.s();!(t=l.n()).done;){var u=t.value;u<0||u>9?console.warn("跨度值超出范围: ".concat(u,"，应为0-9")):s.push({methodId:o[u],betNumbers:JSON.stringify({numbers:[{kuadu:u.toString()}]}),money:a})}}catch(c){l.e(c)}finally{l.f()}return s.length>0?s:null},priority:95},MULTI_KUADU_VALUE_FIRST_KEYWORD_LAST:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9]{2,})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:跨|跨度)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*$/,handler:function(e){var t,n=e[1],a=e[2],i=n.split("").map((function(e){return parseInt(e)})),s=[],l=Object(r["a"])(i);try{for(l.s();!(t=l.n()).done;){var u=t.value;u<0||u>9?console.warn("跨度值超出范围: ".concat(u,"，应为0-9")):s.push({methodId:o[u],betNumbers:JSON.stringify({numbers:[{kuadu:u.toString()}]}),money:a})}}catch(c){l.e(c)}finally{l.f()}return s.length>0?s:null},priority:94}};function s(e){return e>=60&&e<=69}function l(e){return o[e]}function u(e){return e-a["METHOD_ID"].KUADU_0}Object.keys(i).forEach((function(e){var t=i[e].pattern;"string"===typeof t&&t.endsWith("$")?i[e].pattern=t.replace(/\$$/,"[，,./*-=。·、s]*$"):t instanceof RegExp&&t.source.endsWith("$")&&(i[e].pattern=new RegExp(t.source.replace(/\$$/,"[，,./*-=。·、s]*$")))}))},"9a0c":function(e,t,n){"use strict";var r=n("342f");e.exports=/Version\/10(?:\.\d+){1,2}(?: [\w./]+)?(?: Mobile\/\w+)? Safari\//.test(r)},a2bf6:function(e,t,n){"use strict";var r=n("e8b5"),a=n("07fa"),o=n("3511"),i=n("0366"),s=function(e,t,n,l,u,c,d,m){var h,f,I=u,b=0,p=!!d&&i(d,m);while(b<l)b in n&&(h=p?p(n[b],b,t):n[b],c>0&&r(h)?(f=a(h),I=s(e,t,h,f,I,c-1)-1):(o(I+1),e[I]=h),I++),b++;return I};e.exports=s},b2f0:function(e,t,n){"use strict";n.r(t),n.d(t,"handleNumberRecognition",(function(){return I})),n.d(t,"handleSmartRecognition",(function(){return b}));var r=n("5530"),a=n("b85c"),o=n("53ca"),i=n("2909"),s=(n("99af"),n("4de4"),n("caad"),n("a15b"),n("d81d"),n("14d9"),n("e9c4"),n("4ec9"),n("b64b"),n("d3b7"),n("ac1f"),n("00b4"),n("6062"),n("1e70"),n("79a4"),n("c1a1"),n("8b00"),n("a4e7"),n("1e5a"),n("72c3"),n("2532"),n("3ca3"),n("466d"),n("5319"),n("1276"),n("498a"),n("0643"),n("76d6"),n("2382"),n("4e3e"),n("a573"),n("9a9a"),n("159b"),n("ddb0"),n("7b57"));function l(e,t){if(!e||0===e.length)return null;var r=n("f9d6").METHOD_ID,a=0,o=t&&t.match(/(\d{2,9})/);o&&(a=o[1].length);var s=e.filter((function(e){return e.every((function(e){return e.betNumbers&&e.betNumbers.split(",").every((function(e){return e.length===a}))&&e.methodId!==r.DUDAN}))}));0===s.length&&(s=e.filter((function(e){return e.every((function(e){return e.betNumbers&&e.betNumbers.split(",").every((function(e){return e.length===a}))}))}))),0===s.length&&(s=e);var l=Math.max.apply(Math,Object(i["a"])(s.map((function(e){return e.length})))),u=s.filter((function(e){return e.length===l}));if(1===u.length)return u[0];var c=Math.max.apply(Math,Object(i["a"])(u.map((function(e){return Math.max.apply(Math,Object(i["a"])(e.map((function(e){return(e.betNumbers||"").length}))))}))));if(u=u.filter((function(e){return Math.max.apply(Math,Object(i["a"])(e.map((function(e){return(e.betNumbers||"").length}))))===c})),1===u.length)return u[0];var d=Math.max.apply(Math,Object(i["a"])(u.map((function(e){return new Set(e.map((function(e){return e.methodId}))).size}))));if(u=u.filter((function(e){return new Set(e.map((function(e){return e.methodId}))).size===d})),1===u.length)return u[0];var m=[8,10,12,14,16,18],h=[9,11,13,15,17,19];function f(e){var t=e.map((function(e){return e.methodId}));return t.some((function(e){return m.includes(e)}))?2:t.some((function(e){return h.includes(e)}))?1:0}var I=Math.max.apply(Math,Object(i["a"])(u.map(f)));return u=u.filter((function(e){return f(e)===I})),u[0]}function u(e){return e.replace(/[零一二三四五六七八九十百千万亿]+注/g,"")}function c(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];console.log('\n【getBestPatternResult】开始处理文本: "'.concat(e,'", strict模式: ').concat(t));var n=e;e=u(e),n!==e&&console.log("【getBestPatternResult】去除中文注数后:",e),/[*Xx]\d/.test(e)?(console.log("【getBestPatternResult】检测到两码定位格式，保护*号"),e=e.replace(/^[\s\-/+\.,、|~!@#$%^&；'\"]+|[\s\-/+\.,、|~!@#$%^&；'\"]+$/g,"")):e=e.replace(/^[\s\-*/+\.,、|~!@#$%^&；'\"]+|[\s\-*/+\.,、|~!@#$%^&；'\"]+$/g,""),e=e.replace(/共\s*\d+(注|元|米|块)?(\s*\+.*)?$/g,(function(e,t,n){return n||""})),console.log("【getBestPatternResult】最终处理后的文本:",e);var r=[],a=[];for(var i in console.log("【getBestPatternResult】开始遍历所有正则模式，总数:",Object.keys(s["ALL_PATTERNS"]).length),s["ALL_PATTERNS"]){var c=s["ALL_PATTERNS"][i],d=c.pattern,m=c.handler,h=null;Date.now();try{h=e.match(d)}catch(y){console.error("【getBestPatternResult】正则 ".concat(i," 匹配报错:"),y);continue}Date.now();if(d&&"function"===typeof m&&h&&h[0]===e){console.log('【getBestPatternResult】✅ 正则 "'.concat(i,'" 匹配成功!')),console.log("【getBestPatternResult】匹配详情:",h),a.push(i);var f=null;Date.now();try{f=m.length>=3?m(h,e,t):m(h,e)}catch(y){console.log("【getBestPatternResult】正则 ".concat(i," handler执行出错，尝试兼容模式:"),y),f=m(h,e)}Date.now();console.log('【getBestPatternResult】正则 "'.concat(i,'" handler结果:'),f),f&&Array.isArray(f)&&f.length>0?(console.log('【getBestPatternResult】正则 "'.concat(i,'" 返回数组结果，长度:'),f.length),r.push(f)):f&&"object"===Object(o["a"])(f)?(console.log('【getBestPatternResult】正则 "'.concat(i,'" 返回对象结果')),r.push([f])):console.log('【getBestPatternResult】正则 "'.concat(i,'" 返回无效结果:'),f)}}var I={1:[1,2],2:[3,4],3:[5,6,7,100],4:[8,9],5:[10,11],6:[12,13],7:[14,15],8:[16,17],9:[18,19]},b=0,p=e&&e.match(/(\d{1,9})/);p&&(b=p[1].length),console.log("【getBestPatternResult】检测到的输入号码长度:",b);var _=I[b]||[];console.log("【getBestPatternResult】该长度对应的有效玩法ID:",_);for(var g=[],N=20;N<=31;N++)g.push(N);var U=r.filter((function(e){return e.every((function(e){return e.betNumbers&&_.includes(e.methodId)||g.includes(e.methodId)}))}));console.log("【getBestPatternResult】长度过滤后的结果数组长度:",U.length),U.length!==r.length&&console.log("【getBestPatternResult】过滤掉了",r.length-U.length,"个不符合长度要求的结果");var v=U.length>0?U:r;console.log("【getBestPatternResult】进入优先级筛选的结果数组长度:",v.length);var A=l(v,e);return console.log("【getBestPatternResult】最终选择的最佳结果:",JSON.stringify(A,null,2)),A}function d(e){var t=e.replace(/\s+/g,""),n=/(福彩|福|福家|3d|3D|福3D)/.test(t),r=/(排三|排3|体彩|体|排列3|排列三|体J|排)/.test(t);return/(福排|排福|福体|福彩体|体彩福)/.test(t)||n&&r?[1,2]:n?1:r?2:1}function m(e){console.log("【removeLotteryPrefix】原始输入:",e);var t=e.trim();if(/[*Xx]\d/.test(t))return t;var n=/^(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)/,r=/(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)$/,a=0;while(n.test(t))if(t=t.replace(n,""),t=t.trim(),a++,a>10)break;a=0;while(r.test(t))if(t=t.replace(r,""),t=t.trim(),a++,a>10)break;return t}function h(e,t){for(var n=[],r=[],a=0;a<e.length;a++){var o=e[a].trim(),i=!1;for(var s in t){var l=t[s],u=l.pattern,c=l.handler;if(u&&"function"===typeof c){var d=o.match(u);if(d&&d[0]===o){i=!0;break}}}i?(r.length>0&&(n.push(r.join(" ")),r=[]),n.push(o)):r.push(o)}return r.length>0&&n.push(r.join(" ")),n}function f(e){if(!e)return{groups:[{methodId:null,danshuang:null,daxiao:null,dingwei:null,hezhi:null,betNumbers:"",money:"",lotteryId:1}]};var t=e;t=u(t),t=/[*Xx]\d/.test(t)?t.replace(/^[\s\-/+\.,、|~!@#$%^&；'\"]+|[\s\-/+\.,、|~!@#$%^&；'\"]+$/g,""):t.replace(/^[\s\-*/+\.,、|~!@#$%^&；'\"]+|[\s\-*/+\.,、|~!@#$%^&；'\"]+$/g,""),t=t.replace(/共\s*\d+(注|元|米|块)?(\s*\+.*)?$/g,(function(e,t,n){return n||""}));var n=t.split(/\r?\n/).filter((function(e){return e.length>0}));n=h(n,s["ALL_PATTERNS"]);var r,o=new Map,l=[],f=Object(a["a"])(n);try{for(f.s();!(r=f.n()).done;){var I=r.value,b=I.match(/^(\d+)[+＋](\d+)$/);if(b){var _=b[1],g=b[2],N="".concat(_.length,"_").concat(g);o.has(N)||o.set(N,[]),o.get(N).push(I)}else l.push(I)}}catch(x){f.e(x)}finally{f.f()}var U,v=[],A=Object(a["a"])(o.values());try{for(A.s();!(U=A.n()).done;){var y=U.value;v.push(y.join("\n"))}}catch(x){A.e(x)}finally{A.f()}v=v.concat(l);var M=/^\d{3}[\-—~～@#￥%…&!+、一－。\\/*,，.＝=·\s]+\d+[+＋]\d+$/,E=/^\d{3}\/[0-9]+([+＋][0-9]+)?$/;(n.length>1&&n.every((function(e){return M.test(e)}))||n.length>1&&n.every((function(e){return E.test(e)})))&&(v=[n.join("\n")]);var D=d(e),O=m(t),S=c(O,!0);if(console.log("【handleNumberRecognition】整体识别结果:",S),S&&S.length>0){console.log("【handleNumberRecognition】✅ 整体识别成功！");var T=p(S,D);return{groups:T}}if(console.log("【handleNumberRecognition】❌ 整体识别失败，准备进入分段识别"),v.length>1){console.log("=== 【进入分段识别】 ==="),console.log("【分段识别】整体识别失败，开始分段处理");for(var L=[],w=0;w<v.length;w++){var j=v[w];console.log("\n--- 【处理第".concat(w+1,"段】 ---"));var R=m(j);if(R){console.log("【分段识别】去前缀后内容:",R);var $=c(R,!1);if(console.log("【分段识别】该段识别结果:",$),$&&$.length>0)console.log("【分段识别】该段识别成功，添加到结果中"),console.log("【分段识别】该段识别详情:",JSON.stringify($,null,2)),L.push.apply(L,Object(i["a"])($));else{console.log("【分段识别】该段识别失败，使用兜底逻辑");var Z={methodId:null,betNumbers:j,money:"",lotteryId:D};console.log("【分段识别】兜底结果:",JSON.stringify(Z,null,2)),L.push(Z)}console.log("【分段识别】第".concat(w+1,"段处理完成，当前总结果数:"),L.length)}else console.log("【分段识别】该段去前缀后为空，跳过")}console.log("\n=== 【分段识别完成】 ==="),console.log("【分段识别】最终合并结果总数:",L.length),console.log("【分段识别】最终合并结果详情:",JSON.stringify(L,null,2));var H=p(L,D);return console.log("【分段识别】补充彩种ID后的最终结果:",JSON.stringify(H,null,2)),{groups:H}}console.log("\n=== 【进入兜底逻辑】 ==="),console.log("【handleNumberRecognition】整体识别和分段识别都失败，使用兜底逻辑"),console.log("【handleNumberRecognition】splitGroups长度:",v.length);var P={groups:[{methodId:null,betNumbers:t,money:"",lotteryId:D}]};return console.log("【handleNumberRecognition】兜底逻辑返回结果:",JSON.stringify(P,null,2)),P}function I(e){return f(e)}function b(e){if(!e)return{groups:[]};var t=e.split(/\r?\n/).map((function(e){return e.trim()})).filter(Boolean);if(t.length>0&&t.every((function(e){return/^\d+$/.test(e)}))){var n=c(t.join("\n"));return n&&n.length>0?{groups:n}:{groups:t.map((function(e){return{methodId:null,betNumbers:e,money:"",lotteryId:1}}))}}var r,o=[],i=Object(a["a"])(t);try{for(i.s();!(r=i.n()).done;){var s=r.value;if(s){var l=c(s);l&&l.length>0?o=o.concat(l):o.push({methodId:null,betNumbers:s,money:"",lotteryId:1})}}}catch(u){i.e(u)}finally{i.f()}return{groups:o}}function p(e,t){if(!e)return e;if(Array.isArray(e)&&e.length>0&&e.every((function(e){return void 0!==e.lotteryId&&null!==e.lotteryId})))return e;if(Array.isArray(t)){var n,o=[],i=Object(a["a"])(t);try{var s=function(){var t=n.value;e.forEach((function(e){o.push(Object(r["a"])(Object(r["a"])({},e),{},{lotteryId:t}))}))};for(i.s();!(n=i.n()).done;)s()}catch(l){i.e(l)}finally{i.f()}return o}return e.map((function(e){return Object(r["a"])(Object(r["a"])({},e),{},{lotteryId:t})}))}},b9e9:function(e,t,n){},ce3b:function(e,t,n){"use strict";n.r(t),n.d(t,"SINGLE_CODE_PATTERNS",(function(){return s}));var r=n("3835"),a=n("b85c"),o=(n("4de4"),n("a15b"),n("d81d"),n("14d9"),n("fb6a"),n("4ec9"),n("b64b"),n("d3b7"),n("4d63"),n("c607"),n("ac1f"),n("2c3e"),n("00b4"),n("25f0"),n("8a79"),n("3ca3"),n("466d"),n("5319"),n("1276"),n("498a"),n("0643"),n("2382"),n("4e3e"),n("a573"),n("9a9a"),n("159b"),n("ddb0"),n("f9d6")),i={"百":100,"十":101,"个":102},s={DINGWEI_POSITION_FIRST:{pattern:/^([百十个])位(\d)\s*(\d+)$/,handler:function(e,t){var n=e[1],r=e[2],a=e[3];if(1!==r.length)return null;var s=[{methodId:o["METHOD_ID"].YIMA_DINGWEI,betNumbers:r,dingwei:i[n],money:a}];return console.log("一码 handler 返回:",s||results||returnValue),s||results||returnValue}},DINGWEI_NUMBER_FIRST:{pattern:/^(\d)([百十个])-(\d+)$/,handler:function(e,t){var n=e[1],r=e[2],a=e[3];if(1!==n.length)return null;var s=[{methodId:o["METHOD_ID"].YIMA_DINGWEI,betNumbers:n,dingwei:i[r],money:a}];return console.log("一码 handler 返回:",s||results||returnValue),s||results||returnValue}},DINGWEI_POSITION_LAST:{pattern:/^(\d)-(\d+)-([百十个])$/,handler:function(e,t){var n=e[1],r=e[2],a=e[3];if(1!==n.length)return null;var s=[{methodId:o["METHOD_ID"].YIMA_DINGWEI,betNumbers:n,dingwei:i[a],money:r}];return console.log("一码 handler 返回:",s||results||returnValue),s||results||returnValue}},DINGWEI_MULTI:{pattern:/^(?:[（(]?(?:毒|独胆|独|毒胆)[）)]?[、,，.。\\/*\s\n\r\-~一－。～@#￥%…&!、＝=·]*)?(\d(?:[^0-9]+\d)*?)(?:各(?:打)?)?(\d+)(?:块|元)?$/,handler:function(e,t){if(/跨|跨度/.test(t))return null;var r=t.match(/\d+/g)||[];if(r.length<2)return null;var a=r[r.length-1],o=r.slice(0,-1);return!o.length||o.some((function(e){return 1!==e.length}))?null:[{methodId:n("f9d6").METHOD_ID.DUDAN,betNumbers:o.join(","),money:a}]}},DUDAN_MULTI:{pattern:/^(\d(?:[^0-9]+\d)*?)(?:各(?:打)?)?(\d+)(?:块)?$/,handler:function(e,t){if(/跨|跨度/.test(t))return null;var r=t.match(/\d+/g)||[];if(r.length<2)return null;var a=r[r.length-1],o=r.slice(0,-1);return!o.length||o.some((function(e){return 1!==e.length}))?null:[{methodId:n("f9d6").METHOD_ID.DUDAN,betNumbers:o.join(","),money:a}]}},DUDAN_COMMON_EXPRESSIONS:{pattern:/^([\d,\s\/]+?)[买打=\/-]+0*(\d+)$/,handler:function(e,t){var r=e[1],a=e[2].replace(/^0+/,""),o=r.split(/[\s,\/]+/).filter(Boolean);return!o.length||o.some((function(e){return 1!==e.length}))?null:[{methodId:n("f9d6").METHOD_ID.DUDAN,betNumbers:o.join(","),money:a}]}},DUDAN_SPECIAL:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s各打吊赶]*(\d)(?:[\-—~～@#￥%…&!、\\/*,，.＝=·\s各打吊赶]+(\d))*[\-—~～@#￥%…&!、\\/*,，.＝=·\s各打吊赶]+(\d+)(?:元|米|块)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s各打吊赶]*$/,handler:function(e,t,n){var r=t.match(/\d+/g)||[];if(r.length<2)return null;var a=r[r.length-1],i=r.slice(0,-1);return!i.length||i.some((function(e){return 1!==e.length}))?null:[{methodId:o["METHOD_ID"].DUDAN,betNumbers:i.join(","),money:a}]}},DUDAN_SINGLE:{pattern:/^(\d)-(\d+)$/,handler:function(e,t){var n=e[1],r=e[2];return n&&1===n.length?[{methodId:o["METHOD_ID"].DUDAN,betNumbers:n,money:r}]:null}},DUDAN_DIAO:{pattern:/^(?:吊)?(\d)(?:吊|各)?(\d+)$/,handler:function(e,t){var n=e[1],r=e[2];if(!n||1!==n.length)return null;if(/\d{2,}/.test(n)||/\d{2,}/.test(t.split("-")[0]))return null;if(t&&t.split("-")[0]&&1!==t.split("-")[0].replace(/[^0-9]/g,"").length)return null;var a=[{methodId:o["METHOD_ID"].DUDAN,betNumbers:n,money:r}];return console.log("一码 handler 返回:",a||results||returnValue),a||results||returnValue}},DUDAN_KEYWORD_EXPRESSIONS:{pattern:/^([\d,，.。\s\/]+)[独胆码挑]+[，.。\s\/]*([0-9]+)$/,handler:function(e,t){var r=(e[1].match(/\d+/g)||[]).filter((function(e){return 1===e.length})),a=e[2].replace(/^0+/,"");return!r.length||r.some((function(e){return 1!==e.length}))?null:[{methodId:n("f9d6").METHOD_ID.DUDAN,betNumbers:r.join(","),money:a}]}},DUDAN_UNIVERSAL_SUPER:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*(?:毒|独胆|独|毒胆)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*(\d)(?:[\-—~～@#￥%…&!、\\/*,，.＝=·\s]+(\d))*[\-—~～@#￥%…&!、\\/*,，.＝=·\s]+(\d+)(?:元|米|块)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*$/,handler:function(e,t){var r=n("f9d6"),a=r.METHOD_ID,o=t.match(/\d+/g)||[];if(o.length<2)return null;var i=o[o.length-1],s=o.slice(0,-1);return!s.length||s.some((function(e){return 1!==e.length}))?null:[{methodId:a.DUDAN,betNumbers:s.join(","),money:i}]}},DUDAN_MULTI_LINE_COMPLEX:{pattern:/^((?:[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:独胆|毒|独|毒胆)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*\d[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*\d+(?:元|米|块)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*\n[\s\S]*)+)$/m,handler:function(e,t){var o,i=n("f9d6"),s=i.METHOD_ID,l=t.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),u=[],c=/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:独胆|毒|独|毒胆)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|米|块)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*$/,d=Object(a["a"])(l);try{for(d.s();!(o=d.n()).done;){var m=o.value,h=m.match(c);if(!h)return console.log('【DUDAN_MULTI_LINE_COMPLEX】行 "'.concat(m,'" 不匹配独胆格式，返回null')),null;var f=h[1],I=h[2];if(1!==f.length)return console.log('【DUDAN_MULTI_LINE_COMPLEX】行 "'.concat(m,'" 号码不是1位数字，返回null')),null;u.push({line:m,number:f,money:I})}}catch(O){d.e(O)}finally{d.f()}console.log("【DUDAN_MULTI_LINE_COMPLEX】所有 ".concat(u.length," 行都是独胆格式，继续处理"));for(var b=new Map,p=0,_=u;p<_.length;p++){var g=_[p],N=g.number,U=g.money;b.has(U)||b.set(U,[]),b.get(U).push(N)}var v,A=[],y=Object(a["a"])(b.entries());try{for(y.s();!(v=y.n()).done;){var M=Object(r["a"])(v.value,2),E=M[0],D=M[1];D.length>0&&A.push({methodId:s.DUDAN,betNumbers:D.join(","),money:E})}}catch(O){y.e(O)}finally{y.f()}return console.log("【DUDAN_MULTI_LINE_COMPLEX】处理完成，返回 ".concat(A.length," 个结果组")),A.length>0?A:null}},DUDAN_UNIVERSAL_SUPER_NUMBER:{pattern:/^([0-9]{1,})\s+(\d+)(元|块|米)?$/,handler:function(e,t){var r=e[1],a=e[2];return r&&1===r.length?[{methodId:n("f9d6").METHOD_ID.DUDAN,betNumbers:r,money:a}]:null}},YIMA_DINGWEI_POSITION:{pattern:/^(百位|十位|个位)[：: ]?(\d)\+(\d+)[,，\s]*$/m,handler:function(e,t){var r,o=t.split(/\n|\r/).map((function(e){return e.trim()})).filter(Boolean),i=[],s=Object(a["a"])(o);try{for(s.s();!(r=s.n()).done;){var l=r.value,u=l.match(/^(百位|十位|个位)[：: ]?(\d)\+(\d+)[,，\s]*$/);if(u){var c=u[1],d=u[2],m=u[3],h=void 0;if("百位"===c)h=100;else if("十位"===c)h=101;else{if("个位"!==c)continue;h=102}i.push({methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,betNumbers:d,dingwei:h,money:m})}}}catch(f){s.e(f)}finally{s.f()}return i.length?i:null}},DINGWEI_MULTI_POSITION_NUMBER_MONEY:{pattern:/^([个十百]{2,3})[，,、\s]+(\d)--(\d+)$/,handler:function(e,t){var n=e[1].split(""),r=e[2],a=e[3],s=n.map((function(e){return{methodId:o["METHOD_ID"].YIMA_DINGWEI,betNumbers:r,dingwei:i[e],money:a}}));return s}},DUDAN_KEYWORD:{pattern:/^(?:独胆|胆|独)(\d)[-/+吊打赶 ]+(\d+)$/,handler:function(e,t){var n=e[1],r=e[2];return 1!==n.length?null:[{methodId:o["METHOD_ID"].DUDAN,betNumbers:n,money:r}]}},DINGWEI_POSITION_NUMBER_DIAO:{pattern:/^(百位|十位|个位)(\d)吊(\d+)$/,handler:function(e,t){var n,r=e[1],a=e[2],i=e[3];if("百位"===r)n=100;else if("十位"===r)n=101;else{if("个位"!==r)return null;n=102}return[{methodId:o["METHOD_ID"].YIMA_DINGWEI,betNumbers:a,dingwei:n,money:i}]}},DUDAN_DANMA_DIAO:{pattern:/^胆码(\d)吊(\d+)$/,handler:function(e,t){var n=e[1],r=e[2];return 1!==n.length?null:[{methodId:o["METHOD_ID"].DUDAN,betNumbers:n,money:r}]}},DUDAN_DU_DIAO:{pattern:/^独(\d)吊(\d+)$/,handler:function(e,t){var n=e[1],r=e[2];return 1!==n.length?null:[{methodId:o["METHOD_ID"].DUDAN,betNumbers:n,money:r}]}},DUDAN_MULTI_KEYWORD_SUPER:{pattern:/((独胆|胆|独)\d[打吊赶各]+[，,、.。\\/*\s\n\r\-~一－。～@#￥%…&!、＝=·]*\d+[，,、.。\\/*\s\n\r\-~一－。～@#￥%…&!、＝=·]*)+/,handler:function(e,t){var n,r=/(独胆|胆|独)(\d)[打吊赶各]+[，,、.。\\/*\s\n\r\-~～@#￥%…&!、＝=·]*(\d+)/g,a=[];while(null!==(n=r.exec(t)))a.push({methodId:o["METHOD_ID"].DUDAN,betNumbers:n[2],money:n[3]});return a.length?a:null}},DINGWEI_MULTI_POSITION_NUMBER_KEYWORD_SUPER:{pattern:/((百位|十位|个位)\d[打吊赶各]+[，,、.。\\/*\s\n\r\-~一－。～@#￥%…&!、＝=·]*\d+[，,、.。\\/*\s\n\r\-~一－。～@#￥%…&!、＝=·]*)+/,handler:function(e,t){var n,r=/(百位|十位|个位)(\d)[打吊赶各]+[，,、.。\\/*\s\n\r\-~一－。～@#￥%…&!、＝=·]*(\d+)/g,a=[];while(null!==(n=r.exec(t))){var i=void 0;if("百位"===n[1])i=100;else if("十位"===n[1])i=101;else{if("个位"!==n[1])continue;i=102}a.push({methodId:o["METHOD_ID"].YIMA_DINGWEI,betNumbers:n[2],dingwei:i,money:n[3]})}return a.length?a:null}},BAOZI_ALL_SUPER:{pattern:/^豹子[各]*[，,、.。\/*\s\n\r\-~一－。～@#￥%…&!、＝=·]*(\d+)[，,、.。\/*\s\n\r\-~一－。～@#￥%…&!、＝=·]*$/,handler:function(e,t){var n=["000","111","222","333","444","555","666","777","888","999"],r=e[1];return/各/.test(t)||(r=(parseInt(r,10)/10).toString()),[{methodId:o["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:r}]}},BAOZI_DIAO_DIVIDE_TEN:{pattern:/^豹子[打吊赶杀]+[，,、.。\\/*\s\n\r\-~一－。～@#￥%…&!、＝=·]*(\d+)[，,、.。\\/*\s\n\r\-~一－。～@#￥%…&!、＝=·]*$/,handler:function(e,t){var n=["000","111","222","333","444","555","666","777","888","999"],r=parseInt(e[1]),a=Math.floor(r/10);return[{methodId:o["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:a.toString()}]}}};Object.keys(s).forEach((function(e){var t=s[e].pattern;"string"===typeof t&&t.endsWith("$")?s[e].pattern=t.replace(/\$$/,"[，,./*-=。·、s]*$"):t instanceof RegExp&&t.source.endsWith("$")&&(s[e].pattern=new RegExp(t.source.replace(/\$$/,"[，,./*-=。·、s]*$")))}))},d7b7:function(e,t,n){"use strict";n("b9e9")},db21:function(e,t,n){},f398:function(e,t,n){"use strict";n.r(t),n.d(t,"SINGLE_DOUBLE_PATTERNS",(function(){return s}));var r=n("2909"),a=n("b85c"),o=n("3835"),i=(n("99af"),n("4de4"),n("7db0"),n("a630"),n("caad"),n("a15b"),n("d81d"),n("14d9"),n("13d5"),n("4ec9"),n("b680"),n("b64b"),n("d3b7"),n("4d63"),n("c607"),n("ac1f"),n("2c3e"),n("00b4"),n("25f0"),n("8a79"),n("2532"),n("3ca3"),n("466d"),n("5319"),n("1276"),n("498a"),n("0643"),n("2382"),n("fffc"),n("4e3e"),n("a573"),n("9d4a"),n("159b"),n("ddb0"),n("f9d6")),s={BAODA_ZUSAN:{pattern:/^包打?组三(?:-(\d+))?$/,handler:function(e,t){var n=e[1]||"",r=(Object(i["parseLotteryType"])(t),[{methodId:i["METHOD_ID"].BAODA_ZUSAN,betNumbers:"",money:n}]);return console.log("包打组三 handler 返回:",r),r}},HEZHI_MULTI:{pattern:/^和值(?:(\d+(?:\/\d+)*?)各(\d+)|((?:\d+-\d+\/)*\d+-\d+))$/,handler:function(e,t){Object(i["parseLotteryType"])(t);if(e[1]){var n=e[1].split("/"),r=e[2],a=n.map((function(e){return{methodId:i["METHOD_ID"].HEZHI,hezhi:parseInt(e),money:r}}));return console.log("和值多个 handler 返回:",a),a}var s=e[3].split("/").map((function(e){var t=e.split("-"),n=Object(o["a"])(t,2),r=n[0],a=n[1];return{number:r,money:a}})),l=s.map((function(e){return{methodId:i["METHOD_ID"].HEZHI,hezhi:parseInt(e.number),money:e.money}}));return console.log("和值多个 handler 返回:",l),l}},HEZHI_SINGLE_DA_SUPER:{pattern:/^(和值?|和)(\d{1,2})[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s+吊赶打各]+(\d+)$/,handler:function(e,t){var r=n("f9d6"),a=r.getHezhiMethodId,o=r.parseLotteryType,i=r.METHOD_ID,s=e[2],l=e[3],u=(o(t),{methodId:a(s,i),hezhi:parseInt(s),money:l});return console.log("和值单个打（超级分隔符）handler 返回:",u),[u]}},HEZHI_MULTI_EACH_DA_SUPER:{pattern:/^(和值?|和)((?:\d{1,2}[\-—~～@#￥%…&一－。 !、\\/*,，.＝=·\s+吊赶打各\/\\]*)+)[各\-—~～@#￥%…&!、\\/*,一－。，.＝=·\s+吊赶打]+(\d+)$/,handler:function(e,t){var r=n("f9d6"),a=r.getHezhiMethodId,o=r.SUPER_SEPARATORS,i=r.parseLotteryType,s=r.METHOD_ID,l=e[2].split(o).map((function(e){return e.trim()})).filter(Boolean),u=e[3],c=(i(t),l.map((function(e){return{methodId:a(e,s),hezhi:parseInt(e),money:u}})));return console.log("和值各打（超级分隔符）handler 返回:",c),c}},PAO_DA_MONEY:{pattern:/^炮打(\d+)(元|米|块)?$/,handler:function(e,t){var n=["000","111","222","333","444","555","666","777","888","999"];return[{methodId:i["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:"1"}]}},PAO_GE_DA_MONEY:{pattern:/^炮各打(\d+)(元|米|块)?$/,handler:function(e,t){var n=["000","111","222","333","444","555","666","777","888","999"],r=e[1];return[{methodId:i["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:r}]}},PAO_GE_MONEY:{pattern:/^炮各(\d+)(元|米|块)?$/,handler:function(e,t){var n=["000","111","222","333","444","555","666","777","888","999"],r=e[1];return[{methodId:i["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:r}]}},PAO_MONEY:{pattern:/^炮(\d+)(元|米|块)?$/,handler:function(e,t){var n=["000","111","222","333","444","555","666","777","888","999"];return[{methodId:i["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:"1"}]}},PAO_GE_MONEY_GONG:{pattern:/^炮各(\d+)(元|米|块)?共\d+$/,handler:function(e,t){var n=["000","111","222","333","444","555","666","777","888","999"],r=e[1];return[{methodId:i["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:r}]}},TONGPAO_ALLPAO_FANGPAO_MONEY:{pattern:/^(通炮|全炮|防炮)(\d+)(元|米|块)?$/,handler:function(e,t){var n=["000","111","222","333","444","555","666","777","888","999"],r=(parseFloat(e[2])/10).toFixed(1).replace(/\.0$/,"");return[{methodId:i["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:r}]}},TONGPAO_ALLPAO_FANGPAO_GE_MONEY:{pattern:/^(通炮|全炮|防炮)各(\d+)(元|米|块)?$/,handler:function(e,t){var n=["000","111","222","333","444","555","666","777","888","999"],r=e[2];return[{methodId:i["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:r}]}},PAO_09_SLASH_GE_MONEY:{pattern:/^炮0-9[\/][\s]*各?(\d+)(元|米|块)?$/,handler:function(e,t){var n=["000","111","222","333","444","555","666","777","888","999"],r=e[1];return[{methodId:i["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:r}]}},PAO_09_KONG_GE_MONEY:{pattern:/^0[\s]*[-到~—]?[\s]*9[\s]*炮[\s]*各?(\d+)(元|米|块)?$/,handler:function(e,t){var n=["000","111","222","333","444","555","666","777","888","999"],r=e[1];return[{methodId:i["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:r}]}},KONGPAO_GE_MONEY:{pattern:/^空炮[各个]?(\d+)(元|米|块)?$/,handler:function(e,t){var n=["000","111","222","333","444","555","666","777","888","999"],r=e[1];return[{methodId:i["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:r}]}},BAOZI_MULTI_EACH_MONEY:{pattern:/^((?:\d{3}[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*)+)豹子各?(\d+)(元|米|块)?$/,handler:function(e,t){var n=e[1].split(/[\-—~～@#￥%…&!、\\/*,，.＝=·\s]+/).map((function(e){return e.trim()})).filter((function(e){return 3===e.length&&e[0]===e[1]&&e[1]===e[2]})),r=e[2];return[{methodId:i["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:r}]}},BAOZI_MULTI_MONEY:{pattern:/^((?:\d{3}[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*)+)豹子(\d+)(元|米|块)?$/,handler:function(e,t){var n=e[1].split(/[\-—~～@#￥%…&!、\\/*,，.＝=·\s]+/).map((function(e){return e.trim()})).filter((function(e){return 3===e.length&&e[0]===e[1]&&e[1]===e[2]}));return[{methodId:i["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:"1"}]}},PAO_SUPERSEP_MONEY:{pattern:/^炮[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s.。·、]+(\d+)(元|米|块)?$/,handler:function(e,t){var n=["000","111","222","333","444","555","666","777","888","999"];return[{methodId:i["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:"1"}]}},QUANPAO_MONEY:{pattern:/^全炮(各)?(\d+)(元|米|块)?$/,handler:function(e,t){var n=["000","111","222","333","444","555","666","777","888","999"],r=e[2];return[{methodId:i["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:r}]}},BAOZI_ALL_MONEY:{pattern:/^0到9豹子全包(\d+)(?:元|米|块)?$/,handler:function(e,t){var n=["000","111","222","333","444","555","666","777","888","999"],r=e[1];return/各/.test(t)||(r=(parseFloat(r)/10).toFixed(1).replace(/\.0$/,"")),[{methodId:i["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:r}]}},PAO_OTHER:{pattern:/^炮.*?各(\d+)(?:元|米|块)?$/,handler:function(e,t){var n=["000","111","222","333","444","555","666","777","888","999"];return[{methodId:i["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:e[1]}]}},BAODA_ZUSAN_VARIANTS:{pattern:/^(包?对子|对子|对吊|对一|对)[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([0-9]+)(元|块|米)?$/,handler:function(e,t){var n=e[2],r={"一":1,"二":2,"三":3,"四":4,"五":5,"六":6,"七":7,"八":8,"九":9,"十":10,"百":100,"千":1e3,"万":1e4},a=n;return/^[一二三四五六七八九十百千万]+$/.test(n)&&(a=n.split("").reduce((function(e,t){return e+(r[t]||0)}),0)||"1"),/^\d+$/.test(n)&&(a=n),a||(a="1"),[{methodId:i["METHOD_ID"].BAODA_ZUSAN,betNumbers:"",money:a}]}},HEZHI_SIMPLE:{pattern:/^(?:福|福彩|3D|体|体彩|排三|排)?和([\d/\-、,，.。\s]+)[/\-、,，.。\s]+(\d+)(元|块|米)?$/,handler:function(e,t){var r=n("f9d6"),a=r.getHezhiMethodId,o=r.METHOD_ID,i=r.parseLotteryType,s=e[1],l=e[2],u=(i(t),s.split(/[\/\-、,，.。\s]+/).map((function(e){return e.trim()})).filter(Boolean));return u.map((function(e){return{methodId:a(e,o),hezhi:parseInt(e),money:l}}))}},BAO_ZULIU_ALL:{pattern:/^(包打?组六|组六)[\-—~～@#￥%…&!、\\/*,一－。＝=·吊赶打各\s]*([0-9]+)$/,handler:function(e,t){var n=e[2]||"";Object(i["parseLotteryType"])(t);return[{methodId:i["METHOD_ID"].BAODA_ZULIU,betNumbers:"",money:n}]}},BAO_ZUSAN_ALL:{pattern:/^(包打?组三|组三)[\-—~～@#￥%…&!、\\/*,一－。＝=·吊赶打各\s]*([0-9]+)$/,handler:function(e,t){var n=e[2]||"";Object(i["parseLotteryType"])(t);return[{methodId:i["METHOD_ID"].BAODA_ZUSAN,betNumbers:"",money:n}]}},SHA_MA_SUPER:{pattern:/^杀([0-9]+)[\-—~～@#￥%…&!、\\/*,一－。.＝=·吊赶打各\s]+([0-9]+)[\-—~～@#￥%…&!、\\/*,一－。.＝=·吊赶打各\s]*$/,handler:function(e,t){var n=e[1].split("").join(","),r=e[2];return[{methodId:i["METHOD_ID"].SHA_MA,betNumbers:n,money:r}]}},HEZHI_DA_SUPER:{pattern:/^[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*(?:和值?|和)?[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*大[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([0-9]+)$/,handler:function(e,t){var n=e[1];Object(i["parseLotteryType"])(t);return[{methodId:i["METHOD_ID"].HEZHI_DAXIAO,daxiao:300,money:n,betNumbers:null}]}},HEZHI_XIAO_SUPER:{pattern:/^[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*(?:和值?|和)?[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*小[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([0-9]+)$/,handler:function(e,t){var n=e[1];Object(i["parseLotteryType"])(t);return[{methodId:i["METHOD_ID"].HEZHI_DAXIAO,daxiao:301,money:n,betNumbers:null}]}},HEZHI_DAN_SUPER:{pattern:/^[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*(?:和值?|和)?[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*单[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([0-9]+)$/,handler:function(e,t){var n=e[1];Object(i["parseLotteryType"])(t);return[{methodId:i["METHOD_ID"].HEZHI_DANSHUAN,danshuang:200,money:n,betNumbers:null}]}},HEZHI_SHUANG_SUPER:{pattern:/^[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*(?:和值?|和)?[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*双[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([0-9]+)$/,handler:function(e,t){var n=e[1];Object(i["parseLotteryType"])(t);return[{methodId:i["METHOD_ID"].HEZHI_DANSHUAN,danshuang:201,money:n,betNumbers:null}]}},BAOZI_MULTI_LINE_EACH_MONEY:{pattern:/^([\s\S]+)$/m,handler:function(e,t){var i,s=t.split(/\n+/).map((function(e){return e.trim()})).filter(Boolean),l=new Map,u=[],c=/^([0-9]{1,9})[ \t]*(豹子|炮)[ \t]*各([0-9]+)(元|米|块)?[^\d]*$/,d=Object(a["a"])(s);try{for(d.s();!(i=d.n()).done;){var m=i.value,h=m.match(c);if(!h)return console.log('【BAOZI_MULTI_LINE_EACH_MONEY】行 "'.concat(m,'" 不匹配豹子/炮格式，返回null')),null;u.push({line:m,match:h})}}catch(U){d.e(U)}finally{d.f()}console.log("【BAOZI_MULTI_LINE_EACH_MONEY】所有 ".concat(u.length," 行都是豹子/炮格式，继续处理"));for(var f=0,I=u;f<I.length;f++){var b,p=I[f].match,_=p[1].split("").filter((function(e){return/[0-9]/.test(e)})),g=p[3];l.has(g)||l.set(g,[]),(b=l.get(g)).push.apply(b,Object(r["a"])(_.map((function(e){return e+e+e}))))}if(0===l.size)return console.log("【BAOZI_MULTI_LINE_EACH_MONEY】没有有效的豹子/炮内容，返回null"),null;var N=Array.from(l.entries()).map((function(e){var t=Object(o["a"])(e,2),r=t[0],a=t[1];return{methodId:n("f9d6").METHOD_ID.SANMA_ZHIXUAN,betNumbers:a.join(","),money:r}}));return console.log("【BAOZI_MULTI_LINE_EACH_MONEY】处理完成，返回 ".concat(N.length," 个结果组")),N}},YIMA_DANSHUAN_SUPER:{pattern:/^(百位|百|十位|十|个位|个)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*(单|双)(\d+)(元|米|块)?$/,handler:function(e,t){var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},a=e[1],o=e[2],i=e[3];return[{methodId:n("f9d6").METHOD_ID.YIMA_DANSHUAN,dingwei:r[a],betNumbers:o,money:i}]}},YIMA_DAXIAO_SUPER:{pattern:/^(百位|百|十位|十|个位|个)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*(大|小)(\d+)(元|米|块)?$/,handler:function(e,t){var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},a=e[1],o=e[2],i=e[3];return[{methodId:n("f9d6").METHOD_ID.YIMA_DAXIAO,dingwei:r[a],betNumbers:o,money:i}]}},SPECIAL_FU_PAI_PAO:{pattern:/[。.,、\\/*\s\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([福3d3D福彩]+)[炮豹子]+(\d+)(元|米|块)?[。.,、\\/*\\s\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([排排三体体彩]+)[炮豹子]+(\d+)(元|米|块)?/,handler:function(e,t){var r=["000","111","222","333","444","555","666","777","888","999"],a=e[2],o=e[5],i=1,s=2;return[{methodId:n("f9d6").METHOD_ID.SANMA_ZHIXUAN,betNumbers:r.join(","),money:(parseFloat(a)/10).toString(),lotteryId:i},{methodId:n("f9d6").METHOD_ID.SANMA_ZHIXUAN,betNumbers:r.join(","),money:(parseFloat(o)/10).toString(),lotteryId:s}]}},SPECIAL_FU_PAI_PAO_EACH:{pattern:/[。.,、\\/*\s\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([福3d3D福彩]+)[炮豹子]+各(\d+)(元|米|块)?[。.,、\\/*\s\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([排排三体体彩]+)[炮豹子]+各(\d+)(元|米|块)?/,handler:function(e,t){var r=["000","111","222","333","444","555","666","777","888","999"],a=e[2],o=e[5],i=1,s=2;return[{methodId:n("f9d6").METHOD_ID.SANMA_ZHIXUAN,betNumbers:r.join(","),money:a,lotteryId:i},{methodId:n("f9d6").METHOD_ID.SANMA_ZHIXUAN,betNumbers:r.join(","),money:o,lotteryId:s}]}},YIMA_DINGWEI_FORMAT1_NEW:{pattern:/^(百位?|百)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*([0-9]+(?:[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*[0-9]+)*)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*(十位?|十)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*([0-9]+(?:[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*[0-9]+)*)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*(个位?|个)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*([0-9]+(?:[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*[0-9]+)*)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*各[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*([0-9]+)(?:元|块|快|米)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*(一码定位|一码|定)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*$/,handler:function(e,t){if(!e[1]||!e[3]||!e[5])return null;if(!e[8])return null;var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},a=(e[2].match(/[0-9]/g)||[]).join(","),o=(e[4].match(/[0-9]/g)||[]).join(","),i=(e[6].match(/[0-9]/g)||[]).join(","),s=e[7];return[{methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[e[1]],betNumbers:a,money:s},{methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[e[3]],betNumbers:o,money:s},{methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[e[5]],betNumbers:i,money:s}]}},YIMA_DINGWEI_FORMAT2_NEW:{pattern:/^([\s\S]+)$/m,handler:function(e,t){if(!/(?:百位?|百)|(?:十位?|十)|(?:个位?|个)/.test(t))return null;if(!/一码定位|一码|定/.test(t))return null;var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},o=t.split(/\n+/).map((function(e){return e.trim()})).filter(Boolean),i="",s={},l=/^(百位?|百|十位?|十|个位?|个)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*([0-9]+(?:[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*[0-9]+)*)/,u=/各[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*([0-9]+)(?:元|块|快|米)?/,c=/一码定位|一码|定/,d=t.match(u);d&&(i=d[1]);var m,h=Object(a["a"])(o);try{for(h.s();!(m=h.n()).done;){var f=m.value;if(!u.test(f)&&!c.test(f)){var I=f.match(l);if(I){var b=I[1],p=I[2],_=p.match(/[0-9]/g);_&&_.length>0&&(s[b]=_.join(","))}else if(f.trim()&&!/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*$/.test(f))return console.log('【YIMA_DINGWEI_FORMAT2_NEW】行 "'.concat(f,'" 不匹配一码定位格式，返回null')),null}}}catch(v){h.e(v)}finally{h.f()}var g=Object.keys(s).find((function(e){return e.includes("百")})),N=Object.keys(s).find((function(e){return e.includes("十")})),U=Object.keys(s).find((function(e){return e.includes("个")}));return g&&N&&U?(console.log("【YIMA_DINGWEI_FORMAT2_NEW】识别成功，三个位置都有数据"),[{methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[g],betNumbers:s[g],money:i},{methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[N],betNumbers:s[N],money:i},{methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[U],betNumbers:s[U],money:i}]):(console.log("【YIMA_DINGWEI_FORMAT2_NEW】未找到完整的三个位置，返回null"),null)}},YIMA_DINGWEI_FORMAT3_NEW:{pattern:/^([\s\S]+)$/m,handler:function(e,t){if(!/(?:百位?|百)|(?:十位?|十)|(?:个位?|个)/.test(t))return null;if(!/一码定位|一码|定/.test(t))return null;var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},a=t.split(/\n+/).map((function(e){return e.trim()})).filter(Boolean);if(a.length<2)return console.log("【YIMA_DINGWEI_FORMAT3_NEW】行数不足，返回null"),null;var o=a[0],i=o.match(/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*(百位?|百)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]+(十位?|十)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]+(个位?|个)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*$/);if(!i)return console.log('【YIMA_DINGWEI_FORMAT3_NEW】表头行 "'.concat(o,'" 不匹配格式，返回null')),null;for(var s="",l=1;l<a.length;l++)if(/^[\d\-—~～@#￥%…&!、\\/*,，.＝=·\s]+$/.test(a[l])){s=a[l];break}if(!s)return console.log("【YIMA_DINGWEI_FORMAT3_NEW】未找到数字行，返回null"),null;for(var u=/各[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*([0-9]+)(?:元|块|快|米)?/,c=/一码定位|一码|定/,d=1;d<a.length;d++){var m=a[d];if(m!==s&&(!u.test(m)&&!c.test(m)&&!/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*$/.test(m)))return console.log('【YIMA_DINGWEI_FORMAT3_NEW】行 "'.concat(m,'" 不符合表格格式，返回null')),null}var h=s.split(/[\-—~～@#￥%…&!、\\/*,，.＝=·\s]+/).filter(Boolean);if(3!==h.length)return console.log("【YIMA_DINGWEI_FORMAT3_NEW】数字行分组数量不是3个，实际".concat(h.length,"个，返回null")),null;var f="",I=t.match(u);I&&(f=I[1]);var b=(h[0].match(/[0-9]/g)||[]).join(","),p=(h[1].match(/[0-9]/g)||[]).join(","),_=(h[2].match(/[0-9]/g)||[]).join(",");return console.log("【YIMA_DINGWEI_FORMAT3_NEW】表格格式识别成功"),[{methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[i[1]],betNumbers:b,money:f},{methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[i[2]],betNumbers:p,money:f},{methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[i[3]],betNumbers:_,money:f}]}},YIMA_DINGWEI_TABLE_STANDARD:{pattern:/^([\s\S]+)$/m,handler:function(e,t){if(!/(?:百位?|百)|(?:十位?|十)|(?:个位?|个)/.test(t))return null;if(!/一码定位|一码|定/.test(t))return null;var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},a=t.split(/\n+/).map((function(e){return e.trim()})).filter(Boolean);if(a.length<2)return console.log("【YIMA_DINGWEI_TABLE_STANDARD】行数不足，返回null"),null;var o=a[0],i=o.match(/^[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*(百位?|百)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]+(十位?|十)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]+(个位?|个)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*$/);if(!i)return console.log('【YIMA_DINGWEI_TABLE_STANDARD】表头行 "'.concat(o,'" 不匹配格式，返回null')),null;for(var s="",l=1;l<a.length;l++)if(/^[\d\s\-—~～@#￥%…&!、\\/*,，.＝=·]+$/.test(a[l])){s=a[l];break}if(!s)return console.log("【YIMA_DINGWEI_TABLE_STANDARD】未找到数字行，返回null"),null;for(var u=/各[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+)(?:元|块|快|米)?/,c=/一码定位|一码|定/,d=1;d<a.length;d++){var m=a[d];if(m!==s&&(!u.test(m)&&!c.test(m)&&!/^[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*$/.test(m)))return console.log('【YIMA_DINGWEI_TABLE_STANDARD】行 "'.concat(m,'" 不符合标准表格格式，返回null')),null}var h=s.split(/[\s\-—~～@#￥%…&!、\\/*,，.＝=·]+/).filter(Boolean);if(3!==h.length)return console.log("【YIMA_DINGWEI_TABLE_STANDARD】数字行分组数量不是3个，实际".concat(h.length,"个，返回null")),null;var f="",I=t.match(u);I&&(f=I[1]);var b=(h[0].match(/[0-9]/g)||[]).join(","),p=(h[1].match(/[0-9]/g)||[]).join(","),_=(h[2].match(/[0-9]/g)||[]).join(",");return console.log("【YIMA_DINGWEI_TABLE_STANDARD】标准表格格式识别成功"),[{methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[i[1]],betNumbers:b,money:f},{methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[i[2]],betNumbers:p,money:f},{methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[i[3]],betNumbers:_,money:f}]}},YIMA_DINGWEI_SINGLE_POSITION:{pattern:/^(百位?|百|十位?|十|个位?|个)[ \t\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+(?:[ \t\-—~～@#￥%…&!、\\/*,，.＝=·]*[0-9]+)*)[ \t\-—~～@#￥%…&!、\\/*,，.＝=·]*各[ \t\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+)(?:元|块|快|米)?[ \t\-—~～@#￥%…&!、\\/*,，.＝=·]*$/,handler:function(e,t){var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},a=e[1],o=e[2],i=e[3],s=(o.match(/[0-9]/g)||[]).join(",");return[{methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[a],betNumbers:s,money:i}]}},YIMA_DINGWEI_SINGLE_POSITION_NO_GE:{pattern:/^(百位?|百|十位?|十|个位?|个)[ \t\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+(?:[ \t\-—~～@#￥%…&!、\\/*,，.＝=·]*[0-9]+)*)[ \t\-—~～@#￥%…&!、\\/*,，.＝=·]+([0-9]+)(?:元|块|快|米)*$/,handler:function(e,t){if(!/(?:百位?|百|十位?|十|个位?|个)/.test(t))return console.log('【YIMA_DINGWEI_SINGLE_POSITION_NO_GE】行 "'.concat(t,'" 不包含位置标识，返回null')),null;var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},a=e[1],o=e[2],i=e[3];if(!r[a])return console.log('【YIMA_DINGWEI_SINGLE_POSITION_NO_GE】位置标识 "'.concat(a,'" 无效，返回null')),null;var s=(o.match(/[0-9]/g)||[]).join(",");return s?(console.log("【YIMA_DINGWEI_SINGLE_POSITION_NO_GE】识别成功: 位置=".concat(a,", 号码=").concat(s,", 金额=").concat(i)),[{methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[a],betNumbers:s,money:i}]):(console.log("【YIMA_DINGWEI_SINGLE_POSITION_NO_GE】未提取到有效号码，返回null"),null)}},YIMA_DINGWEI_TWO_LINES:{pattern:/^([\s\S]+)$/m,handler:function(e,t){if(!/(?:百位?|百)|(?:十位?|十)|(?:个位?|个)/.test(t))return null;if(!/各[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+)(?:元|块|快|米)?/.test(t))return null;var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},o=t.split(/\n+/).map((function(e){return e.trim()})).filter(Boolean);if(2!==o.length)return console.log("【YIMA_DINGWEI_TWO_LINES】行数不是2行，实际".concat(o.length,"行，返回null")),null;var i="",s={},l=/^(百位?|百|十位?|十|个位?|个)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+(?:[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*[0-9]+)*)/,u=/各[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+)(?:元|块|快|米)?/,c=t.match(u);c&&(i=c[1]);var d,m=Object(a["a"])(o);try{for(m.s();!(d=m.n()).done;){var h=d.value;if(!u.test(h)){var f=h.match(l);if(f){var I=f[1],b=f[2],p=b.match(/[0-9]/g);p&&p.length>0&&(s[I]=p.join(","))}else if(h.trim()&&!/^[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*$/.test(h))return console.log('【YIMA_DINGWEI_TWO_LINES】行 "'.concat(h,'" 不匹配一码定位格式，返回null')),null}}}catch(A){m.e(A)}finally{m.f()}var _=Object.keys(s);if(2!==_.length)return console.log("【YIMA_DINGWEI_TWO_LINES】位置数量不是2个，实际".concat(_.length,"个，返回null")),null;console.log("【YIMA_DINGWEI_TWO_LINES】两行两位置格式识别成功");for(var g=[],N=0,U=_;N<U.length;N++){var v=U[N];g.push({methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[v],betNumbers:s[v],money:i})}return g}},YIMA_DINGWEI_ONE_LINE_TWO_POSITIONS:{pattern:/^(百位?|百|十位?|十|个位?|个)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+(?:[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*[0-9]+)*)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*(百位?|百|十位?|十|个位?|个)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+(?:[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*[0-9]+)*)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*各[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+)(?:元|块|快|米)?[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*$/,handler:function(e,t){var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},a=e[1],o=e[2],i=e[3],s=e[4],l=e[5];if(r[a]===r[i])return null;var u=(o.match(/[0-9]/g)||[]).join(","),c=(s.match(/[0-9]/g)||[]).join(",");return[{methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[a],betNumbers:u,money:l},{methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[i],betNumbers:c,money:l}]}}};Object.keys(s).forEach((function(e){var t=s[e].pattern;"string"===typeof t&&t.endsWith("$")?s[e].pattern=t.replace(/\$$/,"[，,./*-=。·、s]*$"):t instanceof RegExp&&t.source.endsWith("$")&&(s[e].pattern=new RegExp(t.source.replace(/\$$/,"[，,./*-=。·、s]*$")))}))},f97d:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return o}));var r=n("b775");function a(e){return Object(r["a"])({url:"/game/serial/list",method:"get",params:e})}function o(){return Object(r["a"])({url:"/game/serial/list",method:"get",params:{pageNum:1,pageSize:1,sortField:"serialNumbers",sortOrder:"desc"}}).then((function(e){return e&&e.length>0?e[0].serialNumbers:e&&e.rows&&e.rows.length>0?e.rows[0].serialNumbers:null}))}}}]);