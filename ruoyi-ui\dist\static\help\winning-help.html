<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中奖管理使用说明 - 彩票系统帮助文档</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #F56C6C 0%, #67C23A 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #F56C6C 0%, #67C23A 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        .header h1 {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        .back-btn {
            position: absolute;
            left: 30px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-50%) translateY(-2px);
        }
        .content {
            padding: 40px;
        }
        .section-toggle {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px 20px;
            background: linear-gradient(135deg, #F56C6C, #67C23A);
            color: white;
            border-radius: 12px;
            cursor: pointer;
            user-select: none;
            margin: 20px 0 10px 0;
            font-size: 18px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
        }
        .section-toggle:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(245, 108, 108, 0.4);
        }
        .toggle-icon {
            transition: transform 0.3s ease;
            font-size: 14px;
        }
        .toggle-icon.collapsed {
            transform: rotate(-90deg);
        }
        .section-content {
            max-height: 2000px;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 20px;
            padding: 20px;
        }
        .section-content.collapsed {
            max-height: 0;
            padding: 0 20px;
        }
        .subsection-toggle {
            cursor: pointer;
            user-select: none;
            display: flex;
            align-items: center;
            gap: 8px;
            color: #F56C6C;
            margin-top: 20px;
            margin-bottom: 10px;
            font-size: 16px;
            font-weight: 500;
        }
        .subsection-toggle:hover {
            color: #e6453c;
        }
        .subsection-toggle-icon {
            transition: transform 0.3s ease;
            font-size: 12px;
        }
        .subsection-toggle-icon.collapsed {
            transform: rotate(-90deg);
        }
        .subsection-content {
            max-height: 500px;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        .subsection-content.collapsed {
            max-height: 0;
        }
        .function-item {
            background: white;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            border-left: 4px solid #F56C6C;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        .function-title {
            font-weight: 600;
            color: #F56C6C;
            margin-bottom: 8px;
            font-size: 15px;
        }
        .function-desc {
            color: #666;
            margin-bottom: 8px;
            line-height: 1.6;
        }
        .function-usage {
            color: #888;
            font-size: 14px;
            font-style: italic;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }
        .feature-card {
            background: white;
            border-radius: 8px;
            padding: 16px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .tip {
            background: #fff0f0;
            border: 1px solid #ffb3b3;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            color: #cc0000;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            color: #856404;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            color: #155724;
        }
        .highlight {
            background: #fff0f0;
            padding: 2px 6px;
            border-radius: 4px;
            color: #cc0000;
            font-weight: 500;
        }
        .expand-all-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, #F56C6C, #67C23A);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
        }
        .expand-all-btn:hover {
            background: #e6453c;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(245, 108, 108, 0.4);
        }
        .step-list {
            counter-reset: step-counter;
        }
        .step-item {
            counter-increment: step-counter;
            position: relative;
            padding-left: 40px;
            margin-bottom: 16px;
        }
        .step-item::before {
            content: counter(step-counter);
            position: absolute;
            left: 0;
            top: 0;
            width: 24px;
            height: 24px;
            background: #F56C6C;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            margin: 8px 0;
            overflow-x: auto;
        }
        ul, ol {
            padding-left: 20px;
            margin: 8px 0;
        }
        li {
            margin-bottom: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <button class="back-btn" onclick="goBack()">← 返回</button>
            <h1>🏆 中奖管理使用说明</h1>
            <p>详细介绍中奖管理页面的所有功能和使用方法</p>
        </div>

        <div class="content">
            <div class="tip">
                <strong>页面概述：</strong>中奖管理页面用于管理彩票中奖记录，包括中奖查询、对账功能、结算管理等核心功能。普通用户可以查看中奖记录、进行对账操作和导出数据。
            </div>

            <h2 class="section-toggle" onclick="toggleSection('search')">
                <span class="toggle-icon collapsed" id="search-icon">▼</span>
                一、搜索功能区域
            </h2>
            <div class="feature-section section-content collapsed" id="search-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('search-1-1')">
                    <span class="subsection-toggle-icon collapsed" id="search-1-1-icon">▼</span>
                    1.1 中奖用户筛选
                </h3>
                <div class="subsection-content collapsed" id="search-1-1-content">
                    <div class="function-item">
                        <div class="function-title">用户选择</div>
                        <div class="function-desc">支持按中奖用户筛选中奖记录</div>
                        <div class="function-usage">使用方法：在下拉框中选择具体用户，支持搜索功能</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">快速清空</div>
                        <div class="function-desc">点击清空按钮可快速清除用户筛选条件</div>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('search-1-2')">
                    <span class="subsection-toggle-icon collapsed" id="search-1-2-icon">▼</span>
                    1.2 识别框搜索
                </h3>
                <div class="subsection-content collapsed" id="search-1-2-content">
                    <div class="function-item">
                        <div class="function-title">模糊搜索</div>
                        <div class="function-desc">支持在识别框中输入内容进行模糊搜索</div>
                        <div class="function-usage">使用方法：输入关键词，系统会自动展开包含该内容的所有记录</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">搜索提示</div>
                        <div class="function-desc">输入内容后会显示搜索模式提示信息</div>
                    </div>
                </div>
            </div>

            <h2 class="section-toggle" onclick="toggleSection('reconciliation')">
                <span class="toggle-icon collapsed" id="reconciliation-icon">▼</span>
                二、对账模式功能
            </h2>
            <div class="feature-section section-content collapsed" id="reconciliation-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('reconciliation-2-1')">
                    <span class="subsection-toggle-icon collapsed" id="reconciliation-2-1-icon">▼</span>
                    2.1 对账模式操作
                </h3>
                <div class="subsection-content collapsed" id="reconciliation-2-1-content">
                    <div class="function-item">
                        <div class="function-title">进入对账模式</div>
                        <div class="function-desc">点击"开始对账"按钮进入对账模式，系统会加载所有中奖记录</div>
                        <div class="function-usage">使用方法：点击工具栏的"开始对账"按钮</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">对账操作</div>
                        <div class="function-desc">在对账模式下，点击蓝色虚线框的"中奖金额"区域可标记为已对账</div>
                        <div class="function-usage">操作方法：点击表格中的中奖金额区域进行对账标记</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">退出对账模式</div>
                        <div class="function-desc">点击"退出对账"按钮退出对账模式，恢复正常浏览状态</div>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('reconciliation-2-2')">
                    <span class="subsection-toggle-icon collapsed" id="reconciliation-2-2-icon">▼</span>
                    2.2 对账状态管理
                </h3>
                <div class="subsection-content collapsed" id="reconciliation-2-2-content">
                    <div class="function-item">
                        <div class="function-title">对账统计</div>
                        <div class="function-desc">显示已对账记录数量和总记录数量的统计信息</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">状态标识</div>
                        <div class="function-desc">已对账记录显示"已对账"标识，未对账记录显示"待对账"标识</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">状态持久化</div>
                        <div class="function-desc">对账状态会自动保存，页面刷新后仍然保持</div>
                    </div>
                </div>
            </div>

            <h2 class="section-toggle" onclick="toggleSection('table')">
                <span class="toggle-icon collapsed" id="table-icon">▼</span>
                三、数据表格功能
            </h2>
            <div class="feature-section section-content collapsed" id="table-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('table-3-1')">
                    <span class="subsection-toggle-icon collapsed" id="table-3-1-icon">▼</span>
                    3.1 表格显示
                </h3>
                <div class="subsection-content collapsed" id="table-3-1-content">
                    <div class="function-item">
                        <div class="function-title">中奖信息展示</div>
                        <div class="function-desc">表格显示中奖用户、彩种、玩法、中奖号码、中奖金额等详细信息</div>
                    </div>
                    <div class="feature-grid">
                        <div class="feature-card">
                            <div class="function-title">👤 中奖用户</div>
                            <div class="function-desc">显示中奖的用户名称</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">🎲 彩种玩法</div>
                            <div class="function-desc">显示彩种类型和具体玩法</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">🔢 中奖号码</div>
                            <div class="function-desc">显示中奖的号码组合</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">💰 中奖金额</div>
                            <div class="function-desc">显示具体的中奖金额</div>
                        </div>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('table-3-2')">
                    <span class="subsection-toggle-icon collapsed" id="table-3-2-icon">▼</span>
                    3.2 展开详情
                </h3>
                <div class="subsection-content collapsed" id="table-3-2-content">
                    <div class="function-item">
                        <div class="function-title">行展开功能</div>
                        <div class="function-desc">点击表格行可展开查看详细信息</div>
                        <div class="function-usage">使用方法：点击表格行（非操作列）展开详情</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">详细信息</div>
                        <div class="function-desc">展开后显示号码识别、下注号码、流水号等详细信息</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">自动展开</div>
                        <div class="function-desc">识别框搜索时会自动展开所有搜索结果</div>
                    </div>
                </div>
            </div>

            <h2 class="section-toggle" onclick="toggleSection('export')">
                <span class="toggle-icon collapsed" id="export-icon">▼</span>
                四、数据导出功能
            </h2>
            <div class="feature-section section-content collapsed" id="export-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('export-4-1')">
                    <span class="subsection-toggle-icon collapsed" id="export-4-1-icon">▼</span>
                    4.1 导出操作
                </h3>
                <div class="subsection-content collapsed" id="export-4-1-content">
                    <div class="function-item">
                        <div class="function-title">导出Excel</div>
                        <div class="function-desc">将当前筛选条件下的中奖数据导出为Excel文件</div>
                        <div class="function-usage">使用方法：设置筛选条件后点击"导出数据"按钮</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">导出内容</div>
                        <div class="function-desc">包含中奖用户、彩种、玩法名称、下注号码、中奖号码、流水号、中奖金额、号码识别等信息</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">文件命名</div>
                        <div class="function-desc">导出文件自动以"中奖管理_日期时间"格式命名</div>
                    </div>
                </div>
            </div>

            <h2 class="section-toggle" onclick="toggleSection('tips')">
                <span class="toggle-icon collapsed" id="tips-icon">▼</span>
                五、使用技巧与注意事项
            </h2>
            <div class="feature-section section-content collapsed" id="tips-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('tips-5-1')">
                    <span class="subsection-toggle-icon collapsed" id="tips-5-1-icon">▼</span>
                    5.1 对账操作技巧
                </h3>
                <div class="subsection-content collapsed" id="tips-5-1-content">
                    <div class="function-item">
                        <div class="function-title">对账顺序</div>
                        <div class="function-desc">对账模式下记录按正序排列，建议按顺序进行对账</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">状态保存</div>
                        <div class="function-desc">对账状态会自动保存到本地存储，页面刷新后不会丢失</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">批量对账</div>
                        <div class="function-desc">可以连续点击多个记录进行批量对账操作</div>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('tips-5-2')">
                    <span class="subsection-toggle-icon collapsed" id="tips-5-2-icon">▼</span>
                    5.2 注意事项
                </h3>
                <div class="subsection-content collapsed" id="tips-5-2-content">
                    <div class="function-item">
                        <div class="function-title">对账模式特点</div>
                        <div class="function-desc">对账模式下会加载所有数据，不进行分页显示</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">数据权限</div>
                        <div class="function-desc">普通用户只能查看和对账自己的中奖记录</div>
                    </div>
                    <div class="warning">
                        <strong>重要提醒：</strong>对账操作会影响财务统计，请仔细核对后再进行标记
                    </div>
                </div>
            </div>

            <div class="success">
                <strong>使用提示：</strong>中奖管理是彩票系统的重要功能，建议定期进行对账操作，确保数据准确性。如遇问题可联系系统管理员。
            </div>
        </div>
    </div>

    <button class="expand-all-btn" onclick="toggleAllSections()">
        <span id="expandAllText">📖 展开全部</span>
    </button>

    <script>
        // 切换章节显示/隐藏
        function toggleSection(sectionId) {
            const content = document.getElementById(sectionId + '-content');
            const icon = document.getElementById(sectionId + '-icon');
            
            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                icon.classList.remove('collapsed');
            } else {
                content.classList.add('collapsed');
                icon.classList.add('collapsed');
            }
        }

        // 切换子章节显示/隐藏
        function toggleSubsection(subsectionId) {
            const content = document.getElementById(subsectionId + '-content');
            const icon = document.getElementById(subsectionId + '-icon');
            
            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                icon.classList.remove('collapsed');
            } else {
                content.classList.add('collapsed');
                icon.classList.add('collapsed');
            }
        }

        // 切换全部章节和子章节
        function toggleAllSections() {
            const sections = ['search', 'reconciliation', 'table', 'export', 'tips'];
            const subsections = [
                'search-1-1', 'search-1-2',
                'reconciliation-2-1', 'reconciliation-2-2',
                'table-3-1', 'table-3-2',
                'export-4-1',
                'tips-5-1', 'tips-5-2'
            ];

            const expandAllText = document.getElementById('expandAllText');
            const allCollapsed = sections.every(sectionId => 
                document.getElementById(sectionId + '-content').classList.contains('collapsed')
            );

            sections.forEach(sectionId => {
                const content = document.getElementById(sectionId + '-content');
                const icon = document.getElementById(sectionId + '-icon');
                
                if (allCollapsed) {
                    content.classList.remove('collapsed');
                    icon.classList.remove('collapsed');
                } else {
                    content.classList.add('collapsed');
                    icon.classList.add('collapsed');
                }
            });

            subsections.forEach(subsectionId => {
                const content = document.getElementById(subsectionId + '-content');
                const icon = document.getElementById(subsectionId + '-icon');
                
                if (allCollapsed) {
                    content.classList.remove('collapsed');
                    icon.classList.remove('collapsed');
                } else {
                    content.classList.add('collapsed');
                    icon.classList.add('collapsed');
                }
            });

            expandAllText.textContent = allCollapsed ? '📕 收起全部' : '📖 展开全部';
        }

        // 根据URL参数自动展开指定内容
        function autoExpandFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            const expandTarget = urlParams.get('expand');

            if (expandTarget) {
                // 查找包含目标文本的元素
                const allElements = document.querySelectorAll('.function-title, .subsection-toggle, .section-toggle');

                for (let element of allElements) {
                    const text = element.textContent || element.innerText;
                    if (text.includes(expandTarget)) {
                        // 找到匹配的元素，展开其所在的章节和子章节
                        expandToTarget(element);
                        // 滚动到目标位置
                        setTimeout(() => {
                            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            // 高亮显示目标元素
                            highlightElement(element);
                        }, 300);
                        break;
                    }
                }
            }
        }

        // 展开到目标元素
        function expandToTarget(targetElement) {
            let current = targetElement;

            // 向上查找并展开所有父级章节
            while (current) {
                if (current.classList && current.classList.contains('section-content')) {
                    // 展开章节
                    const sectionId = current.id.replace('-content', '');
                    const icon = document.getElementById(sectionId + '-icon');
                    if (icon && icon.classList.contains('collapsed')) {
                        toggleSection(sectionId);
                    }
                }

                if (current.classList && current.classList.contains('subsection-content')) {
                    // 展开子章节
                    const subsectionId = current.id.replace('-content', '');
                    const icon = document.getElementById(subsectionId + '-icon');
                    if (icon && icon.classList.contains('collapsed')) {
                        toggleSubsection(subsectionId);
                    }
                }

                current = current.parentElement;
            }
        }

        // 高亮显示元素
        function highlightElement(element) {
            element.style.backgroundColor = '#fff3cd';
            element.style.border = '2px solid #ffc107';
            element.style.borderRadius = '4px';
            element.style.padding = '8px';
            element.style.transition = 'all 0.3s ease';

            // 3秒后移除高亮
            setTimeout(() => {
                element.style.backgroundColor = '';
                element.style.border = '';
                element.style.borderRadius = '';
                element.style.padding = '';
            }, 3000);
        }

        // 返回功能
        function goBack() {
            const fromHelp = sessionStorage.getItem('helpFromPage');
            if (fromHelp) {
                sessionStorage.removeItem('helpFromPage');
                window.location.href = fromHelp;
            } else {
                window.location.href = 'index.html';
            }
        }

        // 页面加载时处理URL参数
        document.addEventListener('DOMContentLoaded', function() {
            autoExpandFromUrl();
        });
    </script>
</body>
</html>
