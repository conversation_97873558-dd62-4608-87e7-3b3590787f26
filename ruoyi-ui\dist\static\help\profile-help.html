<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心使用说明 - 彩票系统帮助文档</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        .header h1 {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        .back-btn {
            position: absolute;
            left: 30px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-50%) translateY(-2px);
        }
        .content {
            padding: 40px;
        }
        .section-toggle {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 12px;
            cursor: pointer;
            user-select: none;
            margin: 20px 0 10px 0;
            font-size: 18px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        .section-toggle:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        .toggle-icon {
            transition: transform 0.3s ease;
            font-size: 14px;
        }
        .toggle-icon.collapsed {
            transform: rotate(-90deg);
        }
        .section-content {
            max-height: 2000px;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 20px;
            padding: 20px;
        }
        .section-content.collapsed {
            max-height: 0;
            padding: 0 20px;
        }
        .subsection-toggle {
            cursor: pointer;
            user-select: none;
            display: flex;
            align-items: center;
            gap: 8px;
            color: #667eea;
            margin-top: 20px;
            margin-bottom: 10px;
            font-size: 16px;
            font-weight: 500;
        }
        .subsection-toggle:hover {
            color: #5a6fd8;
        }
        .subsection-toggle-icon {
            transition: transform 0.3s ease;
            font-size: 12px;
        }
        .subsection-toggle-icon.collapsed {
            transform: rotate(-90deg);
        }
        .subsection-content {
            max-height: 500px;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        .subsection-content.collapsed {
            max-height: 0;
        }
        .function-item {
            background: white;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            border-left: 4px solid #667eea;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        .function-title {
            font-weight: 600;
            color: #667eea;
            margin-bottom: 8px;
            font-size: 15px;
        }
        .function-desc {
            color: #666;
            margin-bottom: 8px;
            line-height: 1.6;
        }
        .function-usage {
            color: #888;
            font-size: 14px;
            font-style: italic;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }
        .feature-card {
            background: white;
            border-radius: 8px;
            padding: 16px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .tip {
            background: #e8f4fd;
            border: 1px solid #b3d8ff;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            color: #0066cc;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            color: #856404;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            color: #155724;
        }
        .highlight {
            background: #e8f4fd;
            padding: 2px 6px;
            border-radius: 4px;
            color: #0066cc;
            font-weight: 500;
        }
        .expand-all-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
        }
        .expand-all-btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
        }
        .step-list {
            counter-reset: step-counter;
        }
        .step-item {
            counter-increment: step-counter;
            position: relative;
            padding-left: 40px;
            margin-bottom: 16px;
        }
        .step-item::before {
            content: counter(step-counter);
            position: absolute;
            left: 0;
            top: 0;
            width: 24px;
            height: 24px;
            background: #667eea;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            margin: 8px 0;
            overflow-x: auto;
        }
        ul, ol {
            padding-left: 20px;
            margin: 8px 0;
        }
        li {
            margin-bottom: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <button class="back-btn" onclick="goBack()">← 返回</button>
            <h1>👤 个人中心使用说明</h1>
            <p>详细介绍个人中心的所有功能和使用方法</p>
        </div>

        <div class="content">
            <div class="tip">
                <strong>页面概述：</strong>个人中心是用户管理个人信息、查看个人数据、修改设置的专属空间，提供个人资料管理、密码修改、个人统计等功能。
            </div>

            <h2 class="section-toggle" onclick="toggleSection('profile')">
                <span class="toggle-icon collapsed" id="profile-icon">▼</span>
                一、个人资料管理
            </h2>
            <div class="feature-section section-content collapsed" id="profile-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('profile-1-1')">
                    <span class="subsection-toggle-icon collapsed" id="profile-1-1-icon">▼</span>
                    1.1 基本信息
                </h3>
                <div class="subsection-content collapsed" id="profile-1-1-content">
                    <div class="function-item">
                        <div class="function-title">用户信息展示</div>
                        <div class="function-desc">显示当前登录用户的基本信息</div>
                        <div class="function-usage">包含用户名、昵称、邮箱、手机号等信息</div>
                    </div>
                    <div class="feature-grid">
                        <div class="feature-card">
                            <div class="function-title">👤 用户名</div>
                            <div class="function-desc">系统登录用户名，不可修改</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">📝 昵称</div>
                            <div class="function-desc">用户显示名称，可自定义修改</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">📧 邮箱</div>
                            <div class="function-desc">联系邮箱，用于接收通知</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">📱 手机号</div>
                            <div class="function-desc">联系电话，用于安全验证</div>
                        </div>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('profile-1-2')">
                    <span class="subsection-toggle-icon collapsed" id="profile-1-2-icon">▼</span>
                    1.2 信息修改
                </h3>
                <div class="subsection-content collapsed" id="profile-1-2-content">
                    <div class="function-item">
                        <div class="function-title">编辑个人信息</div>
                        <div class="function-desc">点击编辑按钮修改个人资料</div>
                        <div class="function-usage">支持修改昵称、邮箱、手机号等可变信息</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">头像上传</div>
                        <div class="function-desc">上传和更换个人头像</div>
                        <div class="function-usage">支持JPG、PNG格式，文件大小不超过2MB</div>
                    </div>
                </div>
            </div>

            <h2 class="section-toggle" onclick="toggleSection('security')">
                <span class="toggle-icon collapsed" id="security-icon">▼</span>
                二、安全设置
            </h2>
            <div class="feature-section section-content collapsed" id="security-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('security-2-1')">
                    <span class="subsection-toggle-icon collapsed" id="security-2-1-icon">▼</span>
                    2.1 密码管理
                </h3>
                <div class="subsection-content collapsed" id="security-2-1-content">
                    <div class="function-item">
                        <div class="function-title">修改登录密码</div>
                        <div class="function-desc">更改系统登录密码</div>
                        <div class="function-usage">需要输入原密码和新密码进行验证</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">密码强度检测</div>
                        <div class="function-desc">实时检测新密码的安全强度</div>
                        <div class="function-usage">建议使用包含字母、数字、特殊字符的强密码</div>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('security-2-2')">
                    <span class="subsection-toggle-icon collapsed" id="security-2-2-icon">▼</span>
                    2.2 登录记录
                </h3>
                <div class="subsection-content collapsed" id="security-2-2-content">
                    <div class="function-item">
                        <div class="function-title">登录历史</div>
                        <div class="function-desc">查看最近的登录记录</div>
                        <div class="function-usage">显示登录时间、IP地址、设备信息等</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">异常登录提醒</div>
                        <div class="function-desc">检测并提醒异常登录行为</div>
                        <div class="function-usage">如发现异常请及时修改密码</div>
                    </div>
                </div>
            </div>

            <h2 class="section-toggle" onclick="toggleSection('statistics')">
                <span class="toggle-icon collapsed" id="statistics-icon">▼</span>
                三、个人统计
            </h2>
            <div class="feature-section section-content collapsed" id="statistics-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('statistics-3-1')">
                    <span class="subsection-toggle-icon collapsed" id="statistics-3-1-icon">▼</span>
                    3.1 投注统计
                </h3>
                <div class="subsection-content collapsed" id="statistics-3-1-content">
                    <div class="function-item">
                        <div class="function-title">投注总览</div>
                        <div class="function-desc">显示个人投注的总体数据</div>
                        <div class="function-usage">包含总投注金额、投注次数、中奖次数等</div>
                    </div>
                    <div class="feature-grid">
                        <div class="feature-card">
                            <div class="function-title">💰 总投注额</div>
                            <div class="function-desc">累计投注金额统计</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">🎯 投注次数</div>
                            <div class="function-desc">总投注次数统计</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">🏆 中奖次数</div>
                            <div class="function-desc">中奖次数和中奖率</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">📊 盈亏统计</div>
                            <div class="function-desc">盈利亏损情况分析</div>
                        </div>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('statistics-3-2')">
                    <span class="subsection-toggle-icon collapsed" id="statistics-3-2-icon">▼</span>
                    3.2 趋势分析
                </h3>
                <div class="subsection-content collapsed" id="statistics-3-2-content">
                    <div class="function-item">
                        <div class="function-title">投注趋势图</div>
                        <div class="function-desc">显示个人投注金额的时间趋势</div>
                        <div class="function-usage">可按日、周、月查看投注变化</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">玩法偏好</div>
                        <div class="function-desc">分析个人喜好的投注玩法</div>
                        <div class="function-usage">饼图显示各玩法投注比例</div>
                    </div>
                </div>
            </div>

            <h2 class="section-toggle" onclick="toggleSection('settings')">
                <span class="toggle-icon collapsed" id="settings-icon">▼</span>
                四、系统设置
            </h2>
            <div class="feature-section section-content collapsed" id="settings-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('settings-4-1')">
                    <span class="subsection-toggle-icon collapsed" id="settings-4-1-icon">▼</span>
                    4.1 界面设置
                </h3>
                <div class="subsection-content collapsed" id="settings-4-1-content">
                    <div class="function-item">
                        <div class="function-title">主题选择</div>
                        <div class="function-desc">选择系统界面主题</div>
                        <div class="function-usage">支持明亮、暗黑等多种主题模式</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">语言设置</div>
                        <div class="function-desc">设置系统显示语言</div>
                        <div class="function-usage">支持中文、英文等多种语言</div>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('settings-4-2')">
                    <span class="subsection-toggle-icon collapsed" id="settings-4-2-icon">▼</span>
                    4.2 通知设置
                </h3>
                <div class="subsection-content collapsed" id="settings-4-2-content">
                    <div class="function-item">
                        <div class="function-title">消息通知</div>
                        <div class="function-desc">设置系统消息通知方式</div>
                        <div class="function-usage">可选择邮件、短信、站内信等通知方式</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">开奖提醒</div>
                        <div class="function-desc">设置开奖结果通知</div>
                        <div class="function-usage">中奖时自动发送通知提醒</div>
                    </div>
                </div>
            </div>

            <h2 class="section-toggle" onclick="toggleSection('tips')">
                <span class="toggle-icon collapsed" id="tips-icon">▼</span>
                五、使用技巧与注意事项
            </h2>
            <div class="feature-section section-content collapsed" id="tips-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('tips-5-1')">
                    <span class="subsection-toggle-icon collapsed" id="tips-5-1-icon">▼</span>
                    5.1 使用技巧
                </h3>
                <div class="subsection-content collapsed" id="tips-5-1-content">
                    <div class="success">
                        <strong>💡 实用技巧：</strong>
                        <ul>
                            <li>定期更新个人信息，确保联系方式有效</li>
                            <li>设置强密码并定期更换，保障账户安全</li>
                            <li>关注个人统计数据，合理控制投注行为</li>
                            <li>开启消息通知，及时获取重要信息</li>
                        </ul>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('tips-5-2')">
                    <span class="subsection-toggle-icon collapsed" id="tips-5-2-icon">▼</span>
                    5.2 注意事项
                </h3>
                <div class="subsection-content collapsed" id="tips-5-2-content">
                    <div class="warning">
                        <strong>⚠️ 重要提醒：</strong>
                        <ul>
                            <li>不要在公共场所或他人设备上登录个人账户</li>
                            <li>发现异常登录记录请立即修改密码</li>
                            <li>个人信息修改后需要重新登录生效</li>
                            <li>头像上传失败可能是文件格式或大小问题</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <button class="expand-all-btn" onclick="toggleAllSections()">
        <span id="expandAllText">📖 展开全部</span>
    </button>

    <script>
        // 切换章节显示/隐藏
        function toggleSection(sectionId) {
            const content = document.getElementById(sectionId + '-content');
            const icon = document.getElementById(sectionId + '-icon');
            
            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                icon.classList.remove('collapsed');
            } else {
                content.classList.add('collapsed');
                icon.classList.add('collapsed');
            }
        }

        // 切换子章节显示/隐藏
        function toggleSubsection(subsectionId) {
            const content = document.getElementById(subsectionId + '-content');
            const icon = document.getElementById(subsectionId + '-icon');
            
            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                icon.classList.remove('collapsed');
            } else {
                content.classList.add('collapsed');
                icon.classList.add('collapsed');
            }
        }

        // 切换全部章节和子章节
        function toggleAllSections() {
            const sections = ['profile', 'security', 'statistics', 'settings', 'tips'];
            const subsections = [
                'profile-1-1', 'profile-1-2',
                'security-2-1', 'security-2-2',
                'statistics-3-1', 'statistics-3-2',
                'settings-4-1', 'settings-4-2',
                'tips-5-1', 'tips-5-2'
            ];

            const expandAllText = document.getElementById('expandAllText');
            const allCollapsed = sections.every(sectionId =>
                document.getElementById(sectionId + '-content').classList.contains('collapsed')
            );

            sections.forEach(sectionId => {
                const content = document.getElementById(sectionId + '-content');
                const icon = document.getElementById(sectionId + '-icon');

                if (allCollapsed) {
                    content.classList.remove('collapsed');
                    icon.classList.remove('collapsed');
                } else {
                    content.classList.add('collapsed');
                    icon.classList.add('collapsed');
                }
            });

            subsections.forEach(subsectionId => {
                const content = document.getElementById(subsectionId + '-content');
                const icon = document.getElementById(subsectionId + '-icon');

                if (allCollapsed) {
                    content.classList.remove('collapsed');
                    icon.classList.remove('collapsed');
                } else {
                    content.classList.add('collapsed');
                    icon.classList.add('collapsed');
                }
            });

            expandAllText.textContent = allCollapsed ? '📕 收起全部' : '📖 展开全部';
        }

        // 根据URL参数自动展开指定内容
        function autoExpandFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            const expandTarget = urlParams.get('expand');

            if (expandTarget) {
                // 查找包含目标文本的元素
                const allElements = document.querySelectorAll('.function-title, .subsection-toggle, .section-toggle');

                for (let element of allElements) {
                    const text = element.textContent || element.innerText;
                    if (text.includes(expandTarget)) {
                        // 找到匹配的元素，展开其所在的章节和子章节
                        expandToTarget(element);
                        // 滚动到目标位置
                        setTimeout(() => {
                            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            // 高亮显示目标元素
                            highlightElement(element);
                        }, 300);
                        break;
                    }
                }
            }
        }

        // 展开到目标元素
        function expandToTarget(targetElement) {
            let current = targetElement;

            // 向上查找并展开所有父级章节
            while (current) {
                if (current.classList && current.classList.contains('section-content')) {
                    // 展开章节
                    const sectionId = current.id.replace('-content', '');
                    const icon = document.getElementById(sectionId + '-icon');
                    if (icon && icon.classList.contains('collapsed')) {
                        toggleSection(sectionId);
                    }
                }

                if (current.classList && current.classList.contains('subsection-content')) {
                    // 展开子章节
                    const subsectionId = current.id.replace('-content', '');
                    const icon = document.getElementById(subsectionId + '-icon');
                    if (icon && icon.classList.contains('collapsed')) {
                        toggleSubsection(subsectionId);
                    }
                }

                current = current.parentElement;
            }
        }

        // 高亮显示元素
        function highlightElement(element) {
            element.style.backgroundColor = '#fff3cd';
            element.style.border = '2px solid #ffc107';
            element.style.borderRadius = '4px';
            element.style.padding = '8px';
            element.style.transition = 'all 0.3s ease';

            // 3秒后移除高亮
            setTimeout(() => {
                element.style.backgroundColor = '';
                element.style.border = '';
                element.style.borderRadius = '';
                element.style.padding = '';
            }, 3000);
        }

        // 返回功能
        function goBack() {
            const fromHelp = sessionStorage.getItem('helpFromPage');
            if (fromHelp) {
                sessionStorage.removeItem('helpFromPage');
                window.location.href = fromHelp;
            } else {
                window.location.href = 'index.html';
            }
        }

        // 页面加载时处理URL参数
        document.addEventListener('DOMContentLoaded', function() {
            autoExpandFromUrl();
        });
    </script>
</body>
</html>
