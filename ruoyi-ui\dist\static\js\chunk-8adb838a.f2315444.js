(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-8adb838a"],{"0481":function(e,n,t){"use strict";var r=t("23e7"),o=t("a2bf6"),a=t("7b0b"),u=t("07fa"),d=t("5926"),l=t("65f0");r({target:"Array",proto:!0},{flat:function(){var e=arguments.length?arguments[0]:void 0,n=a(this),t=u(n),r=l(n,0);return r.length=o(r,n,n,t,0,void 0===e?1:d(e)),r}})},"07ac":function(e,n,t){"use strict";var r=t("23e7"),o=t("6f53").values;r({target:"Object",stat:!0},{values:function(e){return o(e)}})},"2e8d":function(e,n,t){"use strict";t.r(n),t.d(n,"SEVEN_CODE_PATTERNS",(function(){return d}));var r=t("2909"),o=t("3835"),a=t("b85c"),u=(t("99af"),t("4de4"),t("caad"),t("a15b"),t("d81d"),t("14d9"),t("4e82"),t("4ec9"),t("b64b"),t("d3b7"),t("4d63"),t("c607"),t("ac1f"),t("2c3e"),t("00b4"),t("25f0"),t("8a79"),t("2532"),t("3ca3"),t("466d"),t("5319"),t("1276"),t("498a"),t("0643"),t("76d6"),t("2382"),t("4e3e"),t("a573"),t("9a9a"),t("159b"),t("ddb0"),t("f9d6")),d={QIMA_TRANSFORM:{pattern:/^(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?(\d{7})[\-—~～@#￥%…&!、\\/*,，.＝=各·\s]*?(转|套|拖)(?:各)?(\d+)(?:\+(\d+))?(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?$/,handler:function(e,n){var r=e[2],o=e[4],a=e[5],u=e[6],d=t("f9d6").generateTransformNumbers(r),l=[];return o?l.push({methodId:t("f9d6").METHOD_ID.BAMA_ZULIU,betNumbers:d.join(","),money:o}):a&&u&&(l.push({methodId:t("f9d6").METHOD_ID.BAMA_ZULIU,betNumbers:d.join(","),money:a}),l.push({methodId:t("f9d6").METHOD_ID.BAMA_ZUSAN,betNumbers:d.join(","),money:u})),l}},QIMA_ZUSAN:{pattern:/^(\d{7})[\-—~～@#￥%…&!、\\/*,，一－.＝=·\s]*组三?[\-—~～@#￥%…&!、\\/*,，。，一—－.＝=·\s]*([\d]+)?(元|块|米)?[\-—~～@#￥%…&!、。，一—－\\/*,，.＝=·\s]*$/,handler:function(e,n){return 7!==e[1].length?null:[{methodId:u["METHOD_ID"].QIMA_ZUSAN,betNumbers:e[1],money:e[2]||""}]}},QIMA_FANGDUI:{pattern:/^(\d{7})[\-—~～@#￥%…&!、\\/*,，一－.＝=·\s]*防对?([+＋\-—~～@#￥%…&!、：:\\/*,，。一—－~.＝=·吊赶打各]?(\d+))?$/,handler:function(e,n){return 7!==e[1].length?null:[{methodId:u["METHOD_ID"].QIMA_ZUSAN,betNumbers:e[1],money:e[2]||""}]}},MULTI_QIMA:{pattern:/^(\d{7}(?:[^0-9]+\d{7})*)([+＋\-—~～@#￥%…&!、：:\\/*,，。一—－~.＝=·吊赶打各]([一二三四五六七八九十百千万\d]+))?(?:防([一二三四五六七八九十百千万\d]+))?$/,handler:function(e,n){var r=t("f9d6"),o=r.chineseToNumber,a=n.split(u["BASE_PATTERNS"].SEPARATORS).filter(Boolean).filter((function(e){return 7===e.length})),d=e[2]?o(e[2]):"",l=e[3]?o(e[3]):"",I=[];return d&&I.push({methodId:u["METHOD_ID"].QIMA_ZULIU,betNumbers:a.join(","),money:d}),l&&I.push({methodId:u["METHOD_ID"].QIMA_ZUSAN,betNumbers:a[a.length-1],money:l}),console.log("七码 handler 返回:",I),I}},QIMA_ZULIU_ZUSAN_MULTI_MONEY_SUPER:{pattern:/^[－\-—~～@#￥%…&!、\\/*,一－。＝=·\s+吊赶打各]*(\d{7}(?:[－\-—~～@#￥%…&!、\\/*,一－。＝=·\s+吊赶打各]+\d{7})*)[\-—~～@#￥%…&!、\\/*,一－。＝=·\s+吊赶打各]+(\d+)[\-—~～@#￥%…&!、\\/*,一－。＝=·\s+吊赶打各]*[\+\-\\/*、.]+[\-—~～@#￥%…&!、\\/*,一－。＝=·\s+吊赶打各]*(\d+)(?:元)?$/,handler:function(e,n){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],o=t("f9d6"),a=o.SUPER_SEPARATORS,u=e[1].split(a).filter(Boolean),d=e[2],l=e[3];return r&&(/^\d{7}$/.test(d)||/^\d{7}$/.test(l))?null:[{methodId:t("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:u.join(","),money:d},{methodId:t("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:u.join(","),money:l}]}},QIMA_ZULIU_ZU_SUPER:{pattern:/^(\d{7})组(\d+)$/,handler:function(e,n){var r=e[1],o=e[2];return[{methodId:t("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:r,money:o}]}},QIMA_ZULIU_MULTI_SUPER:{pattern:/^(\d{7}(?:[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各]+\d{7})*)[\-—~～@一－。#￥%…&!、\\/*,，.＝=·\s+吊赶打各]+(\d+)$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=e[1].split(o).filter(Boolean),u=e[2];return[{methodId:t("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:a.join(","),money:u}]}},QIMA_ZUSAN_ZU_SUPER:{pattern:/^(\d{7})组三(\d+)$/,handler:function(e,n){var r=e[1],o=e[2];return[{methodId:t("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:r,money:o}]}},QIMA_ZHIZU_MULTI_SUPER:{pattern:/^(\d{7}(?:[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各]+\d{7})*)[\-—~～@#￥%…&!、\\/*,，.＝=一－。·\s+吊赶打各]*(直组|组直|值组|组值)[\s]*各?(\d+)(?:元)?$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=e[1].split(o).filter(Boolean),u=e[3];return[{methodId:t("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:a.join(","),money:u},{methodId:t("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:a.join(","),money:u}]}},QIMA_ZULIU_FANG:{pattern:/^(\d{7})[+＋\-—~～@#￥%…&!、：:\\/*,，。一—－~.＝=·吊赶打各](\d+)[+防](\d+)$/,handler:function(e,n){var t=e[1],r=e[2],o=e[3];return[{methodId:u["METHOD_ID"].QIMA_ZULIU,betNumbers:t,money:r},{methodId:u["METHOD_ID"].QIMA_ZUSAN,betNumbers:t,money:o}]}},QIMA_MULTI_ZHIZU_EACH_SUPER:{pattern:/^(直组|组直|值组|组值)各([0-9]+)[\s\-—~～@#￥%…&!、一－。 \\/*,，.＝=·吊赶打各\n]+([\d\s\-—~～@#￥%…&!一－。、\\/*,，.＝=·吊赶打各]+)$/i,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=e[2],u=e[3].replace(/\n/g," "),d=u.split(o).map((function(e){return e.trim()})).filter((function(e){return/^\d{7}$/.test(e)}));return[{methodId:t("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:d.join(","),money:a},{methodId:t("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:d.join(","),money:a}]}},QIMA_MULTI_EACH_SUPER:{pattern:/^(\d{7}(?:[\-~～@#￥%…&!一－。、\\/*,，.＝=·\s+吊赶打各]+\d{7})*)各(\d+)$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=e[1].split(o).filter(Boolean);if(!a.length||a.some((function(e){return 7!==e.length})))return null;var u=e[2];return[{methodId:t("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:a.join(","),money:u}]}},QIMA_SLASH_TWO_MONEY:{pattern:/^(?!.*[\r\n])(\d{7})[\-—~～@#￥%…&!、\\/*,，.。\u4e00\uff0d\uff1d=· 　\t]+(\d+)[+]+(\d+)$/,handler:function(e,n){var r=e[1],o=e[2],a=e[3];return[{methodId:t("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:r,money:o},{methodId:t("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:r,money:a}]}},QIMA_MULTI_SUPER_SLASH_TWO_MONEY:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、＝=·吊赶打各]+)\/(\d+)\/(\d+)$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=e[1].split(o).map((function(e){return e.trim()})).filter((function(e){return 7===e.length}));if(!a.length)return[];var u=e[2],d=e[3];return[{methodId:t("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:a.join(","),money:u},{methodId:t("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:a.join(","),money:d}]}},QIMA_UNIVERSAL_SUPER:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥一－—。%…&!、＝=·吊赶打各]+)[\-~～@#￥%…&!一－—。、\\/*,，.＝=·\s+吊赶打各]+([0-9]+)(元|米|块)?$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=r.METHOD_ID,u=e[1].split(o).map((function(e){return e.trim()})).filter(Boolean),d=e[2];return 1===u.length&&7===u[0].length?[{methodId:a.QIMA_ZULIU,betNumbers:u[0],money:d}]:u.length>=2&&u.every((function(e){return 7===e.length}))?[{methodId:a.QIMA_ZULIU,betNumbers:u.join(","),money:d}]:null}},QIMA_EACH_PLUS_SUPER:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥一－。%…&!、＝=·吊赶打各]+)各([0-9]+)\+([0-9]+)$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=r.METHOD_ID,u=e[1].split(o).map((function(e){return e.trim()})).filter(Boolean),d=e[2],l=e[3];return 1===u.length&&7===u[0].length?[{methodId:a.QIMA_ZULIU,betNumbers:u[0],money:d},{methodId:a.QIMA_ZUSAN,betNumbers:u[0],money:l}]:u.length>=2&&u.every((function(e){return 7===e.length}))?[{methodId:a.QIMA_ZULIU,betNumbers:u.join(","),money:d},{methodId:a.QIMA_ZUSAN,betNumbers:u.join(","),money:l}]:null}},QIMA_ZULIU_MONEY:{pattern:/^([0-9]{7,})\s+(\d+)(元|块|米)?$/,handler:function(e,n){var r=e[1],o=e[2];return r&&7===r.length?[{methodId:t("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:r,money:o}]:null}},QIMA_DA_MONEY:{pattern:/^([0-9]{7})打(\d+)(元|块|米)?$/,handler:function(e,n){return[{methodId:t("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:e[1],money:e[2]}]}},QIMA_MULTI_WITH_MONEY_SLASH_PLUS:{pattern:/^(\d{7}(?:[\s,，\-]+?\d{7})*)\/\s*(\d+)\+(\d+)$/,handler:function(e,n){var r=e[1].split(/[\s,，\-]+/).filter(Boolean),o=e[2],a=e[3];return[{methodId:t("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:r.join(","),money:o},{methodId:t("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:r.join(","),money:a}]}},QIMA_MULTI_LINE_MONEY_PLUS:{pattern:/^((?:\d{7}[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+\d+\+\d+\s*\n?)+)$/m,handler:function(e,n){var r,u=t("f9d6"),d=u.METHOD_ID,l=n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),I=new Map,s=!0,i=Object(a["a"])(l);try{for(i.s();!(r=i.n()).done;){var _=r.value,c=_.match(/^(\d{7})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+(\d+)\+(\d+)$/);if(!c){s=!1;break}var h=c[1];if(7!==h.length){s=!1;break}var f=_.split(/[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+/)[0];if(7!==f.length||!/^\d{7}$/.test(f)){s=!1;break}var m="".concat(c[2],"+").concat(c[3]);I.has(m)||I.set(m,[]),I.get(m).push(h)}}catch(L){i.e(L)}finally{i.f()}if(!s||0===I.size)return null;var U,A=[],M=Object(a["a"])(I.entries());try{for(M.s();!(U=M.n()).done;){var N=Object(o["a"])(U.value,2),E=N[0],b=N[1],D=E.split("+"),p=Object(o["a"])(D,2),O=p[0],T=p[1];A.push({methodId:d.QIMA_ZULIU,betNumbers:b.join(","),money:O}),A.push({methodId:d.QIMA_ZUSAN,betNumbers:b.join(","),money:T})}}catch(L){M.e(L)}finally{M.f()}return A.length?A:null}},QIMA_MULTI_LINE_MONEY_PLUS_CN:{pattern:/^((?:\d{7}[，,、]?[一二三四五六七八九十百千万\d]+加[一二三四五六七八九十百千万\d]+\s*\n?)+)$/m,handler:function(e,n){var r,u=t("f9d6"),d=u.METHOD_ID,l=u.chineseToNumber,I=n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),s=new Map,i=Object(a["a"])(I);try{for(i.s();!(r=i.n()).done;){var _=r.value,c=_.match(/^(\d{7})[，,、]?([一二三四五六七八九十百千万\d]+)加([一二三四五六七八九十百千万\d]+)$/);if(c){var h=c[1],f=l(c[2]),m=l(c[3]),U="".concat(f,"+").concat(m);s.has(U)||s.set(U,[]),s.get(U).push(h)}}}catch(g){i.e(g)}finally{i.f()}var A,M=[],N=Object(a["a"])(s.entries());try{for(N.s();!(A=N.n()).done;){var E=Object(o["a"])(A.value,2),b=E[0],D=E[1],p=b.split("+"),O=Object(o["a"])(p,2),T=O[0],L=O[1];M.push({methodId:d.QIMA_ZULIU,betNumbers:D.join(","),money:T}),M.push({methodId:d.QIMA_ZUSAN,betNumbers:D.join(","),money:L})}}catch(g){N.e(g)}finally{N.f()}return M.length?M:null}},QIMA_MULTI_EACH_FANGDUI_SLASH:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥一－。%…&!、＝=·吊赶打各]+)[/\-~～@#￥%…&!、\\/*一－。,，.＝=·吊赶打各]+各防对([一二三四五六七八九十百千万\d]+)$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=r.METHOD_ID,u=r.chineseToNumber,d=e[1].split(o).map((function(e){return e.trim()})).filter((function(e){return 7===e.length})),l=u(e[2]);return d.length?[{methodId:a.QIMA_ZUSAN,betNumbers:d.join(","),money:l}]:null}},QIMA_MULTI_EACH_FANGDUI:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!一－。、＝=·吊赶打各]+)各防对([一二三四五六七八九十百千万\d]+)$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=r.METHOD_ID,u=r.chineseToNumber,d=e[1].split(o).map((function(e){return e.trim()})).filter((function(e){return 7===e.length})),l=u(e[2]);return d.length?[{methodId:a.QIMA_ZUSAN,betNumbers:d.join(","),money:l}]:null}},QIMA_SLASH_MONEY:{pattern:/^\s*(\d{7})\s*\/\s*([一二三四五六七八九十百千万\d]+)\s*$/,handler:function(e,n){var r=t("f9d6"),o=r.METHOD_ID,a=r.chineseToNumber,u=e[1],d=a(e[2]);return[{methodId:o.QIMA_ZULIU,betNumbers:u,money:d}]}},QIMA_ZULIU_ZUSAN_MONEY_PAIR:{pattern:/^(\d{7}[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*)组六[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*(\d+)[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*组三[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*(\d+)$/,handler:function(e,n){var t=e[1],r=e[2],o=e[3];return[{methodId:u["METHOD_ID"].QIMA_ZULIU,betNumbers:t,money:r},{methodId:u["METHOD_ID"].QIMA_ZUSAN,betNumbers:t,money:o}]}},QIMA_ZULIU:{pattern:/^(\d{7})组六?(\d+)[，,./\*\-\=一－。。·、\s]*$/,handler:function(e,n){var t=e[1];if(7!==t.length)return null;var r=e[2]||"";return[{methodId:u["METHOD_ID"].QIMA_ZULIU,betNumbers:t,money:r}]}},QIMA_ZULIU_ZUSAN_COMBO:{pattern:/^(\d{7}(?:[。.．、,，\s]+\d{7})*)\/(\d+)\+(\d+)$/,handler:function(e,n){var r=t("f9d6"),o=(r.SUPER_SEPARATORS,e[1].split(/[。.．、,，\s]+/).map((function(e){return e.trim()})).filter(Boolean)),a=e[2],u=e[3];return o.length&&o.every((function(e){return 7===e.length}))?[{methodId:t("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:o.join(","),money:a},{methodId:t("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:o.join(","),money:u}]:null}},QIMA_SINGLE_LINE_ZULIU_ZUSAN_PLUS:{pattern:/^(\d{7})\s+(\d+)[+＋](\d+)$/,handler:function(e,n){return[{methodId:t("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:e[1],money:e[2]},{methodId:t("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:e[1],money:e[3]}]}},QIMA_MULTI_LINE_ZULIU_MONEY_GROUP:{pattern:/^((?:\d{7}\s+\d+\s*\n?)+)$/m,handler:function(e,n){var r,u=t("f9d6"),d=u.METHOD_ID,l=n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),I=new Map,s=Object(a["a"])(l);try{for(s.s();!(r=s.n()).done;){var i=r.value,_=i.match(/^(\d{7})\s+(\d+)$/);if(_){var c=_[1],h=_[2];I.has(h)||I.set(h,[]),I.get(h).push(c)}}}catch(E){s.e(E)}finally{s.f()}var f,m=[],U=Object(a["a"])(I.entries());try{for(U.s();!(f=U.n()).done;){var A=Object(o["a"])(f.value,2),M=A[0],N=A[1];m.push({methodId:d.QIMA_ZULIU,betNumbers:N.join(","),money:M})}}catch(E){U.e(E)}finally{U.f()}return m.length?m:null}},QIMA_ZULIU_FANG_ZUSAN_PLUS:{pattern:/^(\d{7})[\-—~～@#￥%…&!、\\/*,，.＝一－。=·\s+吊赶打各]*(\d+)[\-—~～@#￥%…&!、一－。\\/*,，.＝=·\s+吊赶打各]*(防对|防)(\d+)$/,handler:function(e,n){return[{methodId:t("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:e[1],money:e[2]},{methodId:t("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:e[1],money:e[3]}]}},QIMA_MULTI_LINE_ZULIU_FANG_ZUSAN_GROUP:{pattern:/^([\s\S]+)$/m,handler:function(e,n){var r,u=n.split(/[\r\n]+/).map((function(e){return e.trim()})).filter(Boolean),d=new Map,l=Object(a["a"])(u);try{for(l.s();!(r=l.n()).done;){var I=r.value,s=I.match(/^(\d{7})-?(\d+)防(\d+)$/);if(s){var i=s[1],_=s[2],c=s[3],h="".concat(_,"|").concat(c);d.has(h)||d.set(h,[]),d.get(h).push(i)}}}catch(O){l.e(O)}finally{l.f()}var f,m=[],U=Object(a["a"])(d.entries());try{for(U.s();!(f=U.n()).done;){var A=Object(o["a"])(f.value,2),M=A[0],N=A[1],E=M.split("|"),b=Object(o["a"])(E,2),D=b[0],p=b[1];m.push({methodId:t("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:N.join(","),money:D}),m.push({methodId:t("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:N.join(","),money:p})}}catch(O){U.e(O)}finally{U.f()}return m.length?m:null}},QIMA_MULTI_TRANSFORM_EACH:{pattern:/^(\d{6}(?:[\-~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各]+\d{6})*)(转|转各|套|套各)(\d+)\+(\d+)$/,handler:function(e,n){var r,o=t("f9d6"),u=o.SUPER_SEPARATORS,d=o.METHOD_ID,l=o.generateTransformNumbers,I=e[1].split(u).filter(Boolean),s=e[3],i=e[4],_=[],c=Object(a["a"])(I);try{for(c.s();!(r=c.n()).done;){var h=r.value,f=l(h);_.push({methodId:d.BAMA_ZULIU,betNumbers:f.join(","),money:s}),_.push({methodId:d.BAMA_ZUSAN,betNumbers:f.join(","),money:i})}}catch(m){c.e(m)}finally{c.f()}return _}},QIMA_MULTI_LINE_TRANSFORM:{pattern:/^((?:(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?\d{7}转\d+(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?\s*\n?)+)$/m,handler:function(e,n){var r,u=t("f9d6"),d=u.METHOD_ID,l=n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),I=new Map,s=Object(a["a"])(l);try{for(s.s();!(r=s.n()).done;){var i=r.value,_=i.match(/^(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?(\d{7})转(\d+)(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?$/);if(_){var c=_[2],h=_[3];I.has(h)||I.set(h,[]),I.get(h).push(c)}}}catch(E){s.e(E)}finally{s.f()}var f,m=[],U=Object(a["a"])(I.entries());try{for(U.s();!(f=U.n()).done;){var A=Object(o["a"])(f.value,2),M=A[0],N=A[1];m.push({methodId:d.BAMA_ZULIU,betNumbers:N.join(","),money:M})}}catch(E){U.e(E)}finally{U.f()}return m.length?m:null}},QIMA_MULTI_COLON_EACH_SUPER:{pattern:/^(\d{7}(?::|：\d{7})*)各(\d+)$/,handler:function(e,n){var r=n.split(/:|：/).map((function(e){return e.trim()})).filter((function(e){return 7===e.length})),o=e[2];return r.length?[{methodId:t("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:r.join(","),money:o}]:null}},QIMA_MULTI_YIGE_EACH_PLUS:{pattern:/^([\d一]+)一([\d一]+)一?各?(\d+)\+(\d+)$/,handler:function(e,n){var r=n.split("一").map((function(e){return e.replace(/[^\d]/g,"")})).filter((function(e){return 7===e.length})),o=e[3],a=e[4];return r.length?[{methodId:t("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:r.join(","),money:o},{methodId:t("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:r.join(","),money:a}]:null}},QIMA_DIAN_XMA_ZULIU_FANG_ZUSAN:{pattern:/^(\d{7})[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]+7码(\d+)防(\d+)$/,handler:function(e,n){return[{methodId:t("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:e[1],money:e[2]},{methodId:t("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:e[1],money:e[3]}]}},QIMA_MULTI_NUMBERS_EACH_FANG:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥一－。%…&!、＝=·吊赶打各]+)各(\d+)(防|防对|放对)(\d+)$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=r.METHOD_ID,u=e[1].split(o).map((function(e){return e.trim()})).filter((function(e){return 7===e.length}));if(!u.length)return null;var d=e[2],l=e[4];return[{methodId:a.QIMA_ZULIU,betNumbers:u.join(","),money:d},{methodId:a.QIMA_ZUSAN,betNumbers:u.join(","),money:l}]}},QIMA_YI_YI_ONE_MONEY:{pattern:/^(\d{7})一(\d{7})一个(\d+)(元|块|米)?$/,handler:function(e,n){var r=e[1],o=e[2],a=e[3];return 7!==r.length||7!==o.length?null:[{methodId:t("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:r+","+o,money:a}]}},QIMA_GE_MONEY:{pattern:/^(\d{7})[，,、\s]*个(\d+)(元|块|米)?$/,handler:function(e,n){var r=e[1],o=e[2];return 7!==r.length?null:[{methodId:t("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:r,money:o}]}},MULTI_LINE_QIMA_MONEY_GROUP:{pattern:/^((?:\d{7}[\-—~～@#￥%…&!、\\/*,一－。.＝=·吊赶打各\s]+\d+\s*\n?)+)$/m,handler:function(e,n){var r,u=t("f9d6"),d=u.METHOD_ID,l=n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),I=new Map,s=Object(a["a"])(l);try{for(s.s();!(r=s.n()).done;){var i=r.value,_=i.match(/^(\d{7})[\-—~～@#￥%…&!、\\/*,一－。.＝=·吊赶打各\s]+([0-9]+)$/);if(_){var c=_[1],h=_[2];I.has(h)||I.set(h,[]),I.get(h).push(c)}}}catch(E){s.e(E)}finally{s.f()}var f,m=[],U=Object(a["a"])(I.entries());try{for(U.s();!(f=U.n()).done;){var A=Object(o["a"])(f.value,2),M=A[0],N=A[1];m.push({methodId:d.QIMA_ZULIU,betNumbers:N.join(","),money:M})}}catch(E){U.e(E)}finally{U.f()}return m.length?m:null}},QIMA_MULTI_LINE_ZULIU_MONEY_SUPER:{pattern:/^([\s\S]+)$/m,handler:function(e,n){var r,u=t("f9d6"),d=u.METHOD_ID,l=n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),I=new Map,s=/^(\d{7})[\-—~～@#￥%…&!、一－。\\/*,，.＝=·\s]+(\d+)(元|米|块)?([，,./\\*\-\=。·、\s]*)?$/,i=[],_=Object(a["a"])(l);try{for(_.s();!(r=_.n()).done;){var c=r.value,h=c.match(s);if(!h)return console.log('【QIMA_MULTI_LINE_ZULIU_MONEY_SUPER】行 "'.concat(c,'" 不匹配七码格式，返回null')),null;if(h[1].length===h[2].length)return console.log('【QIMA_MULTI_LINE_ZULIU_MONEY_SUPER】行 "'.concat(c,'" 号码和金额长度相同，可能误匹配，返回null')),null;i.push({line:c,match:h})}}catch(T){_.e(T)}finally{_.f()}console.log("【QIMA_MULTI_LINE_ZULIU_MONEY_SUPER】所有 ".concat(i.length," 行都是七码格式，继续处理"));for(var f=0,m=i;f<m.length;f++){var U=m[f].match,A=U[1],M=U[2];I.has(M)||I.set(M,[]),I.get(M).push(A)}var N,E=[],b=Object(a["a"])(I.entries());try{for(b.s();!(N=b.n()).done;){var D=Object(o["a"])(N.value,2),p=D[0],O=D[1];E.push({methodId:d.QIMA_ZULIU,betNumbers:O.join(","),money:p})}}catch(T){b.e(T)}finally{b.f()}return console.log("【QIMA_MULTI_LINE_ZULIU_MONEY_SUPER】处理完成，返回 ".concat(E.length," 个结果组")),E.length?E:null}},QIMA_MULTI_FANGDUI_SUPER:{pattern:/^([0-9]{7}(?:[\-—–―－﹣﹘⸺⸻~～@#￥%…&!、\\/*,，.一－。＝=·\s+吊赶打各]+[0-9]{7})+)[\-—–―－﹣﹘⸺⸻~～@#￥%…&!、\\/*,，.一－。＝=·\s+吊赶打各]*(防对|防|组三)([0-9]+)$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=r.METHOD_ID,u=e[1].split(o).map((function(e){return e.trim()})).filter((function(e){return 7===e.length})),d=e[3];return u.length<2?null:[{methodId:a.QIMA_ZUSAN,betNumbers:u.join(","),money:d}]}},QIMA_MULTI_LINE_ZULIU_ZUSAN_KEYWORD:{pattern:/^([0-9]{7}(组六|组三)\d+(元|米|块)?[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各\s]*\n?)+$/m,handler:function(e,n){var t,r=n.split(/\r?\n/).map((function(e){return e.trim()})).filter(Boolean),d=new Map,l=Object(a["a"])(r);try{for(l.s();!(t=l.n()).done;){var I=t.value,s=I.match(/^([0-9]{7})(组六|组三)(\d+)(元|米|块)?[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各\s]*$/);if(s&&7===s[1].length){var i=s[1],_=s[2],c=s[3],h=_+"_"+c;d.has(h)||d.set(h,[]),d.get(h).push(i)}}}catch(O){l.e(O)}finally{l.f()}var f,m=[],U=Object(a["a"])(d.entries());try{for(U.s();!(f=U.n()).done;){var A=Object(o["a"])(f.value,2),M=A[0],N=A[1],E=M.split("_"),b=Object(o["a"])(E,2),D=b[0],p=b[1];m.push({methodId:"组六"===D?u["METHOD_ID"].QIMA_ZULIU:u["METHOD_ID"].QIMA_ZUSAN,betNumbers:N.join(","),money:p})}}catch(O){U.e(O)}finally{U.f()}return m.length?m:null}},QIMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER:{pattern:/^([\s\S]+)$/m,handler:function(e,n){var r=/(福彩|福|3D|3d)/.test(n),u=/(体彩|体|排|排三)/.test(n);if(r||u)return null;var d,l=n.split(/\r?\n/).map((function(e){return e.trim()})).filter(Boolean),I=new Map,s=new Map,i=/^([0-9]{7})[\-—~～@#￥%…&!、\\/*,一－–.。＝=·\s\/]+(\d+)\++(\d+)[元米块]*$/,_=/^([0-9]{7})[\-—~～@#￥%…&!、\\/*,一－–.。＝=·\s\/]+(\d+)[元米块]*$/,c=[],h=Object(a["a"])(l);try{for(h.s();!(d=h.n()).done;){var f=d.value,m=f.match(i);if(m)c.push({line:f,type:"zuliuZusan",match:m});else{if(m=f.match(_),!m)return console.log('【QIMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】行 "'.concat(f,'" 不匹配七码格式，返回null')),null;c.push({line:f,type:"zuliu",match:m})}}}catch(G){h.e(G)}finally{h.f()}console.log("【QIMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】所有 ".concat(c.length," 行都是七码格式，继续处理"));for(var U=0,A=c;U<A.length;U++){var M=A[U],N=M.match,E=M.type;if("zuliuZusan"===E){var b=N[1],D=N[2],p=N[3];I.has(D)||I.set(D,[]),I.get(D).push(b),s.has(p)||s.set(p,[]),s.get(p).push(b)}else if("zuliu"===E){var O=N[1],T=N[2];I.has(T)||I.set(T,[]),I.get(T).push(O)}}var L,g=[],S=Object(a["a"])(I.entries());try{for(S.s();!(L=S.n()).done;){var v=Object(o["a"])(L.value,2),y=v[0],Z=v[1];g.push({methodId:t("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:Z.join(","),money:y})}}catch(G){S.e(G)}finally{S.f()}var H,R=Object(a["a"])(s.entries());try{for(R.s();!(H=R.n()).done;){var j=Object(o["a"])(H.value,2),$=j[0],P=j[1];g.push({methodId:t("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:P.join(","),money:$})}}catch(G){R.e(G)}finally{R.f()}return console.log("【QIMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】处理完成，返回 ".concat(g.length," 个结果组")),g.length?g:null}},QIMA_MONEY_UNIT_FANGDUI_SUPER:{pattern:/^(\d{7})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:元|米|块)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(防|防对|组三)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*$/,handler:function(e,n){var t=e[1],r=e[2];e[3];return 7!==t.length?null:[{methodId:u["METHOD_ID"].QIMA_ZUSAN,betNumbers:t,money:r}]}},LIUMA_TO_QIMA_MULTI_TRANSFORM:{pattern:/^((?:\d{6}[\-—~～@#￥%…&!、\\/*,，.＝=·\s]+){1,}\d{6})(各)?(转|套)[\s]*([0-9]+)\+([0-9]+)$/,handler:function(e,n){var o=t("f9d6"),a=o.SUPER_SEPARATORS,u=e[1],d=e[4],l=e[5],I=u.split(a).filter((function(e){return 6===e.length})),s=[];return I.forEach((function(e){var n=e.split(""),t="0123456789".split("").filter((function(e){return!n.includes(e)})),o=t.map((function(n){return e+n})).sort(),a=o.filter((function(e){return!s.includes(e)}));s.push.apply(s,Object(r["a"])(a))})),[{methodId:t("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:s.join(","),money:d},{methodId:t("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:s.join(","),money:l}]}}};Object.keys(d).forEach((function(e){var n=d[e].pattern;"string"===typeof n&&n.endsWith("$")?d[e].pattern=n.replace(/\$$/,"[，,./*-=。·、s]*$"):n instanceof RegExp&&n.source.endsWith("$")&&(d[e].pattern=new RegExp(n.source.replace(/\$$/,"[，,./*-=。·、s]*$")))}))},3506:function(e,n,t){"use strict";t.r(n),t.d(n,"NINE_CODE_PATTERNS",(function(){return u}));var r=t("3835"),o=t("b85c"),a=(t("99af"),t("4de4"),t("a15b"),t("d81d"),t("14d9"),t("4ec9"),t("b64b"),t("d3b7"),t("4d63"),t("c607"),t("ac1f"),t("2c3e"),t("00b4"),t("25f0"),t("8a79"),t("3ca3"),t("466d"),t("5319"),t("1276"),t("498a"),t("0643"),t("76d6"),t("2382"),t("4e3e"),t("a573"),t("9a9a"),t("159b"),t("ddb0"),t("f9d6")),u={JIUMA_TRANSFORM:{pattern:/^(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?(\d{9})(转|套|拖)(?:各)?(\d+)(?:\+(\d+))?(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?$/,handler:function(e,n){var t=e[2],r=e[3],o=e[4],u=[];return o?(u.push({methodId:a["METHOD_ID"].JIUMA_ZULIU,betNumbers:t,money:r}),u.push({methodId:a["METHOD_ID"].JIUMA_ZUSAN,betNumbers:t,money:o})):u.push({methodId:a["METHOD_ID"].JIUMA_ZULIU,betNumbers:t,money:r}),console.log("九码 handler 返回:",u),u}},JIUMA_ZUSAN:{pattern:/^(\d{9})[\-—~～@#￥%…&!、\\/*,，一－.＝=·\s]*组三?[\-—~～@#￥%…&!、\\/*,，。，—一－.＝=·\s]*([\d]+)?(元|块|米)?[\-—~～@#￥%…&!、。，—一－\\/*,，.＝=·\s]*$/,handler:function(e,n){return 9!==e[1].length?null:[{methodId:a["METHOD_ID"].JIUMA_ZUSAN,betNumbers:e[1],money:e[2]||""}]}},JIUMA_FANGDUI:{pattern:/^(\d{9})防对?(?:-?(\d+))?$/,handler:function(e,n){return 9!==e[1].length?null:[{methodId:a["METHOD_ID"].JIUMA_ZUSAN,betNumbers:e[1],money:e[2]||""}]}},MULTI_JIUMA:{pattern:/^(\d{9}(?:[^0-9]+\d{9})*)(?:各([一二三四五六七八九十百千万\d]+))?(?:防([一二三四五六七八九十百千万\d]+))?$/,handler:function(e,n){var r=t("f9d6"),o=r.chineseToNumber,u=n.split(a["BASE_PATTERNS"].SEPARATORS).filter(Boolean).filter((function(e){return 9===e.length}));if(!u.length||!u.every((function(e){return 9===e.length})))return null;var d=e[2]?o(e[2]):"",l=e[3]?o(e[3]):"",I=[];return d&&I.push({methodId:a["METHOD_ID"].JIUMA_ZULIU,betNumbers:u.join(","),money:d}),l&&I.push({methodId:a["METHOD_ID"].JIUMA_ZUSAN,betNumbers:u[u.length-1],money:l}),I.length?I:null}},JIUMA_ZULIU_ZUSAN_MULTI_MONEY_SUPER:{pattern:/^[－\-—~～@#￥%…&!、\\/*,一－。＝=·\s+吊赶打各]*(\d{9}(?:[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]+\d{9})*)[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]+(\d+)[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]*[+](\d+)(?:元)?$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=e[1].split(o).filter(Boolean),u=e[2],d=e[3];return[{methodId:t("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:a.join(","),money:u},{methodId:t("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:a.join(","),money:d}]}},JIUMA_ZULIU_ZU_SUPER:{pattern:/^(\d{9})组(\d+)$/,handler:function(e,n){var r=e[1],o=e[2];return[{methodId:t("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:r,money:o}]}},JIUMA_ZULIU_MULTI_SUPER:{pattern:/^(\d{9}(?:[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]+\d{9})*)[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]+(\d+)$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=e[1].split(o).filter(Boolean),u=e[2];return[{methodId:t("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:a.join(","),money:u}]}},JIUMA_ZUSAN_ZU_SUPER:{pattern:/^(\d{9})组三(\d+)$/,handler:function(e,n){var r=e[1],o=e[2];return[{methodId:t("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:r,money:o}]}},JIUMA_ZHIZU_MULTI_SUPER:{pattern:/^(\d{9}(?:[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]+\d{9})*)[\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]*(直组|组直|值组|组值)[\s]*各?(\d+)(?:元)?$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=e[1].split(o).filter(Boolean),u=e[3];return[{methodId:t("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:a.join(","),money:u},{methodId:t("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:a.join(","),money:u}]}},JIUMA_ZULIU_FANG:{pattern:/^(\d{9})-(\d+)防(\d+)$/,handler:function(e,n){var t=e[1],r=e[2],o=e[3];return[{methodId:a["METHOD_ID"].JIUMA_ZULIU,betNumbers:t,money:r},{methodId:a["METHOD_ID"].JIUMA_ZUSAN,betNumbers:t,money:o}]}},JIUMA_MULTI_ZHIZU_EACH_SUPER:{pattern:/^(直组|组直|值组|组值)各([0-9]+)[\s\+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各\n]+([\d\s\+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]+)$/i,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=e[2],u=e[3].replace(/\n/g," "),d=u.split(o).map((function(e){return e.trim()})).filter((function(e){return/^\d{9}$/.test(e)}));return[{methodId:t("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:d.join(","),money:a},{methodId:t("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:d.join(","),money:a}]}},JIUMA_MULTI_EACH_SUPER:{pattern:/^(\d{9}(?:[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]+\d{9})*)各(\d+)$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=e[1].split(o).filter(Boolean);if(!a.length||a.some((function(e){return 9!==e.length})))return[];var u=e[2];return[{methodId:t("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:a.join(","),money:u}]}},JIUMA_SLASH_TWO_MONEY:{pattern:/^(?!.*[\r\n])(\d{9})[\-—~～@#￥%…&!、\\/*,，.。\u4e00\uff0d\uff1d=· 　\t]+(\d+)[+]+(\d+)$/,handler:function(e,n){var r=e[1],o=e[2],a=e[3];return[{methodId:t("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:r,money:o},{methodId:t("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:r,money:a}]}},JIUMA_MULTI_SUPER_SLASH_TWO_MONEY:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、＝一－。=·吊赶打各]+)\/(\d+)\/(\d+)$/,handler:function(e,n){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],o=t("f9d6"),a=o.SUPER_SEPARATORS,u=e[1].split(a).map((function(e){return e.trim()})).filter((function(e){return 9===e.length}));if(!u.length)return[];var d=e[2],l=e[3];return r&&(/^\d{9}$/.test(d)||/^\d{9}$/.test(l))?null:[{methodId:t("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:u.join(","),money:d},{methodId:t("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:u.join(","),money:l}]}},JIUMA_UNIVERSAL_SUPER:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～一－—。@#￥%…&!、＝=·吊赶打各]+)[\-~～@#￥%…&!、\\/*,，一－—。.＝=·\s+吊赶打各]+([0-9]+)(元|米|块)?$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=r.METHOD_ID,u=e[1].split(o).map((function(e){return e.trim()})).filter(Boolean),d=e[2];return 1===u.length&&9===u[0].length?[{methodId:a.JIUMA_ZULIU,betNumbers:u[0],money:d}]:u.length>=2&&u.every((function(e){return 9===e.length}))?[{methodId:a.JIUMA_ZULIU,betNumbers:u.join(","),money:d}]:null}},JIUMA_EACH_PLUS_SUPER:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!一－。、＝=·吊赶打各]+)各([0-9]+)\+([0-9]+)$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=r.METHOD_ID,u=e[1].split(o).map((function(e){return e.trim()})).filter(Boolean),d=e[2],l=e[3];return 1===u.length&&9===u[0].length?[{methodId:a.JIUMA_ZULIU,betNumbers:u[0],money:d},{methodId:a.JIUMA_ZUSAN,betNumbers:u[0],money:l}]:u.length>=2&&u.every((function(e){return 9===e.length}))?[{methodId:a.JIUMA_ZULIU,betNumbers:u.join(","),money:d},{methodId:a.JIUMA_ZUSAN,betNumbers:u.join(","),money:l}]:null}},JIUMA_UNIVERSAL_SUPER_NEW:{pattern:/^([0-9]{9,})[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]+(\d+)(元|块|米)?$/,handler:function(e,n){var r=e[1],o=e[2];return r&&9===r.length?[{methodId:t("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:r,money:o}]:null}},JIUMA_DA_MONEY:{pattern:/^([0-9]{9})打(\d+)(元|块|米)?$/,handler:function(e,n){return[{methodId:t("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:e[1],money:e[2]}]}},JIUMA_MULTI_WITH_MONEY_SLASH_PLUS:{pattern:/^(\d{9}(?:[\s,，\-]+?\d{9})*)\/\s*(\d+)\+(\d+)$/,handler:function(e,n){var r=e[1].split(/[\s,，\-]+/).filter(Boolean),o=e[2],a=e[3];return[{methodId:t("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:r.join(","),money:o},{methodId:t("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:r.join(","),money:a}]}},JIUMA_MULTI_LINE_MONEY_PLUS:{pattern:/^((?:\d{9}[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+\d+\+\d+\s*\n?)+)$/m,handler:function(e,n){var a,u=t("f9d6"),d=u.METHOD_ID,l=n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),I=new Map,s=!0,i=Object(o["a"])(l);try{for(i.s();!(a=i.n()).done;){var _=a.value,c=_.match(/^(\d{9})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+(\d+)\+(\d+)$/);if(!c){s=!1;break}var h=c[1];if(9!==h.length){s=!1;break}var f=_.split(/[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+/)[0];if(9!==f.length||!/^\d{9}$/.test(f)){s=!1;break}var m="".concat(c[2],"+").concat(c[3]);I.has(m)||I.set(m,[]),I.get(m).push(h)}}catch(L){i.e(L)}finally{i.f()}if(!s||0===I.size)return null;var U,A=[],M=Object(o["a"])(I.entries());try{for(M.s();!(U=M.n()).done;){var N=Object(r["a"])(U.value,2),E=N[0],b=N[1],D=E.split("+"),p=Object(r["a"])(D,2),O=p[0],T=p[1];A.push({methodId:d.JIUMA_ZULIU,betNumbers:b.join(","),money:O}),A.push({methodId:d.JIUMA_ZUSAN,betNumbers:b.join(","),money:T})}}catch(L){M.e(L)}finally{M.f()}return A.length?A:null}},JIUMA_MULTI_LINE_MONEY_PLUS_CN:{pattern:/^((?:\d{9}[，,、]?[一二三四五六七八九十百千万\d]+加[一二三四五六七八九十百千万\d]+\s*\n?)+)$/m,handler:function(e,n){var a,u=t("f9d6"),d=u.METHOD_ID,l=u.chineseToNumber,I=n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),s=new Map,i=Object(o["a"])(I);try{for(i.s();!(a=i.n()).done;){var _=a.value,c=_.match(/^(\d{9})[，,、]?([一二三四五六七八九十百千万\d]+)加([一二三四五六七八九十百千万\d]+)$/);if(c){var h=c[1],f=l(c[2]),m=l(c[3]),U="".concat(f,"+").concat(m);s.has(U)||s.set(U,[]),s.get(U).push(h)}}}catch(g){i.e(g)}finally{i.f()}var A,M=[],N=Object(o["a"])(s.entries());try{for(N.s();!(A=N.n()).done;){var E=Object(r["a"])(A.value,2),b=E[0],D=E[1],p=b.split("+"),O=Object(r["a"])(p,2),T=O[0],L=O[1];M.push({methodId:d.JIUMA_ZULIU,betNumbers:D.join(","),money:T}),M.push({methodId:d.JIUMA_ZUSAN,betNumbers:D.join(","),money:L})}}catch(g){N.e(g)}finally{N.f()}return M.length?M:null}},JIUMA_MULTI_EACH_FANGDUI_SLASH:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、一－。＝=·吊赶打各]+)[/\-~～@#￥%…&!、\\/*,，.一－。＝=·吊赶打各]+各防对([一二三四五六七八九十百千万\d]+)$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=r.METHOD_ID,u=r.chineseToNumber,d=e[1].split(o).map((function(e){return e.trim()})).filter((function(e){return 9===e.length})),l=u(e[2]);return d.length?[{methodId:a.JIUMA_ZUSAN,betNumbers:d.join(","),money:l}]:null}},JIUMA_MULTI_EACH_FANGDUI:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&一－。!、＝=·吊赶打各]+)各防对([一二三四五六七八九十百千万\d]+)$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=r.METHOD_ID,u=r.chineseToNumber,d=e[1].split(o).map((function(e){return e.trim()})).filter((function(e){return 9===e.length})),l=u(e[2]);return d.length?[{methodId:a.JIUMA_ZUSAN,betNumbers:d.join(","),money:l}]:null}},JIUMA_SLASH_MONEY:{pattern:/^\s*(\d{9})\s*\/\s*([一二三四五六七八九十百千万\d]+)\s*$/,handler:function(e,n){var r=t("f9d6"),o=r.METHOD_ID,a=r.chineseToNumber,u=e[1],d=a(e[2]);return[{methodId:o.JIUMA_ZULIU,betNumbers:u,money:d}]}},JIUMA_ZULIU_ZUSAN_MONEY_PAIR:{pattern:/^(\d{9}[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*)组六[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*(\d+)[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*组三[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*(\d+)$/,handler:function(e,n){var t=e[1],r=e[2],o=e[3];return[{methodId:a["METHOD_ID"].JIUMA_ZULIU,betNumbers:t,money:r},{methodId:a["METHOD_ID"].JIUMA_ZUSAN,betNumbers:t,money:o}]}},JIUMA_ZULIU:{pattern:/^(\d{9})组六?(\d+)[，,./\*\-一－。\=。·、\s]*$/,handler:function(e,n){var t=e[1];if(9!==t.length)return null;var r=e[2]||"";return[{methodId:a["METHOD_ID"].JIUMA_ZULIU,betNumbers:t,money:r}]}},JIUMA_ZULIU_ZUSAN_COMBO:{pattern:/^(\d{9}(?:[+＋\\-—~～@#￥%…&!、\\/*,，。一—~.＝=·吊赶打各\s]+\d{9})*)\/(\d+)\+(\d+)$/,handler:function(e,n){var r=t("f9d6"),o=(r.SUPER_SEPARATORS,e[1].split(/[。.．、,，\s]+/).map((function(e){return e.trim()})).filter(Boolean)),a=e[2],u=e[3];return o.length&&o.every((function(e){return 9===e.length}))?[{methodId:t("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:o.join(","),money:a},{methodId:t("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:o.join(","),money:u}]:null}},JIUMA_SINGLE_LINE_ZULIU_ZUSAN_PLUS:{pattern:/^(\d{9})\s+(\d+)[+＋\\-—~～@#￥%…&!、\\/*,，。一—~.＝=·吊赶打各](\d+)$/,handler:function(e,n){return[{methodId:t("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:e[1],money:e[2]},{methodId:t("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:e[1],money:e[3]}]}},JIUMA_MULTI_LINE_ZULIU_ZUSAN_PLUS:{pattern:/^([\s\S]+)$/m,handler:function(e,n){var a,u=t("f9d6"),d=u.SUPER_SEPARATORS,l=n.split(/\r?\n/).map((function(e){return e.trim()})).filter(Boolean),I=new Map,s=new Map,i=Object(o["a"])(l);try{for(i.s();!(a=i.n()).done;){var _=a.value,c=_.match(new RegExp("^(\\d{9})".concat(d.source,"(\\d+)\\+(\\d+)$")));if(c){var h=c[1],f=c[2],m=c[3],U=f;I.has(U)||I.set(U,[]),I.get(U).push(h);var A=m;s.has(A)||s.set(A,[]),s.get(A).push(h)}else{var M=_.match(new RegExp("^(\\d{9})".concat(d.source,"(\\d+)$")));if(M){var N=M[1],E=M[2];I.has(E)||I.set(E,[]),I.get(E).push(N)}}}}catch(H){i.e(H)}finally{i.f()}var b,D=[],p=Object(o["a"])(I.entries());try{for(p.s();!(b=p.n()).done;){var O=Object(r["a"])(b.value,2),T=O[0],L=O[1];D.push({methodId:t("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:L.join(","),money:T})}}catch(H){p.e(H)}finally{p.f()}var g,S=Object(o["a"])(s.entries());try{for(S.s();!(g=S.n()).done;){var v=Object(r["a"])(g.value,2),y=v[0],Z=v[1];D.push({methodId:t("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:Z.join(","),money:y})}}catch(H){S.e(H)}finally{S.f()}return D.length?D:null}},JIUMA_MULTI_LINE_ZULIU_PLUS:{pattern:/^((?:\d{9}[+＋\\-—~～@#￥%…&!、\\/*,，。一—~.＝=·吊赶打各]\d+\s*\n?)+)$/m,handler:function(e,n){var a,u=t("f9d6"),d=u.METHOD_ID,l=n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),I=new Map,s=[],i=Object(o["a"])(l);try{for(i.s();!(a=i.n()).done;){var _=a.value,c=_.match(/^(\d{9})[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]+(\d+)$/);if(c){var h=c[1],f=c[2];I.has(f)||I.set(f,[]),I.get(f).push(h)}else s.push(_)}}catch(b){i.e(b)}finally{i.f()}var m,U=[],A=Object(o["a"])(I.entries());try{for(A.s();!(m=A.n()).done;){var M=Object(r["a"])(m.value,2),N=M[0],E=M[1];U.push({methodId:d.JIUMA_ZULIU,betNumbers:E.join(","),money:N})}}catch(b){A.e(b)}finally{A.f()}return U}},JIUMA_ZULIU_FANG_ZUSAN_PLUS:{pattern:/^(\d{9})[\-—~～@#￥%…&!、\\/*,，.一－。＝=·吊赶打各]*(\d+)[\-—~～@#￥%…&!、\\/*,，.一－。＝=·吊赶打各]*(防对|防)(\d+)$/,handler:function(e,n){return[{methodId:t("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:e[1],money:e[2]},{methodId:t("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:e[1],money:e[3]}]}},JIUMA_MULTI_LINE_ZULIU_FANG_ZUSAN_GROUP:{pattern:/^([\s\S]+)$/m,handler:function(e,n){var a,u=n.split(/[\r\n]+/).map((function(e){return e.trim()})).filter(Boolean),d=new Map,l=Object(o["a"])(u);try{for(l.s();!(a=l.n()).done;){var I=a.value,s=I.match(/^(\d{9})-?(\d+)防(\d+)$/);if(s){var i=s[1],_=s[2],c=s[3],h="".concat(_,"|").concat(c);d.has(h)||d.set(h,[]),d.get(h).push(i)}}}catch(O){l.e(O)}finally{l.f()}var f,m=[],U=Object(o["a"])(d.entries());try{for(U.s();!(f=U.n()).done;){var A=Object(r["a"])(f.value,2),M=A[0],N=A[1],E=M.split("|"),b=Object(r["a"])(E,2),D=b[0],p=b[1];m.push({methodId:t("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:N.join(","),money:D}),m.push({methodId:t("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:N.join(","),money:p})}}catch(O){U.e(O)}finally{U.f()}return m.length?m:null}},JIUMA_MULTI_TRANSFORM_EACH:{pattern:/^(\d{8}(?:[\-~～@#￥%…&!、\\/*,一－。，.＝=·\s+吊赶打各]+\d{8})*)(转|套|拖)各(\d+)\+(\d+)$/,handler:function(e,n){var r,a=t("f9d6"),u=a.SUPER_SEPARATORS,d=a.METHOD_ID,l=a.generateTransformNumbers,I=e[1].split(u).filter(Boolean),s=e[3],i=e[4],_=[],c=Object(o["a"])(I);try{for(c.s();!(r=c.n()).done;){var h=r.value,f=l(h);_.push({methodId:d.JIUMA_ZULIU,betNumbers:f.join(","),money:s}),_.push({methodId:d.JIUMA_ZUSAN,betNumbers:f.join(","),money:i})}}catch(m){c.e(m)}finally{c.f()}return _}},JIUMA_MULTI_LINE_TRANSFORM:{pattern:/^((?:(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?\d{9}(转|套|拖)\d+(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?\s*\n?)+)$/m,handler:function(e,n){var a,u=t("f9d6"),d=u.METHOD_ID,l=n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),I=new Map,s=Object(o["a"])(l);try{for(s.s();!(a=s.n()).done;){var i=a.value,_=i.match(/^(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?(\d{9})(转|套|拖)(\d+)(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?$/);if(_){var c=_[2],h=_[3];I.has(h)||I.set(h,[]),I.get(h).push(c)}}}catch(E){s.e(E)}finally{s.f()}var f,m=[],U=Object(o["a"])(I.entries());try{for(U.s();!(f=U.n()).done;){var A=Object(r["a"])(f.value,2),M=A[0],N=A[1];m.push({methodId:d.JIUMA_ZULIU,betNumbers:N.join(","),money:M})}}catch(E){U.e(E)}finally{U.f()}return m.length?m:null}},JIUMA_MULTI_COLON_EACH_SUPER:{pattern:/^(\d{9}(?::|：\d{9})*)各(\d+)$/,handler:function(e,n){var r=n.split(/:|：/).map((function(e){return e.trim()})).filter((function(e){return 9===e.length})),o=e[2];return r.length?[{methodId:t("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:r.join(","),money:o}]:null}},JIUMA_MULTI_YIGE_EACH_PLUS:{pattern:/^([\d一]+)一([\d一]+)一?各?(\d+)\+(\d+)$/,handler:function(e,n){var r=n.split("一").map((function(e){return e.replace(/[^\d]/g,"")})).filter((function(e){return 9===e.length})),o=e[3],a=e[4];return r.length?[{methodId:t("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:r.join(","),money:o},{methodId:t("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:r.join(","),money:a}]:null}},JIUMA_DIAN_XMA_ZULIU_FANG_ZUSAN:{pattern:/^(\d{9})[。.．、,，\s]+9码(\d+)防(\d+)$/,handler:function(e,n){return[{methodId:t("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:e[1],money:e[2]},{methodId:t("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:e[1],money:e[3]}]}},JIUMA_MULTI_LINE_ZULIU_MONEY_GROUP:{pattern:/^((?:\d{9}\s+\d+\s*\n?)+)$/m,handler:function(e,n){var a,u=t("f9d6"),d=u.METHOD_ID,l=n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),I=new Map,s=Object(o["a"])(l);try{for(s.s();!(a=s.n()).done;){var i=a.value,_=i.match(/^(\d{9})\s+(\d+)$/);if(_){var c=_[1],h=_[2];I.has(h)||I.set(h,[]),I.get(h).push(c)}}}catch(E){s.e(E)}finally{s.f()}var f,m=[],U=Object(o["a"])(I.entries());try{for(U.s();!(f=U.n()).done;){var A=Object(r["a"])(f.value,2),M=A[0],N=A[1];m.push({methodId:d.JIUMA_ZULIU,betNumbers:N.join(","),money:M})}}catch(E){U.e(E)}finally{U.f()}return m.length?m:null}},JIUMA_MULTI_NUMBERS_EACH_FANG:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、一－。＝=·吊赶打各]+)各(\d+)(防|防对|放对)(\d+)$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=r.METHOD_ID,u=e[1].split(o).map((function(e){return e.trim()})).filter((function(e){return 9===e.length}));if(!u.length)return null;var d=e[2],l=e[4];return[{methodId:a.JIUMA_ZULIU,betNumbers:u.join(","),money:d},{methodId:a.JIUMA_ZUSAN,betNumbers:u.join(","),money:l}]}},JIUMA_YI_YI_ONE_MONEY:{pattern:/^(\d{9})一(\d{9})一个(\d+)(元|块|米)?$/,handler:function(e,n){var r=e[1],o=e[2],a=e[3];return 9!==r.length||9!==o.length?null:[{methodId:t("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:r+","+o,money:a}]}},JIUMA_GE_MONEY:{pattern:/^(\d{9})[，,、\s]*个(\d+)(元|块|米)?$/,handler:function(e,n){var r=e[1],o=e[2];return 9!==r.length?null:[{methodId:t("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:r,money:o}]}},MULTI_LINE_JIUMA_MONEY_GROUP:{pattern:/^((?:\d{9}[\-—~～@#￥%…&!、\\/*,一－。.＝=·吊赶打各\s]+\d+\s*\n?)+)$/m,handler:function(e,n){var a,u=t("f9d6"),d=u.METHOD_ID,l=n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),I=new Map,s=Object(o["a"])(l);try{for(s.s();!(a=s.n()).done;){var i=a.value,_=i.match(/^(\d{9})[\-—~～@#￥%…&!、\\/*,一－。.＝=·吊赶打各\s]+([0-9]+)$/);if(_){var c=_[1],h=_[2];I.has(h)||I.set(h,[]),I.get(h).push(c)}}}catch(E){s.e(E)}finally{s.f()}var f,m=[],U=Object(o["a"])(I.entries());try{for(U.s();!(f=U.n()).done;){var A=Object(r["a"])(f.value,2),M=A[0],N=A[1];m.push({methodId:d.JIUMA_ZULIU,betNumbers:N.join(","),money:M})}}catch(E){U.e(E)}finally{U.f()}return m.length?m:null}},JIUMA_MULTI_LINE_ZULIU_MONEY_SUPER:{pattern:/^([\s\S]+)$/m,handler:function(e,n){var a,u=t("f9d6"),d=u.METHOD_ID,l=n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),I=new Map,s=/^(\d{9})[\-—~～@#￥%…&!、一－。\\/*,，.＝=·\s]+(\d+)(元|米|块)?([，,./\\*\-\=。·、\s]*)?$/,i=[],_=Object(o["a"])(l);try{for(_.s();!(a=_.n()).done;){var c=a.value,h=c.match(s);if(!h)return console.log('【JIUMA_MULTI_LINE_ZULIU_MONEY_SUPER】行 "'.concat(c,'" 不匹配九码格式，返回null')),null;if(h[1].length===h[2].length)return console.log('【JIUMA_MULTI_LINE_ZULIU_MONEY_SUPER】行 "'.concat(c,'" 号码和金额长度相同，可能误匹配，返回null')),null;i.push({line:c,match:h})}}catch(T){_.e(T)}finally{_.f()}console.log("【JIUMA_MULTI_LINE_ZULIU_MONEY_SUPER】所有 ".concat(i.length," 行都是九码格式，继续处理"));for(var f=0,m=i;f<m.length;f++){var U=m[f].match,A=U[1],M=U[2];I.has(M)||I.set(M,[]),I.get(M).push(A)}var N,E=[],b=Object(o["a"])(I.entries());try{for(b.s();!(N=b.n()).done;){var D=Object(r["a"])(N.value,2),p=D[0],O=D[1];E.push({methodId:d.JIUMA_ZULIU,betNumbers:O.join(","),money:p})}}catch(T){b.e(T)}finally{b.f()}return console.log("【JIUMA_MULTI_LINE_ZULIU_MONEY_SUPER】处理完成，返回 ".concat(E.length," 个结果组")),E.length?E:null}},JIUMA_MULTI_FANGDUI_SUPER:{pattern:/^([0-9]{9}(?:[\-—–―－﹣﹘⸺⸻~～@#￥%…&!、\\/*,，.一－。＝=·\s+吊赶打各]+[0-9]{9})+)[\-—–―－﹣﹘⸺⸻~～@#￥%…&!、\\/*,，.一－。＝=·\s+吊赶打各]*(防对|防|组三)([0-9]+)$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=r.METHOD_ID,u=e[1].split(o).map((function(e){return e.trim()})).filter((function(e){return 9===e.length})),d=e[3];return u.length<2?null:[{methodId:a.JIUMA_ZUSAN,betNumbers:u.join(","),money:d}]}},JIUMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER:{pattern:/^([\s\S]+)$/m,handler:function(e,n){var a=/(福彩|福|3D|3d)/.test(n),u=/(体彩|体|排|排三)/.test(n);if(a||u)return null;var d,l=n.split(/\r?\n/).map((function(e){return e.trim()})).filter(Boolean),I=new Map,s=new Map,i=/^([0-9]{9})[\-—~～@#￥%…&!、\\/*,一－–.。＝=·\s\/]+(\d+)\++(\d+)[元米块]*$/,_=/^([0-9]{9})[\-—~～@#￥%…&!、\\/*,一－–.。＝=·\s\/]+(\d+)[元米块]*$/,c=[],h=Object(o["a"])(l);try{for(h.s();!(d=h.n()).done;){var f=d.value,m=f.match(i);if(m)c.push({line:f,type:"zuliuZusan",match:m});else{if(m=f.match(_),!m)return console.log('【JIUMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】行 "'.concat(f,'" 不匹配九码格式，返回null')),null;c.push({line:f,type:"zuliu",match:m})}}}catch(G){h.e(G)}finally{h.f()}console.log("【JIUMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】所有 ".concat(c.length," 行都是九码格式，继续处理"));for(var U=0,A=c;U<A.length;U++){var M=A[U],N=M.match,E=M.type;if("zuliuZusan"===E){var b=N[1],D=N[2],p=N[3];I.has(D)||I.set(D,[]),I.get(D).push(b),s.has(p)||s.set(p,[]),s.get(p).push(b)}else if("zuliu"===E){var O=N[1],T=N[2];I.has(T)||I.set(T,[]),I.get(T).push(O)}}var L,g=[],S=Object(o["a"])(I.entries());try{for(S.s();!(L=S.n()).done;){var v=Object(r["a"])(L.value,2),y=v[0],Z=v[1];g.push({methodId:t("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:Z.join(","),money:y})}}catch(G){S.e(G)}finally{S.f()}var H,R=Object(o["a"])(s.entries());try{for(R.s();!(H=R.n()).done;){var j=Object(r["a"])(H.value,2),$=j[0],P=j[1];g.push({methodId:t("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:P.join(","),money:$})}}catch(G){R.e(G)}finally{R.f()}return console.log("【JIUMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】处理完成，返回 ".concat(g.length," 个结果组")),g.length?g:null}},JIUMA_MONEY_UNIT_FANGDUI_SUPER:{pattern:/^(\d{9})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:元|米|块)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(防|防对|组三)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*$/,handler:function(e,n){var t=e[1],r=e[2];e[3];return 9!==t.length?null:[{methodId:a["METHOD_ID"].JIUMA_ZUSAN,betNumbers:t,money:r}]}},JIUMA_MULTI_LINE_ZULIU_ZUSAN_KEYWORD:{pattern:/^([0-9]{9}(组六|组三)\d+(元|米|块)?[，,./\*\-\=。一－。·、\s]*\n?)+$/m,handler:function(e,n){var t,u=n.split(/\r?\n/).map((function(e){return e.trim()})).filter(Boolean),d=new Map,l=Object(o["a"])(u);try{for(l.s();!(t=l.n()).done;){var I=t.value,s=I.match(/^([0-9]{9})(组六|组三)(\d+)(元|米|块)?[，,./\*\-\=。一－。·、\s]*$/);if(s&&9===s[1].length){var i=s[1],_=s[2],c=s[3],h=_+"_"+c;d.has(h)||d.set(h,[]),d.get(h).push(i)}}}catch(O){l.e(O)}finally{l.f()}var f,m=[],U=Object(o["a"])(d.entries());try{for(U.s();!(f=U.n()).done;){var A=Object(r["a"])(f.value,2),M=A[0],N=A[1],E=M.split("_"),b=Object(r["a"])(E,2),D=b[0],p=b[1];m.push({methodId:"组六"===D?a["METHOD_ID"].JIUMA_ZULIU:a["METHOD_ID"].JIUMA_ZUSAN,betNumbers:N.join(","),money:p})}}catch(O){U.e(O)}finally{U.f()}return m.length?m:null}}};Object.keys(u).forEach((function(e){var n=u[e].pattern;"string"===typeof n&&n.endsWith("$")?u[e].pattern=n.replace(/\$$/,"[，,./*-=。·、s]*$"):n instanceof RegExp&&n.source.endsWith("$")&&(u[e].pattern=new RegExp(n.source.replace(/\$$/,"[，,./*-=。·、s]*$")))}))},4069:function(e,n,t){"use strict";var r=t("44d2");r("flat")},"5c5d":function(e,n,t){"use strict";t.r(n),t.d(n,"TWO_CODE_PATTERNS",(function(){return u}));var r=t("3835"),o=t("b85c"),a=(t("99af"),t("4de4"),t("a630"),t("a15b"),t("d81d"),t("14d9"),t("4ec9"),t("a9e3"),t("b64b"),t("d3b7"),t("4d63"),t("c607"),t("ac1f"),t("2c3e"),t("00b4"),t("25f0"),t("8a79"),t("3ca3"),t("466d"),t("5319"),t("1276"),t("498a"),t("0643"),t("76d6"),t("2382"),t("4e3e"),t("a573"),t("9a9a"),t("159b"),t("ddb0"),t("f9d6")),u={LIANGMA_ZUHE:{pattern:/^(\d{2})(?:组合|组)[\-—~～@#￥%…&!、\\/*,，.＝=·吊赶打各\s]*(\d+)?$/,handler:function(e,n){if(2!==e[1].length)return null;var t=e[1],r=e[2];return t[0]===t[1]?null:[{methodId:a["METHOD_ID"].LIANGMA_ZUHE,betNumbers:t,money:r}]}},LIANGMA_DUIZI:{pattern:/^(\d{2})[\-—~～@#￥%…&!、\\/*,，.＝=·吊赶打各\s]*对子[\-—~～@#￥%…&!、\\/*,，.＝=·吊赶打各\s]*(\d+)?(元|米|块)?$/,handler:function(e,n){if(2!==e[1].length)return null;var t=e[1],r=e[2];return t[0]!==t[1]?null:[{methodId:a["METHOD_ID"].LIANGMA_DUIZI,betNumbers:t,money:r}]}},LIANGMA_ZUSAN:{pattern:/^(\d{2})[\-—~～@#￥%…&!、\\/*,，一－.＝=·\s]*组三[\-—~～@#￥%…&!、\\/*,，.＝=·吊赶打各\s]*(\d+)?(元|米|块)?$/,handler:function(e,n){if(2!==e[1].length)return null;var t=e[1],r=e[2],o=e[3];if(t[0]!==t[1])return null;var u={methodId:a["METHOD_ID"].LIANGMA_DUIZI,betNumbers:t,money:r};return o&&(u.unit=o),[u]}},LIANGMA_ZUHE_DUIZI:{pattern:/^(\d{2})(?:[^0-9]+\d{2})*-(\d+)\+(\d+)$/,handler:function(e,n){var t=n.split(a["BASE_PATTERNS"].SEPARATORS).filter(Boolean).filter((function(e){return 2===e.length})),r=n.split(a["BASE_PATTERNS"].SEPARATORS).filter(Boolean);if(!r.length||r.some((function(e){return 2!==e.length})))return null;var o=e[2],u=e[3];return[{methodId:3,betNumbers:t.join(","),money:o},{methodId:4,betNumbers:t.join(","),money:u}]}},MULTI_LIANGMA:{pattern:/^(\d{2}(?:[^0-9]+\d{2})*)(?:各([一二三四五六七八九十百千万\d]+))?(?:防([一二三四五六七八九十百千万\d]+))?$/,handler:function(e,n){var r=t("f9d6"),o=r.chineseToNumber,u=n.split(a["BASE_PATTERNS"].SEPARATORS).filter(Boolean).filter((function(e){return 2===e.length}));if(!u.length)return null;var d=e[2]?o(e[2]):"",l=e[3]?o(e[3]):"",I=[];return d&&I.push({methodId:a["METHOD_ID"].LIANGMA_ZUHE,betNumbers:u.join(","),money:d}),l&&I.push({methodId:a["METHOD_ID"].LIANGMA_DUIZI,betNumbers:u[u.length-1],money:l}),console.log("两码 handler 返回:",I),I}},LIANGMA_ZUHE_ZU_SUPER:{pattern:/^(\d{2})组(\d+)$/,handler:function(e,n){var r=e[1];if(2!==r.length)return null;var o=e[2];return[{methodId:t("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:r,money:o}]}},LIANGMA_ZUHE_MULTI_SUPER:{pattern:/^(\d{2}(?:[\-~～@#￥%…&!、\\/*一－。,，.＝=· \t+吊赶打各]+\d{2})*)[\-~～@#￥%…&!、\\/*一－。,，.＝=· \t+吊赶打各]+(\d+)(元|块|米)?$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=e[1].split(o).filter(Boolean);if(!a.length||a.some((function(e){return 2!==e.length})))return null;var u=e[2],d=[],l=[];a.forEach((function(e){e[0]===e[1]?l.push(e):d.push(e)}));var I=[];return d.length&&I.push({methodId:t("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:d.join(","),money:u}),l.length&&I.push({methodId:t("f9d6").METHOD_ID.LIANGMA_DUIZI,betNumbers:l.join(","),money:u}),I.length?I:null}},LIANGMA_DUIZI_DUI_SUPER:{pattern:/^(\d{2})对子(\d+)$/,handler:function(e,n){var r=e[1];if(2!==r.length)return null;var o=e[2];return[{methodId:t("f9d6").METHOD_ID.LIANGMA_DUIZI,betNumbers:r,money:o}]}},LIANGMA_SINGLE_WITH_MONEY_SUPER:{pattern:/^(\d{2})[\-~～@#￥%…&!、\\/*,，一－。.＝=·\s+吊赶打各]+(\d{1,4})$/,handler:function(e,n){var r=e[1];if(2!==r.length)return null;var o=e[2];return r[0]===r[1]?[{methodId:t("f9d6").METHOD_ID.LIANGMA_DUIZI,betNumbers:r,money:o}]:[{methodId:t("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:r,money:o}]}},LIANGMA_MULTI_WITH_MONEY_SLASH_SUPER:{pattern:/^(\d{2}(?:[\-~～@#￥%…&!、\\/*,，.＝=一－。·\s+吊赶打各]+\d{2})*)\/(\d{1,4})$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=e[1].split(o).filter(Boolean);if(!a.length||a.some((function(e){return 2!==e.length})))return null;var u=e[2],d=[],l=[];a.forEach((function(e){e[0]===e[1]?l.push(e):d.push(e)}));var I=[];return d.length&&I.push({methodId:t("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:d.join(","),money:u}),l.length&&I.push({methodId:t("f9d6").METHOD_ID.LIANGMA_DUIZI,betNumbers:l.join(","),money:u}),I.length?I:null}},LIANGMA_MULTI_WITH_DOUBLE_MONEY_SLASH_SUPER:{pattern:/^(\d{2}(?:[\-~～@#￥%…&!、\\/*,，一－。.＝=·\s+吊赶打各]+\d{2})*)\/(\d+)-(\d+)$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=e[1].split(o).filter(Boolean);if(!a.length||a.some((function(e){return 2!==e.length})))return null;var u=a,d=e[2],l=e[3];return[{methodId:t("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:u.join(","),money:d},{methodId:t("f9d6").METHOD_ID.LIANGMA_DUIZI,betNumbers:u.join(","),money:l}]}},LIANGMA_ZUHE_FANG:{pattern:/^(\d{2})-(\d+)防(\d+)$/,handler:function(e,n){var r=e[1];if(2!==r.length)return null;var o=e[2],a=e[3];return[{methodId:t("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:r,money:o},{methodId:t("f9d6").METHOD_ID.LIANGMA_DUIZI,betNumbers:r,money:a}]}},LIANGMA_MULTI_ZUHE_DUIZI_SLASH_SUPER:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、＝一－。=·吊赶打各]+)(?:\/|各|\s|吊|赶|打)(\d+)\+(\d+)$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=e[1].split(o).filter(Boolean);if(!a.length||a.some((function(e){return 2!==e.length})))return null;var u=a,d=e[2],l=e[3];return[{methodId:t("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:u.join(","),money:d},{methodId:t("f9d6").METHOD_ID.LIANGMA_DUIZI,betNumbers:u.join(","),money:l}]}},LIANGMA_MULTI_EACH_SUPER:{pattern:/^(\d{2}(?:[\-~～@#￥%…&!、\\/*,，.＝=一－。·\s+吊赶打各]+\d{2})*)各(\d+)(元|块|米)?$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=e[1].split(o).filter(Boolean);if(!a.length||a.some((function(e){return 2!==e.length})))return null;var u=e[2];return[{methodId:t("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:a.join(","),money:u}]}},LIANGMA_MULTI_SUPER_SLASH_TWO_MONEY:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、一－。＝=·吊赶打各]+)\/(\d+)\/(\d+)$/,handler:function(e,n){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],o=t("f9d6"),a=o.SUPER_SEPARATORS,u=e[1].split(a).map((function(e){return e.trim()})).filter((function(e){return 2===e.length}));if(!u.length)return[];var d=e[2],l=e[3];return r&&(/^\d{2}$/.test(d)||/^\d{2}$/.test(l))?null:[{methodId:t("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:u.join(","),money:d},{methodId:t("f9d6").METHOD_ID.LIANGMA_DUIZI,betNumbers:u.join(","),money:l}]}},LIANGMA_MULTI_WITH_MONEY_UNIT_SUPER:{pattern:/^([\s\S]+)$/m,handler:function(e,n){var a,u=n.split(/\n+/).map((function(e){return e.trim()})).filter(Boolean),d=new Map,l=0,I=Object(o["a"])(u);try{for(I.s();!(a=I.n()).done;){var s=a.value,i=s.match(/(\d{2})[\s,，.。\-—~～@#￥%…&!、\\/*]*\/?[\s,，.。\-—~～@#￥%…&!、\\/*]*(\d+)[\s,，.。\-—~～@#￥%…&!、\\/*]*(元|块|米)/);if(i){l++;var _=i[1],c=i[2];d.has(c)||d.set(c,[]),d.get(c).push(_)}}}catch(h){I.e(h)}finally{I.f()}return l<2?null:d.size?Array.from(d.entries()).map((function(e){var n=Object(r["a"])(e,2),o=n[0],a=n[1];return{methodId:t("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:a.join(","),money:o}})):null}},LIANGMA_UNIVERSAL_SUPER:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!一－—。、＝=·吊赶打各]+)[\-~～@#￥%…&!、\\/*,，一－—。.＝=·吊赶打各]+([0-9]+)(元|米|块)?$/,handler:function(e,n){var a=t("f9d6"),u=a.SUPER_SEPARATORS,d=a.METHOD_ID,l=n.split(/\n|\r/).map((function(e){return e.trim()})).filter(Boolean),I=new Map;l.forEach((function(e){var n=e.match(/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!一－—。、＝=·吊赶打各]+)[\-~～@#￥%…&!、\\/*,，一－—。.＝=·吊赶打各]+([0-9]+)(元|米|块)?$/);if(n){var t=n[1].split(u).map((function(e){return e.trim()})).filter((function(e){return 2===e.length})),r=n[2];t.forEach((function(e){var n=null;n=e[0]===e[1]?d.LIANGMA_DUIZI:d.LIANGMA_ZUHE;var t=n+"_"+r;I.has(t)||I.set(t,[]),I.get(t).push(e)}))}}));var s,i=[],_=Object(o["a"])(I.entries());try{for(_.s();!(s=_.n()).done;){var c=Object(r["a"])(s.value,2),h=c[0],f=c[1],m=h.split("_"),U=Object(r["a"])(m,2),A=U[0],M=U[1];i.push({methodId:Number(A),betNumbers:f.join(","),money:M})}}catch(N){_.e(N)}finally{_.f()}return i.length?i:null}},LIANGMA_EACH_PLUS_SUPER:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!一－。、＝=·吊赶打各]+)各([0-9]+)\+([0-9]+)$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=r.METHOD_ID,u=e[1].split(o).map((function(e){return e.trim()})).filter(Boolean),d=e[2],l=e[3];return 1===u.length&&2===u[0].length?[{methodId:a.LIANGMA_ZUHE,betNumbers:u[0],money:d},{methodId:a.LIANGMA_DUIZI,betNumbers:u[0],money:l}]:u.length>=2&&u.every((function(e){return 2===e.length}))?[{methodId:a.LIANGMA_ZUHE,betNumbers:u.join(","),money:d},{methodId:a.LIANGMA_DUIZI,betNumbers:u.join(","),money:l}]:null}},LIANGMA_UNIVERSAL_SUPER_NEW:{pattern:/^([0-9]{2,})\s+(\d+)(元|块|米)?$/,handler:function(e,n){var r=e[1],o=e[2];return r&&2===r.length?[{methodId:t("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:r,money:o}]:null}},LIANGMA_DA_MONEY:{pattern:/^([0-9]{2})打(\d+)(元|块|米)?$/,handler:function(e,n){return[{methodId:t("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:e[1],money:e[2]}]}},LIANGMA_MULTI_WITH_MONEY_SLASH_PLUS:{pattern:/^(\d{2}(?:[\s,，\-]+?\d{2})*)\/\s*(\d+)\+(\d+)$/,handler:function(e,n){var r=e[1].split(/[\s,，\-]+/).filter(Boolean),o=e[2],a=e[3];return[{methodId:t("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:r.join(","),money:o},{methodId:t("f9d6").METHOD_ID.LIANGMA_DUIZI,betNumbers:r.join(","),money:a}]}},LIANGMA_MULTI_LINE_MONEY_PLUS_CN:{pattern:/^((?:\d{2}[，,、]?[一二三四五六七八九十百千万\d]+加[一二三四五六七八九十百千万\d]+\s*\n?)+)$/m,handler:function(e,n){var a,u=t("f9d6"),d=u.METHOD_ID,l=u.chineseToNumber,I=n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),s=new Map,i=[],_=Object(o["a"])(I);try{for(_.s();!(a=_.n()).done;){var c=a.value,h=c.match(/^(\d{2})[，,、]?([一二三四五六七八九十百千万\d]+)加([一二三四五六七八九十百千万\d]+)$/);if(h){var f=h[1],m=l(h[2]),U=l(h[3]),A="".concat(m,"+").concat(U);s.has(A)||s.set(A,[]),s.get(A).push(f)}else i.push(c)}}catch(S){_.e(S)}finally{_.f()}var M,N=[],E=Object(o["a"])(s.entries());try{for(E.s();!(M=E.n()).done;){var b=Object(r["a"])(M.value,2),D=b[0],p=b[1],O=D.split("+"),T=Object(r["a"])(O,2),L=T[0],g=T[1];N.push({methodId:d.LIANGMA_ZUHE,betNumbers:p.join(","),money:L}),N.push({methodId:d.LIANGMA_DUIZI,betNumbers:p.join(","),money:g})}}catch(S){E.e(S)}finally{E.f()}return N.length?N:null}},DUIZI_SLASH_MONEY:{pattern:/^对(\d{2})\/(\d+)$/,handler:function(e,n){return[{methodId:a["METHOD_ID"].LIANGMA_DUIZI,betNumbers:e[1],money:e[2]}]}},ERMA_YI_YI_ONE_MONEY:{pattern:/^(\d{2})一(\d{2})一个(\d+)(元|块|米)?$/,handler:function(e,n){var r=e[1],o=e[2],a=e[3];return[{methodId:t("f9d6").METHOD_ID.ERMA_COMBO,betNumbers:r+","+o,money:a}]}},LIANGMA_MULTI_SINGLE_WITH_MONEY_FEN_SUPER:{pattern:/^([\s\S]+)$/m,handler:function(e,n){if(!/分\s*$/.test(n))return null;n=n.replace(/分\s*$/m,"").replace(/[\r\n]+/g," ").trim();var o,a=/(\d{2})[\-—~～@#￥%…&!、\\/*,，.＝=·吊赶打各\s]+(\d{2,3})(?=[\-—~～@#￥%…&!、\\/*,，.＝=·吊赶打各\s]|$)/g,u=new Map,d=[],l=n;while(null!==(o=a.exec(n)))d.push({num:o[1],money:o[2],raw:o[0]}),l=l.replace(o[0],"@@"),u.has(o[2])||u.set(o[2],[]),u.get(o[2]).push(o[1]);if(0===d.length)return null;if(!/^\d{2}$/.test(d[0].num))return null;if(l.replace(/[@\-—~～@#￥%…&!、\\/*,，.＝=·吊赶打各\s]/g,"").length>0)return null;var I=t("f9d6").METHOD_ID;return Array.from(u.entries()).map((function(e){var n=Object(r["a"])(e,2),t=n[0],o=n[1];return{methodId:I.LIANGMA_MULTI_SINGLE_WITH_MONEY_FEN_SUPER||3,betNumbers:o.join(","),money:t}}))}}};Object.keys(u).forEach((function(e){var n=u[e].pattern;"string"===typeof n&&n.endsWith("$")?u[e].pattern=n.replace(/\$$/,"[，,./*-=。·、s]*$"):n instanceof RegExp&&n.source.endsWith("$")&&(u[e].pattern=new RegExp(n.source.replace(/\$$/,"[，,./*-=。·、s]*$")))}))},"5f85":function(e,n,t){"use strict";t.r(n),t.d(n,"SIX_CODE_PATTERNS",(function(){return d}));var r=t("2909"),o=t("3835"),a=t("b85c"),u=(t("99af"),t("4de4"),t("caad"),t("a15b"),t("d81d"),t("14d9"),t("fb6a"),t("4e82"),t("4ec9"),t("b64b"),t("d3b7"),t("4d63"),t("c607"),t("ac1f"),t("2c3e"),t("00b4"),t("25f0"),t("8a79"),t("2532"),t("3ca3"),t("466d"),t("5319"),t("1276"),t("498a"),t("0643"),t("76d6"),t("2382"),t("4e3e"),t("a573"),t("9a9a"),t("159b"),t("ddb0"),t("f9d6")),d={LIUMA_TRANSFORM:{pattern:/^(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?(\d{6})[\-—~～@#￥%…&!、\\/*,，.＝=各·\s]*?(转|套|拖)(?:各)?(\d+)(?:\+(\d+))?(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?$/,handler:function(e,n){var r=e[2],o=e[4],a=e[5],u=e[6],d=t("f9d6").generateTransformNumbers(r),l=[];return o?l.push({methodId:t("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:d.join(","),money:o}):a&&u&&(l.push({methodId:t("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:d.join(","),money:a}),l.push({methodId:t("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:d.join(","),money:u})),l}},LIUMA_ZUSAN:{pattern:/^(\d{6})[\-—~～@#￥%…&!、\\/*,，。，一－.＝=·\s]*组三?[\-—~～@#￥%…&!、\\/*,，。，一－.＝=·\s]*([\d]+)?(元|块|米)?[\-—~～@#￥%…&!、。，一－\\/*,，.＝=·\s]*$/,handler:function(e,n){return 6!==e[1].length?null:[{methodId:u["METHOD_ID"].LIUMA_ZUSAN,betNumbers:e[1],money:e[2]||""}]}},LIUMA_FANGDUI:{pattern:/^(\d{6})[\-—~～@#￥%…&!、\\/*,，一－.＝=·\s]*防对?(?:-?(\d+))?$/,handler:function(e,n){return 6!==e[1].length?null:[{methodId:u["METHOD_ID"].LIUMA_ZUSAN,betNumbers:e[1],money:e[2]||""}]}},LIUMA_SIMPLE:{pattern:/^(\d{6})\/(\d+)\/?$/,handler:function(e,n){var t=e[1],r=e[2];return[{methodId:u["METHOD_ID"].LIUMA_ZULIU,betNumbers:t,money:r}]}},MULTI_LIUMA:{pattern:/^(\d{6}(?:[^0-9\n]+\d{6})*)(?:各([一二三四五六七八九十百千万\d]+))?(?:防([一二三四五六七八九十百千万\d]+))?$/,handler:function(e,n){var r=t("f9d6"),o=r.chineseToNumber,a=n.split(u["BASE_PATTERNS"].SEPARATORS).filter(Boolean).filter((function(e){return 6===e.length}));if(!a.length||!a.every((function(e){return 6===e.length})))return null;var d=e[2]?o(e[2]):"",l=e[3]?o(e[3]):"",I=[];return d&&I.push({methodId:u["METHOD_ID"].LIUMA_ZULIU,betNumbers:a.join(","),money:d}),l&&I.push({methodId:u["METHOD_ID"].LIUMA_ZUSAN,betNumbers:a[a.length-1],money:l}),I.length?I:null}},LIUMA_ZULIU_ZUSAN_MULTI_MONEY_SUPER:{pattern:/^(\d{6}(?:[－\-—~～@#￥%…&!、\\/*,一－。.＝=·+吊赶打各]+\d{6})*)[\-—~～@#￥%…&!、\\/*,一－.。＝=·+吊赶打各]+(\d+)[\-—~～@#￥%…&!、\\/*,一－.。＝=·+吊赶打各]*[\+\-\\/*、.]+[\-—~～@#￥%…&!、\\/*,一－.。＝=·+吊赶打各]*(\d+)(?:元)?$/,handler:function(e,n){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],o=t("f9d6"),a=o.SUPER_SEPARATORS,u=e[1].split(a).filter(Boolean),d=e[2],l=e[3];return r&&(/^\d{6}$/.test(d)||/^\d{6}$/.test(l))?null:[{methodId:t("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:u.join(","),money:d},{methodId:t("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:u.join(","),money:l}]}},LIUMA_ZULIU_ZU_SUPER:{pattern:/^(\d{6})组(\d+)$/,handler:function(e,n){var r=e[1],o=e[2];return[{methodId:t("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:r,money:o}]}},LIUMA_ZULIU_MULTI_SUPER:{pattern:/^(\d{6}(?:[\-—~～@#￥%…&一－。+!、\\/*,，.＝=·\s\n吊赶打各]+\d{6})*)[\-—~～@#￥%…&一－。!、\\/*,，.＝=·\s+吊赶打各]+各(\d+)(元)?(共计\d+(元)?)?$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=e[1].split(o).filter(Boolean),u=e[2];return[{methodId:t("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:a.join(","),money:u}]}},LIUMA_ZUSAN_ZU_SUPER:{pattern:/^(\d{6})[\-—~～@#￥%…&!、\\/*,，一－.＝=·\s]*组三[\-—~～@#￥%…&!、\\/*,，一－.＝=·\s]*(\d+)$/,handler:function(e,n){var r=e[1],o=e[2];return[{methodId:t("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:r,money:o}]}},LIUMA_ZHIZU_MULTI_SUPER:{pattern:/^(\d{6}(?:[\-—~～@#￥%…&!、\\/一－。*,，.＝=·\s+吊赶打各]+\d{6})*)[\-—~～@#￥%…&!、一－。\\/*,，.＝=·\s+吊赶打各]*(直组|组直|值组|组值)[\s]*各?(\d+)(?:元)?$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=e[1].split(o).filter(Boolean),u=e[3];return[{methodId:t("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:a.join(","),money:u},{methodId:t("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:a.join(","),money:u}]}},LIUMA_ZULIU_FANG:{pattern:/^(\d{6})[+＋\-—~～@#￥%…&!、：:\\/*,，。一—－~.＝=·吊赶打各](\d+)防(\d+)$/,handler:function(e,n){var t=e[1],r=e[2],o=e[3];return[{methodId:u["METHOD_ID"].LIUMA_ZULIU,betNumbers:t,money:r},{methodId:u["METHOD_ID"].LIUMA_ZUSAN,betNumbers:t,money:o}]}},LIUMA_MULTI_ZHIZU_EACH_SUPER:{pattern:/^(直组|组直|值组|组值)各([0-9]+)[\s\-—~～@#￥%…&!一－。、\\/*,，.＝=·吊赶打各\n]+([\d\s\-—~～@#￥%…&!一－。、\\/*,，.＝=·吊赶打各]+)$/i,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=e[2],u=e[3].replace(/\n/g," "),d=u.split(o).map((function(e){return e.trim()})).filter((function(e){return/^\d{6}$/.test(e)}));return[{methodId:t("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:d.join(","),money:a},{methodId:t("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:d.join(","),money:a}]}},LIUMA_MULTI_GAN_FANGDUI_SUPER:{pattern:/^([0-9]{6}(?:[\-—~～@#￥%…&!、\\/*,，.一－。＝=·+吊赶打各 ]+[0-9]{6})*)[\-—~～@#￥%…&!、一－。\\/*,，.＝=·+吊赶打各 ]+赶([0-9]+)[\-—~～@#￥%…&!、一－。\\/*,，.＝=·+吊赶打各 ]*防对([0-9]+)$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=e[1].split(o).filter(Boolean);console.log("格式化后号码:",a.join(","));var u=e[2],d=e[3];return[{methodId:t("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:a.join(","),money:u},{methodId:t("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:a.join(","),money:d}]}},LIUMA_MULTI_EACH_SUPER:{pattern:/^(\d{6}(?:[\-~～@#￥%…&!、\\/*,一－。，.＝=·\+吊赶打各 ]+\d{6})*)[+＋\-—~～@#￥%…&!、：:\\/*,，。一—－~.＝=·吊赶打各 ](\d+)$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=e[1].split(o).filter(Boolean);if(!a.length||a.some((function(e){return 6!==e.length})))return[];var u=e[2];return[{methodId:t("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:a.join(","),money:u}]}},LIUMA_SLASH_TWO_MONEY:{pattern:/^(?!.*[\r\n])(\d{6})[\-—~～@#￥%…&!、\\/*,，.。\u4e00\uff0d\uff1d=· 　\t]+(\d{!6}+)[\-—~～@#￥%…&!、\\/*,，.。\u4e00\uff0d\uff1d=· 　\t]+(\d+)$/,handler:function(e,n){var r=e[1],o=e[2],a=e[3];return[{methodId:t("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:r,money:o},{methodId:t("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:r,money:a}]}},LIUMA_UNIVERSAL_SUPER:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!一－—。、＝=·吊赶打各]+)[\-~～@#￥%…&!、\\/*,，.一－。—＝=·\s+吊赶打各]+([0-9]+)(元|米|块)?$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=r.METHOD_ID,u=e[1].split(o).map((function(e){return e.trim()})).filter(Boolean),d=e[2];return 1===u.length&&6===u[0].length?[{methodId:a.LIUMA_ZULIU,betNumbers:u[0],money:d}]:u.length>=2&&u.every((function(e){return 6===e.length}))?[{methodId:a.LIUMA_ZULIU,betNumbers:u.join(","),money:d}]:null}},LIUMA_EACH_PLUS_SUPER:{pattern:/^([\d、,，.。\\/*\-~～@#￥一－。%…&!、＝=·吊赶打各]+)各([0-9]+)\+([0-9]+)$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=r.METHOD_ID,u=e[1].split(o).map((function(e){return e.trim()})).filter(Boolean),d=e[2],l=e[3];return 1===u.length&&6===u[0].length?[{methodId:a.LIUMA_ZULIU,betNumbers:u[0],money:d},{methodId:a.LIUMA_ZUSAN,betNumbers:u[0],money:l}]:u.length>=2&&u.every((function(e){return 6===e.length}))?[{methodId:a.LIUMA_ZULIU,betNumbers:u.join(","),money:d},{methodId:a.LIUMA_ZUSAN,betNumbers:u.join(","),money:l}]:null}},LIUMA_UNIVERSAL_SUPER_NEW:{pattern:/^([0-9]{6,})\s+(\d+)(元|块|米)?$/,handler:function(e,n){var r=e[1],o=e[2];return r&&6===r.length?[{methodId:t("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:r,money:o}]:null}},LIUMA_DA_MONEY:{pattern:/^([0-9]{6})打(\d+)(元|块|米)?$/,handler:function(e,n){return[{methodId:t("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:e[1],money:e[2]}]}},LIUMA_MULTI_WITH_MONEY_SLASH_PLUS:{pattern:/^(\d{6}(?:[\s,，、\-]+?\d{6})*)(?:\/|各|\s|吊|赶|打|-|\+|、)(\d+)\+(\d+)$/,handler:function(e,n){var r=e[1].split(/[\s,，\-]+/).filter(Boolean),o=e[2],a=e[3];return[{methodId:t("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:r.join(","),money:o},{methodId:t("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:r.join(","),money:a}]}},LIUMA_MULTI_LINE_MONEY_PLUS:{pattern:/^((?:\d{6}[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+\d+\+\d+\s*\n?)+)$/m,handler:function(e,n){var r,u=t("f9d6"),d=u.METHOD_ID,l=(u.SUPER_SEPARATORS,n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean)),I=new Map,s=!0,i=Object(a["a"])(l);try{for(i.s();!(r=i.n()).done;){var _=r.value,c=_.match(/^(\d{6})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+(\d+)\+(\d+)$/);if(!c){s=!1;break}var h=c[1];if(6!==h.length){s=!1;break}var f=_.split(/[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+/)[0];if(6!==f.length||!/^\d{6}$/.test(f)){s=!1;break}var m="".concat(c[2],"+").concat(c[3]);I.has(m)||I.set(m,[]),I.get(m).push(h)}}catch(L){i.e(L)}finally{i.f()}if(!s||0===I.size)return null;var U,A=[],M=Object(a["a"])(I.entries());try{for(M.s();!(U=M.n()).done;){var N=Object(o["a"])(U.value,2),E=N[0],b=N[1],D=E.split("+"),p=Object(o["a"])(D,2),O=p[0],T=p[1];A.push({methodId:d.LIUMA_ZULIU,betNumbers:b.join(","),money:O}),A.push({methodId:d.LIUMA_ZUSAN,betNumbers:b.join(","),money:T})}}catch(L){M.e(L)}finally{M.f()}return A.length?A:null}},LIUMA_MULTI_LINE_MONEY_PLUS_CN:{pattern:/^((?:\d{6}[，,、]?[一二三四五六七八九十百千万\d]+加[一二三四五六七八九十百千万\d]+\s*\n?)+)$/m,handler:function(e,n){var r,u=t("f9d6"),d=u.METHOD_ID,l=u.chineseToNumber,I=n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),s=new Map,i=Object(a["a"])(I);try{for(i.s();!(r=i.n()).done;){var _=r.value,c=_.match(/^(\d{6})[，,、]?([一二三四五六七八九十百千万\d]+)加([一二三四五六七八九十百千万\d]+)$/);if(c){var h=c[1],f=l(c[2]),m=l(c[3]),U="".concat(f,"+").concat(m);s.has(U)||s.set(U,[]),s.get(U).push(h)}}}catch(g){i.e(g)}finally{i.f()}var A,M=[],N=Object(a["a"])(s.entries());try{for(N.s();!(A=N.n()).done;){var E=Object(o["a"])(A.value,2),b=E[0],D=E[1],p=b.split("+"),O=Object(o["a"])(p,2),T=O[0],L=O[1];M.push({methodId:d.LIUMA_ZULIU,betNumbers:D.join(","),money:T}),M.push({methodId:d.LIUMA_ZUSAN,betNumbers:D.join(","),money:L})}}catch(g){N.e(g)}finally{N.f()}return M.length?M:null}},LIUMA_MULTI_EACH_FANGDUI_SLASH:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%一－。…&!、＝=·吊赶打各]+)[/\-~～@#￥%…&!、一－。\\/*,，.＝=·吊赶打各]+各防对([一二三四五六七八九十百千万\d]+)$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=r.METHOD_ID,u=r.chineseToNumber,d=e[1].split(o).map((function(e){return e.trim()})).filter((function(e){return 6===e.length})),l=u(e[2]);return d.length?[{methodId:a.LIUMA_ZUSAN,betNumbers:d.join(","),money:l}]:null}},LIUMA_MULTI_EACH_FANGDUI:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥一－。%…&!、＝=·吊赶打各]+)各防对([一二三四五六七八九十百千万\d]+)$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=r.METHOD_ID,u=r.chineseToNumber,d=e[1].split(o).map((function(e){return e.trim()})).filter((function(e){return 6===e.length})),l=u(e[2]);return d.length?[{methodId:a.LIUMA_ZUSAN,betNumbers:d.join(","),money:l}]:null}},LIUMA_SLASH_MONEY:{pattern:/^\s*(\d{6})\s*\/\s*([一二三四五六七八九十百千万\d]+)\s*$/,handler:function(e,n){var r=t("f9d6"),o=r.METHOD_ID,a=r.chineseToNumber,u=e[1],d=a(e[2]);return[{methodId:o.LIUMA_ZULIU,betNumbers:u,money:d}]}},LIUMA_ZULIU_ZUSAN_MONEY_PAIR:{pattern:/^(\d{6}[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*)组六[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*(\d+)[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*组三[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*(\d+)$/,handler:function(e,n){var t=e[1],r=e[2],o=e[3];return[{methodId:u["METHOD_ID"].LIUMA_ZULIU,betNumbers:t,money:r},{methodId:u["METHOD_ID"].LIUMA_ZUSAN,betNumbers:t,money:o}]}},LIUMA_ZULIU:{pattern:/^(\d{6})组六?(\d+)[，,./\*\-\=。·、\s]*$/,handler:function(e,n){var t=e[1];if(6!==t.length)return null;var r=e[2]||"";return[{methodId:u["METHOD_ID"].LIUMA_ZULIU,betNumbers:t,money:r}]}},LIUMA_ZULIU_ZUSAN_COMBO:{pattern:/^(\d{6}(?:[。.．、,，\s]+\d{6})*)\/(\d+)\+(\d+)$/,handler:function(e,n){var r=t("f9d6"),o=(r.SUPER_SEPARATORS,e[1].split(/[。.．、,，\s]+/).map((function(e){return e.trim()})).filter(Boolean)),a=e[2],u=e[3];return o.length&&o.every((function(e){return 6===e.length}))?[{methodId:t("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:o.join(","),money:a},{methodId:t("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:o.join(","),money:u}]:null}},LIUMA_SINGLE_LINE_ZULIU_ZUSAN_PLUS:{pattern:/^(\d{6})\s+(\d+)[+＋](\d+)$/,handler:function(e,n){return[{methodId:t("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:e[1],money:e[2]},{methodId:t("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:e[1],money:e[3]}]}},LIUMA_MULTI_LINE_ZULIU_PLUS:{pattern:/^((?:\d{6}[+＋]\d+\s*\n?)+)$/m,handler:function(e,n){var r,u=t("f9d6"),d=u.METHOD_ID,l=t("b2f0"),I=(l.getBestPatternResult,n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean)),s=new Map,i=[],_=Object(a["a"])(I);try{for(_.s();!(r=_.n()).done;){var c=r.value,h=c.match(/^(\d{6})[+＋](\d+)$/);if(h){var f=h[1],m=h[2];s.has(m)||s.set(m,[]),s.get(m).push(f)}else i.push(c)}}catch(D){_.e(D)}finally{_.f()}var U,A=[],M=Object(a["a"])(s.entries());try{for(M.s();!(U=M.n()).done;){var N=Object(o["a"])(U.value,2),E=N[0],b=N[1];A.push({methodId:d.LIUMA_ZULIU,betNumbers:b.join(","),money:E})}}catch(D){M.e(D)}finally{M.f()}return A}},LIUMA_ZULIU_FANG_ZUSAN_PLUS:{pattern:/^(\d{6})[\-—~～@#￥%…&!、一－。\\/*,，.＝=·\s+吊赶打各]*(\d+)[\-—~～@#￥%…&!、一－。\\/*,，.＝=·\s+吊赶打各]*(防对|防)(\d+)$/,handler:function(e,n){return[{methodId:t("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:e[1],money:e[2]},{methodId:t("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:e[1],money:e[4]}]}},LIUMA_MULTI_LINE_ZULIU_FANG_ZUSAN_GROUP:{pattern:/^([\s\S]+)$/m,handler:function(e,n){var r,u=n.split(/[\r\n]+/).map((function(e){return e.trim()})).filter(Boolean),d=new Map,l=Object(a["a"])(u);try{for(l.s();!(r=l.n()).done;){var I=r.value,s=I.match(/^(\d{6})-?(\d+)防(\d+)$/);if(s){var i=s[1],_=s[2],c=s[3],h="".concat(_,"|").concat(c);d.has(h)||d.set(h,[]),d.get(h).push(i)}}}catch(O){l.e(O)}finally{l.f()}var f,m=[],U=Object(a["a"])(d.entries());try{for(U.s();!(f=U.n()).done;){var A=Object(o["a"])(f.value,2),M=A[0],N=A[1],E=M.split("|"),b=Object(o["a"])(E,2),D=b[0],p=b[1];m.push({methodId:t("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:N.join(","),money:D}),m.push({methodId:t("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:N.join(","),money:p})}}catch(O){U.e(O)}finally{U.f()}return m.length?m:null}},LIUMA_MULTI_LINE_WHOLE_MONEY:{pattern:/^([0-9]{6})\+(\d+)$/m,handler:function(e,n){var r,o=n.split(/[\r\n]+/).map((function(e){return e.trim()})).filter(Boolean),u=[],d=Object(a["a"])(o);try{for(d.s();!(r=d.n()).done;){var l=r.value,I=l.match(/^([0-9]{6})\+(\d+)$/);I&&u.push({methodId:t("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:I[1],money:I[2]})}}catch(s){d.e(s)}finally{d.f()}return u.length?u:null}},LIUMA_MULTI_TRANSFORM_EACH:{pattern:/^(\d{6}(?:[\-~～@#￥%…&!、\\/*一－。,，.＝=·\s+吊赶打各]+\d{6})*)(转|转各|套|套各)(\d+)\+(\d+)$/,handler:function(e,n){var r,o=t("f9d6"),u=o.SUPER_SEPARATORS,d=o.METHOD_ID,l=o.generateTransformNumbers,I=e[1].split(u).filter(Boolean),s=e[3],i=e[4],_=[],c=Object(a["a"])(I);try{for(c.s();!(r=c.n()).done;){var h=r.value,f=l(h);_.push({methodId:d.QIMA_ZULIU,betNumbers:f.join(","),money:s}),_.push({methodId:d.QIMA_ZUSAN,betNumbers:f.join(","),money:i})}}catch(m){c.e(m)}finally{c.f()}return _}},LIUMA_MULTI_LINE_TRANSFORM:{pattern:/^((?:(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?\d{6}转\d+(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?\s*\n?)+)$/m,handler:function(e,n){var r,u=t("f9d6"),d=u.METHOD_ID,l=n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),I=new Map,s=Object(a["a"])(l);try{for(s.s();!(r=s.n()).done;){var i=r.value,_=i.match(/^(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?(\d{6})转(\d+)(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?$/);if(_){var c=_[2],h=_[3];I.has(h)||I.set(h,[]),I.get(h).push(c)}}}catch(E){s.e(E)}finally{s.f()}var f,m=[],U=Object(a["a"])(I.entries());try{for(U.s();!(f=U.n()).done;){var A=Object(o["a"])(f.value,2),M=A[0],N=A[1];m.push({methodId:d.QIMA_ZULIU,betNumbers:N.join(","),money:M})}}catch(E){U.e(E)}finally{U.f()}return m.length?m:null}},LIUMA_MULTI_COLON_EACH_SUPER:{pattern:/^(\d{6}(?::|：\d{6})*)各(\d+)$/,handler:function(e,n){var r=n.split(/:|：/).map((function(e){return e.trim()})).filter((function(e){return 6===e.length})),o=e[2];return r.length?[{methodId:t("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:r.join(","),money:o}]:null}},LIUMA_MULTI_YIGE_EACH_PLUS:{pattern:/^([\d一]+)一([\d一]+)一?各?(\d+)\+(\d+)$/,handler:function(e,n){var r=n.split("一").map((function(e){return e.replace(/[^\d]/g,"")})).filter((function(e){return 6===e.length})),o=e[3],a=e[4];return r.length?[{methodId:t("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:r.join(","),money:o},{methodId:t("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:r.join(","),money:a}]:null}},LIUMA_DIAN_XMA_ZULIU_FANG_ZUSAN:{pattern:/^(\d{6})[。.．、,，\s]+6码(\d+)防(\d+)$/,handler:function(e,n){return[{methodId:t("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:e[1],money:e[2]},{methodId:t("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:e[1],money:e[3]}]}},LIUMA_MULTI_LINE_ZULIU_MONEY_GROUP:{pattern:/^((?:\d{6}\s+\d+\s*\n?)+)$/m,handler:function(e,n){var r,u=t("f9d6"),d=u.METHOD_ID,l=n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),I=Object(a["a"])(l);try{for(I.s();!(r=I.n()).done;){var s=r.value,i=s.match(/^(\d+)\s+(\d+)$/);if(i&&i[1].length===i[2].length)return null}}catch(O){I.e(O)}finally{I.f()}var _,c=new Map,h=Object(a["a"])(l);try{for(h.s();!(_=h.n()).done;){var f=_.value,m=f.match(/^(\d{6})\s+(\d+)$/);if(m){var U=m[1],A=m[2];c.has(A)||c.set(A,[]),c.get(A).push(U)}}}catch(O){h.e(O)}finally{h.f()}var M,N=[],E=Object(a["a"])(c.entries());try{for(E.s();!(M=E.n()).done;){var b=Object(o["a"])(M.value,2),D=b[0],p=b[1];N.push({methodId:d.LIUMA_ZULIU,betNumbers:p.join(","),money:D})}}catch(O){E.e(O)}finally{E.f()}return N.length?N:null}},LIUMA_MULTI_NUMBERS_EACH_FANG:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～一－。@#￥%…&!、＝=·吊赶打各]+)各(\d+)(防|防对|放对)(\d+)$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=r.METHOD_ID,u=e[1].split(o).map((function(e){return e.trim()})).filter((function(e){return 6===e.length}));if(!u.length)return null;var d=e[2],l=e[4];return[{methodId:a.LIUMA_ZULIU,betNumbers:u.join(","),money:d},{methodId:a.LIUMA_ZUSAN,betNumbers:u.join(","),money:l}]}},LIUMA_YI_YI_ONE_MONEY:{pattern:/^(\d{6})一(\d{6})一个(\d+)(元|块|米)?$/,handler:function(e,n){var r=e[1],o=e[2],a=e[3];return 6!==r.length||6!==o.length?null:[{methodId:t("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:r+","+o,money:a}]}},LIUMA_GE_MONEY:{pattern:/^(\d{6})[，,、\s]*个(\d+)(元|块|米)?$/,handler:function(e,n){var r=e[1],o=e[2];return 6!==r.length?null:[{methodId:t("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:r,money:o}]}},LIUMA_MULTI_LINE_ZULIU_MONEY_SUPER:{pattern:/^([\s\S]+)$/m,handler:function(e,n){var r,u=t("f9d6"),d=u.METHOD_ID,l=n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),I=new Map,s=/^(\d{6})[\-—~～@#￥%…&!、一－。\\/*,，.＝=· 　\t]+(\d+)(元|米|块)?([，,./\\*\-\=。·、 　\t]*)?$/,i=[],_=Object(a["a"])(l);try{for(_.s();!(r=_.n()).done;){var c=r.value,h=c.match(s);if(!h)return console.log('【LIUMA_MULTI_LINE_ZULIU_MONEY_SUPER】行 "'.concat(c,'" 不匹配六码格式，返回null')),null;if(h[1].length===h[2].length)return console.log('【LIUMA_MULTI_LINE_ZULIU_MONEY_SUPER】行 "'.concat(c,'" 号码和金额长度相同，可能误匹配，返回null')),null;i.push({line:c,match:h})}}catch(H){_.e(H)}finally{_.f()}console.log("【LIUMA_MULTI_LINE_ZULIU_MONEY_SUPER】所有 ".concat(i.length," 行都是六码格式，继续处理"));for(var f=0,m=i;f<m.length;f++){var U=m[f].match;if(6===U[1].length){var A=U[1],M=U[2],N=U[3]||"",E=M+N;I.has(E)||I.set(E,[]),I.get(E).push(A)}}var b,D=[],p=Object(a["a"])(I.entries());try{for(p.s();!(b=p.n()).done;){var O=Object(o["a"])(b.value,2),T=O[0],L=O[1];if(L.length>0){var g,S=T.length>0?[T.replace(/(元|米|块)$/,""),(null===(g=T.match(/(元|米|块)$/))||void 0===g?void 0:g[0])||""]:["",""],v=Object(o["a"])(S,2),y=v[0],Z=v[1];D.push({methodId:d.LIUMA_ZULIU,betNumbers:L.join(","),money:y,unit:Z||void 0})}}}catch(H){p.e(H)}finally{p.f()}return console.log("【LIUMA_MULTI_LINE_ZULIU_MONEY_SUPER】处理完成，返回 ".concat(D.length," 个结果组")),D.length>0?D:null}},LIUMA_MULTI_FANGDUI_SUPER:{pattern:/^([0-9]{6}(?:[\-—–―－﹣﹘⸺⸻~～@#￥%…&!、\\/*,，.一－。＝=·\s+吊赶打各]+[0-9]{6})+)[\-—–―－﹣﹘⸺⸻~～@#￥%…&!、\\/*,，.一－。＝=·\s+吊赶打各]*(防对|防|组三)([0-9]+)$/,handler:function(e,n){var r=t("f9d6"),o=r.SUPER_SEPARATORS,a=r.METHOD_ID,u=e[1].split(o).map((function(e){return e.trim()})).filter((function(e){return 6===e.length})),d=e[3];return console.log(e[3]),u.length<2?null:[{methodId:a.LIUMA_ZUSAN,betNumbers:u.join(","),money:d}]}},LIUMA_MULTI_LINE_ZULIU_ZUSAN_KEYWORD:{pattern:/^([0-9]{6}(组六|组三)\d+(元|米|块)?[，,./\*\-\=。一－。·、\s]*\n?)+(总\d+(元|米|块)?[，,./\*\-\=。一－。·、\s]*)?$/m,handler:function(e,n){var t,r=n.split(/\r?\n/).map((function(e){return e.trim()})).filter(Boolean).filter((function(e){return!/^总\d+(元|米|块)?$/.test(e)})),d=new Map,l=Object(a["a"])(r);try{for(l.s();!(t=l.n()).done;){var I=t.value,s=I.match(/^([0-9]{6})(组六|组三)(\d+)(元|米|块)?[，,./\*\-\=。一－。·、\s]*$/);if(s&&6===s[1].length){var i=s[1],_=s[2],c=s[3],h=_+"_"+c;d.has(h)||d.set(h,[]),d.get(h).push(i)}}}catch(O){l.e(O)}finally{l.f()}var f,m=[],U=Object(a["a"])(d.entries());try{for(U.s();!(f=U.n()).done;){var A=Object(o["a"])(f.value,2),M=A[0],N=A[1],E=M.split("_"),b=Object(o["a"])(E,2),D=b[0],p=b[1];m.push({methodId:"组六"===D?u["METHOD_ID"].LIUMA_ZULIU:u["METHOD_ID"].LIUMA_ZUSAN,betNumbers:N.join(","),money:p})}}catch(O){U.e(O)}finally{U.f()}return m.length?m:null}},LIUMA_MULTI_LINE_NUMBERS_PLUS_MONEY:{pattern:/^([\s\S]+)$/m,handler:function(e,n){var r=n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean);if(r.length<2)return null;var o=r.slice(0,-1),a="",u="",d=r[r.length-1],l=d.match(/^(\d{6})?\s*各(\d+)[+＋](\d+)$/);if(l)l[1]&&o.push(l[1]),a=l[2],u=l[3];else{var I=d.match(/^(\d+)[+＋](\d+)$/);if(!I)return null;a=I[1],u=I[2]}return o.length&&o.every((function(e){return/^\d{6}$/.test(e)}))?[{methodId:t("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:o.join(","),money:a},{methodId:t("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:o.join(","),money:u}]:null}},LIUMA_MULTI_LINE_TWO_NUMBERS_CAIZHONG_EACH_SUPER_V2:{pattern:/^((?:\d{6}\s+\d{6}\s*\n?)+)((?:.*?(福彩|福|3D|3d|体彩|体|排|排三)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*各\d+\s*\n?)+)$/im,handler:function(e,n){var r,o=e[1].split(/\n/).map((function(e){return e.trim()})).filter(Boolean),u=[],d=Object(a["a"])(o);try{for(d.s();!(r=d.n()).done;){var l=r.value,I=l.match(/(\d{6})\s+(\d{6})/);I&&u.push(I[1],I[2])}}catch(h){d.e(h)}finally{d.f()}if(!u.length)return null;var s,i=[],_=/(福彩|福|3D|3d|体彩|体|排|排三)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*各(\d+)/g;while(null!==(s=_.exec(e[2]))){var c=1;/体彩|体|排|排三/.test(s[1])&&(c=2),i.push({lotteryId:c,money:s[2]})}return i.length?i.map((function(e){return{methodId:t("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:u.join(","),money:e.money,lotteryId:e.lotteryId}})):null}},LIUMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER:{pattern:/^([\s\S]+)$/m,handler:function(e,n){var r=/(福彩|福|3D|3d)/.test(n),u=/(体彩|体|排|排三)/.test(n);if(r||u)return null;var d,l=n.split(/\r?\n/).map((function(e){return e.trim()})).filter(Boolean),I=new Map,s=new Map,i=/^([0-9]{6})[\-—~～@#￥%…&!、\\/*,一－–.。＝=·\s\/]+(\d+)\++(\d+)[元米块]*$/,_=/^([0-9]{6})[\-—~～@#￥%…&!、\\/*,一－–.。＝=·\s\/]+(\d+)[元米块]*$/,c=[],h=Object(a["a"])(l);try{for(h.s();!(d=h.n()).done;){var f=d.value,m=f.match(i);if(m)c.push({line:f,type:"zuliuZusan",match:m});else{if(m=f.match(_),!m)return console.log('【LIUMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】行 "'.concat(f,'" 不匹配六码格式，返回null')),null;c.push({line:f,type:"zuliu",match:m})}}}catch(G){h.e(G)}finally{h.f()}console.log("【LIUMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】所有 ".concat(c.length," 行都是六码格式，继续处理"));for(var U=0,A=c;U<A.length;U++){var M=A[U],N=M.match,E=M.type;if("zuliuZusan"===E){var b=N[1],D=N[2],p=N[3];I.has(D)||I.set(D,[]),I.get(D).push(b),s.has(p)||s.set(p,[]),s.get(p).push(b)}else if("zuliu"===E){var O=N[1],T=N[2];I.has(T)||I.set(T,[]),I.get(T).push(O)}}var L,g=[],S=Object(a["a"])(I.entries());try{for(S.s();!(L=S.n()).done;){var v=Object(o["a"])(L.value,2),y=v[0],Z=v[1];g.push({methodId:t("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:Z.join(","),money:y})}}catch(G){S.e(G)}finally{S.f()}var H,R=Object(a["a"])(s.entries());try{for(R.s();!(H=R.n()).done;){var j=Object(o["a"])(H.value,2),$=j[0],P=j[1];g.push({methodId:t("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:P.join(","),money:$})}}catch(G){R.e(G)}finally{R.f()}return console.log("【LIUMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】处理完成，返回 ".concat(g.length," 个结果组")),g.length?g:null}},LIUMA_MONEY_UNIT_FANGDUI_SUPER:{pattern:/^(\d{6})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:元|米|块)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(防|防对|组三)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*$/,handler:function(e,n){var t=e[1],r=e[2];e[3];return 6!==t.length?null:[{methodId:u["METHOD_ID"].LIUMA_ZUSAN,betNumbers:t,money:r}]}},WUMA_TO_LIUMA_MULTI_TRANSFORM:{pattern:/^((?:\d{5}[\-—~～@#￥%…&!、\\/*,，.＝=·\s]+){1,}\d{5})(各)?(转|套)[\s]*([0-9]+)\+([0-9]+)$/,handler:function(e,n){var o=t("f9d6"),a=o.SUPER_SEPARATORS,u=e[1],d=e[4],l=e[5],I=u.split(a).filter((function(e){return 5===e.length})),s=[];return I.forEach((function(e){var n=e.split(""),t="0123456789".split("").filter((function(e){return!n.includes(e)})),o=t.map((function(n){return e+n})).sort(),a=o.filter((function(e){return!s.includes(e)}));s.push.apply(s,Object(r["a"])(a))})),[{methodId:t("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:s.join(","),money:d},{methodId:t("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:s.join(","),money:l}]}}};Object.keys(d).forEach((function(e){var n=d[e].pattern;"string"===typeof n&&n.endsWith("$")?d[e].pattern=n.replace(/\$$/,"[，,./*-=。·、s]*$"):n instanceof RegExp&&n.source.endsWith("$")&&(d[e].pattern=new RegExp(n.source.replace(/\$$/,"[，,./*-=。·、s]*$")))}))},"7b57":function(e,n,t){"use strict";t.r(n),t.d(n,"ALL_PATTERNS",(function(){return M})),t.d(n,"mergeGroups",(function(){return N})),t.d(n,"parseBetLine",(function(){return E})),t.d(n,"parseBetContent",(function(){return b}));var r=t("2909"),o=t("b85c"),a=t("5530"),u=(t("99af"),t("4de4"),t("0481"),t("a15b"),t("d81d"),t("14d9"),t("4069"),t("d3b7"),t("07ac"),t("ac1f"),t("5319"),t("1276"),t("498a"),t("0643"),t("2382"),t("4e3e"),t("a573"),t("159b"),t("f9d6"));t.d(n,"BASE_PATTERNS",(function(){return u["BASE_PATTERNS"]}));var d=t("ce3b");t.d(n,"SINGLE_CODE_PATTERNS",(function(){return d["SINGLE_CODE_PATTERNS"]}));var l=t("5c5d");t.d(n,"TWO_CODE_PATTERNS",(function(){return l["TWO_CODE_PATTERNS"]}));var I=t("9608");t.d(n,"THREE_CODE_PATTERNS",(function(){return I["THREE_CODE_PATTERNS"]}));var s=t("172b");t.d(n,"FOUR_CODE_PATTERNS",(function(){return s["FOUR_CODE_PATTERNS"]}));var i=t("47fb");t.d(n,"FIVE_CODE_PATTERNS",(function(){return i["FIVE_CODE_PATTERNS"]}));var _=t("5f85");t.d(n,"SIX_CODE_PATTERNS",(function(){return _["SIX_CODE_PATTERNS"]}));var c=t("2e8d");t.d(n,"SEVEN_CODE_PATTERNS",(function(){return c["SEVEN_CODE_PATTERNS"]}));var h=t("22f7");t.d(n,"EIGHT_CODE_PATTERNS",(function(){return h["EIGHT_CODE_PATTERNS"]}));var f=t("3506");t.d(n,"NINE_CODE_PATTERNS",(function(){return f["NINE_CODE_PATTERNS"]}));var m=t("f398"),U=t("8de0"),A=t("98b6");t.d(n,"cleanNumberString",(function(){return u["cleanNumberString"]})),t.d(n,"parseMoney",(function(){return u["parseMoney"]})),t.d(n,"checkLotteryType",(function(){return u["checkLotteryType"]})),t.d(n,"parseLotteryType",(function(){return u["parseLotteryType"]}));var M=Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])({},A["KUADU_PATTERNS"]),U["DANTUO_PATTERNS"]),d["SINGLE_CODE_PATTERNS"]),l["TWO_CODE_PATTERNS"]),I["THREE_CODE_PATTERNS"]),s["FOUR_CODE_PATTERNS"]),i["FIVE_CODE_PATTERNS"]),_["SIX_CODE_PATTERNS"]),c["SEVEN_CODE_PATTERNS"]),h["EIGHT_CODE_PATTERNS"]),f["NINE_CODE_PATTERNS"]),m["SINGLE_DOUBLE_PATTERNS"]);function N(e){var n={};return e.forEach((function(e){var t="".concat(e.methodId||"","_").concat(e.money||"");n[t]?n[t].betNumbers.push(e.betNumbers):n[t]=Object(a["a"])(Object(a["a"])({},e),{},{betNumbers:[e.betNumbers],methodId:e.methodId,danshuang:e.danshuang,daxiao:e.daxiao,dingwei:e.dingwei,hezhi:e.hezhi,money:e.money||""})})),Object.values(n).map((function(e){var n=e.betNumbers.filter(Boolean).map((function(e){return e.split(/[^0-9]+/)})).flat();return Object(a["a"])(Object(a["a"])({},e),{},{betNumbers:n.join(","),methodId:e.methodId,money:e.money||""})}))}function E(e){if(e=e.trim(),!e)return null;var n=Object(u["parseLotteryType"])(e);e=e.replace(/(福|福彩|3D|福3D|排|排三|体|体彩|福排|福体)/g,"").trim();for(var t=0,r=Object.values(M);t<r.length;t++){var o=r[t],a=o.pattern.exec(e);if(a){var d=o.handler(a,e);if(d)return d.forEach((function(e){e.lotteryId||(e.lotteryId=n?n.id:1)})),d}}return null}function b(e){var n,t=e.split(/[\n\r]+/).filter(Boolean),a=[],u=Object(o["a"])(t);try{for(u.s();!(n=u.n()).done;){var d=n.value,l=E(d);l&&a.push.apply(a,Object(r["a"])(l))}}catch(I){u.e(I)}finally{u.f()}return a}},"8de0":function(e,n,t){"use strict";t.r(n),t.d(n,"DANTUO_PATTERNS",(function(){return o}));t("99af"),t("4de4"),t("a15b"),t("14d9"),t("d3b7"),t("ac1f"),t("00b4"),t("5319"),t("1276"),t("0643"),t("2382");var r=t("f9d6"),o={DANTUO_DEFAULT_ZULIU:{pattern:/^(?:胆|胆码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:拖|拖码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9,，\s]+?)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?$/,handler:function(e,n){var t=e[1],o=e[2],a=e[3],u=o.replace(/[\s,，]+/g,"").split("").filter((function(e){return e&&e!==t}));if(u.length<2||u.length>9)return console.warn("胆拖默认组六拖码数量不合法: ".concat(u.length,"，应为2-9个")),null;var d=Object(r["getDantuoZuliuMethodId"])(u.length);return[{methodId:d,betNumbers:"胆".concat(t,"拖").concat(u.join(",")),money:a}]}},DANTUO_EXPLICIT_ZUSAN:{pattern:/^(?:胆|胆码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:拖|拖码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9,，\s]+?)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?组三$/,handler:function(e,n){var t=e[1],o=e[2],a=e[3],u=o.replace(/[\s,，]+/g,"").split("").filter((function(e){return e&&e!==t}));if(u.length<2||u.length>9)return console.warn("胆拖组三拖码数量不合法: ".concat(u.length,"，应为2-9个")),null;var d=Object(r["getDantuoZusanMethodId"])(u.length);return[{methodId:d,betNumbers:"胆".concat(t,"拖").concat(u.join(",")),money:a}]}},DANTUO_EXPLICIT_ZULIU:{pattern:/^(?:胆|胆码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:拖|拖码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9,，\s]+?)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?组六$/,handler:function(e,n){var t=e[1],o=e[2],a=e[3],u=o.replace(/[\s,，]+/g,"").split("").filter((function(e){return e&&e!==t}));if(u.length<2||u.length>9)return console.warn("胆拖组六拖码数量不合法: ".concat(u.length,"，应为2-9个")),null;var d=Object(r["getDantuoZuliuMethodId"])(u.length);return[{methodId:d,betNumbers:"胆".concat(t,"拖").concat(u.join(",")),money:a}]}},SIMPLE_DANTUO_DEFAULT_ZULIU:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:拖|拖码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9,，\s]+?)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?$/,handler:function(e,n){var t=e[1],o=e[2],a=e[3],u=o.replace(/[\s,，]+/g,"").split("").filter((function(e){return e&&e!==t}));if(u.length<2||u.length>9)return console.warn("简化胆拖默认组六拖码数量不合法: ".concat(u.length,"，应为2-9个")),null;var d=Object(r["getDantuoZuliuMethodId"])(u.length);return[{methodId:d,betNumbers:"胆".concat(t,"拖").concat(u.join(",")),money:a}]}},SIMPLE_DANTUO_ZUSAN:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:拖|拖码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9,，\s]+?)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?组三$/,handler:function(e,n){var t=e[1],o=e[2],a=e[3],u=o.replace(/[\s,，]+/g,"").split("").filter((function(e){return e&&e!==t}));if(u.length<2||u.length>9)return console.warn("简化胆拖组三拖码数量不合法: ".concat(u.length,"，应为2-9个")),null;var d=Object(r["getDantuoZusanMethodId"])(u.length);return[{methodId:d,betNumbers:"胆".concat(t,"拖").concat(u.join(",")),money:a}]}},SIMPLE_DANTUO_ZULIU:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:拖|拖码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9,，\s]+?)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?组六$/,handler:function(e,n){var t=e[1],o=e[2],a=e[3],u=o.replace(/[\s,，]+/g,"").split("").filter((function(e){return e&&e!==t}));if(u.length<2||u.length>9)return console.warn("简化胆拖组六拖码数量不合法: ".concat(u.length,"，应为2-9个")),null;var d=Object(r["getDantuoZuliuMethodId"])(u.length);return[{methodId:d,betNumbers:"胆".concat(t,"拖").concat(u.join(",")),money:a}]}},DANTUO_ZUSAN:{pattern:/^胆(\d)拖([\d,，\s]+)组三[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*(\d+)(?:元|块|米)?$/,handler:function(e,n){var t=e[1],o=e[2],a=e[3],u=o.split(/[,，\s]+/).filter((function(e){return e&&e!==t}));if(u.length<2||u.length>9)return console.warn("胆拖组三拖码数量不合法: ".concat(u.length,"，应为2-9个")),null;var d=Object(r["getDantuoZusanMethodId"])(u.length);return[{methodId:d,betNumbers:"胆".concat(t,"拖").concat(u.join(",")),money:a}]}},DANTUO_ZULIU:{pattern:/^胆(\d)拖([\d,，\s]+)组六[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*(\d+)(?:元|块|米)?$/,handler:function(e,n){var t=e[1],o=e[2],a=e[3],u=o.split(/[,，\s]+/).filter((function(e){return e&&e!==t}));if(u.length<2||u.length>9)return console.warn("胆拖组六拖码数量不合法: ".concat(u.length,"，应为2-9个")),null;var d=Object(r["getDantuoZuliuMethodId"])(u.length);return[{methodId:d,betNumbers:"胆".concat(t,"拖").concat(u.join(",")),money:a}]}},DANTUO_BOTH:{pattern:/^胆(\d)拖([\d,，\s]+)(?:组三组六|组六组三)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*各?(\d+)(?:元|块|米)?$/,handler:function(e,n){var t=e[1],o=e[2],a=e[3],u=o.split(/[,，\s]+/).filter((function(e){return e&&e!==t}));if(u.length<2||u.length>9)return console.warn("胆拖组三组六拖码数量不合法: ".concat(u.length,"，应为2-9个")),null;var d=Object(r["getDantuoZusanMethodId"])(u.length),l=Object(r["getDantuoZuliuMethodId"])(u.length);return[{methodId:d,betNumbers:"胆".concat(t,"拖").concat(u.join(",")),money:a},{methodId:l,betNumbers:"胆".concat(t,"拖").concat(u.join(",")),money:a}]}},DANTUO_DEFAULT_BOTH:{pattern:/^胆(\d)拖([\d,，\s]+)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*各?(\d+)(?:元|块|米)?$/,handler:function(e,n){if(/组三|组六/.test(n))return null;var t=e[1],o=e[2],a=e[3],u=o.split(/[,，\s]+/).filter((function(e){return e&&e!==t}));if(u.length<2||u.length>9)return console.warn("胆拖默认拖码数量不合法: ".concat(u.length,"，应为2-9个")),null;var d=Object(r["getDantuoZusanMethodId"])(u.length),l=Object(r["getDantuoZuliuMethodId"])(u.length);return[{methodId:d,betNumbers:"胆".concat(t,"拖").concat(u.join(",")),money:a},{methodId:l,betNumbers:"胆".concat(t,"拖").concat(u.join(",")),money:a}]}},DANTUO_WITH_LOTTERY_PREFIX:{pattern:/^(?:福彩?|体彩?|排三?|3D)?胆(\d)拖([\d,，\s]+)(?:组三|组六)?[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*(\d+)(?:元|块|米)?$/,handler:function(e,n){var t=e[1],o=e[2],a=e[3],u=o.split(/[,，\s]+/).filter((function(e){return e&&e!==t}));if(u.length<2||u.length>9)return console.warn("胆拖带前缀拖码数量不合法: ".concat(u.length,"，应为2-9个")),null;var d=1;/体彩|排三/.test(n)&&(d=2);var l=[];if(/组三/.test(n)){var I=Object(r["getDantuoZusanMethodId"])(u.length);l.push({methodId:I,betNumbers:"胆".concat(t,"拖").concat(u.join(",")),money:a,lotteryId:d})}else if(/组六/.test(n)){var s=Object(r["getDantuoZuliuMethodId"])(u.length);l.push({methodId:s,betNumbers:"胆".concat(t,"拖").concat(u.join(",")),money:a,lotteryId:d})}else{var i=Object(r["getDantuoZusanMethodId"])(u.length),_=Object(r["getDantuoZuliuMethodId"])(u.length);l.push({methodId:i,betNumbers:"胆".concat(t,"拖").concat(u.join(",")),money:a,lotteryId:d}),l.push({methodId:_,betNumbers:"胆".concat(t,"拖").concat(u.join(",")),money:a,lotteryId:d})}return l}},DANTUO_SEPARATED_NUMBERS_TYPE_BEFORE_MONEY:{pattern:/^(?:胆|胆码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:拖|拖码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*((?:\d[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*){2,9})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(组三|组六)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?$/,handler:function(e){var n,t=e[1],o=e[2],a=e[3],u=e[4],d=o.replace(/[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]/g,"").split("").filter((function(e){return e&&e!==t}));return d.length<2||d.length>9?(console.warn("胆拖分隔数字拖码数量不合法: ".concat(d.length,"，应为2-9个")),null):(n="组三"===a?Object(r["getDantuoZusanMethodId"])(d.length):Object(r["getDantuoZuliuMethodId"])(d.length),[{methodId:n,betNumbers:"胆".concat(t,"拖").concat(d.join(",")),money:u}])}},DANTUO_SEPARATED_NUMBERS_TYPE_AFTER_MONEY:{pattern:/^(?:胆|胆码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:拖|拖码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*((?:\d[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*){2,9})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+(组三|组六)$/,handler:function(e){var n,t=e[1],o=e[2],a=e[3],u=e[4],d=o.replace(/[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]/g,"").split("").filter((function(e){return e&&e!==t}));return d.length<2||d.length>9?(console.warn("胆拖分隔数字拖码数量不合法: ".concat(d.length,"，应为2-9个")),null):(n="组三"===u?Object(r["getDantuoZusanMethodId"])(d.length):Object(r["getDantuoZuliuMethodId"])(d.length),[{methodId:n,betNumbers:"胆".concat(t,"拖").concat(d.join(",")),money:a}])}},DANTUO_ZULIU_ZUSAN_COMBO:{pattern:/^(?:胆|胆码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:拖|拖码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9,，\s]+?)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*\+[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?$/,handler:function(e){var n=e[1],t=e[2],o=e[3],a=e[4],u=t.replace(/[\s,，]+/g,"").split("").filter((function(e){return e&&e!==n}));if(u.length<2||u.length>9)return console.warn("胆拖组合格式拖码数量不合法: ".concat(u.length,"，应为2-9个")),null;var d=Object(r["getDantuoZusanMethodId"])(u.length),l=Object(r["getDantuoZuliuMethodId"])(u.length);return[{methodId:l,betNumbers:"胆".concat(n,"拖").concat(u.join(",")),money:o},{methodId:d,betNumbers:"胆".concat(n,"拖").concat(u.join(",")),money:a}]}},SIMPLE_DANTUO_ZULIU_ZUSAN_COMBO:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:拖|拖码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9,，\s]+?)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*\+[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?$/,handler:function(e){var n=e[1],t=e[2],o=e[3],a=e[4],u=t.replace(/[\s,，]+/g,"").split("").filter((function(e){return e&&e!==n}));if(u.length<2||u.length>9)return console.warn("简化胆拖组合格式拖码数量不合法: ".concat(u.length,"，应为2-9个")),null;var d=Object(r["getDantuoZusanMethodId"])(u.length),l=Object(r["getDantuoZuliuMethodId"])(u.length);return[{methodId:l,betNumbers:"胆".concat(n,"拖").concat(u.join(",")),money:o},{methodId:d,betNumbers:"胆".concat(n,"拖").concat(u.join(",")),money:a}]}}}},"98b6":function(e,n,t){"use strict";t.r(n),t.d(n,"KUADU_PATTERNS",(function(){return u})),t.d(n,"isKuaduMethod",(function(){return d})),t.d(n,"getKuaduMethodId",(function(){return l})),t.d(n,"getKuaduValue",(function(){return I}));var r=t("b85c"),o=(t("d81d"),t("14d9"),t("e9c4"),t("b64b"),t("d3b7"),t("4d63"),t("c607"),t("ac1f"),t("2c3e"),t("25f0"),t("8a79"),t("5319"),t("0643"),t("4e3e"),t("a573"),t("159b"),t("f9d6")),a={0:o["METHOD_ID"].KUADU_0,1:o["METHOD_ID"].KUADU_1,2:o["METHOD_ID"].KUADU_2,3:o["METHOD_ID"].KUADU_3,4:o["METHOD_ID"].KUADU_4,5:o["METHOD_ID"].KUADU_5,6:o["METHOD_ID"].KUADU_6,7:o["METHOD_ID"].KUADU_7,8:o["METHOD_ID"].KUADU_8,9:o["METHOD_ID"].KUADU_9},u={KUADU_VALUE_FIRST:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9])[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:跨|跨度)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*$/,handler:function(e){var n=parseInt(e[1]),t=e[2];return n<0||n>9?(console.warn("跨度值超出范围: ".concat(n,"，应为0-9")),null):[{methodId:a[n],betNumbers:JSON.stringify({numbers:[{kuadu:n.toString()}]}),money:t}]},priority:95},KUADU_VALUE_MIDDLE:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:跨|跨度)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9])[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*$/,handler:function(e){var n=parseInt(e[1]),t=e[2];return n<0||n>9?(console.warn("跨度值超出范围: ".concat(n,"，应为0-9")),null):[{methodId:a[n],betNumbers:JSON.stringify({numbers:[{kuadu:n.toString()}]}),money:t}]},priority:94},KUADU_VALUE_FIRST_KEYWORD_LAST:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9])[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:跨|跨度)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*$/,handler:function(e){var n=parseInt(e[1]),t=e[2];return n<0||n>9?(console.warn("跨度值超出范围: ".concat(n,"，应为0-9")),null):[{methodId:a[n],betNumbers:JSON.stringify({numbers:[{kuadu:n.toString()}]}),money:t}]},priority:93},MULTI_KUADU_VALUE_FIRST:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9]{2,})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:跨|跨度)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*$/,handler:function(e){var n,t=e[1],o=e[2],u=t.split("").map((function(e){return parseInt(e)})),d=[],l=Object(r["a"])(u);try{for(l.s();!(n=l.n()).done;){var I=n.value;I<0||I>9?console.warn("跨度值超出范围: ".concat(I,"，应为0-9")):d.push({methodId:a[I],betNumbers:"跨度".concat(I),money:o})}}catch(s){l.e(s)}finally{l.f()}return d.length>0?d:null},priority:96},MULTI_KUADU_VALUE_MIDDLE:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:跨|跨度)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9]{2,})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*$/,handler:function(e){var n,t=e[1],o=e[2],u=t.split("").map((function(e){return parseInt(e)})),d=[],l=Object(r["a"])(u);try{for(l.s();!(n=l.n()).done;){var I=n.value;I<0||I>9?console.warn("跨度值超出范围: ".concat(I,"，应为0-9")):d.push({methodId:a[I],betNumbers:JSON.stringify({numbers:[{kuadu:I.toString()}]}),money:o})}}catch(s){l.e(s)}finally{l.f()}return d.length>0?d:null},priority:95},MULTI_KUADU_VALUE_FIRST_KEYWORD_LAST:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9]{2,})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:跨|跨度)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*$/,handler:function(e){var n,t=e[1],o=e[2],u=t.split("").map((function(e){return parseInt(e)})),d=[],l=Object(r["a"])(u);try{for(l.s();!(n=l.n()).done;){var I=n.value;I<0||I>9?console.warn("跨度值超出范围: ".concat(I,"，应为0-9")):d.push({methodId:a[I],betNumbers:JSON.stringify({numbers:[{kuadu:I.toString()}]}),money:o})}}catch(s){l.e(s)}finally{l.f()}return d.length>0?d:null},priority:94}};function d(e){return e>=60&&e<=69}function l(e){return a[e]}function I(e){return e-o["METHOD_ID"].KUADU_0}Object.keys(u).forEach((function(e){var n=u[e].pattern;"string"===typeof n&&n.endsWith("$")?u[e].pattern=n.replace(/\$$/,"[，,./*-=。·、s]*$"):n instanceof RegExp&&n.source.endsWith("$")&&(u[e].pattern=new RegExp(n.source.replace(/\$$/,"[，,./*-=。·、s]*$")))}))},a2bf6:function(e,n,t){"use strict";var r=t("e8b5"),o=t("07fa"),a=t("3511"),u=t("0366"),d=function(e,n,t,l,I,s,i,_){var c,h,f=I,m=0,U=!!i&&u(i,_);while(m<l)m in t&&(c=U?U(t[m],m,n):t[m],s>0&&r(c)?(h=o(c),f=d(e,n,c,h,f,s-1)-1):(a(f+1),e[f]=c),f++),m++;return f};e.exports=d},b2f0:function(e,n,t){"use strict";t.r(n),t.d(n,"handleNumberRecognition",(function(){return f})),t.d(n,"handleSmartRecognition",(function(){return m}));var r=t("5530"),o=t("b85c"),a=t("53ca"),u=t("2909"),d=(t("99af"),t("4de4"),t("caad"),t("a15b"),t("d81d"),t("14d9"),t("e9c4"),t("4ec9"),t("b64b"),t("d3b7"),t("ac1f"),t("00b4"),t("6062"),t("1e70"),t("79a4"),t("c1a1"),t("8b00"),t("a4e7"),t("1e5a"),t("72c3"),t("2532"),t("3ca3"),t("466d"),t("5319"),t("1276"),t("498a"),t("0643"),t("76d6"),t("2382"),t("4e3e"),t("a573"),t("9a9a"),t("159b"),t("ddb0"),t("7b57"));function l(e,n){if(!e||0===e.length)return null;var r=t("f9d6").METHOD_ID,o=0,a=n&&n.match(/(\d{2,9})/);a&&(o=a[1].length);var d=e.filter((function(e){return e.every((function(e){return e.betNumbers&&e.betNumbers.split(",").every((function(e){return e.length===o}))&&e.methodId!==r.DUDAN}))}));0===d.length&&(d=e.filter((function(e){return e.every((function(e){return e.betNumbers&&e.betNumbers.split(",").every((function(e){return e.length===o}))}))}))),0===d.length&&(d=e);var l=Math.max.apply(Math,Object(u["a"])(d.map((function(e){return e.length})))),I=d.filter((function(e){return e.length===l}));if(1===I.length)return I[0];var s=Math.max.apply(Math,Object(u["a"])(I.map((function(e){return Math.max.apply(Math,Object(u["a"])(e.map((function(e){return(e.betNumbers||"").length}))))}))));if(I=I.filter((function(e){return Math.max.apply(Math,Object(u["a"])(e.map((function(e){return(e.betNumbers||"").length}))))===s})),1===I.length)return I[0];var i=Math.max.apply(Math,Object(u["a"])(I.map((function(e){return new Set(e.map((function(e){return e.methodId}))).size}))));if(I=I.filter((function(e){return new Set(e.map((function(e){return e.methodId}))).size===i})),1===I.length)return I[0];var _=[8,10,12,14,16,18],c=[9,11,13,15,17,19];function h(e){var n=e.map((function(e){return e.methodId}));return n.some((function(e){return _.includes(e)}))?2:n.some((function(e){return c.includes(e)}))?1:0}var f=Math.max.apply(Math,Object(u["a"])(I.map(h)));return I=I.filter((function(e){return h(e)===f})),I[0]}function I(e){return e.replace(/[零一二三四五六七八九十百千万亿]+注/g,"")}function s(e){var n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];console.log('\n【getBestPatternResult】开始处理文本: "'.concat(e,'", strict模式: ').concat(n));var t=e;e=I(e),t!==e&&console.log("【getBestPatternResult】去除中文注数后:",e),/[*Xx]\d/.test(e)?(console.log("【getBestPatternResult】检测到两码定位格式，保护*号"),e=e.replace(/^[\s\-/+\.,、|~!@#$%^&；'\"]+|[\s\-/+\.,、|~!@#$%^&；'\"]+$/g,"")):e=e.replace(/^[\s\-*/+\.,、|~!@#$%^&；'\"]+|[\s\-*/+\.,、|~!@#$%^&；'\"]+$/g,""),e=e.replace(/共\s*\d+(注|元|米|块)?(\s*\+.*)?$/g,(function(e,n,t){return t||""})),console.log("【getBestPatternResult】最终处理后的文本:",e);var r=[],o=[];for(var u in console.log("【getBestPatternResult】开始遍历所有正则模式，总数:",Object.keys(d["ALL_PATTERNS"]).length),d["ALL_PATTERNS"]){var s=d["ALL_PATTERNS"][u],i=s.pattern,_=s.handler,c=null;Date.now();try{c=e.match(i)}catch(p){console.error("【getBestPatternResult】正则 ".concat(u," 匹配报错:"),p);continue}Date.now();if(i&&"function"===typeof _&&c&&c[0]===e){console.log('【getBestPatternResult】✅ 正则 "'.concat(u,'" 匹配成功!')),console.log("【getBestPatternResult】匹配详情:",c),o.push(u);var h=null;Date.now();try{h=_.length>=3?_(c,e,n):_(c,e)}catch(p){console.log("【getBestPatternResult】正则 ".concat(u," handler执行出错，尝试兼容模式:"),p),h=_(c,e)}Date.now();console.log('【getBestPatternResult】正则 "'.concat(u,'" handler结果:'),h),h&&Array.isArray(h)&&h.length>0?(console.log('【getBestPatternResult】正则 "'.concat(u,'" 返回数组结果，长度:'),h.length),r.push(h)):h&&"object"===Object(a["a"])(h)?(console.log('【getBestPatternResult】正则 "'.concat(u,'" 返回对象结果')),r.push([h])):console.log('【getBestPatternResult】正则 "'.concat(u,'" 返回无效结果:'),h)}}var f={1:[1,2],2:[3,4],3:[5,6,7,100],4:[8,9],5:[10,11],6:[12,13],7:[14,15],8:[16,17],9:[18,19]},m=0,U=e&&e.match(/(\d{1,9})/);U&&(m=U[1].length),console.log("【getBestPatternResult】检测到的输入号码长度:",m);var A=f[m]||[];console.log("【getBestPatternResult】该长度对应的有效玩法ID:",A);for(var M=[],N=20;N<=31;N++)M.push(N);var E=r.filter((function(e){return e.every((function(e){return e.betNumbers&&A.includes(e.methodId)||M.includes(e.methodId)}))}));console.log("【getBestPatternResult】长度过滤后的结果数组长度:",E.length),E.length!==r.length&&console.log("【getBestPatternResult】过滤掉了",r.length-E.length,"个不符合长度要求的结果");var b=E.length>0?E:r;console.log("【getBestPatternResult】进入优先级筛选的结果数组长度:",b.length);var D=l(b,e);return console.log("【getBestPatternResult】最终选择的最佳结果:",JSON.stringify(D,null,2)),D}function i(e){var n=e.replace(/\s+/g,""),t=/(福彩|福|福家|3d|3D|福3D)/.test(n),r=/(排三|排3|体彩|体|排列3|排列三|体J|排)/.test(n);return/(福排|排福|福体|福彩体|体彩福)/.test(n)||t&&r?[1,2]:t?1:r?2:1}function _(e){console.log("【removeLotteryPrefix】原始输入:",e);var n=e.trim();if(/[*Xx]\d/.test(n))return n;var t=/^(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)/,r=/(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)$/,o=0;while(t.test(n))if(n=n.replace(t,""),n=n.trim(),o++,o>10)break;o=0;while(r.test(n))if(n=n.replace(r,""),n=n.trim(),o++,o>10)break;return n}function c(e,n){for(var t=[],r=[],o=0;o<e.length;o++){var a=e[o].trim(),u=!1;for(var d in n){var l=n[d],I=l.pattern,s=l.handler;if(I&&"function"===typeof s){var i=a.match(I);if(i&&i[0]===a){u=!0;break}}}u?(r.length>0&&(t.push(r.join(" ")),r=[]),t.push(a)):r.push(a)}return r.length>0&&t.push(r.join(" ")),t}function h(e){if(!e)return{groups:[{methodId:null,danshuang:null,daxiao:null,dingwei:null,hezhi:null,betNumbers:"",money:"",lotteryId:1}]};var n=e;n=I(n),n=/[*Xx]\d/.test(n)?n.replace(/^[\s\-/+\.,、|~!@#$%^&；'\"]+|[\s\-/+\.,、|~!@#$%^&；'\"]+$/g,""):n.replace(/^[\s\-*/+\.,、|~!@#$%^&；'\"]+|[\s\-*/+\.,、|~!@#$%^&；'\"]+$/g,""),n=n.replace(/共\s*\d+(注|元|米|块)?(\s*\+.*)?$/g,(function(e,n,t){return t||""}));var t=n.split(/\r?\n/).filter((function(e){return e.length>0}));t=c(t,d["ALL_PATTERNS"]);var r,a=new Map,l=[],h=Object(o["a"])(t);try{for(h.s();!(r=h.n()).done;){var f=r.value,m=f.match(/^(\d+)[+＋](\d+)$/);if(m){var A=m[1],M=m[2],N="".concat(A.length,"_").concat(M);a.has(N)||a.set(N,[]),a.get(N).push(f)}else l.push(f)}}catch(B){h.e(B)}finally{h.f()}var E,b=[],D=Object(o["a"])(a.values());try{for(D.s();!(E=D.n()).done;){var p=E.value;b.push(p.join("\n"))}}catch(B){D.e(B)}finally{D.f()}b=b.concat(l);var O=/^\d{3}[\-—~～@#￥%…&!+、一－。\\/*,，.＝=·\s]+\d+[+＋]\d+$/,T=/^\d{3}\/[0-9]+([+＋][0-9]+)?$/;(t.length>1&&t.every((function(e){return O.test(e)}))||t.length>1&&t.every((function(e){return T.test(e)})))&&(b=[t.join("\n")]);var L=i(e),g=_(n),S=s(g,!0);if(console.log("【handleNumberRecognition】整体识别结果:",S),S&&S.length>0){console.log("【handleNumberRecognition】✅ 整体识别成功！");var v=U(S,L);return{groups:v}}if(console.log("【handleNumberRecognition】❌ 整体识别失败，准备进入分段识别"),b.length>1){console.log("=== 【进入分段识别】 ==="),console.log("【分段识别】整体识别失败，开始分段处理");for(var y=[],Z=0;Z<b.length;Z++){var H=b[Z];console.log("\n--- 【处理第".concat(Z+1,"段】 ---"));var R=_(H);if(R){console.log("【分段识别】去前缀后内容:",R);var j=s(R,!1);if(console.log("【分段识别】该段识别结果:",j),j&&j.length>0)console.log("【分段识别】该段识别成功，添加到结果中"),console.log("【分段识别】该段识别详情:",JSON.stringify(j,null,2)),y.push.apply(y,Object(u["a"])(j));else{console.log("【分段识别】该段识别失败，使用兜底逻辑");var $={methodId:null,betNumbers:H,money:"",lotteryId:L};console.log("【分段识别】兜底结果:",JSON.stringify($,null,2)),y.push($)}console.log("【分段识别】第".concat(Z+1,"段处理完成，当前总结果数:"),y.length)}else console.log("【分段识别】该段去前缀后为空，跳过")}console.log("\n=== 【分段识别完成】 ==="),console.log("【分段识别】最终合并结果总数:",y.length),console.log("【分段识别】最终合并结果详情:",JSON.stringify(y,null,2));var P=U(y,L);return console.log("【分段识别】补充彩种ID后的最终结果:",JSON.stringify(P,null,2)),{groups:P}}console.log("\n=== 【进入兜底逻辑】 ==="),console.log("【handleNumberRecognition】整体识别和分段识别都失败，使用兜底逻辑"),console.log("【handleNumberRecognition】splitGroups长度:",b.length);var G={groups:[{methodId:null,betNumbers:n,money:"",lotteryId:L}]};return console.log("【handleNumberRecognition】兜底逻辑返回结果:",JSON.stringify(G,null,2)),G}function f(e){return h(e)}function m(e){if(!e)return{groups:[]};var n=e.split(/\r?\n/).map((function(e){return e.trim()})).filter(Boolean);if(n.length>0&&n.every((function(e){return/^\d+$/.test(e)}))){var t=s(n.join("\n"));return t&&t.length>0?{groups:t}:{groups:n.map((function(e){return{methodId:null,betNumbers:e,money:"",lotteryId:1}}))}}var r,a=[],u=Object(o["a"])(n);try{for(u.s();!(r=u.n()).done;){var d=r.value;if(d){var l=s(d);l&&l.length>0?a=a.concat(l):a.push({methodId:null,betNumbers:d,money:"",lotteryId:1})}}}catch(I){u.e(I)}finally{u.f()}return{groups:a}}function U(e,n){if(!e)return e;if(Array.isArray(e)&&e.length>0&&e.every((function(e){return void 0!==e.lotteryId&&null!==e.lotteryId})))return e;if(Array.isArray(n)){var t,a=[],u=Object(o["a"])(n);try{var d=function(){var n=t.value;e.forEach((function(e){a.push(Object(r["a"])(Object(r["a"])({},e),{},{lotteryId:n}))}))};for(u.s();!(t=u.n()).done;)d()}catch(l){u.e(l)}finally{u.f()}return a}return e.map((function(e){return Object(r["a"])(Object(r["a"])({},e),{},{lotteryId:n})}))}},ce3b:function(e,n,t){"use strict";t.r(n),t.d(n,"SINGLE_CODE_PATTERNS",(function(){return d}));var r=t("3835"),o=t("b85c"),a=(t("4de4"),t("a15b"),t("d81d"),t("14d9"),t("fb6a"),t("4ec9"),t("b64b"),t("d3b7"),t("4d63"),t("c607"),t("ac1f"),t("2c3e"),t("00b4"),t("25f0"),t("8a79"),t("3ca3"),t("466d"),t("5319"),t("1276"),t("498a"),t("0643"),t("2382"),t("4e3e"),t("a573"),t("9a9a"),t("159b"),t("ddb0"),t("f9d6")),u={"百":100,"十":101,"个":102},d={DINGWEI_POSITION_FIRST:{pattern:/^([百十个])位(\d)\s*(\d+)$/,handler:function(e,n){var t=e[1],r=e[2],o=e[3];if(1!==r.length)return null;var d=[{methodId:a["METHOD_ID"].YIMA_DINGWEI,betNumbers:r,dingwei:u[t],money:o}];return console.log("一码 handler 返回:",d||results||returnValue),d||results||returnValue}},DINGWEI_NUMBER_FIRST:{pattern:/^(\d)([百十个])-(\d+)$/,handler:function(e,n){var t=e[1],r=e[2],o=e[3];if(1!==t.length)return null;var d=[{methodId:a["METHOD_ID"].YIMA_DINGWEI,betNumbers:t,dingwei:u[r],money:o}];return console.log("一码 handler 返回:",d||results||returnValue),d||results||returnValue}},DINGWEI_POSITION_LAST:{pattern:/^(\d)-(\d+)-([百十个])$/,handler:function(e,n){var t=e[1],r=e[2],o=e[3];if(1!==t.length)return null;var d=[{methodId:a["METHOD_ID"].YIMA_DINGWEI,betNumbers:t,dingwei:u[o],money:r}];return console.log("一码 handler 返回:",d||results||returnValue),d||results||returnValue}},DINGWEI_MULTI:{pattern:/^(?:[（(]?(?:毒|独胆|独|毒胆)[）)]?[、,，.。\\/*\s\n\r\-~一－。～@#￥%…&!、＝=·]*)?(\d(?:[^0-9]+\d)*?)(?:各(?:打)?)?(\d+)(?:块|元)?$/,handler:function(e,n){if(/跨|跨度/.test(n))return null;var r=n.match(/\d+/g)||[];if(r.length<2)return null;var o=r[r.length-1],a=r.slice(0,-1);return!a.length||a.some((function(e){return 1!==e.length}))?null:[{methodId:t("f9d6").METHOD_ID.DUDAN,betNumbers:a.join(","),money:o}]}},DUDAN_MULTI:{pattern:/^(\d(?:[^0-9]+\d)*?)(?:各(?:打)?)?(\d+)(?:块)?$/,handler:function(e,n){if(/跨|跨度/.test(n))return null;var r=n.match(/\d+/g)||[];if(r.length<2)return null;var o=r[r.length-1],a=r.slice(0,-1);return!a.length||a.some((function(e){return 1!==e.length}))?null:[{methodId:t("f9d6").METHOD_ID.DUDAN,betNumbers:a.join(","),money:o}]}},DUDAN_COMMON_EXPRESSIONS:{pattern:/^([\d,\s\/]+?)[买打=\/-]+0*(\d+)$/,handler:function(e,n){var r=e[1],o=e[2].replace(/^0+/,""),a=r.split(/[\s,\/]+/).filter(Boolean);return!a.length||a.some((function(e){return 1!==e.length}))?null:[{methodId:t("f9d6").METHOD_ID.DUDAN,betNumbers:a.join(","),money:o}]}},DUDAN_SPECIAL:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s各打吊赶]*(\d)(?:[\-—~～@#￥%…&!、\\/*,，.＝=·\s各打吊赶]+(\d))*[\-—~～@#￥%…&!、\\/*,，.＝=·\s各打吊赶]+(\d+)(?:元|米|块)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s各打吊赶]*$/,handler:function(e,n,t){var r=n.match(/\d+/g)||[];if(r.length<2)return null;var o=r[r.length-1],u=r.slice(0,-1);return!u.length||u.some((function(e){return 1!==e.length}))?null:[{methodId:a["METHOD_ID"].DUDAN,betNumbers:u.join(","),money:o}]}},DUDAN_SINGLE:{pattern:/^(\d)-(\d+)$/,handler:function(e,n){var t=e[1],r=e[2];return t&&1===t.length?[{methodId:a["METHOD_ID"].DUDAN,betNumbers:t,money:r}]:null}},DUDAN_DIAO:{pattern:/^(?:吊)?(\d)(?:吊|各)?(\d+)$/,handler:function(e,n){var t=e[1],r=e[2];if(!t||1!==t.length)return null;if(/\d{2,}/.test(t)||/\d{2,}/.test(n.split("-")[0]))return null;if(n&&n.split("-")[0]&&1!==n.split("-")[0].replace(/[^0-9]/g,"").length)return null;var o=[{methodId:a["METHOD_ID"].DUDAN,betNumbers:t,money:r}];return console.log("一码 handler 返回:",o||results||returnValue),o||results||returnValue}},DUDAN_KEYWORD_EXPRESSIONS:{pattern:/^([\d,，.。\s\/]+)[独胆码挑]+[，.。\s\/]*([0-9]+)$/,handler:function(e,n){var r=(e[1].match(/\d+/g)||[]).filter((function(e){return 1===e.length})),o=e[2].replace(/^0+/,"");return!r.length||r.some((function(e){return 1!==e.length}))?null:[{methodId:t("f9d6").METHOD_ID.DUDAN,betNumbers:r.join(","),money:o}]}},DUDAN_UNIVERSAL_SUPER:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*(?:毒|独胆|独|毒胆)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*(\d)(?:[\-—~～@#￥%…&!、\\/*,，.＝=·\s]+(\d))*[\-—~～@#￥%…&!、\\/*,，.＝=·\s]+(\d+)(?:元|米|块)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*$/,handler:function(e,n){var r=t("f9d6"),o=r.METHOD_ID,a=n.match(/\d+/g)||[];if(a.length<2)return null;var u=a[a.length-1],d=a.slice(0,-1);return!d.length||d.some((function(e){return 1!==e.length}))?null:[{methodId:o.DUDAN,betNumbers:d.join(","),money:u}]}},DUDAN_MULTI_LINE_COMPLEX:{pattern:/^((?:[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:独胆|毒|独|毒胆)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*\d[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*\d+(?:元|米|块)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*\n[\s\S]*)+)$/m,handler:function(e,n){var a,u=t("f9d6"),d=u.METHOD_ID,l=n.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),I=[],s=/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:独胆|毒|独|毒胆)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|米|块)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*$/,i=Object(o["a"])(l);try{for(i.s();!(a=i.n()).done;){var _=a.value,c=_.match(s);if(!c)return console.log('【DUDAN_MULTI_LINE_COMPLEX】行 "'.concat(_,'" 不匹配独胆格式，返回null')),null;var h=c[1],f=c[2];if(1!==h.length)return console.log('【DUDAN_MULTI_LINE_COMPLEX】行 "'.concat(_,'" 号码不是1位数字，返回null')),null;I.push({line:_,number:h,money:f})}}catch(g){i.e(g)}finally{i.f()}console.log("【DUDAN_MULTI_LINE_COMPLEX】所有 ".concat(I.length," 行都是独胆格式，继续处理"));for(var m=new Map,U=0,A=I;U<A.length;U++){var M=A[U],N=M.number,E=M.money;m.has(E)||m.set(E,[]),m.get(E).push(N)}var b,D=[],p=Object(o["a"])(m.entries());try{for(p.s();!(b=p.n()).done;){var O=Object(r["a"])(b.value,2),T=O[0],L=O[1];L.length>0&&D.push({methodId:d.DUDAN,betNumbers:L.join(","),money:T})}}catch(g){p.e(g)}finally{p.f()}return console.log("【DUDAN_MULTI_LINE_COMPLEX】处理完成，返回 ".concat(D.length," 个结果组")),D.length>0?D:null}},DUDAN_UNIVERSAL_SUPER_NUMBER:{pattern:/^([0-9]{1,})\s+(\d+)(元|块|米)?$/,handler:function(e,n){var r=e[1],o=e[2];return r&&1===r.length?[{methodId:t("f9d6").METHOD_ID.DUDAN,betNumbers:r,money:o}]:null}},YIMA_DINGWEI_POSITION:{pattern:/^(百位|十位|个位)[：: ]?(\d)\+(\d+)[,，\s]*$/m,handler:function(e,n){var r,a=n.split(/\n|\r/).map((function(e){return e.trim()})).filter(Boolean),u=[],d=Object(o["a"])(a);try{for(d.s();!(r=d.n()).done;){var l=r.value,I=l.match(/^(百位|十位|个位)[：: ]?(\d)\+(\d+)[,，\s]*$/);if(I){var s=I[1],i=I[2],_=I[3],c=void 0;if("百位"===s)c=100;else if("十位"===s)c=101;else{if("个位"!==s)continue;c=102}u.push({methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,betNumbers:i,dingwei:c,money:_})}}}catch(h){d.e(h)}finally{d.f()}return u.length?u:null}},DINGWEI_MULTI_POSITION_NUMBER_MONEY:{pattern:/^([个十百]{2,3})[，,、\s]+(\d)--(\d+)$/,handler:function(e,n){var t=e[1].split(""),r=e[2],o=e[3],d=t.map((function(e){return{methodId:a["METHOD_ID"].YIMA_DINGWEI,betNumbers:r,dingwei:u[e],money:o}}));return d}},DUDAN_KEYWORD:{pattern:/^(?:独胆|胆|独)(\d)[-/+吊打赶 ]+(\d+)$/,handler:function(e,n){var t=e[1],r=e[2];return 1!==t.length?null:[{methodId:a["METHOD_ID"].DUDAN,betNumbers:t,money:r}]}},DINGWEI_POSITION_NUMBER_DIAO:{pattern:/^(百位|十位|个位)(\d)吊(\d+)$/,handler:function(e,n){var t,r=e[1],o=e[2],u=e[3];if("百位"===r)t=100;else if("十位"===r)t=101;else{if("个位"!==r)return null;t=102}return[{methodId:a["METHOD_ID"].YIMA_DINGWEI,betNumbers:o,dingwei:t,money:u}]}},DUDAN_DANMA_DIAO:{pattern:/^胆码(\d)吊(\d+)$/,handler:function(e,n){var t=e[1],r=e[2];return 1!==t.length?null:[{methodId:a["METHOD_ID"].DUDAN,betNumbers:t,money:r}]}},DUDAN_DU_DIAO:{pattern:/^独(\d)吊(\d+)$/,handler:function(e,n){var t=e[1],r=e[2];return 1!==t.length?null:[{methodId:a["METHOD_ID"].DUDAN,betNumbers:t,money:r}]}},DUDAN_MULTI_KEYWORD_SUPER:{pattern:/((独胆|胆|独)\d[打吊赶各]+[，,、.。\\/*\s\n\r\-~一－。～@#￥%…&!、＝=·]*\d+[，,、.。\\/*\s\n\r\-~一－。～@#￥%…&!、＝=·]*)+/,handler:function(e,n){var t,r=/(独胆|胆|独)(\d)[打吊赶各]+[，,、.。\\/*\s\n\r\-~～@#￥%…&!、＝=·]*(\d+)/g,o=[];while(null!==(t=r.exec(n)))o.push({methodId:a["METHOD_ID"].DUDAN,betNumbers:t[2],money:t[3]});return o.length?o:null}},DINGWEI_MULTI_POSITION_NUMBER_KEYWORD_SUPER:{pattern:/((百位|十位|个位)\d[打吊赶各]+[，,、.。\\/*\s\n\r\-~一－。～@#￥%…&!、＝=·]*\d+[，,、.。\\/*\s\n\r\-~一－。～@#￥%…&!、＝=·]*)+/,handler:function(e,n){var t,r=/(百位|十位|个位)(\d)[打吊赶各]+[，,、.。\\/*\s\n\r\-~一－。～@#￥%…&!、＝=·]*(\d+)/g,o=[];while(null!==(t=r.exec(n))){var u=void 0;if("百位"===t[1])u=100;else if("十位"===t[1])u=101;else{if("个位"!==t[1])continue;u=102}o.push({methodId:a["METHOD_ID"].YIMA_DINGWEI,betNumbers:t[2],dingwei:u,money:t[3]})}return o.length?o:null}},BAOZI_ALL_SUPER:{pattern:/^豹子[各]*[，,、.。\/*\s\n\r\-~一－。～@#￥%…&!、＝=·]*(\d+)[，,、.。\/*\s\n\r\-~一－。～@#￥%…&!、＝=·]*$/,handler:function(e,n){var t=["000","111","222","333","444","555","666","777","888","999"],r=e[1];return/各/.test(n)||(r=(parseInt(r,10)/10).toString()),[{methodId:a["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:r}]}},BAOZI_DIAO_DIVIDE_TEN:{pattern:/^豹子[打吊赶杀]+[，,、.。\\/*\s\n\r\-~一－。～@#￥%…&!、＝=·]*(\d+)[，,、.。\\/*\s\n\r\-~一－。～@#￥%…&!、＝=·]*$/,handler:function(e,n){var t=["000","111","222","333","444","555","666","777","888","999"],r=parseInt(e[1]),o=Math.floor(r/10);return[{methodId:a["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:o.toString()}]}}};Object.keys(d).forEach((function(e){var n=d[e].pattern;"string"===typeof n&&n.endsWith("$")?d[e].pattern=n.replace(/\$$/,"[，,./*-=。·、s]*$"):n instanceof RegExp&&n.source.endsWith("$")&&(d[e].pattern=new RegExp(n.source.replace(/\$$/,"[，,./*-=。·、s]*$")))}))},f398:function(e,n,t){"use strict";t.r(n),t.d(n,"SINGLE_DOUBLE_PATTERNS",(function(){return d}));var r=t("2909"),o=t("b85c"),a=t("3835"),u=(t("99af"),t("4de4"),t("7db0"),t("a630"),t("caad"),t("a15b"),t("d81d"),t("14d9"),t("13d5"),t("4ec9"),t("b680"),t("b64b"),t("d3b7"),t("4d63"),t("c607"),t("ac1f"),t("2c3e"),t("00b4"),t("25f0"),t("8a79"),t("2532"),t("3ca3"),t("466d"),t("5319"),t("1276"),t("498a"),t("0643"),t("2382"),t("fffc"),t("4e3e"),t("a573"),t("9d4a"),t("159b"),t("ddb0"),t("f9d6")),d={BAODA_ZUSAN:{pattern:/^包打?组三(?:-(\d+))?$/,handler:function(e,n){var t=e[1]||"",r=(Object(u["parseLotteryType"])(n),[{methodId:u["METHOD_ID"].BAODA_ZUSAN,betNumbers:"",money:t}]);return console.log("包打组三 handler 返回:",r),r}},HEZHI_MULTI:{pattern:/^和值(?:(\d+(?:\/\d+)*?)各(\d+)|((?:\d+-\d+\/)*\d+-\d+))$/,handler:function(e,n){Object(u["parseLotteryType"])(n);if(e[1]){var t=e[1].split("/"),r=e[2],o=t.map((function(e){return{methodId:u["METHOD_ID"].HEZHI,hezhi:parseInt(e),money:r}}));return console.log("和值多个 handler 返回:",o),o}var d=e[3].split("/").map((function(e){var n=e.split("-"),t=Object(a["a"])(n,2),r=t[0],o=t[1];return{number:r,money:o}})),l=d.map((function(e){return{methodId:u["METHOD_ID"].HEZHI,hezhi:parseInt(e.number),money:e.money}}));return console.log("和值多个 handler 返回:",l),l}},HEZHI_SINGLE_DA_SUPER:{pattern:/^(和值?|和)(\d{1,2})[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s+吊赶打各]+(\d+)$/,handler:function(e,n){var r=t("f9d6"),o=r.getHezhiMethodId,a=r.parseLotteryType,u=r.METHOD_ID,d=e[2],l=e[3],I=(a(n),{methodId:o(d,u),hezhi:parseInt(d),money:l});return console.log("和值单个打（超级分隔符）handler 返回:",I),[I]}},HEZHI_MULTI_EACH_DA_SUPER:{pattern:/^(和值?|和)((?:\d{1,2}[\-—~～@#￥%…&一－。 !、\\/*,，.＝=·\s+吊赶打各\/\\]*)+)[各\-—~～@#￥%…&!、\\/*,一－。，.＝=·\s+吊赶打]+(\d+)$/,handler:function(e,n){var r=t("f9d6"),o=r.getHezhiMethodId,a=r.SUPER_SEPARATORS,u=r.parseLotteryType,d=r.METHOD_ID,l=e[2].split(a).map((function(e){return e.trim()})).filter(Boolean),I=e[3],s=(u(n),l.map((function(e){return{methodId:o(e,d),hezhi:parseInt(e),money:I}})));return console.log("和值各打（超级分隔符）handler 返回:",s),s}},PAO_DA_MONEY:{pattern:/^炮打(\d+)(元|米|块)?$/,handler:function(e,n){var t=["000","111","222","333","444","555","666","777","888","999"];return[{methodId:u["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:"1"}]}},PAO_GE_DA_MONEY:{pattern:/^炮各打(\d+)(元|米|块)?$/,handler:function(e,n){var t=["000","111","222","333","444","555","666","777","888","999"],r=e[1];return[{methodId:u["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:r}]}},PAO_GE_MONEY:{pattern:/^炮各(\d+)(元|米|块)?$/,handler:function(e,n){var t=["000","111","222","333","444","555","666","777","888","999"],r=e[1];return[{methodId:u["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:r}]}},PAO_MONEY:{pattern:/^炮(\d+)(元|米|块)?$/,handler:function(e,n){var t=["000","111","222","333","444","555","666","777","888","999"];return[{methodId:u["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:"1"}]}},PAO_GE_MONEY_GONG:{pattern:/^炮各(\d+)(元|米|块)?共\d+$/,handler:function(e,n){var t=["000","111","222","333","444","555","666","777","888","999"],r=e[1];return[{methodId:u["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:r}]}},TONGPAO_ALLPAO_FANGPAO_MONEY:{pattern:/^(通炮|全炮|防炮)(\d+)(元|米|块)?$/,handler:function(e,n){var t=["000","111","222","333","444","555","666","777","888","999"],r=(parseFloat(e[2])/10).toFixed(1).replace(/\.0$/,"");return[{methodId:u["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:r}]}},TONGPAO_ALLPAO_FANGPAO_GE_MONEY:{pattern:/^(通炮|全炮|防炮)各(\d+)(元|米|块)?$/,handler:function(e,n){var t=["000","111","222","333","444","555","666","777","888","999"],r=e[2];return[{methodId:u["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:r}]}},PAO_09_SLASH_GE_MONEY:{pattern:/^炮0-9[\/][\s]*各?(\d+)(元|米|块)?$/,handler:function(e,n){var t=["000","111","222","333","444","555","666","777","888","999"],r=e[1];return[{methodId:u["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:r}]}},PAO_09_KONG_GE_MONEY:{pattern:/^0[\s]*[-到~—]?[\s]*9[\s]*炮[\s]*各?(\d+)(元|米|块)?$/,handler:function(e,n){var t=["000","111","222","333","444","555","666","777","888","999"],r=e[1];return[{methodId:u["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:r}]}},KONGPAO_GE_MONEY:{pattern:/^空炮[各个]?(\d+)(元|米|块)?$/,handler:function(e,n){var t=["000","111","222","333","444","555","666","777","888","999"],r=e[1];return[{methodId:u["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:r}]}},BAOZI_MULTI_EACH_MONEY:{pattern:/^((?:\d{3}[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*)+)豹子各?(\d+)(元|米|块)?$/,handler:function(e,n){var t=e[1].split(/[\-—~～@#￥%…&!、\\/*,，.＝=·\s]+/).map((function(e){return e.trim()})).filter((function(e){return 3===e.length&&e[0]===e[1]&&e[1]===e[2]})),r=e[2];return[{methodId:u["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:r}]}},BAOZI_MULTI_MONEY:{pattern:/^((?:\d{3}[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*)+)豹子(\d+)(元|米|块)?$/,handler:function(e,n){var t=e[1].split(/[\-—~～@#￥%…&!、\\/*,，.＝=·\s]+/).map((function(e){return e.trim()})).filter((function(e){return 3===e.length&&e[0]===e[1]&&e[1]===e[2]}));return[{methodId:u["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:"1"}]}},PAO_SUPERSEP_MONEY:{pattern:/^炮[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s.。·、]+(\d+)(元|米|块)?$/,handler:function(e,n){var t=["000","111","222","333","444","555","666","777","888","999"];return[{methodId:u["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:"1"}]}},QUANPAO_MONEY:{pattern:/^全炮(各)?(\d+)(元|米|块)?$/,handler:function(e,n){var t=["000","111","222","333","444","555","666","777","888","999"],r=e[2];return[{methodId:u["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:r}]}},BAOZI_ALL_MONEY:{pattern:/^0到9豹子全包(\d+)(?:元|米|块)?$/,handler:function(e,n){var t=["000","111","222","333","444","555","666","777","888","999"],r=e[1];return/各/.test(n)||(r=(parseFloat(r)/10).toFixed(1).replace(/\.0$/,"")),[{methodId:u["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:r}]}},PAO_OTHER:{pattern:/^炮.*?各(\d+)(?:元|米|块)?$/,handler:function(e,n){var t=["000","111","222","333","444","555","666","777","888","999"];return[{methodId:u["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:t.join(","),money:e[1]}]}},BAODA_ZUSAN_VARIANTS:{pattern:/^(包?对子|对子|对吊|对一|对)[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([0-9]+)(元|块|米)?$/,handler:function(e,n){var t=e[2],r={"一":1,"二":2,"三":3,"四":4,"五":5,"六":6,"七":7,"八":8,"九":9,"十":10,"百":100,"千":1e3,"万":1e4},o=t;return/^[一二三四五六七八九十百千万]+$/.test(t)&&(o=t.split("").reduce((function(e,n){return e+(r[n]||0)}),0)||"1"),/^\d+$/.test(t)&&(o=t),o||(o="1"),[{methodId:u["METHOD_ID"].BAODA_ZUSAN,betNumbers:"",money:o}]}},HEZHI_SIMPLE:{pattern:/^(?:福|福彩|3D|体|体彩|排三|排)?和([\d/\-、,，.。\s]+)[/\-、,，.。\s]+(\d+)(元|块|米)?$/,handler:function(e,n){var r=t("f9d6"),o=r.getHezhiMethodId,a=r.METHOD_ID,u=r.parseLotteryType,d=e[1],l=e[2],I=(u(n),d.split(/[\/\-、,，.。\s]+/).map((function(e){return e.trim()})).filter(Boolean));return I.map((function(e){return{methodId:o(e,a),hezhi:parseInt(e),money:l}}))}},BAO_ZULIU_ALL:{pattern:/^(包打?组六|组六)[\-—~～@#￥%…&!、\\/*,一－。＝=·吊赶打各\s]*([0-9]+)$/,handler:function(e,n){var t=e[2]||"";Object(u["parseLotteryType"])(n);return[{methodId:u["METHOD_ID"].BAODA_ZULIU,betNumbers:"",money:t}]}},BAO_ZUSAN_ALL:{pattern:/^(包打?组三|组三)[\-—~～@#￥%…&!、\\/*,一－。＝=·吊赶打各\s]*([0-9]+)$/,handler:function(e,n){var t=e[2]||"";Object(u["parseLotteryType"])(n);return[{methodId:u["METHOD_ID"].BAODA_ZUSAN,betNumbers:"",money:t}]}},SHA_MA_SUPER:{pattern:/^杀([0-9]+)[\-—~～@#￥%…&!、\\/*,一－。.＝=·吊赶打各\s]+([0-9]+)[\-—~～@#￥%…&!、\\/*,一－。.＝=·吊赶打各\s]*$/,handler:function(e,n){var t=e[1].split("").join(","),r=e[2];return[{methodId:u["METHOD_ID"].SHA_MA,betNumbers:t,money:r}]}},HEZHI_DA_SUPER:{pattern:/^[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*(?:和值?|和)?[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*大[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([0-9]+)$/,handler:function(e,n){var t=e[1];Object(u["parseLotteryType"])(n);return[{methodId:u["METHOD_ID"].HEZHI_DAXIAO,daxiao:300,money:t,betNumbers:null}]}},HEZHI_XIAO_SUPER:{pattern:/^[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*(?:和值?|和)?[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*小[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([0-9]+)$/,handler:function(e,n){var t=e[1];Object(u["parseLotteryType"])(n);return[{methodId:u["METHOD_ID"].HEZHI_DAXIAO,daxiao:301,money:t,betNumbers:null}]}},HEZHI_DAN_SUPER:{pattern:/^[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*(?:和值?|和)?[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*单[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([0-9]+)$/,handler:function(e,n){var t=e[1];Object(u["parseLotteryType"])(n);return[{methodId:u["METHOD_ID"].HEZHI_DANSHUAN,danshuang:200,money:t,betNumbers:null}]}},HEZHI_SHUANG_SUPER:{pattern:/^[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*(?:和值?|和)?[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*双[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([0-9]+)$/,handler:function(e,n){var t=e[1];Object(u["parseLotteryType"])(n);return[{methodId:u["METHOD_ID"].HEZHI_DANSHUAN,danshuang:201,money:t,betNumbers:null}]}},BAOZI_MULTI_LINE_EACH_MONEY:{pattern:/^([\s\S]+)$/m,handler:function(e,n){var u,d=n.split(/\n+/).map((function(e){return e.trim()})).filter(Boolean),l=new Map,I=[],s=/^([0-9]{1,9})[ \t]*(豹子|炮)[ \t]*各([0-9]+)(元|米|块)?[^\d]*$/,i=Object(o["a"])(d);try{for(i.s();!(u=i.n()).done;){var _=u.value,c=_.match(s);if(!c)return console.log('【BAOZI_MULTI_LINE_EACH_MONEY】行 "'.concat(_,'" 不匹配豹子/炮格式，返回null')),null;I.push({line:_,match:c})}}catch(E){i.e(E)}finally{i.f()}console.log("【BAOZI_MULTI_LINE_EACH_MONEY】所有 ".concat(I.length," 行都是豹子/炮格式，继续处理"));for(var h=0,f=I;h<f.length;h++){var m,U=f[h].match,A=U[1].split("").filter((function(e){return/[0-9]/.test(e)})),M=U[3];l.has(M)||l.set(M,[]),(m=l.get(M)).push.apply(m,Object(r["a"])(A.map((function(e){return e+e+e}))))}if(0===l.size)return console.log("【BAOZI_MULTI_LINE_EACH_MONEY】没有有效的豹子/炮内容，返回null"),null;var N=Array.from(l.entries()).map((function(e){var n=Object(a["a"])(e,2),r=n[0],o=n[1];return{methodId:t("f9d6").METHOD_ID.SANMA_ZHIXUAN,betNumbers:o.join(","),money:r}}));return console.log("【BAOZI_MULTI_LINE_EACH_MONEY】处理完成，返回 ".concat(N.length," 个结果组")),N}},YIMA_DANSHUAN_SUPER:{pattern:/^(百位|百|十位|十|个位|个)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*(单|双)(\d+)(元|米|块)?$/,handler:function(e,n){var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},o=e[1],a=e[2],u=e[3];return[{methodId:t("f9d6").METHOD_ID.YIMA_DANSHUAN,dingwei:r[o],betNumbers:a,money:u}]}},YIMA_DAXIAO_SUPER:{pattern:/^(百位|百|十位|十|个位|个)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*(大|小)(\d+)(元|米|块)?$/,handler:function(e,n){var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},o=e[1],a=e[2],u=e[3];return[{methodId:t("f9d6").METHOD_ID.YIMA_DAXIAO,dingwei:r[o],betNumbers:a,money:u}]}},SPECIAL_FU_PAI_PAO:{pattern:/[。.,、\\/*\s\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([福3d3D福彩]+)[炮豹子]+(\d+)(元|米|块)?[。.,、\\/*\\s\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([排排三体体彩]+)[炮豹子]+(\d+)(元|米|块)?/,handler:function(e,n){var r=["000","111","222","333","444","555","666","777","888","999"],o=e[2],a=e[5],u=1,d=2;return[{methodId:t("f9d6").METHOD_ID.SANMA_ZHIXUAN,betNumbers:r.join(","),money:(parseFloat(o)/10).toString(),lotteryId:u},{methodId:t("f9d6").METHOD_ID.SANMA_ZHIXUAN,betNumbers:r.join(","),money:(parseFloat(a)/10).toString(),lotteryId:d}]}},SPECIAL_FU_PAI_PAO_EACH:{pattern:/[。.,、\\/*\s\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([福3d3D福彩]+)[炮豹子]+各(\d+)(元|米|块)?[。.,、\\/*\s\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([排排三体体彩]+)[炮豹子]+各(\d+)(元|米|块)?/,handler:function(e,n){var r=["000","111","222","333","444","555","666","777","888","999"],o=e[2],a=e[5],u=1,d=2;return[{methodId:t("f9d6").METHOD_ID.SANMA_ZHIXUAN,betNumbers:r.join(","),money:o,lotteryId:u},{methodId:t("f9d6").METHOD_ID.SANMA_ZHIXUAN,betNumbers:r.join(","),money:a,lotteryId:d}]}},YIMA_DINGWEI_FORMAT1_NEW:{pattern:/^(百位?|百)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*([0-9]+(?:[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*[0-9]+)*)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*(十位?|十)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*([0-9]+(?:[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*[0-9]+)*)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*(个位?|个)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*([0-9]+(?:[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*[0-9]+)*)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*各[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*([0-9]+)(?:元|块|快|米)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*(一码定位|一码|定)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*$/,handler:function(e,n){if(!e[1]||!e[3]||!e[5])return null;if(!e[8])return null;var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},o=(e[2].match(/[0-9]/g)||[]).join(","),a=(e[4].match(/[0-9]/g)||[]).join(","),u=(e[6].match(/[0-9]/g)||[]).join(","),d=e[7];return[{methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[e[1]],betNumbers:o,money:d},{methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[e[3]],betNumbers:a,money:d},{methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[e[5]],betNumbers:u,money:d}]}},YIMA_DINGWEI_FORMAT2_NEW:{pattern:/^([\s\S]+)$/m,handler:function(e,n){if(!/(?:百位?|百)|(?:十位?|十)|(?:个位?|个)/.test(n))return null;if(!/一码定位|一码|定/.test(n))return null;var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},a=n.split(/\n+/).map((function(e){return e.trim()})).filter(Boolean),u="",d={},l=/^(百位?|百|十位?|十|个位?|个)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*([0-9]+(?:[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*[0-9]+)*)/,I=/各[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*([0-9]+)(?:元|块|快|米)?/,s=/一码定位|一码|定/,i=n.match(I);i&&(u=i[1]);var _,c=Object(o["a"])(a);try{for(c.s();!(_=c.n()).done;){var h=_.value;if(!I.test(h)&&!s.test(h)){var f=h.match(l);if(f){var m=f[1],U=f[2],A=U.match(/[0-9]/g);A&&A.length>0&&(d[m]=A.join(","))}else if(h.trim()&&!/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*$/.test(h))return console.log('【YIMA_DINGWEI_FORMAT2_NEW】行 "'.concat(h,'" 不匹配一码定位格式，返回null')),null}}}catch(b){c.e(b)}finally{c.f()}var M=Object.keys(d).find((function(e){return e.includes("百")})),N=Object.keys(d).find((function(e){return e.includes("十")})),E=Object.keys(d).find((function(e){return e.includes("个")}));return M&&N&&E?(console.log("【YIMA_DINGWEI_FORMAT2_NEW】识别成功，三个位置都有数据"),[{methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[M],betNumbers:d[M],money:u},{methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[N],betNumbers:d[N],money:u},{methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[E],betNumbers:d[E],money:u}]):(console.log("【YIMA_DINGWEI_FORMAT2_NEW】未找到完整的三个位置，返回null"),null)}},YIMA_DINGWEI_FORMAT3_NEW:{pattern:/^([\s\S]+)$/m,handler:function(e,n){if(!/(?:百位?|百)|(?:十位?|十)|(?:个位?|个)/.test(n))return null;if(!/一码定位|一码|定/.test(n))return null;var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},o=n.split(/\n+/).map((function(e){return e.trim()})).filter(Boolean);if(o.length<2)return console.log("【YIMA_DINGWEI_FORMAT3_NEW】行数不足，返回null"),null;var a=o[0],u=a.match(/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*(百位?|百)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]+(十位?|十)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]+(个位?|个)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*$/);if(!u)return console.log('【YIMA_DINGWEI_FORMAT3_NEW】表头行 "'.concat(a,'" 不匹配格式，返回null')),null;for(var d="",l=1;l<o.length;l++)if(/^[\d\-—~～@#￥%…&!、\\/*,，.＝=·\s]+$/.test(o[l])){d=o[l];break}if(!d)return console.log("【YIMA_DINGWEI_FORMAT3_NEW】未找到数字行，返回null"),null;for(var I=/各[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*([0-9]+)(?:元|块|快|米)?/,s=/一码定位|一码|定/,i=1;i<o.length;i++){var _=o[i];if(_!==d&&(!I.test(_)&&!s.test(_)&&!/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*$/.test(_)))return console.log('【YIMA_DINGWEI_FORMAT3_NEW】行 "'.concat(_,'" 不符合表格格式，返回null')),null}var c=d.split(/[\-—~～@#￥%…&!、\\/*,，.＝=·\s]+/).filter(Boolean);if(3!==c.length)return console.log("【YIMA_DINGWEI_FORMAT3_NEW】数字行分组数量不是3个，实际".concat(c.length,"个，返回null")),null;var h="",f=n.match(I);f&&(h=f[1]);var m=(c[0].match(/[0-9]/g)||[]).join(","),U=(c[1].match(/[0-9]/g)||[]).join(","),A=(c[2].match(/[0-9]/g)||[]).join(",");return console.log("【YIMA_DINGWEI_FORMAT3_NEW】表格格式识别成功"),[{methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[u[1]],betNumbers:m,money:h},{methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[u[2]],betNumbers:U,money:h},{methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[u[3]],betNumbers:A,money:h}]}},YIMA_DINGWEI_TABLE_STANDARD:{pattern:/^([\s\S]+)$/m,handler:function(e,n){if(!/(?:百位?|百)|(?:十位?|十)|(?:个位?|个)/.test(n))return null;if(!/一码定位|一码|定/.test(n))return null;var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},o=n.split(/\n+/).map((function(e){return e.trim()})).filter(Boolean);if(o.length<2)return console.log("【YIMA_DINGWEI_TABLE_STANDARD】行数不足，返回null"),null;var a=o[0],u=a.match(/^[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*(百位?|百)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]+(十位?|十)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]+(个位?|个)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*$/);if(!u)return console.log('【YIMA_DINGWEI_TABLE_STANDARD】表头行 "'.concat(a,'" 不匹配格式，返回null')),null;for(var d="",l=1;l<o.length;l++)if(/^[\d\s\-—~～@#￥%…&!、\\/*,，.＝=·]+$/.test(o[l])){d=o[l];break}if(!d)return console.log("【YIMA_DINGWEI_TABLE_STANDARD】未找到数字行，返回null"),null;for(var I=/各[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+)(?:元|块|快|米)?/,s=/一码定位|一码|定/,i=1;i<o.length;i++){var _=o[i];if(_!==d&&(!I.test(_)&&!s.test(_)&&!/^[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*$/.test(_)))return console.log('【YIMA_DINGWEI_TABLE_STANDARD】行 "'.concat(_,'" 不符合标准表格格式，返回null')),null}var c=d.split(/[\s\-—~～@#￥%…&!、\\/*,，.＝=·]+/).filter(Boolean);if(3!==c.length)return console.log("【YIMA_DINGWEI_TABLE_STANDARD】数字行分组数量不是3个，实际".concat(c.length,"个，返回null")),null;var h="",f=n.match(I);f&&(h=f[1]);var m=(c[0].match(/[0-9]/g)||[]).join(","),U=(c[1].match(/[0-9]/g)||[]).join(","),A=(c[2].match(/[0-9]/g)||[]).join(",");return console.log("【YIMA_DINGWEI_TABLE_STANDARD】标准表格格式识别成功"),[{methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[u[1]],betNumbers:m,money:h},{methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[u[2]],betNumbers:U,money:h},{methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[u[3]],betNumbers:A,money:h}]}},YIMA_DINGWEI_SINGLE_POSITION:{pattern:/^(百位?|百|十位?|十|个位?|个)[ \t\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+(?:[ \t\-—~～@#￥%…&!、\\/*,，.＝=·]*[0-9]+)*)[ \t\-—~～@#￥%…&!、\\/*,，.＝=·]*各[ \t\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+)(?:元|块|快|米)?[ \t\-—~～@#￥%…&!、\\/*,，.＝=·]*$/,handler:function(e,n){var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},o=e[1],a=e[2],u=e[3],d=(a.match(/[0-9]/g)||[]).join(",");return[{methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[o],betNumbers:d,money:u}]}},YIMA_DINGWEI_SINGLE_POSITION_NO_GE:{pattern:/^(百位?|百|十位?|十|个位?|个)[ \t\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+(?:[ \t\-—~～@#￥%…&!、\\/*,，.＝=·]*[0-9]+)*)[ \t\-—~～@#￥%…&!、\\/*,，.＝=·]+([0-9]+)(?:元|块|快|米)*$/,handler:function(e,n){if(!/(?:百位?|百|十位?|十|个位?|个)/.test(n))return console.log('【YIMA_DINGWEI_SINGLE_POSITION_NO_GE】行 "'.concat(n,'" 不包含位置标识，返回null')),null;var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},o=e[1],a=e[2],u=e[3];if(!r[o])return console.log('【YIMA_DINGWEI_SINGLE_POSITION_NO_GE】位置标识 "'.concat(o,'" 无效，返回null')),null;var d=(a.match(/[0-9]/g)||[]).join(",");return d?(console.log("【YIMA_DINGWEI_SINGLE_POSITION_NO_GE】识别成功: 位置=".concat(o,", 号码=").concat(d,", 金额=").concat(u)),[{methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[o],betNumbers:d,money:u}]):(console.log("【YIMA_DINGWEI_SINGLE_POSITION_NO_GE】未提取到有效号码，返回null"),null)}},YIMA_DINGWEI_TWO_LINES:{pattern:/^([\s\S]+)$/m,handler:function(e,n){if(!/(?:百位?|百)|(?:十位?|十)|(?:个位?|个)/.test(n))return null;if(!/各[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+)(?:元|块|快|米)?/.test(n))return null;var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},a=n.split(/\n+/).map((function(e){return e.trim()})).filter(Boolean);if(2!==a.length)return console.log("【YIMA_DINGWEI_TWO_LINES】行数不是2行，实际".concat(a.length,"行，返回null")),null;var u="",d={},l=/^(百位?|百|十位?|十|个位?|个)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+(?:[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*[0-9]+)*)/,I=/各[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+)(?:元|块|快|米)?/,s=n.match(I);s&&(u=s[1]);var i,_=Object(o["a"])(a);try{for(_.s();!(i=_.n()).done;){var c=i.value;if(!I.test(c)){var h=c.match(l);if(h){var f=h[1],m=h[2],U=m.match(/[0-9]/g);U&&U.length>0&&(d[f]=U.join(","))}else if(c.trim()&&!/^[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*$/.test(c))return console.log('【YIMA_DINGWEI_TWO_LINES】行 "'.concat(c,'" 不匹配一码定位格式，返回null')),null}}}catch(D){_.e(D)}finally{_.f()}var A=Object.keys(d);if(2!==A.length)return console.log("【YIMA_DINGWEI_TWO_LINES】位置数量不是2个，实际".concat(A.length,"个，返回null")),null;console.log("【YIMA_DINGWEI_TWO_LINES】两行两位置格式识别成功");for(var M=[],N=0,E=A;N<E.length;N++){var b=E[N];M.push({methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[b],betNumbers:d[b],money:u})}return M}},YIMA_DINGWEI_ONE_LINE_TWO_POSITIONS:{pattern:/^(百位?|百|十位?|十|个位?|个)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+(?:[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*[0-9]+)*)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*(百位?|百|十位?|十|个位?|个)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+(?:[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*[0-9]+)*)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*各[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+)(?:元|块|快|米)?[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*$/,handler:function(e,n){var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},o=e[1],a=e[2],u=e[3],d=e[4],l=e[5];if(r[o]===r[u])return null;var I=(a.match(/[0-9]/g)||[]).join(","),s=(d.match(/[0-9]/g)||[]).join(",");return[{methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[o],betNumbers:I,money:l},{methodId:t("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[u],betNumbers:s,money:l}]}}};Object.keys(d).forEach((function(e){var n=d[e].pattern;"string"===typeof n&&n.endsWith("$")?d[e].pattern=n.replace(/\$$/,"[，,./*-=。·、s]*$"):n instanceof RegExp&&n.source.endsWith("$")&&(d[e].pattern=new RegExp(n.source.replace(/\$$/,"[，,./*-=。·、s]*$")))}))}}]);