<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>彩票系统帮助文档中心</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            position: relative;
            overflow-x: hidden;
        }


        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px 0;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
        }

        .back-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
            white-space: nowrap;
        }

        .back-btn:hover {
            background: linear-gradient(135deg, #5a6fd8, #6a5acd);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .left-section {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(45deg, #409EFF, #67C23A);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            font-weight: bold;
        }

        .logo-text {
            font-size: 24px;
            font-weight: bold;
            color: #409EFF;
        }

        .search-container {
            max-width: 400px;
            width: 100%;
            position: relative;
        }
        
        .search-box {
            width: 100%;
            padding: 12px 50px 12px 20px;
            border: 2px solid #e4e7ed;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: all 0.3s ease;
            background: white;
        }
        
        .search-box:focus {
            border-color: #409EFF;
            box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
        }
        
        .search-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #909399;
            font-size: 18px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .welcome-section {
            text-align: center;
            margin-bottom: 50px;
            color: white;
            padding: 40px 20px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            margin: 30px 20px 40px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .welcome-title {
            font-size: 42px;
            font-weight: bold;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .welcome-subtitle {
            font-size: 18px;
            opacity: 0.9;
            margin-bottom: 30px;
        }
        
        .stats {
            display: flex;
            justify-content: center;
            gap: 40px;
            margin-top: 30px;
        }
        
        .stat-item {
            text-align: center;
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            display: block;
        }

        .stat-label {
            font-size: 14px;
            opacity: 0.8;
            margin-top: 5px;
        }
        
        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }
        
        .module-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .module-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            border-color: #409EFF;
        }
        
        .module-card.hidden {
            display: none;
        }

        .module-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .module-type {
            background: rgba(64, 158, 255, 0.1);
            color: #409EFF;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            margin-left: auto;
        }

        .module-source {
            color: #909399;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .module-footer {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 12px;
            padding-top: 8px;
            border-top: 1px solid #f0f0f0;
        }
        
        .module-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            font-weight: bold;
        }
        
        .module-title {
            font-size: 22px;
            font-weight: bold;
            color: #303133;
        }
        
        .module-description {
            color: #606266;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .module-features {
            margin-bottom: 20px;
        }
        
        .feature-list {
            display: none;
            margin-top: 15px;
        }
        
        .feature-list.show {
            display: block;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            color: #606266;
            font-size: 14px;
        }
        
        .feature-icon {
            color: #67C23A;
            font-size: 12px;
        }
        
        .module-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        
        .btn-primary {
            background: #409EFF;
            color: white;
        }
        
        .btn-primary:hover {
            background: #337ecc;
        }
        
        .btn-secondary {
            background: #f4f4f5;
            color: #606266;
            border: 1px solid #dcdfe6;
        }
        
        .btn-secondary:hover {
            background: #ecf5ff;
            color: #409EFF;
            border-color: #409EFF;
        }
        
        .toggle-btn {
            background: none;
            border: none;
            color: #409EFF;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .toggle-btn:hover {
            color: #337ecc;
        }
        
        .no-results {
            text-align: center;
            padding: 60px 20px;
            color: white;
            display: none;
        }
        
        .no-results.show {
            display: block;
        }
        
        .no-results-icon {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.6;
        }
        
        .no-results-text {
            font-size: 18px;
            margin-bottom: 10px;
        }
        
        .no-results-hint {
            font-size: 14px;
            opacity: 0.8;
        }
        
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 20px;
            }

            .left-section {
                width: 100%;
                justify-content: space-between;
            }

            .search-container {
                max-width: 100%;
            }

            .welcome-title {
                font-size: 32px;
            }

            .stats {
                flex-direction: column;
                gap: 20px;
            }

            .modules-grid {
                grid-template-columns: 1fr;
            }
            
            .welcome-title {
                font-size: 32px;
            }
            
            .stats {
                flex-direction: column;
                gap: 20px;
            }
            
            .modules-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="left-section">
                <button class="back-btn" onclick="goBack()">← 返回系统</button>
                <div class="logo">
                    <div class="logo-icon">📚</div>
                    <div class="logo-text">帮助文档中心</div>
                </div>
            </div>
            <div class="search-container">
                <input type="text" class="search-box" placeholder="搜索功能模块..." id="searchInput">
                <span class="search-icon">🔍</span>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="welcome-section">
            <h1 class="welcome-title">彩票系统使用指南</h1>
            <p class="welcome-subtitle">全面的功能说明，助您快速掌握系统操作</p>
            <div class="stats">
                <div class="stat-item">
                    <span class="stat-number" id="moduleCount">6</span>
                    <span class="stat-label">功能模块</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">100+</span>
                    <span class="stat-label">功能特性</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">24/7</span>
                    <span class="stat-label">在线帮助</span>
                </div>
            </div>
        </div>

        <div class="modules-grid" id="modulesGrid">
            <!-- 模块卡片将通过JavaScript动态生成 -->
        </div>

        <div class="no-results" id="noResults">
            <div class="no-results-icon">🔍</div>
            <div class="no-results-text">未找到匹配的功能模块</div>
            <div class="no-results-hint">请尝试使用其他关键词搜索</div>
        </div>
    </div>

    <script>
        // 功能模块数据
        const modules = [
            {
                id: 'dashboard',
                title: '系统首页',
                description: '系统主页面，提供数据概览、快速导航和统计分析功能。',
                icon: '🏠',
                iconColor: 'linear-gradient(45deg, #667eea, #764ba2)',
                features: [
                    '数据概览区域',
                    '快速导航功能',
                    '统计图表分析',
                    '使用技巧指导'
                ],
                helpFile: 'dashboard-help.html',
                keywords: ['首页', '概览', '导航', '统计', '数据', '图表', '快捷'],
                detailContent: [
                    '统计卡片', '数据刷新', '功能模块入口', '常用操作快捷方式',
                    '投资趋势图', '用户活动指标', '图表分析', '数据可视化',
                    '使用技巧', '注意事项', '快速上手', '功能介绍'
                ]
            },
            {
                id: 'profile',
                title: '个人中心',
                description: '个人信息管理和设置中心，包括资料修改、安全设置、个人统计等功能。',
                icon: '👤',
                iconColor: 'linear-gradient(45deg, #667eea, #764ba2)',
                features: [
                    '个人资料管理',
                    '安全设置',
                    '个人统计',
                    '系统设置'
                ],
                helpFile: 'profile-help.html',
                keywords: ['个人中心', '资料', '密码', '设置', '统计', '安全', '头像'],
                detailContent: [
                    '基本信息', '信息修改', '头像上传', '密码管理', '登录记录',
                    '投注统计', '趋势分析', '界面设置', '通知设置', '使用技巧'
                ]
            },
            {
                id: 'navbar',
                title: '顶部导航',
                description: '系统主导航栏功能说明，包括菜单导航、用户信息、工具栏等功能。',
                icon: '🧭',
                iconColor: 'linear-gradient(45deg, #667eea, #764ba2)',
                features: [
                    '主菜单导航',
                    '用户信息区域',
                    '工具栏功能',
                    '快捷操作'
                ],
                helpFile: 'navbar-help.html',
                keywords: ['导航', '菜单', '用户', '工具栏', '快捷', '通知', '设置'],
                detailContent: [
                    '菜单结构', '菜单操作', '用户下拉菜单', '通知消息', '系统设置',
                    '快速投注', '系统状态', '使用技巧', '注意事项'
                ]
            },
            {
                id: 'record',
                title: '下注记录管理',
                description: '管理和查看所有投注记录，支持多级数据展示、对账功能、批量操作等高级功能。',
                icon: '📊',
                iconColor: 'linear-gradient(45deg, #409EFF, #67C23A)',
                features: [
                    '多条件搜索筛选',
                    '三级数据结构展示',
                    '对账模式功能',
                    '批量操作管理',
                    '数据导出功能',
                    '权限控制管理'
                ],
                helpFile: 'record-index-help.html',
                keywords: ['下注', '记录', '投注', '对账', '流水', '管理', '搜索', '导出'],
                detailContent: [
                    '玩法筛选', '流水号筛选', '用户筛选', '中奖状态筛选', '识别框搜索',
                    '新增按钮', '批量删除流水', '导出按钮', '展开二级', '展开三级', '开始对账', '退出对账',
                    '对账模式特点', '对账操作方法', '对账统计信息', '标记对账状态', '对账进度', '对账金额',
                    '三级数据结构', '流水号分组', '投注记录', '号码详情', '表格列说明', '展开收起', '多选功能', '排序功能',
                    '普通用户权限', '超级管理员权限', '权限控制', '浮动操作按钮', '显示条件', '包含功能',
                    '使用技巧', '注意事项', '故障排除', '页面加载缓慢', '对账状态丢失', '导出失败'
                ]
            },
            {
                id: 'bet-dialog',
                title: '下注弹窗功能',
                description: '投注弹窗的详细使用说明，包括号码识别、玩法选择、金额设置、格式转换等功能。',
                icon: '📝',
                iconColor: 'linear-gradient(45deg, #9C27B0, #673AB7)',
                features: [
                    '智能号码识别',
                    '多玩法选择',
                    '格式转换工具',
                    '投注组管理',
                    '实时金额统计',
                    '表单验证提交'
                ],
                helpFile: 'bet-dialog-help.html',
                keywords: ['下注弹窗', '投注', '号码识别', '玩法选择', '金额设置', '格式转换', '批量投注'],
                detailContent: [
                    '福体开关', '期号设置', '用户选择', '彩种切换', '当前用户显示', '用户切换功能',
                    '智能号码识别', '号码数量限制', '格式转换工具', '自然语言输入', '金额格式转换', '冒号格式转换', '加号格式转换',
                    '投注组操作', '玩法选择', '添加投注组', '删除投注组', '多组投注', '彩种自动匹配',
                    '大小单双设置', '定位玩法', '和值玩法', '单双选择', '大小选择', '定位选择', '和值选择', '动态和值范围',
                    '金额设置', '实时统计', '单注金额', '金额验证', '总金额统计', '总注数统计',
                    '表单验证', '提交操作', '必填项验证', '数据格式验证', '确定按钮', '取消按钮'
                ]
            },
            {
                id: 'draw',
                title: '开奖管理',
                description: '管理彩票开奖信息，包括开奖号码录入、开奖结果查询、中奖计算等功能。',
                icon: '🎯',
                iconColor: 'linear-gradient(45deg, #E6A23C, #F56C6C)',
                features: [
                    '开奖号码管理',
                    '开奖结果查询',
                    '中奖自动计算',
                    '开奖历史记录',
                    '结算状态管理',
                    '开奖公告发布'
                ],
                helpFile: 'draw-help.html',
                keywords: ['开奖', '中奖', '号码', '结果', '计算', '公告', '历史'],
                detailContent: [
                    '开奖号码录入', '开奖结果查询', '中奖计算', '开奖历史', '结算管理', '公告发布',
                    '期号管理', '号码验证', '自动计算', '手动录入', '批量开奖', '开奖统计',
                    '中奖查询', '奖金计算', '派奖管理', '开奖公告', '历史记录', '数据导出'
                ]
            },
            {
                id: 'customer',
                title: '玩家管理',
                description: '管理系统中的玩家信息，包括玩家注册、信息维护、状态管理等功能。',
                icon: '👥',
                iconColor: 'linear-gradient(45deg, #67C23A, #409EFF)',
                features: [
                    '玩家信息管理',
                    '玩家状态控制',
                    '玩家数据统计',
                    '玩家权限设置',
                    '玩家历史记录',
                    '批量操作功能'
                ],
                helpFile: 'customer-help.html',
                keywords: ['玩家', '用户', '客户', '管理', '注册', '信息', '状态'],
                detailContent: [
                    '玩家注册', '信息维护', '状态管理', '玩家查询', '数据统计', '权限设置',
                    '玩家列表', '详细信息', '状态切换', '批量操作', '数据导出', '历史记录',
                    '账户管理', '密码重置', '权限分配', '玩家分组', '标签管理', '备注信息'
                ]
            },
            {
                id: 'odds',
                title: '赔率管理',
                description: '管理各种彩票玩法的赔率设置，支持动态调整和批量配置。',
                icon: '💰',
                iconColor: 'linear-gradient(45deg, #F56C6C, #E6A23C)',
                features: [
                    '赔率配置管理',
                    '玩法赔率设置',
                    '赔率历史记录',
                    '批量赔率调整',
                    '赔率权限控制',
                    '赔率统计分析'
                ],
                helpFile: 'odds-help.html',
                keywords: ['赔率', '玩法', '配置', '设置', '调整', '管理'],
                detailContent: [
                    '赔率配置', '玩法设置', '赔率调整', '批量配置', '历史记录', '权限控制',
                    '赔率计算', '玩法管理', '赔率模板', '快速设置', '赔率复制', '赔率导入',
                    '赔率统计', '赔率分析', '风险控制', '赔率监控', '异常预警', '数据备份'
                ]
            },
            {
                id: 'winning',
                title: '中奖管理',
                description: '中奖记录管理功能，包括中奖查询、对账功能、结算管理等核心功能。',
                icon: '🏆',
                iconColor: 'linear-gradient(45deg, #F56C6C, #67C23A)',
                features: [
                    '中奖记录查询',
                    '对账模式功能',
                    '结算状态管理',
                    '中奖统计分析',
                    '数据导出功能',
                    '批量操作管理'
                ],
                helpFile: 'winning-help.html',
                keywords: ['中奖', '管理', '对账', '结算', '查询', '统计', '导出'],
                detailContent: [
                    '搜索功能', '中奖查询', '对账模式', '结算管理', '统计分析',
                    '数据表格', '中奖信息', '对账操作', '结算操作', '批量处理',
                    '使用技巧', '对账方法', '结算流程', '注意事项'
                ]
            },
            {
                id: 'ticket',
                title: '提交工单',
                description: '工单提交和管理功能，包括问题反馈、需求提交、工单跟踪等功能。',
                icon: '📋',
                iconColor: 'linear-gradient(45deg, #409EFF, #67C23A)',
                features: [
                    '工单创建提交',
                    '工单状态跟踪',
                    '问题分类管理',
                    '附件上传功能',
                    '工单历史记录',
                    '优先级设置'
                ],
                helpFile: 'ticket-help.html',
                keywords: ['工单', '提交', '反馈', '问题', '需求', '跟踪', '管理'],
                detailContent: [
                    '工单创建', '问题描述', '分类选择', '优先级设置', '附件上传',
                    '工单跟踪', '状态查看', '进度更新', '回复查看', '历史记录',
                    '使用技巧', '工单规范', '问题描述', '注意事项'
                ]
            }
        ];

        // 初始化页面
        function initPage() {
            renderModules(modules);
            updateModuleCount();
            bindEvents();
        }

        // 渲染模块卡片
        function renderModules(modulesToRender) {
            const grid = document.getElementById('modulesGrid');
            grid.innerHTML = '';

            modulesToRender.forEach(module => {
                const card = createModuleCard(module);
                grid.appendChild(card);
            });
        }

        // 创建模块卡片
        function createModuleCard(module) {
            const card = document.createElement('div');
            card.className = 'module-card';
            card.dataset.moduleId = module.id;

            card.innerHTML = `
                <div class="module-header">
                    <div class="module-icon" style="background: ${module.iconColor}">
                        ${module.icon}
                    </div>
                    <div class="module-title">${module.title}</div>
                </div>
                <div class="module-description">${module.description}</div>
                <div class="module-features">
                    <button class="toggle-btn" onclick="toggleFeatures('${module.id}')">
                        <span id="toggle-text-${module.id}">展开功能特性</span>
                        <span id="toggle-icon-${module.id}">▼</span>
                    </button>
                    <div class="feature-list" id="features-${module.id}">
                        ${module.features.map(feature => `
                            <div class="feature-item">
                                <span class="feature-icon">✓</span>
                                <span>${feature}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
                <div class="module-actions">
                    <a href="${module.helpFile}" class="btn btn-primary">
                        📖 查看详细说明
                    </a>
                    <button class="btn btn-secondary" onclick="toggleFeatures('${module.id}')">
                        <span id="action-text-${module.id}">展开详情</span>
                    </button>
                </div>
            `;

            return card;
        }

        // 切换功能特性显示
        function toggleFeatures(moduleId) {
            const featureList = document.getElementById(`features-${moduleId}`);
            const toggleText = document.getElementById(`toggle-text-${moduleId}`);
            const toggleIcon = document.getElementById(`toggle-icon-${moduleId}`);
            const actionText = document.getElementById(`action-text-${moduleId}`);

            if (featureList.classList.contains('show')) {
                featureList.classList.remove('show');
                toggleText.textContent = '展开功能特性';
                toggleIcon.textContent = '▼';
                actionText.textContent = '展开详情';
            } else {
                featureList.classList.add('show');
                toggleText.textContent = '收起功能特性';
                toggleIcon.textContent = '▲';
                actionText.textContent = '收起详情';
            }
        }

        // 模糊搜索匹配函数
        function fuzzyMatch(text, query) {
            text = text.toLowerCase();
            query = query.toLowerCase();

            // 完全匹配
            if (text.includes(query)) {
                return true;
            }

            // 模糊匹配：检查查询字符是否按顺序出现在文本中
            let queryIndex = 0;
            for (let i = 0; i < text.length && queryIndex < query.length; i++) {
                if (text[i] === query[queryIndex]) {
                    queryIndex++;
                }
            }
            return queryIndex === query.length;
        }

        // 增强的搜索功能 - 显示具体功能项
        function searchModules(query) {
            const searchText = query.toLowerCase().trim();

            if (searchText === '') {
                renderModules(modules);
                document.getElementById('noResults').classList.remove('show');
                return;
            }

            // 搜索具体功能项
            const searchResults = [];

            modules.forEach(module => {
                // 搜索功能特性
                module.features.forEach(feature => {
                    if (fuzzyMatch(feature, searchText)) {
                        searchResults.push({
                            type: 'feature',
                            title: feature,
                            module: module.title,
                            moduleId: module.id,
                            helpFile: module.helpFile,
                            icon: module.icon,
                            iconColor: module.iconColor,
                            score: calculateFeatureScore(feature, searchText, 'feature')
                        });
                    }
                });

                // 搜索详情页内容（二级功能）
                if (module.detailContent) {
                    module.detailContent.forEach(content => {
                        if (fuzzyMatch(content, searchText)) {
                            searchResults.push({
                                type: 'detail',
                                title: content,
                                module: module.title,
                                moduleId: module.id,
                                helpFile: module.helpFile,
                                icon: module.icon,
                                iconColor: module.iconColor,
                                score: calculateFeatureScore(content, searchText, 'detail')
                            });
                        }
                    });
                }

                // 搜索关键词（如果没有找到具体功能，则显示模块）
                const keywordMatch = module.keywords.some(keyword => fuzzyMatch(keyword, searchText));
                const titleMatch = fuzzyMatch(module.title, searchText);

                if ((keywordMatch || titleMatch) && !searchResults.some(r => r.moduleId === module.id)) {
                    searchResults.push({
                        type: 'module',
                        title: module.title,
                        module: module.title,
                        moduleId: module.id,
                        helpFile: module.helpFile,
                        icon: module.icon,
                        iconColor: module.iconColor,
                        description: module.description,
                        score: calculateFeatureScore(module.title, searchText, 'module')
                    });
                }
            });

            // 按匹配度排序
            const sortedResults = searchResults.sort((a, b) => b.score - a.score);

            // 渲染搜索结果
            renderSearchResults(sortedResults);

            const noResults = document.getElementById('noResults');
            if (sortedResults.length === 0) {
                noResults.classList.add('show');
            } else {
                noResults.classList.remove('show');
            }
        }

        // 计算功能项匹配分数
        function calculateFeatureScore(text, query, type) {
            let score = 0;
            const textLower = text.toLowerCase();
            const queryLower = query.toLowerCase();

            // 完全匹配得分最高
            if (textLower === queryLower) score += 100;

            // 包含匹配
            else if (textLower.includes(queryLower)) score += 80;

            // 模糊匹配
            else if (fuzzyMatch(text, query)) score += 60;

            // 根据类型调整分数
            switch (type) {
                case 'feature': score += 20; break;  // 功能特性优先
                case 'detail': score += 15; break;  // 详情内容次之
                case 'module': score += 5; break;   // 模块标题最低
            }

            return score;
        }

        // 渲染搜索结果
        function renderSearchResults(results) {
            const container = document.getElementById('modulesGrid');

            if (results.length === 0) {
                container.innerHTML = '';
                return;
            }

            const html = results.map(result => {
                const typeLabel = {
                    'feature': '功能',
                    'detail': '详细功能',
                    'module': '模块'
                }[result.type] || '功能';

                // 生成跳转URL，包含要展开的内容参数
                const targetUrl = result.type === 'module'
                    ? result.helpFile
                    : `${result.helpFile}?expand=${encodeURIComponent(result.title)}`;

                return `
                    <div class="module-card" onclick="window.open('${targetUrl}', '_blank')">
                        <div class="module-icon" style="background: ${result.iconColor}">
                            ${result.icon}
                        </div>
                        <div class="module-content">
                            <div class="module-header">
                                <h3 class="module-title">${result.title}</h3>
                                <span class="module-type">${typeLabel}</span>
                            </div>
                            <p class="module-description">
                                ${result.type === 'module' ? result.description : `来自 ${result.module} 模块`}
                            </p>
                            <div class="module-footer">
                                <span class="module-source">📖 ${result.module}</span>
                                <span class="module-arrow">→</span>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');

            container.innerHTML = html;
        }

        // 更新模块数量
        function updateModuleCount() {
            document.getElementById('moduleCount').textContent = modules.length;
        }

        // 绑定事件
        function bindEvents() {
            const searchInput = document.getElementById('searchInput');

            // 搜索输入事件
            searchInput.addEventListener('input', (e) => {
                const query = e.target.value.trim();
                if (query === '') {
                    renderModules(modules);
                    document.getElementById('noResults').classList.remove('show');
                } else {
                    searchModules(query);
                }
            });

            // 回车搜索
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    const query = e.target.value.trim();
                    if (query !== '') {
                        searchModules(query);
                    }
                }
            });
        }

        // 返回功能
        function goBack() {
            // 检查是否有来源页面信息
            const referrer = document.referrer;
            const fromHelp = sessionStorage.getItem('helpFromPage');

            if (fromHelp) {
                // 如果有记录的来源页面，返回到该页面
                sessionStorage.removeItem('helpFromPage');
                window.location.href = fromHelp;
            } else if (referrer && referrer.includes(window.location.origin)) {
                // 如果有referrer且是同域，返回上一页
                window.history.back();
            } else {
                // 否则返回系统首页
                window.location.href = '/';
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initPage);
    </script>
</body>
</html>
