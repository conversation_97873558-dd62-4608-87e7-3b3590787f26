(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0e3c7648","chunk-8adb838a"],{"0068":function(t,e,n){"use strict";n.d(e,"d",(function(){return a})),n.d(e,"a",(function(){return o})),n.d(e,"e",(function(){return i})),n.d(e,"b",(function(){return s})),n.d(e,"c",(function(){return u}));var r=n("b775");function a(t){return Object(r["a"])({url:"/game/qihao/list",method:"get",params:t})}function o(t){return Object(r["a"])({url:"/game/qihao",method:"post",data:t})}function i(t){return Object(r["a"])({url:"/game/qihao",method:"put",data:t})}function s(t){return Object(r["a"])({url:"/game/qihao/"+t,method:"delete"})}function u(t){return Object(r["a"])({url:"/game/qihao/current/"+t,method:"get"})}},"01e0":function(t,e,n){},"0481":function(t,e,n){"use strict";var r=n("23e7"),a=n("a2bf6"),o=n("7b0b"),i=n("07fa"),s=n("5926"),u=n("65f0");r({target:"Array",proto:!0},{flat:function(){var t=arguments.length?arguments[0]:void 0,e=o(this),n=i(e),r=u(e,0);return r.length=a(r,e,e,n,0,void 0===t?1:s(t)),r}})},"07ac":function(t,e,n){"use strict";var r=n("23e7"),a=n("6f53").values;r({target:"Object",stat:!0},{values:function(t){return a(t)}})},"0901":function(t,e,n){"use strict";n.d(e,"e",(function(){return a})),n.d(e,"d",(function(){return o})),n.d(e,"a",(function(){return i})),n.d(e,"f",(function(){return s})),n.d(e,"c",(function(){return u})),n.d(e,"b",(function(){return l}));var r=n("b775");function a(t){return Object(r["a"])({url:"/game/customer/list",method:"get",params:t})}function o(t){return Object(r["a"])({url:"/game/customer/"+t,method:"get"})}function i(t){return Object(r["a"])({url:"/game/customer",method:"post",data:t})}function s(t){return Object(r["a"])({url:"/game/customer",method:"put",data:t})}function u(t){return Object(r["a"])({url:"/game/customer/"+t,method:"delete"})}function l(){return Object(r["a"])({url:"/game/customer/checkAndSetDefault",method:"get"})}},"2e8d":function(t,e,n){"use strict";n.r(e),n.d(e,"SEVEN_CODE_PATTERNS",(function(){return s}));var r=n("2909"),a=n("3835"),o=n("b85c"),i=(n("99af"),n("4de4"),n("caad"),n("a15b"),n("d81d"),n("14d9"),n("4e82"),n("4ec9"),n("b64b"),n("d3b7"),n("4d63"),n("c607"),n("ac1f"),n("2c3e"),n("00b4"),n("25f0"),n("8a79"),n("2532"),n("3ca3"),n("466d"),n("5319"),n("1276"),n("498a"),n("0643"),n("76d6"),n("2382"),n("4e3e"),n("a573"),n("9a9a"),n("159b"),n("ddb0"),n("f9d6")),s={QIMA_TRANSFORM:{pattern:/^(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?(\d{7})[\-—~～@#￥%…&!、\\/*,，.＝=各·\s]*?(转|套|拖)(?:各)?(\d+)(?:\+(\d+))?(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?$/,handler:function(t,e){var r=t[2],a=t[4],o=t[5],i=t[6],s=n("f9d6").generateTransformNumbers(r),u=[];return a?u.push({methodId:n("f9d6").METHOD_ID.BAMA_ZULIU,betNumbers:s.join(","),money:a}):o&&i&&(u.push({methodId:n("f9d6").METHOD_ID.BAMA_ZULIU,betNumbers:s.join(","),money:o}),u.push({methodId:n("f9d6").METHOD_ID.BAMA_ZUSAN,betNumbers:s.join(","),money:i})),u}},QIMA_ZUSAN:{pattern:/^(\d{7})[\-—~～@#￥%…&!、\\/*,，一－.＝=·\s]*组三?[\-—~～@#￥%…&!、\\/*,，。，一—－.＝=·\s]*([\d]+)?(元|块|米)?[\-—~～@#￥%…&!、。，一—－\\/*,，.＝=·\s]*$/,handler:function(t,e){return 7!==t[1].length?null:[{methodId:i["METHOD_ID"].QIMA_ZUSAN,betNumbers:t[1],money:t[2]||""}]}},QIMA_FANGDUI:{pattern:/^(\d{7})[\-—~～@#￥%…&!、\\/*,，一－.＝=·\s]*防对?([+＋\-—~～@#￥%…&!、：:\\/*,，。一—－~.＝=·吊赶打各]?(\d+))?$/,handler:function(t,e){return 7!==t[1].length?null:[{methodId:i["METHOD_ID"].QIMA_ZUSAN,betNumbers:t[1],money:t[2]||""}]}},MULTI_QIMA:{pattern:/^(\d{7}(?:[^0-9]+\d{7})*)([+＋\-—~～@#￥%…&!、：:\\/*,，。一—－~.＝=·吊赶打各]([一二三四五六七八九十百千万\d]+))?(?:防([一二三四五六七八九十百千万\d]+))?$/,handler:function(t,e){var r=n("f9d6"),a=r.chineseToNumber,o=e.split(i["BASE_PATTERNS"].SEPARATORS).filter(Boolean).filter((function(t){return 7===t.length})),s=t[2]?a(t[2]):"",u=t[3]?a(t[3]):"",l=[];return s&&l.push({methodId:i["METHOD_ID"].QIMA_ZULIU,betNumbers:o.join(","),money:s}),u&&l.push({methodId:i["METHOD_ID"].QIMA_ZUSAN,betNumbers:o[o.length-1],money:u}),console.log("七码 handler 返回:",l),l}},QIMA_ZULIU_ZUSAN_MULTI_MONEY_SUPER:{pattern:/^[－\-—~～@#￥%…&!、\\/*,一－。＝=·\s+吊赶打各]*(\d{7}(?:[－\-—~～@#￥%…&!、\\/*,一－。＝=·\s+吊赶打各]+\d{7})*)[\-—~～@#￥%…&!、\\/*,一－。＝=·\s+吊赶打各]+(\d+)[\-—~～@#￥%…&!、\\/*,一－。＝=·\s+吊赶打各]*[\+\-\\/*、.]+[\-—~～@#￥%…&!、\\/*,一－。＝=·\s+吊赶打各]*(\d+)(?:元)?$/,handler:function(t,e){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=n("f9d6"),o=a.SUPER_SEPARATORS,i=t[1].split(o).filter(Boolean),s=t[2],u=t[3];return r&&(/^\d{7}$/.test(s)||/^\d{7}$/.test(u))?null:[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:i.join(","),money:s},{methodId:n("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:i.join(","),money:u}]}},QIMA_ZULIU_ZU_SUPER:{pattern:/^(\d{7})组(\d+)$/,handler:function(t,e){var r=t[1],a=t[2];return[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:r,money:a}]}},QIMA_ZULIU_MULTI_SUPER:{pattern:/^(\d{7}(?:[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各]+\d{7})*)[\-—~～@一－。#￥%…&!、\\/*,，.＝=·\s+吊赶打各]+(\d+)$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=t[1].split(a).filter(Boolean),i=t[2];return[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:o.join(","),money:i}]}},QIMA_ZUSAN_ZU_SUPER:{pattern:/^(\d{7})组三(\d+)$/,handler:function(t,e){var r=t[1],a=t[2];return[{methodId:n("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:r,money:a}]}},QIMA_ZHIZU_MULTI_SUPER:{pattern:/^(\d{7}(?:[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各]+\d{7})*)[\-—~～@#￥%…&!、\\/*,，.＝=一－。·\s+吊赶打各]*(直组|组直|值组|组值)[\s]*各?(\d+)(?:元)?$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=t[1].split(a).filter(Boolean),i=t[3];return[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:o.join(","),money:i},{methodId:n("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:o.join(","),money:i}]}},QIMA_ZULIU_FANG:{pattern:/^(\d{7})[+＋\-—~～@#￥%…&!、：:\\/*,，。一—－~.＝=·吊赶打各](\d+)[+防](\d+)$/,handler:function(t,e){var n=t[1],r=t[2],a=t[3];return[{methodId:i["METHOD_ID"].QIMA_ZULIU,betNumbers:n,money:r},{methodId:i["METHOD_ID"].QIMA_ZUSAN,betNumbers:n,money:a}]}},QIMA_MULTI_ZHIZU_EACH_SUPER:{pattern:/^(直组|组直|值组|组值)各([0-9]+)[\s\-—~～@#￥%…&!、一－。 \\/*,，.＝=·吊赶打各\n]+([\d\s\-—~～@#￥%…&!一－。、\\/*,，.＝=·吊赶打各]+)$/i,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=t[2],i=t[3].replace(/\n/g," "),s=i.split(a).map((function(t){return t.trim()})).filter((function(t){return/^\d{7}$/.test(t)}));return[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:s.join(","),money:o},{methodId:n("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:s.join(","),money:o}]}},QIMA_MULTI_EACH_SUPER:{pattern:/^(\d{7}(?:[\-~～@#￥%…&!一－。、\\/*,，.＝=·\s+吊赶打各]+\d{7})*)各(\d+)$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=t[1].split(a).filter(Boolean);if(!o.length||o.some((function(t){return 7!==t.length})))return null;var i=t[2];return[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:o.join(","),money:i}]}},QIMA_SLASH_TWO_MONEY:{pattern:/^(?!.*[\r\n])(\d{7})[\-—~～@#￥%…&!、\\/*,，.。\u4e00\uff0d\uff1d=· 　\t]+(\d+)[+]+(\d+)$/,handler:function(t,e){var r=t[1],a=t[2],o=t[3];return[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:r,money:a},{methodId:n("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:r,money:o}]}},QIMA_MULTI_SUPER_SLASH_TWO_MONEY:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、＝=·吊赶打各]+)\/(\d+)\/(\d+)$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=t[1].split(a).map((function(t){return t.trim()})).filter((function(t){return 7===t.length}));if(!o.length)return[];var i=t[2],s=t[3];return[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:o.join(","),money:i},{methodId:n("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:o.join(","),money:s}]}},QIMA_UNIVERSAL_SUPER:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥一－—。%…&!、＝=·吊赶打各]+)[\-~～@#￥%…&!一－—。、\\/*,，.＝=·\s+吊赶打各]+([0-9]+)(元|米|块)?$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=t[1].split(a).map((function(t){return t.trim()})).filter(Boolean),s=t[2];return 1===i.length&&7===i[0].length?[{methodId:o.QIMA_ZULIU,betNumbers:i[0],money:s}]:i.length>=2&&i.every((function(t){return 7===t.length}))?[{methodId:o.QIMA_ZULIU,betNumbers:i.join(","),money:s}]:null}},QIMA_EACH_PLUS_SUPER:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥一－。%…&!、＝=·吊赶打各]+)各([0-9]+)\+([0-9]+)$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=t[1].split(a).map((function(t){return t.trim()})).filter(Boolean),s=t[2],u=t[3];return 1===i.length&&7===i[0].length?[{methodId:o.QIMA_ZULIU,betNumbers:i[0],money:s},{methodId:o.QIMA_ZUSAN,betNumbers:i[0],money:u}]:i.length>=2&&i.every((function(t){return 7===t.length}))?[{methodId:o.QIMA_ZULIU,betNumbers:i.join(","),money:s},{methodId:o.QIMA_ZUSAN,betNumbers:i.join(","),money:u}]:null}},QIMA_ZULIU_MONEY:{pattern:/^([0-9]{7,})\s+(\d+)(元|块|米)?$/,handler:function(t,e){var r=t[1],a=t[2];return r&&7===r.length?[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:r,money:a}]:null}},QIMA_DA_MONEY:{pattern:/^([0-9]{7})打(\d+)(元|块|米)?$/,handler:function(t,e){return[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:t[1],money:t[2]}]}},QIMA_MULTI_WITH_MONEY_SLASH_PLUS:{pattern:/^(\d{7}(?:[\s,，\-]+?\d{7})*)\/\s*(\d+)\+(\d+)$/,handler:function(t,e){var r=t[1].split(/[\s,，\-]+/).filter(Boolean),a=t[2],o=t[3];return[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:r.join(","),money:a},{methodId:n("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:r.join(","),money:o}]}},QIMA_MULTI_LINE_MONEY_PLUS:{pattern:/^((?:\d{7}[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+\d+\+\d+\s*\n?)+)$/m,handler:function(t,e){var r,i=n("f9d6"),s=i.METHOD_ID,u=e.split(/\n/).map((function(t){return t.trim()})).filter(Boolean),l=new Map,d=!0,c=Object(o["a"])(u);try{for(c.s();!(r=c.n()).done;){var m=r.value,h=m.match(/^(\d{7})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+(\d+)\+(\d+)$/);if(!h){d=!1;break}var I=h[1];if(7!==I.length){d=!1;break}var f=m.split(/[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+/)[0];if(7!==f.length||!/^\d{7}$/.test(f)){d=!1;break}var _="".concat(h[2],"+").concat(h[3]);l.has(_)||l.set(_,[]),l.get(_).push(I)}}catch(O){c.e(O)}finally{c.f()}if(!d||0===l.size)return null;var b,p=[],U=Object(o["a"])(l.entries());try{for(U.s();!(b=U.n()).done;){var N=Object(a["a"])(b.value,2),A=N[0],M=N[1],g=A.split("+"),D=Object(a["a"])(g,2),E=D[0],v=D[1];p.push({methodId:s.QIMA_ZULIU,betNumbers:M.join(","),money:E}),p.push({methodId:s.QIMA_ZUSAN,betNumbers:M.join(","),money:v})}}catch(O){U.e(O)}finally{U.f()}return p.length?p:null}},QIMA_MULTI_LINE_MONEY_PLUS_CN:{pattern:/^((?:\d{7}[，,、]?[一二三四五六七八九十百千万\d]+加[一二三四五六七八九十百千万\d]+\s*\n?)+)$/m,handler:function(t,e){var r,i=n("f9d6"),s=i.METHOD_ID,u=i.chineseToNumber,l=e.split(/\n/).map((function(t){return t.trim()})).filter(Boolean),d=new Map,c=Object(o["a"])(l);try{for(c.s();!(r=c.n()).done;){var m=r.value,h=m.match(/^(\d{7})[，,、]?([一二三四五六七八九十百千万\d]+)加([一二三四五六七八九十百千万\d]+)$/);if(h){var I=h[1],f=u(h[2]),_=u(h[3]),b="".concat(f,"+").concat(_);d.has(b)||d.set(b,[]),d.get(b).push(I)}}}catch(y){c.e(y)}finally{c.f()}var p,U=[],N=Object(o["a"])(d.entries());try{for(N.s();!(p=N.n()).done;){var A=Object(a["a"])(p.value,2),M=A[0],g=A[1],D=M.split("+"),E=Object(a["a"])(D,2),v=E[0],O=E[1];U.push({methodId:s.QIMA_ZULIU,betNumbers:g.join(","),money:v}),U.push({methodId:s.QIMA_ZUSAN,betNumbers:g.join(","),money:O})}}catch(y){N.e(y)}finally{N.f()}return U.length?U:null}},QIMA_MULTI_EACH_FANGDUI_SLASH:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥一－。%…&!、＝=·吊赶打各]+)[/\-~～@#￥%…&!、\\/*一－。,，.＝=·吊赶打各]+各防对([一二三四五六七八九十百千万\d]+)$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=r.chineseToNumber,s=t[1].split(a).map((function(t){return t.trim()})).filter((function(t){return 7===t.length})),u=i(t[2]);return s.length?[{methodId:o.QIMA_ZUSAN,betNumbers:s.join(","),money:u}]:null}},QIMA_MULTI_EACH_FANGDUI:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!一－。、＝=·吊赶打各]+)各防对([一二三四五六七八九十百千万\d]+)$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=r.chineseToNumber,s=t[1].split(a).map((function(t){return t.trim()})).filter((function(t){return 7===t.length})),u=i(t[2]);return s.length?[{methodId:o.QIMA_ZUSAN,betNumbers:s.join(","),money:u}]:null}},QIMA_SLASH_MONEY:{pattern:/^\s*(\d{7})\s*\/\s*([一二三四五六七八九十百千万\d]+)\s*$/,handler:function(t,e){var r=n("f9d6"),a=r.METHOD_ID,o=r.chineseToNumber,i=t[1],s=o(t[2]);return[{methodId:a.QIMA_ZULIU,betNumbers:i,money:s}]}},QIMA_ZULIU_ZUSAN_MONEY_PAIR:{pattern:/^(\d{7}[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*)组六[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*(\d+)[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*组三[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*(\d+)$/,handler:function(t,e){var n=t[1],r=t[2],a=t[3];return[{methodId:i["METHOD_ID"].QIMA_ZULIU,betNumbers:n,money:r},{methodId:i["METHOD_ID"].QIMA_ZUSAN,betNumbers:n,money:a}]}},QIMA_ZULIU:{pattern:/^(\d{7})组六?(\d+)[，,./\*\-\=一－。。·、\s]*$/,handler:function(t,e){var n=t[1];if(7!==n.length)return null;var r=t[2]||"";return[{methodId:i["METHOD_ID"].QIMA_ZULIU,betNumbers:n,money:r}]}},QIMA_ZULIU_ZUSAN_COMBO:{pattern:/^(\d{7}(?:[。.．、,，\s]+\d{7})*)\/(\d+)\+(\d+)$/,handler:function(t,e){var r=n("f9d6"),a=(r.SUPER_SEPARATORS,t[1].split(/[。.．、,，\s]+/).map((function(t){return t.trim()})).filter(Boolean)),o=t[2],i=t[3];return a.length&&a.every((function(t){return 7===t.length}))?[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:a.join(","),money:o},{methodId:n("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:a.join(","),money:i}]:null}},QIMA_SINGLE_LINE_ZULIU_ZUSAN_PLUS:{pattern:/^(\d{7})\s+(\d+)[+＋](\d+)$/,handler:function(t,e){return[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:t[1],money:t[2]},{methodId:n("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:t[1],money:t[3]}]}},QIMA_MULTI_LINE_ZULIU_MONEY_GROUP:{pattern:/^((?:\d{7}\s+\d+\s*\n?)+)$/m,handler:function(t,e){var r,i=n("f9d6"),s=i.METHOD_ID,u=e.split(/\n/).map((function(t){return t.trim()})).filter(Boolean),l=new Map,d=Object(o["a"])(u);try{for(d.s();!(r=d.n()).done;){var c=r.value,m=c.match(/^(\d{7})\s+(\d+)$/);if(m){var h=m[1],I=m[2];l.has(I)||l.set(I,[]),l.get(I).push(h)}}}catch(A){d.e(A)}finally{d.f()}var f,_=[],b=Object(o["a"])(l.entries());try{for(b.s();!(f=b.n()).done;){var p=Object(a["a"])(f.value,2),U=p[0],N=p[1];_.push({methodId:s.QIMA_ZULIU,betNumbers:N.join(","),money:U})}}catch(A){b.e(A)}finally{b.f()}return _.length?_:null}},QIMA_ZULIU_FANG_ZUSAN_PLUS:{pattern:/^(\d{7})[\-—~～@#￥%…&!、\\/*,，.＝一－。=·\s+吊赶打各]*(\d+)[\-—~～@#￥%…&!、一－。\\/*,，.＝=·\s+吊赶打各]*(防对|防)(\d+)$/,handler:function(t,e){return[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:t[1],money:t[2]},{methodId:n("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:t[1],money:t[3]}]}},QIMA_MULTI_LINE_ZULIU_FANG_ZUSAN_GROUP:{pattern:/^([\s\S]+)$/m,handler:function(t,e){var r,i=e.split(/[\r\n]+/).map((function(t){return t.trim()})).filter(Boolean),s=new Map,u=Object(o["a"])(i);try{for(u.s();!(r=u.n()).done;){var l=r.value,d=l.match(/^(\d{7})-?(\d+)防(\d+)$/);if(d){var c=d[1],m=d[2],h=d[3],I="".concat(m,"|").concat(h);s.has(I)||s.set(I,[]),s.get(I).push(c)}}}catch(E){u.e(E)}finally{u.f()}var f,_=[],b=Object(o["a"])(s.entries());try{for(b.s();!(f=b.n()).done;){var p=Object(a["a"])(f.value,2),U=p[0],N=p[1],A=U.split("|"),M=Object(a["a"])(A,2),g=M[0],D=M[1];_.push({methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:N.join(","),money:g}),_.push({methodId:n("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:N.join(","),money:D})}}catch(E){b.e(E)}finally{b.f()}return _.length?_:null}},QIMA_MULTI_TRANSFORM_EACH:{pattern:/^(\d{6}(?:[\-~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各]+\d{6})*)(转|转各|套|套各)(\d+)\+(\d+)$/,handler:function(t,e){var r,a=n("f9d6"),i=a.SUPER_SEPARATORS,s=a.METHOD_ID,u=a.generateTransformNumbers,l=t[1].split(i).filter(Boolean),d=t[3],c=t[4],m=[],h=Object(o["a"])(l);try{for(h.s();!(r=h.n()).done;){var I=r.value,f=u(I);m.push({methodId:s.BAMA_ZULIU,betNumbers:f.join(","),money:d}),m.push({methodId:s.BAMA_ZUSAN,betNumbers:f.join(","),money:c})}}catch(_){h.e(_)}finally{h.f()}return m}},QIMA_MULTI_LINE_TRANSFORM:{pattern:/^((?:(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?\d{7}转\d+(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?\s*\n?)+)$/m,handler:function(t,e){var r,i=n("f9d6"),s=i.METHOD_ID,u=e.split(/\n/).map((function(t){return t.trim()})).filter(Boolean),l=new Map,d=Object(o["a"])(u);try{for(d.s();!(r=d.n()).done;){var c=r.value,m=c.match(/^(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?(\d{7})转(\d+)(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?$/);if(m){var h=m[2],I=m[3];l.has(I)||l.set(I,[]),l.get(I).push(h)}}}catch(A){d.e(A)}finally{d.f()}var f,_=[],b=Object(o["a"])(l.entries());try{for(b.s();!(f=b.n()).done;){var p=Object(a["a"])(f.value,2),U=p[0],N=p[1];_.push({methodId:s.BAMA_ZULIU,betNumbers:N.join(","),money:U})}}catch(A){b.e(A)}finally{b.f()}return _.length?_:null}},QIMA_MULTI_COLON_EACH_SUPER:{pattern:/^(\d{7}(?::|：\d{7})*)各(\d+)$/,handler:function(t,e){var r=e.split(/:|：/).map((function(t){return t.trim()})).filter((function(t){return 7===t.length})),a=t[2];return r.length?[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:r.join(","),money:a}]:null}},QIMA_MULTI_YIGE_EACH_PLUS:{pattern:/^([\d一]+)一([\d一]+)一?各?(\d+)\+(\d+)$/,handler:function(t,e){var r=e.split("一").map((function(t){return t.replace(/[^\d]/g,"")})).filter((function(t){return 7===t.length})),a=t[3],o=t[4];return r.length?[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:r.join(","),money:a},{methodId:n("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:r.join(","),money:o}]:null}},QIMA_DIAN_XMA_ZULIU_FANG_ZUSAN:{pattern:/^(\d{7})[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]+7码(\d+)防(\d+)$/,handler:function(t,e){return[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:t[1],money:t[2]},{methodId:n("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:t[1],money:t[3]}]}},QIMA_MULTI_NUMBERS_EACH_FANG:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥一－。%…&!、＝=·吊赶打各]+)各(\d+)(防|防对|放对)(\d+)$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=t[1].split(a).map((function(t){return t.trim()})).filter((function(t){return 7===t.length}));if(!i.length)return null;var s=t[2],u=t[4];return[{methodId:o.QIMA_ZULIU,betNumbers:i.join(","),money:s},{methodId:o.QIMA_ZUSAN,betNumbers:i.join(","),money:u}]}},QIMA_YI_YI_ONE_MONEY:{pattern:/^(\d{7})一(\d{7})一个(\d+)(元|块|米)?$/,handler:function(t,e){var r=t[1],a=t[2],o=t[3];return 7!==r.length||7!==a.length?null:[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:r+","+a,money:o}]}},QIMA_GE_MONEY:{pattern:/^(\d{7})[，,、\s]*个(\d+)(元|块|米)?$/,handler:function(t,e){var r=t[1],a=t[2];return 7!==r.length?null:[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:r,money:a}]}},MULTI_LINE_QIMA_MONEY_GROUP:{pattern:/^((?:\d{7}[\-—~～@#￥%…&!、\\/*,一－。.＝=·吊赶打各\s]+\d+\s*\n?)+)$/m,handler:function(t,e){var r,i=n("f9d6"),s=i.METHOD_ID,u=e.split(/\n/).map((function(t){return t.trim()})).filter(Boolean),l=new Map,d=Object(o["a"])(u);try{for(d.s();!(r=d.n()).done;){var c=r.value,m=c.match(/^(\d{7})[\-—~～@#￥%…&!、\\/*,一－。.＝=·吊赶打各\s]+([0-9]+)$/);if(m){var h=m[1],I=m[2];l.has(I)||l.set(I,[]),l.get(I).push(h)}}}catch(A){d.e(A)}finally{d.f()}var f,_=[],b=Object(o["a"])(l.entries());try{for(b.s();!(f=b.n()).done;){var p=Object(a["a"])(f.value,2),U=p[0],N=p[1];_.push({methodId:s.QIMA_ZULIU,betNumbers:N.join(","),money:U})}}catch(A){b.e(A)}finally{b.f()}return _.length?_:null}},QIMA_MULTI_LINE_ZULIU_MONEY_SUPER:{pattern:/^([\s\S]+)$/m,handler:function(t,e){var r,i=n("f9d6"),s=i.METHOD_ID,u=e.split(/\n/).map((function(t){return t.trim()})).filter(Boolean),l=new Map,d=/^(\d{7})[\-—~～@#￥%…&!、一－。\\/*,，.＝=·\s]+(\d+)(元|米|块)?([，,./\\*\-\=。·、\s]*)?$/,c=[],m=Object(o["a"])(u);try{for(m.s();!(r=m.n()).done;){var h=r.value,I=h.match(d);if(!I)return console.log('【QIMA_MULTI_LINE_ZULIU_MONEY_SUPER】行 "'.concat(h,'" 不匹配七码格式，返回null')),null;if(I[1].length===I[2].length)return console.log('【QIMA_MULTI_LINE_ZULIU_MONEY_SUPER】行 "'.concat(h,'" 号码和金额长度相同，可能误匹配，返回null')),null;c.push({line:h,match:I})}}catch(v){m.e(v)}finally{m.f()}console.log("【QIMA_MULTI_LINE_ZULIU_MONEY_SUPER】所有 ".concat(c.length," 行都是七码格式，继续处理"));for(var f=0,_=c;f<_.length;f++){var b=_[f].match,p=b[1],U=b[2];l.has(U)||l.set(U,[]),l.get(U).push(p)}var N,A=[],M=Object(o["a"])(l.entries());try{for(M.s();!(N=M.n()).done;){var g=Object(a["a"])(N.value,2),D=g[0],E=g[1];A.push({methodId:s.QIMA_ZULIU,betNumbers:E.join(","),money:D})}}catch(v){M.e(v)}finally{M.f()}return console.log("【QIMA_MULTI_LINE_ZULIU_MONEY_SUPER】处理完成，返回 ".concat(A.length," 个结果组")),A.length?A:null}},QIMA_MULTI_FANGDUI_SUPER:{pattern:/^([0-9]{7}(?:[\-—–―－﹣﹘⸺⸻~～@#￥%…&!、\\/*,，.一－。＝=·\s+吊赶打各]+[0-9]{7})+)[\-—–―－﹣﹘⸺⸻~～@#￥%…&!、\\/*,，.一－。＝=·\s+吊赶打各]*(防对|防|组三)([0-9]+)$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=t[1].split(a).map((function(t){return t.trim()})).filter((function(t){return 7===t.length})),s=t[3];return i.length<2?null:[{methodId:o.QIMA_ZUSAN,betNumbers:i.join(","),money:s}]}},QIMA_MULTI_LINE_ZULIU_ZUSAN_KEYWORD:{pattern:/^([0-9]{7}(组六|组三)\d+(元|米|块)?[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各\s]*\n?)+$/m,handler:function(t,e){var n,r=e.split(/\r?\n/).map((function(t){return t.trim()})).filter(Boolean),s=new Map,u=Object(o["a"])(r);try{for(u.s();!(n=u.n()).done;){var l=n.value,d=l.match(/^([0-9]{7})(组六|组三)(\d+)(元|米|块)?[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各\s]*$/);if(d&&7===d[1].length){var c=d[1],m=d[2],h=d[3],I=m+"_"+h;s.has(I)||s.set(I,[]),s.get(I).push(c)}}}catch(E){u.e(E)}finally{u.f()}var f,_=[],b=Object(o["a"])(s.entries());try{for(b.s();!(f=b.n()).done;){var p=Object(a["a"])(f.value,2),U=p[0],N=p[1],A=U.split("_"),M=Object(a["a"])(A,2),g=M[0],D=M[1];_.push({methodId:"组六"===g?i["METHOD_ID"].QIMA_ZULIU:i["METHOD_ID"].QIMA_ZUSAN,betNumbers:N.join(","),money:D})}}catch(E){b.e(E)}finally{b.f()}return _.length?_:null}},QIMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER:{pattern:/^([\s\S]+)$/m,handler:function(t,e){var r=/(福彩|福|3D|3d)/.test(e),i=/(体彩|体|排|排三)/.test(e);if(r||i)return null;var s,u=e.split(/\r?\n/).map((function(t){return t.trim()})).filter(Boolean),l=new Map,d=new Map,c=/^([0-9]{7})[\-—~～@#￥%…&!、\\/*,一－–.。＝=·\s\/]+(\d+)\++(\d+)[元米块]*$/,m=/^([0-9]{7})[\-—~～@#￥%…&!、\\/*,一－–.。＝=·\s\/]+(\d+)[元米块]*$/,h=[],I=Object(o["a"])(u);try{for(I.s();!(s=I.n()).done;){var f=s.value,_=f.match(c);if(_)h.push({line:f,type:"zuliuZusan",match:_});else{if(_=f.match(m),!_)return console.log('【QIMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】行 "'.concat(f,'" 不匹配七码格式，返回null')),null;h.push({line:f,type:"zuliu",match:_})}}}catch(G){I.e(G)}finally{I.f()}console.log("【QIMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】所有 ".concat(h.length," 行都是七码格式，继续处理"));for(var b=0,p=h;b<p.length;b++){var U=p[b],N=U.match,A=U.type;if("zuliuZusan"===A){var M=N[1],g=N[2],D=N[3];l.has(g)||l.set(g,[]),l.get(g).push(M),d.has(D)||d.set(D,[]),d.get(D).push(M)}else if("zuliu"===A){var E=N[1],v=N[2];l.has(v)||l.set(v,[]),l.get(v).push(E)}}var O,y=[],S=Object(o["a"])(l.entries());try{for(S.s();!(O=S.n()).done;){var T=Object(a["a"])(O.value,2),L=T[0],j=T[1];y.push({methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:j.join(","),money:L})}}catch(G){S.e(G)}finally{S.f()}var R,Z=Object(o["a"])(d.entries());try{for(Z.s();!(R=Z.n()).done;){var H=Object(a["a"])(R.value,2),$=H[0],P=H[1];y.push({methodId:n("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:P.join(","),money:$})}}catch(G){Z.e(G)}finally{Z.f()}return console.log("【QIMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】处理完成，返回 ".concat(y.length," 个结果组")),y.length?y:null}},QIMA_MONEY_UNIT_FANGDUI_SUPER:{pattern:/^(\d{7})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:元|米|块)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(防|防对|组三)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*$/,handler:function(t,e){var n=t[1],r=t[2];t[3];return 7!==n.length?null:[{methodId:i["METHOD_ID"].QIMA_ZUSAN,betNumbers:n,money:r}]}},LIUMA_TO_QIMA_MULTI_TRANSFORM:{pattern:/^((?:\d{6}[\-—~～@#￥%…&!、\\/*,，.＝=·\s]+){1,}\d{6})(各)?(转|套)[\s]*([0-9]+)\+([0-9]+)$/,handler:function(t,e){var a=n("f9d6"),o=a.SUPER_SEPARATORS,i=t[1],s=t[4],u=t[5],l=i.split(o).filter((function(t){return 6===t.length})),d=[];return l.forEach((function(t){var e=t.split(""),n="0123456789".split("").filter((function(t){return!e.includes(t)})),a=n.map((function(e){return t+e})).sort(),o=a.filter((function(t){return!d.includes(t)}));d.push.apply(d,Object(r["a"])(o))})),[{methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:d.join(","),money:s},{methodId:n("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:d.join(","),money:u}]}}};Object.keys(s).forEach((function(t){var e=s[t].pattern;"string"===typeof e&&e.endsWith("$")?s[t].pattern=e.replace(/\$$/,"[，,./*-=。·、s]*$"):e instanceof RegExp&&e.source.endsWith("$")&&(s[t].pattern=new RegExp(e.source.replace(/\$$/,"[，,./*-=。·、s]*$")))}))},3506:function(t,e,n){"use strict";n.r(e),n.d(e,"NINE_CODE_PATTERNS",(function(){return i}));var r=n("3835"),a=n("b85c"),o=(n("99af"),n("4de4"),n("a15b"),n("d81d"),n("14d9"),n("4ec9"),n("b64b"),n("d3b7"),n("4d63"),n("c607"),n("ac1f"),n("2c3e"),n("00b4"),n("25f0"),n("8a79"),n("3ca3"),n("466d"),n("5319"),n("1276"),n("498a"),n("0643"),n("76d6"),n("2382"),n("4e3e"),n("a573"),n("9a9a"),n("159b"),n("ddb0"),n("f9d6")),i={JIUMA_TRANSFORM:{pattern:/^(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?(\d{9})(转|套|拖)(?:各)?(\d+)(?:\+(\d+))?(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?$/,handler:function(t,e){var n=t[2],r=t[3],a=t[4],i=[];return a?(i.push({methodId:o["METHOD_ID"].JIUMA_ZULIU,betNumbers:n,money:r}),i.push({methodId:o["METHOD_ID"].JIUMA_ZUSAN,betNumbers:n,money:a})):i.push({methodId:o["METHOD_ID"].JIUMA_ZULIU,betNumbers:n,money:r}),console.log("九码 handler 返回:",i),i}},JIUMA_ZUSAN:{pattern:/^(\d{9})[\-—~～@#￥%…&!、\\/*,，一－.＝=·\s]*组三?[\-—~～@#￥%…&!、\\/*,，。，—一－.＝=·\s]*([\d]+)?(元|块|米)?[\-—~～@#￥%…&!、。，—一－\\/*,，.＝=·\s]*$/,handler:function(t,e){return 9!==t[1].length?null:[{methodId:o["METHOD_ID"].JIUMA_ZUSAN,betNumbers:t[1],money:t[2]||""}]}},JIUMA_FANGDUI:{pattern:/^(\d{9})防对?(?:-?(\d+))?$/,handler:function(t,e){return 9!==t[1].length?null:[{methodId:o["METHOD_ID"].JIUMA_ZUSAN,betNumbers:t[1],money:t[2]||""}]}},MULTI_JIUMA:{pattern:/^(\d{9}(?:[^0-9]+\d{9})*)(?:各([一二三四五六七八九十百千万\d]+))?(?:防([一二三四五六七八九十百千万\d]+))?$/,handler:function(t,e){var r=n("f9d6"),a=r.chineseToNumber,i=e.split(o["BASE_PATTERNS"].SEPARATORS).filter(Boolean).filter((function(t){return 9===t.length}));if(!i.length||!i.every((function(t){return 9===t.length})))return null;var s=t[2]?a(t[2]):"",u=t[3]?a(t[3]):"",l=[];return s&&l.push({methodId:o["METHOD_ID"].JIUMA_ZULIU,betNumbers:i.join(","),money:s}),u&&l.push({methodId:o["METHOD_ID"].JIUMA_ZUSAN,betNumbers:i[i.length-1],money:u}),l.length?l:null}},JIUMA_ZULIU_ZUSAN_MULTI_MONEY_SUPER:{pattern:/^[－\-—~～@#￥%…&!、\\/*,一－。＝=·\s+吊赶打各]*(\d{9}(?:[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]+\d{9})*)[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]+(\d+)[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]*[+](\d+)(?:元)?$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=t[1].split(a).filter(Boolean),i=t[2],s=t[3];return[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:o.join(","),money:i},{methodId:n("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:o.join(","),money:s}]}},JIUMA_ZULIU_ZU_SUPER:{pattern:/^(\d{9})组(\d+)$/,handler:function(t,e){var r=t[1],a=t[2];return[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:r,money:a}]}},JIUMA_ZULIU_MULTI_SUPER:{pattern:/^(\d{9}(?:[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]+\d{9})*)[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]+(\d+)$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=t[1].split(a).filter(Boolean),i=t[2];return[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:o.join(","),money:i}]}},JIUMA_ZUSAN_ZU_SUPER:{pattern:/^(\d{9})组三(\d+)$/,handler:function(t,e){var r=t[1],a=t[2];return[{methodId:n("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:r,money:a}]}},JIUMA_ZHIZU_MULTI_SUPER:{pattern:/^(\d{9}(?:[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]+\d{9})*)[\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]*(直组|组直|值组|组值)[\s]*各?(\d+)(?:元)?$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=t[1].split(a).filter(Boolean),i=t[3];return[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:o.join(","),money:i},{methodId:n("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:o.join(","),money:i}]}},JIUMA_ZULIU_FANG:{pattern:/^(\d{9})-(\d+)防(\d+)$/,handler:function(t,e){var n=t[1],r=t[2],a=t[3];return[{methodId:o["METHOD_ID"].JIUMA_ZULIU,betNumbers:n,money:r},{methodId:o["METHOD_ID"].JIUMA_ZUSAN,betNumbers:n,money:a}]}},JIUMA_MULTI_ZHIZU_EACH_SUPER:{pattern:/^(直组|组直|值组|组值)各([0-9]+)[\s\+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各\n]+([\d\s\+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]+)$/i,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=t[2],i=t[3].replace(/\n/g," "),s=i.split(a).map((function(t){return t.trim()})).filter((function(t){return/^\d{9}$/.test(t)}));return[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:s.join(","),money:o},{methodId:n("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:s.join(","),money:o}]}},JIUMA_MULTI_EACH_SUPER:{pattern:/^(\d{9}(?:[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]+\d{9})*)各(\d+)$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=t[1].split(a).filter(Boolean);if(!o.length||o.some((function(t){return 9!==t.length})))return[];var i=t[2];return[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:o.join(","),money:i}]}},JIUMA_SLASH_TWO_MONEY:{pattern:/^(?!.*[\r\n])(\d{9})[\-—~～@#￥%…&!、\\/*,，.。\u4e00\uff0d\uff1d=· 　\t]+(\d+)[+]+(\d+)$/,handler:function(t,e){var r=t[1],a=t[2],o=t[3];return[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:r,money:a},{methodId:n("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:r,money:o}]}},JIUMA_MULTI_SUPER_SLASH_TWO_MONEY:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、＝一－。=·吊赶打各]+)\/(\d+)\/(\d+)$/,handler:function(t,e){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=n("f9d6"),o=a.SUPER_SEPARATORS,i=t[1].split(o).map((function(t){return t.trim()})).filter((function(t){return 9===t.length}));if(!i.length)return[];var s=t[2],u=t[3];return r&&(/^\d{9}$/.test(s)||/^\d{9}$/.test(u))?null:[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:i.join(","),money:s},{methodId:n("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:i.join(","),money:u}]}},JIUMA_UNIVERSAL_SUPER:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～一－—。@#￥%…&!、＝=·吊赶打各]+)[\-~～@#￥%…&!、\\/*,，一－—。.＝=·\s+吊赶打各]+([0-9]+)(元|米|块)?$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=t[1].split(a).map((function(t){return t.trim()})).filter(Boolean),s=t[2];return 1===i.length&&9===i[0].length?[{methodId:o.JIUMA_ZULIU,betNumbers:i[0],money:s}]:i.length>=2&&i.every((function(t){return 9===t.length}))?[{methodId:o.JIUMA_ZULIU,betNumbers:i.join(","),money:s}]:null}},JIUMA_EACH_PLUS_SUPER:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!一－。、＝=·吊赶打各]+)各([0-9]+)\+([0-9]+)$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=t[1].split(a).map((function(t){return t.trim()})).filter(Boolean),s=t[2],u=t[3];return 1===i.length&&9===i[0].length?[{methodId:o.JIUMA_ZULIU,betNumbers:i[0],money:s},{methodId:o.JIUMA_ZUSAN,betNumbers:i[0],money:u}]:i.length>=2&&i.every((function(t){return 9===t.length}))?[{methodId:o.JIUMA_ZULIU,betNumbers:i.join(","),money:s},{methodId:o.JIUMA_ZUSAN,betNumbers:i.join(","),money:u}]:null}},JIUMA_UNIVERSAL_SUPER_NEW:{pattern:/^([0-9]{9,})[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]+(\d+)(元|块|米)?$/,handler:function(t,e){var r=t[1],a=t[2];return r&&9===r.length?[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:r,money:a}]:null}},JIUMA_DA_MONEY:{pattern:/^([0-9]{9})打(\d+)(元|块|米)?$/,handler:function(t,e){return[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:t[1],money:t[2]}]}},JIUMA_MULTI_WITH_MONEY_SLASH_PLUS:{pattern:/^(\d{9}(?:[\s,，\-]+?\d{9})*)\/\s*(\d+)\+(\d+)$/,handler:function(t,e){var r=t[1].split(/[\s,，\-]+/).filter(Boolean),a=t[2],o=t[3];return[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:r.join(","),money:a},{methodId:n("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:r.join(","),money:o}]}},JIUMA_MULTI_LINE_MONEY_PLUS:{pattern:/^((?:\d{9}[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+\d+\+\d+\s*\n?)+)$/m,handler:function(t,e){var o,i=n("f9d6"),s=i.METHOD_ID,u=e.split(/\n/).map((function(t){return t.trim()})).filter(Boolean),l=new Map,d=!0,c=Object(a["a"])(u);try{for(c.s();!(o=c.n()).done;){var m=o.value,h=m.match(/^(\d{9})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+(\d+)\+(\d+)$/);if(!h){d=!1;break}var I=h[1];if(9!==I.length){d=!1;break}var f=m.split(/[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+/)[0];if(9!==f.length||!/^\d{9}$/.test(f)){d=!1;break}var _="".concat(h[2],"+").concat(h[3]);l.has(_)||l.set(_,[]),l.get(_).push(I)}}catch(O){c.e(O)}finally{c.f()}if(!d||0===l.size)return null;var b,p=[],U=Object(a["a"])(l.entries());try{for(U.s();!(b=U.n()).done;){var N=Object(r["a"])(b.value,2),A=N[0],M=N[1],g=A.split("+"),D=Object(r["a"])(g,2),E=D[0],v=D[1];p.push({methodId:s.JIUMA_ZULIU,betNumbers:M.join(","),money:E}),p.push({methodId:s.JIUMA_ZUSAN,betNumbers:M.join(","),money:v})}}catch(O){U.e(O)}finally{U.f()}return p.length?p:null}},JIUMA_MULTI_LINE_MONEY_PLUS_CN:{pattern:/^((?:\d{9}[，,、]?[一二三四五六七八九十百千万\d]+加[一二三四五六七八九十百千万\d]+\s*\n?)+)$/m,handler:function(t,e){var o,i=n("f9d6"),s=i.METHOD_ID,u=i.chineseToNumber,l=e.split(/\n/).map((function(t){return t.trim()})).filter(Boolean),d=new Map,c=Object(a["a"])(l);try{for(c.s();!(o=c.n()).done;){var m=o.value,h=m.match(/^(\d{9})[，,、]?([一二三四五六七八九十百千万\d]+)加([一二三四五六七八九十百千万\d]+)$/);if(h){var I=h[1],f=u(h[2]),_=u(h[3]),b="".concat(f,"+").concat(_);d.has(b)||d.set(b,[]),d.get(b).push(I)}}}catch(y){c.e(y)}finally{c.f()}var p,U=[],N=Object(a["a"])(d.entries());try{for(N.s();!(p=N.n()).done;){var A=Object(r["a"])(p.value,2),M=A[0],g=A[1],D=M.split("+"),E=Object(r["a"])(D,2),v=E[0],O=E[1];U.push({methodId:s.JIUMA_ZULIU,betNumbers:g.join(","),money:v}),U.push({methodId:s.JIUMA_ZUSAN,betNumbers:g.join(","),money:O})}}catch(y){N.e(y)}finally{N.f()}return U.length?U:null}},JIUMA_MULTI_EACH_FANGDUI_SLASH:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、一－。＝=·吊赶打各]+)[/\-~～@#￥%…&!、\\/*,，.一－。＝=·吊赶打各]+各防对([一二三四五六七八九十百千万\d]+)$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=r.chineseToNumber,s=t[1].split(a).map((function(t){return t.trim()})).filter((function(t){return 9===t.length})),u=i(t[2]);return s.length?[{methodId:o.JIUMA_ZUSAN,betNumbers:s.join(","),money:u}]:null}},JIUMA_MULTI_EACH_FANGDUI:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&一－。!、＝=·吊赶打各]+)各防对([一二三四五六七八九十百千万\d]+)$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=r.chineseToNumber,s=t[1].split(a).map((function(t){return t.trim()})).filter((function(t){return 9===t.length})),u=i(t[2]);return s.length?[{methodId:o.JIUMA_ZUSAN,betNumbers:s.join(","),money:u}]:null}},JIUMA_SLASH_MONEY:{pattern:/^\s*(\d{9})\s*\/\s*([一二三四五六七八九十百千万\d]+)\s*$/,handler:function(t,e){var r=n("f9d6"),a=r.METHOD_ID,o=r.chineseToNumber,i=t[1],s=o(t[2]);return[{methodId:a.JIUMA_ZULIU,betNumbers:i,money:s}]}},JIUMA_ZULIU_ZUSAN_MONEY_PAIR:{pattern:/^(\d{9}[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*)组六[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*(\d+)[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*组三[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*(\d+)$/,handler:function(t,e){var n=t[1],r=t[2],a=t[3];return[{methodId:o["METHOD_ID"].JIUMA_ZULIU,betNumbers:n,money:r},{methodId:o["METHOD_ID"].JIUMA_ZUSAN,betNumbers:n,money:a}]}},JIUMA_ZULIU:{pattern:/^(\d{9})组六?(\d+)[，,./\*\-一－。\=。·、\s]*$/,handler:function(t,e){var n=t[1];if(9!==n.length)return null;var r=t[2]||"";return[{methodId:o["METHOD_ID"].JIUMA_ZULIU,betNumbers:n,money:r}]}},JIUMA_ZULIU_ZUSAN_COMBO:{pattern:/^(\d{9}(?:[+＋\\-—~～@#￥%…&!、\\/*,，。一—~.＝=·吊赶打各\s]+\d{9})*)\/(\d+)\+(\d+)$/,handler:function(t,e){var r=n("f9d6"),a=(r.SUPER_SEPARATORS,t[1].split(/[。.．、,，\s]+/).map((function(t){return t.trim()})).filter(Boolean)),o=t[2],i=t[3];return a.length&&a.every((function(t){return 9===t.length}))?[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:a.join(","),money:o},{methodId:n("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:a.join(","),money:i}]:null}},JIUMA_SINGLE_LINE_ZULIU_ZUSAN_PLUS:{pattern:/^(\d{9})\s+(\d+)[+＋\\-—~～@#￥%…&!、\\/*,，。一—~.＝=·吊赶打各](\d+)$/,handler:function(t,e){return[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:t[1],money:t[2]},{methodId:n("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:t[1],money:t[3]}]}},JIUMA_MULTI_LINE_ZULIU_ZUSAN_PLUS:{pattern:/^([\s\S]+)$/m,handler:function(t,e){var o,i=n("f9d6"),s=i.SUPER_SEPARATORS,u=e.split(/\r?\n/).map((function(t){return t.trim()})).filter(Boolean),l=new Map,d=new Map,c=Object(a["a"])(u);try{for(c.s();!(o=c.n()).done;){var m=o.value,h=m.match(new RegExp("^(\\d{9})".concat(s.source,"(\\d+)\\+(\\d+)$")));if(h){var I=h[1],f=h[2],_=h[3],b=f;l.has(b)||l.set(b,[]),l.get(b).push(I);var p=_;d.has(p)||d.set(p,[]),d.get(p).push(I)}else{var U=m.match(new RegExp("^(\\d{9})".concat(s.source,"(\\d+)$")));if(U){var N=U[1],A=U[2];l.has(A)||l.set(A,[]),l.get(A).push(N)}}}}catch(R){c.e(R)}finally{c.f()}var M,g=[],D=Object(a["a"])(l.entries());try{for(D.s();!(M=D.n()).done;){var E=Object(r["a"])(M.value,2),v=E[0],O=E[1];g.push({methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:O.join(","),money:v})}}catch(R){D.e(R)}finally{D.f()}var y,S=Object(a["a"])(d.entries());try{for(S.s();!(y=S.n()).done;){var T=Object(r["a"])(y.value,2),L=T[0],j=T[1];g.push({methodId:n("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:j.join(","),money:L})}}catch(R){S.e(R)}finally{S.f()}return g.length?g:null}},JIUMA_MULTI_LINE_ZULIU_PLUS:{pattern:/^((?:\d{9}[+＋\\-—~～@#￥%…&!、\\/*,，。一—~.＝=·吊赶打各]\d+\s*\n?)+)$/m,handler:function(t,e){var o,i=n("f9d6"),s=i.METHOD_ID,u=e.split(/\n/).map((function(t){return t.trim()})).filter(Boolean),l=new Map,d=[],c=Object(a["a"])(u);try{for(c.s();!(o=c.n()).done;){var m=o.value,h=m.match(/^(\d{9})[+＋\\-—~～@#￥%…&!、\\/*,，。一—－~.＝=·吊赶打各]+(\d+)$/);if(h){var I=h[1],f=h[2];l.has(f)||l.set(f,[]),l.get(f).push(I)}else d.push(m)}}catch(M){c.e(M)}finally{c.f()}var _,b=[],p=Object(a["a"])(l.entries());try{for(p.s();!(_=p.n()).done;){var U=Object(r["a"])(_.value,2),N=U[0],A=U[1];b.push({methodId:s.JIUMA_ZULIU,betNumbers:A.join(","),money:N})}}catch(M){p.e(M)}finally{p.f()}return b}},JIUMA_ZULIU_FANG_ZUSAN_PLUS:{pattern:/^(\d{9})[\-—~～@#￥%…&!、\\/*,，.一－。＝=·吊赶打各]*(\d+)[\-—~～@#￥%…&!、\\/*,，.一－。＝=·吊赶打各]*(防对|防)(\d+)$/,handler:function(t,e){return[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:t[1],money:t[2]},{methodId:n("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:t[1],money:t[3]}]}},JIUMA_MULTI_LINE_ZULIU_FANG_ZUSAN_GROUP:{pattern:/^([\s\S]+)$/m,handler:function(t,e){var o,i=e.split(/[\r\n]+/).map((function(t){return t.trim()})).filter(Boolean),s=new Map,u=Object(a["a"])(i);try{for(u.s();!(o=u.n()).done;){var l=o.value,d=l.match(/^(\d{9})-?(\d+)防(\d+)$/);if(d){var c=d[1],m=d[2],h=d[3],I="".concat(m,"|").concat(h);s.has(I)||s.set(I,[]),s.get(I).push(c)}}}catch(E){u.e(E)}finally{u.f()}var f,_=[],b=Object(a["a"])(s.entries());try{for(b.s();!(f=b.n()).done;){var p=Object(r["a"])(f.value,2),U=p[0],N=p[1],A=U.split("|"),M=Object(r["a"])(A,2),g=M[0],D=M[1];_.push({methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:N.join(","),money:g}),_.push({methodId:n("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:N.join(","),money:D})}}catch(E){b.e(E)}finally{b.f()}return _.length?_:null}},JIUMA_MULTI_TRANSFORM_EACH:{pattern:/^(\d{8}(?:[\-~～@#￥%…&!、\\/*,一－。，.＝=·\s+吊赶打各]+\d{8})*)(转|套|拖)各(\d+)\+(\d+)$/,handler:function(t,e){var r,o=n("f9d6"),i=o.SUPER_SEPARATORS,s=o.METHOD_ID,u=o.generateTransformNumbers,l=t[1].split(i).filter(Boolean),d=t[3],c=t[4],m=[],h=Object(a["a"])(l);try{for(h.s();!(r=h.n()).done;){var I=r.value,f=u(I);m.push({methodId:s.JIUMA_ZULIU,betNumbers:f.join(","),money:d}),m.push({methodId:s.JIUMA_ZUSAN,betNumbers:f.join(","),money:c})}}catch(_){h.e(_)}finally{h.f()}return m}},JIUMA_MULTI_LINE_TRANSFORM:{pattern:/^((?:(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?\d{9}(转|套|拖)\d+(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?\s*\n?)+)$/m,handler:function(t,e){var o,i=n("f9d6"),s=i.METHOD_ID,u=e.split(/\n/).map((function(t){return t.trim()})).filter(Boolean),l=new Map,d=Object(a["a"])(u);try{for(d.s();!(o=d.n()).done;){var c=o.value,m=c.match(/^(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?(\d{9})(转|套|拖)(\d+)(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?$/);if(m){var h=m[2],I=m[3];l.has(I)||l.set(I,[]),l.get(I).push(h)}}}catch(A){d.e(A)}finally{d.f()}var f,_=[],b=Object(a["a"])(l.entries());try{for(b.s();!(f=b.n()).done;){var p=Object(r["a"])(f.value,2),U=p[0],N=p[1];_.push({methodId:s.JIUMA_ZULIU,betNumbers:N.join(","),money:U})}}catch(A){b.e(A)}finally{b.f()}return _.length?_:null}},JIUMA_MULTI_COLON_EACH_SUPER:{pattern:/^(\d{9}(?::|：\d{9})*)各(\d+)$/,handler:function(t,e){var r=e.split(/:|：/).map((function(t){return t.trim()})).filter((function(t){return 9===t.length})),a=t[2];return r.length?[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:r.join(","),money:a}]:null}},JIUMA_MULTI_YIGE_EACH_PLUS:{pattern:/^([\d一]+)一([\d一]+)一?各?(\d+)\+(\d+)$/,handler:function(t,e){var r=e.split("一").map((function(t){return t.replace(/[^\d]/g,"")})).filter((function(t){return 9===t.length})),a=t[3],o=t[4];return r.length?[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:r.join(","),money:a},{methodId:n("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:r.join(","),money:o}]:null}},JIUMA_DIAN_XMA_ZULIU_FANG_ZUSAN:{pattern:/^(\d{9})[。.．、,，\s]+9码(\d+)防(\d+)$/,handler:function(t,e){return[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:t[1],money:t[2]},{methodId:n("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:t[1],money:t[3]}]}},JIUMA_MULTI_LINE_ZULIU_MONEY_GROUP:{pattern:/^((?:\d{9}\s+\d+\s*\n?)+)$/m,handler:function(t,e){var o,i=n("f9d6"),s=i.METHOD_ID,u=e.split(/\n/).map((function(t){return t.trim()})).filter(Boolean),l=new Map,d=Object(a["a"])(u);try{for(d.s();!(o=d.n()).done;){var c=o.value,m=c.match(/^(\d{9})\s+(\d+)$/);if(m){var h=m[1],I=m[2];l.has(I)||l.set(I,[]),l.get(I).push(h)}}}catch(A){d.e(A)}finally{d.f()}var f,_=[],b=Object(a["a"])(l.entries());try{for(b.s();!(f=b.n()).done;){var p=Object(r["a"])(f.value,2),U=p[0],N=p[1];_.push({methodId:s.JIUMA_ZULIU,betNumbers:N.join(","),money:U})}}catch(A){b.e(A)}finally{b.f()}return _.length?_:null}},JIUMA_MULTI_NUMBERS_EACH_FANG:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、一－。＝=·吊赶打各]+)各(\d+)(防|防对|放对)(\d+)$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=t[1].split(a).map((function(t){return t.trim()})).filter((function(t){return 9===t.length}));if(!i.length)return null;var s=t[2],u=t[4];return[{methodId:o.JIUMA_ZULIU,betNumbers:i.join(","),money:s},{methodId:o.JIUMA_ZUSAN,betNumbers:i.join(","),money:u}]}},JIUMA_YI_YI_ONE_MONEY:{pattern:/^(\d{9})一(\d{9})一个(\d+)(元|块|米)?$/,handler:function(t,e){var r=t[1],a=t[2],o=t[3];return 9!==r.length||9!==a.length?null:[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:r+","+a,money:o}]}},JIUMA_GE_MONEY:{pattern:/^(\d{9})[，,、\s]*个(\d+)(元|块|米)?$/,handler:function(t,e){var r=t[1],a=t[2];return 9!==r.length?null:[{methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:r,money:a}]}},MULTI_LINE_JIUMA_MONEY_GROUP:{pattern:/^((?:\d{9}[\-—~～@#￥%…&!、\\/*,一－。.＝=·吊赶打各\s]+\d+\s*\n?)+)$/m,handler:function(t,e){var o,i=n("f9d6"),s=i.METHOD_ID,u=e.split(/\n/).map((function(t){return t.trim()})).filter(Boolean),l=new Map,d=Object(a["a"])(u);try{for(d.s();!(o=d.n()).done;){var c=o.value,m=c.match(/^(\d{9})[\-—~～@#￥%…&!、\\/*,一－。.＝=·吊赶打各\s]+([0-9]+)$/);if(m){var h=m[1],I=m[2];l.has(I)||l.set(I,[]),l.get(I).push(h)}}}catch(A){d.e(A)}finally{d.f()}var f,_=[],b=Object(a["a"])(l.entries());try{for(b.s();!(f=b.n()).done;){var p=Object(r["a"])(f.value,2),U=p[0],N=p[1];_.push({methodId:s.JIUMA_ZULIU,betNumbers:N.join(","),money:U})}}catch(A){b.e(A)}finally{b.f()}return _.length?_:null}},JIUMA_MULTI_LINE_ZULIU_MONEY_SUPER:{pattern:/^([\s\S]+)$/m,handler:function(t,e){var o,i=n("f9d6"),s=i.METHOD_ID,u=e.split(/\n/).map((function(t){return t.trim()})).filter(Boolean),l=new Map,d=/^(\d{9})[\-—~～@#￥%…&!、一－。\\/*,，.＝=·\s]+(\d+)(元|米|块)?([，,./\\*\-\=。·、\s]*)?$/,c=[],m=Object(a["a"])(u);try{for(m.s();!(o=m.n()).done;){var h=o.value,I=h.match(d);if(!I)return console.log('【JIUMA_MULTI_LINE_ZULIU_MONEY_SUPER】行 "'.concat(h,'" 不匹配九码格式，返回null')),null;if(I[1].length===I[2].length)return console.log('【JIUMA_MULTI_LINE_ZULIU_MONEY_SUPER】行 "'.concat(h,'" 号码和金额长度相同，可能误匹配，返回null')),null;c.push({line:h,match:I})}}catch(v){m.e(v)}finally{m.f()}console.log("【JIUMA_MULTI_LINE_ZULIU_MONEY_SUPER】所有 ".concat(c.length," 行都是九码格式，继续处理"));for(var f=0,_=c;f<_.length;f++){var b=_[f].match,p=b[1],U=b[2];l.has(U)||l.set(U,[]),l.get(U).push(p)}var N,A=[],M=Object(a["a"])(l.entries());try{for(M.s();!(N=M.n()).done;){var g=Object(r["a"])(N.value,2),D=g[0],E=g[1];A.push({methodId:s.JIUMA_ZULIU,betNumbers:E.join(","),money:D})}}catch(v){M.e(v)}finally{M.f()}return console.log("【JIUMA_MULTI_LINE_ZULIU_MONEY_SUPER】处理完成，返回 ".concat(A.length," 个结果组")),A.length?A:null}},JIUMA_MULTI_FANGDUI_SUPER:{pattern:/^([0-9]{9}(?:[\-—–―－﹣﹘⸺⸻~～@#￥%…&!、\\/*,，.一－。＝=·\s+吊赶打各]+[0-9]{9})+)[\-—–―－﹣﹘⸺⸻~～@#￥%…&!、\\/*,，.一－。＝=·\s+吊赶打各]*(防对|防|组三)([0-9]+)$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=t[1].split(a).map((function(t){return t.trim()})).filter((function(t){return 9===t.length})),s=t[3];return i.length<2?null:[{methodId:o.JIUMA_ZUSAN,betNumbers:i.join(","),money:s}]}},JIUMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER:{pattern:/^([\s\S]+)$/m,handler:function(t,e){var o=/(福彩|福|3D|3d)/.test(e),i=/(体彩|体|排|排三)/.test(e);if(o||i)return null;var s,u=e.split(/\r?\n/).map((function(t){return t.trim()})).filter(Boolean),l=new Map,d=new Map,c=/^([0-9]{9})[\-—~～@#￥%…&!、\\/*,一－–.。＝=·\s\/]+(\d+)\++(\d+)[元米块]*$/,m=/^([0-9]{9})[\-—~～@#￥%…&!、\\/*,一－–.。＝=·\s\/]+(\d+)[元米块]*$/,h=[],I=Object(a["a"])(u);try{for(I.s();!(s=I.n()).done;){var f=s.value,_=f.match(c);if(_)h.push({line:f,type:"zuliuZusan",match:_});else{if(_=f.match(m),!_)return console.log('【JIUMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】行 "'.concat(f,'" 不匹配九码格式，返回null')),null;h.push({line:f,type:"zuliu",match:_})}}}catch(G){I.e(G)}finally{I.f()}console.log("【JIUMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】所有 ".concat(h.length," 行都是九码格式，继续处理"));for(var b=0,p=h;b<p.length;b++){var U=p[b],N=U.match,A=U.type;if("zuliuZusan"===A){var M=N[1],g=N[2],D=N[3];l.has(g)||l.set(g,[]),l.get(g).push(M),d.has(D)||d.set(D,[]),d.get(D).push(M)}else if("zuliu"===A){var E=N[1],v=N[2];l.has(v)||l.set(v,[]),l.get(v).push(E)}}var O,y=[],S=Object(a["a"])(l.entries());try{for(S.s();!(O=S.n()).done;){var T=Object(r["a"])(O.value,2),L=T[0],j=T[1];y.push({methodId:n("f9d6").METHOD_ID.JIUMA_ZULIU,betNumbers:j.join(","),money:L})}}catch(G){S.e(G)}finally{S.f()}var R,Z=Object(a["a"])(d.entries());try{for(Z.s();!(R=Z.n()).done;){var H=Object(r["a"])(R.value,2),$=H[0],P=H[1];y.push({methodId:n("f9d6").METHOD_ID.JIUMA_ZUSAN,betNumbers:P.join(","),money:$})}}catch(G){Z.e(G)}finally{Z.f()}return console.log("【JIUMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】处理完成，返回 ".concat(y.length," 个结果组")),y.length?y:null}},JIUMA_MONEY_UNIT_FANGDUI_SUPER:{pattern:/^(\d{9})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:元|米|块)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(防|防对|组三)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*$/,handler:function(t,e){var n=t[1],r=t[2];t[3];return 9!==n.length?null:[{methodId:o["METHOD_ID"].JIUMA_ZUSAN,betNumbers:n,money:r}]}},JIUMA_MULTI_LINE_ZULIU_ZUSAN_KEYWORD:{pattern:/^([0-9]{9}(组六|组三)\d+(元|米|块)?[，,./\*\-\=。一－。·、\s]*\n?)+$/m,handler:function(t,e){var n,i=e.split(/\r?\n/).map((function(t){return t.trim()})).filter(Boolean),s=new Map,u=Object(a["a"])(i);try{for(u.s();!(n=u.n()).done;){var l=n.value,d=l.match(/^([0-9]{9})(组六|组三)(\d+)(元|米|块)?[，,./\*\-\=。一－。·、\s]*$/);if(d&&9===d[1].length){var c=d[1],m=d[2],h=d[3],I=m+"_"+h;s.has(I)||s.set(I,[]),s.get(I).push(c)}}}catch(E){u.e(E)}finally{u.f()}var f,_=[],b=Object(a["a"])(s.entries());try{for(b.s();!(f=b.n()).done;){var p=Object(r["a"])(f.value,2),U=p[0],N=p[1],A=U.split("_"),M=Object(r["a"])(A,2),g=M[0],D=M[1];_.push({methodId:"组六"===g?o["METHOD_ID"].JIUMA_ZULIU:o["METHOD_ID"].JIUMA_ZUSAN,betNumbers:N.join(","),money:D})}}catch(E){b.e(E)}finally{b.f()}return _.length?_:null}}};Object.keys(i).forEach((function(t){var e=i[t].pattern;"string"===typeof e&&e.endsWith("$")?i[t].pattern=e.replace(/\$$/,"[，,./*-=。·、s]*$"):e instanceof RegExp&&e.source.endsWith("$")&&(i[t].pattern=new RegExp(e.source.replace(/\$$/,"[，,./*-=。·、s]*$")))}))},"35d4":function(t,e,n){"use strict";n.d(e,"d",(function(){return a})),n.d(e,"e",(function(){return o})),n.d(e,"a",(function(){return i})),n.d(e,"b",(function(){return s})),n.d(e,"c",(function(){return u}));var r=n("b775");function a(t){return Object(r["a"])({url:"/game/record/list",method:"get",params:t})}function o(t){return Object(r["a"])({url:"/game/record",method:"put",data:t})}function i(t){return Object(r["a"])({url:"/game/record/"+t,method:"delete"})}function s(t){return Object(r["a"])({url:"/game/record/serial/"+t,method:"delete"})}function u(){return Object(r["a"])({url:"/game/record/generateSerialNumber",method:"get"})}},4069:function(t,e,n){"use strict";var r=n("44d2");r("flat")},"414a":function(t,e,n){"use strict";n("01e0")},"5c5d":function(t,e,n){"use strict";n.r(e),n.d(e,"TWO_CODE_PATTERNS",(function(){return i}));var r=n("3835"),a=n("b85c"),o=(n("99af"),n("4de4"),n("a630"),n("a15b"),n("d81d"),n("14d9"),n("4ec9"),n("a9e3"),n("b64b"),n("d3b7"),n("4d63"),n("c607"),n("ac1f"),n("2c3e"),n("00b4"),n("25f0"),n("8a79"),n("3ca3"),n("466d"),n("5319"),n("1276"),n("498a"),n("0643"),n("76d6"),n("2382"),n("4e3e"),n("a573"),n("9a9a"),n("159b"),n("ddb0"),n("f9d6")),i={LIANGMA_ZUHE:{pattern:/^(\d{2})(?:组合|组)[\-—~～@#￥%…&!、\\/*,，.＝=·吊赶打各\s]*(\d+)?$/,handler:function(t,e){if(2!==t[1].length)return null;var n=t[1],r=t[2];return n[0]===n[1]?null:[{methodId:o["METHOD_ID"].LIANGMA_ZUHE,betNumbers:n,money:r}]}},LIANGMA_DUIZI:{pattern:/^(\d{2})[\-—~～@#￥%…&!、\\/*,，.＝=·吊赶打各\s]*对子[\-—~～@#￥%…&!、\\/*,，.＝=·吊赶打各\s]*(\d+)?(元|米|块)?$/,handler:function(t,e){if(2!==t[1].length)return null;var n=t[1],r=t[2];return n[0]!==n[1]?null:[{methodId:o["METHOD_ID"].LIANGMA_DUIZI,betNumbers:n,money:r}]}},LIANGMA_ZUSAN:{pattern:/^(\d{2})[\-—~～@#￥%…&!、\\/*,，一－.＝=·\s]*组三[\-—~～@#￥%…&!、\\/*,，.＝=·吊赶打各\s]*(\d+)?(元|米|块)?$/,handler:function(t,e){if(2!==t[1].length)return null;var n=t[1],r=t[2],a=t[3];if(n[0]!==n[1])return null;var i={methodId:o["METHOD_ID"].LIANGMA_DUIZI,betNumbers:n,money:r};return a&&(i.unit=a),[i]}},LIANGMA_ZUHE_DUIZI:{pattern:/^(\d{2})(?:[^0-9]+\d{2})*-(\d+)\+(\d+)$/,handler:function(t,e){var n=e.split(o["BASE_PATTERNS"].SEPARATORS).filter(Boolean).filter((function(t){return 2===t.length})),r=e.split(o["BASE_PATTERNS"].SEPARATORS).filter(Boolean);if(!r.length||r.some((function(t){return 2!==t.length})))return null;var a=t[2],i=t[3];return[{methodId:3,betNumbers:n.join(","),money:a},{methodId:4,betNumbers:n.join(","),money:i}]}},MULTI_LIANGMA:{pattern:/^(\d{2}(?:[^0-9]+\d{2})*)(?:各([一二三四五六七八九十百千万\d]+))?(?:防([一二三四五六七八九十百千万\d]+))?$/,handler:function(t,e){var r=n("f9d6"),a=r.chineseToNumber,i=e.split(o["BASE_PATTERNS"].SEPARATORS).filter(Boolean).filter((function(t){return 2===t.length}));if(!i.length)return null;var s=t[2]?a(t[2]):"",u=t[3]?a(t[3]):"",l=[];return s&&l.push({methodId:o["METHOD_ID"].LIANGMA_ZUHE,betNumbers:i.join(","),money:s}),u&&l.push({methodId:o["METHOD_ID"].LIANGMA_DUIZI,betNumbers:i[i.length-1],money:u}),console.log("两码 handler 返回:",l),l}},LIANGMA_ZUHE_ZU_SUPER:{pattern:/^(\d{2})组(\d+)$/,handler:function(t,e){var r=t[1];if(2!==r.length)return null;var a=t[2];return[{methodId:n("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:r,money:a}]}},LIANGMA_ZUHE_MULTI_SUPER:{pattern:/^(\d{2}(?:[\-~～@#￥%…&!、\\/*一－。,，.＝=· \t+吊赶打各]+\d{2})*)[\-~～@#￥%…&!、\\/*一－。,，.＝=· \t+吊赶打各]+(\d+)(元|块|米)?$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=t[1].split(a).filter(Boolean);if(!o.length||o.some((function(t){return 2!==t.length})))return null;var i=t[2],s=[],u=[];o.forEach((function(t){t[0]===t[1]?u.push(t):s.push(t)}));var l=[];return s.length&&l.push({methodId:n("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:s.join(","),money:i}),u.length&&l.push({methodId:n("f9d6").METHOD_ID.LIANGMA_DUIZI,betNumbers:u.join(","),money:i}),l.length?l:null}},LIANGMA_DUIZI_DUI_SUPER:{pattern:/^(\d{2})对子(\d+)$/,handler:function(t,e){var r=t[1];if(2!==r.length)return null;var a=t[2];return[{methodId:n("f9d6").METHOD_ID.LIANGMA_DUIZI,betNumbers:r,money:a}]}},LIANGMA_SINGLE_WITH_MONEY_SUPER:{pattern:/^(\d{2})[\-~～@#￥%…&!、\\/*,，一－。.＝=·\s+吊赶打各]+(\d{1,4})$/,handler:function(t,e){var r=t[1];if(2!==r.length)return null;var a=t[2];return r[0]===r[1]?[{methodId:n("f9d6").METHOD_ID.LIANGMA_DUIZI,betNumbers:r,money:a}]:[{methodId:n("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:r,money:a}]}},LIANGMA_MULTI_WITH_MONEY_SLASH_SUPER:{pattern:/^(\d{2}(?:[\-~～@#￥%…&!、\\/*,，.＝=一－。·\s+吊赶打各]+\d{2})*)\/(\d{1,4})$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=t[1].split(a).filter(Boolean);if(!o.length||o.some((function(t){return 2!==t.length})))return null;var i=t[2],s=[],u=[];o.forEach((function(t){t[0]===t[1]?u.push(t):s.push(t)}));var l=[];return s.length&&l.push({methodId:n("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:s.join(","),money:i}),u.length&&l.push({methodId:n("f9d6").METHOD_ID.LIANGMA_DUIZI,betNumbers:u.join(","),money:i}),l.length?l:null}},LIANGMA_MULTI_WITH_DOUBLE_MONEY_SLASH_SUPER:{pattern:/^(\d{2}(?:[\-~～@#￥%…&!、\\/*,，一－。.＝=·\s+吊赶打各]+\d{2})*)\/(\d+)-(\d+)$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=t[1].split(a).filter(Boolean);if(!o.length||o.some((function(t){return 2!==t.length})))return null;var i=o,s=t[2],u=t[3];return[{methodId:n("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:i.join(","),money:s},{methodId:n("f9d6").METHOD_ID.LIANGMA_DUIZI,betNumbers:i.join(","),money:u}]}},LIANGMA_ZUHE_FANG:{pattern:/^(\d{2})-(\d+)防(\d+)$/,handler:function(t,e){var r=t[1];if(2!==r.length)return null;var a=t[2],o=t[3];return[{methodId:n("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:r,money:a},{methodId:n("f9d6").METHOD_ID.LIANGMA_DUIZI,betNumbers:r,money:o}]}},LIANGMA_MULTI_ZUHE_DUIZI_SLASH_SUPER:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、＝一－。=·吊赶打各]+)(?:\/|各|\s|吊|赶|打)(\d+)\+(\d+)$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=t[1].split(a).filter(Boolean);if(!o.length||o.some((function(t){return 2!==t.length})))return null;var i=o,s=t[2],u=t[3];return[{methodId:n("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:i.join(","),money:s},{methodId:n("f9d6").METHOD_ID.LIANGMA_DUIZI,betNumbers:i.join(","),money:u}]}},LIANGMA_MULTI_EACH_SUPER:{pattern:/^(\d{2}(?:[\-~～@#￥%…&!、\\/*,，.＝=一－。·\s+吊赶打各]+\d{2})*)各(\d+)(元|块|米)?$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=t[1].split(a).filter(Boolean);if(!o.length||o.some((function(t){return 2!==t.length})))return null;var i=t[2];return[{methodId:n("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:o.join(","),money:i}]}},LIANGMA_MULTI_SUPER_SLASH_TWO_MONEY:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、一－。＝=·吊赶打各]+)\/(\d+)\/(\d+)$/,handler:function(t,e){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=n("f9d6"),o=a.SUPER_SEPARATORS,i=t[1].split(o).map((function(t){return t.trim()})).filter((function(t){return 2===t.length}));if(!i.length)return[];var s=t[2],u=t[3];return r&&(/^\d{2}$/.test(s)||/^\d{2}$/.test(u))?null:[{methodId:n("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:i.join(","),money:s},{methodId:n("f9d6").METHOD_ID.LIANGMA_DUIZI,betNumbers:i.join(","),money:u}]}},LIANGMA_MULTI_WITH_MONEY_UNIT_SUPER:{pattern:/^([\s\S]+)$/m,handler:function(t,e){var o,i=e.split(/\n+/).map((function(t){return t.trim()})).filter(Boolean),s=new Map,u=0,l=Object(a["a"])(i);try{for(l.s();!(o=l.n()).done;){var d=o.value,c=d.match(/(\d{2})[\s,，.。\-—~～@#￥%…&!、\\/*]*\/?[\s,，.。\-—~～@#￥%…&!、\\/*]*(\d+)[\s,，.。\-—~～@#￥%…&!、\\/*]*(元|块|米)/);if(c){u++;var m=c[1],h=c[2];s.has(h)||s.set(h,[]),s.get(h).push(m)}}}catch(I){l.e(I)}finally{l.f()}return u<2?null:s.size?Array.from(s.entries()).map((function(t){var e=Object(r["a"])(t,2),a=e[0],o=e[1];return{methodId:n("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:o.join(","),money:a}})):null}},LIANGMA_UNIVERSAL_SUPER:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!一－—。、＝=·吊赶打各]+)[\-~～@#￥%…&!、\\/*,，一－—。.＝=·吊赶打各]+([0-9]+)(元|米|块)?$/,handler:function(t,e){var o=n("f9d6"),i=o.SUPER_SEPARATORS,s=o.METHOD_ID,u=e.split(/\n|\r/).map((function(t){return t.trim()})).filter(Boolean),l=new Map;u.forEach((function(t){var e=t.match(/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!一－—。、＝=·吊赶打各]+)[\-~～@#￥%…&!、\\/*,，一－—。.＝=·吊赶打各]+([0-9]+)(元|米|块)?$/);if(e){var n=e[1].split(i).map((function(t){return t.trim()})).filter((function(t){return 2===t.length})),r=e[2];n.forEach((function(t){var e=null;e=t[0]===t[1]?s.LIANGMA_DUIZI:s.LIANGMA_ZUHE;var n=e+"_"+r;l.has(n)||l.set(n,[]),l.get(n).push(t)}))}}));var d,c=[],m=Object(a["a"])(l.entries());try{for(m.s();!(d=m.n()).done;){var h=Object(r["a"])(d.value,2),I=h[0],f=h[1],_=I.split("_"),b=Object(r["a"])(_,2),p=b[0],U=b[1];c.push({methodId:Number(p),betNumbers:f.join(","),money:U})}}catch(N){m.e(N)}finally{m.f()}return c.length?c:null}},LIANGMA_EACH_PLUS_SUPER:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!一－。、＝=·吊赶打各]+)各([0-9]+)\+([0-9]+)$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=t[1].split(a).map((function(t){return t.trim()})).filter(Boolean),s=t[2],u=t[3];return 1===i.length&&2===i[0].length?[{methodId:o.LIANGMA_ZUHE,betNumbers:i[0],money:s},{methodId:o.LIANGMA_DUIZI,betNumbers:i[0],money:u}]:i.length>=2&&i.every((function(t){return 2===t.length}))?[{methodId:o.LIANGMA_ZUHE,betNumbers:i.join(","),money:s},{methodId:o.LIANGMA_DUIZI,betNumbers:i.join(","),money:u}]:null}},LIANGMA_UNIVERSAL_SUPER_NEW:{pattern:/^([0-9]{2,})\s+(\d+)(元|块|米)?$/,handler:function(t,e){var r=t[1],a=t[2];return r&&2===r.length?[{methodId:n("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:r,money:a}]:null}},LIANGMA_DA_MONEY:{pattern:/^([0-9]{2})打(\d+)(元|块|米)?$/,handler:function(t,e){return[{methodId:n("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:t[1],money:t[2]}]}},LIANGMA_MULTI_WITH_MONEY_SLASH_PLUS:{pattern:/^(\d{2}(?:[\s,，\-]+?\d{2})*)\/\s*(\d+)\+(\d+)$/,handler:function(t,e){var r=t[1].split(/[\s,，\-]+/).filter(Boolean),a=t[2],o=t[3];return[{methodId:n("f9d6").METHOD_ID.LIANGMA_ZUHE,betNumbers:r.join(","),money:a},{methodId:n("f9d6").METHOD_ID.LIANGMA_DUIZI,betNumbers:r.join(","),money:o}]}},LIANGMA_MULTI_LINE_MONEY_PLUS_CN:{pattern:/^((?:\d{2}[，,、]?[一二三四五六七八九十百千万\d]+加[一二三四五六七八九十百千万\d]+\s*\n?)+)$/m,handler:function(t,e){var o,i=n("f9d6"),s=i.METHOD_ID,u=i.chineseToNumber,l=e.split(/\n/).map((function(t){return t.trim()})).filter(Boolean),d=new Map,c=[],m=Object(a["a"])(l);try{for(m.s();!(o=m.n()).done;){var h=o.value,I=h.match(/^(\d{2})[，,、]?([一二三四五六七八九十百千万\d]+)加([一二三四五六七八九十百千万\d]+)$/);if(I){var f=I[1],_=u(I[2]),b=u(I[3]),p="".concat(_,"+").concat(b);d.has(p)||d.set(p,[]),d.get(p).push(f)}else c.push(h)}}catch(S){m.e(S)}finally{m.f()}var U,N=[],A=Object(a["a"])(d.entries());try{for(A.s();!(U=A.n()).done;){var M=Object(r["a"])(U.value,2),g=M[0],D=M[1],E=g.split("+"),v=Object(r["a"])(E,2),O=v[0],y=v[1];N.push({methodId:s.LIANGMA_ZUHE,betNumbers:D.join(","),money:O}),N.push({methodId:s.LIANGMA_DUIZI,betNumbers:D.join(","),money:y})}}catch(S){A.e(S)}finally{A.f()}return N.length?N:null}},DUIZI_SLASH_MONEY:{pattern:/^对(\d{2})\/(\d+)$/,handler:function(t,e){return[{methodId:o["METHOD_ID"].LIANGMA_DUIZI,betNumbers:t[1],money:t[2]}]}},ERMA_YI_YI_ONE_MONEY:{pattern:/^(\d{2})一(\d{2})一个(\d+)(元|块|米)?$/,handler:function(t,e){var r=t[1],a=t[2],o=t[3];return[{methodId:n("f9d6").METHOD_ID.ERMA_COMBO,betNumbers:r+","+a,money:o}]}},LIANGMA_MULTI_SINGLE_WITH_MONEY_FEN_SUPER:{pattern:/^([\s\S]+)$/m,handler:function(t,e){if(!/分\s*$/.test(e))return null;e=e.replace(/分\s*$/m,"").replace(/[\r\n]+/g," ").trim();var a,o=/(\d{2})[\-—~～@#￥%…&!、\\/*,，.＝=·吊赶打各\s]+(\d{2,3})(?=[\-—~～@#￥%…&!、\\/*,，.＝=·吊赶打各\s]|$)/g,i=new Map,s=[],u=e;while(null!==(a=o.exec(e)))s.push({num:a[1],money:a[2],raw:a[0]}),u=u.replace(a[0],"@@"),i.has(a[2])||i.set(a[2],[]),i.get(a[2]).push(a[1]);if(0===s.length)return null;if(!/^\d{2}$/.test(s[0].num))return null;if(u.replace(/[@\-—~～@#￥%…&!、\\/*,，.＝=·吊赶打各\s]/g,"").length>0)return null;var l=n("f9d6").METHOD_ID;return Array.from(i.entries()).map((function(t){var e=Object(r["a"])(t,2),n=e[0],a=e[1];return{methodId:l.LIANGMA_MULTI_SINGLE_WITH_MONEY_FEN_SUPER||3,betNumbers:a.join(","),money:n}}))}}};Object.keys(i).forEach((function(t){var e=i[t].pattern;"string"===typeof e&&e.endsWith("$")?i[t].pattern=e.replace(/\$$/,"[，,./*-=。·、s]*$"):e instanceof RegExp&&e.source.endsWith("$")&&(i[t].pattern=new RegExp(e.source.replace(/\$$/,"[，,./*-=。·、s]*$")))}))},"5f85":function(t,e,n){"use strict";n.r(e),n.d(e,"SIX_CODE_PATTERNS",(function(){return s}));var r=n("2909"),a=n("3835"),o=n("b85c"),i=(n("99af"),n("4de4"),n("caad"),n("a15b"),n("d81d"),n("14d9"),n("fb6a"),n("4e82"),n("4ec9"),n("b64b"),n("d3b7"),n("4d63"),n("c607"),n("ac1f"),n("2c3e"),n("00b4"),n("25f0"),n("8a79"),n("2532"),n("3ca3"),n("466d"),n("5319"),n("1276"),n("498a"),n("0643"),n("76d6"),n("2382"),n("4e3e"),n("a573"),n("9a9a"),n("159b"),n("ddb0"),n("f9d6")),s={LIUMA_TRANSFORM:{pattern:/^(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?(\d{6})[\-—~～@#￥%…&!、\\/*,，.＝=各·\s]*?(转|套|拖)(?:各)?(\d+)(?:\+(\d+))?(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?$/,handler:function(t,e){var r=t[2],a=t[4],o=t[5],i=t[6],s=n("f9d6").generateTransformNumbers(r),u=[];return a?u.push({methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:s.join(","),money:a}):o&&i&&(u.push({methodId:n("f9d6").METHOD_ID.QIMA_ZULIU,betNumbers:s.join(","),money:o}),u.push({methodId:n("f9d6").METHOD_ID.QIMA_ZUSAN,betNumbers:s.join(","),money:i})),u}},LIUMA_ZUSAN:{pattern:/^(\d{6})[\-—~～@#￥%…&!、\\/*,，。，一－.＝=·\s]*组三?[\-—~～@#￥%…&!、\\/*,，。，一－.＝=·\s]*([\d]+)?(元|块|米)?[\-—~～@#￥%…&!、。，一－\\/*,，.＝=·\s]*$/,handler:function(t,e){return 6!==t[1].length?null:[{methodId:i["METHOD_ID"].LIUMA_ZUSAN,betNumbers:t[1],money:t[2]||""}]}},LIUMA_FANGDUI:{pattern:/^(\d{6})[\-—~～@#￥%…&!、\\/*,，一－.＝=·\s]*防对?(?:-?(\d+))?$/,handler:function(t,e){return 6!==t[1].length?null:[{methodId:i["METHOD_ID"].LIUMA_ZUSAN,betNumbers:t[1],money:t[2]||""}]}},LIUMA_SIMPLE:{pattern:/^(\d{6})\/(\d+)\/?$/,handler:function(t,e){var n=t[1],r=t[2];return[{methodId:i["METHOD_ID"].LIUMA_ZULIU,betNumbers:n,money:r}]}},MULTI_LIUMA:{pattern:/^(\d{6}(?:[^0-9\n]+\d{6})*)(?:各([一二三四五六七八九十百千万\d]+))?(?:防([一二三四五六七八九十百千万\d]+))?$/,handler:function(t,e){var r=n("f9d6"),a=r.chineseToNumber,o=e.split(i["BASE_PATTERNS"].SEPARATORS).filter(Boolean).filter((function(t){return 6===t.length}));if(!o.length||!o.every((function(t){return 6===t.length})))return null;var s=t[2]?a(t[2]):"",u=t[3]?a(t[3]):"",l=[];return s&&l.push({methodId:i["METHOD_ID"].LIUMA_ZULIU,betNumbers:o.join(","),money:s}),u&&l.push({methodId:i["METHOD_ID"].LIUMA_ZUSAN,betNumbers:o[o.length-1],money:u}),l.length?l:null}},LIUMA_ZULIU_ZUSAN_MULTI_MONEY_SUPER:{pattern:/^(\d{6}(?:[－\-—~～@#￥%…&!、\\/*,一－。.＝=·+吊赶打各]+\d{6})*)[\-—~～@#￥%…&!、\\/*,一－.。＝=·+吊赶打各]+(\d+)[\-—~～@#￥%…&!、\\/*,一－.。＝=·+吊赶打各]*[\+\-\\/*、.]+[\-—~～@#￥%…&!、\\/*,一－.。＝=·+吊赶打各]*(\d+)(?:元)?$/,handler:function(t,e){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=n("f9d6"),o=a.SUPER_SEPARATORS,i=t[1].split(o).filter(Boolean),s=t[2],u=t[3];return r&&(/^\d{6}$/.test(s)||/^\d{6}$/.test(u))?null:[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:i.join(","),money:s},{methodId:n("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:i.join(","),money:u}]}},LIUMA_ZULIU_ZU_SUPER:{pattern:/^(\d{6})组(\d+)$/,handler:function(t,e){var r=t[1],a=t[2];return[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:r,money:a}]}},LIUMA_ZULIU_MULTI_SUPER:{pattern:/^(\d{6}(?:[\-—~～@#￥%…&一－。+!、\\/*,，.＝=·\s\n吊赶打各]+\d{6})*)[\-—~～@#￥%…&一－。!、\\/*,，.＝=·\s+吊赶打各]+各(\d+)(元)?(共计\d+(元)?)?$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=t[1].split(a).filter(Boolean),i=t[2];return[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:o.join(","),money:i}]}},LIUMA_ZUSAN_ZU_SUPER:{pattern:/^(\d{6})[\-—~～@#￥%…&!、\\/*,，一－.＝=·\s]*组三[\-—~～@#￥%…&!、\\/*,，一－.＝=·\s]*(\d+)$/,handler:function(t,e){var r=t[1],a=t[2];return[{methodId:n("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:r,money:a}]}},LIUMA_ZHIZU_MULTI_SUPER:{pattern:/^(\d{6}(?:[\-—~～@#￥%…&!、\\/一－。*,，.＝=·\s+吊赶打各]+\d{6})*)[\-—~～@#￥%…&!、一－。\\/*,，.＝=·\s+吊赶打各]*(直组|组直|值组|组值)[\s]*各?(\d+)(?:元)?$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=t[1].split(a).filter(Boolean),i=t[3];return[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:o.join(","),money:i},{methodId:n("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:o.join(","),money:i}]}},LIUMA_ZULIU_FANG:{pattern:/^(\d{6})[+＋\-—~～@#￥%…&!、：:\\/*,，。一—－~.＝=·吊赶打各](\d+)防(\d+)$/,handler:function(t,e){var n=t[1],r=t[2],a=t[3];return[{methodId:i["METHOD_ID"].LIUMA_ZULIU,betNumbers:n,money:r},{methodId:i["METHOD_ID"].LIUMA_ZUSAN,betNumbers:n,money:a}]}},LIUMA_MULTI_ZHIZU_EACH_SUPER:{pattern:/^(直组|组直|值组|组值)各([0-9]+)[\s\-—~～@#￥%…&!一－。、\\/*,，.＝=·吊赶打各\n]+([\d\s\-—~～@#￥%…&!一－。、\\/*,，.＝=·吊赶打各]+)$/i,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=t[2],i=t[3].replace(/\n/g," "),s=i.split(a).map((function(t){return t.trim()})).filter((function(t){return/^\d{6}$/.test(t)}));return[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:s.join(","),money:o},{methodId:n("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:s.join(","),money:o}]}},LIUMA_MULTI_GAN_FANGDUI_SUPER:{pattern:/^([0-9]{6}(?:[\-—~～@#￥%…&!、\\/*,，.一－。＝=·+吊赶打各 ]+[0-9]{6})*)[\-—~～@#￥%…&!、一－。\\/*,，.＝=·+吊赶打各 ]+赶([0-9]+)[\-—~～@#￥%…&!、一－。\\/*,，.＝=·+吊赶打各 ]*防对([0-9]+)$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=t[1].split(a).filter(Boolean);console.log("格式化后号码:",o.join(","));var i=t[2],s=t[3];return[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:o.join(","),money:i},{methodId:n("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:o.join(","),money:s}]}},LIUMA_MULTI_EACH_SUPER:{pattern:/^(\d{6}(?:[\-~～@#￥%…&!、\\/*,一－。，.＝=·\+吊赶打各 ]+\d{6})*)[+＋\-—~～@#￥%…&!、：:\\/*,，。一—－~.＝=·吊赶打各 ](\d+)$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=t[1].split(a).filter(Boolean);if(!o.length||o.some((function(t){return 6!==t.length})))return[];var i=t[2];return[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:o.join(","),money:i}]}},LIUMA_SLASH_TWO_MONEY:{pattern:/^(?!.*[\r\n])(\d{6})[\-—~～@#￥%…&!、\\/*,，.。\u4e00\uff0d\uff1d=· 　\t]+(\d{!6}+)[\-—~～@#￥%…&!、\\/*,，.。\u4e00\uff0d\uff1d=· 　\t]+(\d+)$/,handler:function(t,e){var r=t[1],a=t[2],o=t[3];return[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:r,money:a},{methodId:n("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:r,money:o}]}},LIUMA_UNIVERSAL_SUPER:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!一－—。、＝=·吊赶打各]+)[\-~～@#￥%…&!、\\/*,，.一－。—＝=·\s+吊赶打各]+([0-9]+)(元|米|块)?$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=t[1].split(a).map((function(t){return t.trim()})).filter(Boolean),s=t[2];return 1===i.length&&6===i[0].length?[{methodId:o.LIUMA_ZULIU,betNumbers:i[0],money:s}]:i.length>=2&&i.every((function(t){return 6===t.length}))?[{methodId:o.LIUMA_ZULIU,betNumbers:i.join(","),money:s}]:null}},LIUMA_EACH_PLUS_SUPER:{pattern:/^([\d、,，.。\\/*\-~～@#￥一－。%…&!、＝=·吊赶打各]+)各([0-9]+)\+([0-9]+)$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=t[1].split(a).map((function(t){return t.trim()})).filter(Boolean),s=t[2],u=t[3];return 1===i.length&&6===i[0].length?[{methodId:o.LIUMA_ZULIU,betNumbers:i[0],money:s},{methodId:o.LIUMA_ZUSAN,betNumbers:i[0],money:u}]:i.length>=2&&i.every((function(t){return 6===t.length}))?[{methodId:o.LIUMA_ZULIU,betNumbers:i.join(","),money:s},{methodId:o.LIUMA_ZUSAN,betNumbers:i.join(","),money:u}]:null}},LIUMA_UNIVERSAL_SUPER_NEW:{pattern:/^([0-9]{6,})\s+(\d+)(元|块|米)?$/,handler:function(t,e){var r=t[1],a=t[2];return r&&6===r.length?[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:r,money:a}]:null}},LIUMA_DA_MONEY:{pattern:/^([0-9]{6})打(\d+)(元|块|米)?$/,handler:function(t,e){return[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:t[1],money:t[2]}]}},LIUMA_MULTI_WITH_MONEY_SLASH_PLUS:{pattern:/^(\d{6}(?:[\s,，、\-]+?\d{6})*)(?:\/|各|\s|吊|赶|打|-|\+|、)(\d+)\+(\d+)$/,handler:function(t,e){var r=t[1].split(/[\s,，\-]+/).filter(Boolean),a=t[2],o=t[3];return[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:r.join(","),money:a},{methodId:n("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:r.join(","),money:o}]}},LIUMA_MULTI_LINE_MONEY_PLUS:{pattern:/^((?:\d{6}[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+\d+\+\d+\s*\n?)+)$/m,handler:function(t,e){var r,i=n("f9d6"),s=i.METHOD_ID,u=(i.SUPER_SEPARATORS,e.split(/\n/).map((function(t){return t.trim()})).filter(Boolean)),l=new Map,d=!0,c=Object(o["a"])(u);try{for(c.s();!(r=c.n()).done;){var m=r.value,h=m.match(/^(\d{6})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+(\d+)\+(\d+)$/);if(!h){d=!1;break}var I=h[1];if(6!==I.length){d=!1;break}var f=m.split(/[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+/)[0];if(6!==f.length||!/^\d{6}$/.test(f)){d=!1;break}var _="".concat(h[2],"+").concat(h[3]);l.has(_)||l.set(_,[]),l.get(_).push(I)}}catch(O){c.e(O)}finally{c.f()}if(!d||0===l.size)return null;var b,p=[],U=Object(o["a"])(l.entries());try{for(U.s();!(b=U.n()).done;){var N=Object(a["a"])(b.value,2),A=N[0],M=N[1],g=A.split("+"),D=Object(a["a"])(g,2),E=D[0],v=D[1];p.push({methodId:s.LIUMA_ZULIU,betNumbers:M.join(","),money:E}),p.push({methodId:s.LIUMA_ZUSAN,betNumbers:M.join(","),money:v})}}catch(O){U.e(O)}finally{U.f()}return p.length?p:null}},LIUMA_MULTI_LINE_MONEY_PLUS_CN:{pattern:/^((?:\d{6}[，,、]?[一二三四五六七八九十百千万\d]+加[一二三四五六七八九十百千万\d]+\s*\n?)+)$/m,handler:function(t,e){var r,i=n("f9d6"),s=i.METHOD_ID,u=i.chineseToNumber,l=e.split(/\n/).map((function(t){return t.trim()})).filter(Boolean),d=new Map,c=Object(o["a"])(l);try{for(c.s();!(r=c.n()).done;){var m=r.value,h=m.match(/^(\d{6})[，,、]?([一二三四五六七八九十百千万\d]+)加([一二三四五六七八九十百千万\d]+)$/);if(h){var I=h[1],f=u(h[2]),_=u(h[3]),b="".concat(f,"+").concat(_);d.has(b)||d.set(b,[]),d.get(b).push(I)}}}catch(y){c.e(y)}finally{c.f()}var p,U=[],N=Object(o["a"])(d.entries());try{for(N.s();!(p=N.n()).done;){var A=Object(a["a"])(p.value,2),M=A[0],g=A[1],D=M.split("+"),E=Object(a["a"])(D,2),v=E[0],O=E[1];U.push({methodId:s.LIUMA_ZULIU,betNumbers:g.join(","),money:v}),U.push({methodId:s.LIUMA_ZUSAN,betNumbers:g.join(","),money:O})}}catch(y){N.e(y)}finally{N.f()}return U.length?U:null}},LIUMA_MULTI_EACH_FANGDUI_SLASH:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%一－。…&!、＝=·吊赶打各]+)[/\-~～@#￥%…&!、一－。\\/*,，.＝=·吊赶打各]+各防对([一二三四五六七八九十百千万\d]+)$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=r.chineseToNumber,s=t[1].split(a).map((function(t){return t.trim()})).filter((function(t){return 6===t.length})),u=i(t[2]);return s.length?[{methodId:o.LIUMA_ZUSAN,betNumbers:s.join(","),money:u}]:null}},LIUMA_MULTI_EACH_FANGDUI:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥一－。%…&!、＝=·吊赶打各]+)各防对([一二三四五六七八九十百千万\d]+)$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=r.chineseToNumber,s=t[1].split(a).map((function(t){return t.trim()})).filter((function(t){return 6===t.length})),u=i(t[2]);return s.length?[{methodId:o.LIUMA_ZUSAN,betNumbers:s.join(","),money:u}]:null}},LIUMA_SLASH_MONEY:{pattern:/^\s*(\d{6})\s*\/\s*([一二三四五六七八九十百千万\d]+)\s*$/,handler:function(t,e){var r=n("f9d6"),a=r.METHOD_ID,o=r.chineseToNumber,i=t[1],s=o(t[2]);return[{methodId:a.LIUMA_ZULIU,betNumbers:i,money:s}]}},LIUMA_ZULIU_ZUSAN_MONEY_PAIR:{pattern:/^(\d{6}[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*)组六[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*(\d+)[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*组三[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*(\d+)$/,handler:function(t,e){var n=t[1],r=t[2],a=t[3];return[{methodId:i["METHOD_ID"].LIUMA_ZULIU,betNumbers:n,money:r},{methodId:i["METHOD_ID"].LIUMA_ZUSAN,betNumbers:n,money:a}]}},LIUMA_ZULIU:{pattern:/^(\d{6})组六?(\d+)[，,./\*\-\=。·、\s]*$/,handler:function(t,e){var n=t[1];if(6!==n.length)return null;var r=t[2]||"";return[{methodId:i["METHOD_ID"].LIUMA_ZULIU,betNumbers:n,money:r}]}},LIUMA_ZULIU_ZUSAN_COMBO:{pattern:/^(\d{6}(?:[。.．、,，\s]+\d{6})*)\/(\d+)\+(\d+)$/,handler:function(t,e){var r=n("f9d6"),a=(r.SUPER_SEPARATORS,t[1].split(/[。.．、,，\s]+/).map((function(t){return t.trim()})).filter(Boolean)),o=t[2],i=t[3];return a.length&&a.every((function(t){return 6===t.length}))?[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:a.join(","),money:o},{methodId:n("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:a.join(","),money:i}]:null}},LIUMA_SINGLE_LINE_ZULIU_ZUSAN_PLUS:{pattern:/^(\d{6})\s+(\d+)[+＋](\d+)$/,handler:function(t,e){return[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:t[1],money:t[2]},{methodId:n("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:t[1],money:t[3]}]}},LIUMA_MULTI_LINE_ZULIU_PLUS:{pattern:/^((?:\d{6}[+＋]\d+\s*\n?)+)$/m,handler:function(t,e){var r,i=n("f9d6"),s=i.METHOD_ID,u=n("b2f0"),l=(u.getBestPatternResult,e.split(/\n/).map((function(t){return t.trim()})).filter(Boolean)),d=new Map,c=[],m=Object(o["a"])(l);try{for(m.s();!(r=m.n()).done;){var h=r.value,I=h.match(/^(\d{6})[+＋](\d+)$/);if(I){var f=I[1],_=I[2];d.has(_)||d.set(_,[]),d.get(_).push(f)}else c.push(h)}}catch(g){m.e(g)}finally{m.f()}var b,p=[],U=Object(o["a"])(d.entries());try{for(U.s();!(b=U.n()).done;){var N=Object(a["a"])(b.value,2),A=N[0],M=N[1];p.push({methodId:s.LIUMA_ZULIU,betNumbers:M.join(","),money:A})}}catch(g){U.e(g)}finally{U.f()}return p}},LIUMA_ZULIU_FANG_ZUSAN_PLUS:{pattern:/^(\d{6})[\-—~～@#￥%…&!、一－。\\/*,，.＝=·\s+吊赶打各]*(\d+)[\-—~～@#￥%…&!、一－。\\/*,，.＝=·\s+吊赶打各]*(防对|防)(\d+)$/,handler:function(t,e){return[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:t[1],money:t[2]},{methodId:n("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:t[1],money:t[4]}]}},LIUMA_MULTI_LINE_ZULIU_FANG_ZUSAN_GROUP:{pattern:/^([\s\S]+)$/m,handler:function(t,e){var r,i=e.split(/[\r\n]+/).map((function(t){return t.trim()})).filter(Boolean),s=new Map,u=Object(o["a"])(i);try{for(u.s();!(r=u.n()).done;){var l=r.value,d=l.match(/^(\d{6})-?(\d+)防(\d+)$/);if(d){var c=d[1],m=d[2],h=d[3],I="".concat(m,"|").concat(h);s.has(I)||s.set(I,[]),s.get(I).push(c)}}}catch(E){u.e(E)}finally{u.f()}var f,_=[],b=Object(o["a"])(s.entries());try{for(b.s();!(f=b.n()).done;){var p=Object(a["a"])(f.value,2),U=p[0],N=p[1],A=U.split("|"),M=Object(a["a"])(A,2),g=M[0],D=M[1];_.push({methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:N.join(","),money:g}),_.push({methodId:n("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:N.join(","),money:D})}}catch(E){b.e(E)}finally{b.f()}return _.length?_:null}},LIUMA_MULTI_LINE_WHOLE_MONEY:{pattern:/^([0-9]{6})\+(\d+)$/m,handler:function(t,e){var r,a=e.split(/[\r\n]+/).map((function(t){return t.trim()})).filter(Boolean),i=[],s=Object(o["a"])(a);try{for(s.s();!(r=s.n()).done;){var u=r.value,l=u.match(/^([0-9]{6})\+(\d+)$/);l&&i.push({methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:l[1],money:l[2]})}}catch(d){s.e(d)}finally{s.f()}return i.length?i:null}},LIUMA_MULTI_TRANSFORM_EACH:{pattern:/^(\d{6}(?:[\-~～@#￥%…&!、\\/*一－。,，.＝=·\s+吊赶打各]+\d{6})*)(转|转各|套|套各)(\d+)\+(\d+)$/,handler:function(t,e){var r,a=n("f9d6"),i=a.SUPER_SEPARATORS,s=a.METHOD_ID,u=a.generateTransformNumbers,l=t[1].split(i).filter(Boolean),d=t[3],c=t[4],m=[],h=Object(o["a"])(l);try{for(h.s();!(r=h.n()).done;){var I=r.value,f=u(I);m.push({methodId:s.QIMA_ZULIU,betNumbers:f.join(","),money:d}),m.push({methodId:s.QIMA_ZUSAN,betNumbers:f.join(","),money:c})}}catch(_){h.e(_)}finally{h.f()}return m}},LIUMA_MULTI_LINE_TRANSFORM:{pattern:/^((?:(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?\d{6}转\d+(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?\s*\n?)+)$/m,handler:function(t,e){var r,i=n("f9d6"),s=i.METHOD_ID,u=e.split(/\n/).map((function(t){return t.trim()})).filter(Boolean),l=new Map,d=Object(o["a"])(u);try{for(d.s();!(r=d.n()).done;){var c=r.value,m=c.match(/^(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?(\d{6})转(\d+)(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?$/);if(m){var h=m[2],I=m[3];l.has(I)||l.set(I,[]),l.get(I).push(h)}}}catch(A){d.e(A)}finally{d.f()}var f,_=[],b=Object(o["a"])(l.entries());try{for(b.s();!(f=b.n()).done;){var p=Object(a["a"])(f.value,2),U=p[0],N=p[1];_.push({methodId:s.QIMA_ZULIU,betNumbers:N.join(","),money:U})}}catch(A){b.e(A)}finally{b.f()}return _.length?_:null}},LIUMA_MULTI_COLON_EACH_SUPER:{pattern:/^(\d{6}(?::|：\d{6})*)各(\d+)$/,handler:function(t,e){var r=e.split(/:|：/).map((function(t){return t.trim()})).filter((function(t){return 6===t.length})),a=t[2];return r.length?[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:r.join(","),money:a}]:null}},LIUMA_MULTI_YIGE_EACH_PLUS:{pattern:/^([\d一]+)一([\d一]+)一?各?(\d+)\+(\d+)$/,handler:function(t,e){var r=e.split("一").map((function(t){return t.replace(/[^\d]/g,"")})).filter((function(t){return 6===t.length})),a=t[3],o=t[4];return r.length?[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:r.join(","),money:a},{methodId:n("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:r.join(","),money:o}]:null}},LIUMA_DIAN_XMA_ZULIU_FANG_ZUSAN:{pattern:/^(\d{6})[。.．、,，\s]+6码(\d+)防(\d+)$/,handler:function(t,e){return[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:t[1],money:t[2]},{methodId:n("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:t[1],money:t[3]}]}},LIUMA_MULTI_LINE_ZULIU_MONEY_GROUP:{pattern:/^((?:\d{6}\s+\d+\s*\n?)+)$/m,handler:function(t,e){var r,i=n("f9d6"),s=i.METHOD_ID,u=e.split(/\n/).map((function(t){return t.trim()})).filter(Boolean),l=Object(o["a"])(u);try{for(l.s();!(r=l.n()).done;){var d=r.value,c=d.match(/^(\d+)\s+(\d+)$/);if(c&&c[1].length===c[2].length)return null}}catch(E){l.e(E)}finally{l.f()}var m,h=new Map,I=Object(o["a"])(u);try{for(I.s();!(m=I.n()).done;){var f=m.value,_=f.match(/^(\d{6})\s+(\d+)$/);if(_){var b=_[1],p=_[2];h.has(p)||h.set(p,[]),h.get(p).push(b)}}}catch(E){I.e(E)}finally{I.f()}var U,N=[],A=Object(o["a"])(h.entries());try{for(A.s();!(U=A.n()).done;){var M=Object(a["a"])(U.value,2),g=M[0],D=M[1];N.push({methodId:s.LIUMA_ZULIU,betNumbers:D.join(","),money:g})}}catch(E){A.e(E)}finally{A.f()}return N.length?N:null}},LIUMA_MULTI_NUMBERS_EACH_FANG:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～一－。@#￥%…&!、＝=·吊赶打各]+)各(\d+)(防|防对|放对)(\d+)$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=t[1].split(a).map((function(t){return t.trim()})).filter((function(t){return 6===t.length}));if(!i.length)return null;var s=t[2],u=t[4];return[{methodId:o.LIUMA_ZULIU,betNumbers:i.join(","),money:s},{methodId:o.LIUMA_ZUSAN,betNumbers:i.join(","),money:u}]}},LIUMA_YI_YI_ONE_MONEY:{pattern:/^(\d{6})一(\d{6})一个(\d+)(元|块|米)?$/,handler:function(t,e){var r=t[1],a=t[2],o=t[3];return 6!==r.length||6!==a.length?null:[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:r+","+a,money:o}]}},LIUMA_GE_MONEY:{pattern:/^(\d{6})[，,、\s]*个(\d+)(元|块|米)?$/,handler:function(t,e){var r=t[1],a=t[2];return 6!==r.length?null:[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:r,money:a}]}},LIUMA_MULTI_LINE_ZULIU_MONEY_SUPER:{pattern:/^([\s\S]+)$/m,handler:function(t,e){var r,i=n("f9d6"),s=i.METHOD_ID,u=e.split(/\n/).map((function(t){return t.trim()})).filter(Boolean),l=new Map,d=/^(\d{6})[\-—~～@#￥%…&!、一－。\\/*,，.＝=· 　\t]+(\d+)(元|米|块)?([，,./\\*\-\=。·、 　\t]*)?$/,c=[],m=Object(o["a"])(u);try{for(m.s();!(r=m.n()).done;){var h=r.value,I=h.match(d);if(!I)return console.log('【LIUMA_MULTI_LINE_ZULIU_MONEY_SUPER】行 "'.concat(h,'" 不匹配六码格式，返回null')),null;if(I[1].length===I[2].length)return console.log('【LIUMA_MULTI_LINE_ZULIU_MONEY_SUPER】行 "'.concat(h,'" 号码和金额长度相同，可能误匹配，返回null')),null;c.push({line:h,match:I})}}catch(R){m.e(R)}finally{m.f()}console.log("【LIUMA_MULTI_LINE_ZULIU_MONEY_SUPER】所有 ".concat(c.length," 行都是六码格式，继续处理"));for(var f=0,_=c;f<_.length;f++){var b=_[f].match;if(6===b[1].length){var p=b[1],U=b[2],N=b[3]||"",A=U+N;l.has(A)||l.set(A,[]),l.get(A).push(p)}}var M,g=[],D=Object(o["a"])(l.entries());try{for(D.s();!(M=D.n()).done;){var E=Object(a["a"])(M.value,2),v=E[0],O=E[1];if(O.length>0){var y,S=v.length>0?[v.replace(/(元|米|块)$/,""),(null===(y=v.match(/(元|米|块)$/))||void 0===y?void 0:y[0])||""]:["",""],T=Object(a["a"])(S,2),L=T[0],j=T[1];g.push({methodId:s.LIUMA_ZULIU,betNumbers:O.join(","),money:L,unit:j||void 0})}}}catch(R){D.e(R)}finally{D.f()}return console.log("【LIUMA_MULTI_LINE_ZULIU_MONEY_SUPER】处理完成，返回 ".concat(g.length," 个结果组")),g.length>0?g:null}},LIUMA_MULTI_FANGDUI_SUPER:{pattern:/^([0-9]{6}(?:[\-—–―－﹣﹘⸺⸻~～@#￥%…&!、\\/*,，.一－。＝=·\s+吊赶打各]+[0-9]{6})+)[\-—–―－﹣﹘⸺⸻~～@#￥%…&!、\\/*,，.一－。＝=·\s+吊赶打各]*(防对|防|组三)([0-9]+)$/,handler:function(t,e){var r=n("f9d6"),a=r.SUPER_SEPARATORS,o=r.METHOD_ID,i=t[1].split(a).map((function(t){return t.trim()})).filter((function(t){return 6===t.length})),s=t[3];return console.log(t[3]),i.length<2?null:[{methodId:o.LIUMA_ZUSAN,betNumbers:i.join(","),money:s}]}},LIUMA_MULTI_LINE_ZULIU_ZUSAN_KEYWORD:{pattern:/^([0-9]{6}(组六|组三)\d+(元|米|块)?[，,./\*\-\=。一－。·、\s]*\n?)+(总\d+(元|米|块)?[，,./\*\-\=。一－。·、\s]*)?$/m,handler:function(t,e){var n,r=e.split(/\r?\n/).map((function(t){return t.trim()})).filter(Boolean).filter((function(t){return!/^总\d+(元|米|块)?$/.test(t)})),s=new Map,u=Object(o["a"])(r);try{for(u.s();!(n=u.n()).done;){var l=n.value,d=l.match(/^([0-9]{6})(组六|组三)(\d+)(元|米|块)?[，,./\*\-\=。一－。·、\s]*$/);if(d&&6===d[1].length){var c=d[1],m=d[2],h=d[3],I=m+"_"+h;s.has(I)||s.set(I,[]),s.get(I).push(c)}}}catch(E){u.e(E)}finally{u.f()}var f,_=[],b=Object(o["a"])(s.entries());try{for(b.s();!(f=b.n()).done;){var p=Object(a["a"])(f.value,2),U=p[0],N=p[1],A=U.split("_"),M=Object(a["a"])(A,2),g=M[0],D=M[1];_.push({methodId:"组六"===g?i["METHOD_ID"].LIUMA_ZULIU:i["METHOD_ID"].LIUMA_ZUSAN,betNumbers:N.join(","),money:D})}}catch(E){b.e(E)}finally{b.f()}return _.length?_:null}},LIUMA_MULTI_LINE_NUMBERS_PLUS_MONEY:{pattern:/^([\s\S]+)$/m,handler:function(t,e){var r=e.split(/\n/).map((function(t){return t.trim()})).filter(Boolean);if(r.length<2)return null;var a=r.slice(0,-1),o="",i="",s=r[r.length-1],u=s.match(/^(\d{6})?\s*各(\d+)[+＋](\d+)$/);if(u)u[1]&&a.push(u[1]),o=u[2],i=u[3];else{var l=s.match(/^(\d+)[+＋](\d+)$/);if(!l)return null;o=l[1],i=l[2]}return a.length&&a.every((function(t){return/^\d{6}$/.test(t)}))?[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:a.join(","),money:o},{methodId:n("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:a.join(","),money:i}]:null}},LIUMA_MULTI_LINE_TWO_NUMBERS_CAIZHONG_EACH_SUPER_V2:{pattern:/^((?:\d{6}\s+\d{6}\s*\n?)+)((?:.*?(福彩|福|3D|3d|体彩|体|排|排三)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*各\d+\s*\n?)+)$/im,handler:function(t,e){var r,a=t[1].split(/\n/).map((function(t){return t.trim()})).filter(Boolean),i=[],s=Object(o["a"])(a);try{for(s.s();!(r=s.n()).done;){var u=r.value,l=u.match(/(\d{6})\s+(\d{6})/);l&&i.push(l[1],l[2])}}catch(I){s.e(I)}finally{s.f()}if(!i.length)return null;var d,c=[],m=/(福彩|福|3D|3d|体彩|体|排|排三)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*各(\d+)/g;while(null!==(d=m.exec(t[2]))){var h=1;/体彩|体|排|排三/.test(d[1])&&(h=2),c.push({lotteryId:h,money:d[2]})}return c.length?c.map((function(t){return{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:i.join(","),money:t.money,lotteryId:t.lotteryId}})):null}},LIUMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER:{pattern:/^([\s\S]+)$/m,handler:function(t,e){var r=/(福彩|福|3D|3d)/.test(e),i=/(体彩|体|排|排三)/.test(e);if(r||i)return null;var s,u=e.split(/\r?\n/).map((function(t){return t.trim()})).filter(Boolean),l=new Map,d=new Map,c=/^([0-9]{6})[\-—~～@#￥%…&!、\\/*,一－–.。＝=·\s\/]+(\d+)\++(\d+)[元米块]*$/,m=/^([0-9]{6})[\-—~～@#￥%…&!、\\/*,一－–.。＝=·\s\/]+(\d+)[元米块]*$/,h=[],I=Object(o["a"])(u);try{for(I.s();!(s=I.n()).done;){var f=s.value,_=f.match(c);if(_)h.push({line:f,type:"zuliuZusan",match:_});else{if(_=f.match(m),!_)return console.log('【LIUMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】行 "'.concat(f,'" 不匹配六码格式，返回null')),null;h.push({line:f,type:"zuliu",match:_})}}}catch(G){I.e(G)}finally{I.f()}console.log("【LIUMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】所有 ".concat(h.length," 行都是六码格式，继续处理"));for(var b=0,p=h;b<p.length;b++){var U=p[b],N=U.match,A=U.type;if("zuliuZusan"===A){var M=N[1],g=N[2],D=N[3];l.has(g)||l.set(g,[]),l.get(g).push(M),d.has(D)||d.set(D,[]),d.get(D).push(M)}else if("zuliu"===A){var E=N[1],v=N[2];l.has(v)||l.set(v,[]),l.get(v).push(E)}}var O,y=[],S=Object(o["a"])(l.entries());try{for(S.s();!(O=S.n()).done;){var T=Object(a["a"])(O.value,2),L=T[0],j=T[1];y.push({methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:j.join(","),money:L})}}catch(G){S.e(G)}finally{S.f()}var R,Z=Object(o["a"])(d.entries());try{for(Z.s();!(R=Z.n()).done;){var H=Object(a["a"])(R.value,2),$=H[0],P=H[1];y.push({methodId:n("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:P.join(","),money:$})}}catch(G){Z.e(G)}finally{Z.f()}return console.log("【LIUMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】处理完成，返回 ".concat(y.length," 个结果组")),y.length?y:null}},LIUMA_MONEY_UNIT_FANGDUI_SUPER:{pattern:/^(\d{6})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:元|米|块)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(防|防对|组三)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*$/,handler:function(t,e){var n=t[1],r=t[2];t[3];return 6!==n.length?null:[{methodId:i["METHOD_ID"].LIUMA_ZUSAN,betNumbers:n,money:r}]}},WUMA_TO_LIUMA_MULTI_TRANSFORM:{pattern:/^((?:\d{5}[\-—~～@#￥%…&!、\\/*,，.＝=·\s]+){1,}\d{5})(各)?(转|套)[\s]*([0-9]+)\+([0-9]+)$/,handler:function(t,e){var a=n("f9d6"),o=a.SUPER_SEPARATORS,i=t[1],s=t[4],u=t[5],l=i.split(o).filter((function(t){return 5===t.length})),d=[];return l.forEach((function(t){var e=t.split(""),n="0123456789".split("").filter((function(t){return!e.includes(t)})),a=n.map((function(e){return t+e})).sort(),o=a.filter((function(t){return!d.includes(t)}));d.push.apply(d,Object(r["a"])(o))})),[{methodId:n("f9d6").METHOD_ID.LIUMA_ZULIU,betNumbers:d.join(","),money:s},{methodId:n("f9d6").METHOD_ID.LIUMA_ZUSAN,betNumbers:d.join(","),money:u}]}}};Object.keys(s).forEach((function(t){var e=s[t].pattern;"string"===typeof e&&e.endsWith("$")?s[t].pattern=e.replace(/\$$/,"[，,./*-=。·、s]*$"):e instanceof RegExp&&e.source.endsWith("$")&&(s[t].pattern=new RegExp(e.source.replace(/\$$/,"[，,./*-=。·、s]*$")))}))},7883:function(t,e,n){"use strict";n("db21")},"7b57":function(t,e,n){"use strict";n.r(e),n.d(e,"ALL_PATTERNS",(function(){return U})),n.d(e,"mergeGroups",(function(){return N})),n.d(e,"parseBetLine",(function(){return A})),n.d(e,"parseBetContent",(function(){return M}));var r=n("2909"),a=n("b85c"),o=n("5530"),i=(n("99af"),n("4de4"),n("0481"),n("a15b"),n("d81d"),n("14d9"),n("4069"),n("d3b7"),n("07ac"),n("ac1f"),n("5319"),n("1276"),n("498a"),n("0643"),n("2382"),n("4e3e"),n("a573"),n("159b"),n("f9d6"));n.d(e,"BASE_PATTERNS",(function(){return i["BASE_PATTERNS"]}));var s=n("ce3b");n.d(e,"SINGLE_CODE_PATTERNS",(function(){return s["SINGLE_CODE_PATTERNS"]}));var u=n("5c5d");n.d(e,"TWO_CODE_PATTERNS",(function(){return u["TWO_CODE_PATTERNS"]}));var l=n("9608");n.d(e,"THREE_CODE_PATTERNS",(function(){return l["THREE_CODE_PATTERNS"]}));var d=n("172b");n.d(e,"FOUR_CODE_PATTERNS",(function(){return d["FOUR_CODE_PATTERNS"]}));var c=n("47fb");n.d(e,"FIVE_CODE_PATTERNS",(function(){return c["FIVE_CODE_PATTERNS"]}));var m=n("5f85");n.d(e,"SIX_CODE_PATTERNS",(function(){return m["SIX_CODE_PATTERNS"]}));var h=n("2e8d");n.d(e,"SEVEN_CODE_PATTERNS",(function(){return h["SEVEN_CODE_PATTERNS"]}));var I=n("22f7");n.d(e,"EIGHT_CODE_PATTERNS",(function(){return I["EIGHT_CODE_PATTERNS"]}));var f=n("3506");n.d(e,"NINE_CODE_PATTERNS",(function(){return f["NINE_CODE_PATTERNS"]}));var _=n("f398"),b=n("8de0"),p=n("98b6");n.d(e,"cleanNumberString",(function(){return i["cleanNumberString"]})),n.d(e,"parseMoney",(function(){return i["parseMoney"]})),n.d(e,"checkLotteryType",(function(){return i["checkLotteryType"]})),n.d(e,"parseLotteryType",(function(){return i["parseLotteryType"]}));var U=Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])({},p["KUADU_PATTERNS"]),b["DANTUO_PATTERNS"]),s["SINGLE_CODE_PATTERNS"]),u["TWO_CODE_PATTERNS"]),l["THREE_CODE_PATTERNS"]),d["FOUR_CODE_PATTERNS"]),c["FIVE_CODE_PATTERNS"]),m["SIX_CODE_PATTERNS"]),h["SEVEN_CODE_PATTERNS"]),I["EIGHT_CODE_PATTERNS"]),f["NINE_CODE_PATTERNS"]),_["SINGLE_DOUBLE_PATTERNS"]);function N(t){var e={};return t.forEach((function(t){var n="".concat(t.methodId||"","_").concat(t.money||"");e[n]?e[n].betNumbers.push(t.betNumbers):e[n]=Object(o["a"])(Object(o["a"])({},t),{},{betNumbers:[t.betNumbers],methodId:t.methodId,danshuang:t.danshuang,daxiao:t.daxiao,dingwei:t.dingwei,hezhi:t.hezhi,money:t.money||""})})),Object.values(e).map((function(t){var e=t.betNumbers.filter(Boolean).map((function(t){return t.split(/[^0-9]+/)})).flat();return Object(o["a"])(Object(o["a"])({},t),{},{betNumbers:e.join(","),methodId:t.methodId,money:t.money||""})}))}function A(t){if(t=t.trim(),!t)return null;var e=Object(i["parseLotteryType"])(t);t=t.replace(/(福|福彩|3D|福3D|排|排三|体|体彩|福排|福体)/g,"").trim();for(var n=0,r=Object.values(U);n<r.length;n++){var a=r[n],o=a.pattern.exec(t);if(o){var s=a.handler(o,t);if(s)return s.forEach((function(t){t.lotteryId||(t.lotteryId=e?e.id:1)})),s}}return null}function M(t){var e,n=t.split(/[\n\r]+/).filter(Boolean),o=[],i=Object(a["a"])(n);try{for(i.s();!(e=i.n()).done;){var s=e.value,u=A(s);u&&o.push.apply(o,Object(r["a"])(u))}}catch(l){i.e(l)}finally{i.f()}return o}},"832b":function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("el-dialog",{staticClass:"modern-bet-dialog",staticStyle:{height:"108%",top:"-55px"},attrs:{title:t.dialogTitle,visible:t.dialogVisible,width:"600px","append-to-body":"","close-on-click-modal":!1},on:{"update:visible":function(e){t.dialogVisible=e}}},[n("el-form",{ref:"form",staticClass:"modern-bet-form",attrs:{model:t.formData,rules:t.rules,"label-width":"80px"}},[n("div",{staticClass:"info-card"},[n("div",{staticClass:"info-row"},[n("div",{staticClass:"info-group"},[n("div",{staticClass:"info-label"},[n("i",{staticClass:"el-icon-s-operation"}),n("span",[t._v("福体开关")])]),n("el-switch",{staticClass:"lottery-switch",attrs:{"active-value":"all","inactive-value":"tc","active-text":"全部彩种","inactive-text":"仅体彩",size:"small"},on:{change:t.onFutiSwitchChange},model:{value:t.futiSwitch,callback:function(e){t.futiSwitch=e},expression:"futiSwitch"}})],1)]),n("div",{staticClass:"info-row"},[n("div",{staticClass:"info-group"},[n("div",{staticClass:"info-label"},[n("i",{staticClass:"el-icon-user"}),n("span",[t._v("当前用户")])]),n("el-input",{staticClass:"compact-input",staticStyle:{width:"100px"},attrs:{disabled:"",placeholder:"未获取"},model:{value:t.currentUser.name,callback:function(e){t.$set(t.currentUser,"name",e)},expression:"currentUser.name"}})],1),n("div",{staticClass:"info-group"},[n("div",{staticClass:"info-label"},[n("i",{staticClass:"el-icon-switch-button"}),n("span",[t._v("切换用户")])]),n("el-select",{staticClass:"compact-select",staticStyle:{width:"120px"},attrs:{placeholder:"请选择用户"},model:{value:t.selectedUserId,callback:function(e){t.selectedUserId=e},expression:"selectedUserId"}},t._l(t.userList,(function(t){return n("el-option",{key:t.userId,attrs:{label:t.name,value:t.userId}})})),1),t.row?t._e():n("el-button",{staticClass:"compact-btn",attrs:{type:"primary",disabled:!t.selectedUserId},on:{click:t.switchUser}},[t._v("切换")])],1)]),n("div",{staticClass:"info-row"},[n("div",{staticClass:"info-group"},[n("div",{staticClass:"info-label"},[n("i",{staticClass:"el-icon-document"}),n("span",[t._v("流水号")])]),n("el-input",{staticClass:"compact-input",staticStyle:{width:"120px"},attrs:{placeholder:"流水号"},model:{value:t.serialNumber,callback:function(e){t.serialNumber=t._n(e)},expression:"serialNumber"}}),n("el-button",{staticClass:"compact-btn",attrs:{type:"success",loading:t.generateLoading},on:{click:t.generateNewSerialNumber}},[n("i",{staticClass:"el-icon-refresh"}),t._v(" 生成新流水号 ")]),n("el-button",{staticClass:"compact-btn",attrs:{type:"primary"},on:{click:t.showFormatDialog}},[n("i",{staticClass:"el-icon-s-operation"}),t._v(" 格式转换工具 ")]),t.showStatisticsButton?n("el-button",{staticClass:"compact-btn",attrs:{type:"success"},on:{click:t.showStatistics}},[n("i",{staticClass:"el-icon-data-analysis"}),t._v(" 下注统计 ")]):t._e()],1)]),n("div",{staticStyle:{display:"none"}},[n("el-input",{model:{value:t.formData.fc3dIssueNumber,callback:function(e){t.$set(t.formData,"fc3dIssueNumber",e)},expression:"formData.fc3dIssueNumber"}}),n("el-input",{model:{value:t.formData.tcIssueNumber,callback:function(e){t.$set(t.formData,"tcIssueNumber",e)},expression:"formData.tcIssueNumber"}})],1)]),n("div",{staticClass:"recognition-section"},[n("el-form-item",{attrs:{label:"号码识别",prop:"shibie"}},[n("div",{staticClass:"recognition-wrapper"},[n("div",{staticClass:"recognition-header"},[n("i",{staticClass:"el-icon-view"}),t.isNumberCountExceeded?t.isFirstNumberThreeDigits(t.formData.shibie)?n("span",{staticStyle:{color:"#67c23a"}},[t._v("首组为三位数，不限制组数 ("+t._s(t.numberCount)+"组)")]):n("span",{staticStyle:{color:"#f56c6c"}},[t._v("单次识别不可超过30组，你现在是"+t._s(t.numberCount)+"组")]):n("span",[t._v("智能识别")]),n("el-button",{attrs:{size:"mini",type:"primary"},on:{click:t.showBetFormatDialog}},[t._v("玩法格式")])],1),n("el-input",{class:["recognition-textarea",{"number-count-exceeded":t.isNumberCountExceeded}],attrs:{type:"textarea",rows:4,placeholder:"请将号码输入此处，识别后自动生成下注号码",size:"small",resize:"vertical"},on:{input:t.handleNumberRecognition},model:{value:t.formData.shibie,callback:function(e){t.$set(t.formData,"shibie",e)},expression:"formData.shibie"}})],1)])],1),n("div",{staticClass:"bet-groups-list-wrapper"},[n("div",{staticClass:"bet-groups-list"},t._l(t.formData.betGroups,(function(e,r){return n("div",{key:r,class:["bet-group-card",{"bet-group-even":r%2===1}]},[n("div",{staticStyle:{display:"flex","align-items":"center",gap:"8px","margin-bottom":"8px"}},[n("el-form-item",{staticStyle:{flex:"1","margin-bottom":"0"},attrs:{label:"玩法名称",prop:"betGroups."+r+".methodId"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",remote:"","remote-method":t.filterMethod,loading:t.loadingGameMethods,clearable:"",placeholder:"请选择玩法名称",size:"small"},on:{change:function(n){return t.onMethodChange(e)}},model:{value:e.methodId,callback:function(n){t.$set(e,"methodId",n)},expression:"group.methodId"}},t._l(t.filteredGameMethods,(function(e){return n("el-option",{key:e.methodId,attrs:{label:e.methodName,value:e.methodId}},[t._v(" "+t._s(e.methodName)+" ")])})),1)],1),n("el-form-item",{staticStyle:{width:"200px","margin-bottom":"0"},attrs:{label:"金额",prop:"betGroups."+r+".money"}},[n("el-input",{attrs:{placeholder:"请输入金额",size:"small"},on:{input:t.updateTotals},model:{value:e.money,callback:function(n){t.$set(e,"money",n)},expression:"group.money"}})],1),n("el-button",{attrs:{type:"danger",icon:"el-icon-delete",circle:"",size:"mini"},on:{click:function(e){return t.removeBetGroup(r)}}})],1),29===e.methodId||30===e.methodId||2===e.methodId||[21,22,23,24,25,26,27,37,38,39,40,41,42,43].includes(e.methodId)?n("div",{staticStyle:{"margin-left":"80px","margin-bottom":"8px"}},[29===e.methodId?[n("div",{staticClass:"option-group"},[n("span",{staticClass:"option-label",staticStyle:{"font-size":"16px","font-weight":"600"}},[t._v("单双：")]),n("el-radio-group",{attrs:{size:"medium"},model:{value:e.danshuang,callback:function(n){t.$set(e,"danshuang",n)},expression:"group.danshuang"}},[n("el-radio-button",{attrs:{label:200}},[t._v("单")]),n("el-radio-button",{attrs:{label:201}},[t._v("双")])],1)],1)]:30===e.methodId?[n("div",{staticClass:"option-group"},[n("span",{staticClass:"option-label",staticStyle:{"font-size":"16px","font-weight":"600"}},[t._v("大小：")]),n("el-radio-group",{attrs:{size:"medium"},model:{value:e.daxiao,callback:function(n){t.$set(e,"daxiao",n)},expression:"group.daxiao"}},[n("el-radio-button",{attrs:{label:300}},[t._v("大")]),n("el-radio-button",{attrs:{label:301}},[t._v("小")])],1)],1)]:2===e.methodId?[n("div",{staticClass:"option-group"},[n("span",{staticClass:"option-label",staticStyle:{"font-size":"16px","font-weight":"600"}},[t._v("定位：")]),n("el-radio-group",{attrs:{size:"medium"},model:{value:e.dingwei,callback:function(n){t.$set(e,"dingwei",n)},expression:"group.dingwei"}},[n("el-radio-button",{attrs:{label:100}},[t._v("百位")]),n("el-radio-button",{attrs:{label:101}},[t._v("十位")]),n("el-radio-button",{attrs:{label:102}},[t._v("个位")])],1)],1)]:[21,22,23,24,25,26,27,37,38,39,40,41,42,43].includes(e.methodId)?[n("div",{staticClass:"option-group"},[n("span",{staticClass:"option-label",staticStyle:{"font-size":"16px","font-weight":"600"}},[t._v("和值：")]),n("el-radio-group",{attrs:{size:"medium"},model:{value:e.hezhi,callback:function(n){t.$set(e,"hezhi",n)},expression:"group.hezhi"}},t._l(t.getHezhiOptions(e.methodId),(function(e){return n("el-radio-button",{key:e,attrs:{label:e}},[t._v("和值"+t._s(e))])})),1)],1)]:t._e()],2):t._e(),t.isSpecialMethod(e.methodId)?t._e():n("el-form-item",{staticStyle:{"margin-bottom":"8px"},attrs:{label:"下注号码",prop:"betGroups."+r+".betNumbers"}},[n("el-input",{attrs:{value:t.getDisplayBetNumbers(e),type:"textarea",rows:3,placeholder:"请输入下注号码"},on:{input:function(e){return t.onBetNumbersInput(e,r)}}})],1),n("el-form-item",{staticStyle:{"margin-bottom":"8px"},attrs:{label:"彩种类型"}},[n("div",{staticStyle:{display:"flex","align-items":"center"}},[n("el-radio-group",{staticStyle:{"margin-right":"16px","flex-shrink":"0"},attrs:{size:"small",fill:2===e.lotteryId?"#1890ff":"#ff4949"},model:{value:e.lotteryId,callback:function(n){t.$set(e,"lotteryId",n)},expression:"group.lotteryId"}},[n("el-radio-button",{staticStyle:{"margin-right":"6px"},attrs:{label:1,"data-lottery":"fc3d"}},[t._v("福彩3D")]),n("el-radio-button",{staticStyle:{"margin-right":"0"},attrs:{label:2,"data-lottery":"tc"}},[t._v("体彩排三")])],1),n("div",{staticClass:"group-summary"},[n("div",{staticClass:"summary-item amount"},[n("div",{staticClass:"summary-icon"},[n("i",{staticClass:"el-icon-wallet"})]),n("div",{staticClass:"summary-content"},[n("div",{staticClass:"summary-label"},[t._v("总额")]),n("div",{staticClass:"summary-value"},[t._v("￥"+t._s(t.getGroupAmount(e)))])])]),n("div",{staticClass:"summary-item bets"},[n("div",{staticClass:"summary-icon"},[n("i",{staticClass:"el-icon-tickets"})]),n("div",{staticClass:"summary-content"},[n("div",{staticClass:"summary-label"},[t._v("投注数")]),n("div",{staticClass:"summary-value"},[t._v(t._s(t.getGroupBets(e))+"注")])])])])],1)])],1)})),0),n("button",{staticClass:"bet-group-add-btn",attrs:{title:"添加投注组合"},on:{click:t.addBetGroup}})]),n("el-row",{attrs:{gutter:20}},[n("el-col",{attrs:{span:12}},[n("el-statistic",{attrs:{title:"下注总额",value:t.totalAmount,size:"small"}},[n("template",{slot:"prefix"},[t._v("￥")])],2)],1),n("el-col",{attrs:{span:12}},[n("el-statistic",{attrs:{title:"总投注数",value:t.totalBets,size:"small"}},[n("template",{slot:"suffix"},[t._v("注")])],2)],1)],1),"修改下注管理"===t.dialogTitle?n("el-form-item",{attrs:{label:"是否结算",prop:"jiesuan"}},[n("el-switch",{attrs:{"active-value":1,"inactive-value":0,"active-text":"已结算","inactive-text":"未结算",size:"small"},model:{value:t.formData.jiesuan,callback:function(e){t.$set(t.formData,"jiesuan",e)},expression:"formData.jiesuan"}})],1):t._e()],1),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"center",width:"100%"}},[n("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitForm}},[t._v("确 定")]),n("el-button",{attrs:{size:"small"},on:{click:t.close}},[t._v("取 消")])],1)])],1),n("el-dialog",{staticClass:"format-dialog",attrs:{title:"格式转换工具",visible:t.formatDialogVisible,width:"650px","append-to-body":"","close-on-click-modal":!1},on:{"update:visible":function(e){t.formatDialogVisible=e}}},[n("div",{staticClass:"format-dialog-content"},[n("el-tabs",{staticClass:"format-tabs",attrs:{type:"card"},model:{value:t.formatTab,callback:function(e){t.formatTab=e},expression:"formatTab"}},[n("el-tab-pane",{attrs:{name:"unified"}},[n("span",{attrs:{slot:"label"},slot:"label"},[n("i",{staticClass:"el-icon-edit"}),t._v(" 字符转换 ")]),n("div",{staticClass:"tab-content"},[n("div",{staticClass:"input-section"},[n("div",{staticClass:"section-header"},[n("i",{staticClass:"el-icon-upload2"}),n("span",[t._v("输入内容")])]),n("el-input",{staticClass:"format-input",attrs:{type:"textarea",rows:5,placeholder:"智能识别并转换：汉字金额(如二十)、逗号、句号、冒号、下划线、加号等转为空格&#10;示例：123,456。。789：：：012___345➕678二十元"},on:{input:t.handleUnifiedFormatInput},model:{value:t.unifiedFormatInput,callback:function(e){t.unifiedFormatInput=e},expression:"unifiedFormatInput"}})],1),n("div",{staticClass:"arrow-section"},[n("i",{staticClass:"el-icon-bottom"})]),n("div",{staticClass:"output-section"},[n("div",{staticClass:"section-header"},[n("i",{staticClass:"el-icon-download"}),n("span",[t._v("转换结果")])]),n("el-input",{staticClass:"format-output",attrs:{type:"textarea",rows:5,value:t.unifiedFormatOutput,readonly:"",placeholder:"转换结果将在这里显示..."}})],1),n("div",{staticClass:"button-section"},[n("el-button",{staticClass:"action-button",attrs:{type:"primary",icon:"el-icon-check",disabled:!t.unifiedFormatOutput},on:{click:t.insertUnifiedFormatResult}},[t._v(" 插入到号码识别 ")]),n("el-button",{staticClass:"cancel-button",attrs:{icon:"el-icon-close"},on:{click:function(e){t.formatDialogVisible=!1}}},[t._v(" 关闭 ")])],1)])]),n("el-tab-pane",{attrs:{name:"chain"}},[n("span",{attrs:{slot:"label"},slot:"label"},[n("i",{staticClass:"el-icon-s-grid"}),t._v(" 链子分类 ")]),n("div",{staticClass:"tab-content"},[n("div",{staticClass:"input-section"},[n("div",{staticClass:"section-header"},[n("i",{staticClass:"el-icon-upload2"}),n("span",[t._v("输入内容")])]),n("el-input",{staticClass:"format-input",attrs:{type:"textarea",rows:5,placeholder:"按号码长度自动分类到不同行\n示例：123-1234-568-56789-503-9012-12345678"},on:{input:t.handleChainFormatInput},model:{value:t.chainFormatInput,callback:function(e){t.chainFormatInput=e},expression:"chainFormatInput"}})],1),n("div",{staticClass:"arrow-section"},[n("i",{staticClass:"el-icon-bottom"})]),n("div",{staticClass:"output-section"},[n("div",{staticClass:"section-header"},[n("i",{staticClass:"el-icon-download"}),n("span",[t._v("分类结果")])]),n("el-input",{staticClass:"format-output",attrs:{type:"textarea",rows:6,value:t.chainFormatOutput,readonly:"",placeholder:"分类结果将在这里显示..."}})],1),n("div",{staticClass:"button-section"},[n("el-button",{staticClass:"action-button",attrs:{type:"primary",icon:"el-icon-check",disabled:!t.chainFormatOutput},on:{click:t.insertChainFormatResult}},[t._v(" 插入到号码识别 ")]),n("el-button",{staticClass:"cancel-button",attrs:{icon:"el-icon-close"},on:{click:function(e){t.formatDialogVisible=!1}}},[t._v(" 关闭 ")])],1)])]),n("el-tab-pane",{attrs:{name:"amount"}},[n("span",{attrs:{slot:"label"},slot:"label"},[n("i",{staticClass:"el-icon-money"}),t._v(" 金额整合 ")]),n("div",{staticClass:"tab-content"},[n("div",{staticClass:"input-section"},[n("div",{staticClass:"section-header"},[n("i",{staticClass:"el-icon-upload2"}),n("span",[t._v("输入内容")])]),n("el-input",{staticClass:"format-input",attrs:{type:"textarea",rows:6,placeholder:"将相同金额且相同位数的号码合并到一行\n  支持每行一个或一行内空格分隔多个\n  示例：57-50  59-50  79-50  88-100\n  结果： 57-59-79-50\n        88-100"},on:{input:t.handleAmountFormatInput},model:{value:t.amountFormatInput,callback:function(e){t.amountFormatInput=e},expression:"amountFormatInput"}})],1),n("div",{staticClass:"arrow-section"},[n("i",{staticClass:"el-icon-bottom"})]),n("div",{staticClass:"output-section"},[n("div",{staticClass:"section-header"},[n("i",{staticClass:"el-icon-download"}),n("span",[t._v("整合结果")])]),n("el-input",{staticClass:"format-output",attrs:{type:"textarea",rows:6,value:t.amountFormatOutput,readonly:"",placeholder:"整合结果将在这里显示..."}})],1),n("div",{staticClass:"button-section"},[n("el-button",{staticClass:"action-button",attrs:{type:"primary",icon:"el-icon-check",disabled:!t.amountFormatOutput},on:{click:t.insertAmountFormatResult}},[t._v(" 插入到号码识别 ")]),n("el-button",{staticClass:"cancel-button",attrs:{icon:"el-icon-close"},on:{click:function(e){t.formatDialogVisible=!1}}},[t._v(" 关闭 ")])],1)])]),n("el-tab-pane",{attrs:{name:"remove"}},[n("span",{attrs:{slot:"label"},slot:"label"},[n("i",{staticClass:"el-icon-delete"}),t._v(" 去除分隔符 ")]),n("div",{staticClass:"tab-content"},[n("div",{staticClass:"input-section"},[n("div",{staticClass:"section-header"},[n("i",{staticClass:"el-icon-upload2"}),n("span",[t._v("输入内容")])]),n("el-input",{staticClass:"format-input",attrs:{type:"textarea",rows:6,placeholder:"去除所有分隔符和英文字母，只保留汉字、数字和换行\n示例：123,456。。789：：：abc___def➕ghi二十元ABC\n结果：123456789二十元"},on:{input:t.handleRemoveFormatInput},model:{value:t.removeFormatInput,callback:function(e){t.removeFormatInput=e},expression:"removeFormatInput"}})],1),n("div",{staticClass:"arrow-section"},[n("i",{staticClass:"el-icon-bottom"})]),n("div",{staticClass:"output-section"},[n("div",{staticClass:"section-header"},[n("i",{staticClass:"el-icon-download"}),n("span",[t._v("清理结果")])]),n("el-input",{staticClass:"format-output",attrs:{type:"textarea",rows:6,value:t.removeFormatOutput,readonly:"",placeholder:"清理结果将在这里显示..."}})],1),n("div",{staticClass:"button-section"},[n("el-button",{staticClass:"action-button",attrs:{type:"primary",icon:"el-icon-check",disabled:!t.removeFormatOutput},on:{click:t.insertRemoveFormatResult}},[t._v(" 插入到号码识别 ")]),n("el-button",{staticClass:"cancel-button",attrs:{icon:"el-icon-close"},on:{click:function(e){t.formatDialogVisible=!1}}},[t._v(" 关闭 ")])],1)])])],1)],1)]),n("bet-statistics",{attrs:{visible:t.statisticsVisible},on:{"update:visible":function(e){t.statisticsVisible=e}}}),n("bet-format-dialog",{ref:"betFormatDialog"})],1)},a=[],o=n("ade3"),i=n("b85c"),s=n("3835"),u=n("5530"),l=n("c14f"),d=n("1da1"),c=(n("99af"),n("4de4"),n("c740"),n("caad"),n("a15b"),n("d81d"),n("14d9"),n("13d5"),n("fb6a"),n("4e82"),n("a434"),n("e9c4"),n("a9e3"),n("b64b"),n("d3b7"),n("07ac"),n("ac1f"),n("00b4"),n("25f0"),n("2532"),n("3ca3"),n("466d"),n("5319"),n("1276"),n("2ca0"),n("498a"),n("0643"),n("2382"),n("4e3e"),n("a573"),n("9d4a"),n("9a9a"),n("159b"),n("ddb0"),n("b2f0")),m=n("b775"),h=n("f97d"),I=n("35d4"),f=n("0068"),_=n("0901"),b=n("c0c7"),p=n("6455"),U=n("9b7b"),N={name:"BetDialog",components:{BetStatistics:p["default"],BetFormatDialog:U["default"]},props:{visible:{type:Boolean,default:!1},title:{type:String,default:"新增下注"},row:{type:Object,default:null}},data:function(){return{dialogVisible:!1,dialogTitle:"",totalAmount:0,totalBets:0,pendingRequests:[],gameMethodsData:[],filteredGameMethods:[],loadingGameMethods:!1,formData:{fc3dIssueNumber:"",tcIssueNumber:"",shibie:"",betGroups:[{methodId:void 0,money:"",betNumbers:"",danshuang:null,daxiao:null,dingwei:null,hezhi:null,lotteryId:1}]},numberCount:0,isNumberCountExceeded:!1,rules:{betGroups:{methodId:[{required:!0,message:"请选择玩法",trigger:"change"}],money:[{required:!0,message:"请输入金额",trigger:"blur"}]}},localForm:{fc3dIssueNumber:"",tcIssueNumber:"",shibie:"",betGroups:[]},currentUser:{name:""},userList:[],selectedUserId:null,futiSwitch:sessionStorage.getItem("futiSwitch")||"all",formatDialogVisible:!1,formatInput:"",formatOutput:"",formatTab:"unified",colonFormatInput:"",colonFormatOutput:"",moneyFormatInput:"",moneyFormatOutput:"",underscoreFormatInput:"",underscoreFormatOutput:"",unifiedFormatInput:"",unifiedFormatOutput:"",chainFormatInput:"",chainFormatOutput:"",amountFormatInput:"",amountFormatOutput:"",removeFormatInput:"",removeFormatOutput:"",serialNumber:1,maxSerialNumber:1,generateLoading:!1,originalUserId:null,originalSerialNumber:null,statisticsVisible:!1,hasStatisticsPermission:!1}},computed:{showStatisticsButton:function(){return this.hasStatisticsPermission}},watch:{visible:function(t){if(this.dialogVisible=t,t){if(this.checkPlayerStatus(),this.getAllUsers(),this.checkStatisticsPermission(),this.row&&this.row.userId)this.selectedUserId=this.row.userId;else{var e=sessionStorage.getItem("User_Id");this.selectedUserId=e?Number(e):null}this.initSerialNumber(),this.row&&this.initFormData(),this.onFutiSwitchChange(this.futiSwitch)}},dialogVisible:function(t){this.$emit("update:visible",t),t||this.resetForm()},selectedUserId:{handler:function(t,e){this.row&&this.row.betId&&null!==this.originalUserId&&void 0!==e&&this.handleUserIdChange(t,e)},immediate:!1},formatDialogVisible:function(t){t||(this.formatInput="",this.formatOutput="",this.colonFormatInput="",this.colonFormatOutput="",this.moneyFormatInput="",this.moneyFormatOutput="",this.underscoreFormatInput="",this.underscoreFormatOutput="",this.unifiedFormatInput="",this.unifiedFormatOutput="",this.chainFormatInput="",this.chainFormatOutput="",this.amountFormatInput="",this.amountFormatOutput="",this.removeFormatInput="",this.removeFormatOutput="")}},created:function(){this.getGameMethods()},methods:{showBetFormatDialog:function(){this.$refs.betFormatDialog.show()},checkPlayerStatus:function(){var t=this;return Object(d["a"])(Object(l["a"])().m((function e(){var n,r;return Object(l["a"])().w((function(e){while(1)switch(e.n){case 0:return e.p=0,e.n=1,Object(_["b"])();case 1:if(n=e.v,500!==n.code||!n.needCreatePlayer){e.n=2;break}return t.$confirm(n.msg+" 是否立即前往创建？","提示",{confirmButtonText:"前往创建",cancelButtonText:"取消",type:"warning"}).then((function(){t.dialogVisible=!1,t.$emit("update:visible",!1),t.$router.push("/game/customer")})).catch((function(){t.dialogVisible=!1,t.$emit("update:visible",!1)})),e.a(2);case 2:200===n.code&&n.hasPlayers&&(n.defaultUserId,t.initializeDialog()),e.n=4;break;case 3:e.p=3,r=e.v,console.error("检查玩家状态失败:",r),t.$message.error("检查玩家状态失败，请重试"),t.dialogVisible=!1,t.$emit("update:visible",!1);case 4:return e.a(2)}}),e,null,[[0,3]])})))()},initializeDialog:function(){this.getCurrentIssueNumber(),this.getGameMethods(),this.getCurrentUser()},getGameMethods:function(){var t=this;return this.loadingGameMethods=!0,Object(m["a"])({url:"/game/method/list",method:"get",params:{pageNum:1,pageSize:100}}).then((function(e){var n=[];if(200===e.code)if(Array.isArray(e.data))n=e.data;else if(e.rows&&Array.isArray(e.rows))n=e.rows;else if("string"===typeof e.data)try{var r=JSON.parse(e.data);n=Array.isArray(r)?r:[]}catch(a){n=[]}t.gameMethodsData=n,t.filteredGameMethods=n,t.loadingGameMethods=!1})).catch((function(){t.gameMethodsData=[],t.filteredGameMethods=[],t.loadingGameMethods=!1}))},show:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"新增下注";this.dialogTitle=t,this.dialogVisible=!0},close:function(){this.dialogVisible=!1,this.$emit("update:visible",!1),this.$emit("cancel")},resetForm:function(){this.formData={fc3dIssueNumber:"",tcIssueNumber:"",shibie:"",betGroups:[{methodId:void 0,money:"",betNumbers:"",danshuang:null,daxiao:null,dingwei:null,hezhi:null,lotteryId:1}]},this.totalAmount=0,this.totalBets=0,this.numberCount=0,this.isNumberCountExceeded=!1},addBetGroup:function(){var t=this,e={methodId:void 0,money:"",betNumbers:"",danshuang:null,daxiao:null,dingwei:null,hezhi:null,lotteryId:"tc"===this.futiSwitch?2:1};this.formData.betGroups||(this.formData.betGroups=[]),this.formData.betGroups.push(Object(u["a"])({},e)),this.$nextTick((function(){t.updateTotals()}))},removeBetGroup:function(t){var e=this;this.formData.betGroups.length<=1?this.formData.betGroups=[{methodId:void 0,money:"",betNumbers:"",danshuang:null,daxiao:null,dingwei:null,hezhi:null,lotteryId:1}]:this.formData.betGroups.splice(t,1),this.$nextTick((function(){e.updateTotals(),e.$forceUpdate()}))},updateTotals:function(){var t=this;this.totalAmount=this.formData.betGroups.reduce((function(e,n){return e+Number(t.getGroupAmount(n))}),0),this.totalBets=this.formData.betGroups.reduce((function(e,n){return e+Number(t.getGroupBets(n))}),0)},getHezhiOptions:function(t){var e={21:[7,20],22:[8,19],23:[9,18],24:[10,17],25:[11,16],26:[12,15],27:[13,14],37:[0,27],38:[1,26],39:[2,25],40:[3,24],41:[4,23],42:[5,22],43:[6,21]};return e[t]||[]},onMethodChange:function(t){var e=this.formData.betGroups.findIndex((function(e){return e===t}));if(e>-1){var n=Object(u["a"])(Object(u["a"])({},t),{},{danshuang:null,daxiao:null,dingwei:null,hezhi:null});if(29===n.methodId)n.danshuang=200;else if(30===n.methodId)n.daxiao=300;else if(2===n.methodId)n.dingwei=100;else if([21,22,23,24,25,26,27,37,38,39,40,41,42,43].includes(n.methodId)){var r=this.getHezhiOptions(n.methodId);r&&r.length>0&&(n.hezhi=r[0])}this.$set(this.formData.betGroups,e,n),this.$forceUpdate()}},getCurrentIssueNumber:function(){var t=this,e=this.$loading({lock:!0,text:"正在获取期号...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)",customClass:"bet-dialog-loading"});Promise.all([Object(f["c"])(1).catch((function(t){return console.error("获取福彩3D期号失败:",t),{code:500,msg:"获取失败"}})),Object(f["c"])(2).catch((function(t){return console.error("获取体彩排三期号失败:",t),{code:500,msg:"获取失败"}}))]).then((function(e){var n=Object(s["a"])(e,2),r=n[0],a=n[1];200===r.code?t.formData.fc3dIssueNumber=r.msg:(console.error("获取福彩3D期号失败:",r),t.$message.error("获取福彩3D期号失败")),200===a.code?t.formData.tcIssueNumber=a.msg:(console.error("获取体彩排三期号失败:",a),t.$message.error("获取体彩排三期号失败"))})).finally((function(){e.close()}))},calculateNumberCount:function(t){if(!t||!t.trim())return 0;var e=t.trim(),n=/\d{2,9}/g,r=e.match(n);if(!r)return 0;var a,o=e.split(/[\r\n]+/).filter((function(t){return t.trim()})),s=0,u=Object(i["a"])(o);try{for(u.s();!(a=u.n()).done;){var l=a.value,d=l.trim();if(d){var c=d.match(n);if(c){var m=/[元块米钱赶组直吊放防各]/,h=m.test(d),I=/[+＋\/]/.test(d);s+=h||I?Math.max(0,c.length-1):c.length}}}}catch(f){u.e(f)}finally{u.f()}return s},smartPreprocessInput:function(t){if(!t)return t;var e=t.split(/[—\-\n\r]+/).map((function(t){return t.trim()})).filter(Boolean);if(e.length>1){var n=/(赶|组|直|吊|放|防|元|各)[^\d]*\d+.*$/,r=e[e.length-1];if(n.test(r))return e.slice(0,-1).join("—")+"—"+r}return t},isFirstNumberThreeDigits:function(t){if(!t||!t.trim())return!1;var e=t.trim(),n=e.match(/\d{2,9}/);return!!n&&3===n[0].length},handleNumberRecognition:function(t){var e=this;this.numberCount=this.calculateNumberCount(t);var n=this.isFirstNumberThreeDigits(t);if(this.isNumberCountExceeded=!n&&this.numberCount>30,t){if(!this.isNumberCountExceeded){var r=Object(c["handleNumberRecognition"])(t);if(!r||!r.groups||!r.groups.some((function(t){return t.methodId}))){var a,o=t.split(/[\r\n]+/).map((function(t){return t.trim()})).filter(Boolean),s=[],l=Object(i["a"])(o);try{for(l.s();!(a=l.n()).done;){var d=a.value,m=Object(c["handleNumberRecognition"])(d);m&&m.groups&&m.groups.length>0&&(s=s.concat(m.groups))}}catch(f){l.e(f)}finally{l.f()}r={groups:s}}if(r){if(r.groups&&r.groups.length>0)this.$set(this.formData,"betGroups",[]),r.groups.forEach((function(t){var n={methodId:t.methodId,danshuang:t.danshuang,daxiao:t.daxiao,dingwei:t.dingwei,hezhi:t.hezhi,betNumbers:t.betNumbers,money:null!=t.money?String(t.money):"",lotteryId:"tc"===e.futiSwitch?2:t.lotteryId||1};e.formData.betGroups.push(n)}));else{var h={methodId:r.methodId,danshuang:r.danshuang,daxiao:r.daxiao,dingwei:r.dingwei,hezhi:r.hezhi,betNumbers:r.betNumbers,money:null!=r.money?String(r.money):"",lotteryId:"tc"===this.futiSwitch?2:r.lotteryId||1};this.$set(this.formData,"betGroups",[h])}var I=this.formData.betGroups.map((function(t){return Object(u["a"])(Object(u["a"])({},t),{},{danshuang:t.danshuang||null,daxiao:t.daxiao||null,dingwei:t.dingwei||null,hezhi:t.hezhi||null,betNumbers:t.betNumbers||"",money:t.money||null,lotteryId:t.lotteryId||1})}));this.$set(this.localForm,"betGroups",I),this.$nextTick((function(){e.updateTotals(),e.formData.betGroups.forEach((function(t,n){var r=Object(u["a"])({},t);e.$set(e.formData.betGroups,n,r)})),e.$forceUpdate()}))}}}else this.formData.betGroups=[{methodId:void 0,money:"",betNumbers:"",danshuang:null,daxiao:null,dingwei:null,hezhi:null,lotteryId:"tc"===this.futiSwitch?2:1}]},formatBetNumbersToDisplay:function(t){try{if(!t)return"";if(!t.startsWith("{"))return t;var e="string"===typeof t?JSON.parse(t):t;return e.numbers&&Array.isArray(e.numbers)?e.numbers.map((function(t){if(void 0!==t.kuadu)return"跨度".concat(t.kuadu);if(void 0!==t.danma&&void 0!==t.tuoma)return"胆".concat(t.danma,"拖").concat(t.tuoma);var e=Object.values(t);return e.join("")})).join(","):t}catch(n){return console.error("格式化下注号码失败:",n,"jsonStr:",t),t}},getDisplayBetNumbers:function(t){if(!t.betNumbers)return"";if(t.methodId>=44&&t.methodId<=59||t.methodId>=60&&t.methodId<=69)try{return t.betNumbers.startsWith("{")&&t.betNumbers.includes("numbers")?this.formatBetNumbersToDisplay(t.betNumbers):t.betNumbers}catch(e){return console.error("格式化显示号码失败:",e),t.betNumbers}return t.betNumbers},onBetNumbersInput:function(t,e){var n=this.formData.betGroups[e];n.methodId>=44&&n.methodId<=59||n.methodId>=60&&n.methodId,this.$set(this.formData.betGroups,e,Object(u["a"])(Object(u["a"])({},n),{},{betNumbers:t})),this.updateTotals()},initFormData:function(){var t=this;try{this.originalUserId=this.row.userId,this.originalSerialNumber=this.row.serialNumber,this.formData.fc3dIssueNumber=this.row.issueNumber,this.formData.tcIssueNumber=this.row.issueNumber;var e=this.formatBetNumbersToDisplay(this.row.betNumbers),n={methodId:this.row.methodId,money:null!=this.row.money?String(this.row.money):"",betNumbers:e,danshuang:this.row.danshuang,daxiao:this.row.daxiao,dingwei:this.row.dingwei,hezhi:this.row.hezhi,lotteryId:this.row.lotteryId};this.formData.betGroups=[n],this.row.shibie&&(this.formData.shibie=this.row.shibie),this.row.serialNumber&&(this.serialNumber=this.row.serialNumber),this.$nextTick((function(){t.updateTotals(),t.$forceUpdate()}))}catch(r){console.error("初始化表单数据失败:",r)}},formatNumbersForRequest:function(t,e){if(!t)return{numbers:[]};if(e>=44&&e<=59)return this.formatDantuoNumbers(t,e);if(e>=60&&e<=69)return this.formatKuaduNumbers(t,e);var n=t.split(",").filter((function(t){return t.trim()})),r={1:function(t){return{a:parseInt(t)}},2:function(t){return{a:parseInt(t)}},33:function(t){return{a:parseInt(t)}},34:function(t){return{a:t}},35:function(t){return{a:t}},36:function(t){return{a:t}},3:function(t){var e=t.split("").map((function(t){return parseInt(t)}));return{a:e[0],b:e[1]}},4:function(t){var e=t.split("").map((function(t){return parseInt(t)}));return{a:e[0],b:e[1]}},5:function(t){var e=t.split("").map((function(t){return parseInt(t)}));return{a:e[0],b:e[1],c:e[2]}},6:function(t){var e=t.split("").map((function(t){return parseInt(t)}));return{a:e[0],b:e[1],c:e[2]}},7:function(t){var e=t.split("").map((function(t){return parseInt(t)}));return{a:e[0],b:e[1],c:e[2]}},100:function(t){var e=t.split("").map((function(t){return parseInt(t)}));return{a:e[0],b:e[1],c:e[2]}},8:function(t){var e=t.split("").map((function(t){return parseInt(t)}));return{a:e[0],b:e[1],c:e[2],d:e[3]}},9:function(t){var e=t.split("").map((function(t){return parseInt(t)}));return{a:e[0],b:e[1],c:e[2],d:e[3]}},10:function(t){var e=t.split("").map((function(t){return parseInt(t)}));return{a:e[0],b:e[1],c:e[2],d:e[3],e:e[4]}},11:function(t){var e=t.split("").map((function(t){return parseInt(t)}));return{a:e[0],b:e[1],c:e[2],d:e[3],e:e[4]}},12:function(t){var e=t.split("").map((function(t){return parseInt(t)}));return{a:e[0],b:e[1],c:e[2],d:e[3],e:e[4],f:e[5]}},13:function(t){var e=t.split("").map((function(t){return parseInt(t)}));return{a:e[0],b:e[1],c:e[2],d:e[3],e:e[4],f:e[5]}},14:function(t){var e=t.split("").map((function(t){return parseInt(t)}));return{a:e[0],b:e[1],c:e[2],d:e[3],e:e[4],f:e[5],g:e[6]}},15:function(t){var e=t.split("").map((function(t){return parseInt(t)}));return{a:e[0],b:e[1],c:e[2],d:e[3],e:e[4],f:e[5],g:e[6]}},16:function(t){var e=t.split("").map((function(t){return parseInt(t)}));return{a:e[0],b:e[1],c:e[2],d:e[3],e:e[4],f:e[5],g:e[6],h:e[7]}},17:function(t){var e=t.split("").map((function(t){return parseInt(t)}));return{a:e[0],b:e[1],c:e[2],d:e[3],e:e[4],f:e[5],g:e[6],h:e[7]}},18:function(t){var e=t.split("").map((function(t){return parseInt(t)}));return{a:e[0],b:e[1],c:e[2],d:e[3],e:e[4],f:e[5],g:e[6],h:e[7],i:e[8]}},19:function(t){var e=t.split("").map((function(t){return parseInt(t)}));return{a:e[0],b:e[1],c:e[2],d:e[3],e:e[4],f:e[5],g:e[6],h:e[7],i:e[8]}},20:function(){return{}},31:function(){return{}},21:function(){return{}},22:function(){return{}},23:function(){return{}},24:function(){return{}},25:function(){return{}},26:function(){return{}},27:function(){return{}},37:function(){return{}},38:function(){return{}},39:function(){return{}},40:function(){return{}},41:function(){return{}},42:function(){return{}},43:function(){return{}},28:function(){return{}},29:function(){return{}},30:function(){return{}}},a=r[e];return a?{numbers:n.map((function(t){return a(t.trim())}))}:{numbers:[]}},submitForm:function(){var t=this,e=this.validateMoney();e.valid?this.$refs["form"].validate((function(e){if(e){var n=t.$loading({lock:!0,text:"正在提交下注...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),r=t.formData.betGroups.map((function(e,n){var r=t.formatNumbersForRequest(e.betNumbers,e.methodId),a=sessionStorage.getItem("sysUserId"),o={lotteryId:e.lotteryId,issueNumber:1===e.lotteryId?t.formData.fc3dIssueNumber:t.formData.tcIssueNumber,methodId:e.methodId,userId:Number(t.selectedUserId),sysUserId:a?Number(a):null,betNumbers:JSON.stringify(r),money:e.money,danshuang:e.danshuang,daxiao:e.daxiao,dingwei:e.dingwei,hezhi:e.hezhi,totalAmount:t.getGroupAmount(e),totalBets:t.getGroupBets(e),shibie:t.formData.shibie,betTime:t.formatDate(new Date),serialNumber:t.serialNumber};return t.row&&0===n&&(o.betId=t.row.betId),o})),a=t.row?"/game/record":"/game/record/batch",o=t.row?"put":"post",i=t.row?r[0]:r;Object(m["a"])({url:a,method:o,data:i}).then((function(e){if(200===e.code)t.$modal.msgSuccess("操作成功"),t.$emit("success"),t.formData.shibie="",t.formData.betGroups=[{methodId:void 0,money:"",betNumbers:"",danshuang:null,daxiao:null,dingwei:null,hezhi:null,lotteryId:1}],t.updateTotals(),t.row&&t.close();else if(e.data&&Array.isArray(e.data.failedIndices)){var n=t.formData.betGroups.filter((function(t,n){return e.data.failedIndices.includes(n)}));t.$set(t.formData,"betGroups",n),t.$modal.msgError("".concat(e.data.failedIndices.length,"个下注失败，请重试"))}else e._errorHandled||t.$modal.msgError(e.msg||"操作失败，请重试")})).catch((function(e){console.error("网络请求失败:",e),t.$modal.msgError("网络连接失败，请检查网络后重试")})).finally((function(){n.close(),t.updateTotals()}))}})):this.$alert(e.message,"错误",{confirmButtonText:"确定",type:"error",dangerouslyUseHTMLString:!0})},getGroupBets:function(t){return t.methodId>=44&&t.methodId<=59||t.methodId>=60&&t.methodId<=69?1:this.isSpecialMethod(t.methodId)?[21,22,23,24,25,26,27,37,38,39,40,41,42,43].includes(t.methodId)?t.hezhi?1:0:[29,30].includes(t.methodId)?t.danshuang||t.daxiao?1:0:[20,31].includes(t.methodId)?1:0:t.betNumbers?t.betNumbers.split(",").filter((function(t){return t.trim()})).length:0},getGroupAmount:function(t){var e=this.getGroupBets(t),n=Number(t.money)||0;return e*n},isSpecialMethod:function(t){return[20,21,22,23,24,25,26,27,29,30,31,37,38,39,40,41,42,43].includes(t)},formatDantuoNumbers:function(t,e){var n=t.split("拖");if(2!==n.length)return{numbers:[]};var r=n[0].replace(/[^0-9]/g,""),a=n[1].replace(/[^0-9,，\s]/g,"").split(/[,，\s]+/).filter((function(t){return t&&t!==r}));return{numbers:[{danma:r,tuoma:a.join(",")}]}},formatKuaduNumbers:function(t,e){if(console.log("formatKuaduNumbers 输入:",t,"methodId:",e),t.startsWith("{"))try{var n=JSON.parse(t);return console.log("已是JSON格式:",n),n}catch(i){console.error("JSON解析失败:",i)}var r=null,a=t.match(/跨度(\d)/);if(a?r=a[1]:(a=t.match(/跨(\d)[\s\-]*\d+/),a?r=a[1]:e>=60&&e<=69&&(r=(e-60).toString())),console.log("解析出的跨度值:",r),null===r)return console.warn("无法解析跨度值，返回空数组"),{numbers:[]};var o={numbers:[{kuadu:r}]};return console.log("formatKuaduNumbers 输出:",o),o},updateGroupValue:function(t,e,n){var r=this.formData.betGroups.findIndex((function(e){return e===t}));if(r>-1){var a=Object(u["a"])(Object(u["a"])({},this.formData.betGroups[r]),{},Object(o["a"])({},e,n));this.$set(this.formData.betGroups,r,a),this.$forceUpdate()}},formatDate:function(t){var e=function(t){return t<10?"0".concat(t):t},n=t.getFullYear(),r=e(t.getMonth()+1),a=e(t.getDate()),o=e(t.getHours()),i=e(t.getMinutes()),s=e(t.getSeconds());return"".concat(n,"-").concat(r,"-").concat(a," ").concat(o,":").concat(i,":").concat(s)},getCurrentUser:function(){var t=sessionStorage.getItem("User_Id");t?(this.selectedUserId=Number(t),this.loadUserById(t)):this.initializeFirstUser()},loadUserById:function(t){var e=this;Object(m["a"])({url:"/game/customer/".concat(t),method:"get"}).then((function(t){200===t.code&&t.data?e.currentUser=t.data:e.currentUser={name:"未知用户"}})).catch((function(t){console.error("获取用户信息失败:",t),e.currentUser={name:"未知用户"}}))},initializeFirstUser:function(){var t=this;Object(m["a"])({url:"/game/customer/list",method:"get",params:{pageNum:1,pageSize:1e3}}).then((function(e){if(200===e.code&&e.rows&&e.rows.length>0){var n=e.rows[0];t.selectedUserId=n.userId,t.currentUser=n,sessionStorage.setItem("User_Id",n.userId)}else console.warn("当前用户没有玩家数据"),t.currentUser={name:"无玩家数据"},t.selectedUserId=null})).catch((function(e){console.error("获取玩家列表失败:",e),t.currentUser={name:"获取失败"},t.selectedUserId=null}))},getAllUsers:function(){var t=this;Object(m["a"])({url:"/game/customer/list",method:"get",params:{pageNum:1,pageSize:1e3}}).then((function(e){200===e.code&&e.rows?t.userList=e.rows:t.userList=[]}))},switchUser:function(){this.selectedUserId&&(sessionStorage.setItem("User_Id",this.selectedUserId),this.getCurrentUser(),this.$message.success("切换用户成功"))},onFutiSwitchChange:function(t){sessionStorage.setItem("futiSwitch",t),"tc"===t?this.formData&&this.formData.betGroups&&this.formData.betGroups.forEach((function(t){t.lotteryId=2,null!=t.money&&"string"!==typeof t.money&&(t.money=String(t.money))})):this.formData&&this.formData.betGroups&&this.formData.betGroups.forEach((function(t){null!=t.money&&"string"!==typeof t.money&&(t.money=String(t.money))})),this.$forceUpdate()},filterMethod:function(t){this.filteredGameMethods=t?this.gameMethodsData.filter((function(e){return e.methodName&&-1!==e.methodName.indexOf(t)||"3"===t&&e.methodName&&-1!==e.methodName.indexOf("三")})):this.gameMethodsData},showFormatDialog:function(){this.formatDialogVisible=!0,this.formatInput="",this.formatOutput="",this.formatTab="unified"},showStatistics:function(){this.statisticsVisible=!0},validateMoney:function(){for(var t=0;t<this.formData.betGroups.length;t++){var e=this.formData.betGroups[t],n=e.money,r=t+1,a=null!=n?String(n):"";if(!a||""===a.trim())return{valid:!1,message:'第<span style="color: #ff4757; font-weight: bold; font-size: 16px;">'.concat(r,'</span>组下注金额不能为<span style="color: #ff4757; font-weight: bold; font-size: 16px;">空</span>')};var o=Number(a.trim());if(isNaN(o)||o<=0)return{valid:!1,message:'第<span style="color: #ff4757; font-weight: bold; font-size: 16px;">'.concat(r,'</span>组下注金额不能为<span style="color: #ff4757; font-weight: bold; font-size: 16px;">"').concat(a,'"</span>，请修改')}}return{valid:!0,message:""}},checkStatisticsPermission:function(){var t=this;return Object(d["a"])(Object(l["a"])().m((function e(){var n,r;return Object(l["a"])().w((function(e){while(1)switch(e.n){case 0:return e.p=0,e.n=1,Object(b["getStatisticsPermission"])();case 1:n=e.v,n&&200===n.code&&void 0!==n.data?t.hasStatisticsPermission="1"===n.data:t.hasStatisticsPermission=!1,e.n=3;break;case 2:e.p=2,r=e.v,console.error("获取用户权限失败:",r),t.hasStatisticsPermission=!1;case 3:return e.a(2)}}),e,null,[[0,2]])})))()},handleFormatInput:function(){var t=this,e=(this.formatInput||"").replace(/[。．.]/g,"-");e=e.replace(/-+/g,"-");var n=e.split("\n").map((function(e){return t.replaceChineseNumber(e)}));n=this.autoConvertChineseMoney(n),this.formatOutput=n.filter((function(t){return t})).join("\n")},insertFormatResult:function(){this.formData.shibie=this.formatOutput,this.formatDialogVisible=!1,this.handleNumberRecognition(this.formatOutput)},handleColonFormatInput:function(){var t=this,e=(this.colonFormatInput||"").replace(/[：:]/g,"-");e=e.replace(/-+/g,"-");var n=e.split("\n").map((function(e){return t.replaceChineseNumber(e)}));n=this.autoConvertChineseMoney(n),this.colonFormatOutput=n.filter((function(t){return t})).join("\n")},insertColonFormatResult:function(){this.formData.shibie=this.colonFormatOutput,this.formatDialogVisible=!1,this.handleNumberRecognition(this.colonFormatOutput)},handleMoneyFormatInput:function(){var t=this,e=(this.moneyFormatInput||"").split("\n").map((function(e){return t.replaceChineseNumber(e)})),n=/([一二三四五六七八九十百千万两零]+)(元|块|米)?/g;e=e.map((function(e){var r,a;while(null!==(a=n.exec(e)))r=a;if(r){var o=t.chineseToNumber(r[1]),i=r[2]||"",s=r.index,u=s+r[0].length,l=e.slice(0,s);return/[\/\s,，.。-]+$/.test(l)||(l+="/"),l+o+i+e.slice(u)}return e})),this.moneyFormatOutput=e.filter((function(t){return t})).join("\n")},insertMoneyFormatResult:function(){this.formData.shibie=this.moneyFormatOutput,this.formatDialogVisible=!1,this.handleNumberRecognition(this.moneyFormatOutput)},handleUnifiedFormatInput:function(){var t=this.unifiedFormatInput||"",e=t.split("\n").map((function(t){return t.trim()})).filter(Boolean);e=this.autoConvertChineseMoney(e),t=e.join("\n"),t=this.replaceChineseNumber(t),t=t.replace(/[,，]+/g," "),t=t.replace(/[。．.]+/g," "),t=t.replace(/[：:]+/g," "),t=t.replace(/_+/g," "),t=t.replace(/[➕＋﹢✚✛✜✙❇️❌❎]+/g," "),t=t.replace(/\s+/g," "),this.unifiedFormatOutput=t.split("\n").map((function(t){return t.trim()})).filter(Boolean).join("\n")},insertUnifiedFormatResult:function(){this.formData.shibie=this.unifiedFormatOutput,this.formatDialogVisible=!1,this.handleNumberRecognition(this.unifiedFormatOutput)},handleChainFormatInput:function(){var t=this.chainFormatInput||"";t=this.replaceChineseNumber(t);var e=/\d+/g,n=t.match(e)||[],r={},a={};n.forEach((function(t,e){var n=t.length;r[n]||(r[n]=[],a[n]=e),r[n].push(t)}));var o=Object.keys(r).sort((function(t,e){return a[t]-a[e]})),i=[];o.forEach((function(t){var e=r[t];if(e.length>0){var n=e.join(" ");i.push(n)}})),this.chainFormatOutput=i.join("\n")},insertChainFormatResult:function(){this.formData.shibie=this.chainFormatOutput,this.formatDialogVisible=!1,this.handleNumberRecognition(this.chainFormatOutput)},handleAmountFormatInput:function(){var t=this,e=this.amountFormatInput||"",n=e.split("\n").filter((function(t){return t.trim()})),r={},a=[];n.forEach((function(e){var n=e.trim();if(n){var o=n.split(/\s+/).filter((function(t){return t.trim()}));1===o.length?t.processAmountFormatItem(o[0],r,a):o.forEach((function(e){t.processAmountFormatItem(e,r,a)}))}}));var o=[];a.forEach((function(t){var e=r[t];if(e&&e.numbers.length>0){var n=e.numbers.join("-")+"-"+e.amount;o.push(n)}})),this.amountFormatOutput=o.join("\n")},processAmountFormatItem:function(t,e,n){var r=t.trim();if(r){var a=r.lastIndexOf("-");if(-1!==a){var o=r.substring(0,a).trim(),i=r.substring(a+1).trim();if(o&&i){var s=o.length,u="".concat(i,"_").concat(s);e[u]||(e[u]={amount:i,length:s,numbers:[]},n.push(u)),e[u].numbers.push(o)}}}},insertAmountFormatResult:function(){this.formData.shibie=this.amountFormatOutput,this.formatDialogVisible=!1,this.handleNumberRecognition(this.amountFormatOutput)},handleRemoveFormatInput:function(){var t=this.removeFormatInput||"",e=t.replace(/[^\u4e00-\u9fff\d\n\r]/g,"");this.removeFormatOutput=e.split("\n").map((function(t){return t.trim()})).filter((function(t,e,n){return t||e>0&&n[e-1]})).join("\n").replace(/\n{3,}/g,"\n\n")},insertRemoveFormatResult:function(){this.formData.shibie=this.removeFormatOutput,this.formatDialogVisible=!1,this.handleNumberRecognition(this.removeFormatOutput)},handleUnderscoreFormatInput:function(){var t=this,e=(this.underscoreFormatInput||"").replace(/_+/g," "),n=e.split("\n").map((function(e){return t.replaceChineseNumber(e)}));n=this.autoConvertChineseMoney(n),this.underscoreFormatOutput=n.filter((function(t){return t})).join("\n")},insertUnderscoreFormatResult:function(){this.formData.shibie=this.underscoreFormatOutput,this.formatDialogVisible=!1,this.handleNumberRecognition(this.underscoreFormatOutput)},replaceChineseNumber:function(t){var e=this,n=/[零一二两三四五六七八九十百千万]+/g;return t.replace(n,(function(t){if(1===t.length&&!/[十百千万]/.test(t)){var n={"零":0,"一":1,"二":2,"两":2,"三":3,"四":4,"五":5,"六":6,"七":7,"八":8,"九":9};return n[t]||t}return e.chineseToNumber(t)}))},chineseToNumber:function(t){if(!t)return 0;if(/^\d+$/.test(t))return parseInt(t,10);for(var e=t.replace(/元|块|米/g,""),n={"零":0,"一":1,"二":2,"两":2,"三":3,"四":4,"五":5,"六":6,"七":7,"八":8,"九":9},r={"十":10,"百":100,"千":1e3,"万":1e4},a=0,o=0,i=!1,s=0;s<e.length;s++){var u=e[s];if(n.hasOwnProperty(u))o=n[u],i=!0;else if(r.hasOwnProperty(u)){var l=r[u];"十"!==u||i||(o=1),1e4===l?(a=(a+o)*l,o=0):(a+=o*l,o=0),i=!1}}return a+=o,a},autoConvertChineseMoney:function(t){var e=this;"string"===typeof t&&(t=t.split(/\n/));var n=/([零一二三四五六七八九十百千万两]+)(元|块|米)?/g;return t.map((function(t){var r,a;n.lastIndex=0;while(null!==(a=n.exec(t)))/[一二三四五六七八九十百千万两零]/.test(a[1])&&(r=a);if(r){var o=e.chineseToNumber(r[1]),i=r[2]||"",s=r.index,u=s+r[0].length,l=t.slice(0,s);return l&&!/[\s\/,，.。-]$/.test(l)&&(l+=" "),l+o+i+t.slice(u)}return t}))},initSerialNumber:function(){var t=this;if(this.row&&this.row.serialNumber)return this.serialNumber=this.row.serialNumber,this.originalSerialNumber=this.row.serialNumber,void Object(h["a"])().then((function(e){t.maxSerialNumber=e?Number(e):1})).catch((function(){t.maxSerialNumber=1}));Object(h["a"])().then((function(e){null==e?(t.maxSerialNumber=1,t.serialNumber=1):(t.maxSerialNumber=Number(e),t.serialNumber=Number(e))})).catch((function(){t.maxSerialNumber=1,t.serialNumber=1}))},increaseSerialNumber:function(){var t=Number(this.maxSerialNumber)+1;this.serialNumber<t&&(this.serialNumber=t)},generateNewSerialNumber:function(){var t=this;this.generateLoading=!0,Object(I["c"])().then((function(e){200===e.code?(t.serialNumber=e.serialNumber,t.maxSerialNumber=e.serialNumber,t.$message.success("生成新流水号: ".concat(e.serialNumber))):t.$message.error("生成流水号失败: "+e.msg)})).catch((function(e){console.error("生成流水号失败:",e),t.$message.error("生成流水号失败")})).finally((function(){t.generateLoading=!1}))},handleUserIdChange:function(t,e){t===this.originalUserId?(this.serialNumber=this.originalSerialNumber,this.$message.info("已恢复原流水号: ".concat(this.originalSerialNumber))):this.autoGenerateSerialNumber()},autoGenerateSerialNumber:function(){var t=this;Object(I["c"])().then((function(e){200===e.code?(t.serialNumber=e.serialNumber,t.maxSerialNumber=e.serialNumber,t.$message.info("用户变更，自动生成新流水号: ".concat(e.serialNumber))):t.$message.error("自动生成流水号失败: "+e.msg)})).catch((function(e){console.error("自动生成流水号失败:",e),t.$message.error("自动生成流水号失败")}))}}},A=N,M=(n("414a"),n("7883"),n("2877")),g=Object(M["a"])(A,r,a,!1,null,"5f13ee76",null);e["default"]=g.exports},"8de0":function(t,e,n){"use strict";n.r(e),n.d(e,"DANTUO_PATTERNS",(function(){return a}));n("99af"),n("4de4"),n("a15b"),n("14d9"),n("d3b7"),n("ac1f"),n("00b4"),n("5319"),n("1276"),n("0643"),n("2382");var r=n("f9d6"),a={DANTUO_DEFAULT_ZULIU:{pattern:/^(?:胆|胆码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:拖|拖码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9,，\s]+?)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?$/,handler:function(t,e){var n=t[1],a=t[2],o=t[3],i=a.replace(/[\s,，]+/g,"").split("").filter((function(t){return t&&t!==n}));if(i.length<2||i.length>9)return console.warn("胆拖默认组六拖码数量不合法: ".concat(i.length,"，应为2-9个")),null;var s=Object(r["getDantuoZuliuMethodId"])(i.length);return[{methodId:s,betNumbers:"胆".concat(n,"拖").concat(i.join(",")),money:o}]}},DANTUO_EXPLICIT_ZUSAN:{pattern:/^(?:胆|胆码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:拖|拖码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9,，\s]+?)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?组三$/,handler:function(t,e){var n=t[1],a=t[2],o=t[3],i=a.replace(/[\s,，]+/g,"").split("").filter((function(t){return t&&t!==n}));if(i.length<2||i.length>9)return console.warn("胆拖组三拖码数量不合法: ".concat(i.length,"，应为2-9个")),null;var s=Object(r["getDantuoZusanMethodId"])(i.length);return[{methodId:s,betNumbers:"胆".concat(n,"拖").concat(i.join(",")),money:o}]}},DANTUO_EXPLICIT_ZULIU:{pattern:/^(?:胆|胆码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:拖|拖码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9,，\s]+?)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?组六$/,handler:function(t,e){var n=t[1],a=t[2],o=t[3],i=a.replace(/[\s,，]+/g,"").split("").filter((function(t){return t&&t!==n}));if(i.length<2||i.length>9)return console.warn("胆拖组六拖码数量不合法: ".concat(i.length,"，应为2-9个")),null;var s=Object(r["getDantuoZuliuMethodId"])(i.length);return[{methodId:s,betNumbers:"胆".concat(n,"拖").concat(i.join(",")),money:o}]}},SIMPLE_DANTUO_DEFAULT_ZULIU:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:拖|拖码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9,，\s]+?)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?$/,handler:function(t,e){var n=t[1],a=t[2],o=t[3],i=a.replace(/[\s,，]+/g,"").split("").filter((function(t){return t&&t!==n}));if(i.length<2||i.length>9)return console.warn("简化胆拖默认组六拖码数量不合法: ".concat(i.length,"，应为2-9个")),null;var s=Object(r["getDantuoZuliuMethodId"])(i.length);return[{methodId:s,betNumbers:"胆".concat(n,"拖").concat(i.join(",")),money:o}]}},SIMPLE_DANTUO_ZUSAN:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:拖|拖码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9,，\s]+?)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?组三$/,handler:function(t,e){var n=t[1],a=t[2],o=t[3],i=a.replace(/[\s,，]+/g,"").split("").filter((function(t){return t&&t!==n}));if(i.length<2||i.length>9)return console.warn("简化胆拖组三拖码数量不合法: ".concat(i.length,"，应为2-9个")),null;var s=Object(r["getDantuoZusanMethodId"])(i.length);return[{methodId:s,betNumbers:"胆".concat(n,"拖").concat(i.join(",")),money:o}]}},SIMPLE_DANTUO_ZULIU:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:拖|拖码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9,，\s]+?)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?组六$/,handler:function(t,e){var n=t[1],a=t[2],o=t[3],i=a.replace(/[\s,，]+/g,"").split("").filter((function(t){return t&&t!==n}));if(i.length<2||i.length>9)return console.warn("简化胆拖组六拖码数量不合法: ".concat(i.length,"，应为2-9个")),null;var s=Object(r["getDantuoZuliuMethodId"])(i.length);return[{methodId:s,betNumbers:"胆".concat(n,"拖").concat(i.join(",")),money:o}]}},DANTUO_ZUSAN:{pattern:/^胆(\d)拖([\d,，\s]+)组三[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*(\d+)(?:元|块|米)?$/,handler:function(t,e){var n=t[1],a=t[2],o=t[3],i=a.split(/[,，\s]+/).filter((function(t){return t&&t!==n}));if(i.length<2||i.length>9)return console.warn("胆拖组三拖码数量不合法: ".concat(i.length,"，应为2-9个")),null;var s=Object(r["getDantuoZusanMethodId"])(i.length);return[{methodId:s,betNumbers:"胆".concat(n,"拖").concat(i.join(",")),money:o}]}},DANTUO_ZULIU:{pattern:/^胆(\d)拖([\d,，\s]+)组六[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*(\d+)(?:元|块|米)?$/,handler:function(t,e){var n=t[1],a=t[2],o=t[3],i=a.split(/[,，\s]+/).filter((function(t){return t&&t!==n}));if(i.length<2||i.length>9)return console.warn("胆拖组六拖码数量不合法: ".concat(i.length,"，应为2-9个")),null;var s=Object(r["getDantuoZuliuMethodId"])(i.length);return[{methodId:s,betNumbers:"胆".concat(n,"拖").concat(i.join(",")),money:o}]}},DANTUO_BOTH:{pattern:/^胆(\d)拖([\d,，\s]+)(?:组三组六|组六组三)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*各?(\d+)(?:元|块|米)?$/,handler:function(t,e){var n=t[1],a=t[2],o=t[3],i=a.split(/[,，\s]+/).filter((function(t){return t&&t!==n}));if(i.length<2||i.length>9)return console.warn("胆拖组三组六拖码数量不合法: ".concat(i.length,"，应为2-9个")),null;var s=Object(r["getDantuoZusanMethodId"])(i.length),u=Object(r["getDantuoZuliuMethodId"])(i.length);return[{methodId:s,betNumbers:"胆".concat(n,"拖").concat(i.join(",")),money:o},{methodId:u,betNumbers:"胆".concat(n,"拖").concat(i.join(",")),money:o}]}},DANTUO_DEFAULT_BOTH:{pattern:/^胆(\d)拖([\d,，\s]+)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*各?(\d+)(?:元|块|米)?$/,handler:function(t,e){if(/组三|组六/.test(e))return null;var n=t[1],a=t[2],o=t[3],i=a.split(/[,，\s]+/).filter((function(t){return t&&t!==n}));if(i.length<2||i.length>9)return console.warn("胆拖默认拖码数量不合法: ".concat(i.length,"，应为2-9个")),null;var s=Object(r["getDantuoZusanMethodId"])(i.length),u=Object(r["getDantuoZuliuMethodId"])(i.length);return[{methodId:s,betNumbers:"胆".concat(n,"拖").concat(i.join(",")),money:o},{methodId:u,betNumbers:"胆".concat(n,"拖").concat(i.join(",")),money:o}]}},DANTUO_WITH_LOTTERY_PREFIX:{pattern:/^(?:福彩?|体彩?|排三?|3D)?胆(\d)拖([\d,，\s]+)(?:组三|组六)?[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*(\d+)(?:元|块|米)?$/,handler:function(t,e){var n=t[1],a=t[2],o=t[3],i=a.split(/[,，\s]+/).filter((function(t){return t&&t!==n}));if(i.length<2||i.length>9)return console.warn("胆拖带前缀拖码数量不合法: ".concat(i.length,"，应为2-9个")),null;var s=1;/体彩|排三/.test(e)&&(s=2);var u=[];if(/组三/.test(e)){var l=Object(r["getDantuoZusanMethodId"])(i.length);u.push({methodId:l,betNumbers:"胆".concat(n,"拖").concat(i.join(",")),money:o,lotteryId:s})}else if(/组六/.test(e)){var d=Object(r["getDantuoZuliuMethodId"])(i.length);u.push({methodId:d,betNumbers:"胆".concat(n,"拖").concat(i.join(",")),money:o,lotteryId:s})}else{var c=Object(r["getDantuoZusanMethodId"])(i.length),m=Object(r["getDantuoZuliuMethodId"])(i.length);u.push({methodId:c,betNumbers:"胆".concat(n,"拖").concat(i.join(",")),money:o,lotteryId:s}),u.push({methodId:m,betNumbers:"胆".concat(n,"拖").concat(i.join(",")),money:o,lotteryId:s})}return u}},DANTUO_SEPARATED_NUMBERS_TYPE_BEFORE_MONEY:{pattern:/^(?:胆|胆码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:拖|拖码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*((?:\d[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*){2,9})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(组三|组六)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?$/,handler:function(t){var e,n=t[1],a=t[2],o=t[3],i=t[4],s=a.replace(/[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]/g,"").split("").filter((function(t){return t&&t!==n}));return s.length<2||s.length>9?(console.warn("胆拖分隔数字拖码数量不合法: ".concat(s.length,"，应为2-9个")),null):(e="组三"===o?Object(r["getDantuoZusanMethodId"])(s.length):Object(r["getDantuoZuliuMethodId"])(s.length),[{methodId:e,betNumbers:"胆".concat(n,"拖").concat(s.join(",")),money:i}])}},DANTUO_SEPARATED_NUMBERS_TYPE_AFTER_MONEY:{pattern:/^(?:胆|胆码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:拖|拖码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*((?:\d[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*){2,9})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+(组三|组六)$/,handler:function(t){var e,n=t[1],a=t[2],o=t[3],i=t[4],s=a.replace(/[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]/g,"").split("").filter((function(t){return t&&t!==n}));return s.length<2||s.length>9?(console.warn("胆拖分隔数字拖码数量不合法: ".concat(s.length,"，应为2-9个")),null):(e="组三"===i?Object(r["getDantuoZusanMethodId"])(s.length):Object(r["getDantuoZuliuMethodId"])(s.length),[{methodId:e,betNumbers:"胆".concat(n,"拖").concat(s.join(",")),money:o}])}},DANTUO_ZULIU_ZUSAN_COMBO:{pattern:/^(?:胆|胆码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:拖|拖码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9,，\s]+?)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*\+[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?$/,handler:function(t){var e=t[1],n=t[2],a=t[3],o=t[4],i=n.replace(/[\s,，]+/g,"").split("").filter((function(t){return t&&t!==e}));if(i.length<2||i.length>9)return console.warn("胆拖组合格式拖码数量不合法: ".concat(i.length,"，应为2-9个")),null;var s=Object(r["getDantuoZusanMethodId"])(i.length),u=Object(r["getDantuoZuliuMethodId"])(i.length);return[{methodId:u,betNumbers:"胆".concat(e,"拖").concat(i.join(",")),money:a},{methodId:s,betNumbers:"胆".concat(e,"拖").concat(i.join(",")),money:o}]}},SIMPLE_DANTUO_ZULIU_ZUSAN_COMBO:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:拖|拖码)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9,，\s]+?)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*\+[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?$/,handler:function(t){var e=t[1],n=t[2],a=t[3],o=t[4],i=n.replace(/[\s,，]+/g,"").split("").filter((function(t){return t&&t!==e}));if(i.length<2||i.length>9)return console.warn("简化胆拖组合格式拖码数量不合法: ".concat(i.length,"，应为2-9个")),null;var s=Object(r["getDantuoZusanMethodId"])(i.length),u=Object(r["getDantuoZuliuMethodId"])(i.length);return[{methodId:u,betNumbers:"胆".concat(e,"拖").concat(i.join(",")),money:a},{methodId:s,betNumbers:"胆".concat(e,"拖").concat(i.join(",")),money:o}]}}}},"98b6":function(t,e,n){"use strict";n.r(e),n.d(e,"KUADU_PATTERNS",(function(){return i})),n.d(e,"isKuaduMethod",(function(){return s})),n.d(e,"getKuaduMethodId",(function(){return u})),n.d(e,"getKuaduValue",(function(){return l}));var r=n("b85c"),a=(n("d81d"),n("14d9"),n("e9c4"),n("b64b"),n("d3b7"),n("4d63"),n("c607"),n("ac1f"),n("2c3e"),n("25f0"),n("8a79"),n("5319"),n("0643"),n("4e3e"),n("a573"),n("159b"),n("f9d6")),o={0:a["METHOD_ID"].KUADU_0,1:a["METHOD_ID"].KUADU_1,2:a["METHOD_ID"].KUADU_2,3:a["METHOD_ID"].KUADU_3,4:a["METHOD_ID"].KUADU_4,5:a["METHOD_ID"].KUADU_5,6:a["METHOD_ID"].KUADU_6,7:a["METHOD_ID"].KUADU_7,8:a["METHOD_ID"].KUADU_8,9:a["METHOD_ID"].KUADU_9},i={KUADU_VALUE_FIRST:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9])[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:跨|跨度)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*$/,handler:function(t){var e=parseInt(t[1]),n=t[2];return e<0||e>9?(console.warn("跨度值超出范围: ".concat(e,"，应为0-9")),null):[{methodId:o[e],betNumbers:JSON.stringify({numbers:[{kuadu:e.toString()}]}),money:n}]},priority:95},KUADU_VALUE_MIDDLE:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:跨|跨度)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9])[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*$/,handler:function(t){var e=parseInt(t[1]),n=t[2];return e<0||e>9?(console.warn("跨度值超出范围: ".concat(e,"，应为0-9")),null):[{methodId:o[e],betNumbers:JSON.stringify({numbers:[{kuadu:e.toString()}]}),money:n}]},priority:94},KUADU_VALUE_FIRST_KEYWORD_LAST:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9])[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:跨|跨度)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*$/,handler:function(t){var e=parseInt(t[1]),n=t[2];return e<0||e>9?(console.warn("跨度值超出范围: ".concat(e,"，应为0-9")),null):[{methodId:o[e],betNumbers:JSON.stringify({numbers:[{kuadu:e.toString()}]}),money:n}]},priority:93},MULTI_KUADU_VALUE_FIRST:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9]{2,})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:跨|跨度)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*$/,handler:function(t){var e,n=t[1],a=t[2],i=n.split("").map((function(t){return parseInt(t)})),s=[],u=Object(r["a"])(i);try{for(u.s();!(e=u.n()).done;){var l=e.value;l<0||l>9?console.warn("跨度值超出范围: ".concat(l,"，应为0-9")):s.push({methodId:o[l],betNumbers:"跨度".concat(l),money:a})}}catch(d){u.e(d)}finally{u.f()}return s.length>0?s:null},priority:96},MULTI_KUADU_VALUE_MIDDLE:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:跨|跨度)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9]{2,})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*$/,handler:function(t){var e,n=t[1],a=t[2],i=n.split("").map((function(t){return parseInt(t)})),s=[],u=Object(r["a"])(i);try{for(u.s();!(e=u.n()).done;){var l=e.value;l<0||l>9?console.warn("跨度值超出范围: ".concat(l,"，应为0-9")):s.push({methodId:o[l],betNumbers:JSON.stringify({numbers:[{kuadu:l.toString()}]}),money:a})}}catch(d){u.e(d)}finally{u.f()}return s.length>0?s:null},priority:95},MULTI_KUADU_VALUE_FIRST_KEYWORD_LAST:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*([0-9]{2,})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|块|米)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:跨|跨度)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*$/,handler:function(t){var e,n=t[1],a=t[2],i=n.split("").map((function(t){return parseInt(t)})),s=[],u=Object(r["a"])(i);try{for(u.s();!(e=u.n()).done;){var l=e.value;l<0||l>9?console.warn("跨度值超出范围: ".concat(l,"，应为0-9")):s.push({methodId:o[l],betNumbers:JSON.stringify({numbers:[{kuadu:l.toString()}]}),money:a})}}catch(d){u.e(d)}finally{u.f()}return s.length>0?s:null},priority:94}};function s(t){return t>=60&&t<=69}function u(t){return o[t]}function l(t){return t-a["METHOD_ID"].KUADU_0}Object.keys(i).forEach((function(t){var e=i[t].pattern;"string"===typeof e&&e.endsWith("$")?i[t].pattern=e.replace(/\$$/,"[，,./*-=。·、s]*$"):e instanceof RegExp&&e.source.endsWith("$")&&(i[t].pattern=new RegExp(e.source.replace(/\$$/,"[，,./*-=。·、s]*$")))}))},a2bf6:function(t,e,n){"use strict";var r=n("e8b5"),a=n("07fa"),o=n("3511"),i=n("0366"),s=function(t,e,n,u,l,d,c,m){var h,I,f=l,_=0,b=!!c&&i(c,m);while(_<u)_ in n&&(h=b?b(n[_],_,e):n[_],d>0&&r(h)?(I=a(h),f=s(t,e,h,I,f,d-1)-1):(o(f+1),t[f]=h),f++),_++;return f};t.exports=s},b2f0:function(t,e,n){"use strict";n.r(e),n.d(e,"handleNumberRecognition",(function(){return f})),n.d(e,"handleSmartRecognition",(function(){return _}));var r=n("5530"),a=n("b85c"),o=n("53ca"),i=n("2909"),s=(n("99af"),n("4de4"),n("caad"),n("a15b"),n("d81d"),n("14d9"),n("e9c4"),n("4ec9"),n("b64b"),n("d3b7"),n("ac1f"),n("00b4"),n("6062"),n("1e70"),n("79a4"),n("c1a1"),n("8b00"),n("a4e7"),n("1e5a"),n("72c3"),n("2532"),n("3ca3"),n("466d"),n("5319"),n("1276"),n("498a"),n("0643"),n("76d6"),n("2382"),n("4e3e"),n("a573"),n("9a9a"),n("159b"),n("ddb0"),n("7b57"));function u(t,e){if(!t||0===t.length)return null;var r=n("f9d6").METHOD_ID,a=0,o=e&&e.match(/(\d{2,9})/);o&&(a=o[1].length);var s=t.filter((function(t){return t.every((function(t){return t.betNumbers&&t.betNumbers.split(",").every((function(t){return t.length===a}))&&t.methodId!==r.DUDAN}))}));0===s.length&&(s=t.filter((function(t){return t.every((function(t){return t.betNumbers&&t.betNumbers.split(",").every((function(t){return t.length===a}))}))}))),0===s.length&&(s=t);var u=Math.max.apply(Math,Object(i["a"])(s.map((function(t){return t.length})))),l=s.filter((function(t){return t.length===u}));if(1===l.length)return l[0];var d=Math.max.apply(Math,Object(i["a"])(l.map((function(t){return Math.max.apply(Math,Object(i["a"])(t.map((function(t){return(t.betNumbers||"").length}))))}))));if(l=l.filter((function(t){return Math.max.apply(Math,Object(i["a"])(t.map((function(t){return(t.betNumbers||"").length}))))===d})),1===l.length)return l[0];var c=Math.max.apply(Math,Object(i["a"])(l.map((function(t){return new Set(t.map((function(t){return t.methodId}))).size}))));if(l=l.filter((function(t){return new Set(t.map((function(t){return t.methodId}))).size===c})),1===l.length)return l[0];var m=[8,10,12,14,16,18],h=[9,11,13,15,17,19];function I(t){var e=t.map((function(t){return t.methodId}));return e.some((function(t){return m.includes(t)}))?2:e.some((function(t){return h.includes(t)}))?1:0}var f=Math.max.apply(Math,Object(i["a"])(l.map(I)));return l=l.filter((function(t){return I(t)===f})),l[0]}function l(t){return t.replace(/[零一二三四五六七八九十百千万亿]+注/g,"")}function d(t){var e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];console.log('\n【getBestPatternResult】开始处理文本: "'.concat(t,'", strict模式: ').concat(e));var n=t;t=l(t),n!==t&&console.log("【getBestPatternResult】去除中文注数后:",t),/[*Xx]\d/.test(t)?(console.log("【getBestPatternResult】检测到两码定位格式，保护*号"),t=t.replace(/^[\s\-/+\.,、|~!@#$%^&；'\"]+|[\s\-/+\.,、|~!@#$%^&；'\"]+$/g,"")):t=t.replace(/^[\s\-*/+\.,、|~!@#$%^&；'\"]+|[\s\-*/+\.,、|~!@#$%^&；'\"]+$/g,""),t=t.replace(/共\s*\d+(注|元|米|块)?(\s*\+.*)?$/g,(function(t,e,n){return n||""})),console.log("【getBestPatternResult】最终处理后的文本:",t);var r=[],a=[];for(var i in console.log("【getBestPatternResult】开始遍历所有正则模式，总数:",Object.keys(s["ALL_PATTERNS"]).length),s["ALL_PATTERNS"]){var d=s["ALL_PATTERNS"][i],c=d.pattern,m=d.handler,h=null;Date.now();try{h=t.match(c)}catch(D){console.error("【getBestPatternResult】正则 ".concat(i," 匹配报错:"),D);continue}Date.now();if(c&&"function"===typeof m&&h&&h[0]===t){console.log('【getBestPatternResult】✅ 正则 "'.concat(i,'" 匹配成功!')),console.log("【getBestPatternResult】匹配详情:",h),a.push(i);var I=null;Date.now();try{I=m.length>=3?m(h,t,e):m(h,t)}catch(D){console.log("【getBestPatternResult】正则 ".concat(i," handler执行出错，尝试兼容模式:"),D),I=m(h,t)}Date.now();console.log('【getBestPatternResult】正则 "'.concat(i,'" handler结果:'),I),I&&Array.isArray(I)&&I.length>0?(console.log('【getBestPatternResult】正则 "'.concat(i,'" 返回数组结果，长度:'),I.length),r.push(I)):I&&"object"===Object(o["a"])(I)?(console.log('【getBestPatternResult】正则 "'.concat(i,'" 返回对象结果')),r.push([I])):console.log('【getBestPatternResult】正则 "'.concat(i,'" 返回无效结果:'),I)}}var f={1:[1,2],2:[3,4],3:[5,6,7,100],4:[8,9],5:[10,11],6:[12,13],7:[14,15],8:[16,17],9:[18,19]},_=0,b=t&&t.match(/(\d{1,9})/);b&&(_=b[1].length),console.log("【getBestPatternResult】检测到的输入号码长度:",_);var p=f[_]||[];console.log("【getBestPatternResult】该长度对应的有效玩法ID:",p);for(var U=[],N=20;N<=31;N++)U.push(N);var A=r.filter((function(t){return t.every((function(t){return t.betNumbers&&p.includes(t.methodId)||U.includes(t.methodId)}))}));console.log("【getBestPatternResult】长度过滤后的结果数组长度:",A.length),A.length!==r.length&&console.log("【getBestPatternResult】过滤掉了",r.length-A.length,"个不符合长度要求的结果");var M=A.length>0?A:r;console.log("【getBestPatternResult】进入优先级筛选的结果数组长度:",M.length);var g=u(M,t);return console.log("【getBestPatternResult】最终选择的最佳结果:",JSON.stringify(g,null,2)),g}function c(t){var e=t.replace(/\s+/g,""),n=/(福彩|福|福家|3d|3D|福3D)/.test(e),r=/(排三|排3|体彩|体|排列3|排列三|体J|排)/.test(e);return/(福排|排福|福体|福彩体|体彩福)/.test(e)||n&&r?[1,2]:n?1:r?2:1}function m(t){console.log("【removeLotteryPrefix】原始输入:",t);var e=t.trim();if(/[*Xx]\d/.test(e))return e;var n=/^(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)/,r=/(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)$/,a=0;while(n.test(e))if(e=e.replace(n,""),e=e.trim(),a++,a>10)break;a=0;while(r.test(e))if(e=e.replace(r,""),e=e.trim(),a++,a>10)break;return e}function h(t,e){for(var n=[],r=[],a=0;a<t.length;a++){var o=t[a].trim(),i=!1;for(var s in e){var u=e[s],l=u.pattern,d=u.handler;if(l&&"function"===typeof d){var c=o.match(l);if(c&&c[0]===o){i=!0;break}}}i?(r.length>0&&(n.push(r.join(" ")),r=[]),n.push(o)):r.push(o)}return r.length>0&&n.push(r.join(" ")),n}function I(t){if(!t)return{groups:[{methodId:null,danshuang:null,daxiao:null,dingwei:null,hezhi:null,betNumbers:"",money:"",lotteryId:1}]};var e=t;e=l(e),e=/[*Xx]\d/.test(e)?e.replace(/^[\s\-/+\.,、|~!@#$%^&；'\"]+|[\s\-/+\.,、|~!@#$%^&；'\"]+$/g,""):e.replace(/^[\s\-*/+\.,、|~!@#$%^&；'\"]+|[\s\-*/+\.,、|~!@#$%^&；'\"]+$/g,""),e=e.replace(/共\s*\d+(注|元|米|块)?(\s*\+.*)?$/g,(function(t,e,n){return n||""}));var n=e.split(/\r?\n/).filter((function(t){return t.length>0}));n=h(n,s["ALL_PATTERNS"]);var r,o=new Map,u=[],I=Object(a["a"])(n);try{for(I.s();!(r=I.n()).done;){var f=r.value,_=f.match(/^(\d+)[+＋](\d+)$/);if(_){var p=_[1],U=_[2],N="".concat(p.length,"_").concat(U);o.has(N)||o.set(N,[]),o.get(N).push(f)}else u.push(f)}}catch(w){I.e(w)}finally{I.f()}var A,M=[],g=Object(a["a"])(o.values());try{for(g.s();!(A=g.n()).done;){var D=A.value;M.push(D.join("\n"))}}catch(w){g.e(w)}finally{g.f()}M=M.concat(u);var E=/^\d{3}[\-—~～@#￥%…&!+、一－。\\/*,，.＝=·\s]+\d+[+＋]\d+$/,v=/^\d{3}\/[0-9]+([+＋][0-9]+)?$/;(n.length>1&&n.every((function(t){return E.test(t)}))||n.length>1&&n.every((function(t){return v.test(t)})))&&(M=[n.join("\n")]);var O=c(t),y=m(e),S=d(y,!0);if(console.log("【handleNumberRecognition】整体识别结果:",S),S&&S.length>0){console.log("【handleNumberRecognition】✅ 整体识别成功！");var T=b(S,O);return{groups:T}}if(console.log("【handleNumberRecognition】❌ 整体识别失败，准备进入分段识别"),M.length>1){console.log("=== 【进入分段识别】 ==="),console.log("【分段识别】整体识别失败，开始分段处理");for(var L=[],j=0;j<M.length;j++){var R=M[j];console.log("\n--- 【处理第".concat(j+1,"段】 ---"));var Z=m(R);if(Z){console.log("【分段识别】去前缀后内容:",Z);var H=d(Z,!1);if(console.log("【分段识别】该段识别结果:",H),H&&H.length>0)console.log("【分段识别】该段识别成功，添加到结果中"),console.log("【分段识别】该段识别详情:",JSON.stringify(H,null,2)),L.push.apply(L,Object(i["a"])(H));else{console.log("【分段识别】该段识别失败，使用兜底逻辑");var $={methodId:null,betNumbers:R,money:"",lotteryId:O};console.log("【分段识别】兜底结果:",JSON.stringify($,null,2)),L.push($)}console.log("【分段识别】第".concat(j+1,"段处理完成，当前总结果数:"),L.length)}else console.log("【分段识别】该段去前缀后为空，跳过")}console.log("\n=== 【分段识别完成】 ==="),console.log("【分段识别】最终合并结果总数:",L.length),console.log("【分段识别】最终合并结果详情:",JSON.stringify(L,null,2));var P=b(L,O);return console.log("【分段识别】补充彩种ID后的最终结果:",JSON.stringify(P,null,2)),{groups:P}}console.log("\n=== 【进入兜底逻辑】 ==="),console.log("【handleNumberRecognition】整体识别和分段识别都失败，使用兜底逻辑"),console.log("【handleNumberRecognition】splitGroups长度:",M.length);var G={groups:[{methodId:null,betNumbers:e,money:"",lotteryId:O}]};return console.log("【handleNumberRecognition】兜底逻辑返回结果:",JSON.stringify(G,null,2)),G}function f(t){return I(t)}function _(t){if(!t)return{groups:[]};var e=t.split(/\r?\n/).map((function(t){return t.trim()})).filter(Boolean);if(e.length>0&&e.every((function(t){return/^\d+$/.test(t)}))){var n=d(e.join("\n"));return n&&n.length>0?{groups:n}:{groups:e.map((function(t){return{methodId:null,betNumbers:t,money:"",lotteryId:1}}))}}var r,o=[],i=Object(a["a"])(e);try{for(i.s();!(r=i.n()).done;){var s=r.value;if(s){var u=d(s);u&&u.length>0?o=o.concat(u):o.push({methodId:null,betNumbers:s,money:"",lotteryId:1})}}}catch(l){i.e(l)}finally{i.f()}return{groups:o}}function b(t,e){if(!t)return t;if(Array.isArray(t)&&t.length>0&&t.every((function(t){return void 0!==t.lotteryId&&null!==t.lotteryId})))return t;if(Array.isArray(e)){var n,o=[],i=Object(a["a"])(e);try{var s=function(){var e=n.value;t.forEach((function(t){o.push(Object(r["a"])(Object(r["a"])({},t),{},{lotteryId:e}))}))};for(i.s();!(n=i.n()).done;)s()}catch(u){i.e(u)}finally{i.f()}return o}return t.map((function(t){return Object(r["a"])(Object(r["a"])({},t),{},{lotteryId:e})}))}},ce3b:function(t,e,n){"use strict";n.r(e),n.d(e,"SINGLE_CODE_PATTERNS",(function(){return s}));var r=n("3835"),a=n("b85c"),o=(n("4de4"),n("a15b"),n("d81d"),n("14d9"),n("fb6a"),n("4ec9"),n("b64b"),n("d3b7"),n("4d63"),n("c607"),n("ac1f"),n("2c3e"),n("00b4"),n("25f0"),n("8a79"),n("3ca3"),n("466d"),n("5319"),n("1276"),n("498a"),n("0643"),n("2382"),n("4e3e"),n("a573"),n("9a9a"),n("159b"),n("ddb0"),n("f9d6")),i={"百":100,"十":101,"个":102},s={DINGWEI_POSITION_FIRST:{pattern:/^([百十个])位(\d)\s*(\d+)$/,handler:function(t,e){var n=t[1],r=t[2],a=t[3];if(1!==r.length)return null;var s=[{methodId:o["METHOD_ID"].YIMA_DINGWEI,betNumbers:r,dingwei:i[n],money:a}];return console.log("一码 handler 返回:",s||results||returnValue),s||results||returnValue}},DINGWEI_NUMBER_FIRST:{pattern:/^(\d)([百十个])-(\d+)$/,handler:function(t,e){var n=t[1],r=t[2],a=t[3];if(1!==n.length)return null;var s=[{methodId:o["METHOD_ID"].YIMA_DINGWEI,betNumbers:n,dingwei:i[r],money:a}];return console.log("一码 handler 返回:",s||results||returnValue),s||results||returnValue}},DINGWEI_POSITION_LAST:{pattern:/^(\d)-(\d+)-([百十个])$/,handler:function(t,e){var n=t[1],r=t[2],a=t[3];if(1!==n.length)return null;var s=[{methodId:o["METHOD_ID"].YIMA_DINGWEI,betNumbers:n,dingwei:i[a],money:r}];return console.log("一码 handler 返回:",s||results||returnValue),s||results||returnValue}},DINGWEI_MULTI:{pattern:/^(?:[（(]?(?:毒|独胆|独|毒胆)[）)]?[、,，.。\\/*\s\n\r\-~一－。～@#￥%…&!、＝=·]*)?(\d(?:[^0-9]+\d)*?)(?:各(?:打)?)?(\d+)(?:块|元)?$/,handler:function(t,e){if(/跨|跨度/.test(e))return null;var r=e.match(/\d+/g)||[];if(r.length<2)return null;var a=r[r.length-1],o=r.slice(0,-1);return!o.length||o.some((function(t){return 1!==t.length}))?null:[{methodId:n("f9d6").METHOD_ID.DUDAN,betNumbers:o.join(","),money:a}]}},DUDAN_MULTI:{pattern:/^(\d(?:[^0-9]+\d)*?)(?:各(?:打)?)?(\d+)(?:块)?$/,handler:function(t,e){if(/跨|跨度/.test(e))return null;var r=e.match(/\d+/g)||[];if(r.length<2)return null;var a=r[r.length-1],o=r.slice(0,-1);return!o.length||o.some((function(t){return 1!==t.length}))?null:[{methodId:n("f9d6").METHOD_ID.DUDAN,betNumbers:o.join(","),money:a}]}},DUDAN_COMMON_EXPRESSIONS:{pattern:/^([\d,\s\/]+?)[买打=\/-]+0*(\d+)$/,handler:function(t,e){var r=t[1],a=t[2].replace(/^0+/,""),o=r.split(/[\s,\/]+/).filter(Boolean);return!o.length||o.some((function(t){return 1!==t.length}))?null:[{methodId:n("f9d6").METHOD_ID.DUDAN,betNumbers:o.join(","),money:a}]}},DUDAN_SPECIAL:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s各打吊赶]*(\d)(?:[\-—~～@#￥%…&!、\\/*,，.＝=·\s各打吊赶]+(\d))*[\-—~～@#￥%…&!、\\/*,，.＝=·\s各打吊赶]+(\d+)(?:元|米|块)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s各打吊赶]*$/,handler:function(t,e,n){var r=e.match(/\d+/g)||[];if(r.length<2)return null;var a=r[r.length-1],i=r.slice(0,-1);return!i.length||i.some((function(t){return 1!==t.length}))?null:[{methodId:o["METHOD_ID"].DUDAN,betNumbers:i.join(","),money:a}]}},DUDAN_SINGLE:{pattern:/^(\d)-(\d+)$/,handler:function(t,e){var n=t[1],r=t[2];return n&&1===n.length?[{methodId:o["METHOD_ID"].DUDAN,betNumbers:n,money:r}]:null}},DUDAN_DIAO:{pattern:/^(?:吊)?(\d)(?:吊|各)?(\d+)$/,handler:function(t,e){var n=t[1],r=t[2];if(!n||1!==n.length)return null;if(/\d{2,}/.test(n)||/\d{2,}/.test(e.split("-")[0]))return null;if(e&&e.split("-")[0]&&1!==e.split("-")[0].replace(/[^0-9]/g,"").length)return null;var a=[{methodId:o["METHOD_ID"].DUDAN,betNumbers:n,money:r}];return console.log("一码 handler 返回:",a||results||returnValue),a||results||returnValue}},DUDAN_KEYWORD_EXPRESSIONS:{pattern:/^([\d,，.。\s\/]+)[独胆码挑]+[，.。\s\/]*([0-9]+)$/,handler:function(t,e){var r=(t[1].match(/\d+/g)||[]).filter((function(t){return 1===t.length})),a=t[2].replace(/^0+/,"");return!r.length||r.some((function(t){return 1!==t.length}))?null:[{methodId:n("f9d6").METHOD_ID.DUDAN,betNumbers:r.join(","),money:a}]}},DUDAN_UNIVERSAL_SUPER:{pattern:/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*(?:毒|独胆|独|毒胆)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*(\d)(?:[\-—~～@#￥%…&!、\\/*,，.＝=·\s]+(\d))*[\-—~～@#￥%…&!、\\/*,，.＝=·\s]+(\d+)(?:元|米|块)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*$/,handler:function(t,e){var r=n("f9d6"),a=r.METHOD_ID,o=e.match(/\d+/g)||[];if(o.length<2)return null;var i=o[o.length-1],s=o.slice(0,-1);return!s.length||s.some((function(t){return 1!==t.length}))?null:[{methodId:a.DUDAN,betNumbers:s.join(","),money:i}]}},DUDAN_MULTI_LINE_COMPLEX:{pattern:/^((?:[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:独胆|毒|独|毒胆)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*\d[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*\d+(?:元|米|块)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*\n[\s\S]*)+)$/m,handler:function(t,e){var o,i=n("f9d6"),s=i.METHOD_ID,u=e.split(/\n/).map((function(t){return t.trim()})).filter(Boolean),l=[],d=/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:独胆|毒|独|毒胆)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)(?:元|米|块)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*$/,c=Object(a["a"])(u);try{for(c.s();!(o=c.n()).done;){var m=o.value,h=m.match(d);if(!h)return console.log('【DUDAN_MULTI_LINE_COMPLEX】行 "'.concat(m,'" 不匹配独胆格式，返回null')),null;var I=h[1],f=h[2];if(1!==I.length)return console.log('【DUDAN_MULTI_LINE_COMPLEX】行 "'.concat(m,'" 号码不是1位数字，返回null')),null;l.push({line:m,number:I,money:f})}}catch(y){c.e(y)}finally{c.f()}console.log("【DUDAN_MULTI_LINE_COMPLEX】所有 ".concat(l.length," 行都是独胆格式，继续处理"));for(var _=new Map,b=0,p=l;b<p.length;b++){var U=p[b],N=U.number,A=U.money;_.has(A)||_.set(A,[]),_.get(A).push(N)}var M,g=[],D=Object(a["a"])(_.entries());try{for(D.s();!(M=D.n()).done;){var E=Object(r["a"])(M.value,2),v=E[0],O=E[1];O.length>0&&g.push({methodId:s.DUDAN,betNumbers:O.join(","),money:v})}}catch(y){D.e(y)}finally{D.f()}return console.log("【DUDAN_MULTI_LINE_COMPLEX】处理完成，返回 ".concat(g.length," 个结果组")),g.length>0?g:null}},DUDAN_UNIVERSAL_SUPER_NUMBER:{pattern:/^([0-9]{1,})\s+(\d+)(元|块|米)?$/,handler:function(t,e){var r=t[1],a=t[2];return r&&1===r.length?[{methodId:n("f9d6").METHOD_ID.DUDAN,betNumbers:r,money:a}]:null}},YIMA_DINGWEI_POSITION:{pattern:/^(百位|十位|个位)[：: ]?(\d)\+(\d+)[,，\s]*$/m,handler:function(t,e){var r,o=e.split(/\n|\r/).map((function(t){return t.trim()})).filter(Boolean),i=[],s=Object(a["a"])(o);try{for(s.s();!(r=s.n()).done;){var u=r.value,l=u.match(/^(百位|十位|个位)[：: ]?(\d)\+(\d+)[,，\s]*$/);if(l){var d=l[1],c=l[2],m=l[3],h=void 0;if("百位"===d)h=100;else if("十位"===d)h=101;else{if("个位"!==d)continue;h=102}i.push({methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,betNumbers:c,dingwei:h,money:m})}}}catch(I){s.e(I)}finally{s.f()}return i.length?i:null}},DINGWEI_MULTI_POSITION_NUMBER_MONEY:{pattern:/^([个十百]{2,3})[，,、\s]+(\d)--(\d+)$/,handler:function(t,e){var n=t[1].split(""),r=t[2],a=t[3],s=n.map((function(t){return{methodId:o["METHOD_ID"].YIMA_DINGWEI,betNumbers:r,dingwei:i[t],money:a}}));return s}},DUDAN_KEYWORD:{pattern:/^(?:独胆|胆|独)(\d)[-/+吊打赶 ]+(\d+)$/,handler:function(t,e){var n=t[1],r=t[2];return 1!==n.length?null:[{methodId:o["METHOD_ID"].DUDAN,betNumbers:n,money:r}]}},DINGWEI_POSITION_NUMBER_DIAO:{pattern:/^(百位|十位|个位)(\d)吊(\d+)$/,handler:function(t,e){var n,r=t[1],a=t[2],i=t[3];if("百位"===r)n=100;else if("十位"===r)n=101;else{if("个位"!==r)return null;n=102}return[{methodId:o["METHOD_ID"].YIMA_DINGWEI,betNumbers:a,dingwei:n,money:i}]}},DUDAN_DANMA_DIAO:{pattern:/^胆码(\d)吊(\d+)$/,handler:function(t,e){var n=t[1],r=t[2];return 1!==n.length?null:[{methodId:o["METHOD_ID"].DUDAN,betNumbers:n,money:r}]}},DUDAN_DU_DIAO:{pattern:/^独(\d)吊(\d+)$/,handler:function(t,e){var n=t[1],r=t[2];return 1!==n.length?null:[{methodId:o["METHOD_ID"].DUDAN,betNumbers:n,money:r}]}},DUDAN_MULTI_KEYWORD_SUPER:{pattern:/((独胆|胆|独)\d[打吊赶各]+[，,、.。\\/*\s\n\r\-~一－。～@#￥%…&!、＝=·]*\d+[，,、.。\\/*\s\n\r\-~一－。～@#￥%…&!、＝=·]*)+/,handler:function(t,e){var n,r=/(独胆|胆|独)(\d)[打吊赶各]+[，,、.。\\/*\s\n\r\-~～@#￥%…&!、＝=·]*(\d+)/g,a=[];while(null!==(n=r.exec(e)))a.push({methodId:o["METHOD_ID"].DUDAN,betNumbers:n[2],money:n[3]});return a.length?a:null}},DINGWEI_MULTI_POSITION_NUMBER_KEYWORD_SUPER:{pattern:/((百位|十位|个位)\d[打吊赶各]+[，,、.。\\/*\s\n\r\-~一－。～@#￥%…&!、＝=·]*\d+[，,、.。\\/*\s\n\r\-~一－。～@#￥%…&!、＝=·]*)+/,handler:function(t,e){var n,r=/(百位|十位|个位)(\d)[打吊赶各]+[，,、.。\\/*\s\n\r\-~一－。～@#￥%…&!、＝=·]*(\d+)/g,a=[];while(null!==(n=r.exec(e))){var i=void 0;if("百位"===n[1])i=100;else if("十位"===n[1])i=101;else{if("个位"!==n[1])continue;i=102}a.push({methodId:o["METHOD_ID"].YIMA_DINGWEI,betNumbers:n[2],dingwei:i,money:n[3]})}return a.length?a:null}},BAOZI_ALL_SUPER:{pattern:/^豹子[各]*[，,、.。\/*\s\n\r\-~一－。～@#￥%…&!、＝=·]*(\d+)[，,、.。\/*\s\n\r\-~一－。～@#￥%…&!、＝=·]*$/,handler:function(t,e){var n=["000","111","222","333","444","555","666","777","888","999"],r=t[1];return/各/.test(e)||(r=(parseInt(r,10)/10).toString()),[{methodId:o["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:r}]}},BAOZI_DIAO_DIVIDE_TEN:{pattern:/^豹子[打吊赶杀]+[，,、.。\\/*\s\n\r\-~一－。～@#￥%…&!、＝=·]*(\d+)[，,、.。\\/*\s\n\r\-~一－。～@#￥%…&!、＝=·]*$/,handler:function(t,e){var n=["000","111","222","333","444","555","666","777","888","999"],r=parseInt(t[1]),a=Math.floor(r/10);return[{methodId:o["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:a.toString()}]}}};Object.keys(s).forEach((function(t){var e=s[t].pattern;"string"===typeof e&&e.endsWith("$")?s[t].pattern=e.replace(/\$$/,"[，,./*-=。·、s]*$"):e instanceof RegExp&&e.source.endsWith("$")&&(s[t].pattern=new RegExp(e.source.replace(/\$$/,"[，,./*-=。·、s]*$")))}))},db21:function(t,e,n){},f398:function(t,e,n){"use strict";n.r(e),n.d(e,"SINGLE_DOUBLE_PATTERNS",(function(){return s}));var r=n("2909"),a=n("b85c"),o=n("3835"),i=(n("99af"),n("4de4"),n("7db0"),n("a630"),n("caad"),n("a15b"),n("d81d"),n("14d9"),n("13d5"),n("4ec9"),n("b680"),n("b64b"),n("d3b7"),n("4d63"),n("c607"),n("ac1f"),n("2c3e"),n("00b4"),n("25f0"),n("8a79"),n("2532"),n("3ca3"),n("466d"),n("5319"),n("1276"),n("498a"),n("0643"),n("2382"),n("fffc"),n("4e3e"),n("a573"),n("9d4a"),n("159b"),n("ddb0"),n("f9d6")),s={BAODA_ZUSAN:{pattern:/^包打?组三(?:-(\d+))?$/,handler:function(t,e){var n=t[1]||"",r=(Object(i["parseLotteryType"])(e),[{methodId:i["METHOD_ID"].BAODA_ZUSAN,betNumbers:"",money:n}]);return console.log("包打组三 handler 返回:",r),r}},HEZHI_MULTI:{pattern:/^和值(?:(\d+(?:\/\d+)*?)各(\d+)|((?:\d+-\d+\/)*\d+-\d+))$/,handler:function(t,e){Object(i["parseLotteryType"])(e);if(t[1]){var n=t[1].split("/"),r=t[2],a=n.map((function(t){return{methodId:i["METHOD_ID"].HEZHI,hezhi:parseInt(t),money:r}}));return console.log("和值多个 handler 返回:",a),a}var s=t[3].split("/").map((function(t){var e=t.split("-"),n=Object(o["a"])(e,2),r=n[0],a=n[1];return{number:r,money:a}})),u=s.map((function(t){return{methodId:i["METHOD_ID"].HEZHI,hezhi:parseInt(t.number),money:t.money}}));return console.log("和值多个 handler 返回:",u),u}},HEZHI_SINGLE_DA_SUPER:{pattern:/^(和值?|和)(\d{1,2})[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s+吊赶打各]+(\d+)$/,handler:function(t,e){var r=n("f9d6"),a=r.getHezhiMethodId,o=r.parseLotteryType,i=r.METHOD_ID,s=t[2],u=t[3],l=(o(e),{methodId:a(s,i),hezhi:parseInt(s),money:u});return console.log("和值单个打（超级分隔符）handler 返回:",l),[l]}},HEZHI_MULTI_EACH_DA_SUPER:{pattern:/^(和值?|和)((?:\d{1,2}[\-—~～@#￥%…&一－。 !、\\/*,，.＝=·\s+吊赶打各\/\\]*)+)[各\-—~～@#￥%…&!、\\/*,一－。，.＝=·\s+吊赶打]+(\d+)$/,handler:function(t,e){var r=n("f9d6"),a=r.getHezhiMethodId,o=r.SUPER_SEPARATORS,i=r.parseLotteryType,s=r.METHOD_ID,u=t[2].split(o).map((function(t){return t.trim()})).filter(Boolean),l=t[3],d=(i(e),u.map((function(t){return{methodId:a(t,s),hezhi:parseInt(t),money:l}})));return console.log("和值各打（超级分隔符）handler 返回:",d),d}},PAO_DA_MONEY:{pattern:/^炮打(\d+)(元|米|块)?$/,handler:function(t,e){var n=["000","111","222","333","444","555","666","777","888","999"];return[{methodId:i["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:"1"}]}},PAO_GE_DA_MONEY:{pattern:/^炮各打(\d+)(元|米|块)?$/,handler:function(t,e){var n=["000","111","222","333","444","555","666","777","888","999"],r=t[1];return[{methodId:i["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:r}]}},PAO_GE_MONEY:{pattern:/^炮各(\d+)(元|米|块)?$/,handler:function(t,e){var n=["000","111","222","333","444","555","666","777","888","999"],r=t[1];return[{methodId:i["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:r}]}},PAO_MONEY:{pattern:/^炮(\d+)(元|米|块)?$/,handler:function(t,e){var n=["000","111","222","333","444","555","666","777","888","999"];return[{methodId:i["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:"1"}]}},PAO_GE_MONEY_GONG:{pattern:/^炮各(\d+)(元|米|块)?共\d+$/,handler:function(t,e){var n=["000","111","222","333","444","555","666","777","888","999"],r=t[1];return[{methodId:i["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:r}]}},TONGPAO_ALLPAO_FANGPAO_MONEY:{pattern:/^(通炮|全炮|防炮)(\d+)(元|米|块)?$/,handler:function(t,e){var n=["000","111","222","333","444","555","666","777","888","999"],r=(parseFloat(t[2])/10).toFixed(1).replace(/\.0$/,"");return[{methodId:i["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:r}]}},TONGPAO_ALLPAO_FANGPAO_GE_MONEY:{pattern:/^(通炮|全炮|防炮)各(\d+)(元|米|块)?$/,handler:function(t,e){var n=["000","111","222","333","444","555","666","777","888","999"],r=t[2];return[{methodId:i["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:r}]}},PAO_09_SLASH_GE_MONEY:{pattern:/^炮0-9[\/][\s]*各?(\d+)(元|米|块)?$/,handler:function(t,e){var n=["000","111","222","333","444","555","666","777","888","999"],r=t[1];return[{methodId:i["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:r}]}},PAO_09_KONG_GE_MONEY:{pattern:/^0[\s]*[-到~—]?[\s]*9[\s]*炮[\s]*各?(\d+)(元|米|块)?$/,handler:function(t,e){var n=["000","111","222","333","444","555","666","777","888","999"],r=t[1];return[{methodId:i["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:r}]}},KONGPAO_GE_MONEY:{pattern:/^空炮[各个]?(\d+)(元|米|块)?$/,handler:function(t,e){var n=["000","111","222","333","444","555","666","777","888","999"],r=t[1];return[{methodId:i["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:r}]}},BAOZI_MULTI_EACH_MONEY:{pattern:/^((?:\d{3}[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*)+)豹子各?(\d+)(元|米|块)?$/,handler:function(t,e){var n=t[1].split(/[\-—~～@#￥%…&!、\\/*,，.＝=·\s]+/).map((function(t){return t.trim()})).filter((function(t){return 3===t.length&&t[0]===t[1]&&t[1]===t[2]})),r=t[2];return[{methodId:i["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:r}]}},BAOZI_MULTI_MONEY:{pattern:/^((?:\d{3}[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*)+)豹子(\d+)(元|米|块)?$/,handler:function(t,e){var n=t[1].split(/[\-—~～@#￥%…&!、\\/*,，.＝=·\s]+/).map((function(t){return t.trim()})).filter((function(t){return 3===t.length&&t[0]===t[1]&&t[1]===t[2]}));return[{methodId:i["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:"1"}]}},PAO_SUPERSEP_MONEY:{pattern:/^炮[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s.。·、]+(\d+)(元|米|块)?$/,handler:function(t,e){var n=["000","111","222","333","444","555","666","777","888","999"];return[{methodId:i["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:"1"}]}},QUANPAO_MONEY:{pattern:/^全炮(各)?(\d+)(元|米|块)?$/,handler:function(t,e){var n=["000","111","222","333","444","555","666","777","888","999"],r=t[2];return[{methodId:i["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:r}]}},BAOZI_ALL_MONEY:{pattern:/^0到9豹子全包(\d+)(?:元|米|块)?$/,handler:function(t,e){var n=["000","111","222","333","444","555","666","777","888","999"],r=t[1];return/各/.test(e)||(r=(parseFloat(r)/10).toFixed(1).replace(/\.0$/,"")),[{methodId:i["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:r}]}},PAO_OTHER:{pattern:/^炮.*?各(\d+)(?:元|米|块)?$/,handler:function(t,e){var n=["000","111","222","333","444","555","666","777","888","999"];return[{methodId:i["METHOD_ID"].SANMA_ZHIXUAN,betNumbers:n.join(","),money:t[1]}]}},BAODA_ZUSAN_VARIANTS:{pattern:/^(包?对子|对子|对吊|对一|对)[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([0-9]+)(元|块|米)?$/,handler:function(t,e){var n=t[2],r={"一":1,"二":2,"三":3,"四":4,"五":5,"六":6,"七":7,"八":8,"九":9,"十":10,"百":100,"千":1e3,"万":1e4},a=n;return/^[一二三四五六七八九十百千万]+$/.test(n)&&(a=n.split("").reduce((function(t,e){return t+(r[e]||0)}),0)||"1"),/^\d+$/.test(n)&&(a=n),a||(a="1"),[{methodId:i["METHOD_ID"].BAODA_ZUSAN,betNumbers:"",money:a}]}},HEZHI_SIMPLE:{pattern:/^(?:福|福彩|3D|体|体彩|排三|排)?和([\d/\-、,，.。\s]+)[/\-、,，.。\s]+(\d+)(元|块|米)?$/,handler:function(t,e){var r=n("f9d6"),a=r.getHezhiMethodId,o=r.METHOD_ID,i=r.parseLotteryType,s=t[1],u=t[2],l=(i(e),s.split(/[\/\-、,，.。\s]+/).map((function(t){return t.trim()})).filter(Boolean));return l.map((function(t){return{methodId:a(t,o),hezhi:parseInt(t),money:u}}))}},BAO_ZULIU_ALL:{pattern:/^(包打?组六|组六)[\-—~～@#￥%…&!、\\/*,一－。＝=·吊赶打各\s]*([0-9]+)$/,handler:function(t,e){var n=t[2]||"";Object(i["parseLotteryType"])(e);return[{methodId:i["METHOD_ID"].BAODA_ZULIU,betNumbers:"",money:n}]}},BAO_ZUSAN_ALL:{pattern:/^(包打?组三|组三)[\-—~～@#￥%…&!、\\/*,一－。＝=·吊赶打各\s]*([0-9]+)$/,handler:function(t,e){var n=t[2]||"";Object(i["parseLotteryType"])(e);return[{methodId:i["METHOD_ID"].BAODA_ZUSAN,betNumbers:"",money:n}]}},SHA_MA_SUPER:{pattern:/^杀([0-9]+)[\-—~～@#￥%…&!、\\/*,一－。.＝=·吊赶打各\s]+([0-9]+)[\-—~～@#￥%…&!、\\/*,一－。.＝=·吊赶打各\s]*$/,handler:function(t,e){var n=t[1].split("").join(","),r=t[2];return[{methodId:i["METHOD_ID"].SHA_MA,betNumbers:n,money:r}]}},HEZHI_DA_SUPER:{pattern:/^[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*(?:和值?|和)?[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*大[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([0-9]+)$/,handler:function(t,e){var n=t[1];Object(i["parseLotteryType"])(e);return[{methodId:i["METHOD_ID"].HEZHI_DAXIAO,daxiao:300,money:n,betNumbers:null}]}},HEZHI_XIAO_SUPER:{pattern:/^[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*(?:和值?|和)?[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*小[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([0-9]+)$/,handler:function(t,e){var n=t[1];Object(i["parseLotteryType"])(e);return[{methodId:i["METHOD_ID"].HEZHI_DAXIAO,daxiao:301,money:n,betNumbers:null}]}},HEZHI_DAN_SUPER:{pattern:/^[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*(?:和值?|和)?[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*单[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([0-9]+)$/,handler:function(t,e){var n=t[1];Object(i["parseLotteryType"])(e);return[{methodId:i["METHOD_ID"].HEZHI_DANSHUAN,danshuang:200,money:n,betNumbers:null}]}},HEZHI_SHUANG_SUPER:{pattern:/^[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*(?:和值?|和)?[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*双[\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([0-9]+)$/,handler:function(t,e){var n=t[1];Object(i["parseLotteryType"])(e);return[{methodId:i["METHOD_ID"].HEZHI_DANSHUAN,danshuang:201,money:n,betNumbers:null}]}},BAOZI_MULTI_LINE_EACH_MONEY:{pattern:/^([\s\S]+)$/m,handler:function(t,e){var i,s=e.split(/\n+/).map((function(t){return t.trim()})).filter(Boolean),u=new Map,l=[],d=/^([0-9]{1,9})[ \t]*(豹子|炮)[ \t]*各([0-9]+)(元|米|块)?[^\d]*$/,c=Object(a["a"])(s);try{for(c.s();!(i=c.n()).done;){var m=i.value,h=m.match(d);if(!h)return console.log('【BAOZI_MULTI_LINE_EACH_MONEY】行 "'.concat(m,'" 不匹配豹子/炮格式，返回null')),null;l.push({line:m,match:h})}}catch(A){c.e(A)}finally{c.f()}console.log("【BAOZI_MULTI_LINE_EACH_MONEY】所有 ".concat(l.length," 行都是豹子/炮格式，继续处理"));for(var I=0,f=l;I<f.length;I++){var _,b=f[I].match,p=b[1].split("").filter((function(t){return/[0-9]/.test(t)})),U=b[3];u.has(U)||u.set(U,[]),(_=u.get(U)).push.apply(_,Object(r["a"])(p.map((function(t){return t+t+t}))))}if(0===u.size)return console.log("【BAOZI_MULTI_LINE_EACH_MONEY】没有有效的豹子/炮内容，返回null"),null;var N=Array.from(u.entries()).map((function(t){var e=Object(o["a"])(t,2),r=e[0],a=e[1];return{methodId:n("f9d6").METHOD_ID.SANMA_ZHIXUAN,betNumbers:a.join(","),money:r}}));return console.log("【BAOZI_MULTI_LINE_EACH_MONEY】处理完成，返回 ".concat(N.length," 个结果组")),N}},YIMA_DANSHUAN_SUPER:{pattern:/^(百位|百|十位|十|个位|个)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*(单|双)(\d+)(元|米|块)?$/,handler:function(t,e){var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},a=t[1],o=t[2],i=t[3];return[{methodId:n("f9d6").METHOD_ID.YIMA_DANSHUAN,dingwei:r[a],betNumbers:o,money:i}]}},YIMA_DAXIAO_SUPER:{pattern:/^(百位|百|十位|十|个位|个)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*(大|小)(\d+)(元|米|块)?$/,handler:function(t,e){var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},a=t[1],o=t[2],i=t[3];return[{methodId:n("f9d6").METHOD_ID.YIMA_DAXIAO,dingwei:r[a],betNumbers:o,money:i}]}},SPECIAL_FU_PAI_PAO:{pattern:/[。.,、\\/*\s\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([福3d3D福彩]+)[炮豹子]+(\d+)(元|米|块)?[。.,、\\/*\\s\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([排排三体体彩]+)[炮豹子]+(\d+)(元|米|块)?/,handler:function(t,e){var r=["000","111","222","333","444","555","666","777","888","999"],a=t[2],o=t[5],i=1,s=2;return[{methodId:n("f9d6").METHOD_ID.SANMA_ZHIXUAN,betNumbers:r.join(","),money:(parseFloat(a)/10).toString(),lotteryId:i},{methodId:n("f9d6").METHOD_ID.SANMA_ZHIXUAN,betNumbers:r.join(","),money:(parseFloat(o)/10).toString(),lotteryId:s}]}},SPECIAL_FU_PAI_PAO_EACH:{pattern:/[。.,、\\/*\s\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([福3d3D福彩]+)[炮豹子]+各(\d+)(元|米|块)?[。.,、\\/*\s\-—~～@#￥%…&!一－。、\\/*,，.＝=·\s]*([排排三体体彩]+)[炮豹子]+各(\d+)(元|米|块)?/,handler:function(t,e){var r=["000","111","222","333","444","555","666","777","888","999"],a=t[2],o=t[5],i=1,s=2;return[{methodId:n("f9d6").METHOD_ID.SANMA_ZHIXUAN,betNumbers:r.join(","),money:a,lotteryId:i},{methodId:n("f9d6").METHOD_ID.SANMA_ZHIXUAN,betNumbers:r.join(","),money:o,lotteryId:s}]}},YIMA_DINGWEI_FORMAT1_NEW:{pattern:/^(百位?|百)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*([0-9]+(?:[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*[0-9]+)*)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*(十位?|十)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*([0-9]+(?:[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*[0-9]+)*)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*(个位?|个)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*([0-9]+(?:[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*[0-9]+)*)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*各[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*([0-9]+)(?:元|块|快|米)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*(一码定位|一码|定)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*$/,handler:function(t,e){if(!t[1]||!t[3]||!t[5])return null;if(!t[8])return null;var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},a=(t[2].match(/[0-9]/g)||[]).join(","),o=(t[4].match(/[0-9]/g)||[]).join(","),i=(t[6].match(/[0-9]/g)||[]).join(","),s=t[7];return[{methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[t[1]],betNumbers:a,money:s},{methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[t[3]],betNumbers:o,money:s},{methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[t[5]],betNumbers:i,money:s}]}},YIMA_DINGWEI_FORMAT2_NEW:{pattern:/^([\s\S]+)$/m,handler:function(t,e){if(!/(?:百位?|百)|(?:十位?|十)|(?:个位?|个)/.test(e))return null;if(!/一码定位|一码|定/.test(e))return null;var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},o=e.split(/\n+/).map((function(t){return t.trim()})).filter(Boolean),i="",s={},u=/^(百位?|百|十位?|十|个位?|个)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*([0-9]+(?:[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*[0-9]+)*)/,l=/各[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*([0-9]+)(?:元|块|快|米)?/,d=/一码定位|一码|定/,c=e.match(l);c&&(i=c[1]);var m,h=Object(a["a"])(o);try{for(h.s();!(m=h.n()).done;){var I=m.value;if(!l.test(I)&&!d.test(I)){var f=I.match(u);if(f){var _=f[1],b=f[2],p=b.match(/[0-9]/g);p&&p.length>0&&(s[_]=p.join(","))}else if(I.trim()&&!/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*$/.test(I))return console.log('【YIMA_DINGWEI_FORMAT2_NEW】行 "'.concat(I,'" 不匹配一码定位格式，返回null')),null}}}catch(M){h.e(M)}finally{h.f()}var U=Object.keys(s).find((function(t){return t.includes("百")})),N=Object.keys(s).find((function(t){return t.includes("十")})),A=Object.keys(s).find((function(t){return t.includes("个")}));return U&&N&&A?(console.log("【YIMA_DINGWEI_FORMAT2_NEW】识别成功，三个位置都有数据"),[{methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[U],betNumbers:s[U],money:i},{methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[N],betNumbers:s[N],money:i},{methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[A],betNumbers:s[A],money:i}]):(console.log("【YIMA_DINGWEI_FORMAT2_NEW】未找到完整的三个位置，返回null"),null)}},YIMA_DINGWEI_FORMAT3_NEW:{pattern:/^([\s\S]+)$/m,handler:function(t,e){if(!/(?:百位?|百)|(?:十位?|十)|(?:个位?|个)/.test(e))return null;if(!/一码定位|一码|定/.test(e))return null;var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},a=e.split(/\n+/).map((function(t){return t.trim()})).filter(Boolean);if(a.length<2)return console.log("【YIMA_DINGWEI_FORMAT3_NEW】行数不足，返回null"),null;var o=a[0],i=o.match(/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*(百位?|百)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]+(十位?|十)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]+(个位?|个)[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*$/);if(!i)return console.log('【YIMA_DINGWEI_FORMAT3_NEW】表头行 "'.concat(o,'" 不匹配格式，返回null')),null;for(var s="",u=1;u<a.length;u++)if(/^[\d\-—~～@#￥%…&!、\\/*,，.＝=·\s]+$/.test(a[u])){s=a[u];break}if(!s)return console.log("【YIMA_DINGWEI_FORMAT3_NEW】未找到数字行，返回null"),null;for(var l=/各[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*([0-9]+)(?:元|块|快|米)?/,d=/一码定位|一码|定/,c=1;c<a.length;c++){var m=a[c];if(m!==s&&(!l.test(m)&&!d.test(m)&&!/^[\-—~～@#￥%…&!、\\/*,，.＝=·\s]*$/.test(m)))return console.log('【YIMA_DINGWEI_FORMAT3_NEW】行 "'.concat(m,'" 不符合表格格式，返回null')),null}var h=s.split(/[\-—~～@#￥%…&!、\\/*,，.＝=·\s]+/).filter(Boolean);if(3!==h.length)return console.log("【YIMA_DINGWEI_FORMAT3_NEW】数字行分组数量不是3个，实际".concat(h.length,"个，返回null")),null;var I="",f=e.match(l);f&&(I=f[1]);var _=(h[0].match(/[0-9]/g)||[]).join(","),b=(h[1].match(/[0-9]/g)||[]).join(","),p=(h[2].match(/[0-9]/g)||[]).join(",");return console.log("【YIMA_DINGWEI_FORMAT3_NEW】表格格式识别成功"),[{methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[i[1]],betNumbers:_,money:I},{methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[i[2]],betNumbers:b,money:I},{methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[i[3]],betNumbers:p,money:I}]}},YIMA_DINGWEI_TABLE_STANDARD:{pattern:/^([\s\S]+)$/m,handler:function(t,e){if(!/(?:百位?|百)|(?:十位?|十)|(?:个位?|个)/.test(e))return null;if(!/一码定位|一码|定/.test(e))return null;var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},a=e.split(/\n+/).map((function(t){return t.trim()})).filter(Boolean);if(a.length<2)return console.log("【YIMA_DINGWEI_TABLE_STANDARD】行数不足，返回null"),null;var o=a[0],i=o.match(/^[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*(百位?|百)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]+(十位?|十)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]+(个位?|个)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*$/);if(!i)return console.log('【YIMA_DINGWEI_TABLE_STANDARD】表头行 "'.concat(o,'" 不匹配格式，返回null')),null;for(var s="",u=1;u<a.length;u++)if(/^[\d\s\-—~～@#￥%…&!、\\/*,，.＝=·]+$/.test(a[u])){s=a[u];break}if(!s)return console.log("【YIMA_DINGWEI_TABLE_STANDARD】未找到数字行，返回null"),null;for(var l=/各[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+)(?:元|块|快|米)?/,d=/一码定位|一码|定/,c=1;c<a.length;c++){var m=a[c];if(m!==s&&(!l.test(m)&&!d.test(m)&&!/^[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*$/.test(m)))return console.log('【YIMA_DINGWEI_TABLE_STANDARD】行 "'.concat(m,'" 不符合标准表格格式，返回null')),null}var h=s.split(/[\s\-—~～@#￥%…&!、\\/*,，.＝=·]+/).filter(Boolean);if(3!==h.length)return console.log("【YIMA_DINGWEI_TABLE_STANDARD】数字行分组数量不是3个，实际".concat(h.length,"个，返回null")),null;var I="",f=e.match(l);f&&(I=f[1]);var _=(h[0].match(/[0-9]/g)||[]).join(","),b=(h[1].match(/[0-9]/g)||[]).join(","),p=(h[2].match(/[0-9]/g)||[]).join(",");return console.log("【YIMA_DINGWEI_TABLE_STANDARD】标准表格格式识别成功"),[{methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[i[1]],betNumbers:_,money:I},{methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[i[2]],betNumbers:b,money:I},{methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[i[3]],betNumbers:p,money:I}]}},YIMA_DINGWEI_SINGLE_POSITION:{pattern:/^(百位?|百|十位?|十|个位?|个)[ \t\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+(?:[ \t\-—~～@#￥%…&!、\\/*,，.＝=·]*[0-9]+)*)[ \t\-—~～@#￥%…&!、\\/*,，.＝=·]*各[ \t\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+)(?:元|块|快|米)?[ \t\-—~～@#￥%…&!、\\/*,，.＝=·]*$/,handler:function(t,e){var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},a=t[1],o=t[2],i=t[3],s=(o.match(/[0-9]/g)||[]).join(",");return[{methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[a],betNumbers:s,money:i}]}},YIMA_DINGWEI_SINGLE_POSITION_NO_GE:{pattern:/^(百位?|百|十位?|十|个位?|个)[ \t\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+(?:[ \t\-—~～@#￥%…&!、\\/*,，.＝=·]*[0-9]+)*)[ \t\-—~～@#￥%…&!、\\/*,，.＝=·]+([0-9]+)(?:元|块|快|米)*$/,handler:function(t,e){if(!/(?:百位?|百|十位?|十|个位?|个)/.test(e))return console.log('【YIMA_DINGWEI_SINGLE_POSITION_NO_GE】行 "'.concat(e,'" 不包含位置标识，返回null')),null;var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},a=t[1],o=t[2],i=t[3];if(!r[a])return console.log('【YIMA_DINGWEI_SINGLE_POSITION_NO_GE】位置标识 "'.concat(a,'" 无效，返回null')),null;var s=(o.match(/[0-9]/g)||[]).join(",");return s?(console.log("【YIMA_DINGWEI_SINGLE_POSITION_NO_GE】识别成功: 位置=".concat(a,", 号码=").concat(s,", 金额=").concat(i)),[{methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[a],betNumbers:s,money:i}]):(console.log("【YIMA_DINGWEI_SINGLE_POSITION_NO_GE】未提取到有效号码，返回null"),null)}},YIMA_DINGWEI_TWO_LINES:{pattern:/^([\s\S]+)$/m,handler:function(t,e){if(!/(?:百位?|百)|(?:十位?|十)|(?:个位?|个)/.test(e))return null;if(!/各[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+)(?:元|块|快|米)?/.test(e))return null;var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},o=e.split(/\n+/).map((function(t){return t.trim()})).filter(Boolean);if(2!==o.length)return console.log("【YIMA_DINGWEI_TWO_LINES】行数不是2行，实际".concat(o.length,"行，返回null")),null;var i="",s={},u=/^(百位?|百|十位?|十|个位?|个)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+(?:[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*[0-9]+)*)/,l=/各[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+)(?:元|块|快|米)?/,d=e.match(l);d&&(i=d[1]);var c,m=Object(a["a"])(o);try{for(m.s();!(c=m.n()).done;){var h=c.value;if(!l.test(h)){var I=h.match(u);if(I){var f=I[1],_=I[2],b=_.match(/[0-9]/g);b&&b.length>0&&(s[f]=b.join(","))}else if(h.trim()&&!/^[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*$/.test(h))return console.log('【YIMA_DINGWEI_TWO_LINES】行 "'.concat(h,'" 不匹配一码定位格式，返回null')),null}}}catch(g){m.e(g)}finally{m.f()}var p=Object.keys(s);if(2!==p.length)return console.log("【YIMA_DINGWEI_TWO_LINES】位置数量不是2个，实际".concat(p.length,"个，返回null")),null;console.log("【YIMA_DINGWEI_TWO_LINES】两行两位置格式识别成功");for(var U=[],N=0,A=p;N<A.length;N++){var M=A[N];U.push({methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[M],betNumbers:s[M],money:i})}return U}},YIMA_DINGWEI_ONE_LINE_TWO_POSITIONS:{pattern:/^(百位?|百|十位?|十|个位?|个)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+(?:[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*[0-9]+)*)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*(百位?|百|十位?|十|个位?|个)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+(?:[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*[0-9]+)*)[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*各[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*([0-9]+)(?:元|块|快|米)?[\s\-—~～@#￥%…&!、\\/*,，.＝=·]*$/,handler:function(t,e){var r={"百":100,"百位":100,"十":101,"十位":101,"个":102,"个位":102},a=t[1],o=t[2],i=t[3],s=t[4],u=t[5];if(r[a]===r[i])return null;var l=(o.match(/[0-9]/g)||[]).join(","),d=(s.match(/[0-9]/g)||[]).join(",");return[{methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[a],betNumbers:l,money:u},{methodId:n("f9d6").METHOD_ID.YIMA_DINGWEI,dingwei:r[i],betNumbers:d,money:u}]}}};Object.keys(s).forEach((function(t){var e=s[t].pattern;"string"===typeof e&&e.endsWith("$")?s[t].pattern=e.replace(/\$$/,"[，,./*-=。·、s]*$"):e instanceof RegExp&&e.source.endsWith("$")&&(s[t].pattern=new RegExp(e.source.replace(/\$$/,"[，,./*-=。·、s]*$")))}))},f97d:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"a",(function(){return o}));var r=n("b775");function a(t){return Object(r["a"])({url:"/game/serial/list",method:"get",params:t})}function o(){return Object(r["a"])({url:"/game/serial/list",method:"get",params:{pageNum:1,pageSize:1,sortField:"serialNumbers",sortOrder:"desc"}}).then((function(t){return t&&t.length>0?t[0].serialNumbers:t&&t.rows&&t.rows.length>0?t.rows[0].serialNumbers:null}))}}}]);