{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetStatistics.vue?vue&type=style&index=0&id=090740bc&prod&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetStatistics.vue", "mtime": 1756000107447}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750942927475}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750942929511}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750942928175}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750942928768}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLyog566h55CG5ZGY5o6n5Yi25Yy65Z+f5qC35byPICovCi5hZG1pbi1jb250cm9scyB7CiAgYm9yZGVyOiAxcHggc29saWQgI2U0ZTdlZDsKICBib3JkZXItcmFkaXVzOiA2cHg7CiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2Y4ZjlmYSAwJSwgI2U5ZWNlZiAxMDAlKTsKfQoKLmFkbWluLWNvbnRyb2xzIC5lbC1zZWxlY3QgewogIGJhY2tncm91bmQ6IHdoaXRlOwp9CgovKiDlvannp43moIfnrb7pobXmoLflvI8gKi8KLmxvdHRlcnktdHlwZS10YWJzIHsKICBtYXJnaW4tbGVmdDogMjBweDsKICBmbGV4OiAxOwp9CgoubG90dGVyeS10eXBlLXRhYnMtY29udGFpbmVyIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogY2VudGVyOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7Cn0KCi5sb3R0ZXJ5LXR5cGUtdGFicyAuZWwtdGFicy0tY2FyZCA+IC5lbC10YWJzX19oZWFkZXIsCi5sb3R0ZXJ5LXR5cGUtdGFicy1jb250YWluZXIgLmVsLXRhYnMtLWNhcmQgPiAuZWwtdGFic19faGVhZGVyIHsKICBib3JkZXItYm90dG9tOiBub25lOwogIG1hcmdpbjogMDsKfQoKLmxvdHRlcnktdHlwZS10YWJzIC5lbC10YWJzLS1jYXJkID4gLmVsLXRhYnNfX2hlYWRlciAuZWwtdGFic19fbmF2LAoubG90dGVyeS10eXBlLXRhYnMtY29udGFpbmVyIC5lbC10YWJzLS1jYXJkID4gLmVsLXRhYnNfX2hlYWRlciAuZWwtdGFic19fbmF2IHsKICBib3JkZXI6IG5vbmU7CiAgYm9yZGVyLXJhZGl1czogOHB4OwogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmOGY5ZmEgMCUsICNlOWVjZWYgMTAwJSk7CiAgcGFkZGluZzogNHB4Owp9CgoubG90dGVyeS10eXBlLXRhYnMgLmVsLXRhYnMtLWNhcmQgPiAuZWwtdGFic19faGVhZGVyIC5lbC10YWJzX19pdGVtLAoubG90dGVyeS10eXBlLXRhYnMtY29udGFpbmVyIC5lbC10YWJzLS1jYXJkID4gLmVsLXRhYnNfX2hlYWRlciAuZWwtdGFic19faXRlbSB7CiAgYm9yZGVyOiBub25lOwogIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50OwogIGNvbG9yOiAjNjA2MjY2OwogIGZvbnQtd2VpZ2h0OiA1MDA7CiAgcGFkZGluZzogOHB4IDE2cHg7CiAgbWFyZ2luLXJpZ2h0OiA0cHg7CiAgYm9yZGVyLXJhZGl1czogNnB4OwogIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7CiAgcG9zaXRpb246IHJlbGF0aXZlOwogIG92ZXJmbG93OiBoaWRkZW47Cn0KCi5sb3R0ZXJ5LXR5cGUtdGFicyAuZWwtdGFicy0tY2FyZCA+IC5lbC10YWJzX19oZWFkZXIgLmVsLXRhYnNfX2l0ZW06aG92ZXIsCi5sb3R0ZXJ5LXR5cGUtdGFicy1jb250YWluZXIgLmVsLXRhYnMtLWNhcmQgPiAuZWwtdGFic19faGVhZGVyIC5lbC10YWJzX19pdGVtOmhvdmVyIHsKICBjb2xvcjogIzQwOWVmZjsKICBiYWNrZ3JvdW5kOiByZ2JhKDY0LCAxNTgsIDI1NSwgMC4xKTsKICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCk7Cn0KCi5sb3R0ZXJ5LXR5cGUtdGFicyAuZWwtdGFicy0tY2FyZCA+IC5lbC10YWJzX19oZWFkZXIgLmVsLXRhYnNfX2l0ZW0uaXMtYWN0aXZlLAoubG90dGVyeS10eXBlLXRhYnMtY29udGFpbmVyIC5lbC10YWJzLS1jYXJkID4gLmVsLXRhYnNfX2hlYWRlciAuZWwtdGFic19faXRlbS5pcy1hY3RpdmUgewogIGNvbG9yOiB3aGl0ZTsKICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpOwogIGZvbnQtd2VpZ2h0OiA2MDA7CiAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDEwMiwgMTI2LCAyMzQsIDAuMyk7CiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpOwp9CgoubG90dGVyeS10eXBlLXRhYnMgLmVsLXRhYnMtLWNhcmQgPiAuZWwtdGFic19faGVhZGVyIC5lbC10YWJzX19pdGVtLmlzLWFjdGl2ZTo6YmVmb3JlLAoubG90dGVyeS10eXBlLXRhYnMtY29udGFpbmVyIC5lbC10YWJzLS1jYXJkID4gLmVsLXRhYnNfX2hlYWRlciAuZWwtdGFic19faXRlbS5pcy1hY3RpdmU6OmJlZm9yZSB7CiAgY29udGVudDogJyc7CiAgcG9zaXRpb246IGFic29sdXRlOwogIHRvcDogMDsKICBsZWZ0OiAwOwogIHJpZ2h0OiAwOwogIGJvdHRvbTogMDsKICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCByZ2JhKDI1NSwyNTUsMjU1LDAuMikgMCUsIHRyYW5zcGFyZW50IDEwMCUpOwogIHBvaW50ZXItZXZlbnRzOiBub25lOwp9CgoubG90dGVyeS10eXBlLXRhYnMgLmVsLXRhYnMtLWNhcmQgPiAuZWwtdGFic19faGVhZGVyIC5lbC10YWJzX19pdGVtIGksCi5sb3R0ZXJ5LXR5cGUtdGFicy1jb250YWluZXIgLmVsLXRhYnMtLWNhcmQgPiAuZWwtdGFic19faGVhZGVyIC5lbC10YWJzX19pdGVtIGkgewogIG1hcmdpbi1yaWdodDogNnB4OwogIGZvbnQtc2l6ZTogMTZweDsKfQoKLmxvdHRlcnktdHlwZS10YWJzIC5lbC10YWJzX19jb250ZW50LAoubG90dGVyeS10eXBlLXRhYnMtY29udGFpbmVyIC5lbC10YWJzX19jb250ZW50IHsKICBkaXNwbGF5OiBub25lOyAvKiDpmpDol4/lhoXlrrnljLrln5/vvIzlm6DkuLrmiJHku6zlj6rnlKjmoIfnrb7pobXliIfmjaIgKi8KfQoKLnN0YXRpc3RpY3MtZGlhbG9nIHsKICAuZWwtZGlhbG9nIHsKICAgIGJvcmRlci1yYWRpdXM6IDEycHg7CiAgfQogIAogIC5lbC1kaWFsb2dfX2hlYWRlciB7CiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpOwogICAgY29sb3I6IHdoaXRlOwogICAgcGFkZGluZzogMjBweCAyNHB4OwogIH0KICAKICAuZWwtZGlhbG9nX190aXRsZSB7CiAgICBjb2xvcjogd2hpdGU7CiAgICBmb250LXdlaWdodDogNjAwOwogICAgZm9udC1zaXplOiAxOHB4OwogIH0KfQoKLnN0YXRpc3RpY3MtY29udGFpbmVyIHsKICBwYWRkaW5nOiAxMHB4IDA7Cn0KCi5tYWluLXNlY3Rpb24tdGl0bGUgewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBmb250LXNpemU6IDE2cHg7CiAgZm9udC13ZWlnaHQ6IDYwMDsKICBjb2xvcjogIzJjM2U1MDsKICBtYXJnaW4tYm90dG9tOiA4cHg7CiAgcGFkZGluZy1ib3R0b206IDRweDsKICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2VjZjBmMTsKfQoKLm1haW4tc2VjdGlvbi10aXRsZSBpIHsKICBtYXJnaW4tcmlnaHQ6IDRweDsKICBjb2xvcjogIzY2N2VlYTsKICBmb250LXNpemU6IDE0cHg7Cn0KCi8qIOeOqeazleaOkuihjOamnOagt+W8jyAqLwoubWV0aG9kLXJhbmtpbmdzLXNlY3Rpb24gewogIG1hcmdpbi1ib3R0b206IDE1cHg7Cn0KCi5tZXRob2QtZ3JpZCB7CiAgZGlzcGxheTogZ3JpZDsKICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCg1LCAxZnIpOwogIGdhcDogNnB4Owp9CgpAbWVkaWEgKG1heC13aWR0aDogMTQwMHB4KSB7CiAgLm1ldGhvZC1ncmlkIHsKICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDQsIDFmcik7CiAgfQp9CgpAbWVkaWEgKG1heC13aWR0aDogMTEwMHB4KSB7CiAgLm1ldGhvZC1ncmlkIHsKICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDMsIDFmcik7CiAgfQp9CgpAbWVkaWEgKG1heC13aWR0aDogODAwcHgpIHsKICAubWV0aG9kLWdyaWQgewogICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoMiwgMWZyKTsKICB9Cn0KCkBtZWRpYSAobWF4LXdpZHRoOiA1MDBweCkgewogIC5tZXRob2QtZ3JpZCB7CiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmcjsKICB9Cn0KCi5tZXRob2QtY2FyZCB7CiAgYmFja2dyb3VuZDogd2hpdGU7CiAgYm9yZGVyLXJhZGl1czogMTBweDsKICBib3gtc2hhZG93OiAwIDNweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xMik7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4zcyBlYXNlLCBib3gtc2hhZG93IDAuM3MgZWFzZTsKICBtaW4td2lkdGg6IDA7IC8qIOmYsuatouWGheWuuea6ouWHuiAqLwogIG1pbi1oZWlnaHQ6IDIwMHB4OyAvKiDmnIDlsI/pq5jluqYgKi8KICBtYXgtaGVpZ2h0OiA1MDBweDsgLyog5aKe5Yqg5pyA5aSn6auY5bqm5Lul6YCC5bqU5pu05aSa5pWw5o2uICovCn0KCi5tZXRob2QtY2FyZDpob3ZlciB7CiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpOwogIGJveC1zaGFkb3c6IDAgNnB4IDIwcHggcmdiYSgwLCAwLCAwLCAwLjE4KTsKfQoKLm1ldGhvZC1jYXJkLWhlYWRlciB7CiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKTsKICBjb2xvcjogd2hpdGU7CiAgcGFkZGluZzogOHB4IDEycHg7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKfQoKLm1ldGhvZC1uYW1lIHsKICBmb250LXNpemU6IDE1cHg7CiAgZm9udC13ZWlnaHQ6IDcwMDsKICB3aGl0ZS1zcGFjZTogbm93cmFwOwogIG92ZXJmbG93OiBoaWRkZW47CiAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7Cn0KCi5tZXRob2QtY291bnQgewogIGZvbnQtc2l6ZTogMTNweDsKICBvcGFjaXR5OiAwLjk7Cn0KCi5yYW5raW5nLWxpc3QgewogIHBhZGRpbmc6IDhweDsKICBtYXgtaGVpZ2h0OiA0MjBweDsgLyog5aKe5Yqg5YiX6KGo6auY5bqm5Lul6YCC5bqU5pu05aSa5pWw5o2uICovCiAgb3ZlcmZsb3cteTogYXV0bzsgLyog5re75Yqg5Z6C55u05rua5Yqo5p2hICovCn0KCi8qIOa7muWKqOadoeagt+W8jyAqLwoucmFua2luZy1saXN0Ojotd2Via2l0LXNjcm9sbGJhciB7CiAgd2lkdGg6IDRweDsKfQoKLnJhbmtpbmctbGlzdDo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sgewogIGJhY2tncm91bmQ6ICNmMWYxZjE7CiAgYm9yZGVyLXJhZGl1czogMnB4Owp9CgoucmFua2luZy1saXN0Ojotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7CiAgYmFja2dyb3VuZDogI2MxYzFjMTsKICBib3JkZXItcmFkaXVzOiAycHg7Cn0KCi5yYW5raW5nLWxpc3Q6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iOmhvdmVyIHsKICBiYWNrZ3JvdW5kOiAjYThhOGE4Owp9CgovKiDng63pl6jkuInkvY3mlbDmjpLooYzmppzmu5rliqjmnaHmoLflvI8gKi8KLm51bWJlcnMtcmFua2luZy1saXN0Ojotd2Via2l0LXNjcm9sbGJhciB7CiAgd2lkdGg6IDRweDsKfQoKLm51bWJlcnMtcmFua2luZy1saXN0Ojotd2Via2l0LXNjcm9sbGJhci10cmFjayB7CiAgYmFja2dyb3VuZDogI2YxZjFmMTsKICBib3JkZXItcmFkaXVzOiAycHg7Cn0KCi5udW1iZXJzLXJhbmtpbmctbGlzdDo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIgewogIGJhY2tncm91bmQ6ICNjMWMxYzE7CiAgYm9yZGVyLXJhZGl1czogMnB4Owp9CgoubnVtYmVycy1yYW5raW5nLWxpc3Q6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iOmhvdmVyIHsKICBiYWNrZ3JvdW5kOiAjYThhOGE4Owp9CgoucmFua2luZy1pdGVtIHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgcGFkZGluZzogNHB4IDhweDsKICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2YwZjBmMDsKfQoKLnJhbmtpbmctaXRlbTpsYXN0LWNoaWxkIHsKICBib3JkZXItYm90dG9tOiBub25lOwp9CgoucmFuay1iYWRnZSB7CiAgbWluLXdpZHRoOiAxOHB4OwogIGhlaWdodDogMThweDsKICBwYWRkaW5nOiAwIDRweDsKICBib3JkZXItcmFkaXVzOiA5cHg7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGp1c3RpZnktY29udGVudDogY2VudGVyOwogIGZvbnQtd2VpZ2h0OiBib2xkOwogIGZvbnQtc2l6ZTogMTBweDsKICBjb2xvcjogd2hpdGU7CiAgZmxleDogMCAwIGF1dG87CiAgbWFyZ2luLXJpZ2h0OiA4cHg7CiAgd2hpdGUtc3BhY2U6IG5vd3JhcDsKfQoKLnJhbmstZmlyc3QgewogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmZjZiNmIsICNlZTVhMjQpOwp9CgoucmFuay1zZWNvbmQgewogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmZWNhNTcsICNmZjlmZjMpOwp9CgoucmFuay10aGlyZCB7CiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzQ4ZGJmYiwgIzBhYmRlMyk7Cn0KCi5yYW5rLW90aGVyIHsKICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjYTRiMGJlLCAjNzQ3ZDhjKTsKfQoKLmJldC1pbmZvIHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgZmxleDogMTsKICBtaW4td2lkdGg6IDA7Cn0KCi5iZXQtbnVtYmVycyB7CiAgZm9udC1zaXplOiAxNXB4OwogIGZvbnQtd2VpZ2h0OiA5MDA7CiAgY29sb3I6ICNmZmZmZmY7CiAgZm9udC1mYW1pbHk6ICdDb3VyaWVyIE5ldycsIG1vbm9zcGFjZTsKICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhLCAjNzY0YmEyKTsKICBwYWRkaW5nOiAzcHggOHB4OwogIGJvcmRlci1yYWRpdXM6IDVweDsKICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgYm94LXNoYWRvdzogMCAycHggNnB4IHJnYmEoMTAyLCAxMjYsIDIzNCwgMC4zKTsKICBsZXR0ZXItc3BhY2luZzogMC41cHg7CiAgZmxleDogMjsKICBtYXJnaW4tcmlnaHQ6IDhweDsKfQoKLmFtb3VudCB7CiAgY29sb3I6ICNlNzRjM2M7CiAgZm9udC13ZWlnaHQ6IDcwMDsKICBmb250LXNpemU6IDE0cHg7CiAgd2hpdGUtc3BhY2U6IG5vd3JhcDsKICBmbGV4OiAxOwogIHRleHQtYWxpZ246IHJpZ2h0Owp9CgoucmVjb3JkLWNvdW50IHsKICBjb2xvcjogIzk1YTVhNjsKICBmb250LXNpemU6IDExcHg7CiAgdGV4dC1hbGlnbjogcmlnaHQ7CiAgbWFyZ2luLXRvcDogMnB4Owp9CgovKiDng63pl6jkuInkvY3mlbDmoLflvI8gKi8KLm51bWJlcnMtc2VjdGlvbiB7CiAgbWFyZ2luLWJvdHRvbTogMzBweDsKfQoKLnJhbmtpbmctdGFibGUgewogIGJvcmRlci1yYWRpdXM6IDhweDsKICBvdmVyZmxvdzogaGlkZGVuOwogIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMSk7Cn0KCi5hbW91bnQtdGV4dCB7CiAgZm9udC13ZWlnaHQ6IDYwMDsKICBjb2xvcjogI2U3NGMzYzsKfQoKLnBlcmNlbnRhZ2UtdGV4dCB7CiAgZm9udC13ZWlnaHQ6IDUwMDsKICBjb2xvcjogIzM0OThkYjsKfQoKLyog54Ot6Zeo5LiJ5L2N5pWw5Y2h54mH5qC35byPICovCi5udW1iZXJzLXJhbmtpbmdzLXNlY3Rpb24gewogIG1hcmdpbi1ib3R0b206IDE1cHg7Cn0KCi5udW1iZXJzLWdyaWQgewogIGRpc3BsYXk6IGdyaWQ7CiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoNSwgMWZyKTsKICBnYXA6IDZweDsKfQoKQG1lZGlhIChtYXgtd2lkdGg6IDE0MDBweCkgewogIC5udW1iZXJzLWdyaWQgewogICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoNCwgMWZyKTsKICB9Cn0KCkBtZWRpYSAobWF4LXdpZHRoOiAxMTAwcHgpIHsKICAubnVtYmVycy1ncmlkIHsKICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDMsIDFmcik7CiAgfQp9CgpAbWVkaWEgKG1heC13aWR0aDogODAwcHgpIHsKICAubnVtYmVycy1ncmlkIHsKICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDIsIDFmcik7CiAgfQp9CgpAbWVkaWEgKG1heC13aWR0aDogNTAwcHgpIHsKICAubnVtYmVycy1ncmlkIHsKICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyOwogIH0KfQoKLm51bWJlcnMtY2FyZCB7CiAgYmFja2dyb3VuZDogd2hpdGU7CiAgYm9yZGVyLXJhZGl1czogOHB4OwogIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMSk7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4zcyBlYXNlLCBib3gtc2hhZG93IDAuM3MgZWFzZTsKICBtaW4td2lkdGg6IDA7CiAgbWluLWhlaWdodDogMTIwcHg7CiAgbWF4LWhlaWdodDogNDAwcHg7IC8qIOWinuWKoOacgOWkp+mrmOW6puS7pemAguW6lOabtOWkmuaVsOaNriAqLwp9CgoubnVtYmVycy1jYXJkOmhvdmVyIHsKICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCk7CiAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMTUpOwp9CgoubnVtYmVycy1jYXJkLWhlYWRlciB7CiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzI3YWU2MCAwJSwgIzJlY2M3MSAxMDAlKTsKICBjb2xvcjogd2hpdGU7CiAgcGFkZGluZzogNHB4IDZweDsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBhbGlnbi1pdGVtczogY2VudGVyOwp9CgoubnVtYmVycy1yYW5raW5nLWxpc3QgewogIHBhZGRpbmc6IDRweDsKICBtYXgtaGVpZ2h0OiAzMjBweDsgLyog6ZmQ5Yi25YiX6KGo6auY5bqm5Lul6YCC5bqU5pu05aSa5pWw5o2uICovCiAgb3ZlcmZsb3cteTogYXV0bzsgLyog5re75Yqg5Z6C55u05rua5Yqo5p2hICovCn0KCi5udW1iZXJzLXJhbmtpbmctaXRlbSB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIHBhZGRpbmc6IDJweCA0cHg7CiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmMGYwZjA7Cn0KCi5udW1iZXJzLXJhbmtpbmctaXRlbTpsYXN0LWNoaWxkIHsKICBib3JkZXItYm90dG9tOiBub25lOwp9CgoubnVtYmVycy1pbmZvIHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgZmxleDogMTsKICBtaW4td2lkdGg6IDA7Cn0KCi5udW1iZXJzLWluZm8gLmhvdC1udW1iZXIgewogIGZvbnQtZmFtaWx5OiAnQ291cmllciBOZXcnLCBtb25vc3BhY2U7CiAgZm9udC13ZWlnaHQ6IDkwMDsKICBmb250LXNpemU6IDEzcHg7CiAgY29sb3I6ICNmZmZmZmY7CiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzI3YWU2MCwgIzJlY2M3MSk7CiAgcGFkZGluZzogMnB4IDRweDsKICBib3JkZXItcmFkaXVzOiAzcHg7CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIGZsZXg6IDI7CiAgbWFyZ2luLXJpZ2h0OiA0cHg7Cn0KCi5udW1iZXJzLWluZm8gLmFtb3VudCB7CiAgY29sb3I6ICNlNzRjM2M7CiAgZm9udC13ZWlnaHQ6IDcwMDsKICBmb250LXNpemU6IDEzcHg7CiAgd2hpdGUtc3BhY2U6IG5vd3JhcDsKICBmbGV4OiAxOwogIHRleHQtYWxpZ246IHJpZ2h0Owp9CgoKCi5kaWFsb2ctZm9vdGVyIHsKICB0ZXh0LWFsaWduOiByaWdodDsKICBwYWRkaW5nLXRvcDogMjBweDsKICBib3JkZXItdG9wOiAxcHggc29saWQgI2VjZjBmMTsKfQo="}, null]}