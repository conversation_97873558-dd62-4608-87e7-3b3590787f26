{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetStatistics.vue?vue&type=style&index=0&id=a37bd9be&prod&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetStatistics.vue", "mtime": 1756002739139}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750942927475}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750942929511}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750942928175}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750942928768}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi8qIOeuoeeQhuWRmOaOp+WItuWMuuWfn+agt+W8jyAqLwouYWRtaW4tY29udHJvbHMgewogIGJvcmRlcjogMXB4IHNvbGlkICNlNGU3ZWQ7CiAgYm9yZGVyLXJhZGl1czogNnB4OwogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmOGY5ZmEgMCUsICNlOWVjZWYgMTAwJSk7Cn0KCi5hZG1pbi1jb250cm9scyAuZWwtc2VsZWN0IHsKICBiYWNrZ3JvdW5kOiB3aGl0ZTsKfQoKLyog5b2p56eN5qCH562+6aG15qC35byPICovCi5sb3R0ZXJ5LXR5cGUtdGFicyB7CiAgbWFyZ2luLWxlZnQ6IDIwcHg7CiAgZmxleDogMTsKfQoKLmxvdHRlcnktdHlwZS10YWJzLWNvbnRhaW5lciB7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICBhbGlnbi1pdGVtczogY2VudGVyOwp9CgoubG90dGVyeS10eXBlLXRhYnMgLmVsLXRhYnMtLWNhcmQgPiAuZWwtdGFic19faGVhZGVyLAoubG90dGVyeS10eXBlLXRhYnMtY29udGFpbmVyIC5lbC10YWJzLS1jYXJkID4gLmVsLXRhYnNfX2hlYWRlciB7CiAgYm9yZGVyLWJvdHRvbTogbm9uZTsKICBtYXJnaW46IDA7Cn0KCi5sb3R0ZXJ5LXR5cGUtdGFicyAuZWwtdGFicy0tY2FyZCA+IC5lbC10YWJzX19oZWFkZXIgLmVsLXRhYnNfX25hdiwKLmxvdHRlcnktdHlwZS10YWJzLWNvbnRhaW5lciAuZWwtdGFicy0tY2FyZCA+IC5lbC10YWJzX19oZWFkZXIgLmVsLXRhYnNfX25hdiB7CiAgYm9yZGVyOiBub25lOwogIGJvcmRlci1yYWRpdXM6IDhweDsKICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjhmOWZhIDAlLCAjZTllY2VmIDEwMCUpOwogIHBhZGRpbmc6IDRweDsKfQoKLmxvdHRlcnktdHlwZS10YWJzIC5lbC10YWJzLS1jYXJkID4gLmVsLXRhYnNfX2hlYWRlciAuZWwtdGFic19faXRlbSwKLmxvdHRlcnktdHlwZS10YWJzLWNvbnRhaW5lciAuZWwtdGFicy0tY2FyZCA+IC5lbC10YWJzX19oZWFkZXIgLmVsLXRhYnNfX2l0ZW0gewogIGJvcmRlcjogbm9uZTsKICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDsKICBjb2xvcjogIzYwNjI2NjsKICBmb250LXdlaWdodDogNTAwOwogIHBhZGRpbmc6IDhweCAxNnB4OwogIG1hcmdpbi1yaWdodDogNHB4OwogIGJvcmRlci1yYWRpdXM6IDZweDsKICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOwogIHBvc2l0aW9uOiByZWxhdGl2ZTsKICBvdmVyZmxvdzogaGlkZGVuOwp9CgoubG90dGVyeS10eXBlLXRhYnMgLmVsLXRhYnMtLWNhcmQgPiAuZWwtdGFic19faGVhZGVyIC5lbC10YWJzX19pdGVtOmhvdmVyLAoubG90dGVyeS10eXBlLXRhYnMtY29udGFpbmVyIC5lbC10YWJzLS1jYXJkID4gLmVsLXRhYnNfX2hlYWRlciAuZWwtdGFic19faXRlbTpob3ZlciB7CiAgY29sb3I6ICM0MDllZmY7CiAgYmFja2dyb3VuZDogcmdiYSg2NCwgMTU4LCAyNTUsIDAuMSk7CiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpOwp9CgoubG90dGVyeS10eXBlLXRhYnMgLmVsLXRhYnMtLWNhcmQgPiAuZWwtdGFic19faGVhZGVyIC5lbC10YWJzX19pdGVtLmlzLWFjdGl2ZSwKLmxvdHRlcnktdHlwZS10YWJzLWNvbnRhaW5lciAuZWwtdGFicy0tY2FyZCA+IC5lbC10YWJzX19oZWFkZXIgLmVsLXRhYnNfX2l0ZW0uaXMtYWN0aXZlIHsKICBjb2xvcjogd2hpdGU7CiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKTsKICBmb250LXdlaWdodDogNjAwOwogIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjMpOwogIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTsKfQoKLmxvdHRlcnktdHlwZS10YWJzIC5lbC10YWJzLS1jYXJkID4gLmVsLXRhYnNfX2hlYWRlciAuZWwtdGFic19faXRlbS5pcy1hY3RpdmU6OmJlZm9yZSwKLmxvdHRlcnktdHlwZS10YWJzLWNvbnRhaW5lciAuZWwtdGFicy0tY2FyZCA+IC5lbC10YWJzX19oZWFkZXIgLmVsLXRhYnNfX2l0ZW0uaXMtYWN0aXZlOjpiZWZvcmUgewogIGNvbnRlbnQ6ICcnOwogIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICB0b3A6IDA7CiAgbGVmdDogMDsKICByaWdodDogMDsKICBib3R0b206IDA7CiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgcmdiYSgyNTUsMjU1LDI1NSwwLjIpIDAlLCB0cmFuc3BhcmVudCAxMDAlKTsKICBwb2ludGVyLWV2ZW50czogbm9uZTsKfQoKLmxvdHRlcnktdHlwZS10YWJzIC5lbC10YWJzLS1jYXJkID4gLmVsLXRhYnNfX2hlYWRlciAuZWwtdGFic19faXRlbSBpLAoubG90dGVyeS10eXBlLXRhYnMtY29udGFpbmVyIC5lbC10YWJzLS1jYXJkID4gLmVsLXRhYnNfX2hlYWRlciAuZWwtdGFic19faXRlbSBpIHsKICBtYXJnaW4tcmlnaHQ6IDZweDsKICBmb250LXNpemU6IDE2cHg7Cn0KCi5sb3R0ZXJ5LXR5cGUtdGFicyAuZWwtdGFic19fY29udGVudCwKLmxvdHRlcnktdHlwZS10YWJzLWNvbnRhaW5lciAuZWwtdGFic19fY29udGVudCB7CiAgZGlzcGxheTogbm9uZTsgLyog6ZqQ6JeP5YaF5a655Yy65Z+f77yM5Zug5Li65oiR5Lus5Y+q55So5qCH562+6aG15YiH5o2iICovCn0KCi5zdGF0aXN0aWNzLWRpYWxvZyB7CiAgLmVsLWRpYWxvZyB7CiAgICBib3JkZXItcmFkaXVzOiAxMnB4OwogIH0KICAKICAuZWwtZGlhbG9nX19oZWFkZXIgewogICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKTsKICAgIGNvbG9yOiB3aGl0ZTsKICAgIHBhZGRpbmc6IDIwcHggMjRweDsKICB9CiAgCiAgLmVsLWRpYWxvZ19fdGl0bGUgewogICAgY29sb3I6IHdoaXRlOwogICAgZm9udC13ZWlnaHQ6IDYwMDsKICAgIGZvbnQtc2l6ZTogMThweDsKICB9Cn0KCi5zdGF0aXN0aWNzLWNvbnRhaW5lciB7CiAgcGFkZGluZzogMTBweCAwOwp9CgoubWFpbi1zZWN0aW9uLXRpdGxlIHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgZm9udC1zaXplOiAxNnB4OwogIGZvbnQtd2VpZ2h0OiA2MDA7CiAgY29sb3I6ICMyYzNlNTA7CiAgbWFyZ2luLWJvdHRvbTogOHB4OwogIHBhZGRpbmctYm90dG9tOiA0cHg7CiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlY2YwZjE7Cn0KCi5tYWluLXNlY3Rpb24tdGl0bGUgaSB7CiAgbWFyZ2luLXJpZ2h0OiA0cHg7CiAgY29sb3I6ICM2NjdlZWE7CiAgZm9udC1zaXplOiAxNHB4Owp9CgovKiDnjqnms5XmjpLooYzmppzmoLflvI8gKi8KLm1ldGhvZC1yYW5raW5ncy1zZWN0aW9uIHsKICBtYXJnaW4tYm90dG9tOiAxNXB4Owp9CgoubWV0aG9kLWdyaWQgewogIGRpc3BsYXk6IGdyaWQ7CiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoNSwgMWZyKTsKICBnYXA6IDZweDsKfQoKQG1lZGlhIChtYXgtd2lkdGg6IDE0MDBweCkgewogIC5tZXRob2QtZ3JpZCB7CiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCg0LCAxZnIpOwogIH0KfQoKQG1lZGlhIChtYXgtd2lkdGg6IDExMDBweCkgewogIC5tZXRob2QtZ3JpZCB7CiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCgzLCAxZnIpOwogIH0KfQoKQG1lZGlhIChtYXgtd2lkdGg6IDgwMHB4KSB7CiAgLm1ldGhvZC1ncmlkIHsKICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDIsIDFmcik7CiAgfQp9CgpAbWVkaWEgKG1heC13aWR0aDogNTAwcHgpIHsKICAubWV0aG9kLWdyaWQgewogICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnI7CiAgfQp9CgoubWV0aG9kLWNhcmQgewogIGJhY2tncm91bmQ6IHdoaXRlOwogIGJvcmRlci1yYWRpdXM6IDEwcHg7CiAgYm94LXNoYWRvdzogMCAzcHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMTIpOwogIG92ZXJmbG93OiBoaWRkZW47CiAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuM3MgZWFzZSwgYm94LXNoYWRvdyAwLjNzIGVhc2U7CiAgbWluLXdpZHRoOiAwOyAvKiDpmLLmraLlhoXlrrnmuqLlh7ogKi8KICBtaW4taGVpZ2h0OiAyMDBweDsgLyog5pyA5bCP6auY5bqmICovCiAgbWF4LWhlaWdodDogNTAwcHg7IC8qIOWinuWKoOacgOWkp+mrmOW6puS7pemAguW6lOabtOWkmuaVsOaNriAqLwp9CgoubWV0aG9kLWNhcmQ6aG92ZXIgewogIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTsKICBib3gtc2hhZG93OiAwIDZweCAyMHB4IHJnYmEoMCwgMCwgMCwgMC4xOCk7Cn0KCi5tZXRob2QtY2FyZC1oZWFkZXIgewogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7CiAgY29sb3I6IHdoaXRlOwogIHBhZGRpbmc6IDhweCAxMnB4OwogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7Cn0KCi5tZXRob2QtbmFtZSB7CiAgZm9udC1zaXplOiAxNXB4OwogIGZvbnQtd2VpZ2h0OiA3MDA7CiAgd2hpdGUtc3BhY2U6IG5vd3JhcDsKICBvdmVyZmxvdzogaGlkZGVuOwogIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOwp9CgoubWV0aG9kLWNvdW50IHsKICBmb250LXNpemU6IDEzcHg7CiAgb3BhY2l0eTogMC45Owp9CgoucmFua2luZy1saXN0IHsKICBwYWRkaW5nOiA4cHg7CiAgbWF4LWhlaWdodDogNDIwcHg7IC8qIOWinuWKoOWIl+ihqOmrmOW6puS7pemAguW6lOabtOWkmuaVsOaNriAqLwogIG92ZXJmbG93LXk6IGF1dG87IC8qIOa3u+WKoOWeguebtOa7muWKqOadoSAqLwp9CgovKiDmu5rliqjmnaHmoLflvI8gKi8KLnJhbmtpbmctbGlzdDo6LXdlYmtpdC1zY3JvbGxiYXIgewogIHdpZHRoOiA0cHg7Cn0KCi5yYW5raW5nLWxpc3Q6Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHsKICBiYWNrZ3JvdW5kOiAjZjFmMWYxOwogIGJvcmRlci1yYWRpdXM6IDJweDsKfQoKLnJhbmtpbmctbGlzdDo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIgewogIGJhY2tncm91bmQ6ICNjMWMxYzE7CiAgYm9yZGVyLXJhZGl1czogMnB4Owp9CgoucmFua2luZy1saXN0Ojotd2Via2l0LXNjcm9sbGJhci10aHVtYjpob3ZlciB7CiAgYmFja2dyb3VuZDogI2E4YThhODsKfQoKLyog54Ot6Zeo5LiJ5L2N5pWw5o6S6KGM5qac5rua5Yqo5p2h5qC35byPICovCi5udW1iZXJzLXJhbmtpbmctbGlzdDo6LXdlYmtpdC1zY3JvbGxiYXIgewogIHdpZHRoOiA0cHg7Cn0KCi5udW1iZXJzLXJhbmtpbmctbGlzdDo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sgewogIGJhY2tncm91bmQ6ICNmMWYxZjE7CiAgYm9yZGVyLXJhZGl1czogMnB4Owp9CgoubnVtYmVycy1yYW5raW5nLWxpc3Q6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHsKICBiYWNrZ3JvdW5kOiAjYzFjMWMxOwogIGJvcmRlci1yYWRpdXM6IDJweDsKfQoKLm51bWJlcnMtcmFua2luZy1saXN0Ojotd2Via2l0LXNjcm9sbGJhci10aHVtYjpob3ZlciB7CiAgYmFja2dyb3VuZDogI2E4YThhODsKfQoKLnJhbmtpbmctaXRlbSB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIHBhZGRpbmc6IDRweCA4cHg7CiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmMGYwZjA7Cn0KCi5yYW5raW5nLWl0ZW06bGFzdC1jaGlsZCB7CiAgYm9yZGVyLWJvdHRvbTogbm9uZTsKfQoKLnJhbmstYmFkZ2UgewogIG1pbi13aWR0aDogMThweDsKICBoZWlnaHQ6IDE4cHg7CiAgcGFkZGluZzogMCA0cHg7CiAgYm9yZGVyLXJhZGl1czogOXB4OwogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICBmb250LXdlaWdodDogYm9sZDsKICBmb250LXNpemU6IDEwcHg7CiAgY29sb3I6IHdoaXRlOwogIGZsZXg6IDAgMCBhdXRvOwogIG1hcmdpbi1yaWdodDogOHB4OwogIHdoaXRlLXNwYWNlOiBub3dyYXA7Cn0KCi5yYW5rLWZpcnN0IHsKICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZmY2YjZiLCAjZWU1YTI0KTsKfQoKLnJhbmstc2Vjb25kIHsKICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZmVjYTU3LCAjZmY5ZmYzKTsKfQoKLnJhbmstdGhpcmQgewogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM0OGRiZmIsICMwYWJkZTMpOwp9CgoucmFuay1vdGhlciB7CiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2E0YjBiZSwgIzc0N2Q4Yyk7Cn0KCi5iZXQtaW5mbyB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGZsZXg6IDE7CiAgbWluLXdpZHRoOiAwOwp9CgouYmV0LW51bWJlcnMgewogIGZvbnQtc2l6ZTogMTVweDsKICBmb250LXdlaWdodDogOTAwOwogIGNvbG9yOiAjZmZmZmZmOwogIGZvbnQtZmFtaWx5OiAnQ291cmllciBOZXcnLCBtb25vc3BhY2U7CiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSwgIzc2NGJhMik7CiAgcGFkZGluZzogM3B4IDhweDsKICBib3JkZXItcmFkaXVzOiA1cHg7CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIGJveC1zaGFkb3c6IDAgMnB4IDZweCByZ2JhKDEwMiwgMTI2LCAyMzQsIDAuMyk7CiAgbGV0dGVyLXNwYWNpbmc6IDAuNXB4OwogIGZsZXg6IDI7CiAgbWFyZ2luLXJpZ2h0OiA4cHg7Cn0KCi5hbW91bnQgewogIGNvbG9yOiAjZTc0YzNjOwogIGZvbnQtd2VpZ2h0OiA3MDA7CiAgZm9udC1zaXplOiAxNHB4OwogIHdoaXRlLXNwYWNlOiBub3dyYXA7CiAgZmxleDogMTsKICB0ZXh0LWFsaWduOiByaWdodDsKfQoKLnJlY29yZC1jb3VudCB7CiAgY29sb3I6ICM5NWE1YTY7CiAgZm9udC1zaXplOiAxMXB4OwogIHRleHQtYWxpZ246IHJpZ2h0OwogIG1hcmdpbi10b3A6IDJweDsKfQoKLyog54Ot6Zeo5LiJ5L2N5pWw5qC35byPICovCi5udW1iZXJzLXNlY3Rpb24gewogIG1hcmdpbi1ib3R0b206IDMwcHg7Cn0KCi5yYW5raW5nLXRhYmxlIHsKICBib3JkZXItcmFkaXVzOiA4cHg7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpOwp9CgouYW1vdW50LXRleHQgewogIGZvbnQtd2VpZ2h0OiA2MDA7CiAgY29sb3I6ICNlNzRjM2M7Cn0KCi5wZXJjZW50YWdlLXRleHQgewogIGZvbnQtd2VpZ2h0OiA1MDA7CiAgY29sb3I6ICMzNDk4ZGI7Cn0KCi8qIOeDremXqOS4ieS9jeaVsOWNoeeJh+agt+W8jyAqLwoubnVtYmVycy1yYW5raW5ncy1zZWN0aW9uIHsKICBtYXJnaW4tYm90dG9tOiAxNXB4Owp9CgoubnVtYmVycy1ncmlkIHsKICBkaXNwbGF5OiBncmlkOwogIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDUsIDFmcik7CiAgZ2FwOiA2cHg7Cn0KCkBtZWRpYSAobWF4LXdpZHRoOiAxNDAwcHgpIHsKICAubnVtYmVycy1ncmlkIHsKICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDQsIDFmcik7CiAgfQp9CgpAbWVkaWEgKG1heC13aWR0aDogMTEwMHB4KSB7CiAgLm51bWJlcnMtZ3JpZCB7CiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCgzLCAxZnIpOwogIH0KfQoKQG1lZGlhIChtYXgtd2lkdGg6IDgwMHB4KSB7CiAgLm51bWJlcnMtZ3JpZCB7CiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCgyLCAxZnIpOwogIH0KfQoKQG1lZGlhIChtYXgtd2lkdGg6IDUwMHB4KSB7CiAgLm51bWJlcnMtZ3JpZCB7CiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmcjsKICB9Cn0KCi5udW1iZXJzLWNhcmQgewogIGJhY2tncm91bmQ6IHdoaXRlOwogIGJvcmRlci1yYWRpdXM6IDhweDsKICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpOwogIG92ZXJmbG93OiBoaWRkZW47CiAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuM3MgZWFzZSwgYm94LXNoYWRvdyAwLjNzIGVhc2U7CiAgbWluLXdpZHRoOiAwOwogIG1pbi1oZWlnaHQ6IDEyMHB4OwogIG1heC1oZWlnaHQ6IDQwMHB4OyAvKiDlop7liqDmnIDlpKfpq5jluqbku6XpgILlupTmm7TlpJrmlbDmja4gKi8KfQoKLm51bWJlcnMtY2FyZDpob3ZlciB7CiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpOwogIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjE1KTsKfQoKLm51bWJlcnMtY2FyZC1oZWFkZXIgewogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMyN2FlNjAgMCUsICMyZWNjNzEgMTAwJSk7CiAgY29sb3I6IHdoaXRlOwogIHBhZGRpbmc6IDRweCA2cHg7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKfQoKLm51bWJlcnMtcmFua2luZy1saXN0IHsKICBwYWRkaW5nOiA0cHg7CiAgbWF4LWhlaWdodDogMzIwcHg7IC8qIOmZkOWItuWIl+ihqOmrmOW6puS7pemAguW6lOabtOWkmuaVsOaNriAqLwogIG92ZXJmbG93LXk6IGF1dG87IC8qIOa3u+WKoOWeguebtOa7muWKqOadoSAqLwp9CgoubnVtYmVycy1yYW5raW5nLWl0ZW0gewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBwYWRkaW5nOiAycHggNHB4OwogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZjBmMGYwOwp9CgoubnVtYmVycy1yYW5raW5nLWl0ZW06bGFzdC1jaGlsZCB7CiAgYm9yZGVyLWJvdHRvbTogbm9uZTsKfQoKLm51bWJlcnMtaW5mbyB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGZsZXg6IDE7CiAgbWluLXdpZHRoOiAwOwp9CgoubnVtYmVycy1pbmZvIC5ob3QtbnVtYmVyIHsKICBmb250LWZhbWlseTogJ0NvdXJpZXIgTmV3JywgbW9ub3NwYWNlOwogIGZvbnQtd2VpZ2h0OiA5MDA7CiAgZm9udC1zaXplOiAxM3B4OwogIGNvbG9yOiAjZmZmZmZmOwogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMyN2FlNjAsICMyZWNjNzEpOwogIHBhZGRpbmc6IDJweCA0cHg7CiAgYm9yZGVyLXJhZGl1czogM3B4OwogIHRleHQtYWxpZ246IGNlbnRlcjsKICBmbGV4OiAyOwogIG1hcmdpbi1yaWdodDogNHB4Owp9CgoubnVtYmVycy1pbmZvIC5hbW91bnQgewogIGNvbG9yOiAjZTc0YzNjOwogIGZvbnQtd2VpZ2h0OiA3MDA7CiAgZm9udC1zaXplOiAxM3B4OwogIHdoaXRlLXNwYWNlOiBub3dyYXA7CiAgZmxleDogMTsKICB0ZXh0LWFsaWduOiByaWdodDsKfQoKCgouZGlhbG9nLWZvb3RlciB7CiAgdGV4dC1hbGlnbjogcmlnaHQ7CiAgcGFkZGluZy10b3A6IDIwcHg7CiAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlY2YwZjE7Cn0KCi8qIOaQnOe0ouWKn+iDveagt+W8jyAqLwouc2VhcmNoLWNvbnRhaW5lciB7CiAgcGFkZGluZzogMTVweCAyMHB4OwogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmOGY5ZmEgMCUsICNlOWVjZWYgMTAwJSk7CiAgYm9yZGVyLXJhZGl1czogOHB4OwogIGJvcmRlcjogMXB4IHNvbGlkICNlNGU3ZWQ7Cn0KCi5zZWFyY2gtYm94IHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgZmxleC13cmFwOiB3cmFwOwogIGdhcDogMTBweDsKfQoKLnNlYXJjaC1ib3ggLmVsLWlucHV0IHsKICBtYXgtd2lkdGg6IDMwMHB4OwogIGZsZXg6IDE7CiAgbWluLXdpZHRoOiAyMDBweDsKfQoKLnNlYXJjaC1ib3ggLmVsLWJ1dHRvbiB7CiAgYm9yZGVyLXJhZGl1czogNnB4OwogIGZvbnQtd2VpZ2h0OiA1MDA7CiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsKfQoKLnNlYXJjaC1ib3ggLmVsLWJ1dHRvbjpob3ZlciB7CiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpOwogIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjE1KTsKfQoKLnNlYXJjaC1yZXN1bHQtdGlwIHsKICB0ZXh0LWFsaWduOiBjZW50ZXI7Cn0KCi5zZWFyY2gtcmVzdWx0LXRpcCAuZWwtYWxlcnQgewogIGJvcmRlci1yYWRpdXM6IDZweDsKfQoKLnNlYXJjaC1yZXN1bHQtdGlwIC5lbC1hbGVydC0taW5mbyB7CiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSg2NCwgMTU4LCAyNTUsIDAuMSk7CiAgYm9yZGVyLWNvbG9yOiByZ2JhKDY0LCAxNTgsIDI1NSwgMC4yKTsKfQoKLyog5ZON5bqU5byP6LCD5pW0ICovCkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkgewogIC5zZWFyY2gtYm94IHsKICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgICBhbGlnbi1pdGVtczogc3RyZXRjaDsKICB9CgogIC5zZWFyY2gtYm94IC5lbC1pbnB1dCB7CiAgICBtYXgtd2lkdGg6IG5vbmU7CiAgICB3aWR0aDogMTAwJTsKICB9CgogIC5zZWFyY2gtYm94IC5lbC1idXR0b24gewogICAgd2lkdGg6IDEwMCU7CiAgICBtYXJnaW4tdG9wOiA1cHg7CiAgfQp9Cg=="}, null]}