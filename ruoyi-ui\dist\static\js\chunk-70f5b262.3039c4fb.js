(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-70f5b262","chunk-269fa9d1","chunk-240d66cb","chunk-7910d0ad"],{"1e8b":function(e,t,s){"use strict";s.r(t);var a=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"user-info-container"},[e._m(0),s("el-form",{ref:"form",staticClass:"user-info-form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[s("el-row",{attrs:{gutter:24}},[s("el-col",{attrs:{lg:12,md:24}},[s("el-form-item",{attrs:{label:"用户昵称",prop:"nickName"}},[s("div",{staticClass:"input-wrapper"},[s("el-input",{staticClass:"form-input",attrs:{maxlength:"30",placeholder:"请输入用户昵称","prefix-icon":"el-icon-user"},model:{value:e.form.nickName,callback:function(t){e.$set(e.form,"nickName",t)},expression:"form.nickName"}})],1)])],1),s("el-col",{attrs:{lg:12,md:24}},[s("el-form-item",{attrs:{label:"手机号码",prop:"phonenumber"}},[s("div",{staticClass:"input-wrapper"},[s("el-input",{staticClass:"form-input",attrs:{maxlength:"11",placeholder:"请输入手机号码","prefix-icon":"el-icon-phone"},model:{value:e.form.phonenumber,callback:function(t){e.$set(e.form,"phonenumber",t)},expression:"form.phonenumber"}})],1)])],1)],1),s("el-row",{attrs:{gutter:24}},[s("el-col",{attrs:{lg:12,md:24}},[s("el-form-item",{attrs:{label:"邮箱地址",prop:"email"}},[s("div",{staticClass:"input-wrapper"},[s("el-input",{staticClass:"form-input",attrs:{maxlength:"50",placeholder:"请输入邮箱地址","prefix-icon":"el-icon-message"},model:{value:e.form.email,callback:function(t){e.$set(e.form,"email",t)},expression:"form.email"}})],1)])],1),s("el-col",{attrs:{lg:12,md:24}},[s("el-form-item",{attrs:{label:"性别"}},[s("div",{staticClass:"radio-wrapper"},[s("el-radio-group",{staticClass:"gender-radio",model:{value:e.form.sex,callback:function(t){e.$set(e.form,"sex",t)},expression:"form.sex"}},[s("el-radio",{staticClass:"gender-option",attrs:{label:"0"}},[s("i",{staticClass:"el-icon-male"}),e._v(" 男 ")]),s("el-radio",{staticClass:"gender-option",attrs:{label:"1"}},[s("i",{staticClass:"el-icon-female"}),e._v(" 女 ")])],1)],1)])],1)],1),s("div",{staticClass:"form-actions"},[s("el-button",{staticClass:"save-btn",attrs:{type:"primary"},on:{click:e.submit}},[s("i",{staticClass:"el-icon-check"}),e._v(" 保存修改 ")]),s("el-button",{staticClass:"cancel-btn",on:{click:e.close}},[s("i",{staticClass:"el-icon-close"}),e._v(" 取消 ")])],1)],1)],1)},r=[function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"form-header"},[s("h4",[e._v("基本信息")]),s("p",[e._v("更新您的个人基本信息")])])}],i=s("c0c7"),n={props:{user:{type:Object}},data:function(){return{form:{},rules:{nickName:[{required:!0,message:"用户昵称不能为空",trigger:"blur"}],email:[{required:!0,message:"邮箱地址不能为空",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:["blur","change"]}],phonenumber:[{required:!0,message:"手机号码不能为空",trigger:"blur"},{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"请输入正确的手机号码",trigger:"blur"}]}}},watch:{user:{handler:function(e){e&&(this.form={nickName:e.nickName,phonenumber:e.phonenumber,email:e.email,sex:e.sex})},immediate:!0}},methods:{submit:function(){var e=this;this.$refs["form"].validate((function(t){t&&Object(i["updateUserProfile"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.user.phonenumber=e.form.phonenumber,e.user.email=e.form.email,e.$emit("refresh")}))}))},close:function(){this.$tab.closePage()}}},o=n,l=(s("fd1a"),s("2877")),c=Object(l["a"])(o,a,r,!1,null,"02063764",null);t["default"]=c.exports},"30d7":function(e,t,s){},"3fa1":function(e,t,s){},"4c1b":function(e,t,s){"use strict";s.r(t);var a=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"profile-container"},[e._m(0),s("div",{staticClass:"profile-main"},[s("el-row",{attrs:{gutter:24}},[s("el-col",{attrs:{lg:8,md:24,sm:24}},[s("div",{staticClass:"user-card"},[s("div",{staticClass:"user-avatar-section"},[s("div",{staticClass:"avatar-container"},[s("img",{staticClass:"user-avatar",attrs:{src:e.displayAvatar,alt:"用户头像"}}),s("div",{staticClass:"avatar-overlay"},[s("i",{staticClass:"el-icon-camera"})])]),s("div",{staticClass:"user-basic-info"},[s("h3",{staticClass:"user-name"},[e._v(e._s(e.user.nickName||e.user.userName))]),s("p",{staticClass:"user-role"},[e._v(e._s(e.roleGroup))]),s("div",{staticClass:"user-status"},[s("el-tag",{attrs:{type:"success",size:"small"}},[s("i",{staticClass:"el-icon-check"}),e._v(" 账户正常 ")])],1)])]),s("div",{staticClass:"user-stats"},[s("div",{staticClass:"stat-item"},[s("div",{staticClass:"stat-value"},[e._v(e._s(e.formatDate(e.user.loginDate)))]),s("div",{staticClass:"stat-label"},[e._v("最后登录")])]),s("div",{staticClass:"stat-item"},[s("div",{staticClass:"stat-value"},[e._v(e._s(e.formatDate(e.user.createTime)))]),s("div",{staticClass:"stat-label"},[e._v("注册时间")])])])])]),s("el-col",{attrs:{lg:16,md:24,sm:24}},[s("div",{staticClass:"form-card"},[s("el-tabs",{staticClass:"profile-tabs",model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}},[s("el-tab-pane",{attrs:{name:"userinfo"}},[s("span",{attrs:{slot:"label"},slot:"label"},[s("i",{staticClass:"el-icon-edit"}),e._v(" 基本资料 ")]),s("userInfo",{attrs:{user:e.user},on:{refresh:e.getUser}})],1),s("el-tab-pane",{attrs:{name:"resetPwd"}},[s("span",{attrs:{slot:"label"},slot:"label"},[s("i",{staticClass:"el-icon-lock"}),e._v(" 修改密码 ")]),s("resetPwd")],1)],1)],1)])],1)],1)])},r=[function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"profile-header"},[s("div",{staticClass:"header-content"},[s("div",{staticClass:"header-icon"},[s("i",{staticClass:"el-icon-user"})]),s("div",{staticClass:"header-info"},[s("h2",[e._v("个人中心")]),s("p",[e._v("管理您的个人信息和账户设置")])])])])}],i=s("9429"),n=s("1e8b"),o=s("ee46"),l=s("c0c7"),c={name:"Profile",components:{userAvatar:i["default"],userInfo:n["default"],resetPwd:o["default"]},data:function(){return{user:{},roleGroup:{},postGroup:{},activeTab:"userinfo",userAvatar:""}},computed:{displayAvatar:function(){return this.user.avatar||s("4b94")}},created:function(){this.getUser()},methods:{getUser:function(){var e=this;Object(l["getUserProfile"])().then((function(t){e.user=t.data,e.roleGroup=t.roleGroup,e.postGroup=t.postGroup,t.createTime&&(e.user.createTime=t.createTime),t.loginDate&&(e.user.loginDate=t.loginDate)}))},formatDate:function(e){if(!e)return"--";try{var t=new Date(e);return isNaN(t.getTime())?"--":t.toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}catch(s){return console.error("日期格式化错误:",s),"--"}}}},u=c,d=(s("5055"),s("2877")),p=Object(d["a"])(u,a,r,!1,null,"68f1a635",null);t["default"]=p.exports},5055:function(e,t,s){"use strict";s("3fa1")},9429:function(e,t,s){"use strict";s.r(t);var a=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",[s("div",{staticClass:"user-info-head",on:{click:function(t){return e.editCropper()}}},[s("img",{staticClass:"img-circle img-lg",attrs:{src:e.options.img,title:"点击上传头像"}})]),s("el-dialog",{attrs:{title:e.title,visible:e.open,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.open=t},opened:e.modalOpened,close:e.closeDialog}},[s("el-row",[s("el-col",{style:{height:"350px"},attrs:{xs:24,md:12}},[e.visible?s("vue-cropper",{ref:"cropper",attrs:{img:e.options.img,info:!0,autoCrop:e.options.autoCrop,autoCropWidth:e.options.autoCropWidth,autoCropHeight:e.options.autoCropHeight,fixedBox:e.options.fixedBox,outputType:e.options.outputType},on:{realTime:e.realTime}}):e._e()],1),s("el-col",{style:{height:"350px"},attrs:{xs:24,md:12}},[s("div",{staticClass:"avatar-upload-preview"},[s("img",{style:e.previews.img,attrs:{src:e.previews.url}})])])],1),s("br"),s("el-row",[s("el-col",{attrs:{lg:2,sm:3,xs:3}},[s("el-upload",{attrs:{action:"#","http-request":e.requestUpload,"show-file-list":!1,"before-upload":e.beforeUpload}},[s("el-button",{attrs:{size:"small"}},[e._v(" 选择 "),s("i",{staticClass:"el-icon-upload el-icon--right"})])],1)],1),s("el-col",{attrs:{lg:{span:1,offset:2},sm:2,xs:2}},[s("el-button",{attrs:{icon:"el-icon-plus",size:"small"},on:{click:function(t){return e.changeScale(1)}}})],1),s("el-col",{attrs:{lg:{span:1,offset:1},sm:2,xs:2}},[s("el-button",{attrs:{icon:"el-icon-minus",size:"small"},on:{click:function(t){return e.changeScale(-1)}}})],1),s("el-col",{attrs:{lg:{span:1,offset:1},sm:2,xs:2}},[s("el-button",{attrs:{icon:"el-icon-refresh-left",size:"small"},on:{click:function(t){return e.rotateLeft()}}})],1),s("el-col",{attrs:{lg:{span:1,offset:1},sm:2,xs:2}},[s("el-button",{attrs:{icon:"el-icon-refresh-right",size:"small"},on:{click:function(t){return e.rotateRight()}}})],1),s("el-col",{attrs:{lg:{span:2,offset:6},sm:2,xs:2}},[s("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.uploadImg()}}},[e._v("提 交")])],1)],1)],1)],1)},r=[],i=(s("b0c0"),s("4360")),n=s("7e79"),o=s("c0c7"),l=s("ed08"),c={components:{VueCropper:n["VueCropper"]},data:function(){return{open:!1,visible:!1,title:"修改头像",options:{img:i["a"].getters.avatar,autoCrop:!0,autoCropWidth:200,autoCropHeight:200,fixedBox:!0,outputType:"png",filename:"avatar"},previews:{},resizeHandler:null}},methods:{editCropper:function(){this.open=!0},modalOpened:function(){var e=this;this.visible=!0,this.resizeHandler||(this.resizeHandler=Object(l["b"])((function(){e.refresh()}),100)),window.addEventListener("resize",this.resizeHandler)},refresh:function(){this.$refs.cropper.refresh()},requestUpload:function(){},rotateLeft:function(){this.$refs.cropper.rotateLeft()},rotateRight:function(){this.$refs.cropper.rotateRight()},changeScale:function(e){e=e||1,this.$refs.cropper.changeScale(e)},beforeUpload:function(e){var t=this;if(-1==e.type.indexOf("image/"))this.$modal.msgError("文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。");else{var s=new FileReader;s.readAsDataURL(e),s.onload=function(){t.options.img=s.result,t.options.filename=e.name}}},uploadImg:function(){var e=this;this.$refs.cropper.getCropBlob((function(t){var s=new FormData;s.append("avatarfile",t,e.options.filename),Object(o["uploadAvatar"])(s).then((function(t){e.open=!1,e.options.img="/prod-api"+t.imgUrl,i["a"].commit("SET_AVATAR",e.options.img),e.$modal.msgSuccess("修改成功"),e.visible=!1}))}))},realTime:function(e){this.previews=e},closeDialog:function(){this.options.img=i["a"].getters.avatar,this.visible=!1,window.removeEventListener("resize",this.resizeHandler)}}},u=c,d=(s("b59e"),s("2877")),p=Object(d["a"])(u,a,r,!1,null,"026db168",null);t["default"]=p.exports},9532:function(e,t,s){"use strict";s("b16f")},9558:function(e,t,s){},b16f:function(e,t,s){},b59e:function(e,t,s){"use strict";s("9558")},ed08:function(e,t,s){"use strict";s.d(t,"b",(function(){return a})),s.d(t,"e",(function(){return r})),s.d(t,"c",(function(){return i})),s.d(t,"a",(function(){return n})),s.d(t,"f",(function(){return o})),s.d(t,"d",(function(){return l}));s("53ca"),s("d9e2"),s("a630"),s("a15b"),s("d81d"),s("14d9"),s("fb6a"),s("b64b"),s("d3b7"),s("4d63"),s("c607"),s("ac1f"),s("2c3e"),s("00b4"),s("25f0"),s("6062"),s("1e70"),s("79a4"),s("c1a1"),s("8b00"),s("a4e7"),s("1e5a"),s("72c3"),s("3ca3"),s("466d"),s("5319"),s("0643"),s("4e3e"),s("a573"),s("159b"),s("ddb0"),s("c38a");function a(e,t,s){var a,r,i,n,o,l=function(){var c=+new Date-n;c<t&&c>0?a=setTimeout(l,t-c):(a=null,s||(o=e.apply(i,r),a||(i=r=null)))};return function(){for(var r=arguments.length,c=new Array(r),u=0;u<r;u++)c[u]=arguments[u];i=this,n=+new Date;var d=s&&!a;return a||(a=setTimeout(l,t)),d&&(o=e.apply(i,c),i=c=null),o}}function r(e,t){for(var s=Object.create(null),a=e.split(","),r=0;r<a.length;r++)s[a[r]]=!0;return t?function(e){return s[e.toLowerCase()]}:function(e){return s[e]}}var i="export default ",n={html:{indent_size:"2",indent_char:" ",max_preserve_newlines:"-1",preserve_newlines:!1,keep_array_indentation:!1,break_chained_methods:!1,indent_scripts:"separate",brace_style:"end-expand",space_before_conditional:!0,unescape_strings:!1,jslint_happy:!1,end_with_newline:!0,wrap_line_length:"110",indent_inner_html:!0,comma_first:!1,e4x:!0,indent_empty_lines:!0},js:{indent_size:"2",indent_char:" ",max_preserve_newlines:"-1",preserve_newlines:!1,keep_array_indentation:!1,break_chained_methods:!1,indent_scripts:"normal",brace_style:"end-expand",space_before_conditional:!0,unescape_strings:!1,jslint_happy:!0,end_with_newline:!0,wrap_line_length:"110",indent_inner_html:!0,comma_first:!1,e4x:!0,indent_empty_lines:!0}};function o(e){return e.replace(/( |^)[a-z]/g,(function(e){return e.toUpperCase()}))}function l(e){return/^[+-]?(0|([1-9]\d*))(\.\d+)?$/g.test(e)}},ee46:function(e,t,s){"use strict";s.r(t);var a=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"reset-pwd-container"},[e._m(0),e._m(1),s("el-form",{ref:"form",staticClass:"reset-pwd-form",attrs:{model:e.user,rules:e.rules,"label-width":"100px"}},[s("el-form-item",{attrs:{label:"当前密码",prop:"oldPassword"}},[s("div",{staticClass:"input-wrapper"},[s("el-input",{staticClass:"form-input",attrs:{placeholder:"请输入当前密码",type:"password","show-password":"","prefix-icon":"el-icon-lock"},model:{value:e.user.oldPassword,callback:function(t){e.$set(e.user,"oldPassword",t)},expression:"user.oldPassword"}})],1)]),s("el-form-item",{attrs:{label:"新密码",prop:"newPassword"}},[s("div",{staticClass:"input-wrapper"},[s("el-input",{staticClass:"form-input",attrs:{placeholder:"请输入新密码",type:"password","show-password":"","prefix-icon":"el-icon-key"},on:{input:e.checkPasswordStrength},model:{value:e.user.newPassword,callback:function(t){e.$set(e.user,"newPassword",t)},expression:"user.newPassword"}})],1),e.user.newPassword?s("div",{staticClass:"password-strength"},[s("div",{staticClass:"strength-label"},[e._v("密码强度：")]),s("div",{staticClass:"strength-bar"},[s("div",{staticClass:"strength-fill",class:e.passwordStrength.class,style:{width:e.passwordStrength.width}})]),s("span",{staticClass:"strength-text",class:e.passwordStrength.class},[e._v(" "+e._s(e.passwordStrength.text)+" ")])]):e._e()]),s("el-form-item",{attrs:{label:"确认密码",prop:"confirmPassword"}},[s("div",{staticClass:"input-wrapper"},[s("el-input",{staticClass:"form-input",attrs:{placeholder:"请再次输入新密码",type:"password","show-password":"","prefix-icon":"el-icon-check"},model:{value:e.user.confirmPassword,callback:function(t){e.$set(e.user,"confirmPassword",t)},expression:"user.confirmPassword"}})],1)]),s("div",{staticClass:"form-actions"},[s("el-button",{staticClass:"save-btn",attrs:{type:"primary"},on:{click:e.submit}},[s("i",{staticClass:"el-icon-check"}),e._v(" 修改密码 ")]),s("el-button",{staticClass:"cancel-btn",on:{click:e.close}},[s("i",{staticClass:"el-icon-close"}),e._v(" 取消 ")])],1)],1)],1)},r=[function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"form-header"},[s("h4",[e._v("修改密码")]),s("p",[e._v("为了账户安全，请定期更换密码")])])},function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"security-tips"},[s("div",{staticClass:"tip-item"},[s("i",{staticClass:"el-icon-info"}),s("span",[e._v("密码长度为6-20个字符")])]),s("div",{staticClass:"tip-item"},[s("i",{staticClass:"el-icon-info"}),s("span",[e._v("不能包含特殊字符：< > \" ' \\ |")])])])}],i=(s("d9e2"),s("14d9"),s("ac1f"),s("00b4"),s("c0c7")),n={data:function(){var e=this,t=function(t,s,a){e.user.newPassword!==s?a(new Error("两次输入的密码不一致")):a()};return{user:{oldPassword:void 0,newPassword:void 0,confirmPassword:void 0},passwordStrength:{width:"0%",class:"",text:""},rules:{oldPassword:[{required:!0,message:"旧密码不能为空",trigger:"blur"}],newPassword:[{required:!0,message:"新密码不能为空",trigger:"blur"},{min:6,max:20,message:"长度在 6 到 20 个字符",trigger:"blur"},{pattern:/^[^<>"'|\\]+$/,message:"不能包含非法字符：< > \" ' \\ |",trigger:"blur"}],confirmPassword:[{required:!0,message:"确认密码不能为空",trigger:"blur"},{required:!0,validator:t,trigger:"blur"}]}}},methods:{submit:function(){var e=this;this.$refs["form"].validate((function(t){t&&Object(i["updateUserPwd"])(e.user.oldPassword,e.user.newPassword).then((function(t){e.$modal.msgSuccess("密码修改成功"),e.resetForm()})).catch((function(t){e.$modal.msgError("密码修改失败："+(t.msg||t.message||"未知错误"))}))}))},close:function(){this.resetForm(),this.$tab.closePage()},resetForm:function(){var e=this;this.user={oldPassword:void 0,newPassword:void 0,confirmPassword:void 0},this.passwordStrength={width:"0%",class:"",text:""},this.$nextTick((function(){e.$refs.form&&e.$refs.form.clearValidate()}))},checkPasswordStrength:function(){var e=this.user.newPassword;if(e){var t=0,s=[];e.length>=8?t+=25:s.push("至少8位"),/\d/.test(e)?t+=25:s.push("包含数字"),/[a-z]/.test(e)?t+=25:s.push("包含小写字母"),/[A-Z]/.test(e)||/[!@#$%^&*(),.?":{}|<>]/.test(e)?t+=25:s.push("包含大写字母或特殊字符"),this.passwordStrength=t<=25?{width:"25%",class:"weak",text:"弱"}:t<=50?{width:"50%",class:"fair",text:"一般"}:t<=75?{width:"75%",class:"good",text:"良好"}:{width:"100%",class:"strong",text:"强"}}else this.passwordStrength={width:"0%",class:"",text:""}}}},o=n,l=(s("9532"),s("2877")),c=Object(l["a"])(o,a,r,!1,null,"da14463c",null);t["default"]=c.exports},fd1a:function(e,t,s){"use strict";s("30d7")}}]);