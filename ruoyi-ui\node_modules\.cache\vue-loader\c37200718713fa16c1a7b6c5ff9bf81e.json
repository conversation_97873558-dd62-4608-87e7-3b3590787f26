{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetStatistics.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetStatistics.vue", "mtime": 1756003065942}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750942928175}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750942928768}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCByZXF1ZXN0IGZyb20gJ0AvdXRpbHMvcmVxdWVzdCcKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnQmV0U3RhdGlzdGljcycsCiAgcHJvcHM6IHsKICAgIHZpc2libGU6IHsKICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgZGVmYXVsdDogZmFsc2UKICAgIH0KICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgbWV0aG9kUmFua2luZ0J5R3JvdXA6IFtdLCAvLyDmjInnjqnms5XliIbnu4TnmoTmjpLooYzmppwKICAgICAgbnVtYmVyUmFua2luZ0J5R3JvdXA6IFtdLCAvLyDmjInnjqnms5XliIbnu4TnmoTlj7fnoIHmjpLooYzmppwKICAgICAgZ2FtZU1ldGhvZHNEYXRhOiBbXSwgLy8g546p5rOV5pWw5o2uCiAgICAgIC8vIOeuoeeQhuWRmOWKn+iDveebuOWFswogICAgICBzZWxlY3RlZFVzZXJJZDogbnVsbCwgLy8g6YCJ5Lit55qE55So5oi3SUQKICAgICAgdXNlckxpc3Q6IFtdLCAvLyDnlKjmiLfliJfooagKICAgICAgbG9hZGluZzogZmFsc2UsIC8vIOWKoOi9veeKtuaAgQogICAgICAvLyDlvannp43nm7jlhbMKICAgICAgYWN0aXZlTG90dGVyeVR5cGU6ICdmdWNhaScsIC8vIOW9k+WJjemAieS4reeahOW9qeenje+8mmZ1Y2FpKOemj+W9qTNEKSDmiJYgdGljYWko5L2T5b2pUDMpCiAgICAgIC8vIOWIhuW9qeenjeeahOe7n+iuoeaVsOaNrgogICAgICBmdWNhaU1ldGhvZFJhbmtpbmc6IFtdLCAvLyDnpo/lvanmipXms6jmjpLooYwKICAgICAgZnVjYWlOdW1iZXJSYW5raW5nOiBbXSwgLy8g56aP5b2p54Ot6Zeo5LiJ5L2N5pWw5o6S6KGMCiAgICAgIHRpY2FpTWV0aG9kUmFua2luZzogW10sIC8vIOS9k+W9qeaKleazqOaOkuihjAogICAgICB0aWNhaU51bWJlclJhbmtpbmc6IFtdLCAvLyDkvZPlvanng63pl6jkuInkvY3mlbDmjpLooYwKICAgICAgLy8g5pCc57Si5Yqf6IO955u45YWzCiAgICAgIHNlYXJjaE51bWJlcjogJycsIC8vIOaQnOe0oueahOWPt+eggQogICAgICBpc1NlYXJjaE1vZGU6IGZhbHNlLCAvLyDmmK/lkKblpITkuo7mkJzntKLmqKHlvI8KICAgICAgc2VhcmNoUmVzdWx0Q291bnQ6IDAsIC8vIOaQnOe0oue7k+aenOaVsOmHjwogICAgICAvLyDljp/lp4vlrozmlbTmlbDmja7vvIjnlKjkuo7mkJzntKLov4fmu6TvvIkKICAgICAgb3JpZ2luYWxGdWNhaU1ldGhvZFJhbmtpbmc6IFtdLAogICAgICBvcmlnaW5hbEZ1Y2FpTnVtYmVyUmFua2luZzogW10sCiAgICAgIG9yaWdpbmFsVGljYWlNZXRob2RSYW5raW5nOiBbXSwKICAgICAgb3JpZ2luYWxUaWNhaU51bWJlclJhbmtpbmc6IFtdCiAgICB9CiAgfSwKICBjb21wdXRlZDogewogICAgLyoqIOaYr+WQpuaYr+euoeeQhuWRmCAqLwogICAgaXNBZG1pbigpIHsKICAgICAgY29uc3QgdXNlckluZm8gPSB0aGlzLiRzdG9yZS5nZXR0ZXJzLnVzZXJJbmZvOwogICAgICBjb25zdCByb2xlcyA9IHRoaXMuJHN0b3JlLmdldHRlcnMucm9sZXM7CgogICAgICAvLyDmo4Dmn6XmmK/lkKbmmK/otoXnuqfnrqHnkIblkZjvvIjnlKjmiLdJROS4ujHvvIkKICAgICAgaWYgKHVzZXJJbmZvICYmIHVzZXJJbmZvLnVzZXJJZCA9PT0gMSkgewogICAgICAgIHJldHVybiB0cnVlOwogICAgICB9CgogICAgICAvLyDmo4Dmn6XmmK/lkKbmnInnrqHnkIblkZjop5LoibIKICAgICAgaWYgKHJvbGVzICYmIHJvbGVzLmxlbmd0aCA+IDApIHsKICAgICAgICByZXR1cm4gcm9sZXMuaW5jbHVkZXMoJ2FkbWluJykgfHwgcm9sZXMuaW5jbHVkZXMoJzNkX2FkbWluJyk7CiAgICAgIH0KCiAgICAgIHJldHVybiBmYWxzZTsKICAgIH0sCgogICAgLyoqIOW9k+WJjeW9qeenjeeahOaKleazqOaOkuihjOaVsOaNriAqLwogICAgY3VycmVudE1ldGhvZFJhbmtpbmcoKSB7CiAgICAgIHJldHVybiB0aGlzLmFjdGl2ZUxvdHRlcnlUeXBlID09PSAnZnVjYWknID8gdGhpcy5mdWNhaU1ldGhvZFJhbmtpbmcgOiB0aGlzLnRpY2FpTWV0aG9kUmFua2luZzsKICAgIH0sCgogICAgLyoqIOW9k+WJjeW9qeenjeeahOeDremXqOS4ieS9jeaVsOaOkuihjOaVsOaNriAqLwogICAgY3VycmVudE51bWJlclJhbmtpbmcoKSB7CiAgICAgIHJldHVybiB0aGlzLmFjdGl2ZUxvdHRlcnlUeXBlID09PSAnZnVjYWknID8gdGhpcy5mdWNhaU51bWJlclJhbmtpbmcgOiB0aGlzLnRpY2FpTnVtYmVyUmFua2luZzsKICAgIH0sCgogICAgLyoqIOaQnOe0oue7k+aenOagh+mimCAqLwogICAgc2VhcmNoUmVzdWx0VGl0bGUoKSB7CiAgICAgIHJldHVybiBg5pCc57Si5Y+356CBICIke3RoaXMuc2VhcmNoTnVtYmVyfSIg55qE57uT5p6c77ya5YWx5om+5YiwICR7dGhpcy5zZWFyY2hSZXN1bHRDb3VudH0g5Liq5Yy56YWN6aG5YDsKICAgIH0KICB9LAogIHdhdGNoOiB7CiAgICB2aXNpYmxlKHZhbCkgewogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB2YWw7CiAgICAgIGlmICh2YWwpIHsKICAgICAgICAvLyDov5DooYzmtYvor5UKICAgICAgICB0aGlzLnRlc3RUaHJlZURpZ2l0RXh0cmFjdGlvbigpOwoKICAgICAgICAvLyDlpoLmnpzmmK/nrqHnkIblkZjvvIzlj6rliqDovb3nlKjmiLfliJfooajlkoznjqnms5XmlbDmja7vvIzkuI3oh6rliqjorqHnrpfnu5/orqEKICAgICAgICBpZiAodGhpcy5pc0FkbWluKSB7CiAgICAgICAgICB0aGlzLmxvYWRVc2VyTGlzdCgpOwogICAgICAgICAgdGhpcy5sb2FkR2FtZU1ldGhvZHMoKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgLy8g5pmu6YCa55So5oi36Ieq5Yqo5Yqg6L2957uf6K6hCiAgICAgICAgICB0aGlzLmxvYWRHYW1lTWV0aG9kcygpLnRoZW4oYXN5bmMgKCkgPT4gewogICAgICAgICAgICAvLyDmma7pgJrnlKjmiLfku45zZXNzaW9uU3RvcmFnZeiOt+WPluiHquW3seeahOaVsOaNrgogICAgICAgICAgICBjb25zdCBzeXNVc2VySWQgPSBzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKCdzeXNVc2VySWQnKTsKICAgICAgICAgICAgY29uc29sZS5sb2coJ1tCZXRTdGF0aXN0aWNzXSBzZXNzaW9uU3RvcmFnZeS4reeahHN5c1VzZXJJZDonLCBzeXNVc2VySWQpOwoKICAgICAgICAgICAgaWYgKHN5c1VzZXJJZCkgewogICAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRVc2VySWQgPSBwYXJzZUludChzeXNVc2VySWQpOwogICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdbQmV0U3RhdGlzdGljc10g6K6+572uc2VsZWN0ZWRVc2VySWQ6JywgdGhpcy5zZWxlY3RlZFVzZXJJZCk7CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgY29uc29sZS53YXJuKCdbQmV0U3RhdGlzdGljc10g5peg5rOV5LuOc2Vzc2lvblN0b3JhZ2Xojrflj5ZzeXNVc2VySWQnKTsKICAgICAgICAgICAgfQogICAgICAgICAgICBhd2FpdCB0aGlzLmNhbGN1bGF0ZVN0YXRpc3RpY3MoKTsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgIGRpYWxvZ1Zpc2libGUodmFsKSB7CiAgICAgIHRoaXMuJGVtaXQoJ3VwZGF0ZTp2aXNpYmxlJywgdmFsKTsKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDliqDovb3njqnms5XmlbDmja4gKi8KICAgIGFzeW5jIGxvYWRHYW1lTWV0aG9kcygpIHsKICAgICAgdHJ5IHsKICAgICAgICAvLyDnm7TmjqXku45BUEnojrflj5bnjqnms5XmlbDmja4KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHJlcXVlc3QoewogICAgICAgICAgdXJsOiAnL2dhbWUvb2Rkcy9saXN0JywKICAgICAgICAgIG1ldGhvZDogJ2dldCcsCiAgICAgICAgICBwYXJhbXM6IHsgcGFnZU51bTogMSwgcGFnZVNpemU6IDEwMDAgfQogICAgICAgIH0pOwoKICAgICAgICBpZiAocmVzcG9uc2UgJiYgcmVzcG9uc2Uucm93cykgewogICAgICAgICAgdGhpcy5nYW1lTWV0aG9kc0RhdGEgPSByZXNwb25zZS5yb3dzOwogICAgICAgICAKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy5nYW1lTWV0aG9kc0RhdGEgPSBbXTsKICAgICAgICAgIGNvbnNvbGUud2Fybign546p5rOV5pWw5o2u5Li656m6Jyk7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlueOqeazleaVsOaNruWksei0pTonLCBlcnJvcik7CiAgICAgICAgLy8g5aaC5p6cQVBJ5aSx6LSl77yM5L2/55So5Z+65pys55qE546p5rOV5pig5bCECiAgICAgICAgdGhpcy5nYW1lTWV0aG9kc0RhdGEgPSB0aGlzLmdldEJhc2ljTWV0aG9kc0RhdGEoKTsKICAgICAgfQogICAgfSwKICAgIC8qKiDojrflj5bln7rmnKznjqnms5XmlbDmja7vvIhBUEnlpLHotKXml7bnmoTlpIfnlKjmlrnmoYjvvIkgKi8KICAgIGdldEJhc2ljTWV0aG9kc0RhdGEoKSB7CiAgICAgIHJldHVybiBbCiAgICAgICAgeyBtZXRob2RJZDogMSwgbWV0aG9kTmFtZTogJ+eLrOiDhicgfSwKICAgICAgICB7IG1ldGhvZElkOiAyLCBtZXRob2ROYW1lOiAn5LiA56CB5a6a5L2NJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDMsIG1ldGhvZE5hbWU6ICfkuKTnoIHnu4TlkIgnIH0sCiAgICAgICAgeyBtZXRob2RJZDogNCwgbWV0aG9kTmFtZTogJ+S4pOeggeWvueWtkCcgfSwKICAgICAgICB7IG1ldGhvZElkOiA1LCBtZXRob2ROYW1lOiAn5LiJ56CB55u06YCJJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDYsIG1ldGhvZE5hbWU6ICfkuInnoIHnu4TpgIknIH0sCiAgICAgICAgeyBtZXRob2RJZDogNywgbWV0aG9kTmFtZTogJ+S4ieeggee7hOS4iScgfSwKICAgICAgICB7IG1ldGhvZElkOiA4LCBtZXRob2ROYW1lOiAn5Zub56CB57uE5YWtJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDksIG1ldGhvZE5hbWU6ICflm5vnoIHnu4TkuIknIH0sCiAgICAgICAgeyBtZXRob2RJZDogMTAsIG1ldGhvZE5hbWU6ICfkupTnoIHnu4Tlha0nIH0sCiAgICAgICAgeyBtZXRob2RJZDogMTEsIG1ldGhvZE5hbWU6ICfkupTnoIHnu4TkuIknIH0sCiAgICAgICAgeyBtZXRob2RJZDogMTIsIG1ldGhvZE5hbWU6ICflha3noIHnu4Tlha0nIH0sCiAgICAgICAgeyBtZXRob2RJZDogMTMsIG1ldGhvZE5hbWU6ICflha3noIHnu4TkuIknIH0sCiAgICAgICAgeyBtZXRob2RJZDogMTQsIG1ldGhvZE5hbWU6ICfkuIPnoIHnu4Tlha0nIH0sCiAgICAgICAgeyBtZXRob2RJZDogMTUsIG1ldGhvZE5hbWU6ICfkuIPnoIHnu4TkuIknIH0sCiAgICAgICAgeyBtZXRob2RJZDogMTYsIG1ldGhvZE5hbWU6ICflhavnoIHnu4Tlha0nIH0sCiAgICAgICAgeyBtZXRob2RJZDogMTcsIG1ldGhvZE5hbWU6ICflhavnoIHnu4TkuIknIH0sCiAgICAgICAgeyBtZXRob2RJZDogMTgsIG1ldGhvZE5hbWU6ICfkuZ3noIHnu4Tlha0nIH0sCiAgICAgICAgeyBtZXRob2RJZDogMTksIG1ldGhvZE5hbWU6ICfkuZ3noIHnu4TkuIknIH0sCiAgICAgICAgeyBtZXRob2RJZDogMjAsIG1ldGhvZE5hbWU6ICfljIXmiZPnu4Tlha0nIH0sCiAgICAgICAgeyBtZXRob2RJZDogMjEsIG1ldGhvZE5hbWU6ICc35oiWMjDlkozlgLwnIH0sCiAgICAgICAgeyBtZXRob2RJZDogMjIsIG1ldGhvZE5hbWU6ICc45oiWMTnlkozlgLwnIH0sCiAgICAgICAgeyBtZXRob2RJZDogMjMsIG1ldGhvZE5hbWU6ICc55oiWMTjlkozlgLwnIH0sCiAgICAgICAgeyBtZXRob2RJZDogMjQsIG1ldGhvZE5hbWU6ICcxMOaIljE35ZKM5YC8JyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDI1LCBtZXRob2ROYW1lOiAnMTHmiJYxNuWSjOWAvCcgfSwKICAgICAgICB7IG1ldGhvZElkOiAyNiwgbWV0aG9kTmFtZTogJzEy5oiWMTXlkozlgLwnIH0sCiAgICAgICAgeyBtZXRob2RJZDogMjcsIG1ldGhvZE5hbWU6ICcxM+aIljE05ZKM5YC8JyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDI4LCBtZXRob2ROYW1lOiAn55u06YCJ5aSN5byPJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDI5LCBtZXRob2ROYW1lOiAn5ZKM5YC85Y2V5Y+MJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDMwLCBtZXRob2ROYW1lOiAn5ZKM5YC85aSn5bCPJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDMxLCBtZXRob2ROYW1lOiAn5YyF5omT57uE5LiJJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDEwMCwgbWV0aG9kTmFtZTogJ+S4ieeggemYsuWvuScgfQogICAgICBdOwogICAgfSwKICAgIC8qKiDojrflj5bnjqnms5XlkI3np7AgKi8KICAgIGdldE1ldGhvZE5hbWUobWV0aG9kSWQpIHsKICAgICAgY29uc3QgbWV0aG9kID0gdGhpcy5nYW1lTWV0aG9kc0RhdGEuZmluZChtID0+IG0ubWV0aG9kSWQgPT09IG1ldGhvZElkKTsKICAgICAgcmV0dXJuIG1ldGhvZCA/IG1ldGhvZC5tZXRob2ROYW1lIDogYOeOqeazlSR7bWV0aG9kSWR9YDsKICAgIH0sCiAgICAvKiog6I635Y+W5o6S5ZCN5qC35byP57G7ICovCiAgICBnZXRSYW5rQ2xhc3MocmFuaykgewogICAgICBpZiAocmFuayA9PT0gMSkgcmV0dXJuICdyYW5rLWZpcnN0JzsKICAgICAgaWYgKHJhbmsgPT09IDIpIHJldHVybiAncmFuay1zZWNvbmQnOwogICAgICBpZiAocmFuayA9PT0gMykgcmV0dXJuICdyYW5rLXRoaXJkJzsKICAgICAgcmV0dXJuICdyYW5rLW90aGVyJzsKICAgIH0sCiAgICAvKiog6K6h566X57uf6K6h5pWw5o2uICovCiAgICBhc3luYyBjYWxjdWxhdGVTdGF0aXN0aWNzKCkgewogICAgICBjb25zdCBkYXRhID0gYXdhaXQgdGhpcy5nZXRSZWNvcmREYXRhKCk7CgoKICAgICAgaWYgKCFkYXRhIHx8IGRhdGEubGVuZ3RoID09PSAwKSB7CiAgICAgICAgaWYgKHRoaXMuaXNBZG1pbiAmJiAhdGhpcy5zZWxlY3RlZFVzZXJJZCkgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7fpgInmi6nopoHmn6XnnIvnu5/orqHnmoTnlKjmiLcnKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfmmoLml6Dnu5/orqHmlbDmja7vvIzor7flhYjliLfmlrByZWNvcmTpobXpnaInKTsKICAgICAgICB9CiAgICAgICAgdGhpcy5jbGVhckFsbFJhbmtpbmdEYXRhKCk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICAvLyDmjInlvannp43liIbliKvorqHnrpfnu5/orqHmlbDmja4KICAgICAgdGhpcy5jYWxjdWxhdGVTdGF0aXN0aWNzQnlMb3R0ZXJ5VHlwZShkYXRhKTsKCiAgICAgIC8vIOiwg+ivleS/oeaBrwogICAgICBjb25zb2xlLmxvZygn57uf6K6h6K6h566X5a6M5oiQOicsIHsKICAgICAgICDnpo/lvanmipXms6jmjpLooYw6IHRoaXMuZnVjYWlNZXRob2RSYW5raW5nLmxlbmd0aCwKICAgICAgICDnpo/lvanng63pl6jkuInkvY3mlbA6IHRoaXMuZnVjYWlOdW1iZXJSYW5raW5nLmxlbmd0aCwKICAgICAgICDkvZPlvanmipXms6jmjpLooYw6IHRoaXMudGljYWlNZXRob2RSYW5raW5nLmxlbmd0aCwKICAgICAgICDkvZPlvanng63pl6jkuInkvY3mlbA6IHRoaXMudGljYWlOdW1iZXJSYW5raW5nLmxlbmd0aCwKICAgICAgICDlvZPliY3pgInkuK3lvannp406IHRoaXMuYWN0aXZlTG90dGVyeVR5cGUKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOiOt+WPlnJlY29yZOaVsOaNriAqLwogICAgYXN5bmMgZ2V0UmVjb3JkRGF0YSgpIHsKICAgICAgdHJ5IHsKICAgICAgICAvLyDlpoLmnpzmnInpgInmi6nnmoTnlKjmiLdJRO+8iOeuoeeQhuWRmOmAieaLqeeahOeUqOaIt+aIluaZrumAmueUqOaIt+iHquW3se+8ie+8jOebtOaOpeS7jkFQSeiOt+WPluaVsOaNrgogICAgICAgIGlmICh0aGlzLnNlbGVjdGVkVXNlcklkKSB7CiAgICAgICAgICByZXR1cm4gYXdhaXQgdGhpcy5mZXRjaFVzZXJSZWNvcmREYXRhKHRoaXMuc2VsZWN0ZWRVc2VySWQpOwogICAgICAgIH0KCiAgICAgICAgLy8g5aaC5p6c5piv566h55CG5ZGY5L2G5rKh5pyJ6YCJ5oup55So5oi377yM5o+Q56S66YCJ5oup55So5oi3CiAgICAgICAgaWYgKHRoaXMuaXNBZG1pbikgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7fpgInmi6nopoHmn6XnnIvnu5/orqHnmoTnlKjmiLcnKTsKICAgICAgICAgIHJldHVybiBbXTsKICAgICAgICB9CgogICAgICAgIC8vIOaZrumAmueUqOaIt++8muebtOaOpeS7jkFQSeiOt+WPluW9k+WJjeeUqOaIt+aVsOaNru+8jOS7jnNlc3Npb25TdG9yYWdl6I635Y+W55So5oi3SUQKICAgICAgICBjb25zb2xlLmxvZygnW0JldFN0YXRpc3RpY3NdIOaZrumAmueUqOaIt++8jOebtOaOpeS7jkFQSeiOt+WPluaVsOaNricpOwoKICAgICAgICAvLyDku45zZXNzaW9uU3RvcmFnZeiOt+WPlueUqOaIt0lECiAgICAgICAgY29uc3Qgc3lzVXNlcklkID0gc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgnc3lzVXNlcklkJyk7CiAgICAgICAgY29uc29sZS5sb2coJ1tCZXRTdGF0aXN0aWNzXSDku45zZXNzaW9uU3RvcmFnZeiOt+WPlueahHN5c1VzZXJJZDonLCBzeXNVc2VySWQpOwoKICAgICAgICBpZiAoc3lzVXNlcklkKSB7CiAgICAgICAgICBjb25zb2xlLmxvZygnW0JldFN0YXRpc3RpY3NdIOS9v+eUqHN5c1VzZXJJZOiwg+eUqEFQSTonLCBzeXNVc2VySWQpOwogICAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHRoaXMuZmV0Y2hVc2VyUmVjb3JkRGF0YShwYXJzZUludChzeXNVc2VySWQpKTsKICAgICAgICAgIGNvbnNvbGUubG9nKCdbQmV0U3RhdGlzdGljc10gQVBJ6I635Y+W5Yiw55qE5pWw5o2uOicsIGRhdGEpOwoKICAgICAgICAgIGlmIChkYXRhICYmIGRhdGEubGVuZ3RoID4gMCkgewogICAgICAgICAgICBjb25zb2xlLmxvZyhgW0JldFN0YXRpc3RpY3NdIOaIkOWKn+WKoOi9veS6hiAke2RhdGEubGVuZ3RofSDmnaHnu5/orqHmlbDmja5gKTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIGNvbnNvbGUud2FybignW0JldFN0YXRpc3RpY3NdIEFQSei/lOWbnuepuuaVsOaNricpOwogICAgICAgICAgfQoKICAgICAgICAgIHJldHVybiBkYXRhOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBjb25zb2xlLndhcm4oJ1tCZXRTdGF0aXN0aWNzXSDml6Dms5Xku45zZXNzaW9uU3RvcmFnZeiOt+WPlnN5c1VzZXJJZCcpOwogICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfml6Dms5Xojrflj5bnlKjmiLfkv6Hmga/vvIzor7fph43mlrDnmbvlvZUnKTsKICAgICAgICAgIHJldHVybiBbXTsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign6Kej5p6Q57uf6K6h5pWw5o2u5aSx6LSlOicsIGVycm9yKTsKICAgICAgICByZXR1cm4gW107CiAgICAgIH0KICAgIH0sCiAgICAvKiog5riF56m65omA5pyJ5o6S6KGM5pWw5o2uICovCiAgICBjbGVhckFsbFJhbmtpbmdEYXRhKCkgewogICAgICB0aGlzLmZ1Y2FpTWV0aG9kUmFua2luZyA9IFtdOwogICAgICB0aGlzLmZ1Y2FpTnVtYmVyUmFua2luZyA9IFtdOwogICAgICB0aGlzLnRpY2FpTWV0aG9kUmFua2luZyA9IFtdOwogICAgICB0aGlzLnRpY2FpTnVtYmVyUmFua2luZyA9IFtdOwogICAgICAvLyDmuIXnqbrljp/lp4vmlbDmja4KICAgICAgdGhpcy5vcmlnaW5hbEZ1Y2FpTWV0aG9kUmFua2luZyA9IFtdOwogICAgICB0aGlzLm9yaWdpbmFsRnVjYWlOdW1iZXJSYW5raW5nID0gW107CiAgICAgIHRoaXMub3JpZ2luYWxUaWNhaU1ldGhvZFJhbmtpbmcgPSBbXTsKICAgICAgdGhpcy5vcmlnaW5hbFRpY2FpTnVtYmVyUmFua2luZyA9IFtdOwogICAgICAvLyDph43nva7mkJzntKLnirbmgIEKICAgICAgdGhpcy5jbGVhclNlYXJjaCgpOwogICAgfSwKCiAgICAvKiog5oyJ5b2p56eN5YiG5Yir6K6h566X57uf6K6h5pWw5o2uICovCiAgICBjYWxjdWxhdGVTdGF0aXN0aWNzQnlMb3R0ZXJ5VHlwZShkYXRhKSB7CiAgICAgIC8vIOiwg+ivle+8muafpeeci+aVsOaNrue7k+aehAogICAgICBpZiAoZGF0YS5sZW5ndGggPiAwKSB7CiAgICAgICAgY29uc29sZS5sb2coJ+aVsOaNruagt+acrDonLCBkYXRhWzBdKTsKICAgICAgICBjb25zb2xlLmxvZygn5Y+v6IO955qE5b2p56eN5a2X5q61OicsIHsKICAgICAgICAgIGxvdHRlcnlUeXBlOiBkYXRhWzBdLmxvdHRlcnlUeXBlLAogICAgICAgICAgZ2FtZVR5cGU6IGRhdGFbMF0uZ2FtZVR5cGUsCiAgICAgICAgICBsb3R0ZXJ5SWQ6IGRhdGFbMF0ubG90dGVyeUlkLAogICAgICAgICAgZ2FtZU5hbWU6IGRhdGFbMF0uZ2FtZU5hbWUsCiAgICAgICAgICBtZXRob2ROYW1lOiBkYXRhWzBdLm1ldGhvZE5hbWUKICAgICAgICB9KTsKCiAgICAgICAgLy8g57uf6K6h5omA5pyJ5Y+v6IO955qE5b2p56eN5qCH6K+GCiAgICAgICAgY29uc3QgbG90dGVyeVR5cGVzID0gWy4uLm5ldyBTZXQoZGF0YS5tYXAocmVjb3JkID0+IHJlY29yZC5sb3R0ZXJ5VHlwZSkpXTsKICAgICAgICBjb25zdCBnYW1lVHlwZXMgPSBbLi4ubmV3IFNldChkYXRhLm1hcChyZWNvcmQgPT4gcmVjb3JkLmdhbWVUeXBlKSldOwogICAgICAgIGNvbnN0IGxvdHRlcnlJZHMgPSBbLi4ubmV3IFNldChkYXRhLm1hcChyZWNvcmQgPT4gcmVjb3JkLmxvdHRlcnlJZCkpXTsKICAgICAgICBjb25zdCBnYW1lTmFtZXMgPSBbLi4ubmV3IFNldChkYXRhLm1hcChyZWNvcmQgPT4gcmVjb3JkLmdhbWVOYW1lKSldOwoKICAgICAgICBjb25zb2xlLmxvZygn5pWw5o2u5Lit55qE5b2p56eN5qCH6K+GOicsIHsKICAgICAgICAgIGxvdHRlcnlUeXBlcywKICAgICAgICAgIGdhbWVUeXBlcywKICAgICAgICAgIGxvdHRlcnlJZHMsCiAgICAgICAgICBnYW1lTmFtZXMKICAgICAgICB9KTsKICAgICAgfQoKICAgICAgLy8g5oyJ5b2p56eN5YiG57uE5pWw5o2u77yI5L2/55SobG90dGVyeUlk5a2X5q6177yJCiAgICAgIGNvbnN0IGZ1Y2FpRGF0YSA9IGRhdGEuZmlsdGVyKHJlY29yZCA9PiB0aGlzLmlzRnVjYWlMb3R0ZXJ5KHJlY29yZC5sb3R0ZXJ5SWQpKTsKICAgICAgY29uc3QgdGljYWlEYXRhID0gZGF0YS5maWx0ZXIocmVjb3JkID0+IHRoaXMuaXNUaWNhaUxvdHRlcnkocmVjb3JkLmxvdHRlcnlJZCkpOwoKICAgICAgY29uc29sZS5sb2coYOemj+W9qTNE5pWw5o2uOiAke2Z1Y2FpRGF0YS5sZW5ndGh95p2hLCDkvZPlvalQM+aVsOaNrjogJHt0aWNhaURhdGEubGVuZ3RofeadoWApOwoKICAgICAgLy8g5aaC5p6c5rKh5pyJ5piO56Gu55qE5b2p56eN5Yy65YiG77yM5bCG5omA5pyJ5pWw5o2u5b2S57G75Li656aP5b2pM0TvvIjlhbzlrrnmgKflpITnkIbvvIkKICAgICAgaWYgKGZ1Y2FpRGF0YS5sZW5ndGggPT09IDAgJiYgdGljYWlEYXRhLmxlbmd0aCA9PT0gMCAmJiBkYXRhLmxlbmd0aCA+IDApIHsKICAgICAgICBjb25zb2xlLmxvZygn5pyq5qOA5rWL5Yiw5b2p56eN5a2X5q6177yM5bCG5omA5pyJ5pWw5o2u5b2S57G75Li656aP5b2pM0QnKTsKICAgICAgICB0aGlzLmZ1Y2FpTWV0aG9kUmFua2luZyA9IHRoaXMuY2FsY3VsYXRlTWV0aG9kUmFua2luZ0J5R3JvdXAoZGF0YSk7CiAgICAgICAgdGhpcy5mdWNhaU51bWJlclJhbmtpbmcgPSB0aGlzLmNhbGN1bGF0ZU51bWJlclJhbmtpbmcoZGF0YSk7CiAgICAgICAgdGhpcy50aWNhaU1ldGhvZFJhbmtpbmcgPSBbXTsKICAgICAgICB0aGlzLnRpY2FpTnVtYmVyUmFua2luZyA9IFtdOwogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgLy8g5YiG5Yir6K6h566X56aP5b2p5ZKM5L2T5b2p55qE57uf6K6h5pWw5o2uCiAgICAgIGlmIChmdWNhaURhdGEubGVuZ3RoID4gMCkgewogICAgICAgIHRoaXMub3JpZ2luYWxGdWNhaU1ldGhvZFJhbmtpbmcgPSB0aGlzLmNhbGN1bGF0ZU1ldGhvZFJhbmtpbmdCeUdyb3VwKGZ1Y2FpRGF0YSk7CiAgICAgICAgdGhpcy5vcmlnaW5hbEZ1Y2FpTnVtYmVyUmFua2luZyA9IHRoaXMuY2FsY3VsYXRlTnVtYmVyUmFua2luZyhmdWNhaURhdGEpOwogICAgICAgIHRoaXMuZnVjYWlNZXRob2RSYW5raW5nID0gWy4uLnRoaXMub3JpZ2luYWxGdWNhaU1ldGhvZFJhbmtpbmddOwogICAgICAgIHRoaXMuZnVjYWlOdW1iZXJSYW5raW5nID0gWy4uLnRoaXMub3JpZ2luYWxGdWNhaU51bWJlclJhbmtpbmddOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMub3JpZ2luYWxGdWNhaU1ldGhvZFJhbmtpbmcgPSBbXTsKICAgICAgICB0aGlzLm9yaWdpbmFsRnVjYWlOdW1iZXJSYW5raW5nID0gW107CiAgICAgICAgdGhpcy5mdWNhaU1ldGhvZFJhbmtpbmcgPSBbXTsKICAgICAgICB0aGlzLmZ1Y2FpTnVtYmVyUmFua2luZyA9IFtdOwogICAgICB9CgogICAgICBpZiAodGljYWlEYXRhLmxlbmd0aCA+IDApIHsKICAgICAgICB0aGlzLm9yaWdpbmFsVGljYWlNZXRob2RSYW5raW5nID0gdGhpcy5jYWxjdWxhdGVNZXRob2RSYW5raW5nQnlHcm91cCh0aWNhaURhdGEpOwogICAgICAgIHRoaXMub3JpZ2luYWxUaWNhaU51bWJlclJhbmtpbmcgPSB0aGlzLmNhbGN1bGF0ZU51bWJlclJhbmtpbmcodGljYWlEYXRhKTsKICAgICAgICB0aGlzLnRpY2FpTWV0aG9kUmFua2luZyA9IFsuLi50aGlzLm9yaWdpbmFsVGljYWlNZXRob2RSYW5raW5nXTsKICAgICAgICB0aGlzLnRpY2FpTnVtYmVyUmFua2luZyA9IFsuLi50aGlzLm9yaWdpbmFsVGljYWlOdW1iZXJSYW5raW5nXTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLm9yaWdpbmFsVGljYWlNZXRob2RSYW5raW5nID0gW107CiAgICAgICAgdGhpcy5vcmlnaW5hbFRpY2FpTnVtYmVyUmFua2luZyA9IFtdOwogICAgICAgIHRoaXMudGljYWlNZXRob2RSYW5raW5nID0gW107CiAgICAgICAgdGhpcy50aWNhaU51bWJlclJhbmtpbmcgPSBbXTsKICAgICAgfQogICAgfSwKCiAgICAvKiog5Yik5pat5piv5ZCm5Li656aP5b2pM0QgKi8KICAgIGlzRnVjYWlMb3R0ZXJ5KGxvdHRlcnlJZCkgewogICAgICAvLyDnpo/lvakzROeahGxvdHRlcnlJZOaYrzEKICAgICAgcmV0dXJuIGxvdHRlcnlJZCA9PT0gMSB8fCBsb3R0ZXJ5SWQgPT09ICcxJzsKICAgIH0sCgogICAgLyoqIOWIpOaWreaYr+WQpuS4uuS9k+W9qVAzICovCiAgICBpc1RpY2FpTG90dGVyeShsb3R0ZXJ5SWQpIHsKICAgICAgLy8g5L2T5b2pUDPnmoRsb3R0ZXJ5SWTmmK8yCiAgICAgIHJldHVybiBsb3R0ZXJ5SWQgPT09IDIgfHwgbG90dGVyeUlkID09PSAnMic7CiAgICB9LAoKICAgIC8qKiDmjInnjqnms5XliIbnu4TorqHnrpfmjpLooYzmppwgKi8KICAgIGNhbGN1bGF0ZU1ldGhvZFJhbmtpbmdCeUdyb3VwKGRhdGEpIHsKICAgICAgY29uc29sZS5sb2coJ1tjYWxjdWxhdGVNZXRob2RSYW5raW5nQnlHcm91cF0g5byA5aeL6K6h566X5ZCE546p5rOV5oqV5rOo5o6S6KGM5qacJyk7CgogICAgICAvLyDmjInnjqnms5XliIbnu4TvvIznm7jlkIzlj7fnoIHlkIjlubbntK/orqHph5Hpop0KICAgICAgY29uc3QgbWV0aG9kR3JvdXBzID0ge307CgogICAgICBkYXRhLmZvckVhY2gocmVjb3JkID0+IHsKICAgICAgICBjb25zdCBtZXRob2RJZCA9IHJlY29yZC5tZXRob2RJZDsKICAgICAgICBjb25zdCBhbW91bnQgPSBwYXJzZUZsb2F0KHJlY29yZC5tb25leSkgfHwgMDsKCiAgICAgICAgaWYgKCFtZXRob2RHcm91cHNbbWV0aG9kSWRdKSB7CiAgICAgICAgICBtZXRob2RHcm91cHNbbWV0aG9kSWRdID0gbmV3IE1hcCgpOyAvLyDkvb/nlKhNYXDmnaXlrZjlgqjlj7fnoIHlkozlr7nlupTnmoTntK/orqHmlbDmja4KICAgICAgICB9CgogICAgICAgIC8vIOino+aekOaKleazqOWPt+eggQogICAgICAgIHRyeSB7CiAgICAgICAgICBjb25zdCBiZXROdW1iZXJzID0gSlNPTi5wYXJzZShyZWNvcmQuYmV0TnVtYmVycyB8fCAne30nKTsKICAgICAgICAgIGNvbnN0IG51bWJlcnMgPSBiZXROdW1iZXJzLm51bWJlcnMgfHwgW107CgogICAgICAgICAgLy8g5Li65q+P5Liq5Y+356CB57Sv6K6h6YeR6aKdCiAgICAgICAgICBudW1iZXJzLmZvckVhY2goKG51bWJlck9iaikgPT4gewogICAgICAgICAgICBsZXQgc2luZ2xlTnVtYmVyRGlzcGxheSA9ICcnOwoKICAgICAgICAgICAgaWYgKHR5cGVvZiBudW1iZXJPYmogPT09ICdvYmplY3QnICYmIG51bWJlck9iaiAhPT0gbnVsbCkgewogICAgICAgICAgICAgIC8vIOWkhOeQhiB7YToxLCBiOjIsIGM6MywgZDo0LCBlOjV9IOagvOW8jwogICAgICAgICAgICAgIGNvbnN0IGtleXMgPSBPYmplY3Qua2V5cyhudW1iZXJPYmopLnNvcnQoKTsKICAgICAgICAgICAgICBjb25zdCB2YWx1ZXMgPSBrZXlzLm1hcChrZXkgPT4gU3RyaW5nKG51bWJlck9ialtrZXldKSk7CiAgICAgICAgICAgICAgc2luZ2xlTnVtYmVyRGlzcGxheSA9IHZhbHVlcy5qb2luKCcnKTsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICBzaW5nbGVOdW1iZXJEaXNwbGF5ID0gU3RyaW5nKG51bWJlck9iaik7CiAgICAgICAgICAgIH0KCiAgICAgICAgICAgIC8vIOWmguaenOWPt+eggeW3suWtmOWcqO+8jOe0r+iuoemHkemineWSjOiusOW9leaVsAogICAgICAgICAgICBpZiAobWV0aG9kR3JvdXBzW21ldGhvZElkXS5oYXMoc2luZ2xlTnVtYmVyRGlzcGxheSkpIHsKICAgICAgICAgICAgICBjb25zdCBleGlzdGluZyA9IG1ldGhvZEdyb3Vwc1ttZXRob2RJZF0uZ2V0KHNpbmdsZU51bWJlckRpc3BsYXkpOwogICAgICAgICAgICAgIGV4aXN0aW5nLnRvdGFsQW1vdW50ICs9IGFtb3VudDsKICAgICAgICAgICAgICBleGlzdGluZy5yZWNvcmRDb3VudCArPSAxOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIC8vIOaWsOWPt+egge+8jOWIm+W7uuiusOW9lQogICAgICAgICAgICAgIG1ldGhvZEdyb3Vwc1ttZXRob2RJZF0uc2V0KHNpbmdsZU51bWJlckRpc3BsYXksIHsKICAgICAgICAgICAgICAgIGJldE51bWJlcnM6IHNpbmdsZU51bWJlckRpc3BsYXksCiAgICAgICAgICAgICAgICB0b3RhbEFtb3VudDogYW1vdW50LAogICAgICAgICAgICAgICAgcmVjb3JkQ291bnQ6IDEKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgIGNvbnNvbGUud2Fybign6Kej5p6Q5oqV5rOo5Y+356CB5aSx6LSlOicsIHJlY29yZC5iZXROdW1iZXJzLCBlcnJvcik7CiAgICAgICAgICAvLyDlpoLmnpzop6PmnpDlpLHotKXvvIzkvb/nlKjljp/lp4vmlbDmja4KICAgICAgICAgIGNvbnN0IGZhbGxiYWNrTnVtYmVycyA9IHJlY29yZC5iZXROdW1iZXJzIHx8ICfmnKrnn6Xlj7fnoIEnOwoKICAgICAgICAgIGlmIChtZXRob2RHcm91cHNbbWV0aG9kSWRdLmhhcyhmYWxsYmFja051bWJlcnMpKSB7CiAgICAgICAgICAgIGNvbnN0IGV4aXN0aW5nID0gbWV0aG9kR3JvdXBzW21ldGhvZElkXS5nZXQoZmFsbGJhY2tOdW1iZXJzKTsKICAgICAgICAgICAgZXhpc3RpbmcudG90YWxBbW91bnQgKz0gYW1vdW50OwogICAgICAgICAgICBleGlzdGluZy5yZWNvcmRDb3VudCArPSAxOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgbWV0aG9kR3JvdXBzW21ldGhvZElkXS5zZXQoZmFsbGJhY2tOdW1iZXJzLCB7CiAgICAgICAgICAgICAgYmV0TnVtYmVyczogZmFsbGJhY2tOdW1iZXJzLAogICAgICAgICAgICAgIHRvdGFsQW1vdW50OiBhbW91bnQsCiAgICAgICAgICAgICAgcmVjb3JkQ291bnQ6IDEKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KTsKCiAgICAgIGNvbnNvbGUubG9nKCdbY2FsY3VsYXRlTWV0aG9kUmFua2luZ0J5R3JvdXBdIOW8gOWni+eUn+aIkOaOkuihjOamnCcpOwoKICAgICAgLy8g5Li65q+P5Liq546p5rOV55Sf5oiQ5o6S6KGM5qac5bm26L+U5ZueCiAgICAgIHJldHVybiBPYmplY3QuZW50cmllcyhtZXRob2RHcm91cHMpLm1hcCgoW21ldGhvZElkLCBudW1iZXJzTWFwXSkgPT4gewogICAgICAgIC8vIOWwhk1hcOi9rOaNouS4uuaVsOe7hOW5tuaMieaAu+mHkeminemZjeW6j+aOkuW6jwogICAgICAgIGNvbnN0IHJhbmtpbmcgPSBBcnJheS5mcm9tKG51bWJlcnNNYXAudmFsdWVzKCkpCiAgICAgICAgICAuc29ydCgoYSwgYikgPT4gYi50b3RhbEFtb3VudCAtIGEudG90YWxBbW91bnQpCiAgICAgICAgICAubWFwKChpdGVtLCBpbmRleCkgPT4gKHsKICAgICAgICAgICAgcmFuazogaW5kZXggKyAxLAogICAgICAgICAgICBiZXROdW1iZXJzOiBpdGVtLmJldE51bWJlcnMsCiAgICAgICAgICAgIHRvdGFsQW1vdW50OiBpdGVtLnRvdGFsQW1vdW50LAogICAgICAgICAgICByZWNvcmRDb3VudDogaXRlbS5yZWNvcmRDb3VudAogICAgICAgICAgfSkpOwoKICAgICAgICBjb25zb2xlLmxvZyhgW2NhbGN1bGF0ZU1ldGhvZFJhbmtpbmdCeUdyb3VwXSDnjqnms5Uke21ldGhvZElkfSgke3RoaXMuZ2V0TWV0aG9kTmFtZShwYXJzZUludChtZXRob2RJZCkpfSnvvJoke3JhbmtpbmcubGVuZ3RofeS4quS4jeWQjOWPt+eggWApOwoKICAgICAgICByZXR1cm4gewogICAgICAgICAgbWV0aG9kSWQ6IHBhcnNlSW50KG1ldGhvZElkKSwKICAgICAgICAgIG1ldGhvZE5hbWU6IHRoaXMuZ2V0TWV0aG9kTmFtZShwYXJzZUludChtZXRob2RJZCkpLAogICAgICAgICAgcmFua2luZwogICAgICAgIH07CiAgICAgIH0pLmZpbHRlcihncm91cCA9PiBncm91cC5yYW5raW5nLmxlbmd0aCA+IDApOyAvLyDlj6rmmL7npLrmnInmlbDmja7nmoTnjqnms5UKICAgIH0sCiAgICAvKiog5qC85byP5YyW5oqV5rOo5Y+356CB55So5LqO5pi+56S6ICovCiAgICBmb3JtYXRCZXROdW1iZXJzRm9yRGlzcGxheShiZXROdW1iZXJzS2V5KSB7CiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgbnVtYmVycyA9IEpTT04ucGFyc2UoYmV0TnVtYmVyc0tleSk7CiAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkobnVtYmVycykgJiYgbnVtYmVycy5sZW5ndGggPiAwKSB7CiAgICAgICAgICByZXR1cm4gbnVtYmVycy5tYXAobnVtID0+IHsKICAgICAgICAgICAgaWYgKHR5cGVvZiBudW0gPT09ICdvYmplY3QnICYmIG51bSAhPT0gbnVsbCkgewogICAgICAgICAgICAgIC8vIOWkhOeQhiB7YToxLCBiOjIsIGM6MywgZDo0LCBlOjV9IOagvOW8jwogICAgICAgICAgICAgIGNvbnN0IGtleXMgPSBPYmplY3Qua2V5cyhudW0pLnNvcnQoKTsKICAgICAgICAgICAgICBjb25zdCB2YWx1ZXMgPSBrZXlzLm1hcChrZXkgPT4gU3RyaW5nKG51bVtrZXldKSk7CiAgICAgICAgICAgICAgcmV0dXJuIHZhbHVlcy5qb2luKCcnKTsKICAgICAgICAgICAgfQogICAgICAgICAgICByZXR1cm4gU3RyaW5nKG51bSk7CiAgICAgICAgICB9KS5qb2luKCcsICcpOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAvLyDlpoLmnpzop6PmnpDlpLHotKXvvIznm7TmjqXov5Tlm57ljp/lp4vlrZfnrKbkuLIKICAgICAgfQogICAgICByZXR1cm4gYmV0TnVtYmVyc0tleS5sZW5ndGggPiAyMCA/IGJldE51bWJlcnNLZXkuc3Vic3RyaW5nKDAsIDIwKSArICcuLi4nIDogYmV0TnVtYmVyc0tleTsKICAgIH0sCiAgICAvKiog5oyJ546p5rOV5YiG57uE6K6h566X5Y+356CB5o6S6KGMIC0g5Y+q5L2/55SobW9uZXnlrZfmrrUgKi8KICAgIGNhbGN1bGF0ZU51bWJlclJhbmtpbmcoZGF0YSkgewogICAgICAvLyDliJ3lp4vljJbov5Tlm57mlbDnu4QKICAgICAgY29uc3QgbnVtYmVyUmFua2luZ0J5R3JvdXAgPSBbXTsKCiAgICAgIC8vIOaMieeOqeazleWIhue7hOe7n+iuoQogICAgICBjb25zdCBtZXRob2RHcm91cHMgPSB7fTsKCiAgICAgIGRhdGEuZm9yRWFjaChyZWNvcmQgPT4gewogICAgICAgIHRyeSB7CiAgICAgICAgICBjb25zdCBtZXRob2RJZCA9IHJlY29yZC5tZXRob2RJZDsKICAgICAgICAgIGNvbnN0IG1vbmV5ID0gcGFyc2VGbG9hdChyZWNvcmQubW9uZXkgfHwgMCk7CiAgICAgICAgICBjb25zdCBiZXROdW1iZXJzID0gSlNPTi5wYXJzZShyZWNvcmQuYmV0TnVtYmVycyB8fCAne30nKTsKICAgICAgICAgIGNvbnN0IG51bWJlcnMgPSBiZXROdW1iZXJzLm51bWJlcnMgfHwgW107CgogICAgICAgICAgLy8g5Yid5aeL5YyW546p5rOV5YiG57uECiAgICAgICAgICBpZiAoIW1ldGhvZEdyb3Vwc1ttZXRob2RJZF0pIHsKICAgICAgICAgICAgbWV0aG9kR3JvdXBzW21ldGhvZElkXSA9IHsKICAgICAgICAgICAgICBtZXRob2RUb3RhbDogMCwKICAgICAgICAgICAgICB0aHJlZURpZ2l0TWFwOiBuZXcgTWFwKCkKICAgICAgICAgICAgfTsKICAgICAgICAgIH0KCiAgICAgICAgICBtZXRob2RHcm91cHNbbWV0aG9kSWRdLm1ldGhvZFRvdGFsICs9IG1vbmV5OwoKICAgICAgICAgIC8vIOWkhOeQhuavj+S4quaKleazqOWPt+eggQogICAgICAgICAgbnVtYmVycy5mb3JFYWNoKG51bWJlck9iaiA9PiB7CiAgICAgICAgICAgIGNvbnN0IHRocmVlRGlnaXRzID0gdGhpcy5leHRyYWN0VGhyZWVEaWdpdE51bWJlcnMobnVtYmVyT2JqKTsKCiAgICAgICAgICAgIC8vIOWvueW9k+WJjeWPt+eggeeahOS4ieS9jeaVsOe7hOWQiOWOu+mHje+8iOmBv+WFjeWQjOS4gOWPt+eggeWGhemHjeWkjee7hOWQiOWkmuasoee0r+iuoe+8iQogICAgICAgICAgICBjb25zdCB1bmlxdWVOb3JtYWxpemVkRGlnaXRzID0gbmV3IFNldCgpOwoKICAgICAgICAgICAgdGhyZWVEaWdpdHMuZm9yRWFjaChkaWdpdCA9PiB7CiAgICAgICAgICAgICAgLy8g5b2S5LiA5YyW5LiJ5L2N5pWw77ya5bCG5pWw5a2X5oyJ5LuO5bCP5Yiw5aSn5o6S5bqP5L2c5Li657uf5LiAa2V5CiAgICAgICAgICAgICAgY29uc3Qgbm9ybWFsaXplZERpZ2l0ID0gdGhpcy5ub3JtYWxpemVUaHJlZURpZ2l0cyhkaWdpdCk7CiAgICAgICAgICAgICAgdW5pcXVlTm9ybWFsaXplZERpZ2l0cy5hZGQobm9ybWFsaXplZERpZ2l0KTsKICAgICAgICAgICAgfSk7CgogICAgICAgICAgICAvLyDmr4/kuKrlvZLkuIDljJblkI7nmoTkuInkvY3mlbDntK/liqDph5Hpop0KICAgICAgICAgICAgdW5pcXVlTm9ybWFsaXplZERpZ2l0cy5mb3JFYWNoKG5vcm1hbGl6ZWREaWdpdCA9PiB7CiAgICAgICAgICAgICAgY29uc3QgY3VycmVudEFtb3VudCA9IG1ldGhvZEdyb3Vwc1ttZXRob2RJZF0udGhyZWVEaWdpdE1hcC5nZXQobm9ybWFsaXplZERpZ2l0KSB8fCAwOwogICAgICAgICAgICAgIG1ldGhvZEdyb3Vwc1ttZXRob2RJZF0udGhyZWVEaWdpdE1hcC5zZXQobm9ybWFsaXplZERpZ2l0LCBjdXJyZW50QW1vdW50ICsgbW9uZXkpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0pOwoKICAgICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgICAgY29uc29sZS5lcnJvcign6Kej5p6Q5aSx6LSlOicsIGVycm9yKTsKICAgICAgICB9CiAgICAgIH0pOwoKCgogICAgICAvLyDnlJ/miJDmjpLooYzmppwKICAgCiAgICAgIHRoaXMubnVtYmVyUmFua2luZ0J5R3JvdXAgPSBbXTsKCiAgICAgIE9iamVjdC5lbnRyaWVzKG1ldGhvZEdyb3VwcykuZm9yRWFjaCgoW21ldGhvZElkLCBncm91cF0pID0+IHsKICAgICAgICAvLyDovazmjaJNYXDkuLrmlbDnu4TlubbmjpLluo/vvIzmmL7npLrmiYDmnInmlbDmja4KICAgICAgICBjb25zdCBzb3J0ZWRUaHJlZURpZ2l0cyA9IEFycmF5LmZyb20oZ3JvdXAudGhyZWVEaWdpdE1hcC5lbnRyaWVzKCkpCiAgICAgICAgICAuc29ydCgoWyxhXSwgWyxiXSkgPT4gYiAtIGEpOwoKICAgICAgICBjb25zdCByYW5raW5nID0gc29ydGVkVGhyZWVEaWdpdHMubWFwKChbbnVtYmVyLCBhbW91bnRdLCBpbmRleCkgPT4gKHsKICAgICAgICAgIHJhbms6IGluZGV4ICsgMSwKICAgICAgICAgIG51bWJlciwKICAgICAgICAgIGNvdW50OiAxLAogICAgICAgICAgdG90YWxBbW91bnQ6IGFtb3VudCwKICAgICAgICAgIHBlcmNlbnRhZ2U6IGdyb3VwLm1ldGhvZFRvdGFsID4gMCA/ICgoYW1vdW50IC8gZ3JvdXAubWV0aG9kVG90YWwpICogMTAwKS50b0ZpeGVkKDEpIDogJzAuMCcKICAgICAgICB9KSk7CgogICAgICAgIGlmIChyYW5raW5nLmxlbmd0aCA+IDApIHsKICAgICAgICAgIG51bWJlclJhbmtpbmdCeUdyb3VwLnB1c2goewogICAgICAgICAgICBtZXRob2RJZDogcGFyc2VJbnQobWV0aG9kSWQpLAogICAgICAgICAgICBtZXRob2ROYW1lOiB0aGlzLmdldE1ldGhvZE5hbWUocGFyc2VJbnQobWV0aG9kSWQpKSwKICAgICAgICAgICAgcmFua2luZwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKCiAgICAgIHJldHVybiBudW1iZXJSYW5raW5nQnlHcm91cDsKICAgIH0sCiAgICAvKiog5LuO5oqV5rOo5Y+356CB5Lit5o+Q5Y+W5omA5pyJ5Y+v6IO955qE5LiJ5L2N5pWw57uE5ZCIICovCiAgICBleHRyYWN0VGhyZWVEaWdpdE51bWJlcnMobnVtYmVyT2JqKSB7CiAgICAgIGNvbnN0IG51bWJlcnMgPSBbXTsKCiAgICAgIC8vIOWkhOeQhuS4jeWQjOeahOWPt+eggeagvOW8jwogICAgICBpZiAodHlwZW9mIG51bWJlck9iaiA9PT0gJ3N0cmluZycpIHsKICAgICAgICAvLyDnm7TmjqXmmK/lrZfnrKbkuLLmoLzlvI/nmoTlj7fnoIEKICAgICAgICBjb25zdCBjbGVhblN0ciA9IG51bWJlck9iai5yZXBsYWNlKC9cRC9nLCAnJyk7IC8vIOenu+mZpOmdnuaVsOWtl+Wtl+espgogICAgICAgIHRoaXMuZ2VuZXJhdGVUaHJlZURpZ2l0Q29tYmluYXRpb25zKGNsZWFuU3RyLnNwbGl0KCcnKSwgbnVtYmVycyk7CiAgICAgIH0gZWxzZSBpZiAodHlwZW9mIG51bWJlck9iaiA9PT0gJ29iamVjdCcgJiYgbnVtYmVyT2JqICE9PSBudWxsKSB7CiAgICAgICAgLy8g5a+56LGh5qC85byPIHthOiAxLCBiOiAyLCBjOiAzLCBkOiA0LCBlOiA1fQogICAgICAgIGNvbnN0IGtleXMgPSBPYmplY3Qua2V5cyhudW1iZXJPYmopLnNvcnQoKTsKICAgICAgICBjb25zdCBkaWdpdHMgPSBrZXlzLm1hcChrZXkgPT4gU3RyaW5nKG51bWJlck9ialtrZXldKSk7CgogICAgCgogICAgICAgIC8vIOeUn+aIkOaJgOacieWPr+iDveeahOS4ieS9jeaVsOe7hOWQiAogICAgICAgIHRoaXMuZ2VuZXJhdGVUaHJlZURpZ2l0Q29tYmluYXRpb25zKGRpZ2l0cywgbnVtYmVycyk7CiAgICAgIH0KCiAgICAgIHJldHVybiBudW1iZXJzOwogICAgfSwKICAgIC8qKiDnlJ/miJDmiYDmnInlj6/og73nmoTkuInkvY3mlbDnu4TlkIggKi8KICAgIGdlbmVyYXRlVGhyZWVEaWdpdENvbWJpbmF0aW9ucyhkaWdpdHMsIG51bWJlcnMpIHsKICAgICAgY29uc3QgbiA9IGRpZ2l0cy5sZW5ndGg7CgogICAgICAvLyDlpoLmnpzmlbDlrZflsJHkuo4z5Liq77yM5peg5rOV57uE5oiQ5LiJ5L2N5pWwCiAgICAgIGlmIChuIDwgMykgewogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgLy8g55Sf5oiQ5omA5pyJ5Y+v6IO955qE5LiJ5L2N5pWw57uE5ZCI77yIQyhuLDMp77yJCiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbiAtIDI7IGkrKykgewogICAgICAgIGZvciAobGV0IGogPSBpICsgMTsgaiA8IG4gLSAxOyBqKyspIHsKICAgICAgICAgIGZvciAobGV0IGsgPSBqICsgMTsgayA8IG47IGsrKykgewogICAgICAgICAgICBjb25zdCB0aHJlZURpZ2l0ID0gZGlnaXRzW2ldICsgZGlnaXRzW2pdICsgZGlnaXRzW2tdOwogICAgICAgICAgICBpZiAoL15cZHszfSQvLnRlc3QodGhyZWVEaWdpdCkpIHsKICAgICAgICAgICAgICBudW1iZXJzLnB1c2godGhyZWVEaWdpdCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KICAgIH0sCgogICAgLyoqIOW9kuS4gOWMluS4ieS9jeaVsO+8muWwhuaVsOWtl+aMieS7juWwj+WIsOWkp+aOkuW6j++8jOe7n+S4gOebuOWQjOaVsOWtl+eahOS4jeWQjOaOkuWIlyAqLwogICAgbm9ybWFsaXplVGhyZWVEaWdpdHModGhyZWVEaWdpdCkgewogICAgICAvLyDlsIbkuInkvY3mlbDlrZfnrKbkuLLovazmjaLkuLrmlbDnu4TvvIzmjpLluo/lkI7ph43mlrDnu4TlkIgKICAgICAgcmV0dXJuIHRocmVlRGlnaXQuc3BsaXQoJycpLnNvcnQoKS5qb2luKCcnKTsKICAgIH0sCgogICAgLyoqIOaQnOe0oui+k+WFpeWkhOeQhiAtIOWunuaXtuaQnOe0oiAqLwogICAgaGFuZGxlU2VhcmNoSW5wdXQodmFsdWUpIHsKICAgICAgLy8g5Y+q5YWB6K646L6T5YWl5pWw5a2XCiAgICAgIGNvbnN0IGNsZWFuVmFsdWUgPSB2YWx1ZS5yZXBsYWNlKC9cRC9nLCAnJyk7CiAgICAgIHRoaXMuc2VhcmNoTnVtYmVyID0gY2xlYW5WYWx1ZTsKCiAgICAgIC8vIOWunuaXtuaQnOe0ogogICAgICBpZiAoY2xlYW5WYWx1ZS50cmltKCkpIHsKICAgICAgICAvLyDmnInovpPlhaXlhoXlrrnml7bmiafooYzmkJzntKIKICAgICAgICB0aGlzLnBlcmZvcm1TZWFyY2hJbnRlcm5hbChjbGVhblZhbHVlKTsKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDovpPlhaXkuLrnqbrml7bmuIXpmaTmkJzntKIKICAgICAgICB0aGlzLmNsZWFyU2VhcmNoKCk7CiAgICAgIH0KICAgIH0sCgogICAgLyoqIOWGhemDqOaQnOe0ouaWueazlSAtIOS4jeaYvuekuua2iOaBr+aPkOekuiAqLwogICAgcGVyZm9ybVNlYXJjaEludGVybmFsKHNlYXJjaFZhbHVlKSB7CiAgICAgIGlmICghc2VhcmNoVmFsdWUgfHwgIXNlYXJjaFZhbHVlLnRyaW0oKSkgewogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgdGhpcy5pc1NlYXJjaE1vZGUgPSB0cnVlOwoKICAgICAgdHJ5IHsKICAgICAgICAvLyDmkJzntKLlvZPliY3lvannp43nmoTmlbDmja4KICAgICAgICBjb25zdCBzZWFyY2hSZXN1bHRzID0gdGhpcy5zZWFyY2hJblJhbmtpbmdEYXRhKHNlYXJjaFZhbHVlKTsKCiAgICAgICAgLy8g5pu05paw5pi+56S65pWw5o2u5Li65pCc57Si57uT5p6cCiAgICAgICAgaWYgKHRoaXMuYWN0aXZlTG90dGVyeVR5cGUgPT09ICdmdWNhaScpIHsKICAgICAgICAgIHRoaXMuZnVjYWlNZXRob2RSYW5raW5nID0gc2VhcmNoUmVzdWx0cy5tZXRob2RSYW5raW5nOwogICAgICAgICAgdGhpcy5mdWNhaU51bWJlclJhbmtpbmcgPSBzZWFyY2hSZXN1bHRzLm51bWJlclJhbmtpbmc7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMudGljYWlNZXRob2RSYW5raW5nID0gc2VhcmNoUmVzdWx0cy5tZXRob2RSYW5raW5nOwogICAgICAgICAgdGhpcy50aWNhaU51bWJlclJhbmtpbmcgPSBzZWFyY2hSZXN1bHRzLm51bWJlclJhbmtpbmc7CiAgICAgICAgfQoKICAgICAgICAvLyDorqHnrpfmkJzntKLnu5PmnpzmlbDph48KICAgICAgICB0aGlzLnNlYXJjaFJlc3VsdENvdW50ID0gdGhpcy5jYWxjdWxhdGVTZWFyY2hSZXN1bHRDb3VudChzZWFyY2hSZXN1bHRzKTsKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCfmkJzntKLlpLHotKU6JywgZXJyb3IpOwogICAgICAgIC8vIOWunuaXtuaQnOe0ouS4jeaYvuekuumUmeivr+a2iOaBr++8jOmBv+WFjemikee5geW8ueeqlwogICAgICB9CiAgICB9LAoKICAgIC8qKiDmuIXpmaTmkJzntKIgKi8KICAgIGNsZWFyU2VhcmNoKCkgewogICAgICB0aGlzLnNlYXJjaE51bWJlciA9ICcnOwogICAgICB0aGlzLmlzU2VhcmNoTW9kZSA9IGZhbHNlOwogICAgICB0aGlzLnNlYXJjaFJlc3VsdENvdW50ID0gMDsKCiAgICAgIC8vIOaBouWkjeWOn+Wni+aVsOaNrgogICAgICB0aGlzLmZ1Y2FpTWV0aG9kUmFua2luZyA9IFsuLi50aGlzLm9yaWdpbmFsRnVjYWlNZXRob2RSYW5raW5nXTsKICAgICAgdGhpcy5mdWNhaU51bWJlclJhbmtpbmcgPSBbLi4udGhpcy5vcmlnaW5hbEZ1Y2FpTnVtYmVyUmFua2luZ107CiAgICAgIHRoaXMudGljYWlNZXRob2RSYW5raW5nID0gWy4uLnRoaXMub3JpZ2luYWxUaWNhaU1ldGhvZFJhbmtpbmddOwogICAgICB0aGlzLnRpY2FpTnVtYmVyUmFua2luZyA9IFsuLi50aGlzLm9yaWdpbmFsVGljYWlOdW1iZXJSYW5raW5nXTsKICAgIH0sCgogICAgLyoqIOWcqOaOkuihjOaVsOaNruS4reaQnOe0ouWMheWQq+aMh+WumuWPt+eggeeahOmhueebriAqLwogICAgc2VhcmNoSW5SYW5raW5nRGF0YShzZWFyY2hOdW1iZXIpIHsKICAgICAgY29uc3Qgb3JpZ2luYWxNZXRob2RSYW5raW5nID0gdGhpcy5hY3RpdmVMb3R0ZXJ5VHlwZSA9PT0gJ2Z1Y2FpJwogICAgICAgID8gdGhpcy5vcmlnaW5hbEZ1Y2FpTWV0aG9kUmFua2luZwogICAgICAgIDogdGhpcy5vcmlnaW5hbFRpY2FpTWV0aG9kUmFua2luZzsKCiAgICAgIGNvbnN0IG9yaWdpbmFsTnVtYmVyUmFua2luZyA9IHRoaXMuYWN0aXZlTG90dGVyeVR5cGUgPT09ICdmdWNhaScKICAgICAgICA/IHRoaXMub3JpZ2luYWxGdWNhaU51bWJlclJhbmtpbmcKICAgICAgICA6IHRoaXMub3JpZ2luYWxUaWNhaU51bWJlclJhbmtpbmc7CgogICAgICAvLyDmkJzntKLmipXms6jmjpLooYzmppwKICAgICAgY29uc3QgZmlsdGVyZWRNZXRob2RSYW5raW5nID0gb3JpZ2luYWxNZXRob2RSYW5raW5nLm1hcChtZXRob2RHcm91cCA9PiB7CiAgICAgICAgY29uc3QgZmlsdGVyZWRSYW5raW5nID0gbWV0aG9kR3JvdXAucmFua2luZy5maWx0ZXIoaXRlbSA9PiB7CiAgICAgICAgICByZXR1cm4gdGhpcy5pc051bWJlck1hdGNoKGl0ZW0uYmV0TnVtYmVycywgc2VhcmNoTnVtYmVyKTsKICAgICAgICB9KTsKCiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgIC4uLm1ldGhvZEdyb3VwLAogICAgICAgICAgcmFua2luZzogZmlsdGVyZWRSYW5raW5nCiAgICAgICAgfTsKICAgICAgfSkuZmlsdGVyKG1ldGhvZEdyb3VwID0+IG1ldGhvZEdyb3VwLnJhbmtpbmcubGVuZ3RoID4gMCk7CgogICAgICAvLyDmkJzntKLng63pl6jkuInkvY3mlbDmjpLooYzmppzvvIjkvb/nlKjkuJPpl6jnmoTkuInkvY3mlbDljLnphY3pgLvovpHvvIkKICAgICAgY29uc3QgZmlsdGVyZWROdW1iZXJSYW5raW5nID0gb3JpZ2luYWxOdW1iZXJSYW5raW5nLm1hcChtZXRob2RHcm91cCA9PiB7CiAgICAgICAgY29uc3QgZmlsdGVyZWRSYW5raW5nID0gbWV0aG9kR3JvdXAucmFua2luZy5maWx0ZXIoaXRlbSA9PiB7CiAgICAgICAgICByZXR1cm4gdGhpcy5pc1RocmVlRGlnaXRNYXRjaChpdGVtLm51bWJlciwgc2VhcmNoTnVtYmVyKTsKICAgICAgICB9KTsKCiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgIC4uLm1ldGhvZEdyb3VwLAogICAgICAgICAgcmFua2luZzogZmlsdGVyZWRSYW5raW5nCiAgICAgICAgfTsKICAgICAgfSkuZmlsdGVyKG1ldGhvZEdyb3VwID0+IG1ldGhvZEdyb3VwLnJhbmtpbmcubGVuZ3RoID4gMCk7CgogICAgICByZXR1cm4gewogICAgICAgIG1ldGhvZFJhbmtpbmc6IGZpbHRlcmVkTWV0aG9kUmFua2luZywKICAgICAgICBudW1iZXJSYW5raW5nOiBmaWx0ZXJlZE51bWJlclJhbmtpbmcKICAgICAgfTsKICAgIH0sCgogICAgLyoqIOWIpOaWreWPt+eggeaYr+WQpuWMuemFjeaQnOe0ouadoeS7tiAqLwogICAgaXNOdW1iZXJNYXRjaCh0YXJnZXROdW1iZXIsIHNlYXJjaE51bWJlcikgewogICAgICBpZiAoIXRhcmdldE51bWJlciB8fCAhc2VhcmNoTnVtYmVyKSByZXR1cm4gZmFsc2U7CgogICAgICBjb25zdCB0YXJnZXQgPSBTdHJpbmcodGFyZ2V0TnVtYmVyKTsKICAgICAgY29uc3Qgc2VhcmNoID0gU3RyaW5nKHNlYXJjaE51bWJlcik7CiAgICAgIGNvbnN0IHNlYXJjaERpZ2l0cyA9IHNlYXJjaC5zcGxpdCgnJyk7CiAgICAgIGNvbnN0IHRhcmdldERpZ2l0cyA9IHRhcmdldC5zcGxpdCgnJyk7CgogICAgICAvLyDmoLjlv4PpgLvovpHvvJrmo4Dmn6XmkJzntKLlj7fnoIHkuK3nmoTmr4/kuKrmlbDlrZfmmK/lkKbpg73lnKjnm67moIflj7fnoIHkuK3lh7rnjrAKICAgICAgLy8g6L+Z5qC35Y+v5Lul5Yy56YWN5ZCE56eN6ZW/5bqm55qE55uu5qCH5Y+356CBCgogICAgICAvLyDmlrnmoYgx77ya5Lu75oSP5Yy56YWNIC0g5pCc57Si5Y+356CB5Lit55qE5Lu75LiA5pWw5a2X5Zyo55uu5qCH5Y+356CB5Lit5Ye6546w5Y2z5Yy56YWNCiAgICAgIC8vIOmAgueUqOS6jueLrOiDhuetieWNleaVsOWtl+eOqeazlQogICAgICBjb25zdCBoYXNBbnlEaWdpdCA9IHNlYXJjaERpZ2l0cy5zb21lKGRpZ2l0ID0+IHRhcmdldERpZ2l0cy5pbmNsdWRlcyhkaWdpdCkpOwoKICAgICAgLy8g5pa55qGIMu+8muWujOWFqOWMuemFjSAtIOaQnOe0ouWPt+eggeS4reeahOaJgOacieaVsOWtl+mDveWcqOebruagh+WPt+eggeS4reWHuueOsAogICAgICAvLyDpgILnlKjkuo7lpJrmlbDlrZfnu4TlkIjnjqnms5UKICAgICAgY29uc3QgaGFzQWxsRGlnaXRzID0gc2VhcmNoRGlnaXRzLmV2ZXJ5KGRpZ2l0ID0+IHRhcmdldERpZ2l0cy5pbmNsdWRlcyhkaWdpdCkpOwoKICAgICAgLy8g5pa55qGIM++8mueyvuehruWMuemFjSAtIOebruagh+WPt+eggeWMheWQq+aQnOe0ouWPt+eggeeahOi/nue7reWtkOS4sgogICAgICBjb25zdCBoYXNFeGFjdFNlcXVlbmNlID0gdGFyZ2V0LmluY2x1ZGVzKHNlYXJjaCk7CgogICAgICAvLyDmoLnmja7kuI3lkIzmg4XlhrXph4fnlKjkuI3lkIznmoTljLnphY3nrZbnlaUKICAgICAgaWYgKHNlYXJjaC5sZW5ndGggPT09IDEpIHsKICAgICAgICAvLyDljZXmlbDlrZfmkJzntKLvvJrlj6ropoHnm67moIflj7fnoIHljIXlkKvor6XmlbDlrZfljbPljLnphY0KICAgICAgICByZXR1cm4gaGFzQW55RGlnaXQ7CiAgICAgIH0gZWxzZSBpZiAoc2VhcmNoLmxlbmd0aCA9PT0gMikgewogICAgICAgIC8vIOWPjOaVsOWtl+aQnOe0ou+8muagueaNruebruagh+mVv+W6pumHh+eUqOS4jeWQjOetlueVpQogICAgICAgIGlmICh0YXJnZXQubGVuZ3RoID09PSAxKSB7CiAgICAgICAgICAvLyDni6zog4bvvJrku7vmhI/ljLnphY3vvIjmkJzntKIxMuaXtuaYvuekujHlkowy77yJCiAgICAgICAgICByZXR1cm4gaGFzQW55RGlnaXQ7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIC8vIOS4pOeggeWPiuS7peS4iu+8muimgeaxguWMheWQq+aJgOacieaQnOe0ouaVsOWtlwogICAgICAgICAgcmV0dXJuIGhhc0V4YWN0U2VxdWVuY2UgfHwgaGFzQWxsRGlnaXRzOwogICAgICAgIH0KICAgICAgfSBlbHNlIGlmIChzZWFyY2gubGVuZ3RoID49IDMpIHsKICAgICAgICAvLyDkuInkvY3mlbDlj4rku6XkuIrmkJzntKLvvJrph4fnlKjlpJrnp43ljLnphY3nrZbnlaUKCiAgICAgICAgLy8g562W55WlMe+8mueyvuehruW6j+WIl+WMuemFje+8iOWmguaQnOe0ojEyNe+8jOebruaghzEyNTM05Yy56YWN77yJCiAgICAgICAgaWYgKGhhc0V4YWN0U2VxdWVuY2UpIHsKICAgICAgICAgIHJldHVybiB0cnVlOwogICAgICAgIH0KCiAgICAgICAgLy8g562W55WlMu+8muWvueS6juWNleaVsOWtl+ebruagh+WPt+egge+8jOmHh+eUqOS7u+aEj+WMuemFjQogICAgICAgIGlmICh0YXJnZXQubGVuZ3RoID09PSAxKSB7CiAgICAgICAgICByZXR1cm4gaGFzQW55RGlnaXQ7CiAgICAgICAgfQoKICAgICAgICAvLyDnrZbnlaUz77ya5a+55LqO5Lik5L2N5pWw55uu5qCH5Y+356CB77yM5qOA5p+l5piv5ZCm5Li65pCc57Si5Y+356CB55qE5a2Q57uE5ZCICiAgICAgICAgaWYgKHRhcmdldC5sZW5ndGggPT09IDIpIHsKICAgICAgICAgIC8vIOajgOafpeS4pOS9jeaVsOebruagh+aYr+WQpuaYr+aQnOe0ouWPt+eggeS4reS7u+aEj+S4pOS4quaVsOWtl+eahOe7hOWQiAogICAgICAgICAgcmV0dXJuIHRoaXMuaXNUd29EaWdpdFN1YnNldCh0YXJnZXQsIHNlYXJjaCk7CiAgICAgICAgfQoKICAgICAgICAvLyDnrZbnlaU077ya5a+55LqO5LiJ5L2N5pWw55uu5qCH55qE54m55q6K5aSE55CGCiAgICAgICAgaWYgKHRhcmdldC5sZW5ndGggPT09IDMpIHsKICAgICAgICAgIGlmIChzZWFyY2gubGVuZ3RoID09PSAyKSB7CiAgICAgICAgICAgIC8vIOaQnOe0ouS4pOS9jeaVsO+8muS4ieS9jeaVsOW/hemhu+WQjOaXtuWMheWQq+i/meS4pOS4quaVsOWtlwogICAgICAgICAgICByZXR1cm4gdGhpcy5pc1RocmVlRGlnaXRDb250YWluc1R3b0RpZ2l0cyh0YXJnZXQsIHNlYXJjaCk7CiAgICAgICAgICB9IGVsc2UgaWYgKHNlYXJjaC5sZW5ndGggPT09IDMpIHsKICAgICAgICAgICAgLy8g5pCc57Si5LiJ5L2N5pWw77ya5qOA5p+l5piv5ZCm5Li655u45ZCM5pWw5a2X57uE5ZCI77yI5b2S5LiA5YyW5q+U6L6D77yJCiAgICAgICAgICAgIGNvbnN0IG5vcm1hbGl6ZWRTZWFyY2ggPSB0aGlzLm5vcm1hbGl6ZVRocmVlRGlnaXRzKHNlYXJjaCk7CiAgICAgICAgICAgIGNvbnN0IG5vcm1hbGl6ZWRUYXJnZXQgPSB0aGlzLm5vcm1hbGl6ZVRocmVlRGlnaXRzKHRhcmdldCk7CiAgICAgICAgICAgIHJldHVybiBub3JtYWxpemVkU2VhcmNoID09PSBub3JtYWxpemVkVGFyZ2V0OwogICAgICAgICAgfQogICAgICAgIH0KCiAgICAgICAgLy8g562W55WlNe+8muWvueS6juWbm+eggeWPiuS7peS4iueahOebruagh+WPt+egge+8jOagueaNruaQnOe0oumVv+W6pumHh+eUqOS4jeWQjOetlueVpQogICAgICAgIGlmICh0YXJnZXQubGVuZ3RoID49IDQpIHsKICAgICAgICAgIGlmIChzZWFyY2gubGVuZ3RoID09PSAxKSB7CiAgICAgICAgICAgIC8vIOaQnOe0ouWNleaVsOWtl++8muebruagh+WPt+eggeW/hemhu+WMheWQq+ivpeaVsOWtlwogICAgICAgICAgICByZXR1cm4gaGFzQW55RGlnaXQ7CiAgICAgICAgICB9IGVsc2UgaWYgKHNlYXJjaC5sZW5ndGggPT09IDIpIHsKICAgICAgICAgICAgLy8g5pCc57Si5Lik5L2N5pWw77ya55uu5qCH5Y+356CB5b+F6aG75ZCM5pe25YyF5ZCr6L+Z5Lik5Liq5pWw5a2XCiAgICAgICAgICAgIGNvbnN0IFtkaWdpdDEsIGRpZ2l0Ml0gPSBzZWFyY2guc3BsaXQoJycpOwogICAgICAgICAgICBjb25zdCB0YXJnZXREaWdpdHMgPSB0YXJnZXQuc3BsaXQoJycpOwogICAgICAgICAgICByZXR1cm4gdGFyZ2V0RGlnaXRzLmluY2x1ZGVzKGRpZ2l0MSkgJiYgdGFyZ2V0RGlnaXRzLmluY2x1ZGVzKGRpZ2l0Mik7CiAgICAgICAgICB9IGVsc2UgaWYgKHNlYXJjaC5sZW5ndGggPj0gMykgewogICAgICAgICAgICAvLyDmkJzntKLkuInkvY3mlbDlj4rku6XkuIrvvJrnm67moIflj7fnoIHlv4XpobvljIXlkKvmiYDmnInmkJzntKLmlbDlrZcKICAgICAgICAgICAgcmV0dXJuIGhhc0FsbERpZ2l0czsKICAgICAgICAgIH0KICAgICAgICB9CgogICAgICAgIC8vIOetlueVpTbvvJrlhbbku5bmg4XlhrXph4fnlKjlrozlhajljLnphY0KICAgICAgICByZXR1cm4gaGFzQWxsRGlnaXRzOwogICAgICB9CgogICAgICByZXR1cm4gZmFsc2U7CiAgICB9LAoKICAgIC8qKiDmo4Dmn6XkuKTkvY3mlbDmmK/lkKbkuLrmkJzntKLlj7fnoIHnmoTlrZDnu4TlkIggKi8KICAgIGlzVHdvRGlnaXRTdWJzZXQodHdvRGlnaXRUYXJnZXQsIHNlYXJjaE51bWJlcikgewogICAgICBjb25zdCB0YXJnZXQgPSBTdHJpbmcodHdvRGlnaXRUYXJnZXQpOwogICAgICBjb25zdCBzZWFyY2ggPSBTdHJpbmcoc2VhcmNoTnVtYmVyKTsKCiAgICAgIGlmICh0YXJnZXQubGVuZ3RoICE9PSAyIHx8IHNlYXJjaC5sZW5ndGggPCAyKSB7CiAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICB9CgogICAgICBjb25zdCBbdGFyZ2V0RGlnaXQxLCB0YXJnZXREaWdpdDJdID0gdGFyZ2V0LnNwbGl0KCcnKTsKICAgICAgY29uc3Qgc2VhcmNoRGlnaXRzID0gc2VhcmNoLnNwbGl0KCcnKTsKCiAgICAgIC8vIOajgOafpeS4pOS9jeaVsOeahOS4pOS4quaVsOWtl+aYr+WQpumDveWcqOaQnOe0ouWPt+eggeS4rQogICAgICBjb25zdCBoYXNEaWdpdDEgPSBzZWFyY2hEaWdpdHMuaW5jbHVkZXModGFyZ2V0RGlnaXQxKTsKICAgICAgY29uc3QgaGFzRGlnaXQyID0gc2VhcmNoRGlnaXRzLmluY2x1ZGVzKHRhcmdldERpZ2l0Mik7CgogICAgICByZXR1cm4gaGFzRGlnaXQxICYmIGhhc0RpZ2l0MjsKICAgIH0sCgogICAgLyoqIOajgOafpeS4ieS9jeaVsOaYr+WQpuWMheWQq+aMh+WumueahOS4pOS4quaVsOWtlyAqLwogICAgaXNUaHJlZURpZ2l0Q29udGFpbnNUd29EaWdpdHModGhyZWVEaWdpdFRhcmdldCwgdHdvRGlnaXRTZWFyY2gpIHsKICAgICAgY29uc3QgdGFyZ2V0ID0gU3RyaW5nKHRocmVlRGlnaXRUYXJnZXQpOwogICAgICBjb25zdCBzZWFyY2ggPSBTdHJpbmcodHdvRGlnaXRTZWFyY2gpOwoKICAgICAgaWYgKHRhcmdldC5sZW5ndGggIT09IDMgfHwgc2VhcmNoLmxlbmd0aCAhPT0gMikgewogICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgfQoKICAgICAgY29uc3QgW3NlYXJjaERpZ2l0MSwgc2VhcmNoRGlnaXQyXSA9IHNlYXJjaC5zcGxpdCgnJyk7CiAgICAgIGNvbnN0IHRhcmdldERpZ2l0cyA9IHRhcmdldC5zcGxpdCgnJyk7CgogICAgICAvLyDmo4Dmn6XkuInkvY3mlbDmmK/lkKblkIzml7bljIXlkKvmkJzntKLnmoTkuKTkuKrmlbDlrZcKICAgICAgY29uc3QgaGFzRGlnaXQxID0gdGFyZ2V0RGlnaXRzLmluY2x1ZGVzKHNlYXJjaERpZ2l0MSk7CiAgICAgIGNvbnN0IGhhc0RpZ2l0MiA9IHRhcmdldERpZ2l0cy5pbmNsdWRlcyhzZWFyY2hEaWdpdDIpOwoKICAgICAgcmV0dXJuIGhhc0RpZ2l0MSAmJiBoYXNEaWdpdDI7CiAgICB9LAoKICAgIC8qKiDkuJPpl6jnlKjkuo7kuInkvY3mlbDmjpLooYzmppznmoTljLnphY3pgLvovpEgKi8KICAgIGlzVGhyZWVEaWdpdE1hdGNoKHRhcmdldFRocmVlRGlnaXQsIHNlYXJjaE51bWJlcikgewogICAgICBpZiAoIXRhcmdldFRocmVlRGlnaXQgfHwgIXNlYXJjaE51bWJlcikgcmV0dXJuIGZhbHNlOwoKICAgICAgY29uc3QgdGFyZ2V0ID0gU3RyaW5nKHRhcmdldFRocmVlRGlnaXQpOwogICAgICBjb25zdCBzZWFyY2ggPSBTdHJpbmcoc2VhcmNoTnVtYmVyKTsKCiAgICAgIC8vIOS4ieS9jeaVsOaOkuihjOamnOeahOebruagh+mDveaYrzPkvY3mlbDvvIzpnIDopoHnibnmrorlpITnkIYKICAgICAgaWYgKHRhcmdldC5sZW5ndGggIT09IDMpIHJldHVybiBmYWxzZTsKCiAgICAgIGlmIChzZWFyY2gubGVuZ3RoID09PSAxKSB7CiAgICAgICAgLy8g5pCc57Si5Y2V5pWw5a2X77ya5LiJ5L2N5pWw5Lit5YyF5ZCr6K+l5pWw5a2X5Y2z5Yy56YWNCiAgICAgICAgcmV0dXJuIHRhcmdldC5pbmNsdWRlcyhzZWFyY2gpOwogICAgICB9IGVsc2UgaWYgKHNlYXJjaC5sZW5ndGggPT09IDIpIHsKICAgICAgICAvLyDmkJzntKLlj4zmlbDlrZfvvJrkuInkvY3mlbDkuK3lv4XpobvlkIzml7bljIXlkKvov5nkuKTkuKrmlbDlrZcKICAgICAgICBjb25zdCBbZGlnaXQxLCBkaWdpdDJdID0gc2VhcmNoLnNwbGl0KCcnKTsKICAgICAgICBjb25zdCB0YXJnZXREaWdpdHMgPSB0YXJnZXQuc3BsaXQoJycpOwogICAgICAgIHJldHVybiB0YXJnZXREaWdpdHMuaW5jbHVkZXMoZGlnaXQxKSAmJiB0YXJnZXREaWdpdHMuaW5jbHVkZXMoZGlnaXQyKTsKICAgICAgfSBlbHNlIGlmIChzZWFyY2gubGVuZ3RoID09PSAzKSB7CiAgICAgICAgLy8g5pCc57Si5LiJ5L2N5pWw77ya5b+F6aG75piv55u45ZCM55qE5pWw5a2X57uE5ZCI77yI5b2S5LiA5YyW5ZCO5q+U6L6D77yJCiAgICAgICAgY29uc3Qgbm9ybWFsaXplZFNlYXJjaCA9IHRoaXMubm9ybWFsaXplVGhyZWVEaWdpdHMoc2VhcmNoKTsKICAgICAgICBjb25zdCBub3JtYWxpemVkVGFyZ2V0ID0gdGhpcy5ub3JtYWxpemVUaHJlZURpZ2l0cyh0YXJnZXQpOwogICAgICAgIHJldHVybiBub3JtYWxpemVkU2VhcmNoID09PSBub3JtYWxpemVkVGFyZ2V0OwogICAgICB9IGVsc2UgaWYgKHNlYXJjaC5sZW5ndGggPiAzKSB7CiAgICAgICAgLy8g5pCc57Si6LaF6L+H5LiJ5L2N5pWw77ya5qOA5p+l5LiJ5L2N5pWw5piv5ZCm5YyF5ZCr5pCc57Si5Y+356CB5Lit55qE5Lu75oSP5LiJ5Liq5pWw5a2X57uE5ZCICiAgICAgICAgY29uc3Qgc2VhcmNoRGlnaXRzID0gc2VhcmNoLnNwbGl0KCcnKTsKCiAgICAgICAgLy8g55Sf5oiQ5pCc57Si5Y+356CB55qE5omA5pyJ5LiJ5L2N5pWw57uE5ZCI77yM55yL5piv5ZCm5pyJ5Yy56YWN55qECiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBzZWFyY2hEaWdpdHMubGVuZ3RoIC0gMjsgaSsrKSB7CiAgICAgICAgICBmb3IgKGxldCBqID0gaSArIDE7IGogPCBzZWFyY2hEaWdpdHMubGVuZ3RoIC0gMTsgaisrKSB7CiAgICAgICAgICAgIGZvciAobGV0IGsgPSBqICsgMTsgayA8IHNlYXJjaERpZ2l0cy5sZW5ndGg7IGsrKykgewogICAgICAgICAgICAgIGNvbnN0IHNlYXJjaENvbWJvID0gc2VhcmNoRGlnaXRzW2ldICsgc2VhcmNoRGlnaXRzW2pdICsgc2VhcmNoRGlnaXRzW2tdOwogICAgICAgICAgICAgIGNvbnN0IG5vcm1hbGl6ZWRTZWFyY2hDb21ibyA9IHRoaXMubm9ybWFsaXplVGhyZWVEaWdpdHMoc2VhcmNoQ29tYm8pOwogICAgICAgICAgICAgIGNvbnN0IG5vcm1hbGl6ZWRUYXJnZXQgPSB0aGlzLm5vcm1hbGl6ZVRocmVlRGlnaXRzKHRhcmdldCk7CiAgICAgICAgICAgICAgaWYgKG5vcm1hbGl6ZWRTZWFyY2hDb21ibyA9PT0gbm9ybWFsaXplZFRhcmdldCkgewogICAgICAgICAgICAgICAgcmV0dXJuIHRydWU7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgfQoKICAgICAgcmV0dXJuIGZhbHNlOwogICAgfSwKCiAgICAvKiog6K6h566X5pCc57Si57uT5p6c5pWw6YePICovCiAgICBjYWxjdWxhdGVTZWFyY2hSZXN1bHRDb3VudChzZWFyY2hSZXN1bHRzKSB7CiAgICAgIGxldCBjb3VudCA9IDA7CgogICAgICAvLyDnu5/orqHmipXms6jmjpLooYzmppzljLnphY3pobkKICAgICAgc2VhcmNoUmVzdWx0cy5tZXRob2RSYW5raW5nLmZvckVhY2gobWV0aG9kR3JvdXAgPT4gewogICAgICAgIGNvdW50ICs9IG1ldGhvZEdyb3VwLnJhbmtpbmcubGVuZ3RoOwogICAgICB9KTsKCiAgICAgIC8vIOe7n+iuoeeDremXqOS4ieS9jeaVsOaOkuihjOamnOWMuemFjemhuQogICAgICBzZWFyY2hSZXN1bHRzLm51bWJlclJhbmtpbmcuZm9yRWFjaChtZXRob2RHcm91cCA9PiB7CiAgICAgICAgY291bnQgKz0gbWV0aG9kR3JvdXAucmFua2luZy5sZW5ndGg7CiAgICAgIH0pOwoKICAgICAgcmV0dXJuIGNvdW50OwogICAgfSwKICAgIC8qKiDliLfmlrDmlbDmja4gKi8KICAgIGFzeW5jIHJlZnJlc2hEYXRhKCkgewogICAgICBhd2FpdCB0aGlzLmNhbGN1bGF0ZVN0YXRpc3RpY3MoKTsKICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmlbDmja7lt7LliLfmlrAnKTsKICAgIH0sCiAgICAvKiog5rWL6K+V5LiJ5L2N5pWw57uE5ZCI5o+Q5Y+W6YC76L6RICovCiAgICB0ZXN0VGhyZWVEaWdpdEV4dHJhY3Rpb24oKSB7CiAgICAgIGNvbnNvbGUubG9nKCc9PT0g5rWL6K+V5LiJ5L2N5pWw57uE5ZCI5o+Q5Y+W5ZKM5b2S5LiA5YyW6YC76L6RID09PScpOwoKICAgICAgLy8g5rWL6K+V5LiN5ZCM6ZW/5bqm55qE5Y+356CBCiAgICAgIGNvbnN0IHRlc3RDYXNlcyA9IFsKICAgICAgICB7YTogMSwgYjogMiwgYzogM30sICAgICAgICAgICAgICAgICAgICAvLyDkuInkvY3mlbAKICAgICAgICB7YTogMSwgYjogMiwgYzogMywgZDogNH0sICAgICAgICAgICAgIC8vIOWbm+S9jeaVsAogICAgICAgIHthOiAwLCBiOiAxLCBjOiAyLCBkOiAzLCBlOiA0LCBmOiA1fSwgLy8g5YWt5L2N5pWwCiAgICAgICAge2E6IDEsIGI6IDIsIGM6IDMsIGQ6IDQsIGU6IDV9LCAgICAgICAvLyDkupTkvY3mlbAKICAgICAgXTsKCiAgICAgIHRlc3RDYXNlcy5mb3JFYWNoKCh0ZXN0Q2FzZSwgaW5kZXgpID0+IHsKICAgICAgICBjb25zb2xlLmxvZyhgXG7mtYvor5XnlKjkvosgJHtpbmRleCArIDF9OmAsIHRlc3RDYXNlKTsKICAgICAgICBjb25zdCB0aHJlZURpZ2l0cyA9IHRoaXMuZXh0cmFjdFRocmVlRGlnaXROdW1iZXJzKHRlc3RDYXNlKTsKICAgICAgICBjb25zb2xlLmxvZygn5o+Q5Y+W55qE5LiJ5L2N5pWw57uE5ZCIOicsIHRocmVlRGlnaXRzKTsKCiAgICAgICAgLy8g5rWL6K+V5b2S5LiA5YyW5pWI5p6cCiAgICAgICAgY29uc3Qgbm9ybWFsaXplZFNldCA9IG5ldyBTZXQoKTsKICAgICAgICB0aHJlZURpZ2l0cy5mb3JFYWNoKGRpZ2l0ID0+IHsKICAgICAgICAgIGNvbnN0IG5vcm1hbGl6ZWQgPSB0aGlzLm5vcm1hbGl6ZVRocmVlRGlnaXRzKGRpZ2l0KTsKICAgICAgICAgIG5vcm1hbGl6ZWRTZXQuYWRkKG5vcm1hbGl6ZWQpOwogICAgICAgICAgY29uc29sZS5sb2coYCR7ZGlnaXR9IC0+ICR7bm9ybWFsaXplZH1gKTsKICAgICAgICB9KTsKICAgICAgICBjb25zb2xlLmxvZygn5b2S5LiA5YyW5ZCO55qE5ZSv5LiA57uE5ZCIOicsIEFycmF5LmZyb20obm9ybWFsaXplZFNldCkpOwogICAgICB9KTsKCiAgICAgIGNvbnNvbGUubG9nKCc9PT0g5rWL6K+V5a6M5oiQID09PScpOwogICAgfSwKICAgIC8qKiDorqHnrpfnu4TlkIjmlbAgQyhuLHIpICovCiAgICBjYWxjdWxhdGVDb21iaW5hdGlvbnMobiwgcikgewogICAgICBpZiAociA+IG4pIHJldHVybiAwOwogICAgICBpZiAociA9PT0gMCB8fCByID09PSBuKSByZXR1cm4gMTsKCiAgICAgIGxldCByZXN1bHQgPSAxOwogICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHI7IGkrKykgewogICAgICAgIHJlc3VsdCA9IHJlc3VsdCAqIChuIC0gaSkgLyAoaSArIDEpOwogICAgICB9CiAgICAgIHJldHVybiBNYXRoLnJvdW5kKHJlc3VsdCk7CiAgICB9LAogICAgLyoqIOWFs+mXreWvueivneahhiAqLwogICAgLyoqIOWKoOi9veeUqOaIt+WIl+ihqCAqLwogICAgYXN5bmMgbG9hZFVzZXJMaXN0KCkgewogICAgICB0cnkgewogICAgICAgIC8vIOWFiOiOt+WPluaJgOacieeUqOaItwogICAgICAgIGNvbnN0IHVzZXJSZXNwb25zZSA9IGF3YWl0IHJlcXVlc3QoewogICAgICAgICAgdXJsOiAnL3N5c3RlbS91c2VyL2xpc3QnLAogICAgICAgICAgbWV0aG9kOiAnZ2V0JywKICAgICAgICAgIHBhcmFtczogeyBwYWdlTnVtOiAxLCBwYWdlU2l6ZTogMTAwMCB9CiAgICAgICAgfSk7CgogICAgICAgIGlmICghdXNlclJlc3BvbnNlIHx8ICF1c2VyUmVzcG9uc2Uucm93cykgewogICAgICAgICAgdGhpcy51c2VyTGlzdCA9IFtdOwogICAgICAgICAgcmV0dXJuOwogICAgICAgIH0KCiAgICAgICAgY29uc3QgYWxsVXNlcnMgPSB1c2VyUmVzcG9uc2Uucm93cy5maWx0ZXIodXNlciA9PiB1c2VyLnN0YXR1cyA9PT0gJzAnKTsgLy8g5Y+q6I635Y+W5q2j5bi454q25oCB55qE55So5oi3CgogICAgICAgIC8vIOiOt+WPluacieS4i+azqOiusOW9leeahOeUqOaIt0lE5YiX6KGoCiAgICAgICAgY29uc3QgcmVjb3JkUmVzcG9uc2UgPSBhd2FpdCByZXF1ZXN0KHsKICAgICAgICAgIHVybDogJy9nYW1lL3JlY29yZC9saXN0JywKICAgICAgICAgIG1ldGhvZDogJ2dldCcsCiAgICAgICAgICBwYXJhbXM6IHsKICAgICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgICAgcGFnZVNpemU6IDEwMDAwMCAvLyDojrflj5bmiYDmnInorrDlvZXmnaXnu5/orqHnlKjmiLcKICAgICAgICAgIH0KICAgICAgICB9KTsKCiAgICAgICAgaWYgKHJlY29yZFJlc3BvbnNlICYmIHJlY29yZFJlc3BvbnNlLnJvd3MpIHsKICAgICAgICAgIC8vIOe7n+iuoeavj+S4queUqOaIt+eahOS4i+azqOiusOW9leaVsOmHjwogICAgICAgICAgY29uc3QgdXNlclJlY29yZENvdW50cyA9IHt9OwogICAgICAgICAgcmVjb3JkUmVzcG9uc2Uucm93cy5mb3JFYWNoKHJlY29yZCA9PiB7CiAgICAgICAgICAgIGNvbnN0IHVzZXJJZCA9IHJlY29yZC5zeXNVc2VySWQ7CiAgICAgICAgICAgIHVzZXJSZWNvcmRDb3VudHNbdXNlcklkXSA9ICh1c2VyUmVjb3JkQ291bnRzW3VzZXJJZF0gfHwgMCkgKyAxOwogICAgICAgICAgfSk7CgogICAgICAgICAgLy8g5Y+q5L+d55WZ5pyJ5LiL5rOo6K6w5b2V55qE55So5oi377yM5bm25re75Yqg6K6w5b2V5pWw6YeP5L+h5oGvCiAgICAgICAgICB0aGlzLnVzZXJMaXN0ID0gYWxsVXNlcnMKICAgICAgICAgICAgLmZpbHRlcih1c2VyID0+IHVzZXJSZWNvcmRDb3VudHNbdXNlci51c2VySWRdKQogICAgICAgICAgICAubWFwKHVzZXIgPT4gKHsKICAgICAgICAgICAgICAuLi51c2VyLAogICAgICAgICAgICAgIHJlY29yZENvdW50OiB1c2VyUmVjb3JkQ291bnRzW3VzZXIudXNlcklkXSB8fCAwCiAgICAgICAgICAgIH0pKQogICAgICAgICAgICAuc29ydCgoYSwgYikgPT4gYi5yZWNvcmRDb3VudCAtIGEucmVjb3JkQ291bnQpOyAvLyDmjInkuIvms6jorrDlvZXmlbDph4/pmY3luo/mjpLliJcKCiAgICAgICAgICBjb25zb2xlLmxvZyhg5Yqg6L2955So5oi35YiX6KGo5a6M5oiQ77yM5YWxICR7dGhpcy51c2VyTGlzdC5sZW5ndGh9IOS4quacieS4i+azqOiusOW9leeahOeUqOaIt2ApOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLnVzZXJMaXN0ID0gW107CiAgICAgICAgICBjb25zb2xlLndhcm4oJ+acquiOt+WPluWIsOS4i+azqOiusOW9leaVsOaNricpOwogICAgICAgIH0KCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W55So5oi35YiX6KGo5aSx6LSlOicsIGVycm9yKTsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5bnlKjmiLfliJfooajlpLHotKUnKTsKICAgICAgICB0aGlzLnVzZXJMaXN0ID0gW107CiAgICAgIH0KICAgIH0sCgogICAgLyoqIOiOt+WPluaMh+WumueUqOaIt+eahOiusOW9leaVsOaNriAqLwogICAgYXN5bmMgZmV0Y2hVc2VyUmVjb3JkRGF0YSh1c2VySWQpIHsKICAgICAgdHJ5IHsKICAgICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgcmVxdWVzdCh7CiAgICAgICAgICB1cmw6ICcvZ2FtZS9yZWNvcmQvbGlzdCcsCiAgICAgICAgICBtZXRob2Q6ICdnZXQnLAogICAgICAgICAgcGFyYW1zOiB7CiAgICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICAgIHBhZ2VTaXplOiAxMDAwMDAsCiAgICAgICAgICAgIHN5c1VzZXJJZDogdXNlcklkCiAgICAgICAgICB9CiAgICAgICAgfSk7CgogICAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS5yb3dzKSB7CiAgICAgICAgICByZXR1cm4gcmVzcG9uc2Uucm93czsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgcmV0dXJuIFtdOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bnlKjmiLforrDlvZXmlbDmja7lpLHotKU6JywgZXJyb3IpOwogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPlueUqOaIt+iusOW9leaVsOaNruWksei0pScpOwogICAgICAgIHJldHVybiBbXTsKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfQogICAgfSwKCiAgICAvKiog55So5oi36YCJ5oup5Y+Y5YyW5aSE55CGICovCiAgICBhc3luYyBoYW5kbGVVc2VyQ2hhbmdlKHVzZXJJZCkgewogICAgICAvLyDmuIXnqbrlvZPliY3nu5/orqHmlbDmja4KICAgICAgdGhpcy5tZXRob2RSYW5raW5nQnlHcm91cCA9IFtdOwogICAgICB0aGlzLm51bWJlclJhbmtpbmdCeUdyb3VwID0gW107CgogICAgICBpZiAodXNlcklkKSB7CiAgICAgICAgLy8g55u05o6l5Yqg6L296YCJ5Lit55So5oi355qE57uf6K6h5pWw5o2uCiAgICAgICAgYXdhaXQgdGhpcy5jYWxjdWxhdGVTdGF0aXN0aWNzKCk7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDlt7LliqDovb3nlKjmiLcgJHt0aGlzLmdldEN1cnJlbnRVc2VyTmFtZSgpfSDnmoTnu5/orqHmlbDmja5gKTsKICAgICAgfQogICAgfSwKCiAgICAvKiog6I635Y+W5b2T5YmN6YCJ5Lit55So5oi355qE5ZCN56ewICovCiAgICBnZXRDdXJyZW50VXNlck5hbWUoKSB7CiAgICAgIGlmICghdGhpcy5zZWxlY3RlZFVzZXJJZCkgcmV0dXJuICcnOwogICAgICBjb25zdCB1c2VyID0gdGhpcy51c2VyTGlzdC5maW5kKHUgPT4gdS51c2VySWQgPT09IHRoaXMuc2VsZWN0ZWRVc2VySWQpOwogICAgICByZXR1cm4gdXNlciA/ICh1c2VyLm5pY2tOYW1lIHx8IHVzZXIudXNlck5hbWUpIDogJyc7CiAgICB9LAoKICAgIC8qKiDlvannp43liIfmjaLlpITnkIYgKi8KICAgIGhhbmRsZUxvdHRlcnlUeXBlQ2hhbmdlKHRhYikgewogICAgICBjb25zb2xlLmxvZyhg5YiH5o2i5Yiw5b2p56eNOiAke3RhYi5uYW1lID09PSAnZnVjYWknID8gJ+emj+W9qTNEJyA6ICfkvZPlvalQMyd9YCk7CiAgICAgIC8vIOWIh+aNouW9qeenjeaXtu+8jOiuoeeul+WxnuaAp+S8muiHquWKqOabtOaWsOaYvuekuueahOaVsOaNrgogICAgfSwKCiAgICAvKiog5Yi35paw57uf6K6h5pWw5o2uICovCiAgICBhc3luYyByZWZyZXNoU3RhdGlzdGljcygpIHsKICAgICAgaWYgKHRoaXMuaXNBZG1pbiAmJiAhdGhpcy5zZWxlY3RlZFVzZXJJZCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+35YWI6YCJ5oup6KaB5p+l55yL57uf6K6h55qE55So5oi3Jyk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICB0cnkgewogICAgICAgIGF3YWl0IHRoaXMubG9hZEdhbWVNZXRob2RzKCk7CiAgICAgICAgYXdhaXQgdGhpcy5jYWxjdWxhdGVTdGF0aXN0aWNzKCk7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfnu5/orqHmlbDmja7lt7LliLfmlrAnKTsKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCfliLfmlrDnu5/orqHlpLHotKU6JywgZXJyb3IpOwogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WIt+aWsOe7n+iuoeWksei0pScpOwogICAgICB9CiAgICB9LAoKICAgIGNsb3NlKCkgewogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBmYWxzZTsKICAgICAgLy8g5riF56m66YCJ5oup54q25oCBCiAgICAgIHRoaXMuc2VsZWN0ZWRVc2VySWQgPSBudWxsOwogICAgICB0aGlzLmFjdGl2ZUxvdHRlcnlUeXBlID0gJ2Z1Y2FpJzsgLy8g6YeN572u5Li656aP5b2pCiAgICAgIC8vIOa4heepuuaJgOaciee7n+iuoeaVsOaNrgogICAgICB0aGlzLmNsZWFyQWxsUmFua2luZ0RhdGEoKTsKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["BetStatistics.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyMA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;;AAIA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BetStatistics.vue", "sourceRoot": "src/views/game/record/components", "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      title=\"下注统计\"\n      :visible.sync=\"dialogVisible\"\n      width=\"95%\"\n      append-to-body\n      style=\"margin-top: -50px;\"\n      :close-on-click-modal=\"false\"\n      class=\"statistics-dialog\">\n\n      <!-- 管理员用户选择器 -->\n      <div v-if=\"isAdmin\" class=\"admin-controls\" style=\"margin-bottom: 20px; padding: 15px; background: #f5f7fa; border-radius: 4px;\">\n        <div style=\"display: flex; align-items: center; gap: 15px;\">\n          <span style=\"font-weight: 500; color: #606266;\">选择用户:</span>\n          <el-select\n            v-model=\"selectedUserId\"\n            placeholder=\"选择用户查看统计\"\n            @change=\"handleUserChange\"\n            clearable\n            style=\"width: 250px;\">\n            <el-option\n              v-for=\"user in userList\"\n              :key=\"user.userId\"\n              :label=\"`${user.nickName || user.userName} (ID: ${user.userId}) - ${user.recordCount}条记录`\"\n              :value=\"user.userId\">\n            </el-option>\n          </el-select>\n          <el-button\n            type=\"primary\"\n            size=\"small\"\n            @click=\"refreshStatistics\"\n            :loading=\"loading\">\n            <i class=\"el-icon-refresh\"></i> 刷新统计\n          </el-button>\n          <span v-if=\"selectedUserId\" style=\"color: #67c23a; font-size: 12px;\">\n            当前查看: {{ getCurrentUserName() }}\n          </span>\n        </div>\n      </div>\n\n      <!-- 彩种选择标签页 - 所有用户都可见 -->\n      <div class=\"lottery-type-tabs-container\" style=\"margin-bottom: 20px; text-align: center;\">\n        <el-tabs v-model=\"activeLotteryType\" @tab-click=\"handleLotteryTypeChange\" type=\"card\">\n          <el-tab-pane name=\"fucai\">\n            <span slot=\"label\">\n              <i class=\"el-icon-medal-1\"></i>\n              福彩3D\n            </span>\n          </el-tab-pane>\n          <el-tab-pane name=\"ticai\">\n            <span slot=\"label\">\n              <i class=\"el-icon-trophy-1\"></i>\n              体彩排三\n            </span>\n          </el-tab-pane>\n        </el-tabs>\n      </div>\n\n      <!-- 号码搜索功能 -->\n      <div v-if=\"!isAdmin || (isAdmin && selectedUserId)\" class=\"search-container\" style=\"margin-bottom: 20px;\">\n        <div class=\"search-box\">\n          <el-input\n            v-model=\"searchNumber\"\n            placeholder=\"输入号码实时搜索相关投注（如：125）\"\n            prefix-icon=\"el-icon-search\"\n            clearable\n            @input=\"handleSearchInput\"\n            @clear=\"clearSearch\"\n            style=\"width: 350px; margin-right: 10px;\">\n          </el-input>\n          <el-button\n            v-if=\"isSearchMode\"\n            type=\"info\"\n            icon=\"el-icon-refresh-left\"\n            @click=\"clearSearch\">\n            显示全部\n          </el-button>\n        </div>\n\n        <!-- 搜索结果提示 -->\n        <div v-if=\"isSearchMode\" class=\"search-result-tip\" style=\"margin-top: 10px;\">\n          <el-alert\n            :title=\"searchResultTitle\"\n            type=\"info\"\n            :closable=\"false\"\n            show-icon>\n          </el-alert>\n        </div>\n      </div>\n\n      <!-- 管理员未选择用户时的提示 -->\n      <div v-if=\"isAdmin && !selectedUserId\" class=\"admin-no-selection\" style=\"text-align: center; padding: 60px 20px; color: #909399;\">\n        <i class=\"el-icon-user\" style=\"font-size: 48px; margin-bottom: 20px; display: block;\"></i>\n        <h3 style=\"margin: 0 0 10px 0; color: #606266;\">请选择要查看统计的用户</h3>\n        <p style=\"margin: 0; font-size: 14px;\">选择用户后将自动加载该用户的下注统计数据</p>\n      </div>\n\n      <!-- 统计内容区域 -->\n      <div v-if=\"!isAdmin || (isAdmin && selectedUserId)\" class=\"statistics-content\">\n\n        <!-- 当前彩种无数据时的提示 -->\n        <div v-if=\"currentMethodRanking.length === 0 && currentNumberRanking.length === 0\"\n             class=\"no-data-tip\"\n             style=\"text-align: center; padding: 60px 20px; color: #909399;\">\n          <i class=\"el-icon-data-analysis\" style=\"font-size: 48px; margin-bottom: 20px; display: block;\"></i>\n          <h3 style=\"margin: 0 0 10px 0; color: #606266;\">\n            {{ activeLotteryType === 'fucai' ? '福彩3D' : '体彩排三' }} 暂无统计数据\n          </h3>\n          <p style=\"margin: 0; font-size: 14px;\">\n            {{ activeLotteryType === 'fucai' ? '切换到体彩排三查看数据' : '切换到福彩3D查看数据' }}\n          </p>\n        </div>\n          <!-- 按玩法分组的热门三位数排行 -->\n        <div v-if=\"currentNumberRanking.length > 0\" class=\"numbers-rankings-section\">\n          <h3 class=\"main-section-title\">\n            <i class=\"el-icon-data-line\"></i>\n            各玩法热门三位数排行榜\n          </h3>\n\n          <!-- 使用Grid布局，一行显示五个玩法 -->\n          <div class=\"numbers-grid\">\n            <div\n              v-for=\"methodGroup in currentNumberRanking\"\n              :key=\"methodGroup.methodId\"\n              class=\"numbers-card\">\n\n              <div class=\"numbers-card-header\">\n                <span class=\"method-name\">{{ methodGroup.methodName }}</span>\n                <span class=\"method-count\">共{{ methodGroup.ranking.length }}项</span>\n              </div>\n\n              <div class=\"numbers-ranking-list\">\n                <div\n                  v-for=\"item in methodGroup.ranking\"\n                  :key=\"item.rank\"\n                  class=\"numbers-ranking-item\">\n                  <div class=\"rank-badge\" :class=\"getRankClass(item.rank)\">\n                    {{ item.rank }}\n                  </div>\n                  <div class=\"numbers-info\">\n                    <div class=\"hot-number\">{{ item.number }}</div>\n                    <div class=\"amount\">￥{{ item.totalAmount.toFixed(0) }}</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      <div class=\"statistics-container\">\n        <!-- 按玩法分组的投注金额排行 - 两列布局 -->\n        <div v-if=\"currentMethodRanking.length > 0\" class=\"method-rankings-section\">\n          <h3 class=\"main-section-title\">\n            <i class=\"el-icon-trophy\"></i>\n            各玩法投注排行榜\n          </h3>\n\n          <!-- 使用Grid布局，一行显示两个玩法 -->\n          <div class=\"method-grid\">\n            <div\n              v-for=\"methodGroup in currentMethodRanking\"\n              :key=\"methodGroup.methodId\"\n              class=\"method-card\">\n\n              <div class=\"method-card-header\">\n                <span class=\"method-name\">{{ methodGroup.methodName }}</span>\n                <span class=\"method-count\">共{{ methodGroup.ranking.length }}项</span>\n              </div>\n\n              <div class=\"ranking-list\">\n                <div\n                  v-for=\"item in methodGroup.ranking\"\n                  :key=\"item.rank\"\n                  class=\"ranking-item\">\n                  <div class=\"rank-badge\" :class=\"getRankClass(item.rank)\">\n                    {{ item.rank }}\n                  </div>\n                  <div class=\"bet-info\">\n                    <div class=\"bet-numbers\">{{ item.betNumbers }}</div>\n                    <div class=\"amount\">￥{{ item.totalAmount.toFixed(0) }}</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n\n      </div>\n      <!-- 统计内容区域结束 -->\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"refreshData\" type=\"primary\" icon=\"el-icon-refresh\">刷新数据</el-button>\n        <el-button @click=\"close\">关闭</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport request from '@/utils/request'\n\nexport default {\n  name: 'BetStatistics',\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      dialogVisible: false,\n      methodRankingByGroup: [], // 按玩法分组的排行榜\n      numberRankingByGroup: [], // 按玩法分组的号码排行榜\n      gameMethodsData: [], // 玩法数据\n      // 管理员功能相关\n      selectedUserId: null, // 选中的用户ID\n      userList: [], // 用户列表\n      loading: false, // 加载状态\n      // 彩种相关\n      activeLotteryType: 'fucai', // 当前选中的彩种：fucai(福彩3D) 或 ticai(体彩P3)\n      // 分彩种的统计数据\n      fucaiMethodRanking: [], // 福彩投注排行\n      fucaiNumberRanking: [], // 福彩热门三位数排行\n      ticaiMethodRanking: [], // 体彩投注排行\n      ticaiNumberRanking: [], // 体彩热门三位数排行\n      // 搜索功能相关\n      searchNumber: '', // 搜索的号码\n      isSearchMode: false, // 是否处于搜索模式\n      searchResultCount: 0, // 搜索结果数量\n      // 原始完整数据（用于搜索过滤）\n      originalFucaiMethodRanking: [],\n      originalFucaiNumberRanking: [],\n      originalTicaiMethodRanking: [],\n      originalTicaiNumberRanking: []\n    }\n  },\n  computed: {\n    /** 是否是管理员 */\n    isAdmin() {\n      const userInfo = this.$store.getters.userInfo;\n      const roles = this.$store.getters.roles;\n\n      // 检查是否是超级管理员（用户ID为1）\n      if (userInfo && userInfo.userId === 1) {\n        return true;\n      }\n\n      // 检查是否有管理员角色\n      if (roles && roles.length > 0) {\n        return roles.includes('admin') || roles.includes('3d_admin');\n      }\n\n      return false;\n    },\n\n    /** 当前彩种的投注排行数据 */\n    currentMethodRanking() {\n      return this.activeLotteryType === 'fucai' ? this.fucaiMethodRanking : this.ticaiMethodRanking;\n    },\n\n    /** 当前彩种的热门三位数排行数据 */\n    currentNumberRanking() {\n      return this.activeLotteryType === 'fucai' ? this.fucaiNumberRanking : this.ticaiNumberRanking;\n    },\n\n    /** 搜索结果标题 */\n    searchResultTitle() {\n      return `搜索号码 \"${this.searchNumber}\" 的结果：共找到 ${this.searchResultCount} 个匹配项`;\n    }\n  },\n  watch: {\n    visible(val) {\n      this.dialogVisible = val;\n      if (val) {\n        // 运行测试\n        this.testThreeDigitExtraction();\n\n        // 如果是管理员，只加载用户列表和玩法数据，不自动计算统计\n        if (this.isAdmin) {\n          this.loadUserList();\n          this.loadGameMethods();\n        } else {\n          // 普通用户自动加载统计\n          this.loadGameMethods().then(async () => {\n            // 普通用户从sessionStorage获取自己的数据\n            const sysUserId = sessionStorage.getItem('sysUserId');\n            console.log('[BetStatistics] sessionStorage中的sysUserId:', sysUserId);\n\n            if (sysUserId) {\n              this.selectedUserId = parseInt(sysUserId);\n              console.log('[BetStatistics] 设置selectedUserId:', this.selectedUserId);\n            } else {\n              console.warn('[BetStatistics] 无法从sessionStorage获取sysUserId');\n            }\n            await this.calculateStatistics();\n          });\n        }\n      }\n    },\n    dialogVisible(val) {\n      this.$emit('update:visible', val);\n    }\n  },\n  methods: {\n    /** 加载玩法数据 */\n    async loadGameMethods() {\n      try {\n        // 直接从API获取玩法数据\n        const response = await request({\n          url: '/game/odds/list',\n          method: 'get',\n          params: { pageNum: 1, pageSize: 1000 }\n        });\n\n        if (response && response.rows) {\n          this.gameMethodsData = response.rows;\n         \n        } else {\n          this.gameMethodsData = [];\n          console.warn('玩法数据为空');\n        }\n      } catch (error) {\n        console.error('获取玩法数据失败:', error);\n        // 如果API失败，使用基本的玩法映射\n        this.gameMethodsData = this.getBasicMethodsData();\n      }\n    },\n    /** 获取基本玩法数据（API失败时的备用方案） */\n    getBasicMethodsData() {\n      return [\n        { methodId: 1, methodName: '独胆' },\n        { methodId: 2, methodName: '一码定位' },\n        { methodId: 3, methodName: '两码组合' },\n        { methodId: 4, methodName: '两码对子' },\n        { methodId: 5, methodName: '三码直选' },\n        { methodId: 6, methodName: '三码组选' },\n        { methodId: 7, methodName: '三码组三' },\n        { methodId: 8, methodName: '四码组六' },\n        { methodId: 9, methodName: '四码组三' },\n        { methodId: 10, methodName: '五码组六' },\n        { methodId: 11, methodName: '五码组三' },\n        { methodId: 12, methodName: '六码组六' },\n        { methodId: 13, methodName: '六码组三' },\n        { methodId: 14, methodName: '七码组六' },\n        { methodId: 15, methodName: '七码组三' },\n        { methodId: 16, methodName: '八码组六' },\n        { methodId: 17, methodName: '八码组三' },\n        { methodId: 18, methodName: '九码组六' },\n        { methodId: 19, methodName: '九码组三' },\n        { methodId: 20, methodName: '包打组六' },\n        { methodId: 21, methodName: '7或20和值' },\n        { methodId: 22, methodName: '8或19和值' },\n        { methodId: 23, methodName: '9或18和值' },\n        { methodId: 24, methodName: '10或17和值' },\n        { methodId: 25, methodName: '11或16和值' },\n        { methodId: 26, methodName: '12或15和值' },\n        { methodId: 27, methodName: '13或14和值' },\n        { methodId: 28, methodName: '直选复式' },\n        { methodId: 29, methodName: '和值单双' },\n        { methodId: 30, methodName: '和值大小' },\n        { methodId: 31, methodName: '包打组三' },\n        { methodId: 100, methodName: '三码防对' }\n      ];\n    },\n    /** 获取玩法名称 */\n    getMethodName(methodId) {\n      const method = this.gameMethodsData.find(m => m.methodId === methodId);\n      return method ? method.methodName : `玩法${methodId}`;\n    },\n    /** 获取排名样式类 */\n    getRankClass(rank) {\n      if (rank === 1) return 'rank-first';\n      if (rank === 2) return 'rank-second';\n      if (rank === 3) return 'rank-third';\n      return 'rank-other';\n    },\n    /** 计算统计数据 */\n    async calculateStatistics() {\n      const data = await this.getRecordData();\n\n\n      if (!data || data.length === 0) {\n        if (this.isAdmin && !this.selectedUserId) {\n          this.$message.warning('请选择要查看统计的用户');\n        } else {\n          this.$message.warning('暂无统计数据，请先刷新record页面');\n        }\n        this.clearAllRankingData();\n        return;\n      }\n\n      // 按彩种分别计算统计数据\n      this.calculateStatisticsByLotteryType(data);\n\n      // 调试信息\n      console.log('统计计算完成:', {\n        福彩投注排行: this.fucaiMethodRanking.length,\n        福彩热门三位数: this.fucaiNumberRanking.length,\n        体彩投注排行: this.ticaiMethodRanking.length,\n        体彩热门三位数: this.ticaiNumberRanking.length,\n        当前选中彩种: this.activeLotteryType\n      });\n    },\n    /** 获取record数据 */\n    async getRecordData() {\n      try {\n        // 如果有选择的用户ID（管理员选择的用户或普通用户自己），直接从API获取数据\n        if (this.selectedUserId) {\n          return await this.fetchUserRecordData(this.selectedUserId);\n        }\n\n        // 如果是管理员但没有选择用户，提示选择用户\n        if (this.isAdmin) {\n          this.$message.warning('请选择要查看统计的用户');\n          return [];\n        }\n\n        // 普通用户：直接从API获取当前用户数据，从sessionStorage获取用户ID\n        console.log('[BetStatistics] 普通用户，直接从API获取数据');\n\n        // 从sessionStorage获取用户ID\n        const sysUserId = sessionStorage.getItem('sysUserId');\n        console.log('[BetStatistics] 从sessionStorage获取的sysUserId:', sysUserId);\n\n        if (sysUserId) {\n          console.log('[BetStatistics] 使用sysUserId调用API:', sysUserId);\n          const data = await this.fetchUserRecordData(parseInt(sysUserId));\n          console.log('[BetStatistics] API获取到的数据:', data);\n\n          if (data && data.length > 0) {\n            console.log(`[BetStatistics] 成功加载了 ${data.length} 条统计数据`);\n          } else {\n            console.warn('[BetStatistics] API返回空数据');\n          }\n\n          return data;\n        } else {\n          console.warn('[BetStatistics] 无法从sessionStorage获取sysUserId');\n          this.$message.warning('无法获取用户信息，请重新登录');\n          return [];\n        }\n      } catch (error) {\n        console.error('解析统计数据失败:', error);\n        return [];\n      }\n    },\n    /** 清空所有排行数据 */\n    clearAllRankingData() {\n      this.fucaiMethodRanking = [];\n      this.fucaiNumberRanking = [];\n      this.ticaiMethodRanking = [];\n      this.ticaiNumberRanking = [];\n      // 清空原始数据\n      this.originalFucaiMethodRanking = [];\n      this.originalFucaiNumberRanking = [];\n      this.originalTicaiMethodRanking = [];\n      this.originalTicaiNumberRanking = [];\n      // 重置搜索状态\n      this.clearSearch();\n    },\n\n    /** 按彩种分别计算统计数据 */\n    calculateStatisticsByLotteryType(data) {\n      // 调试：查看数据结构\n      if (data.length > 0) {\n        console.log('数据样本:', data[0]);\n        console.log('可能的彩种字段:', {\n          lotteryType: data[0].lotteryType,\n          gameType: data[0].gameType,\n          lotteryId: data[0].lotteryId,\n          gameName: data[0].gameName,\n          methodName: data[0].methodName\n        });\n\n        // 统计所有可能的彩种标识\n        const lotteryTypes = [...new Set(data.map(record => record.lotteryType))];\n        const gameTypes = [...new Set(data.map(record => record.gameType))];\n        const lotteryIds = [...new Set(data.map(record => record.lotteryId))];\n        const gameNames = [...new Set(data.map(record => record.gameName))];\n\n        console.log('数据中的彩种标识:', {\n          lotteryTypes,\n          gameTypes,\n          lotteryIds,\n          gameNames\n        });\n      }\n\n      // 按彩种分组数据（使用lotteryId字段）\n      const fucaiData = data.filter(record => this.isFucaiLottery(record.lotteryId));\n      const ticaiData = data.filter(record => this.isTicaiLottery(record.lotteryId));\n\n      console.log(`福彩3D数据: ${fucaiData.length}条, 体彩P3数据: ${ticaiData.length}条`);\n\n      // 如果没有明确的彩种区分，将所有数据归类为福彩3D（兼容性处理）\n      if (fucaiData.length === 0 && ticaiData.length === 0 && data.length > 0) {\n        console.log('未检测到彩种字段，将所有数据归类为福彩3D');\n        this.fucaiMethodRanking = this.calculateMethodRankingByGroup(data);\n        this.fucaiNumberRanking = this.calculateNumberRanking(data);\n        this.ticaiMethodRanking = [];\n        this.ticaiNumberRanking = [];\n        return;\n      }\n\n      // 分别计算福彩和体彩的统计数据\n      if (fucaiData.length > 0) {\n        this.originalFucaiMethodRanking = this.calculateMethodRankingByGroup(fucaiData);\n        this.originalFucaiNumberRanking = this.calculateNumberRanking(fucaiData);\n        this.fucaiMethodRanking = [...this.originalFucaiMethodRanking];\n        this.fucaiNumberRanking = [...this.originalFucaiNumberRanking];\n      } else {\n        this.originalFucaiMethodRanking = [];\n        this.originalFucaiNumberRanking = [];\n        this.fucaiMethodRanking = [];\n        this.fucaiNumberRanking = [];\n      }\n\n      if (ticaiData.length > 0) {\n        this.originalTicaiMethodRanking = this.calculateMethodRankingByGroup(ticaiData);\n        this.originalTicaiNumberRanking = this.calculateNumberRanking(ticaiData);\n        this.ticaiMethodRanking = [...this.originalTicaiMethodRanking];\n        this.ticaiNumberRanking = [...this.originalTicaiNumberRanking];\n      } else {\n        this.originalTicaiMethodRanking = [];\n        this.originalTicaiNumberRanking = [];\n        this.ticaiMethodRanking = [];\n        this.ticaiNumberRanking = [];\n      }\n    },\n\n    /** 判断是否为福彩3D */\n    isFucaiLottery(lotteryId) {\n      // 福彩3D的lotteryId是1\n      return lotteryId === 1 || lotteryId === '1';\n    },\n\n    /** 判断是否为体彩P3 */\n    isTicaiLottery(lotteryId) {\n      // 体彩P3的lotteryId是2\n      return lotteryId === 2 || lotteryId === '2';\n    },\n\n    /** 按玩法分组计算排行榜 */\n    calculateMethodRankingByGroup(data) {\n      console.log('[calculateMethodRankingByGroup] 开始计算各玩法投注排行榜');\n\n      // 按玩法分组，相同号码合并累计金额\n      const methodGroups = {};\n\n      data.forEach(record => {\n        const methodId = record.methodId;\n        const amount = parseFloat(record.money) || 0;\n\n        if (!methodGroups[methodId]) {\n          methodGroups[methodId] = new Map(); // 使用Map来存储号码和对应的累计数据\n        }\n\n        // 解析投注号码\n        try {\n          const betNumbers = JSON.parse(record.betNumbers || '{}');\n          const numbers = betNumbers.numbers || [];\n\n          // 为每个号码累计金额\n          numbers.forEach((numberObj) => {\n            let singleNumberDisplay = '';\n\n            if (typeof numberObj === 'object' && numberObj !== null) {\n              // 处理 {a:1, b:2, c:3, d:4, e:5} 格式\n              const keys = Object.keys(numberObj).sort();\n              const values = keys.map(key => String(numberObj[key]));\n              singleNumberDisplay = values.join('');\n            } else {\n              singleNumberDisplay = String(numberObj);\n            }\n\n            // 如果号码已存在，累计金额和记录数\n            if (methodGroups[methodId].has(singleNumberDisplay)) {\n              const existing = methodGroups[methodId].get(singleNumberDisplay);\n              existing.totalAmount += amount;\n              existing.recordCount += 1;\n            } else {\n              // 新号码，创建记录\n              methodGroups[methodId].set(singleNumberDisplay, {\n                betNumbers: singleNumberDisplay,\n                totalAmount: amount,\n                recordCount: 1\n              });\n            }\n          });\n        } catch (error) {\n          console.warn('解析投注号码失败:', record.betNumbers, error);\n          // 如果解析失败，使用原始数据\n          const fallbackNumbers = record.betNumbers || '未知号码';\n\n          if (methodGroups[methodId].has(fallbackNumbers)) {\n            const existing = methodGroups[methodId].get(fallbackNumbers);\n            existing.totalAmount += amount;\n            existing.recordCount += 1;\n          } else {\n            methodGroups[methodId].set(fallbackNumbers, {\n              betNumbers: fallbackNumbers,\n              totalAmount: amount,\n              recordCount: 1\n            });\n          }\n        }\n      });\n\n      console.log('[calculateMethodRankingByGroup] 开始生成排行榜');\n\n      // 为每个玩法生成排行榜并返回\n      return Object.entries(methodGroups).map(([methodId, numbersMap]) => {\n        // 将Map转换为数组并按总金额降序排序\n        const ranking = Array.from(numbersMap.values())\n          .sort((a, b) => b.totalAmount - a.totalAmount)\n          .map((item, index) => ({\n            rank: index + 1,\n            betNumbers: item.betNumbers,\n            totalAmount: item.totalAmount,\n            recordCount: item.recordCount\n          }));\n\n        console.log(`[calculateMethodRankingByGroup] 玩法${methodId}(${this.getMethodName(parseInt(methodId))})：${ranking.length}个不同号码`);\n\n        return {\n          methodId: parseInt(methodId),\n          methodName: this.getMethodName(parseInt(methodId)),\n          ranking\n        };\n      }).filter(group => group.ranking.length > 0); // 只显示有数据的玩法\n    },\n    /** 格式化投注号码用于显示 */\n    formatBetNumbersForDisplay(betNumbersKey) {\n      try {\n        const numbers = JSON.parse(betNumbersKey);\n        if (Array.isArray(numbers) && numbers.length > 0) {\n          return numbers.map(num => {\n            if (typeof num === 'object' && num !== null) {\n              // 处理 {a:1, b:2, c:3, d:4, e:5} 格式\n              const keys = Object.keys(num).sort();\n              const values = keys.map(key => String(num[key]));\n              return values.join('');\n            }\n            return String(num);\n          }).join(', ');\n        }\n      } catch (error) {\n        // 如果解析失败，直接返回原始字符串\n      }\n      return betNumbersKey.length > 20 ? betNumbersKey.substring(0, 20) + '...' : betNumbersKey;\n    },\n    /** 按玩法分组计算号码排行 - 只使用money字段 */\n    calculateNumberRanking(data) {\n      // 初始化返回数组\n      const numberRankingByGroup = [];\n\n      // 按玩法分组统计\n      const methodGroups = {};\n\n      data.forEach(record => {\n        try {\n          const methodId = record.methodId;\n          const money = parseFloat(record.money || 0);\n          const betNumbers = JSON.parse(record.betNumbers || '{}');\n          const numbers = betNumbers.numbers || [];\n\n          // 初始化玩法分组\n          if (!methodGroups[methodId]) {\n            methodGroups[methodId] = {\n              methodTotal: 0,\n              threeDigitMap: new Map()\n            };\n          }\n\n          methodGroups[methodId].methodTotal += money;\n\n          // 处理每个投注号码\n          numbers.forEach(numberObj => {\n            const threeDigits = this.extractThreeDigitNumbers(numberObj);\n\n            // 对当前号码的三位数组合去重（避免同一号码内重复组合多次累计）\n            const uniqueNormalizedDigits = new Set();\n\n            threeDigits.forEach(digit => {\n              // 归一化三位数：将数字按从小到大排序作为统一key\n              const normalizedDigit = this.normalizeThreeDigits(digit);\n              uniqueNormalizedDigits.add(normalizedDigit);\n            });\n\n            // 每个归一化后的三位数累加金额\n            uniqueNormalizedDigits.forEach(normalizedDigit => {\n              const currentAmount = methodGroups[methodId].threeDigitMap.get(normalizedDigit) || 0;\n              methodGroups[methodId].threeDigitMap.set(normalizedDigit, currentAmount + money);\n            });\n          });\n\n        } catch (error) {\n          console.error('解析失败:', error);\n        }\n      });\n\n\n\n      // 生成排行榜\n   \n      this.numberRankingByGroup = [];\n\n      Object.entries(methodGroups).forEach(([methodId, group]) => {\n        // 转换Map为数组并排序，显示所有数据\n        const sortedThreeDigits = Array.from(group.threeDigitMap.entries())\n          .sort(([,a], [,b]) => b - a);\n\n        const ranking = sortedThreeDigits.map(([number, amount], index) => ({\n          rank: index + 1,\n          number,\n          count: 1,\n          totalAmount: amount,\n          percentage: group.methodTotal > 0 ? ((amount / group.methodTotal) * 100).toFixed(1) : '0.0'\n        }));\n\n        if (ranking.length > 0) {\n          numberRankingByGroup.push({\n            methodId: parseInt(methodId),\n            methodName: this.getMethodName(parseInt(methodId)),\n            ranking\n          });\n        }\n      });\n\n      return numberRankingByGroup;\n    },\n    /** 从投注号码中提取所有可能的三位数组合 */\n    extractThreeDigitNumbers(numberObj) {\n      const numbers = [];\n\n      // 处理不同的号码格式\n      if (typeof numberObj === 'string') {\n        // 直接是字符串格式的号码\n        const cleanStr = numberObj.replace(/\\D/g, ''); // 移除非数字字符\n        this.generateThreeDigitCombinations(cleanStr.split(''), numbers);\n      } else if (typeof numberObj === 'object' && numberObj !== null) {\n        // 对象格式 {a: 1, b: 2, c: 3, d: 4, e: 5}\n        const keys = Object.keys(numberObj).sort();\n        const digits = keys.map(key => String(numberObj[key]));\n\n    \n\n        // 生成所有可能的三位数组合\n        this.generateThreeDigitCombinations(digits, numbers);\n      }\n\n      return numbers;\n    },\n    /** 生成所有可能的三位数组合 */\n    generateThreeDigitCombinations(digits, numbers) {\n      const n = digits.length;\n\n      // 如果数字少于3个，无法组成三位数\n      if (n < 3) {\n        return;\n      }\n\n      // 生成所有可能的三位数组合（C(n,3)）\n      for (let i = 0; i < n - 2; i++) {\n        for (let j = i + 1; j < n - 1; j++) {\n          for (let k = j + 1; k < n; k++) {\n            const threeDigit = digits[i] + digits[j] + digits[k];\n            if (/^\\d{3}$/.test(threeDigit)) {\n              numbers.push(threeDigit);\n            }\n          }\n        }\n      }\n    },\n\n    /** 归一化三位数：将数字按从小到大排序，统一相同数字的不同排列 */\n    normalizeThreeDigits(threeDigit) {\n      // 将三位数字符串转换为数组，排序后重新组合\n      return threeDigit.split('').sort().join('');\n    },\n\n    /** 搜索输入处理 - 实时搜索 */\n    handleSearchInput(value) {\n      // 只允许输入数字\n      const cleanValue = value.replace(/\\D/g, '');\n      this.searchNumber = cleanValue;\n\n      // 实时搜索\n      if (cleanValue.trim()) {\n        // 有输入内容时执行搜索\n        this.performSearchInternal(cleanValue);\n      } else {\n        // 输入为空时清除搜索\n        this.clearSearch();\n      }\n    },\n\n    /** 内部搜索方法 - 不显示消息提示 */\n    performSearchInternal(searchValue) {\n      if (!searchValue || !searchValue.trim()) {\n        return;\n      }\n\n      this.isSearchMode = true;\n\n      try {\n        // 搜索当前彩种的数据\n        const searchResults = this.searchInRankingData(searchValue);\n\n        // 更新显示数据为搜索结果\n        if (this.activeLotteryType === 'fucai') {\n          this.fucaiMethodRanking = searchResults.methodRanking;\n          this.fucaiNumberRanking = searchResults.numberRanking;\n        } else {\n          this.ticaiMethodRanking = searchResults.methodRanking;\n          this.ticaiNumberRanking = searchResults.numberRanking;\n        }\n\n        // 计算搜索结果数量\n        this.searchResultCount = this.calculateSearchResultCount(searchResults);\n      } catch (error) {\n        console.error('搜索失败:', error);\n        // 实时搜索不显示错误消息，避免频繁弹窗\n      }\n    },\n\n    /** 清除搜索 */\n    clearSearch() {\n      this.searchNumber = '';\n      this.isSearchMode = false;\n      this.searchResultCount = 0;\n\n      // 恢复原始数据\n      this.fucaiMethodRanking = [...this.originalFucaiMethodRanking];\n      this.fucaiNumberRanking = [...this.originalFucaiNumberRanking];\n      this.ticaiMethodRanking = [...this.originalTicaiMethodRanking];\n      this.ticaiNumberRanking = [...this.originalTicaiNumberRanking];\n    },\n\n    /** 在排行数据中搜索包含指定号码的项目 */\n    searchInRankingData(searchNumber) {\n      const originalMethodRanking = this.activeLotteryType === 'fucai'\n        ? this.originalFucaiMethodRanking\n        : this.originalTicaiMethodRanking;\n\n      const originalNumberRanking = this.activeLotteryType === 'fucai'\n        ? this.originalFucaiNumberRanking\n        : this.originalTicaiNumberRanking;\n\n      // 搜索投注排行榜\n      const filteredMethodRanking = originalMethodRanking.map(methodGroup => {\n        const filteredRanking = methodGroup.ranking.filter(item => {\n          return this.isNumberMatch(item.betNumbers, searchNumber);\n        });\n\n        return {\n          ...methodGroup,\n          ranking: filteredRanking\n        };\n      }).filter(methodGroup => methodGroup.ranking.length > 0);\n\n      // 搜索热门三位数排行榜（使用专门的三位数匹配逻辑）\n      const filteredNumberRanking = originalNumberRanking.map(methodGroup => {\n        const filteredRanking = methodGroup.ranking.filter(item => {\n          return this.isThreeDigitMatch(item.number, searchNumber);\n        });\n\n        return {\n          ...methodGroup,\n          ranking: filteredRanking\n        };\n      }).filter(methodGroup => methodGroup.ranking.length > 0);\n\n      return {\n        methodRanking: filteredMethodRanking,\n        numberRanking: filteredNumberRanking\n      };\n    },\n\n    /** 判断号码是否匹配搜索条件 */\n    isNumberMatch(targetNumber, searchNumber) {\n      if (!targetNumber || !searchNumber) return false;\n\n      const target = String(targetNumber);\n      const search = String(searchNumber);\n      const searchDigits = search.split('');\n      const targetDigits = target.split('');\n\n      // 核心逻辑：检查搜索号码中的每个数字是否都在目标号码中出现\n      // 这样可以匹配各种长度的目标号码\n\n      // 方案1：任意匹配 - 搜索号码中的任一数字在目标号码中出现即匹配\n      // 适用于独胆等单数字玩法\n      const hasAnyDigit = searchDigits.some(digit => targetDigits.includes(digit));\n\n      // 方案2：完全匹配 - 搜索号码中的所有数字都在目标号码中出现\n      // 适用于多数字组合玩法\n      const hasAllDigits = searchDigits.every(digit => targetDigits.includes(digit));\n\n      // 方案3：精确匹配 - 目标号码包含搜索号码的连续子串\n      const hasExactSequence = target.includes(search);\n\n      // 根据不同情况采用不同的匹配策略\n      if (search.length === 1) {\n        // 单数字搜索：只要目标号码包含该数字即匹配\n        return hasAnyDigit;\n      } else if (search.length === 2) {\n        // 双数字搜索：根据目标长度采用不同策略\n        if (target.length === 1) {\n          // 独胆：任意匹配（搜索12时显示1和2）\n          return hasAnyDigit;\n        } else {\n          // 两码及以上：要求包含所有搜索数字\n          return hasExactSequence || hasAllDigits;\n        }\n      } else if (search.length >= 3) {\n        // 三位数及以上搜索：采用多种匹配策略\n\n        // 策略1：精确序列匹配（如搜索125，目标12534匹配）\n        if (hasExactSequence) {\n          return true;\n        }\n\n        // 策略2：对于单数字目标号码，采用任意匹配\n        if (target.length === 1) {\n          return hasAnyDigit;\n        }\n\n        // 策略3：对于两位数目标号码，检查是否为搜索号码的子组合\n        if (target.length === 2) {\n          // 检查两位数目标是否是搜索号码中任意两个数字的组合\n          return this.isTwoDigitSubset(target, search);\n        }\n\n        // 策略4：对于三位数目标的特殊处理\n        if (target.length === 3) {\n          if (search.length === 2) {\n            // 搜索两位数：三位数必须同时包含这两个数字\n            return this.isThreeDigitContainsTwoDigits(target, search);\n          } else if (search.length === 3) {\n            // 搜索三位数：检查是否为相同数字组合（归一化比较）\n            const normalizedSearch = this.normalizeThreeDigits(search);\n            const normalizedTarget = this.normalizeThreeDigits(target);\n            return normalizedSearch === normalizedTarget;\n          }\n        }\n\n        // 策略5：对于四码及以上的目标号码，根据搜索长度采用不同策略\n        if (target.length >= 4) {\n          if (search.length === 1) {\n            // 搜索单数字：目标号码必须包含该数字\n            return hasAnyDigit;\n          } else if (search.length === 2) {\n            // 搜索两位数：目标号码必须同时包含这两个数字\n            const [digit1, digit2] = search.split('');\n            const targetDigits = target.split('');\n            return targetDigits.includes(digit1) && targetDigits.includes(digit2);\n          } else if (search.length >= 3) {\n            // 搜索三位数及以上：目标号码必须包含所有搜索数字\n            return hasAllDigits;\n          }\n        }\n\n        // 策略6：其他情况采用完全匹配\n        return hasAllDigits;\n      }\n\n      return false;\n    },\n\n    /** 检查两位数是否为搜索号码的子组合 */\n    isTwoDigitSubset(twoDigitTarget, searchNumber) {\n      const target = String(twoDigitTarget);\n      const search = String(searchNumber);\n\n      if (target.length !== 2 || search.length < 2) {\n        return false;\n      }\n\n      const [targetDigit1, targetDigit2] = target.split('');\n      const searchDigits = search.split('');\n\n      // 检查两位数的两个数字是否都在搜索号码中\n      const hasDigit1 = searchDigits.includes(targetDigit1);\n      const hasDigit2 = searchDigits.includes(targetDigit2);\n\n      return hasDigit1 && hasDigit2;\n    },\n\n    /** 检查三位数是否包含指定的两个数字 */\n    isThreeDigitContainsTwoDigits(threeDigitTarget, twoDigitSearch) {\n      const target = String(threeDigitTarget);\n      const search = String(twoDigitSearch);\n\n      if (target.length !== 3 || search.length !== 2) {\n        return false;\n      }\n\n      const [searchDigit1, searchDigit2] = search.split('');\n      const targetDigits = target.split('');\n\n      // 检查三位数是否同时包含搜索的两个数字\n      const hasDigit1 = targetDigits.includes(searchDigit1);\n      const hasDigit2 = targetDigits.includes(searchDigit2);\n\n      return hasDigit1 && hasDigit2;\n    },\n\n    /** 专门用于三位数排行榜的匹配逻辑 */\n    isThreeDigitMatch(targetThreeDigit, searchNumber) {\n      if (!targetThreeDigit || !searchNumber) return false;\n\n      const target = String(targetThreeDigit);\n      const search = String(searchNumber);\n\n      // 三位数排行榜的目标都是3位数，需要特殊处理\n      if (target.length !== 3) return false;\n\n      if (search.length === 1) {\n        // 搜索单数字：三位数中包含该数字即匹配\n        return target.includes(search);\n      } else if (search.length === 2) {\n        // 搜索双数字：三位数中必须同时包含这两个数字\n        const [digit1, digit2] = search.split('');\n        const targetDigits = target.split('');\n        return targetDigits.includes(digit1) && targetDigits.includes(digit2);\n      } else if (search.length === 3) {\n        // 搜索三位数：必须是相同的数字组合（归一化后比较）\n        const normalizedSearch = this.normalizeThreeDigits(search);\n        const normalizedTarget = this.normalizeThreeDigits(target);\n        return normalizedSearch === normalizedTarget;\n      } else if (search.length > 3) {\n        // 搜索超过三位数：检查三位数是否包含搜索号码中的任意三个数字组合\n        const searchDigits = search.split('');\n\n        // 生成搜索号码的所有三位数组合，看是否有匹配的\n        for (let i = 0; i < searchDigits.length - 2; i++) {\n          for (let j = i + 1; j < searchDigits.length - 1; j++) {\n            for (let k = j + 1; k < searchDigits.length; k++) {\n              const searchCombo = searchDigits[i] + searchDigits[j] + searchDigits[k];\n              const normalizedSearchCombo = this.normalizeThreeDigits(searchCombo);\n              const normalizedTarget = this.normalizeThreeDigits(target);\n              if (normalizedSearchCombo === normalizedTarget) {\n                return true;\n              }\n            }\n          }\n        }\n        return false;\n      }\n\n      return false;\n    },\n\n    /** 计算搜索结果数量 */\n    calculateSearchResultCount(searchResults) {\n      let count = 0;\n\n      // 统计投注排行榜匹配项\n      searchResults.methodRanking.forEach(methodGroup => {\n        count += methodGroup.ranking.length;\n      });\n\n      // 统计热门三位数排行榜匹配项\n      searchResults.numberRanking.forEach(methodGroup => {\n        count += methodGroup.ranking.length;\n      });\n\n      return count;\n    },\n    /** 刷新数据 */\n    async refreshData() {\n      await this.calculateStatistics();\n      this.$message.success('数据已刷新');\n    },\n    /** 测试三位数组合提取逻辑 */\n    testThreeDigitExtraction() {\n      console.log('=== 测试三位数组合提取和归一化逻辑 ===');\n\n      // 测试不同长度的号码\n      const testCases = [\n        {a: 1, b: 2, c: 3},                    // 三位数\n        {a: 1, b: 2, c: 3, d: 4},             // 四位数\n        {a: 0, b: 1, c: 2, d: 3, e: 4, f: 5}, // 六位数\n        {a: 1, b: 2, c: 3, d: 4, e: 5},       // 五位数\n      ];\n\n      testCases.forEach((testCase, index) => {\n        console.log(`\\n测试用例 ${index + 1}:`, testCase);\n        const threeDigits = this.extractThreeDigitNumbers(testCase);\n        console.log('提取的三位数组合:', threeDigits);\n\n        // 测试归一化效果\n        const normalizedSet = new Set();\n        threeDigits.forEach(digit => {\n          const normalized = this.normalizeThreeDigits(digit);\n          normalizedSet.add(normalized);\n          console.log(`${digit} -> ${normalized}`);\n        });\n        console.log('归一化后的唯一组合:', Array.from(normalizedSet));\n      });\n\n      console.log('=== 测试完成 ===');\n    },\n    /** 计算组合数 C(n,r) */\n    calculateCombinations(n, r) {\n      if (r > n) return 0;\n      if (r === 0 || r === n) return 1;\n\n      let result = 1;\n      for (let i = 0; i < r; i++) {\n        result = result * (n - i) / (i + 1);\n      }\n      return Math.round(result);\n    },\n    /** 关闭对话框 */\n    /** 加载用户列表 */\n    async loadUserList() {\n      try {\n        // 先获取所有用户\n        const userResponse = await request({\n          url: '/system/user/list',\n          method: 'get',\n          params: { pageNum: 1, pageSize: 1000 }\n        });\n\n        if (!userResponse || !userResponse.rows) {\n          this.userList = [];\n          return;\n        }\n\n        const allUsers = userResponse.rows.filter(user => user.status === '0'); // 只获取正常状态的用户\n\n        // 获取有下注记录的用户ID列表\n        const recordResponse = await request({\n          url: '/game/record/list',\n          method: 'get',\n          params: {\n            pageNum: 1,\n            pageSize: 100000 // 获取所有记录来统计用户\n          }\n        });\n\n        if (recordResponse && recordResponse.rows) {\n          // 统计每个用户的下注记录数量\n          const userRecordCounts = {};\n          recordResponse.rows.forEach(record => {\n            const userId = record.sysUserId;\n            userRecordCounts[userId] = (userRecordCounts[userId] || 0) + 1;\n          });\n\n          // 只保留有下注记录的用户，并添加记录数量信息\n          this.userList = allUsers\n            .filter(user => userRecordCounts[user.userId])\n            .map(user => ({\n              ...user,\n              recordCount: userRecordCounts[user.userId] || 0\n            }))\n            .sort((a, b) => b.recordCount - a.recordCount); // 按下注记录数量降序排列\n\n          console.log(`加载用户列表完成，共 ${this.userList.length} 个有下注记录的用户`);\n        } else {\n          this.userList = [];\n          console.warn('未获取到下注记录数据');\n        }\n\n      } catch (error) {\n        console.error('获取用户列表失败:', error);\n        this.$message.error('获取用户列表失败');\n        this.userList = [];\n      }\n    },\n\n    /** 获取指定用户的记录数据 */\n    async fetchUserRecordData(userId) {\n      try {\n        this.loading = true;\n        const response = await request({\n          url: '/game/record/list',\n          method: 'get',\n          params: {\n            pageNum: 1,\n            pageSize: 100000,\n            sysUserId: userId\n          }\n        });\n\n        if (response && response.rows) {\n          return response.rows;\n        } else {\n          return [];\n        }\n      } catch (error) {\n        console.error('获取用户记录数据失败:', error);\n        this.$message.error('获取用户记录数据失败');\n        return [];\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    /** 用户选择变化处理 */\n    async handleUserChange(userId) {\n      // 清空当前统计数据\n      this.methodRankingByGroup = [];\n      this.numberRankingByGroup = [];\n\n      if (userId) {\n        // 直接加载选中用户的统计数据\n        await this.calculateStatistics();\n        this.$message.success(`已加载用户 ${this.getCurrentUserName()} 的统计数据`);\n      }\n    },\n\n    /** 获取当前选中用户的名称 */\n    getCurrentUserName() {\n      if (!this.selectedUserId) return '';\n      const user = this.userList.find(u => u.userId === this.selectedUserId);\n      return user ? (user.nickName || user.userName) : '';\n    },\n\n    /** 彩种切换处理 */\n    handleLotteryTypeChange(tab) {\n      console.log(`切换到彩种: ${tab.name === 'fucai' ? '福彩3D' : '体彩P3'}`);\n      // 切换彩种时，计算属性会自动更新显示的数据\n    },\n\n    /** 刷新统计数据 */\n    async refreshStatistics() {\n      if (this.isAdmin && !this.selectedUserId) {\n        this.$message.warning('请先选择要查看统计的用户');\n        return;\n      }\n\n      try {\n        await this.loadGameMethods();\n        await this.calculateStatistics();\n        this.$message.success('统计数据已刷新');\n      } catch (error) {\n        console.error('刷新统计失败:', error);\n        this.$message.error('刷新统计失败');\n      }\n    },\n\n    close() {\n      this.dialogVisible = false;\n      // 清空选择状态\n      this.selectedUserId = null;\n      this.activeLotteryType = 'fucai'; // 重置为福彩\n      // 清空所有统计数据\n      this.clearAllRankingData();\n    }\n  }\n}\n</script>\n\n<style scoped>\n/* 管理员控制区域样式 */\n.admin-controls {\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n}\n\n.admin-controls .el-select {\n  background: white;\n}\n\n/* 彩种标签页样式 */\n.lottery-type-tabs {\n  margin-left: 20px;\n  flex: 1;\n}\n\n.lottery-type-tabs-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header {\n  border-bottom: none;\n  margin: 0;\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__nav,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__nav {\n  border: none;\n  border-radius: 8px;\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n  padding: 4px;\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__item,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__item {\n  border: none;\n  background: transparent;\n  color: #606266;\n  font-weight: 500;\n  padding: 8px 16px;\n  margin-right: 4px;\n  border-radius: 6px;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__item:hover,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__item:hover {\n  color: #409eff;\n  background: rgba(64, 158, 255, 0.1);\n  transform: translateY(-1px);\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__item.is-active,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__item.is-active {\n  color: white;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  font-weight: 600;\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n  transform: translateY(-1px);\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__item.is-active::before,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__item.is-active::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, transparent 100%);\n  pointer-events: none;\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__item i,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__item i {\n  margin-right: 6px;\n  font-size: 16px;\n}\n\n.lottery-type-tabs .el-tabs__content,\n.lottery-type-tabs-container .el-tabs__content {\n  display: none; /* 隐藏内容区域，因为我们只用标签页切换 */\n}\n\n.statistics-dialog {\n  .el-dialog {\n    border-radius: 12px;\n  }\n  \n  .el-dialog__header {\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    color: white;\n    padding: 20px 24px;\n  }\n  \n  .el-dialog__title {\n    color: white;\n    font-weight: 600;\n    font-size: 18px;\n  }\n}\n\n.statistics-container {\n  padding: 10px 0;\n}\n\n.main-section-title {\n  display: flex;\n  align-items: center;\n  font-size: 16px;\n  font-weight: 600;\n  color: #2c3e50;\n  margin-bottom: 8px;\n  padding-bottom: 4px;\n  border-bottom: 1px solid #ecf0f1;\n}\n\n.main-section-title i {\n  margin-right: 4px;\n  color: #667eea;\n  font-size: 14px;\n}\n\n/* 玩法排行榜样式 */\n.method-rankings-section {\n  margin-bottom: 15px;\n}\n\n.method-grid {\n  display: grid;\n  grid-template-columns: repeat(5, 1fr);\n  gap: 6px;\n}\n\n@media (max-width: 1400px) {\n  .method-grid {\n    grid-template-columns: repeat(4, 1fr);\n  }\n}\n\n@media (max-width: 1100px) {\n  .method-grid {\n    grid-template-columns: repeat(3, 1fr);\n  }\n}\n\n@media (max-width: 800px) {\n  .method-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n\n@media (max-width: 500px) {\n  .method-grid {\n    grid-template-columns: 1fr;\n  }\n}\n\n.method-card {\n  background: white;\n  border-radius: 10px;\n  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.12);\n  overflow: hidden;\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n  min-width: 0; /* 防止内容溢出 */\n  min-height: 200px; /* 最小高度 */\n  max-height: 500px; /* 增加最大高度以适应更多数据 */\n}\n\n.method-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.18);\n}\n\n.method-card-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 8px 12px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.method-name {\n  font-size: 15px;\n  font-weight: 700;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.method-count {\n  font-size: 13px;\n  opacity: 0.9;\n}\n\n.ranking-list {\n  padding: 8px;\n  max-height: 420px; /* 增加列表高度以适应更多数据 */\n  overflow-y: auto; /* 添加垂直滚动条 */\n}\n\n/* 滚动条样式 */\n.ranking-list::-webkit-scrollbar {\n  width: 4px;\n}\n\n.ranking-list::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 2px;\n}\n\n.ranking-list::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 2px;\n}\n\n.ranking-list::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n/* 热门三位数排行榜滚动条样式 */\n.numbers-ranking-list::-webkit-scrollbar {\n  width: 4px;\n}\n\n.numbers-ranking-list::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 2px;\n}\n\n.numbers-ranking-list::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 2px;\n}\n\n.numbers-ranking-list::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n.ranking-item {\n  display: flex;\n  align-items: center;\n  padding: 4px 8px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.ranking-item:last-child {\n  border-bottom: none;\n}\n\n.rank-badge {\n  min-width: 18px;\n  height: 18px;\n  padding: 0 4px;\n  border-radius: 9px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  font-size: 10px;\n  color: white;\n  flex: 0 0 auto;\n  margin-right: 8px;\n  white-space: nowrap;\n}\n\n.rank-first {\n  background: linear-gradient(135deg, #ff6b6b, #ee5a24);\n}\n\n.rank-second {\n  background: linear-gradient(135deg, #feca57, #ff9ff3);\n}\n\n.rank-third {\n  background: linear-gradient(135deg, #48dbfb, #0abde3);\n}\n\n.rank-other {\n  background: linear-gradient(135deg, #a4b0be, #747d8c);\n}\n\n.bet-info {\n  display: flex;\n  align-items: center;\n  flex: 1;\n  min-width: 0;\n}\n\n.bet-numbers {\n  font-size: 15px;\n  font-weight: 900;\n  color: #ffffff;\n  font-family: 'Courier New', monospace;\n  background: linear-gradient(135deg, #667eea, #764ba2);\n  padding: 3px 8px;\n  border-radius: 5px;\n  text-align: center;\n  box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);\n  letter-spacing: 0.5px;\n  flex: 2;\n  margin-right: 8px;\n}\n\n.amount {\n  color: #e74c3c;\n  font-weight: 700;\n  font-size: 14px;\n  white-space: nowrap;\n  flex: 1;\n  text-align: right;\n}\n\n.record-count {\n  color: #95a5a6;\n  font-size: 11px;\n  text-align: right;\n  margin-top: 2px;\n}\n\n/* 热门三位数样式 */\n.numbers-section {\n  margin-bottom: 30px;\n}\n\n.ranking-table {\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.amount-text {\n  font-weight: 600;\n  color: #e74c3c;\n}\n\n.percentage-text {\n  font-weight: 500;\n  color: #3498db;\n}\n\n/* 热门三位数卡片样式 */\n.numbers-rankings-section {\n  margin-bottom: 15px;\n}\n\n.numbers-grid {\n  display: grid;\n  grid-template-columns: repeat(5, 1fr);\n  gap: 6px;\n}\n\n@media (max-width: 1400px) {\n  .numbers-grid {\n    grid-template-columns: repeat(4, 1fr);\n  }\n}\n\n@media (max-width: 1100px) {\n  .numbers-grid {\n    grid-template-columns: repeat(3, 1fr);\n  }\n}\n\n@media (max-width: 800px) {\n  .numbers-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n\n@media (max-width: 500px) {\n  .numbers-grid {\n    grid-template-columns: 1fr;\n  }\n}\n\n.numbers-card {\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n  min-width: 0;\n  min-height: 120px;\n  max-height: 400px; /* 增加最大高度以适应更多数据 */\n}\n\n.numbers-card:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.numbers-card-header {\n  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);\n  color: white;\n  padding: 4px 6px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.numbers-ranking-list {\n  padding: 4px;\n  max-height: 320px; /* 限制列表高度以适应更多数据 */\n  overflow-y: auto; /* 添加垂直滚动条 */\n}\n\n.numbers-ranking-item {\n  display: flex;\n  align-items: center;\n  padding: 2px 4px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.numbers-ranking-item:last-child {\n  border-bottom: none;\n}\n\n.numbers-info {\n  display: flex;\n  align-items: center;\n  flex: 1;\n  min-width: 0;\n}\n\n.numbers-info .hot-number {\n  font-family: 'Courier New', monospace;\n  font-weight: 900;\n  font-size: 13px;\n  color: #ffffff;\n  background: linear-gradient(135deg, #27ae60, #2ecc71);\n  padding: 2px 4px;\n  border-radius: 3px;\n  text-align: center;\n  flex: 2;\n  margin-right: 4px;\n}\n\n.numbers-info .amount {\n  color: #e74c3c;\n  font-weight: 700;\n  font-size: 13px;\n  white-space: nowrap;\n  flex: 1;\n  text-align: right;\n}\n\n\n\n.dialog-footer {\n  text-align: right;\n  padding-top: 20px;\n  border-top: 1px solid #ecf0f1;\n}\n\n/* 搜索功能样式 */\n.search-container {\n  padding: 15px 20px;\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n  border-radius: 8px;\n  border: 1px solid #e4e7ed;\n}\n\n.search-box {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-wrap: wrap;\n  gap: 10px;\n}\n\n.search-box .el-input {\n  max-width: 300px;\n  flex: 1;\n  min-width: 200px;\n}\n\n.search-box .el-button {\n  border-radius: 6px;\n  font-weight: 500;\n  transition: all 0.3s ease;\n}\n\n.search-box .el-button:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.search-result-tip {\n  text-align: center;\n}\n\n.search-result-tip .el-alert {\n  border-radius: 6px;\n}\n\n.search-result-tip .el-alert--info {\n  background-color: rgba(64, 158, 255, 0.1);\n  border-color: rgba(64, 158, 255, 0.2);\n}\n\n/* 响应式调整 */\n@media (max-width: 768px) {\n  .search-box {\n    flex-direction: column;\n    align-items: stretch;\n  }\n\n  .search-box .el-input {\n    max-width: none;\n    width: 100%;\n  }\n\n  .search-box .el-button {\n    width: 100%;\n    margin-top: 5px;\n  }\n}\n</style>\n"]}]}