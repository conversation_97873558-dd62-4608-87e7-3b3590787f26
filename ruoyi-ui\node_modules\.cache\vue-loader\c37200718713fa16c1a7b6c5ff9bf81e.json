{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetStatistics.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetStatistics.vue", "mtime": 1756001963955}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750942928175}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750942928768}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCByZXF1ZXN0IGZyb20gJ0AvdXRpbHMvcmVxdWVzdCcKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnQmV0U3RhdGlzdGljcycsCiAgcHJvcHM6IHsKICAgIHZpc2libGU6IHsKICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgZGVmYXVsdDogZmFsc2UKICAgIH0KICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgbWV0aG9kUmFua2luZ0J5R3JvdXA6IFtdLCAvLyDmjInnjqnms5XliIbnu4TnmoTmjpLooYzmppwKICAgICAgbnVtYmVyUmFua2luZ0J5R3JvdXA6IFtdLCAvLyDmjInnjqnms5XliIbnu4TnmoTlj7fnoIHmjpLooYzmppwKICAgICAgZ2FtZU1ldGhvZHNEYXRhOiBbXSwgLy8g546p5rOV5pWw5o2uCiAgICAgIC8vIOeuoeeQhuWRmOWKn+iDveebuOWFswogICAgICBzZWxlY3RlZFVzZXJJZDogbnVsbCwgLy8g6YCJ5Lit55qE55So5oi3SUQKICAgICAgdXNlckxpc3Q6IFtdLCAvLyDnlKjmiLfliJfooagKICAgICAgbG9hZGluZzogZmFsc2UsIC8vIOWKoOi9veeKtuaAgQogICAgICAvLyDlvannp43nm7jlhbMKICAgICAgYWN0aXZlTG90dGVyeVR5cGU6ICdmdWNhaScsIC8vIOW9k+WJjemAieS4reeahOW9qeenje+8mmZ1Y2FpKOemj+W9qTNEKSDmiJYgdGljYWko5L2T5b2pUDMpCiAgICAgIC8vIOWIhuW9qeenjeeahOe7n+iuoeaVsOaNrgogICAgICBmdWNhaU1ldGhvZFJhbmtpbmc6IFtdLCAvLyDnpo/lvanmipXms6jmjpLooYwKICAgICAgZnVjYWlOdW1iZXJSYW5raW5nOiBbXSwgLy8g56aP5b2p54Ot6Zeo5LiJ5L2N5pWw5o6S6KGMCiAgICAgIHRpY2FpTWV0aG9kUmFua2luZzogW10sIC8vIOS9k+W9qeaKleazqOaOkuihjAogICAgICB0aWNhaU51bWJlclJhbmtpbmc6IFtdLCAvLyDkvZPlvanng63pl6jkuInkvY3mlbDmjpLooYwKICAgICAgLy8g5pCc57Si5Yqf6IO955u45YWzCiAgICAgIHNlYXJjaE51bWJlcjogJycsIC8vIOaQnOe0oueahOWPt+eggQogICAgICBpc1NlYXJjaE1vZGU6IGZhbHNlLCAvLyDmmK/lkKblpITkuo7mkJzntKLmqKHlvI8KICAgICAgc2VhcmNoTG9hZGluZzogZmFsc2UsIC8vIOaQnOe0ouWKoOi9veeKtuaAgQogICAgICBzZWFyY2hSZXN1bHRDb3VudDogMCwgLy8g5pCc57Si57uT5p6c5pWw6YePCiAgICAgIC8vIOWOn+Wni+WujOaVtOaVsOaNru+8iOeUqOS6juaQnOe0oui/h+a7pO+8iQogICAgICBvcmlnaW5hbEZ1Y2FpTWV0aG9kUmFua2luZzogW10sCiAgICAgIG9yaWdpbmFsRnVjYWlOdW1iZXJSYW5raW5nOiBbXSwKICAgICAgb3JpZ2luYWxUaWNhaU1ldGhvZFJhbmtpbmc6IFtdLAogICAgICBvcmlnaW5hbFRpY2FpTnVtYmVyUmFua2luZzogW10KICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICAvKiog5piv5ZCm5piv566h55CG5ZGYICovCiAgICBpc0FkbWluKCkgewogICAgICBjb25zdCB1c2VySW5mbyA9IHRoaXMuJHN0b3JlLmdldHRlcnMudXNlckluZm87CiAgICAgIGNvbnN0IHJvbGVzID0gdGhpcy4kc3RvcmUuZ2V0dGVycy5yb2xlczsKCiAgICAgIC8vIOajgOafpeaYr+WQpuaYr+i2hee6p+euoeeQhuWRmO+8iOeUqOaIt0lE5Li6Me+8iQogICAgICBpZiAodXNlckluZm8gJiYgdXNlckluZm8udXNlcklkID09PSAxKSB7CiAgICAgICAgcmV0dXJuIHRydWU7CiAgICAgIH0KCiAgICAgIC8vIOajgOafpeaYr+WQpuacieeuoeeQhuWRmOinkuiJsgogICAgICBpZiAocm9sZXMgJiYgcm9sZXMubGVuZ3RoID4gMCkgewogICAgICAgIHJldHVybiByb2xlcy5pbmNsdWRlcygnYWRtaW4nKSB8fCByb2xlcy5pbmNsdWRlcygnM2RfYWRtaW4nKTsKICAgICAgfQoKICAgICAgcmV0dXJuIGZhbHNlOwogICAgfSwKCiAgICAvKiog5b2T5YmN5b2p56eN55qE5oqV5rOo5o6S6KGM5pWw5o2uICovCiAgICBjdXJyZW50TWV0aG9kUmFua2luZygpIHsKICAgICAgcmV0dXJuIHRoaXMuYWN0aXZlTG90dGVyeVR5cGUgPT09ICdmdWNhaScgPyB0aGlzLmZ1Y2FpTWV0aG9kUmFua2luZyA6IHRoaXMudGljYWlNZXRob2RSYW5raW5nOwogICAgfSwKCiAgICAvKiog5b2T5YmN5b2p56eN55qE54Ot6Zeo5LiJ5L2N5pWw5o6S6KGM5pWw5o2uICovCiAgICBjdXJyZW50TnVtYmVyUmFua2luZygpIHsKICAgICAgcmV0dXJuIHRoaXMuYWN0aXZlTG90dGVyeVR5cGUgPT09ICdmdWNhaScgPyB0aGlzLmZ1Y2FpTnVtYmVyUmFua2luZyA6IHRoaXMudGljYWlOdW1iZXJSYW5raW5nOwogICAgfSwKCiAgICAvKiog5pCc57Si57uT5p6c5qCH6aKYICovCiAgICBzZWFyY2hSZXN1bHRUaXRsZSgpIHsKICAgICAgcmV0dXJuIGDmkJzntKLlj7fnoIEgIiR7dGhpcy5zZWFyY2hOdW1iZXJ9IiDnmoTnu5PmnpzvvJrlhbHmib7liLAgJHt0aGlzLnNlYXJjaFJlc3VsdENvdW50fSDkuKrljLnphY3poblgOwogICAgfQogIH0sCiAgd2F0Y2g6IHsKICAgIHZpc2libGUodmFsKSB7CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHZhbDsKICAgICAgaWYgKHZhbCkgewogICAgICAgIC8vIOi/kOihjOa1i+ivlQogICAgICAgIHRoaXMudGVzdFRocmVlRGlnaXRFeHRyYWN0aW9uKCk7CgogICAgICAgIC8vIOWmguaenOaYr+euoeeQhuWRmO+8jOWPquWKoOi9veeUqOaIt+WIl+ihqOWSjOeOqeazleaVsOaNru+8jOS4jeiHquWKqOiuoeeul+e7n+iuoQogICAgICAgIGlmICh0aGlzLmlzQWRtaW4pIHsKICAgICAgICAgIHRoaXMubG9hZFVzZXJMaXN0KCk7CiAgICAgICAgICB0aGlzLmxvYWRHYW1lTWV0aG9kcygpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAvLyDmma7pgJrnlKjmiLfoh6rliqjliqDovb3nu5/orqEKICAgICAgICAgIHRoaXMubG9hZEdhbWVNZXRob2RzKCkudGhlbihhc3luYyAoKSA9PiB7CiAgICAgICAgICAgIC8vIOaZrumAmueUqOaIt+S7jnNlc3Npb25TdG9yYWdl6I635Y+W6Ieq5bex55qE5pWw5o2uCiAgICAgICAgICAgIGNvbnN0IHN5c1VzZXJJZCA9IHNlc3Npb25TdG9yYWdlLmdldEl0ZW0oJ3N5c1VzZXJJZCcpOwogICAgICAgICAgICBjb25zb2xlLmxvZygnW0JldFN0YXRpc3RpY3NdIHNlc3Npb25TdG9yYWdl5Lit55qEc3lzVXNlcklkOicsIHN5c1VzZXJJZCk7CgogICAgICAgICAgICBpZiAoc3lzVXNlcklkKSB7CiAgICAgICAgICAgICAgdGhpcy5zZWxlY3RlZFVzZXJJZCA9IHBhcnNlSW50KHN5c1VzZXJJZCk7CiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ1tCZXRTdGF0aXN0aWNzXSDorr7nva5zZWxlY3RlZFVzZXJJZDonLCB0aGlzLnNlbGVjdGVkVXNlcklkKTsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICBjb25zb2xlLndhcm4oJ1tCZXRTdGF0aXN0aWNzXSDml6Dms5Xku45zZXNzaW9uU3RvcmFnZeiOt+WPlnN5c1VzZXJJZCcpOwogICAgICAgICAgICB9CiAgICAgICAgICAgIGF3YWl0IHRoaXMuY2FsY3VsYXRlU3RhdGlzdGljcygpOwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgZGlhbG9nVmlzaWJsZSh2YWwpIHsKICAgICAgdGhpcy4kZW1pdCgndXBkYXRlOnZpc2libGUnLCB2YWwpOwogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgLyoqIOWKoOi9veeOqeazleaVsOaNriAqLwogICAgYXN5bmMgbG9hZEdhbWVNZXRob2RzKCkgewogICAgICB0cnkgewogICAgICAgIC8vIOebtOaOpeS7jkFQSeiOt+WPlueOqeazleaVsOaNrgogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgcmVxdWVzdCh7CiAgICAgICAgICB1cmw6ICcvZ2FtZS9vZGRzL2xpc3QnLAogICAgICAgICAgbWV0aG9kOiAnZ2V0JywKICAgICAgICAgIHBhcmFtczogeyBwYWdlTnVtOiAxLCBwYWdlU2l6ZTogMTAwMCB9CiAgICAgICAgfSk7CgogICAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS5yb3dzKSB7CiAgICAgICAgICB0aGlzLmdhbWVNZXRob2RzRGF0YSA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgIAogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLmdhbWVNZXRob2RzRGF0YSA9IFtdOwogICAgICAgICAgY29uc29sZS53YXJuKCfnjqnms5XmlbDmja7kuLrnqbonKTsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W546p5rOV5pWw5o2u5aSx6LSlOicsIGVycm9yKTsKICAgICAgICAvLyDlpoLmnpxBUEnlpLHotKXvvIzkvb/nlKjln7rmnKznmoTnjqnms5XmmKDlsIQKICAgICAgICB0aGlzLmdhbWVNZXRob2RzRGF0YSA9IHRoaXMuZ2V0QmFzaWNNZXRob2RzRGF0YSgpOwogICAgICB9CiAgICB9LAogICAgLyoqIOiOt+WPluWfuuacrOeOqeazleaVsOaNru+8iEFQSeWksei0peaXtueahOWkh+eUqOaWueahiO+8iSAqLwogICAgZ2V0QmFzaWNNZXRob2RzRGF0YSgpIHsKICAgICAgcmV0dXJuIFsKICAgICAgICB7IG1ldGhvZElkOiAxLCBtZXRob2ROYW1lOiAn54us6IOGJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDIsIG1ldGhvZE5hbWU6ICfkuIDnoIHlrprkvY0nIH0sCiAgICAgICAgeyBtZXRob2RJZDogMywgbWV0aG9kTmFtZTogJ+S4pOeggee7hOWQiCcgfSwKICAgICAgICB7IG1ldGhvZElkOiA0LCBtZXRob2ROYW1lOiAn5Lik56CB5a+55a2QJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDUsIG1ldGhvZE5hbWU6ICfkuInnoIHnm7TpgIknIH0sCiAgICAgICAgeyBtZXRob2RJZDogNiwgbWV0aG9kTmFtZTogJ+S4ieeggee7hOmAiScgfSwKICAgICAgICB7IG1ldGhvZElkOiA3LCBtZXRob2ROYW1lOiAn5LiJ56CB57uE5LiJJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDgsIG1ldGhvZE5hbWU6ICflm5vnoIHnu4Tlha0nIH0sCiAgICAgICAgeyBtZXRob2RJZDogOSwgbWV0aG9kTmFtZTogJ+Wbm+eggee7hOS4iScgfSwKICAgICAgICB7IG1ldGhvZElkOiAxMCwgbWV0aG9kTmFtZTogJ+S6lOeggee7hOWFrScgfSwKICAgICAgICB7IG1ldGhvZElkOiAxMSwgbWV0aG9kTmFtZTogJ+S6lOeggee7hOS4iScgfSwKICAgICAgICB7IG1ldGhvZElkOiAxMiwgbWV0aG9kTmFtZTogJ+WFreeggee7hOWFrScgfSwKICAgICAgICB7IG1ldGhvZElkOiAxMywgbWV0aG9kTmFtZTogJ+WFreeggee7hOS4iScgfSwKICAgICAgICB7IG1ldGhvZElkOiAxNCwgbWV0aG9kTmFtZTogJ+S4g+eggee7hOWFrScgfSwKICAgICAgICB7IG1ldGhvZElkOiAxNSwgbWV0aG9kTmFtZTogJ+S4g+eggee7hOS4iScgfSwKICAgICAgICB7IG1ldGhvZElkOiAxNiwgbWV0aG9kTmFtZTogJ+WFq+eggee7hOWFrScgfSwKICAgICAgICB7IG1ldGhvZElkOiAxNywgbWV0aG9kTmFtZTogJ+WFq+eggee7hOS4iScgfSwKICAgICAgICB7IG1ldGhvZElkOiAxOCwgbWV0aG9kTmFtZTogJ+S5neeggee7hOWFrScgfSwKICAgICAgICB7IG1ldGhvZElkOiAxOSwgbWV0aG9kTmFtZTogJ+S5neeggee7hOS4iScgfSwKICAgICAgICB7IG1ldGhvZElkOiAyMCwgbWV0aG9kTmFtZTogJ+WMheaJk+e7hOWFrScgfSwKICAgICAgICB7IG1ldGhvZElkOiAyMSwgbWV0aG9kTmFtZTogJzfmiJYyMOWSjOWAvCcgfSwKICAgICAgICB7IG1ldGhvZElkOiAyMiwgbWV0aG9kTmFtZTogJzjmiJYxOeWSjOWAvCcgfSwKICAgICAgICB7IG1ldGhvZElkOiAyMywgbWV0aG9kTmFtZTogJznmiJYxOOWSjOWAvCcgfSwKICAgICAgICB7IG1ldGhvZElkOiAyNCwgbWV0aG9kTmFtZTogJzEw5oiWMTflkozlgLwnIH0sCiAgICAgICAgeyBtZXRob2RJZDogMjUsIG1ldGhvZE5hbWU6ICcxMeaIljE25ZKM5YC8JyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDI2LCBtZXRob2ROYW1lOiAnMTLmiJYxNeWSjOWAvCcgfSwKICAgICAgICB7IG1ldGhvZElkOiAyNywgbWV0aG9kTmFtZTogJzEz5oiWMTTlkozlgLwnIH0sCiAgICAgICAgeyBtZXRob2RJZDogMjgsIG1ldGhvZE5hbWU6ICfnm7TpgInlpI3lvI8nIH0sCiAgICAgICAgeyBtZXRob2RJZDogMjksIG1ldGhvZE5hbWU6ICflkozlgLzljZXlj4wnIH0sCiAgICAgICAgeyBtZXRob2RJZDogMzAsIG1ldGhvZE5hbWU6ICflkozlgLzlpKflsI8nIH0sCiAgICAgICAgeyBtZXRob2RJZDogMzEsIG1ldGhvZE5hbWU6ICfljIXmiZPnu4TkuIknIH0sCiAgICAgICAgeyBtZXRob2RJZDogMTAwLCBtZXRob2ROYW1lOiAn5LiJ56CB6Ziy5a+5JyB9CiAgICAgIF07CiAgICB9LAogICAgLyoqIOiOt+WPlueOqeazleWQjeensCAqLwogICAgZ2V0TWV0aG9kTmFtZShtZXRob2RJZCkgewogICAgICBjb25zdCBtZXRob2QgPSB0aGlzLmdhbWVNZXRob2RzRGF0YS5maW5kKG0gPT4gbS5tZXRob2RJZCA9PT0gbWV0aG9kSWQpOwogICAgICByZXR1cm4gbWV0aG9kID8gbWV0aG9kLm1ldGhvZE5hbWUgOiBg546p5rOVJHttZXRob2RJZH1gOwogICAgfSwKICAgIC8qKiDojrflj5bmjpLlkI3moLflvI/nsbsgKi8KICAgIGdldFJhbmtDbGFzcyhyYW5rKSB7CiAgICAgIGlmIChyYW5rID09PSAxKSByZXR1cm4gJ3JhbmstZmlyc3QnOwogICAgICBpZiAocmFuayA9PT0gMikgcmV0dXJuICdyYW5rLXNlY29uZCc7CiAgICAgIGlmIChyYW5rID09PSAzKSByZXR1cm4gJ3JhbmstdGhpcmQnOwogICAgICByZXR1cm4gJ3Jhbmstb3RoZXInOwogICAgfSwKICAgIC8qKiDorqHnrpfnu5/orqHmlbDmja4gKi8KICAgIGFzeW5jIGNhbGN1bGF0ZVN0YXRpc3RpY3MoKSB7CiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCB0aGlzLmdldFJlY29yZERhdGEoKTsKCgogICAgICBpZiAoIWRhdGEgfHwgZGF0YS5sZW5ndGggPT09IDApIHsKICAgICAgICBpZiAodGhpcy5pc0FkbWluICYmICF0aGlzLnNlbGVjdGVkVXNlcklkKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+mAieaLqeimgeafpeeci+e7n+iuoeeahOeUqOaItycpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+aaguaXoOe7n+iuoeaVsOaNru+8jOivt+WFiOWIt+aWsHJlY29yZOmhtemdoicpOwogICAgICAgIH0KICAgICAgICB0aGlzLmNsZWFyQWxsUmFua2luZ0RhdGEoKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIC8vIOaMieW9qeenjeWIhuWIq+iuoeeul+e7n+iuoeaVsOaNrgogICAgICB0aGlzLmNhbGN1bGF0ZVN0YXRpc3RpY3NCeUxvdHRlcnlUeXBlKGRhdGEpOwoKICAgICAgLy8g6LCD6K+V5L+h5oGvCiAgICAgIGNvbnNvbGUubG9nKCfnu5/orqHorqHnrpflrozmiJA6JywgewogICAgICAgIOemj+W9qeaKleazqOaOkuihjDogdGhpcy5mdWNhaU1ldGhvZFJhbmtpbmcubGVuZ3RoLAogICAgICAgIOemj+W9qeeDremXqOS4ieS9jeaVsDogdGhpcy5mdWNhaU51bWJlclJhbmtpbmcubGVuZ3RoLAogICAgICAgIOS9k+W9qeaKleazqOaOkuihjDogdGhpcy50aWNhaU1ldGhvZFJhbmtpbmcubGVuZ3RoLAogICAgICAgIOS9k+W9qeeDremXqOS4ieS9jeaVsDogdGhpcy50aWNhaU51bWJlclJhbmtpbmcubGVuZ3RoLAogICAgICAgIOW9k+WJjemAieS4reW9qeenjTogdGhpcy5hY3RpdmVMb3R0ZXJ5VHlwZQogICAgICB9KTsKICAgIH0sCiAgICAvKiog6I635Y+WcmVjb3Jk5pWw5o2uICovCiAgICBhc3luYyBnZXRSZWNvcmREYXRhKCkgewogICAgICB0cnkgewogICAgICAgIC8vIOWmguaenOaciemAieaLqeeahOeUqOaIt0lE77yI566h55CG5ZGY6YCJ5oup55qE55So5oi35oiW5pmu6YCa55So5oi36Ieq5bex77yJ77yM55u05o6l5LuOQVBJ6I635Y+W5pWw5o2uCiAgICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRVc2VySWQpIHsKICAgICAgICAgIHJldHVybiBhd2FpdCB0aGlzLmZldGNoVXNlclJlY29yZERhdGEodGhpcy5zZWxlY3RlZFVzZXJJZCk7CiAgICAgICAgfQoKICAgICAgICAvLyDlpoLmnpzmmK/nrqHnkIblkZjkvYbmsqHmnInpgInmi6nnlKjmiLfvvIzmj5DnpLrpgInmi6nnlKjmiLcKICAgICAgICBpZiAodGhpcy5pc0FkbWluKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+mAieaLqeimgeafpeeci+e7n+iuoeeahOeUqOaItycpOwogICAgICAgICAgcmV0dXJuIFtdOwogICAgICAgIH0KCiAgICAgICAgLy8g5pmu6YCa55So5oi377ya55u05o6l5LuOQVBJ6I635Y+W5b2T5YmN55So5oi35pWw5o2u77yM5LuOc2Vzc2lvblN0b3JhZ2Xojrflj5bnlKjmiLdJRAogICAgICAgIGNvbnNvbGUubG9nKCdbQmV0U3RhdGlzdGljc10g5pmu6YCa55So5oi377yM55u05o6l5LuOQVBJ6I635Y+W5pWw5o2uJyk7CgogICAgICAgIC8vIOS7jnNlc3Npb25TdG9yYWdl6I635Y+W55So5oi3SUQKICAgICAgICBjb25zdCBzeXNVc2VySWQgPSBzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKCdzeXNVc2VySWQnKTsKICAgICAgICBjb25zb2xlLmxvZygnW0JldFN0YXRpc3RpY3NdIOS7jnNlc3Npb25TdG9yYWdl6I635Y+W55qEc3lzVXNlcklkOicsIHN5c1VzZXJJZCk7CgogICAgICAgIGlmIChzeXNVc2VySWQpIHsKICAgICAgICAgIGNvbnNvbGUubG9nKCdbQmV0U3RhdGlzdGljc10g5L2/55Soc3lzVXNlcklk6LCD55SoQVBJOicsIHN5c1VzZXJJZCk7CiAgICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgdGhpcy5mZXRjaFVzZXJSZWNvcmREYXRhKHBhcnNlSW50KHN5c1VzZXJJZCkpOwogICAgICAgICAgY29uc29sZS5sb2coJ1tCZXRTdGF0aXN0aWNzXSBBUEnojrflj5bliLDnmoTmlbDmja46JywgZGF0YSk7CgogICAgICAgICAgaWYgKGRhdGEgJiYgZGF0YS5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgIGNvbnNvbGUubG9nKGBbQmV0U3RhdGlzdGljc10g5oiQ5Yqf5Yqg6L295LqGICR7ZGF0YS5sZW5ndGh9IOadoee7n+iuoeaVsOaNrmApOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgY29uc29sZS53YXJuKCdbQmV0U3RhdGlzdGljc10gQVBJ6L+U5Zue56m65pWw5o2uJyk7CiAgICAgICAgICB9CgogICAgICAgICAgcmV0dXJuIGRhdGE7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIGNvbnNvbGUud2FybignW0JldFN0YXRpc3RpY3NdIOaXoOazleS7jnNlc3Npb25TdG9yYWdl6I635Y+Wc3lzVXNlcklkJyk7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+aXoOazleiOt+WPlueUqOaIt+S/oeaBr++8jOivt+mHjeaWsOeZu+W9lScpOwogICAgICAgICAgcmV0dXJuIFtdOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCfop6PmnpDnu5/orqHmlbDmja7lpLHotKU6JywgZXJyb3IpOwogICAgICAgIHJldHVybiBbXTsKICAgICAgfQogICAgfSwKICAgIC8qKiDmuIXnqbrmiYDmnInmjpLooYzmlbDmja4gKi8KICAgIGNsZWFyQWxsUmFua2luZ0RhdGEoKSB7CiAgICAgIHRoaXMuZnVjYWlNZXRob2RSYW5raW5nID0gW107CiAgICAgIHRoaXMuZnVjYWlOdW1iZXJSYW5raW5nID0gW107CiAgICAgIHRoaXMudGljYWlNZXRob2RSYW5raW5nID0gW107CiAgICAgIHRoaXMudGljYWlOdW1iZXJSYW5raW5nID0gW107CiAgICAgIC8vIOa4heepuuWOn+Wni+aVsOaNrgogICAgICB0aGlzLm9yaWdpbmFsRnVjYWlNZXRob2RSYW5raW5nID0gW107CiAgICAgIHRoaXMub3JpZ2luYWxGdWNhaU51bWJlclJhbmtpbmcgPSBbXTsKICAgICAgdGhpcy5vcmlnaW5hbFRpY2FpTWV0aG9kUmFua2luZyA9IFtdOwogICAgICB0aGlzLm9yaWdpbmFsVGljYWlOdW1iZXJSYW5raW5nID0gW107CiAgICAgIC8vIOmHjee9ruaQnOe0oueKtuaAgQogICAgICB0aGlzLmNsZWFyU2VhcmNoKCk7CiAgICB9LAoKICAgIC8qKiDmjInlvannp43liIbliKvorqHnrpfnu5/orqHmlbDmja4gKi8KICAgIGNhbGN1bGF0ZVN0YXRpc3RpY3NCeUxvdHRlcnlUeXBlKGRhdGEpIHsKICAgICAgLy8g6LCD6K+V77ya5p+l55yL5pWw5o2u57uT5p6ECiAgICAgIGlmIChkYXRhLmxlbmd0aCA+IDApIHsKICAgICAgICBjb25zb2xlLmxvZygn5pWw5o2u5qC35pysOicsIGRhdGFbMF0pOwogICAgICAgIGNvbnNvbGUubG9nKCflj6/og73nmoTlvannp43lrZfmrrU6JywgewogICAgICAgICAgbG90dGVyeVR5cGU6IGRhdGFbMF0ubG90dGVyeVR5cGUsCiAgICAgICAgICBnYW1lVHlwZTogZGF0YVswXS5nYW1lVHlwZSwKICAgICAgICAgIGxvdHRlcnlJZDogZGF0YVswXS5sb3R0ZXJ5SWQsCiAgICAgICAgICBnYW1lTmFtZTogZGF0YVswXS5nYW1lTmFtZSwKICAgICAgICAgIG1ldGhvZE5hbWU6IGRhdGFbMF0ubWV0aG9kTmFtZQogICAgICAgIH0pOwoKICAgICAgICAvLyDnu5/orqHmiYDmnInlj6/og73nmoTlvannp43moIfor4YKICAgICAgICBjb25zdCBsb3R0ZXJ5VHlwZXMgPSBbLi4ubmV3IFNldChkYXRhLm1hcChyZWNvcmQgPT4gcmVjb3JkLmxvdHRlcnlUeXBlKSldOwogICAgICAgIGNvbnN0IGdhbWVUeXBlcyA9IFsuLi5uZXcgU2V0KGRhdGEubWFwKHJlY29yZCA9PiByZWNvcmQuZ2FtZVR5cGUpKV07CiAgICAgICAgY29uc3QgbG90dGVyeUlkcyA9IFsuLi5uZXcgU2V0KGRhdGEubWFwKHJlY29yZCA9PiByZWNvcmQubG90dGVyeUlkKSldOwogICAgICAgIGNvbnN0IGdhbWVOYW1lcyA9IFsuLi5uZXcgU2V0KGRhdGEubWFwKHJlY29yZCA9PiByZWNvcmQuZ2FtZU5hbWUpKV07CgogICAgICAgIGNvbnNvbGUubG9nKCfmlbDmja7kuK3nmoTlvannp43moIfor4Y6JywgewogICAgICAgICAgbG90dGVyeVR5cGVzLAogICAgICAgICAgZ2FtZVR5cGVzLAogICAgICAgICAgbG90dGVyeUlkcywKICAgICAgICAgIGdhbWVOYW1lcwogICAgICAgIH0pOwogICAgICB9CgogICAgICAvLyDmjInlvannp43liIbnu4TmlbDmja7vvIjkvb/nlKhsb3R0ZXJ5SWTlrZfmrrXvvIkKICAgICAgY29uc3QgZnVjYWlEYXRhID0gZGF0YS5maWx0ZXIocmVjb3JkID0+IHRoaXMuaXNGdWNhaUxvdHRlcnkocmVjb3JkLmxvdHRlcnlJZCkpOwogICAgICBjb25zdCB0aWNhaURhdGEgPSBkYXRhLmZpbHRlcihyZWNvcmQgPT4gdGhpcy5pc1RpY2FpTG90dGVyeShyZWNvcmQubG90dGVyeUlkKSk7CgogICAgICBjb25zb2xlLmxvZyhg56aP5b2pM0TmlbDmja46ICR7ZnVjYWlEYXRhLmxlbmd0aH3mnaEsIOS9k+W9qVAz5pWw5o2uOiAke3RpY2FpRGF0YS5sZW5ndGh95p2hYCk7CgogICAgICAvLyDlpoLmnpzmsqHmnInmmI7noa7nmoTlvannp43ljLrliIbvvIzlsIbmiYDmnInmlbDmja7lvZLnsbvkuLrnpo/lvakzRO+8iOWFvOWuueaAp+WkhOeQhu+8iQogICAgICBpZiAoZnVjYWlEYXRhLmxlbmd0aCA9PT0gMCAmJiB0aWNhaURhdGEubGVuZ3RoID09PSAwICYmIGRhdGEubGVuZ3RoID4gMCkgewogICAgICAgIGNvbnNvbGUubG9nKCfmnKrmo4DmtYvliLDlvannp43lrZfmrrXvvIzlsIbmiYDmnInmlbDmja7lvZLnsbvkuLrnpo/lvakzRCcpOwogICAgICAgIHRoaXMuZnVjYWlNZXRob2RSYW5raW5nID0gdGhpcy5jYWxjdWxhdGVNZXRob2RSYW5raW5nQnlHcm91cChkYXRhKTsKICAgICAgICB0aGlzLmZ1Y2FpTnVtYmVyUmFua2luZyA9IHRoaXMuY2FsY3VsYXRlTnVtYmVyUmFua2luZyhkYXRhKTsKICAgICAgICB0aGlzLnRpY2FpTWV0aG9kUmFua2luZyA9IFtdOwogICAgICAgIHRoaXMudGljYWlOdW1iZXJSYW5raW5nID0gW107CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICAvLyDliIbliKvorqHnrpfnpo/lvanlkozkvZPlvannmoTnu5/orqHmlbDmja4KICAgICAgaWYgKGZ1Y2FpRGF0YS5sZW5ndGggPiAwKSB7CiAgICAgICAgdGhpcy5vcmlnaW5hbEZ1Y2FpTWV0aG9kUmFua2luZyA9IHRoaXMuY2FsY3VsYXRlTWV0aG9kUmFua2luZ0J5R3JvdXAoZnVjYWlEYXRhKTsKICAgICAgICB0aGlzLm9yaWdpbmFsRnVjYWlOdW1iZXJSYW5raW5nID0gdGhpcy5jYWxjdWxhdGVOdW1iZXJSYW5raW5nKGZ1Y2FpRGF0YSk7CiAgICAgICAgdGhpcy5mdWNhaU1ldGhvZFJhbmtpbmcgPSBbLi4udGhpcy5vcmlnaW5hbEZ1Y2FpTWV0aG9kUmFua2luZ107CiAgICAgICAgdGhpcy5mdWNhaU51bWJlclJhbmtpbmcgPSBbLi4udGhpcy5vcmlnaW5hbEZ1Y2FpTnVtYmVyUmFua2luZ107CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5vcmlnaW5hbEZ1Y2FpTWV0aG9kUmFua2luZyA9IFtdOwogICAgICAgIHRoaXMub3JpZ2luYWxGdWNhaU51bWJlclJhbmtpbmcgPSBbXTsKICAgICAgICB0aGlzLmZ1Y2FpTWV0aG9kUmFua2luZyA9IFtdOwogICAgICAgIHRoaXMuZnVjYWlOdW1iZXJSYW5raW5nID0gW107CiAgICAgIH0KCiAgICAgIGlmICh0aWNhaURhdGEubGVuZ3RoID4gMCkgewogICAgICAgIHRoaXMub3JpZ2luYWxUaWNhaU1ldGhvZFJhbmtpbmcgPSB0aGlzLmNhbGN1bGF0ZU1ldGhvZFJhbmtpbmdCeUdyb3VwKHRpY2FpRGF0YSk7CiAgICAgICAgdGhpcy5vcmlnaW5hbFRpY2FpTnVtYmVyUmFua2luZyA9IHRoaXMuY2FsY3VsYXRlTnVtYmVyUmFua2luZyh0aWNhaURhdGEpOwogICAgICAgIHRoaXMudGljYWlNZXRob2RSYW5raW5nID0gWy4uLnRoaXMub3JpZ2luYWxUaWNhaU1ldGhvZFJhbmtpbmddOwogICAgICAgIHRoaXMudGljYWlOdW1iZXJSYW5raW5nID0gWy4uLnRoaXMub3JpZ2luYWxUaWNhaU51bWJlclJhbmtpbmddOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMub3JpZ2luYWxUaWNhaU1ldGhvZFJhbmtpbmcgPSBbXTsKICAgICAgICB0aGlzLm9yaWdpbmFsVGljYWlOdW1iZXJSYW5raW5nID0gW107CiAgICAgICAgdGhpcy50aWNhaU1ldGhvZFJhbmtpbmcgPSBbXTsKICAgICAgICB0aGlzLnRpY2FpTnVtYmVyUmFua2luZyA9IFtdOwogICAgICB9CiAgICB9LAoKICAgIC8qKiDliKTmlq3mmK/lkKbkuLrnpo/lvakzRCAqLwogICAgaXNGdWNhaUxvdHRlcnkobG90dGVyeUlkKSB7CiAgICAgIC8vIOemj+W9qTNE55qEbG90dGVyeUlk5pivMQogICAgICByZXR1cm4gbG90dGVyeUlkID09PSAxIHx8IGxvdHRlcnlJZCA9PT0gJzEnOwogICAgfSwKCiAgICAvKiog5Yik5pat5piv5ZCm5Li65L2T5b2pUDMgKi8KICAgIGlzVGljYWlMb3R0ZXJ5KGxvdHRlcnlJZCkgewogICAgICAvLyDkvZPlvalQM+eahGxvdHRlcnlJZOaYrzIKICAgICAgcmV0dXJuIGxvdHRlcnlJZCA9PT0gMiB8fCBsb3R0ZXJ5SWQgPT09ICcyJzsKICAgIH0sCgogICAgLyoqIOaMieeOqeazleWIhue7hOiuoeeul+aOkuihjOamnCAqLwogICAgY2FsY3VsYXRlTWV0aG9kUmFua2luZ0J5R3JvdXAoZGF0YSkgewogICAgICBjb25zb2xlLmxvZygnW2NhbGN1bGF0ZU1ldGhvZFJhbmtpbmdCeUdyb3VwXSDlvIDlp4vorqHnrpflkITnjqnms5XmipXms6jmjpLooYzmppwnKTsKCiAgICAgIC8vIOaMieeOqeazleWIhue7hO+8jOebuOWQjOWPt+eggeWQiOW5tue0r+iuoemHkeminQogICAgICBjb25zdCBtZXRob2RHcm91cHMgPSB7fTsKCiAgICAgIGRhdGEuZm9yRWFjaChyZWNvcmQgPT4gewogICAgICAgIGNvbnN0IG1ldGhvZElkID0gcmVjb3JkLm1ldGhvZElkOwogICAgICAgIGNvbnN0IGFtb3VudCA9IHBhcnNlRmxvYXQocmVjb3JkLm1vbmV5KSB8fCAwOwoKICAgICAgICBpZiAoIW1ldGhvZEdyb3Vwc1ttZXRob2RJZF0pIHsKICAgICAgICAgIG1ldGhvZEdyb3Vwc1ttZXRob2RJZF0gPSBuZXcgTWFwKCk7IC8vIOS9v+eUqE1hcOadpeWtmOWCqOWPt+eggeWSjOWvueW6lOeahOe0r+iuoeaVsOaNrgogICAgICAgIH0KCiAgICAgICAgLy8g6Kej5p6Q5oqV5rOo5Y+356CBCiAgICAgICAgdHJ5IHsKICAgICAgICAgIGNvbnN0IGJldE51bWJlcnMgPSBKU09OLnBhcnNlKHJlY29yZC5iZXROdW1iZXJzIHx8ICd7fScpOwogICAgICAgICAgY29uc3QgbnVtYmVycyA9IGJldE51bWJlcnMubnVtYmVycyB8fCBbXTsKCiAgICAgICAgICAvLyDkuLrmr4/kuKrlj7fnoIHntK/orqHph5Hpop0KICAgICAgICAgIG51bWJlcnMuZm9yRWFjaCgobnVtYmVyT2JqKSA9PiB7CiAgICAgICAgICAgIGxldCBzaW5nbGVOdW1iZXJEaXNwbGF5ID0gJyc7CgogICAgICAgICAgICBpZiAodHlwZW9mIG51bWJlck9iaiA9PT0gJ29iamVjdCcgJiYgbnVtYmVyT2JqICE9PSBudWxsKSB7CiAgICAgICAgICAgICAgLy8g5aSE55CGIHthOjEsIGI6MiwgYzozLCBkOjQsIGU6NX0g5qC85byPCiAgICAgICAgICAgICAgY29uc3Qga2V5cyA9IE9iamVjdC5rZXlzKG51bWJlck9iaikuc29ydCgpOwogICAgICAgICAgICAgIGNvbnN0IHZhbHVlcyA9IGtleXMubWFwKGtleSA9PiBTdHJpbmcobnVtYmVyT2JqW2tleV0pKTsKICAgICAgICAgICAgICBzaW5nbGVOdW1iZXJEaXNwbGF5ID0gdmFsdWVzLmpvaW4oJycpOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIHNpbmdsZU51bWJlckRpc3BsYXkgPSBTdHJpbmcobnVtYmVyT2JqKTsKICAgICAgICAgICAgfQoKICAgICAgICAgICAgLy8g5aaC5p6c5Y+356CB5bey5a2Y5Zyo77yM57Sv6K6h6YeR6aKd5ZKM6K6w5b2V5pWwCiAgICAgICAgICAgIGlmIChtZXRob2RHcm91cHNbbWV0aG9kSWRdLmhhcyhzaW5nbGVOdW1iZXJEaXNwbGF5KSkgewogICAgICAgICAgICAgIGNvbnN0IGV4aXN0aW5nID0gbWV0aG9kR3JvdXBzW21ldGhvZElkXS5nZXQoc2luZ2xlTnVtYmVyRGlzcGxheSk7CiAgICAgICAgICAgICAgZXhpc3RpbmcudG90YWxBbW91bnQgKz0gYW1vdW50OwogICAgICAgICAgICAgIGV4aXN0aW5nLnJlY29yZENvdW50ICs9IDE7CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgLy8g5paw5Y+356CB77yM5Yib5bu66K6w5b2VCiAgICAgICAgICAgICAgbWV0aG9kR3JvdXBzW21ldGhvZElkXS5zZXQoc2luZ2xlTnVtYmVyRGlzcGxheSwgewogICAgICAgICAgICAgICAgYmV0TnVtYmVyczogc2luZ2xlTnVtYmVyRGlzcGxheSwKICAgICAgICAgICAgICAgIHRvdGFsQW1vdW50OiBhbW91bnQsCiAgICAgICAgICAgICAgICByZWNvcmRDb3VudDogMQogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgICAgY29uc29sZS53YXJuKCfop6PmnpDmipXms6jlj7fnoIHlpLHotKU6JywgcmVjb3JkLmJldE51bWJlcnMsIGVycm9yKTsKICAgICAgICAgIC8vIOWmguaenOino+aekOWksei0pe+8jOS9v+eUqOWOn+Wni+aVsOaNrgogICAgICAgICAgY29uc3QgZmFsbGJhY2tOdW1iZXJzID0gcmVjb3JkLmJldE51bWJlcnMgfHwgJ+acquefpeWPt+eggSc7CgogICAgICAgICAgaWYgKG1ldGhvZEdyb3Vwc1ttZXRob2RJZF0uaGFzKGZhbGxiYWNrTnVtYmVycykpIHsKICAgICAgICAgICAgY29uc3QgZXhpc3RpbmcgPSBtZXRob2RHcm91cHNbbWV0aG9kSWRdLmdldChmYWxsYmFja051bWJlcnMpOwogICAgICAgICAgICBleGlzdGluZy50b3RhbEFtb3VudCArPSBhbW91bnQ7CiAgICAgICAgICAgIGV4aXN0aW5nLnJlY29yZENvdW50ICs9IDE7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBtZXRob2RHcm91cHNbbWV0aG9kSWRdLnNldChmYWxsYmFja051bWJlcnMsIHsKICAgICAgICAgICAgICBiZXROdW1iZXJzOiBmYWxsYmFja051bWJlcnMsCiAgICAgICAgICAgICAgdG90YWxBbW91bnQ6IGFtb3VudCwKICAgICAgICAgICAgICByZWNvcmRDb3VudDogMQogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pOwoKICAgICAgY29uc29sZS5sb2coJ1tjYWxjdWxhdGVNZXRob2RSYW5raW5nQnlHcm91cF0g5byA5aeL55Sf5oiQ5o6S6KGM5qacJyk7CgogICAgICAvLyDkuLrmr4/kuKrnjqnms5XnlJ/miJDmjpLooYzmppzlubbov5Tlm54KICAgICAgcmV0dXJuIE9iamVjdC5lbnRyaWVzKG1ldGhvZEdyb3VwcykubWFwKChbbWV0aG9kSWQsIG51bWJlcnNNYXBdKSA9PiB7CiAgICAgICAgLy8g5bCGTWFw6L2s5o2i5Li65pWw57uE5bm25oyJ5oC76YeR6aKd6ZmN5bqP5o6S5bqPCiAgICAgICAgY29uc3QgcmFua2luZyA9IEFycmF5LmZyb20obnVtYmVyc01hcC52YWx1ZXMoKSkKICAgICAgICAgIC5zb3J0KChhLCBiKSA9PiBiLnRvdGFsQW1vdW50IC0gYS50b3RhbEFtb3VudCkKICAgICAgICAgIC5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoewogICAgICAgICAgICByYW5rOiBpbmRleCArIDEsCiAgICAgICAgICAgIGJldE51bWJlcnM6IGl0ZW0uYmV0TnVtYmVycywKICAgICAgICAgICAgdG90YWxBbW91bnQ6IGl0ZW0udG90YWxBbW91bnQsCiAgICAgICAgICAgIHJlY29yZENvdW50OiBpdGVtLnJlY29yZENvdW50CiAgICAgICAgICB9KSk7CgogICAgICAgIGNvbnNvbGUubG9nKGBbY2FsY3VsYXRlTWV0aG9kUmFua2luZ0J5R3JvdXBdIOeOqeazlSR7bWV0aG9kSWR9KCR7dGhpcy5nZXRNZXRob2ROYW1lKHBhcnNlSW50KG1ldGhvZElkKSl9Ke+8miR7cmFua2luZy5sZW5ndGh95Liq5LiN5ZCM5Y+356CBYCk7CgogICAgICAgIHJldHVybiB7CiAgICAgICAgICBtZXRob2RJZDogcGFyc2VJbnQobWV0aG9kSWQpLAogICAgICAgICAgbWV0aG9kTmFtZTogdGhpcy5nZXRNZXRob2ROYW1lKHBhcnNlSW50KG1ldGhvZElkKSksCiAgICAgICAgICByYW5raW5nCiAgICAgICAgfTsKICAgICAgfSkuZmlsdGVyKGdyb3VwID0+IGdyb3VwLnJhbmtpbmcubGVuZ3RoID4gMCk7IC8vIOWPquaYvuekuuacieaVsOaNrueahOeOqeazlQogICAgfSwKICAgIC8qKiDmoLzlvI/ljJbmipXms6jlj7fnoIHnlKjkuo7mmL7npLogKi8KICAgIGZvcm1hdEJldE51bWJlcnNGb3JEaXNwbGF5KGJldE51bWJlcnNLZXkpIHsKICAgICAgdHJ5IHsKICAgICAgICBjb25zdCBudW1iZXJzID0gSlNPTi5wYXJzZShiZXROdW1iZXJzS2V5KTsKICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShudW1iZXJzKSAmJiBudW1iZXJzLmxlbmd0aCA+IDApIHsKICAgICAgICAgIHJldHVybiBudW1iZXJzLm1hcChudW0gPT4gewogICAgICAgICAgICBpZiAodHlwZW9mIG51bSA9PT0gJ29iamVjdCcgJiYgbnVtICE9PSBudWxsKSB7CiAgICAgICAgICAgICAgLy8g5aSE55CGIHthOjEsIGI6MiwgYzozLCBkOjQsIGU6NX0g5qC85byPCiAgICAgICAgICAgICAgY29uc3Qga2V5cyA9IE9iamVjdC5rZXlzKG51bSkuc29ydCgpOwogICAgICAgICAgICAgIGNvbnN0IHZhbHVlcyA9IGtleXMubWFwKGtleSA9PiBTdHJpbmcobnVtW2tleV0pKTsKICAgICAgICAgICAgICByZXR1cm4gdmFsdWVzLmpvaW4oJycpOwogICAgICAgICAgICB9CiAgICAgICAgICAgIHJldHVybiBTdHJpbmcobnVtKTsKICAgICAgICAgIH0pLmpvaW4oJywgJyk7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIC8vIOWmguaenOino+aekOWksei0pe+8jOebtOaOpei/lOWbnuWOn+Wni+Wtl+espuS4sgogICAgICB9CiAgICAgIHJldHVybiBiZXROdW1iZXJzS2V5Lmxlbmd0aCA+IDIwID8gYmV0TnVtYmVyc0tleS5zdWJzdHJpbmcoMCwgMjApICsgJy4uLicgOiBiZXROdW1iZXJzS2V5OwogICAgfSwKICAgIC8qKiDmjInnjqnms5XliIbnu4TorqHnrpflj7fnoIHmjpLooYwgLSDlj6rkvb/nlKhtb25leeWtl+autSAqLwogICAgY2FsY3VsYXRlTnVtYmVyUmFua2luZyhkYXRhKSB7CiAgICAgIC8vIOWIneWni+WMlui/lOWbnuaVsOe7hAogICAgICBjb25zdCBudW1iZXJSYW5raW5nQnlHcm91cCA9IFtdOwoKICAgICAgLy8g5oyJ546p5rOV5YiG57uE57uf6K6hCiAgICAgIGNvbnN0IG1ldGhvZEdyb3VwcyA9IHt9OwoKICAgICAgZGF0YS5mb3JFYWNoKHJlY29yZCA9PiB7CiAgICAgICAgdHJ5IHsKICAgICAgICAgIGNvbnN0IG1ldGhvZElkID0gcmVjb3JkLm1ldGhvZElkOwogICAgICAgICAgY29uc3QgbW9uZXkgPSBwYXJzZUZsb2F0KHJlY29yZC5tb25leSB8fCAwKTsKICAgICAgICAgIGNvbnN0IGJldE51bWJlcnMgPSBKU09OLnBhcnNlKHJlY29yZC5iZXROdW1iZXJzIHx8ICd7fScpOwogICAgICAgICAgY29uc3QgbnVtYmVycyA9IGJldE51bWJlcnMubnVtYmVycyB8fCBbXTsKCiAgICAgICAgICAvLyDliJ3lp4vljJbnjqnms5XliIbnu4QKICAgICAgICAgIGlmICghbWV0aG9kR3JvdXBzW21ldGhvZElkXSkgewogICAgICAgICAgICBtZXRob2RHcm91cHNbbWV0aG9kSWRdID0gewogICAgICAgICAgICAgIG1ldGhvZFRvdGFsOiAwLAogICAgICAgICAgICAgIHRocmVlRGlnaXRNYXA6IG5ldyBNYXAoKQogICAgICAgICAgICB9OwogICAgICAgICAgfQoKICAgICAgICAgIG1ldGhvZEdyb3Vwc1ttZXRob2RJZF0ubWV0aG9kVG90YWwgKz0gbW9uZXk7CgogICAgICAgICAgLy8g5aSE55CG5q+P5Liq5oqV5rOo5Y+356CBCiAgICAgICAgICBudW1iZXJzLmZvckVhY2gobnVtYmVyT2JqID0+IHsKICAgICAgICAgICAgY29uc3QgdGhyZWVEaWdpdHMgPSB0aGlzLmV4dHJhY3RUaHJlZURpZ2l0TnVtYmVycyhudW1iZXJPYmopOwoKICAgICAgICAgICAgLy8g5a+55b2T5YmN5Y+356CB55qE5LiJ5L2N5pWw57uE5ZCI5Y676YeN77yI6YG/5YWN5ZCM5LiA5Y+356CB5YaF6YeN5aSN57uE5ZCI5aSa5qyh57Sv6K6h77yJCiAgICAgICAgICAgIGNvbnN0IHVuaXF1ZU5vcm1hbGl6ZWREaWdpdHMgPSBuZXcgU2V0KCk7CgogICAgICAgICAgICB0aHJlZURpZ2l0cy5mb3JFYWNoKGRpZ2l0ID0+IHsKICAgICAgICAgICAgICAvLyDlvZLkuIDljJbkuInkvY3mlbDvvJrlsIbmlbDlrZfmjInku47lsI/liLDlpKfmjpLluo/kvZzkuLrnu5/kuIBrZXkKICAgICAgICAgICAgICBjb25zdCBub3JtYWxpemVkRGlnaXQgPSB0aGlzLm5vcm1hbGl6ZVRocmVlRGlnaXRzKGRpZ2l0KTsKICAgICAgICAgICAgICB1bmlxdWVOb3JtYWxpemVkRGlnaXRzLmFkZChub3JtYWxpemVkRGlnaXQpOwogICAgICAgICAgICB9KTsKCiAgICAgICAgICAgIC8vIOavj+S4quW9kuS4gOWMluWQjueahOS4ieS9jeaVsOe0r+WKoOmHkeminQogICAgICAgICAgICB1bmlxdWVOb3JtYWxpemVkRGlnaXRzLmZvckVhY2gobm9ybWFsaXplZERpZ2l0ID0+IHsKICAgICAgICAgICAgICBjb25zdCBjdXJyZW50QW1vdW50ID0gbWV0aG9kR3JvdXBzW21ldGhvZElkXS50aHJlZURpZ2l0TWFwLmdldChub3JtYWxpemVkRGlnaXQpIHx8IDA7CiAgICAgICAgICAgICAgbWV0aG9kR3JvdXBzW21ldGhvZElkXS50aHJlZURpZ2l0TWFwLnNldChub3JtYWxpemVkRGlnaXQsIGN1cnJlbnRBbW91bnQgKyBtb25leSk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSk7CgogICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgICBjb25zb2xlLmVycm9yKCfop6PmnpDlpLHotKU6JywgZXJyb3IpOwogICAgICAgIH0KICAgICAgfSk7CgoKCiAgICAgIC8vIOeUn+aIkOaOkuihjOamnAogICAKICAgICAgdGhpcy5udW1iZXJSYW5raW5nQnlHcm91cCA9IFtdOwoKICAgICAgT2JqZWN0LmVudHJpZXMobWV0aG9kR3JvdXBzKS5mb3JFYWNoKChbbWV0aG9kSWQsIGdyb3VwXSkgPT4gewogICAgICAgIC8vIOi9rOaNok1hcOS4uuaVsOe7hOW5tuaOkuW6j++8jOaYvuekuuaJgOacieaVsOaNrgogICAgICAgIGNvbnN0IHNvcnRlZFRocmVlRGlnaXRzID0gQXJyYXkuZnJvbShncm91cC50aHJlZURpZ2l0TWFwLmVudHJpZXMoKSkKICAgICAgICAgIC5zb3J0KChbLGFdLCBbLGJdKSA9PiBiIC0gYSk7CgogICAgICAgIGNvbnN0IHJhbmtpbmcgPSBzb3J0ZWRUaHJlZURpZ2l0cy5tYXAoKFtudW1iZXIsIGFtb3VudF0sIGluZGV4KSA9PiAoewogICAgICAgICAgcmFuazogaW5kZXggKyAxLAogICAgICAgICAgbnVtYmVyLAogICAgICAgICAgY291bnQ6IDEsCiAgICAgICAgICB0b3RhbEFtb3VudDogYW1vdW50LAogICAgICAgICAgcGVyY2VudGFnZTogZ3JvdXAubWV0aG9kVG90YWwgPiAwID8gKChhbW91bnQgLyBncm91cC5tZXRob2RUb3RhbCkgKiAxMDApLnRvRml4ZWQoMSkgOiAnMC4wJwogICAgICAgIH0pKTsKCiAgICAgICAgaWYgKHJhbmtpbmcubGVuZ3RoID4gMCkgewogICAgICAgICAgbnVtYmVyUmFua2luZ0J5R3JvdXAucHVzaCh7CiAgICAgICAgICAgIG1ldGhvZElkOiBwYXJzZUludChtZXRob2RJZCksCiAgICAgICAgICAgIG1ldGhvZE5hbWU6IHRoaXMuZ2V0TWV0aG9kTmFtZShwYXJzZUludChtZXRob2RJZCkpLAogICAgICAgICAgICByYW5raW5nCiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwoKICAgICAgcmV0dXJuIG51bWJlclJhbmtpbmdCeUdyb3VwOwogICAgfSwKICAgIC8qKiDku47mipXms6jlj7fnoIHkuK3mj5Dlj5bmiYDmnInlj6/og73nmoTkuInkvY3mlbDnu4TlkIggKi8KICAgIGV4dHJhY3RUaHJlZURpZ2l0TnVtYmVycyhudW1iZXJPYmopIHsKICAgICAgY29uc3QgbnVtYmVycyA9IFtdOwoKICAgICAgLy8g5aSE55CG5LiN5ZCM55qE5Y+356CB5qC85byPCiAgICAgIGlmICh0eXBlb2YgbnVtYmVyT2JqID09PSAnc3RyaW5nJykgewogICAgICAgIC8vIOebtOaOpeaYr+Wtl+espuS4suagvOW8j+eahOWPt+eggQogICAgICAgIGNvbnN0IGNsZWFuU3RyID0gbnVtYmVyT2JqLnJlcGxhY2UoL1xEL2csICcnKTsgLy8g56e76Zmk6Z2e5pWw5a2X5a2X56ymCiAgICAgICAgdGhpcy5nZW5lcmF0ZVRocmVlRGlnaXRDb21iaW5hdGlvbnMoY2xlYW5TdHIuc3BsaXQoJycpLCBudW1iZXJzKTsKICAgICAgfSBlbHNlIGlmICh0eXBlb2YgbnVtYmVyT2JqID09PSAnb2JqZWN0JyAmJiBudW1iZXJPYmogIT09IG51bGwpIHsKICAgICAgICAvLyDlr7nosaHmoLzlvI8ge2E6IDEsIGI6IDIsIGM6IDMsIGQ6IDQsIGU6IDV9CiAgICAgICAgY29uc3Qga2V5cyA9IE9iamVjdC5rZXlzKG51bWJlck9iaikuc29ydCgpOwogICAgICAgIGNvbnN0IGRpZ2l0cyA9IGtleXMubWFwKGtleSA9PiBTdHJpbmcobnVtYmVyT2JqW2tleV0pKTsKCiAgICAKCiAgICAgICAgLy8g55Sf5oiQ5omA5pyJ5Y+v6IO955qE5LiJ5L2N5pWw57uE5ZCICiAgICAgICAgdGhpcy5nZW5lcmF0ZVRocmVlRGlnaXRDb21iaW5hdGlvbnMoZGlnaXRzLCBudW1iZXJzKTsKICAgICAgfQoKICAgICAgcmV0dXJuIG51bWJlcnM7CiAgICB9LAogICAgLyoqIOeUn+aIkOaJgOacieWPr+iDveeahOS4ieS9jeaVsOe7hOWQiCAqLwogICAgZ2VuZXJhdGVUaHJlZURpZ2l0Q29tYmluYXRpb25zKGRpZ2l0cywgbnVtYmVycykgewogICAgICBjb25zdCBuID0gZGlnaXRzLmxlbmd0aDsKCiAgICAgIC8vIOWmguaenOaVsOWtl+WwkeS6jjPkuKrvvIzml6Dms5Xnu4TmiJDkuInkvY3mlbAKICAgICAgaWYgKG4gPCAzKSB7CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICAvLyDnlJ/miJDmiYDmnInlj6/og73nmoTkuInkvY3mlbDnu4TlkIjvvIhDKG4sMynvvIkKICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBuIC0gMjsgaSsrKSB7CiAgICAgICAgZm9yIChsZXQgaiA9IGkgKyAxOyBqIDwgbiAtIDE7IGorKykgewogICAgICAgICAgZm9yIChsZXQgayA9IGogKyAxOyBrIDwgbjsgaysrKSB7CiAgICAgICAgICAgIGNvbnN0IHRocmVlRGlnaXQgPSBkaWdpdHNbaV0gKyBkaWdpdHNbal0gKyBkaWdpdHNba107CiAgICAgICAgICAgIGlmICgvXlxkezN9JC8udGVzdCh0aHJlZURpZ2l0KSkgewogICAgICAgICAgICAgIG51bWJlcnMucHVzaCh0aHJlZURpZ2l0KTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQogICAgfSwKCiAgICAvKiog5b2S5LiA5YyW5LiJ5L2N5pWw77ya5bCG5pWw5a2X5oyJ5LuO5bCP5Yiw5aSn5o6S5bqP77yM57uf5LiA55u45ZCM5pWw5a2X55qE5LiN5ZCM5o6S5YiXICovCiAgICBub3JtYWxpemVUaHJlZURpZ2l0cyh0aHJlZURpZ2l0KSB7CiAgICAgIC8vIOWwhuS4ieS9jeaVsOWtl+espuS4sui9rOaNouS4uuaVsOe7hO+8jOaOkuW6j+WQjumHjeaWsOe7hOWQiAogICAgICByZXR1cm4gdGhyZWVEaWdpdC5zcGxpdCgnJykuc29ydCgpLmpvaW4oJycpOwogICAgfSwKCiAgICAvKiog5pCc57Si6L6T5YWl5aSE55CGICovCiAgICBoYW5kbGVTZWFyY2hJbnB1dCh2YWx1ZSkgewogICAgICAvLyDlj6rlhYHorrjovpPlhaXmlbDlrZcKICAgICAgdGhpcy5zZWFyY2hOdW1iZXIgPSB2YWx1ZS5yZXBsYWNlKC9cRC9nLCAnJyk7CiAgICB9LAoKICAgIC8qKiDmiafooYzmkJzntKIgKi8KICAgIHBlcmZvcm1TZWFyY2goKSB7CiAgICAgIGlmICghdGhpcy5zZWFyY2hOdW1iZXIudHJpbSgpKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7fovpPlhaXopoHmkJzntKLnmoTlj7fnoIEnKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIHRoaXMuc2VhcmNoTG9hZGluZyA9IHRydWU7CiAgICAgIHRoaXMuaXNTZWFyY2hNb2RlID0gdHJ1ZTsKCiAgICAgIHRyeSB7CiAgICAgICAgLy8g5pCc57Si5b2T5YmN5b2p56eN55qE5pWw5o2uCiAgICAgICAgY29uc3Qgc2VhcmNoUmVzdWx0cyA9IHRoaXMuc2VhcmNoSW5SYW5raW5nRGF0YSh0aGlzLnNlYXJjaE51bWJlcik7CgogICAgICAgIC8vIOabtOaWsOaYvuekuuaVsOaNruS4uuaQnOe0oue7k+aenAogICAgICAgIGlmICh0aGlzLmFjdGl2ZUxvdHRlcnlUeXBlID09PSAnZnVjYWknKSB7CiAgICAgICAgICB0aGlzLmZ1Y2FpTWV0aG9kUmFua2luZyA9IHNlYXJjaFJlc3VsdHMubWV0aG9kUmFua2luZzsKICAgICAgICAgIHRoaXMuZnVjYWlOdW1iZXJSYW5raW5nID0gc2VhcmNoUmVzdWx0cy5udW1iZXJSYW5raW5nOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLnRpY2FpTWV0aG9kUmFua2luZyA9IHNlYXJjaFJlc3VsdHMubWV0aG9kUmFua2luZzsKICAgICAgICAgIHRoaXMudGljYWlOdW1iZXJSYW5raW5nID0gc2VhcmNoUmVzdWx0cy5udW1iZXJSYW5raW5nOwogICAgICAgIH0KCiAgICAgICAgLy8g6K6h566X5pCc57Si57uT5p6c5pWw6YePCiAgICAgICAgdGhpcy5zZWFyY2hSZXN1bHRDb3VudCA9IHRoaXMuY2FsY3VsYXRlU2VhcmNoUmVzdWx0Q291bnQoc2VhcmNoUmVzdWx0cyk7CgogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg5pCc57Si5a6M5oiQ77yM5om+5YiwICR7dGhpcy5zZWFyY2hSZXN1bHRDb3VudH0g5Liq5Yy56YWN6aG5YCk7CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign5pCc57Si5aSx6LSlOicsIGVycm9yKTsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmkJzntKLlpLHotKXvvIzor7fph43or5UnKTsKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICB0aGlzLnNlYXJjaExvYWRpbmcgPSBmYWxzZTsKICAgICAgfQogICAgfSwKCiAgICAvKiog5riF6Zmk5pCc57SiICovCiAgICBjbGVhclNlYXJjaCgpIHsKICAgICAgdGhpcy5zZWFyY2hOdW1iZXIgPSAnJzsKICAgICAgdGhpcy5pc1NlYXJjaE1vZGUgPSBmYWxzZTsKICAgICAgdGhpcy5zZWFyY2hSZXN1bHRDb3VudCA9IDA7CgogICAgICAvLyDmgaLlpI3ljp/lp4vmlbDmja4KICAgICAgdGhpcy5mdWNhaU1ldGhvZFJhbmtpbmcgPSBbLi4udGhpcy5vcmlnaW5hbEZ1Y2FpTWV0aG9kUmFua2luZ107CiAgICAgIHRoaXMuZnVjYWlOdW1iZXJSYW5raW5nID0gWy4uLnRoaXMub3JpZ2luYWxGdWNhaU51bWJlclJhbmtpbmddOwogICAgICB0aGlzLnRpY2FpTWV0aG9kUmFua2luZyA9IFsuLi50aGlzLm9yaWdpbmFsVGljYWlNZXRob2RSYW5raW5nXTsKICAgICAgdGhpcy50aWNhaU51bWJlclJhbmtpbmcgPSBbLi4udGhpcy5vcmlnaW5hbFRpY2FpTnVtYmVyUmFua2luZ107CiAgICB9LAoKICAgIC8qKiDlnKjmjpLooYzmlbDmja7kuK3mkJzntKLljIXlkKvmjIflrprlj7fnoIHnmoTpobnnm64gKi8KICAgIHNlYXJjaEluUmFua2luZ0RhdGEoc2VhcmNoTnVtYmVyKSB7CiAgICAgIGNvbnN0IG9yaWdpbmFsTWV0aG9kUmFua2luZyA9IHRoaXMuYWN0aXZlTG90dGVyeVR5cGUgPT09ICdmdWNhaScKICAgICAgICA/IHRoaXMub3JpZ2luYWxGdWNhaU1ldGhvZFJhbmtpbmcKICAgICAgICA6IHRoaXMub3JpZ2luYWxUaWNhaU1ldGhvZFJhbmtpbmc7CgogICAgICBjb25zdCBvcmlnaW5hbE51bWJlclJhbmtpbmcgPSB0aGlzLmFjdGl2ZUxvdHRlcnlUeXBlID09PSAnZnVjYWknCiAgICAgICAgPyB0aGlzLm9yaWdpbmFsRnVjYWlOdW1iZXJSYW5raW5nCiAgICAgICAgOiB0aGlzLm9yaWdpbmFsVGljYWlOdW1iZXJSYW5raW5nOwoKICAgICAgLy8g5pCc57Si5oqV5rOo5o6S6KGM5qacCiAgICAgIGNvbnN0IGZpbHRlcmVkTWV0aG9kUmFua2luZyA9IG9yaWdpbmFsTWV0aG9kUmFua2luZy5tYXAobWV0aG9kR3JvdXAgPT4gewogICAgICAgIGNvbnN0IGZpbHRlcmVkUmFua2luZyA9IG1ldGhvZEdyb3VwLnJhbmtpbmcuZmlsdGVyKGl0ZW0gPT4gewogICAgICAgICAgcmV0dXJuIHRoaXMuaXNOdW1iZXJNYXRjaChpdGVtLmJldE51bWJlcnMsIHNlYXJjaE51bWJlcik7CiAgICAgICAgfSk7CgogICAgICAgIHJldHVybiB7CiAgICAgICAgICAuLi5tZXRob2RHcm91cCwKICAgICAgICAgIHJhbmtpbmc6IGZpbHRlcmVkUmFua2luZwogICAgICAgIH07CiAgICAgIH0pLmZpbHRlcihtZXRob2RHcm91cCA9PiBtZXRob2RHcm91cC5yYW5raW5nLmxlbmd0aCA+IDApOwoKICAgICAgLy8g5pCc57Si54Ot6Zeo5LiJ5L2N5pWw5o6S6KGM5qac77yI5L2/55So5LiT6Zeo55qE5LiJ5L2N5pWw5Yy56YWN6YC76L6R77yJCiAgICAgIGNvbnN0IGZpbHRlcmVkTnVtYmVyUmFua2luZyA9IG9yaWdpbmFsTnVtYmVyUmFua2luZy5tYXAobWV0aG9kR3JvdXAgPT4gewogICAgICAgIGNvbnN0IGZpbHRlcmVkUmFua2luZyA9IG1ldGhvZEdyb3VwLnJhbmtpbmcuZmlsdGVyKGl0ZW0gPT4gewogICAgICAgICAgcmV0dXJuIHRoaXMuaXNUaHJlZURpZ2l0TWF0Y2goaXRlbS5udW1iZXIsIHNlYXJjaE51bWJlcik7CiAgICAgICAgfSk7CgogICAgICAgIHJldHVybiB7CiAgICAgICAgICAuLi5tZXRob2RHcm91cCwKICAgICAgICAgIHJhbmtpbmc6IGZpbHRlcmVkUmFua2luZwogICAgICAgIH07CiAgICAgIH0pLmZpbHRlcihtZXRob2RHcm91cCA9PiBtZXRob2RHcm91cC5yYW5raW5nLmxlbmd0aCA+IDApOwoKICAgICAgcmV0dXJuIHsKICAgICAgICBtZXRob2RSYW5raW5nOiBmaWx0ZXJlZE1ldGhvZFJhbmtpbmcsCiAgICAgICAgbnVtYmVyUmFua2luZzogZmlsdGVyZWROdW1iZXJSYW5raW5nCiAgICAgIH07CiAgICB9LAoKICAgIC8qKiDliKTmlq3lj7fnoIHmmK/lkKbljLnphY3mkJzntKLmnaHku7YgKi8KICAgIGlzTnVtYmVyTWF0Y2godGFyZ2V0TnVtYmVyLCBzZWFyY2hOdW1iZXIpIHsKICAgICAgaWYgKCF0YXJnZXROdW1iZXIgfHwgIXNlYXJjaE51bWJlcikgcmV0dXJuIGZhbHNlOwoKICAgICAgY29uc3QgdGFyZ2V0ID0gU3RyaW5nKHRhcmdldE51bWJlcik7CiAgICAgIGNvbnN0IHNlYXJjaCA9IFN0cmluZyhzZWFyY2hOdW1iZXIpOwogICAgICBjb25zdCBzZWFyY2hEaWdpdHMgPSBzZWFyY2guc3BsaXQoJycpOwogICAgICBjb25zdCB0YXJnZXREaWdpdHMgPSB0YXJnZXQuc3BsaXQoJycpOwoKICAgICAgLy8g5qC45b+D6YC76L6R77ya5qOA5p+l5pCc57Si5Y+356CB5Lit55qE5q+P5Liq5pWw5a2X5piv5ZCm6YO95Zyo55uu5qCH5Y+356CB5Lit5Ye6546wCiAgICAgIC8vIOi/meagt+WPr+S7peWMuemFjeWQhOenjemVv+W6pueahOebruagh+WPt+eggQoKICAgICAgLy8g5pa55qGIMe+8muS7u+aEj+WMuemFjSAtIOaQnOe0ouWPt+eggeS4reeahOS7u+S4gOaVsOWtl+WcqOebruagh+WPt+eggeS4reWHuueOsOWNs+WMuemFjQogICAgICAvLyDpgILnlKjkuo7ni6zog4bnrYnljZXmlbDlrZfnjqnms5UKICAgICAgY29uc3QgaGFzQW55RGlnaXQgPSBzZWFyY2hEaWdpdHMuc29tZShkaWdpdCA9PiB0YXJnZXREaWdpdHMuaW5jbHVkZXMoZGlnaXQpKTsKCiAgICAgIC8vIOaWueahiDLvvJrlrozlhajljLnphY0gLSDmkJzntKLlj7fnoIHkuK3nmoTmiYDmnInmlbDlrZfpg73lnKjnm67moIflj7fnoIHkuK3lh7rnjrAKICAgICAgLy8g6YCC55So5LqO5aSa5pWw5a2X57uE5ZCI546p5rOVCiAgICAgIGNvbnN0IGhhc0FsbERpZ2l0cyA9IHNlYXJjaERpZ2l0cy5ldmVyeShkaWdpdCA9PiB0YXJnZXREaWdpdHMuaW5jbHVkZXMoZGlnaXQpKTsKCiAgICAgIC8vIOaWueahiDPvvJrnsr7noa7ljLnphY0gLSDnm67moIflj7fnoIHljIXlkKvmkJzntKLlj7fnoIHnmoTov57nu63lrZDkuLIKICAgICAgY29uc3QgaGFzRXhhY3RTZXF1ZW5jZSA9IHRhcmdldC5pbmNsdWRlcyhzZWFyY2gpOwoKICAgICAgLy8g5qC55o2u5LiN5ZCM5oOF5Ya16YeH55So5LiN5ZCM55qE5Yy56YWN562W55WlCiAgICAgIGlmIChzZWFyY2gubGVuZ3RoID09PSAxKSB7CiAgICAgICAgLy8g5Y2V5pWw5a2X5pCc57Si77ya5Y+q6KaB55uu5qCH5Y+356CB5YyF5ZCr6K+l5pWw5a2X5Y2z5Yy56YWNCiAgICAgICAgcmV0dXJuIGhhc0FueURpZ2l0OwogICAgICB9IGVsc2UgaWYgKHNlYXJjaC5sZW5ndGggPT09IDIpIHsKICAgICAgICAvLyDlj4zmlbDlrZfmkJzntKLvvJrkvJjlhYjnsr7noa7ljLnphY3vvIzlhbbmrKHku7vmhI/ljLnphY0KICAgICAgICByZXR1cm4gaGFzRXhhY3RTZXF1ZW5jZSB8fCBoYXNBbnlEaWdpdDsKICAgICAgfSBlbHNlIGlmIChzZWFyY2gubGVuZ3RoID49IDMpIHsKICAgICAgICAvLyDkuInkvY3mlbDlj4rku6XkuIrmkJzntKLvvJrph4fnlKjlpJrnp43ljLnphY3nrZbnlaUKCiAgICAgICAgLy8g562W55WlMe+8mueyvuehruW6j+WIl+WMuemFje+8iOWmguaQnOe0ojEyNe+8jOebruaghzEyNTM05Yy56YWN77yJCiAgICAgICAgaWYgKGhhc0V4YWN0U2VxdWVuY2UpIHsKICAgICAgICAgIHJldHVybiB0cnVlOwogICAgICAgIH0KCiAgICAgICAgLy8g562W55WlMu+8muWvueS6juefreebruagh+WPt+egge+8iDEtMuS9je+8ie+8jOmHh+eUqOS7u+aEj+WMuemFjQogICAgICAgIGlmICh0YXJnZXQubGVuZ3RoIDw9IDIpIHsKICAgICAgICAgIHJldHVybiBoYXNBbnlEaWdpdDsKICAgICAgICB9CgogICAgICAgIC8vIOetlueVpTPvvJrlr7nkuo7kuInkvY3mlbDnm67moIfvvIzmo4Dmn6XmmK/lkKbkuLrnm7jlkIzmlbDlrZfnu4TlkIjvvIjlvZLkuIDljJbmr5TovoPvvIkKICAgICAgICBpZiAodGFyZ2V0Lmxlbmd0aCA9PT0gMyAmJiBzZWFyY2gubGVuZ3RoID09PSAzKSB7CiAgICAgICAgICBjb25zdCBub3JtYWxpemVkU2VhcmNoID0gdGhpcy5ub3JtYWxpemVUaHJlZURpZ2l0cyhzZWFyY2gpOwogICAgICAgICAgY29uc3Qgbm9ybWFsaXplZFRhcmdldCA9IHRoaXMubm9ybWFsaXplVGhyZWVEaWdpdHModGFyZ2V0KTsKICAgICAgICAgIHJldHVybiBub3JtYWxpemVkU2VhcmNoID09PSBub3JtYWxpemVkVGFyZ2V0OwogICAgICAgIH0KCiAgICAgICAgLy8g562W55WlNO+8muWvueS6jumVv+ebruagh+WPt+egge+8jOimgeaxguWMheWQq+aJgOacieaQnOe0ouaVsOWtlwogICAgICAgIGlmICh0YXJnZXQubGVuZ3RoID49IHNlYXJjaC5sZW5ndGgpIHsKICAgICAgICAgIHJldHVybiBoYXNBbGxEaWdpdHM7CiAgICAgICAgfQoKICAgICAgICAvLyDnrZbnlaU177ya5a+55LqO5Lit562J6ZW/5bqm55uu5qCH5Y+356CB77yM6YeH55So5Lu75oSP5Yy56YWNCiAgICAgICAgcmV0dXJuIGhhc0FueURpZ2l0OwogICAgICB9CgogICAgICByZXR1cm4gZmFsc2U7CiAgICB9LAoKICAgIC8qKiDkuJPpl6jnlKjkuo7kuInkvY3mlbDmjpLooYzmppznmoTljLnphY3pgLvovpEgKi8KICAgIGlzVGhyZWVEaWdpdE1hdGNoKHRhcmdldFRocmVlRGlnaXQsIHNlYXJjaE51bWJlcikgewogICAgICBpZiAoIXRhcmdldFRocmVlRGlnaXQgfHwgIXNlYXJjaE51bWJlcikgcmV0dXJuIGZhbHNlOwoKICAgICAgY29uc3QgdGFyZ2V0ID0gU3RyaW5nKHRhcmdldFRocmVlRGlnaXQpOwogICAgICBjb25zdCBzZWFyY2ggPSBTdHJpbmcoc2VhcmNoTnVtYmVyKTsKCiAgICAgIC8vIOS4ieS9jeaVsOaOkuihjOamnOeahOebruagh+mDveaYrzPkvY3mlbDvvIzpnIDopoHnibnmrorlpITnkIYKICAgICAgaWYgKHRhcmdldC5sZW5ndGggIT09IDMpIHJldHVybiBmYWxzZTsKCiAgICAgIGlmIChzZWFyY2gubGVuZ3RoID09PSAxKSB7CiAgICAgICAgLy8g5pCc57Si5Y2V5pWw5a2X77ya5LiJ5L2N5pWw5Lit5YyF5ZCr6K+l5pWw5a2X5Y2z5Yy56YWNCiAgICAgICAgcmV0dXJuIHRhcmdldC5pbmNsdWRlcyhzZWFyY2gpOwogICAgICB9IGVsc2UgaWYgKHNlYXJjaC5sZW5ndGggPT09IDIpIHsKICAgICAgICAvLyDmkJzntKLlj4zmlbDlrZfvvJrkuInkvY3mlbDkuK3ljIXlkKvov5nkuKTkuKrmlbDlrZfljbPljLnphY0KICAgICAgICBjb25zdCBbZGlnaXQxLCBkaWdpdDJdID0gc2VhcmNoLnNwbGl0KCcnKTsKICAgICAgICByZXR1cm4gdGFyZ2V0LmluY2x1ZGVzKGRpZ2l0MSkgJiYgdGFyZ2V0LmluY2x1ZGVzKGRpZ2l0Mik7CiAgICAgIH0gZWxzZSBpZiAoc2VhcmNoLmxlbmd0aCA9PT0gMykgewogICAgICAgIC8vIOaQnOe0ouS4ieS9jeaVsO+8muW/hemhu+aYr+ebuOWQjOeahOaVsOWtl+e7hOWQiO+8iOW9kuS4gOWMluWQjuavlOi+g++8iQogICAgICAgIGNvbnN0IG5vcm1hbGl6ZWRTZWFyY2ggPSB0aGlzLm5vcm1hbGl6ZVRocmVlRGlnaXRzKHNlYXJjaCk7CiAgICAgICAgY29uc3Qgbm9ybWFsaXplZFRhcmdldCA9IHRoaXMubm9ybWFsaXplVGhyZWVEaWdpdHModGFyZ2V0KTsKICAgICAgICByZXR1cm4gbm9ybWFsaXplZFNlYXJjaCA9PT0gbm9ybWFsaXplZFRhcmdldDsKICAgICAgfSBlbHNlIGlmIChzZWFyY2gubGVuZ3RoID4gMykgewogICAgICAgIC8vIOaQnOe0oui2hei/h+S4ieS9jeaVsO+8muajgOafpeS4ieS9jeaVsOaYr+WQpuWMheWQq+aQnOe0ouWPt+eggeS4reeahOS7u+aEj+S4ieS4quaVsOWtl+e7hOWQiAogICAgICAgIGNvbnN0IHNlYXJjaERpZ2l0cyA9IHNlYXJjaC5zcGxpdCgnJyk7CgogICAgICAgIC8vIOeUn+aIkOaQnOe0ouWPt+eggeeahOaJgOacieS4ieS9jeaVsOe7hOWQiO+8jOeci+aYr+WQpuacieWMuemFjeeahAogICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgc2VhcmNoRGlnaXRzLmxlbmd0aCAtIDI7IGkrKykgewogICAgICAgICAgZm9yIChsZXQgaiA9IGkgKyAxOyBqIDwgc2VhcmNoRGlnaXRzLmxlbmd0aCAtIDE7IGorKykgewogICAgICAgICAgICBmb3IgKGxldCBrID0gaiArIDE7IGsgPCBzZWFyY2hEaWdpdHMubGVuZ3RoOyBrKyspIHsKICAgICAgICAgICAgICBjb25zdCBzZWFyY2hDb21ibyA9IHNlYXJjaERpZ2l0c1tpXSArIHNlYXJjaERpZ2l0c1tqXSArIHNlYXJjaERpZ2l0c1trXTsKICAgICAgICAgICAgICBjb25zdCBub3JtYWxpemVkU2VhcmNoQ29tYm8gPSB0aGlzLm5vcm1hbGl6ZVRocmVlRGlnaXRzKHNlYXJjaENvbWJvKTsKICAgICAgICAgICAgICBjb25zdCBub3JtYWxpemVkVGFyZ2V0ID0gdGhpcy5ub3JtYWxpemVUaHJlZURpZ2l0cyh0YXJnZXQpOwogICAgICAgICAgICAgIGlmIChub3JtYWxpemVkU2VhcmNoQ29tYm8gPT09IG5vcm1hbGl6ZWRUYXJnZXQpIHsKICAgICAgICAgICAgICAgIHJldHVybiB0cnVlOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KCiAgICAgIHJldHVybiBmYWxzZTsKICAgIH0sCgogICAgLyoqIOiuoeeul+aQnOe0oue7k+aenOaVsOmHjyAqLwogICAgY2FsY3VsYXRlU2VhcmNoUmVzdWx0Q291bnQoc2VhcmNoUmVzdWx0cykgewogICAgICBsZXQgY291bnQgPSAwOwoKICAgICAgLy8g57uf6K6h5oqV5rOo5o6S6KGM5qac5Yy56YWN6aG5CiAgICAgIHNlYXJjaFJlc3VsdHMubWV0aG9kUmFua2luZy5mb3JFYWNoKG1ldGhvZEdyb3VwID0+IHsKICAgICAgICBjb3VudCArPSBtZXRob2RHcm91cC5yYW5raW5nLmxlbmd0aDsKICAgICAgfSk7CgogICAgICAvLyDnu5/orqHng63pl6jkuInkvY3mlbDmjpLooYzmppzljLnphY3pobkKICAgICAgc2VhcmNoUmVzdWx0cy5udW1iZXJSYW5raW5nLmZvckVhY2gobWV0aG9kR3JvdXAgPT4gewogICAgICAgIGNvdW50ICs9IG1ldGhvZEdyb3VwLnJhbmtpbmcubGVuZ3RoOwogICAgICB9KTsKCiAgICAgIHJldHVybiBjb3VudDsKICAgIH0sCiAgICAvKiog5Yi35paw5pWw5o2uICovCiAgICBhc3luYyByZWZyZXNoRGF0YSgpIHsKICAgICAgYXdhaXQgdGhpcy5jYWxjdWxhdGVTdGF0aXN0aWNzKCk7CiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5pWw5o2u5bey5Yi35pawJyk7CiAgICB9LAogICAgLyoqIOa1i+ivleS4ieS9jeaVsOe7hOWQiOaPkOWPlumAu+i+kSAqLwogICAgdGVzdFRocmVlRGlnaXRFeHRyYWN0aW9uKCkgewogICAgICBjb25zb2xlLmxvZygnPT09IOa1i+ivleS4ieS9jeaVsOe7hOWQiOaPkOWPluWSjOW9kuS4gOWMlumAu+i+kSA9PT0nKTsKCiAgICAgIC8vIOa1i+ivleS4jeWQjOmVv+W6pueahOWPt+eggQogICAgICBjb25zdCB0ZXN0Q2FzZXMgPSBbCiAgICAgICAge2E6IDEsIGI6IDIsIGM6IDN9LCAgICAgICAgICAgICAgICAgICAgLy8g5LiJ5L2N5pWwCiAgICAgICAge2E6IDEsIGI6IDIsIGM6IDMsIGQ6IDR9LCAgICAgICAgICAgICAvLyDlm5vkvY3mlbAKICAgICAgICB7YTogMCwgYjogMSwgYzogMiwgZDogMywgZTogNCwgZjogNX0sIC8vIOWFreS9jeaVsAogICAgICAgIHthOiAxLCBiOiAyLCBjOiAzLCBkOiA0LCBlOiA1fSwgICAgICAgLy8g5LqU5L2N5pWwCiAgICAgIF07CgogICAgICB0ZXN0Q2FzZXMuZm9yRWFjaCgodGVzdENhc2UsIGluZGV4KSA9PiB7CiAgICAgICAgY29uc29sZS5sb2coYFxu5rWL6K+V55So5L6LICR7aW5kZXggKyAxfTpgLCB0ZXN0Q2FzZSk7CiAgICAgICAgY29uc3QgdGhyZWVEaWdpdHMgPSB0aGlzLmV4dHJhY3RUaHJlZURpZ2l0TnVtYmVycyh0ZXN0Q2FzZSk7CiAgICAgICAgY29uc29sZS5sb2coJ+aPkOWPlueahOS4ieS9jeaVsOe7hOWQiDonLCB0aHJlZURpZ2l0cyk7CgogICAgICAgIC8vIOa1i+ivleW9kuS4gOWMluaViOaenAogICAgICAgIGNvbnN0IG5vcm1hbGl6ZWRTZXQgPSBuZXcgU2V0KCk7CiAgICAgICAgdGhyZWVEaWdpdHMuZm9yRWFjaChkaWdpdCA9PiB7CiAgICAgICAgICBjb25zdCBub3JtYWxpemVkID0gdGhpcy5ub3JtYWxpemVUaHJlZURpZ2l0cyhkaWdpdCk7CiAgICAgICAgICBub3JtYWxpemVkU2V0LmFkZChub3JtYWxpemVkKTsKICAgICAgICAgIGNvbnNvbGUubG9nKGAke2RpZ2l0fSAtPiAke25vcm1hbGl6ZWR9YCk7CiAgICAgICAgfSk7CiAgICAgICAgY29uc29sZS5sb2coJ+W9kuS4gOWMluWQjueahOWUr+S4gOe7hOWQiDonLCBBcnJheS5mcm9tKG5vcm1hbGl6ZWRTZXQpKTsKICAgICAgfSk7CgogICAgICBjb25zb2xlLmxvZygnPT09IOa1i+ivleWujOaIkCA9PT0nKTsKICAgIH0sCiAgICAvKiog6K6h566X57uE5ZCI5pWwIEMobixyKSAqLwogICAgY2FsY3VsYXRlQ29tYmluYXRpb25zKG4sIHIpIHsKICAgICAgaWYgKHIgPiBuKSByZXR1cm4gMDsKICAgICAgaWYgKHIgPT09IDAgfHwgciA9PT0gbikgcmV0dXJuIDE7CgogICAgICBsZXQgcmVzdWx0ID0gMTsKICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCByOyBpKyspIHsKICAgICAgICByZXN1bHQgPSByZXN1bHQgKiAobiAtIGkpIC8gKGkgKyAxKTsKICAgICAgfQogICAgICByZXR1cm4gTWF0aC5yb3VuZChyZXN1bHQpOwogICAgfSwKICAgIC8qKiDlhbPpl63lr7nor53moYYgKi8KICAgIC8qKiDliqDovb3nlKjmiLfliJfooaggKi8KICAgIGFzeW5jIGxvYWRVc2VyTGlzdCgpIHsKICAgICAgdHJ5IHsKICAgICAgICAvLyDlhYjojrflj5bmiYDmnInnlKjmiLcKICAgICAgICBjb25zdCB1c2VyUmVzcG9uc2UgPSBhd2FpdCByZXF1ZXN0KHsKICAgICAgICAgIHVybDogJy9zeXN0ZW0vdXNlci9saXN0JywKICAgICAgICAgIG1ldGhvZDogJ2dldCcsCiAgICAgICAgICBwYXJhbXM6IHsgcGFnZU51bTogMSwgcGFnZVNpemU6IDEwMDAgfQogICAgICAgIH0pOwoKICAgICAgICBpZiAoIXVzZXJSZXNwb25zZSB8fCAhdXNlclJlc3BvbnNlLnJvd3MpIHsKICAgICAgICAgIHRoaXMudXNlckxpc3QgPSBbXTsKICAgICAgICAgIHJldHVybjsKICAgICAgICB9CgogICAgICAgIGNvbnN0IGFsbFVzZXJzID0gdXNlclJlc3BvbnNlLnJvd3MuZmlsdGVyKHVzZXIgPT4gdXNlci5zdGF0dXMgPT09ICcwJyk7IC8vIOWPquiOt+WPluato+W4uOeKtuaAgeeahOeUqOaItwoKICAgICAgICAvLyDojrflj5bmnInkuIvms6jorrDlvZXnmoTnlKjmiLdJROWIl+ihqAogICAgICAgIGNvbnN0IHJlY29yZFJlc3BvbnNlID0gYXdhaXQgcmVxdWVzdCh7CiAgICAgICAgICB1cmw6ICcvZ2FtZS9yZWNvcmQvbGlzdCcsCiAgICAgICAgICBtZXRob2Q6ICdnZXQnLAogICAgICAgICAgcGFyYW1zOiB7CiAgICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICAgIHBhZ2VTaXplOiAxMDAwMDAgLy8g6I635Y+W5omA5pyJ6K6w5b2V5p2l57uf6K6h55So5oi3CiAgICAgICAgICB9CiAgICAgICAgfSk7CgogICAgICAgIGlmIChyZWNvcmRSZXNwb25zZSAmJiByZWNvcmRSZXNwb25zZS5yb3dzKSB7CiAgICAgICAgICAvLyDnu5/orqHmr4/kuKrnlKjmiLfnmoTkuIvms6jorrDlvZXmlbDph48KICAgICAgICAgIGNvbnN0IHVzZXJSZWNvcmRDb3VudHMgPSB7fTsKICAgICAgICAgIHJlY29yZFJlc3BvbnNlLnJvd3MuZm9yRWFjaChyZWNvcmQgPT4gewogICAgICAgICAgICBjb25zdCB1c2VySWQgPSByZWNvcmQuc3lzVXNlcklkOwogICAgICAgICAgICB1c2VyUmVjb3JkQ291bnRzW3VzZXJJZF0gPSAodXNlclJlY29yZENvdW50c1t1c2VySWRdIHx8IDApICsgMTsKICAgICAgICAgIH0pOwoKICAgICAgICAgIC8vIOWPquS/neeVmeacieS4i+azqOiusOW9leeahOeUqOaIt++8jOW5tua3u+WKoOiusOW9leaVsOmHj+S/oeaBrwogICAgICAgICAgdGhpcy51c2VyTGlzdCA9IGFsbFVzZXJzCiAgICAgICAgICAgIC5maWx0ZXIodXNlciA9PiB1c2VyUmVjb3JkQ291bnRzW3VzZXIudXNlcklkXSkKICAgICAgICAgICAgLm1hcCh1c2VyID0+ICh7CiAgICAgICAgICAgICAgLi4udXNlciwKICAgICAgICAgICAgICByZWNvcmRDb3VudDogdXNlclJlY29yZENvdW50c1t1c2VyLnVzZXJJZF0gfHwgMAogICAgICAgICAgICB9KSkKICAgICAgICAgICAgLnNvcnQoKGEsIGIpID0+IGIucmVjb3JkQ291bnQgLSBhLnJlY29yZENvdW50KTsgLy8g5oyJ5LiL5rOo6K6w5b2V5pWw6YeP6ZmN5bqP5o6S5YiXCgogICAgICAgICAgY29uc29sZS5sb2coYOWKoOi9veeUqOaIt+WIl+ihqOWujOaIkO+8jOWFsSAke3RoaXMudXNlckxpc3QubGVuZ3RofSDkuKrmnInkuIvms6jorrDlvZXnmoTnlKjmiLdgKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy51c2VyTGlzdCA9IFtdOwogICAgICAgICAgY29uc29sZS53YXJuKCfmnKrojrflj5bliLDkuIvms6jorrDlvZXmlbDmja4nKTsKICAgICAgICB9CgogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlueUqOaIt+WIl+ihqOWksei0pTonLCBlcnJvcik7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+W55So5oi35YiX6KGo5aSx6LSlJyk7CiAgICAgICAgdGhpcy51c2VyTGlzdCA9IFtdOwogICAgICB9CiAgICB9LAoKICAgIC8qKiDojrflj5bmjIflrprnlKjmiLfnmoTorrDlvZXmlbDmja4gKi8KICAgIGFzeW5jIGZldGNoVXNlclJlY29yZERhdGEodXNlcklkKSB7CiAgICAgIHRyeSB7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHJlcXVlc3QoewogICAgICAgICAgdXJsOiAnL2dhbWUvcmVjb3JkL2xpc3QnLAogICAgICAgICAgbWV0aG9kOiAnZ2V0JywKICAgICAgICAgIHBhcmFtczogewogICAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgICBwYWdlU2l6ZTogMTAwMDAwLAogICAgICAgICAgICBzeXNVc2VySWQ6IHVzZXJJZAogICAgICAgICAgfQogICAgICAgIH0pOwoKICAgICAgICBpZiAocmVzcG9uc2UgJiYgcmVzcG9uc2Uucm93cykgewogICAgICAgICAgcmV0dXJuIHJlc3BvbnNlLnJvd3M7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHJldHVybiBbXTsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W55So5oi36K6w5b2V5pWw5o2u5aSx6LSlOicsIGVycm9yKTsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5bnlKjmiLforrDlvZXmlbDmja7lpLHotKUnKTsKICAgICAgICByZXR1cm4gW107CiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0KICAgIH0sCgogICAgLyoqIOeUqOaIt+mAieaLqeWPmOWMluWkhOeQhiAqLwogICAgYXN5bmMgaGFuZGxlVXNlckNoYW5nZSh1c2VySWQpIHsKICAgICAgLy8g5riF56m65b2T5YmN57uf6K6h5pWw5o2uCiAgICAgIHRoaXMubWV0aG9kUmFua2luZ0J5R3JvdXAgPSBbXTsKICAgICAgdGhpcy5udW1iZXJSYW5raW5nQnlHcm91cCA9IFtdOwoKICAgICAgaWYgKHVzZXJJZCkgewogICAgICAgIC8vIOebtOaOpeWKoOi9vemAieS4reeUqOaIt+eahOe7n+iuoeaVsOaNrgogICAgICAgIGF3YWl0IHRoaXMuY2FsY3VsYXRlU3RhdGlzdGljcygpOwogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg5bey5Yqg6L2955So5oi3ICR7dGhpcy5nZXRDdXJyZW50VXNlck5hbWUoKX0g55qE57uf6K6h5pWw5o2uYCk7CiAgICAgIH0KICAgIH0sCgogICAgLyoqIOiOt+WPluW9k+WJjemAieS4reeUqOaIt+eahOWQjeensCAqLwogICAgZ2V0Q3VycmVudFVzZXJOYW1lKCkgewogICAgICBpZiAoIXRoaXMuc2VsZWN0ZWRVc2VySWQpIHJldHVybiAnJzsKICAgICAgY29uc3QgdXNlciA9IHRoaXMudXNlckxpc3QuZmluZCh1ID0+IHUudXNlcklkID09PSB0aGlzLnNlbGVjdGVkVXNlcklkKTsKICAgICAgcmV0dXJuIHVzZXIgPyAodXNlci5uaWNrTmFtZSB8fCB1c2VyLnVzZXJOYW1lKSA6ICcnOwogICAgfSwKCiAgICAvKiog5b2p56eN5YiH5o2i5aSE55CGICovCiAgICBoYW5kbGVMb3R0ZXJ5VHlwZUNoYW5nZSh0YWIpIHsKICAgICAgY29uc29sZS5sb2coYOWIh+aNouWIsOW9qeenjTogJHt0YWIubmFtZSA9PT0gJ2Z1Y2FpJyA/ICfnpo/lvakzRCcgOiAn5L2T5b2pUDMnfWApOwogICAgICAvLyDliIfmjaLlvannp43ml7bvvIzorqHnrpflsZ7mgKfkvJroh6rliqjmm7TmlrDmmL7npLrnmoTmlbDmja4KICAgIH0sCgogICAgLyoqIOWIt+aWsOe7n+iuoeaVsOaNriAqLwogICAgYXN5bmMgcmVmcmVzaFN0YXRpc3RpY3MoKSB7CiAgICAgIGlmICh0aGlzLmlzQWRtaW4gJiYgIXRoaXMuc2VsZWN0ZWRVc2VySWQpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+WFiOmAieaLqeimgeafpeeci+e7n+iuoeeahOeUqOaItycpOwogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgdHJ5IHsKICAgICAgICBhd2FpdCB0aGlzLmxvYWRHYW1lTWV0aG9kcygpOwogICAgICAgIGF3YWl0IHRoaXMuY2FsY3VsYXRlU3RhdGlzdGljcygpOwogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn57uf6K6h5pWw5o2u5bey5Yi35pawJyk7CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign5Yi35paw57uf6K6h5aSx6LSlOicsIGVycm9yKTsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfliLfmlrDnu5/orqHlpLHotKUnKTsKICAgICAgfQogICAgfSwKCiAgICBjbG9zZSgpIHsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2U7CiAgICAgIC8vIOa4heepuumAieaLqeeKtuaAgQogICAgICB0aGlzLnNlbGVjdGVkVXNlcklkID0gbnVsbDsKICAgICAgdGhpcy5hY3RpdmVMb3R0ZXJ5VHlwZSA9ICdmdWNhaSc7IC8vIOmHjee9ruS4uuemj+W9qQogICAgICAvLyDmuIXnqbrmiYDmnInnu5/orqHmlbDmja4KICAgICAgdGhpcy5jbGVhckFsbFJhbmtpbmdEYXRhKCk7CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["BetStatistics.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgNA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;;AAIA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BetStatistics.vue", "sourceRoot": "src/views/game/record/components", "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      title=\"下注统计\"\n      :visible.sync=\"dialogVisible\"\n      width=\"95%\"\n      append-to-body\n      style=\"margin-top: -50px;\"\n      :close-on-click-modal=\"false\"\n      class=\"statistics-dialog\">\n\n      <!-- 管理员用户选择器 -->\n      <div v-if=\"isAdmin\" class=\"admin-controls\" style=\"margin-bottom: 20px; padding: 15px; background: #f5f7fa; border-radius: 4px;\">\n        <div style=\"display: flex; align-items: center; gap: 15px;\">\n          <span style=\"font-weight: 500; color: #606266;\">选择用户:</span>\n          <el-select\n            v-model=\"selectedUserId\"\n            placeholder=\"选择用户查看统计\"\n            @change=\"handleUserChange\"\n            clearable\n            style=\"width: 250px;\">\n            <el-option\n              v-for=\"user in userList\"\n              :key=\"user.userId\"\n              :label=\"`${user.nickName || user.userName} (ID: ${user.userId}) - ${user.recordCount}条记录`\"\n              :value=\"user.userId\">\n            </el-option>\n          </el-select>\n          <el-button\n            type=\"primary\"\n            size=\"small\"\n            @click=\"refreshStatistics\"\n            :loading=\"loading\">\n            <i class=\"el-icon-refresh\"></i> 刷新统计\n          </el-button>\n          <span v-if=\"selectedUserId\" style=\"color: #67c23a; font-size: 12px;\">\n            当前查看: {{ getCurrentUserName() }}\n          </span>\n        </div>\n      </div>\n\n      <!-- 彩种选择标签页 - 所有用户都可见 -->\n      <div class=\"lottery-type-tabs-container\" style=\"margin-bottom: 20px; text-align: center;\">\n        <el-tabs v-model=\"activeLotteryType\" @tab-click=\"handleLotteryTypeChange\" type=\"card\">\n          <el-tab-pane name=\"fucai\">\n            <span slot=\"label\">\n              <i class=\"el-icon-medal-1\"></i>\n              福彩3D\n            </span>\n          </el-tab-pane>\n          <el-tab-pane name=\"ticai\">\n            <span slot=\"label\">\n              <i class=\"el-icon-trophy-1\"></i>\n              体彩排三\n            </span>\n          </el-tab-pane>\n        </el-tabs>\n      </div>\n\n      <!-- 号码搜索功能 -->\n      <div v-if=\"!isAdmin || (isAdmin && selectedUserId)\" class=\"search-container\" style=\"margin-bottom: 20px;\">\n        <div class=\"search-box\">\n          <el-input\n            v-model=\"searchNumber\"\n            placeholder=\"输入号码搜索相关投注（如：125）\"\n            prefix-icon=\"el-icon-search\"\n            clearable\n            @input=\"handleSearchInput\"\n            @clear=\"clearSearch\"\n            style=\"width: 300px; margin-right: 10px;\">\n          </el-input>\n          <el-button\n            type=\"primary\"\n            icon=\"el-icon-search\"\n            @click=\"performSearch\"\n            :loading=\"searchLoading\">\n            搜索\n          </el-button>\n          <el-button\n            v-if=\"isSearchMode\"\n            type=\"info\"\n            icon=\"el-icon-refresh-left\"\n            @click=\"clearSearch\">\n            显示全部\n          </el-button>\n        </div>\n\n        <!-- 搜索结果提示 -->\n        <div v-if=\"isSearchMode\" class=\"search-result-tip\" style=\"margin-top: 10px;\">\n          <el-alert\n            :title=\"searchResultTitle\"\n            type=\"info\"\n            :closable=\"false\"\n            show-icon>\n          </el-alert>\n        </div>\n      </div>\n\n      <!-- 管理员未选择用户时的提示 -->\n      <div v-if=\"isAdmin && !selectedUserId\" class=\"admin-no-selection\" style=\"text-align: center; padding: 60px 20px; color: #909399;\">\n        <i class=\"el-icon-user\" style=\"font-size: 48px; margin-bottom: 20px; display: block;\"></i>\n        <h3 style=\"margin: 0 0 10px 0; color: #606266;\">请选择要查看统计的用户</h3>\n        <p style=\"margin: 0; font-size: 14px;\">选择用户后将自动加载该用户的下注统计数据</p>\n      </div>\n\n      <!-- 统计内容区域 -->\n      <div v-if=\"!isAdmin || (isAdmin && selectedUserId)\" class=\"statistics-content\">\n\n        <!-- 当前彩种无数据时的提示 -->\n        <div v-if=\"currentMethodRanking.length === 0 && currentNumberRanking.length === 0\"\n             class=\"no-data-tip\"\n             style=\"text-align: center; padding: 60px 20px; color: #909399;\">\n          <i class=\"el-icon-data-analysis\" style=\"font-size: 48px; margin-bottom: 20px; display: block;\"></i>\n          <h3 style=\"margin: 0 0 10px 0; color: #606266;\">\n            {{ activeLotteryType === 'fucai' ? '福彩3D' : '体彩排三' }} 暂无统计数据\n          </h3>\n          <p style=\"margin: 0; font-size: 14px;\">\n            {{ activeLotteryType === 'fucai' ? '切换到体彩排三查看数据' : '切换到福彩3D查看数据' }}\n          </p>\n        </div>\n          <!-- 按玩法分组的热门三位数排行 -->\n        <div v-if=\"currentNumberRanking.length > 0\" class=\"numbers-rankings-section\">\n          <h3 class=\"main-section-title\">\n            <i class=\"el-icon-data-line\"></i>\n            各玩法热门三位数排行榜\n          </h3>\n\n          <!-- 使用Grid布局，一行显示五个玩法 -->\n          <div class=\"numbers-grid\">\n            <div\n              v-for=\"methodGroup in currentNumberRanking\"\n              :key=\"methodGroup.methodId\"\n              class=\"numbers-card\">\n\n              <div class=\"numbers-card-header\">\n                <span class=\"method-name\">{{ methodGroup.methodName }}</span>\n                <span class=\"method-count\">共{{ methodGroup.ranking.length }}项</span>\n              </div>\n\n              <div class=\"numbers-ranking-list\">\n                <div\n                  v-for=\"item in methodGroup.ranking\"\n                  :key=\"item.rank\"\n                  class=\"numbers-ranking-item\">\n                  <div class=\"rank-badge\" :class=\"getRankClass(item.rank)\">\n                    {{ item.rank }}\n                  </div>\n                  <div class=\"numbers-info\">\n                    <div class=\"hot-number\">{{ item.number }}</div>\n                    <div class=\"amount\">￥{{ item.totalAmount.toFixed(0) }}</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      <div class=\"statistics-container\">\n        <!-- 按玩法分组的投注金额排行 - 两列布局 -->\n        <div v-if=\"currentMethodRanking.length > 0\" class=\"method-rankings-section\">\n          <h3 class=\"main-section-title\">\n            <i class=\"el-icon-trophy\"></i>\n            各玩法投注排行榜\n          </h3>\n\n          <!-- 使用Grid布局，一行显示两个玩法 -->\n          <div class=\"method-grid\">\n            <div\n              v-for=\"methodGroup in currentMethodRanking\"\n              :key=\"methodGroup.methodId\"\n              class=\"method-card\">\n\n              <div class=\"method-card-header\">\n                <span class=\"method-name\">{{ methodGroup.methodName }}</span>\n                <span class=\"method-count\">共{{ methodGroup.ranking.length }}项</span>\n              </div>\n\n              <div class=\"ranking-list\">\n                <div\n                  v-for=\"item in methodGroup.ranking\"\n                  :key=\"item.rank\"\n                  class=\"ranking-item\">\n                  <div class=\"rank-badge\" :class=\"getRankClass(item.rank)\">\n                    {{ item.rank }}\n                  </div>\n                  <div class=\"bet-info\">\n                    <div class=\"bet-numbers\">{{ item.betNumbers }}</div>\n                    <div class=\"amount\">￥{{ item.totalAmount.toFixed(0) }}</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n\n      </div>\n      <!-- 统计内容区域结束 -->\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"refreshData\" type=\"primary\" icon=\"el-icon-refresh\">刷新数据</el-button>\n        <el-button @click=\"close\">关闭</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport request from '@/utils/request'\n\nexport default {\n  name: 'BetStatistics',\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      dialogVisible: false,\n      methodRankingByGroup: [], // 按玩法分组的排行榜\n      numberRankingByGroup: [], // 按玩法分组的号码排行榜\n      gameMethodsData: [], // 玩法数据\n      // 管理员功能相关\n      selectedUserId: null, // 选中的用户ID\n      userList: [], // 用户列表\n      loading: false, // 加载状态\n      // 彩种相关\n      activeLotteryType: 'fucai', // 当前选中的彩种：fucai(福彩3D) 或 ticai(体彩P3)\n      // 分彩种的统计数据\n      fucaiMethodRanking: [], // 福彩投注排行\n      fucaiNumberRanking: [], // 福彩热门三位数排行\n      ticaiMethodRanking: [], // 体彩投注排行\n      ticaiNumberRanking: [], // 体彩热门三位数排行\n      // 搜索功能相关\n      searchNumber: '', // 搜索的号码\n      isSearchMode: false, // 是否处于搜索模式\n      searchLoading: false, // 搜索加载状态\n      searchResultCount: 0, // 搜索结果数量\n      // 原始完整数据（用于搜索过滤）\n      originalFucaiMethodRanking: [],\n      originalFucaiNumberRanking: [],\n      originalTicaiMethodRanking: [],\n      originalTicaiNumberRanking: []\n    }\n  },\n  computed: {\n    /** 是否是管理员 */\n    isAdmin() {\n      const userInfo = this.$store.getters.userInfo;\n      const roles = this.$store.getters.roles;\n\n      // 检查是否是超级管理员（用户ID为1）\n      if (userInfo && userInfo.userId === 1) {\n        return true;\n      }\n\n      // 检查是否有管理员角色\n      if (roles && roles.length > 0) {\n        return roles.includes('admin') || roles.includes('3d_admin');\n      }\n\n      return false;\n    },\n\n    /** 当前彩种的投注排行数据 */\n    currentMethodRanking() {\n      return this.activeLotteryType === 'fucai' ? this.fucaiMethodRanking : this.ticaiMethodRanking;\n    },\n\n    /** 当前彩种的热门三位数排行数据 */\n    currentNumberRanking() {\n      return this.activeLotteryType === 'fucai' ? this.fucaiNumberRanking : this.ticaiNumberRanking;\n    },\n\n    /** 搜索结果标题 */\n    searchResultTitle() {\n      return `搜索号码 \"${this.searchNumber}\" 的结果：共找到 ${this.searchResultCount} 个匹配项`;\n    }\n  },\n  watch: {\n    visible(val) {\n      this.dialogVisible = val;\n      if (val) {\n        // 运行测试\n        this.testThreeDigitExtraction();\n\n        // 如果是管理员，只加载用户列表和玩法数据，不自动计算统计\n        if (this.isAdmin) {\n          this.loadUserList();\n          this.loadGameMethods();\n        } else {\n          // 普通用户自动加载统计\n          this.loadGameMethods().then(async () => {\n            // 普通用户从sessionStorage获取自己的数据\n            const sysUserId = sessionStorage.getItem('sysUserId');\n            console.log('[BetStatistics] sessionStorage中的sysUserId:', sysUserId);\n\n            if (sysUserId) {\n              this.selectedUserId = parseInt(sysUserId);\n              console.log('[BetStatistics] 设置selectedUserId:', this.selectedUserId);\n            } else {\n              console.warn('[BetStatistics] 无法从sessionStorage获取sysUserId');\n            }\n            await this.calculateStatistics();\n          });\n        }\n      }\n    },\n    dialogVisible(val) {\n      this.$emit('update:visible', val);\n    }\n  },\n  methods: {\n    /** 加载玩法数据 */\n    async loadGameMethods() {\n      try {\n        // 直接从API获取玩法数据\n        const response = await request({\n          url: '/game/odds/list',\n          method: 'get',\n          params: { pageNum: 1, pageSize: 1000 }\n        });\n\n        if (response && response.rows) {\n          this.gameMethodsData = response.rows;\n         \n        } else {\n          this.gameMethodsData = [];\n          console.warn('玩法数据为空');\n        }\n      } catch (error) {\n        console.error('获取玩法数据失败:', error);\n        // 如果API失败，使用基本的玩法映射\n        this.gameMethodsData = this.getBasicMethodsData();\n      }\n    },\n    /** 获取基本玩法数据（API失败时的备用方案） */\n    getBasicMethodsData() {\n      return [\n        { methodId: 1, methodName: '独胆' },\n        { methodId: 2, methodName: '一码定位' },\n        { methodId: 3, methodName: '两码组合' },\n        { methodId: 4, methodName: '两码对子' },\n        { methodId: 5, methodName: '三码直选' },\n        { methodId: 6, methodName: '三码组选' },\n        { methodId: 7, methodName: '三码组三' },\n        { methodId: 8, methodName: '四码组六' },\n        { methodId: 9, methodName: '四码组三' },\n        { methodId: 10, methodName: '五码组六' },\n        { methodId: 11, methodName: '五码组三' },\n        { methodId: 12, methodName: '六码组六' },\n        { methodId: 13, methodName: '六码组三' },\n        { methodId: 14, methodName: '七码组六' },\n        { methodId: 15, methodName: '七码组三' },\n        { methodId: 16, methodName: '八码组六' },\n        { methodId: 17, methodName: '八码组三' },\n        { methodId: 18, methodName: '九码组六' },\n        { methodId: 19, methodName: '九码组三' },\n        { methodId: 20, methodName: '包打组六' },\n        { methodId: 21, methodName: '7或20和值' },\n        { methodId: 22, methodName: '8或19和值' },\n        { methodId: 23, methodName: '9或18和值' },\n        { methodId: 24, methodName: '10或17和值' },\n        { methodId: 25, methodName: '11或16和值' },\n        { methodId: 26, methodName: '12或15和值' },\n        { methodId: 27, methodName: '13或14和值' },\n        { methodId: 28, methodName: '直选复式' },\n        { methodId: 29, methodName: '和值单双' },\n        { methodId: 30, methodName: '和值大小' },\n        { methodId: 31, methodName: '包打组三' },\n        { methodId: 100, methodName: '三码防对' }\n      ];\n    },\n    /** 获取玩法名称 */\n    getMethodName(methodId) {\n      const method = this.gameMethodsData.find(m => m.methodId === methodId);\n      return method ? method.methodName : `玩法${methodId}`;\n    },\n    /** 获取排名样式类 */\n    getRankClass(rank) {\n      if (rank === 1) return 'rank-first';\n      if (rank === 2) return 'rank-second';\n      if (rank === 3) return 'rank-third';\n      return 'rank-other';\n    },\n    /** 计算统计数据 */\n    async calculateStatistics() {\n      const data = await this.getRecordData();\n\n\n      if (!data || data.length === 0) {\n        if (this.isAdmin && !this.selectedUserId) {\n          this.$message.warning('请选择要查看统计的用户');\n        } else {\n          this.$message.warning('暂无统计数据，请先刷新record页面');\n        }\n        this.clearAllRankingData();\n        return;\n      }\n\n      // 按彩种分别计算统计数据\n      this.calculateStatisticsByLotteryType(data);\n\n      // 调试信息\n      console.log('统计计算完成:', {\n        福彩投注排行: this.fucaiMethodRanking.length,\n        福彩热门三位数: this.fucaiNumberRanking.length,\n        体彩投注排行: this.ticaiMethodRanking.length,\n        体彩热门三位数: this.ticaiNumberRanking.length,\n        当前选中彩种: this.activeLotteryType\n      });\n    },\n    /** 获取record数据 */\n    async getRecordData() {\n      try {\n        // 如果有选择的用户ID（管理员选择的用户或普通用户自己），直接从API获取数据\n        if (this.selectedUserId) {\n          return await this.fetchUserRecordData(this.selectedUserId);\n        }\n\n        // 如果是管理员但没有选择用户，提示选择用户\n        if (this.isAdmin) {\n          this.$message.warning('请选择要查看统计的用户');\n          return [];\n        }\n\n        // 普通用户：直接从API获取当前用户数据，从sessionStorage获取用户ID\n        console.log('[BetStatistics] 普通用户，直接从API获取数据');\n\n        // 从sessionStorage获取用户ID\n        const sysUserId = sessionStorage.getItem('sysUserId');\n        console.log('[BetStatistics] 从sessionStorage获取的sysUserId:', sysUserId);\n\n        if (sysUserId) {\n          console.log('[BetStatistics] 使用sysUserId调用API:', sysUserId);\n          const data = await this.fetchUserRecordData(parseInt(sysUserId));\n          console.log('[BetStatistics] API获取到的数据:', data);\n\n          if (data && data.length > 0) {\n            console.log(`[BetStatistics] 成功加载了 ${data.length} 条统计数据`);\n          } else {\n            console.warn('[BetStatistics] API返回空数据');\n          }\n\n          return data;\n        } else {\n          console.warn('[BetStatistics] 无法从sessionStorage获取sysUserId');\n          this.$message.warning('无法获取用户信息，请重新登录');\n          return [];\n        }\n      } catch (error) {\n        console.error('解析统计数据失败:', error);\n        return [];\n      }\n    },\n    /** 清空所有排行数据 */\n    clearAllRankingData() {\n      this.fucaiMethodRanking = [];\n      this.fucaiNumberRanking = [];\n      this.ticaiMethodRanking = [];\n      this.ticaiNumberRanking = [];\n      // 清空原始数据\n      this.originalFucaiMethodRanking = [];\n      this.originalFucaiNumberRanking = [];\n      this.originalTicaiMethodRanking = [];\n      this.originalTicaiNumberRanking = [];\n      // 重置搜索状态\n      this.clearSearch();\n    },\n\n    /** 按彩种分别计算统计数据 */\n    calculateStatisticsByLotteryType(data) {\n      // 调试：查看数据结构\n      if (data.length > 0) {\n        console.log('数据样本:', data[0]);\n        console.log('可能的彩种字段:', {\n          lotteryType: data[0].lotteryType,\n          gameType: data[0].gameType,\n          lotteryId: data[0].lotteryId,\n          gameName: data[0].gameName,\n          methodName: data[0].methodName\n        });\n\n        // 统计所有可能的彩种标识\n        const lotteryTypes = [...new Set(data.map(record => record.lotteryType))];\n        const gameTypes = [...new Set(data.map(record => record.gameType))];\n        const lotteryIds = [...new Set(data.map(record => record.lotteryId))];\n        const gameNames = [...new Set(data.map(record => record.gameName))];\n\n        console.log('数据中的彩种标识:', {\n          lotteryTypes,\n          gameTypes,\n          lotteryIds,\n          gameNames\n        });\n      }\n\n      // 按彩种分组数据（使用lotteryId字段）\n      const fucaiData = data.filter(record => this.isFucaiLottery(record.lotteryId));\n      const ticaiData = data.filter(record => this.isTicaiLottery(record.lotteryId));\n\n      console.log(`福彩3D数据: ${fucaiData.length}条, 体彩P3数据: ${ticaiData.length}条`);\n\n      // 如果没有明确的彩种区分，将所有数据归类为福彩3D（兼容性处理）\n      if (fucaiData.length === 0 && ticaiData.length === 0 && data.length > 0) {\n        console.log('未检测到彩种字段，将所有数据归类为福彩3D');\n        this.fucaiMethodRanking = this.calculateMethodRankingByGroup(data);\n        this.fucaiNumberRanking = this.calculateNumberRanking(data);\n        this.ticaiMethodRanking = [];\n        this.ticaiNumberRanking = [];\n        return;\n      }\n\n      // 分别计算福彩和体彩的统计数据\n      if (fucaiData.length > 0) {\n        this.originalFucaiMethodRanking = this.calculateMethodRankingByGroup(fucaiData);\n        this.originalFucaiNumberRanking = this.calculateNumberRanking(fucaiData);\n        this.fucaiMethodRanking = [...this.originalFucaiMethodRanking];\n        this.fucaiNumberRanking = [...this.originalFucaiNumberRanking];\n      } else {\n        this.originalFucaiMethodRanking = [];\n        this.originalFucaiNumberRanking = [];\n        this.fucaiMethodRanking = [];\n        this.fucaiNumberRanking = [];\n      }\n\n      if (ticaiData.length > 0) {\n        this.originalTicaiMethodRanking = this.calculateMethodRankingByGroup(ticaiData);\n        this.originalTicaiNumberRanking = this.calculateNumberRanking(ticaiData);\n        this.ticaiMethodRanking = [...this.originalTicaiMethodRanking];\n        this.ticaiNumberRanking = [...this.originalTicaiNumberRanking];\n      } else {\n        this.originalTicaiMethodRanking = [];\n        this.originalTicaiNumberRanking = [];\n        this.ticaiMethodRanking = [];\n        this.ticaiNumberRanking = [];\n      }\n    },\n\n    /** 判断是否为福彩3D */\n    isFucaiLottery(lotteryId) {\n      // 福彩3D的lotteryId是1\n      return lotteryId === 1 || lotteryId === '1';\n    },\n\n    /** 判断是否为体彩P3 */\n    isTicaiLottery(lotteryId) {\n      // 体彩P3的lotteryId是2\n      return lotteryId === 2 || lotteryId === '2';\n    },\n\n    /** 按玩法分组计算排行榜 */\n    calculateMethodRankingByGroup(data) {\n      console.log('[calculateMethodRankingByGroup] 开始计算各玩法投注排行榜');\n\n      // 按玩法分组，相同号码合并累计金额\n      const methodGroups = {};\n\n      data.forEach(record => {\n        const methodId = record.methodId;\n        const amount = parseFloat(record.money) || 0;\n\n        if (!methodGroups[methodId]) {\n          methodGroups[methodId] = new Map(); // 使用Map来存储号码和对应的累计数据\n        }\n\n        // 解析投注号码\n        try {\n          const betNumbers = JSON.parse(record.betNumbers || '{}');\n          const numbers = betNumbers.numbers || [];\n\n          // 为每个号码累计金额\n          numbers.forEach((numberObj) => {\n            let singleNumberDisplay = '';\n\n            if (typeof numberObj === 'object' && numberObj !== null) {\n              // 处理 {a:1, b:2, c:3, d:4, e:5} 格式\n              const keys = Object.keys(numberObj).sort();\n              const values = keys.map(key => String(numberObj[key]));\n              singleNumberDisplay = values.join('');\n            } else {\n              singleNumberDisplay = String(numberObj);\n            }\n\n            // 如果号码已存在，累计金额和记录数\n            if (methodGroups[methodId].has(singleNumberDisplay)) {\n              const existing = methodGroups[methodId].get(singleNumberDisplay);\n              existing.totalAmount += amount;\n              existing.recordCount += 1;\n            } else {\n              // 新号码，创建记录\n              methodGroups[methodId].set(singleNumberDisplay, {\n                betNumbers: singleNumberDisplay,\n                totalAmount: amount,\n                recordCount: 1\n              });\n            }\n          });\n        } catch (error) {\n          console.warn('解析投注号码失败:', record.betNumbers, error);\n          // 如果解析失败，使用原始数据\n          const fallbackNumbers = record.betNumbers || '未知号码';\n\n          if (methodGroups[methodId].has(fallbackNumbers)) {\n            const existing = methodGroups[methodId].get(fallbackNumbers);\n            existing.totalAmount += amount;\n            existing.recordCount += 1;\n          } else {\n            methodGroups[methodId].set(fallbackNumbers, {\n              betNumbers: fallbackNumbers,\n              totalAmount: amount,\n              recordCount: 1\n            });\n          }\n        }\n      });\n\n      console.log('[calculateMethodRankingByGroup] 开始生成排行榜');\n\n      // 为每个玩法生成排行榜并返回\n      return Object.entries(methodGroups).map(([methodId, numbersMap]) => {\n        // 将Map转换为数组并按总金额降序排序\n        const ranking = Array.from(numbersMap.values())\n          .sort((a, b) => b.totalAmount - a.totalAmount)\n          .map((item, index) => ({\n            rank: index + 1,\n            betNumbers: item.betNumbers,\n            totalAmount: item.totalAmount,\n            recordCount: item.recordCount\n          }));\n\n        console.log(`[calculateMethodRankingByGroup] 玩法${methodId}(${this.getMethodName(parseInt(methodId))})：${ranking.length}个不同号码`);\n\n        return {\n          methodId: parseInt(methodId),\n          methodName: this.getMethodName(parseInt(methodId)),\n          ranking\n        };\n      }).filter(group => group.ranking.length > 0); // 只显示有数据的玩法\n    },\n    /** 格式化投注号码用于显示 */\n    formatBetNumbersForDisplay(betNumbersKey) {\n      try {\n        const numbers = JSON.parse(betNumbersKey);\n        if (Array.isArray(numbers) && numbers.length > 0) {\n          return numbers.map(num => {\n            if (typeof num === 'object' && num !== null) {\n              // 处理 {a:1, b:2, c:3, d:4, e:5} 格式\n              const keys = Object.keys(num).sort();\n              const values = keys.map(key => String(num[key]));\n              return values.join('');\n            }\n            return String(num);\n          }).join(', ');\n        }\n      } catch (error) {\n        // 如果解析失败，直接返回原始字符串\n      }\n      return betNumbersKey.length > 20 ? betNumbersKey.substring(0, 20) + '...' : betNumbersKey;\n    },\n    /** 按玩法分组计算号码排行 - 只使用money字段 */\n    calculateNumberRanking(data) {\n      // 初始化返回数组\n      const numberRankingByGroup = [];\n\n      // 按玩法分组统计\n      const methodGroups = {};\n\n      data.forEach(record => {\n        try {\n          const methodId = record.methodId;\n          const money = parseFloat(record.money || 0);\n          const betNumbers = JSON.parse(record.betNumbers || '{}');\n          const numbers = betNumbers.numbers || [];\n\n          // 初始化玩法分组\n          if (!methodGroups[methodId]) {\n            methodGroups[methodId] = {\n              methodTotal: 0,\n              threeDigitMap: new Map()\n            };\n          }\n\n          methodGroups[methodId].methodTotal += money;\n\n          // 处理每个投注号码\n          numbers.forEach(numberObj => {\n            const threeDigits = this.extractThreeDigitNumbers(numberObj);\n\n            // 对当前号码的三位数组合去重（避免同一号码内重复组合多次累计）\n            const uniqueNormalizedDigits = new Set();\n\n            threeDigits.forEach(digit => {\n              // 归一化三位数：将数字按从小到大排序作为统一key\n              const normalizedDigit = this.normalizeThreeDigits(digit);\n              uniqueNormalizedDigits.add(normalizedDigit);\n            });\n\n            // 每个归一化后的三位数累加金额\n            uniqueNormalizedDigits.forEach(normalizedDigit => {\n              const currentAmount = methodGroups[methodId].threeDigitMap.get(normalizedDigit) || 0;\n              methodGroups[methodId].threeDigitMap.set(normalizedDigit, currentAmount + money);\n            });\n          });\n\n        } catch (error) {\n          console.error('解析失败:', error);\n        }\n      });\n\n\n\n      // 生成排行榜\n   \n      this.numberRankingByGroup = [];\n\n      Object.entries(methodGroups).forEach(([methodId, group]) => {\n        // 转换Map为数组并排序，显示所有数据\n        const sortedThreeDigits = Array.from(group.threeDigitMap.entries())\n          .sort(([,a], [,b]) => b - a);\n\n        const ranking = sortedThreeDigits.map(([number, amount], index) => ({\n          rank: index + 1,\n          number,\n          count: 1,\n          totalAmount: amount,\n          percentage: group.methodTotal > 0 ? ((amount / group.methodTotal) * 100).toFixed(1) : '0.0'\n        }));\n\n        if (ranking.length > 0) {\n          numberRankingByGroup.push({\n            methodId: parseInt(methodId),\n            methodName: this.getMethodName(parseInt(methodId)),\n            ranking\n          });\n        }\n      });\n\n      return numberRankingByGroup;\n    },\n    /** 从投注号码中提取所有可能的三位数组合 */\n    extractThreeDigitNumbers(numberObj) {\n      const numbers = [];\n\n      // 处理不同的号码格式\n      if (typeof numberObj === 'string') {\n        // 直接是字符串格式的号码\n        const cleanStr = numberObj.replace(/\\D/g, ''); // 移除非数字字符\n        this.generateThreeDigitCombinations(cleanStr.split(''), numbers);\n      } else if (typeof numberObj === 'object' && numberObj !== null) {\n        // 对象格式 {a: 1, b: 2, c: 3, d: 4, e: 5}\n        const keys = Object.keys(numberObj).sort();\n        const digits = keys.map(key => String(numberObj[key]));\n\n    \n\n        // 生成所有可能的三位数组合\n        this.generateThreeDigitCombinations(digits, numbers);\n      }\n\n      return numbers;\n    },\n    /** 生成所有可能的三位数组合 */\n    generateThreeDigitCombinations(digits, numbers) {\n      const n = digits.length;\n\n      // 如果数字少于3个，无法组成三位数\n      if (n < 3) {\n        return;\n      }\n\n      // 生成所有可能的三位数组合（C(n,3)）\n      for (let i = 0; i < n - 2; i++) {\n        for (let j = i + 1; j < n - 1; j++) {\n          for (let k = j + 1; k < n; k++) {\n            const threeDigit = digits[i] + digits[j] + digits[k];\n            if (/^\\d{3}$/.test(threeDigit)) {\n              numbers.push(threeDigit);\n            }\n          }\n        }\n      }\n    },\n\n    /** 归一化三位数：将数字按从小到大排序，统一相同数字的不同排列 */\n    normalizeThreeDigits(threeDigit) {\n      // 将三位数字符串转换为数组，排序后重新组合\n      return threeDigit.split('').sort().join('');\n    },\n\n    /** 搜索输入处理 */\n    handleSearchInput(value) {\n      // 只允许输入数字\n      this.searchNumber = value.replace(/\\D/g, '');\n    },\n\n    /** 执行搜索 */\n    performSearch() {\n      if (!this.searchNumber.trim()) {\n        this.$message.warning('请输入要搜索的号码');\n        return;\n      }\n\n      this.searchLoading = true;\n      this.isSearchMode = true;\n\n      try {\n        // 搜索当前彩种的数据\n        const searchResults = this.searchInRankingData(this.searchNumber);\n\n        // 更新显示数据为搜索结果\n        if (this.activeLotteryType === 'fucai') {\n          this.fucaiMethodRanking = searchResults.methodRanking;\n          this.fucaiNumberRanking = searchResults.numberRanking;\n        } else {\n          this.ticaiMethodRanking = searchResults.methodRanking;\n          this.ticaiNumberRanking = searchResults.numberRanking;\n        }\n\n        // 计算搜索结果数量\n        this.searchResultCount = this.calculateSearchResultCount(searchResults);\n\n        this.$message.success(`搜索完成，找到 ${this.searchResultCount} 个匹配项`);\n      } catch (error) {\n        console.error('搜索失败:', error);\n        this.$message.error('搜索失败，请重试');\n      } finally {\n        this.searchLoading = false;\n      }\n    },\n\n    /** 清除搜索 */\n    clearSearch() {\n      this.searchNumber = '';\n      this.isSearchMode = false;\n      this.searchResultCount = 0;\n\n      // 恢复原始数据\n      this.fucaiMethodRanking = [...this.originalFucaiMethodRanking];\n      this.fucaiNumberRanking = [...this.originalFucaiNumberRanking];\n      this.ticaiMethodRanking = [...this.originalTicaiMethodRanking];\n      this.ticaiNumberRanking = [...this.originalTicaiNumberRanking];\n    },\n\n    /** 在排行数据中搜索包含指定号码的项目 */\n    searchInRankingData(searchNumber) {\n      const originalMethodRanking = this.activeLotteryType === 'fucai'\n        ? this.originalFucaiMethodRanking\n        : this.originalTicaiMethodRanking;\n\n      const originalNumberRanking = this.activeLotteryType === 'fucai'\n        ? this.originalFucaiNumberRanking\n        : this.originalTicaiNumberRanking;\n\n      // 搜索投注排行榜\n      const filteredMethodRanking = originalMethodRanking.map(methodGroup => {\n        const filteredRanking = methodGroup.ranking.filter(item => {\n          return this.isNumberMatch(item.betNumbers, searchNumber);\n        });\n\n        return {\n          ...methodGroup,\n          ranking: filteredRanking\n        };\n      }).filter(methodGroup => methodGroup.ranking.length > 0);\n\n      // 搜索热门三位数排行榜（使用专门的三位数匹配逻辑）\n      const filteredNumberRanking = originalNumberRanking.map(methodGroup => {\n        const filteredRanking = methodGroup.ranking.filter(item => {\n          return this.isThreeDigitMatch(item.number, searchNumber);\n        });\n\n        return {\n          ...methodGroup,\n          ranking: filteredRanking\n        };\n      }).filter(methodGroup => methodGroup.ranking.length > 0);\n\n      return {\n        methodRanking: filteredMethodRanking,\n        numberRanking: filteredNumberRanking\n      };\n    },\n\n    /** 判断号码是否匹配搜索条件 */\n    isNumberMatch(targetNumber, searchNumber) {\n      if (!targetNumber || !searchNumber) return false;\n\n      const target = String(targetNumber);\n      const search = String(searchNumber);\n      const searchDigits = search.split('');\n      const targetDigits = target.split('');\n\n      // 核心逻辑：检查搜索号码中的每个数字是否都在目标号码中出现\n      // 这样可以匹配各种长度的目标号码\n\n      // 方案1：任意匹配 - 搜索号码中的任一数字在目标号码中出现即匹配\n      // 适用于独胆等单数字玩法\n      const hasAnyDigit = searchDigits.some(digit => targetDigits.includes(digit));\n\n      // 方案2：完全匹配 - 搜索号码中的所有数字都在目标号码中出现\n      // 适用于多数字组合玩法\n      const hasAllDigits = searchDigits.every(digit => targetDigits.includes(digit));\n\n      // 方案3：精确匹配 - 目标号码包含搜索号码的连续子串\n      const hasExactSequence = target.includes(search);\n\n      // 根据不同情况采用不同的匹配策略\n      if (search.length === 1) {\n        // 单数字搜索：只要目标号码包含该数字即匹配\n        return hasAnyDigit;\n      } else if (search.length === 2) {\n        // 双数字搜索：优先精确匹配，其次任意匹配\n        return hasExactSequence || hasAnyDigit;\n      } else if (search.length >= 3) {\n        // 三位数及以上搜索：采用多种匹配策略\n\n        // 策略1：精确序列匹配（如搜索125，目标12534匹配）\n        if (hasExactSequence) {\n          return true;\n        }\n\n        // 策略2：对于短目标号码（1-2位），采用任意匹配\n        if (target.length <= 2) {\n          return hasAnyDigit;\n        }\n\n        // 策略3：对于三位数目标，检查是否为相同数字组合（归一化比较）\n        if (target.length === 3 && search.length === 3) {\n          const normalizedSearch = this.normalizeThreeDigits(search);\n          const normalizedTarget = this.normalizeThreeDigits(target);\n          return normalizedSearch === normalizedTarget;\n        }\n\n        // 策略4：对于长目标号码，要求包含所有搜索数字\n        if (target.length >= search.length) {\n          return hasAllDigits;\n        }\n\n        // 策略5：对于中等长度目标号码，采用任意匹配\n        return hasAnyDigit;\n      }\n\n      return false;\n    },\n\n    /** 专门用于三位数排行榜的匹配逻辑 */\n    isThreeDigitMatch(targetThreeDigit, searchNumber) {\n      if (!targetThreeDigit || !searchNumber) return false;\n\n      const target = String(targetThreeDigit);\n      const search = String(searchNumber);\n\n      // 三位数排行榜的目标都是3位数，需要特殊处理\n      if (target.length !== 3) return false;\n\n      if (search.length === 1) {\n        // 搜索单数字：三位数中包含该数字即匹配\n        return target.includes(search);\n      } else if (search.length === 2) {\n        // 搜索双数字：三位数中包含这两个数字即匹配\n        const [digit1, digit2] = search.split('');\n        return target.includes(digit1) && target.includes(digit2);\n      } else if (search.length === 3) {\n        // 搜索三位数：必须是相同的数字组合（归一化后比较）\n        const normalizedSearch = this.normalizeThreeDigits(search);\n        const normalizedTarget = this.normalizeThreeDigits(target);\n        return normalizedSearch === normalizedTarget;\n      } else if (search.length > 3) {\n        // 搜索超过三位数：检查三位数是否包含搜索号码中的任意三个数字组合\n        const searchDigits = search.split('');\n\n        // 生成搜索号码的所有三位数组合，看是否有匹配的\n        for (let i = 0; i < searchDigits.length - 2; i++) {\n          for (let j = i + 1; j < searchDigits.length - 1; j++) {\n            for (let k = j + 1; k < searchDigits.length; k++) {\n              const searchCombo = searchDigits[i] + searchDigits[j] + searchDigits[k];\n              const normalizedSearchCombo = this.normalizeThreeDigits(searchCombo);\n              const normalizedTarget = this.normalizeThreeDigits(target);\n              if (normalizedSearchCombo === normalizedTarget) {\n                return true;\n              }\n            }\n          }\n        }\n        return false;\n      }\n\n      return false;\n    },\n\n    /** 计算搜索结果数量 */\n    calculateSearchResultCount(searchResults) {\n      let count = 0;\n\n      // 统计投注排行榜匹配项\n      searchResults.methodRanking.forEach(methodGroup => {\n        count += methodGroup.ranking.length;\n      });\n\n      // 统计热门三位数排行榜匹配项\n      searchResults.numberRanking.forEach(methodGroup => {\n        count += methodGroup.ranking.length;\n      });\n\n      return count;\n    },\n    /** 刷新数据 */\n    async refreshData() {\n      await this.calculateStatistics();\n      this.$message.success('数据已刷新');\n    },\n    /** 测试三位数组合提取逻辑 */\n    testThreeDigitExtraction() {\n      console.log('=== 测试三位数组合提取和归一化逻辑 ===');\n\n      // 测试不同长度的号码\n      const testCases = [\n        {a: 1, b: 2, c: 3},                    // 三位数\n        {a: 1, b: 2, c: 3, d: 4},             // 四位数\n        {a: 0, b: 1, c: 2, d: 3, e: 4, f: 5}, // 六位数\n        {a: 1, b: 2, c: 3, d: 4, e: 5},       // 五位数\n      ];\n\n      testCases.forEach((testCase, index) => {\n        console.log(`\\n测试用例 ${index + 1}:`, testCase);\n        const threeDigits = this.extractThreeDigitNumbers(testCase);\n        console.log('提取的三位数组合:', threeDigits);\n\n        // 测试归一化效果\n        const normalizedSet = new Set();\n        threeDigits.forEach(digit => {\n          const normalized = this.normalizeThreeDigits(digit);\n          normalizedSet.add(normalized);\n          console.log(`${digit} -> ${normalized}`);\n        });\n        console.log('归一化后的唯一组合:', Array.from(normalizedSet));\n      });\n\n      console.log('=== 测试完成 ===');\n    },\n    /** 计算组合数 C(n,r) */\n    calculateCombinations(n, r) {\n      if (r > n) return 0;\n      if (r === 0 || r === n) return 1;\n\n      let result = 1;\n      for (let i = 0; i < r; i++) {\n        result = result * (n - i) / (i + 1);\n      }\n      return Math.round(result);\n    },\n    /** 关闭对话框 */\n    /** 加载用户列表 */\n    async loadUserList() {\n      try {\n        // 先获取所有用户\n        const userResponse = await request({\n          url: '/system/user/list',\n          method: 'get',\n          params: { pageNum: 1, pageSize: 1000 }\n        });\n\n        if (!userResponse || !userResponse.rows) {\n          this.userList = [];\n          return;\n        }\n\n        const allUsers = userResponse.rows.filter(user => user.status === '0'); // 只获取正常状态的用户\n\n        // 获取有下注记录的用户ID列表\n        const recordResponse = await request({\n          url: '/game/record/list',\n          method: 'get',\n          params: {\n            pageNum: 1,\n            pageSize: 100000 // 获取所有记录来统计用户\n          }\n        });\n\n        if (recordResponse && recordResponse.rows) {\n          // 统计每个用户的下注记录数量\n          const userRecordCounts = {};\n          recordResponse.rows.forEach(record => {\n            const userId = record.sysUserId;\n            userRecordCounts[userId] = (userRecordCounts[userId] || 0) + 1;\n          });\n\n          // 只保留有下注记录的用户，并添加记录数量信息\n          this.userList = allUsers\n            .filter(user => userRecordCounts[user.userId])\n            .map(user => ({\n              ...user,\n              recordCount: userRecordCounts[user.userId] || 0\n            }))\n            .sort((a, b) => b.recordCount - a.recordCount); // 按下注记录数量降序排列\n\n          console.log(`加载用户列表完成，共 ${this.userList.length} 个有下注记录的用户`);\n        } else {\n          this.userList = [];\n          console.warn('未获取到下注记录数据');\n        }\n\n      } catch (error) {\n        console.error('获取用户列表失败:', error);\n        this.$message.error('获取用户列表失败');\n        this.userList = [];\n      }\n    },\n\n    /** 获取指定用户的记录数据 */\n    async fetchUserRecordData(userId) {\n      try {\n        this.loading = true;\n        const response = await request({\n          url: '/game/record/list',\n          method: 'get',\n          params: {\n            pageNum: 1,\n            pageSize: 100000,\n            sysUserId: userId\n          }\n        });\n\n        if (response && response.rows) {\n          return response.rows;\n        } else {\n          return [];\n        }\n      } catch (error) {\n        console.error('获取用户记录数据失败:', error);\n        this.$message.error('获取用户记录数据失败');\n        return [];\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    /** 用户选择变化处理 */\n    async handleUserChange(userId) {\n      // 清空当前统计数据\n      this.methodRankingByGroup = [];\n      this.numberRankingByGroup = [];\n\n      if (userId) {\n        // 直接加载选中用户的统计数据\n        await this.calculateStatistics();\n        this.$message.success(`已加载用户 ${this.getCurrentUserName()} 的统计数据`);\n      }\n    },\n\n    /** 获取当前选中用户的名称 */\n    getCurrentUserName() {\n      if (!this.selectedUserId) return '';\n      const user = this.userList.find(u => u.userId === this.selectedUserId);\n      return user ? (user.nickName || user.userName) : '';\n    },\n\n    /** 彩种切换处理 */\n    handleLotteryTypeChange(tab) {\n      console.log(`切换到彩种: ${tab.name === 'fucai' ? '福彩3D' : '体彩P3'}`);\n      // 切换彩种时，计算属性会自动更新显示的数据\n    },\n\n    /** 刷新统计数据 */\n    async refreshStatistics() {\n      if (this.isAdmin && !this.selectedUserId) {\n        this.$message.warning('请先选择要查看统计的用户');\n        return;\n      }\n\n      try {\n        await this.loadGameMethods();\n        await this.calculateStatistics();\n        this.$message.success('统计数据已刷新');\n      } catch (error) {\n        console.error('刷新统计失败:', error);\n        this.$message.error('刷新统计失败');\n      }\n    },\n\n    close() {\n      this.dialogVisible = false;\n      // 清空选择状态\n      this.selectedUserId = null;\n      this.activeLotteryType = 'fucai'; // 重置为福彩\n      // 清空所有统计数据\n      this.clearAllRankingData();\n    }\n  }\n}\n</script>\n\n<style scoped>\n/* 管理员控制区域样式 */\n.admin-controls {\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n}\n\n.admin-controls .el-select {\n  background: white;\n}\n\n/* 彩种标签页样式 */\n.lottery-type-tabs {\n  margin-left: 20px;\n  flex: 1;\n}\n\n.lottery-type-tabs-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header {\n  border-bottom: none;\n  margin: 0;\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__nav,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__nav {\n  border: none;\n  border-radius: 8px;\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n  padding: 4px;\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__item,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__item {\n  border: none;\n  background: transparent;\n  color: #606266;\n  font-weight: 500;\n  padding: 8px 16px;\n  margin-right: 4px;\n  border-radius: 6px;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__item:hover,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__item:hover {\n  color: #409eff;\n  background: rgba(64, 158, 255, 0.1);\n  transform: translateY(-1px);\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__item.is-active,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__item.is-active {\n  color: white;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  font-weight: 600;\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n  transform: translateY(-1px);\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__item.is-active::before,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__item.is-active::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, transparent 100%);\n  pointer-events: none;\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__item i,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__item i {\n  margin-right: 6px;\n  font-size: 16px;\n}\n\n.lottery-type-tabs .el-tabs__content,\n.lottery-type-tabs-container .el-tabs__content {\n  display: none; /* 隐藏内容区域，因为我们只用标签页切换 */\n}\n\n.statistics-dialog {\n  .el-dialog {\n    border-radius: 12px;\n  }\n  \n  .el-dialog__header {\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    color: white;\n    padding: 20px 24px;\n  }\n  \n  .el-dialog__title {\n    color: white;\n    font-weight: 600;\n    font-size: 18px;\n  }\n}\n\n.statistics-container {\n  padding: 10px 0;\n}\n\n.main-section-title {\n  display: flex;\n  align-items: center;\n  font-size: 16px;\n  font-weight: 600;\n  color: #2c3e50;\n  margin-bottom: 8px;\n  padding-bottom: 4px;\n  border-bottom: 1px solid #ecf0f1;\n}\n\n.main-section-title i {\n  margin-right: 4px;\n  color: #667eea;\n  font-size: 14px;\n}\n\n/* 玩法排行榜样式 */\n.method-rankings-section {\n  margin-bottom: 15px;\n}\n\n.method-grid {\n  display: grid;\n  grid-template-columns: repeat(5, 1fr);\n  gap: 6px;\n}\n\n@media (max-width: 1400px) {\n  .method-grid {\n    grid-template-columns: repeat(4, 1fr);\n  }\n}\n\n@media (max-width: 1100px) {\n  .method-grid {\n    grid-template-columns: repeat(3, 1fr);\n  }\n}\n\n@media (max-width: 800px) {\n  .method-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n\n@media (max-width: 500px) {\n  .method-grid {\n    grid-template-columns: 1fr;\n  }\n}\n\n.method-card {\n  background: white;\n  border-radius: 10px;\n  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.12);\n  overflow: hidden;\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n  min-width: 0; /* 防止内容溢出 */\n  min-height: 200px; /* 最小高度 */\n  max-height: 500px; /* 增加最大高度以适应更多数据 */\n}\n\n.method-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.18);\n}\n\n.method-card-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 8px 12px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.method-name {\n  font-size: 15px;\n  font-weight: 700;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.method-count {\n  font-size: 13px;\n  opacity: 0.9;\n}\n\n.ranking-list {\n  padding: 8px;\n  max-height: 420px; /* 增加列表高度以适应更多数据 */\n  overflow-y: auto; /* 添加垂直滚动条 */\n}\n\n/* 滚动条样式 */\n.ranking-list::-webkit-scrollbar {\n  width: 4px;\n}\n\n.ranking-list::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 2px;\n}\n\n.ranking-list::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 2px;\n}\n\n.ranking-list::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n/* 热门三位数排行榜滚动条样式 */\n.numbers-ranking-list::-webkit-scrollbar {\n  width: 4px;\n}\n\n.numbers-ranking-list::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 2px;\n}\n\n.numbers-ranking-list::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 2px;\n}\n\n.numbers-ranking-list::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n.ranking-item {\n  display: flex;\n  align-items: center;\n  padding: 4px 8px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.ranking-item:last-child {\n  border-bottom: none;\n}\n\n.rank-badge {\n  min-width: 18px;\n  height: 18px;\n  padding: 0 4px;\n  border-radius: 9px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  font-size: 10px;\n  color: white;\n  flex: 0 0 auto;\n  margin-right: 8px;\n  white-space: nowrap;\n}\n\n.rank-first {\n  background: linear-gradient(135deg, #ff6b6b, #ee5a24);\n}\n\n.rank-second {\n  background: linear-gradient(135deg, #feca57, #ff9ff3);\n}\n\n.rank-third {\n  background: linear-gradient(135deg, #48dbfb, #0abde3);\n}\n\n.rank-other {\n  background: linear-gradient(135deg, #a4b0be, #747d8c);\n}\n\n.bet-info {\n  display: flex;\n  align-items: center;\n  flex: 1;\n  min-width: 0;\n}\n\n.bet-numbers {\n  font-size: 15px;\n  font-weight: 900;\n  color: #ffffff;\n  font-family: 'Courier New', monospace;\n  background: linear-gradient(135deg, #667eea, #764ba2);\n  padding: 3px 8px;\n  border-radius: 5px;\n  text-align: center;\n  box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);\n  letter-spacing: 0.5px;\n  flex: 2;\n  margin-right: 8px;\n}\n\n.amount {\n  color: #e74c3c;\n  font-weight: 700;\n  font-size: 14px;\n  white-space: nowrap;\n  flex: 1;\n  text-align: right;\n}\n\n.record-count {\n  color: #95a5a6;\n  font-size: 11px;\n  text-align: right;\n  margin-top: 2px;\n}\n\n/* 热门三位数样式 */\n.numbers-section {\n  margin-bottom: 30px;\n}\n\n.ranking-table {\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.amount-text {\n  font-weight: 600;\n  color: #e74c3c;\n}\n\n.percentage-text {\n  font-weight: 500;\n  color: #3498db;\n}\n\n/* 热门三位数卡片样式 */\n.numbers-rankings-section {\n  margin-bottom: 15px;\n}\n\n.numbers-grid {\n  display: grid;\n  grid-template-columns: repeat(5, 1fr);\n  gap: 6px;\n}\n\n@media (max-width: 1400px) {\n  .numbers-grid {\n    grid-template-columns: repeat(4, 1fr);\n  }\n}\n\n@media (max-width: 1100px) {\n  .numbers-grid {\n    grid-template-columns: repeat(3, 1fr);\n  }\n}\n\n@media (max-width: 800px) {\n  .numbers-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n\n@media (max-width: 500px) {\n  .numbers-grid {\n    grid-template-columns: 1fr;\n  }\n}\n\n.numbers-card {\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n  min-width: 0;\n  min-height: 120px;\n  max-height: 400px; /* 增加最大高度以适应更多数据 */\n}\n\n.numbers-card:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.numbers-card-header {\n  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);\n  color: white;\n  padding: 4px 6px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.numbers-ranking-list {\n  padding: 4px;\n  max-height: 320px; /* 限制列表高度以适应更多数据 */\n  overflow-y: auto; /* 添加垂直滚动条 */\n}\n\n.numbers-ranking-item {\n  display: flex;\n  align-items: center;\n  padding: 2px 4px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.numbers-ranking-item:last-child {\n  border-bottom: none;\n}\n\n.numbers-info {\n  display: flex;\n  align-items: center;\n  flex: 1;\n  min-width: 0;\n}\n\n.numbers-info .hot-number {\n  font-family: 'Courier New', monospace;\n  font-weight: 900;\n  font-size: 13px;\n  color: #ffffff;\n  background: linear-gradient(135deg, #27ae60, #2ecc71);\n  padding: 2px 4px;\n  border-radius: 3px;\n  text-align: center;\n  flex: 2;\n  margin-right: 4px;\n}\n\n.numbers-info .amount {\n  color: #e74c3c;\n  font-weight: 700;\n  font-size: 13px;\n  white-space: nowrap;\n  flex: 1;\n  text-align: right;\n}\n\n\n\n.dialog-footer {\n  text-align: right;\n  padding-top: 20px;\n  border-top: 1px solid #ecf0f1;\n}\n\n/* 搜索功能样式 */\n.search-container {\n  padding: 15px 20px;\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n  border-radius: 8px;\n  border: 1px solid #e4e7ed;\n}\n\n.search-box {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-wrap: wrap;\n  gap: 10px;\n}\n\n.search-box .el-input {\n  max-width: 300px;\n  flex: 1;\n  min-width: 200px;\n}\n\n.search-box .el-button {\n  border-radius: 6px;\n  font-weight: 500;\n  transition: all 0.3s ease;\n}\n\n.search-box .el-button:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.search-result-tip {\n  text-align: center;\n}\n\n.search-result-tip .el-alert {\n  border-radius: 6px;\n}\n\n.search-result-tip .el-alert--info {\n  background-color: rgba(64, 158, 255, 0.1);\n  border-color: rgba(64, 158, 255, 0.2);\n}\n\n/* 响应式调整 */\n@media (max-width: 768px) {\n  .search-box {\n    flex-direction: column;\n    align-items: stretch;\n  }\n\n  .search-box .el-input {\n    max-width: none;\n    width: 100%;\n  }\n\n  .search-box .el-button {\n    width: 100%;\n    margin-top: 5px;\n  }\n}\n</style>\n"]}]}