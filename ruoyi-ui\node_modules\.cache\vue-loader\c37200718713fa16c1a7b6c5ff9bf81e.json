{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetStatistics.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetStatistics.vue", "mtime": 1756000107447}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750942928175}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750942928768}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCByZXF1ZXN0IGZyb20gJ0AvdXRpbHMvcmVxdWVzdCcKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnQmV0U3RhdGlzdGljcycsCiAgcHJvcHM6IHsKICAgIHZpc2libGU6IHsKICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgZGVmYXVsdDogZmFsc2UKICAgIH0KICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgbWV0aG9kUmFua2luZ0J5R3JvdXA6IFtdLCAvLyDmjInnjqnms5XliIbnu4TnmoTmjpLooYzmppwKICAgICAgbnVtYmVyUmFua2luZ0J5R3JvdXA6IFtdLCAvLyDmjInnjqnms5XliIbnu4TnmoTlj7fnoIHmjpLooYzmppwKICAgICAgZ2FtZU1ldGhvZHNEYXRhOiBbXSwgLy8g546p5rOV5pWw5o2uCiAgICAgIC8vIOeuoeeQhuWRmOWKn+iDveebuOWFswogICAgICBzZWxlY3RlZFVzZXJJZDogbnVsbCwgLy8g6YCJ5Lit55qE55So5oi3SUQKICAgICAgdXNlckxpc3Q6IFtdLCAvLyDnlKjmiLfliJfooagKICAgICAgbG9hZGluZzogZmFsc2UsIC8vIOWKoOi9veeKtuaAgQogICAgICAvLyDlvannp43nm7jlhbMKICAgICAgYWN0aXZlTG90dGVyeVR5cGU6ICdmdWNhaScsIC8vIOW9k+WJjemAieS4reeahOW9qeenje+8mmZ1Y2FpKOemj+W9qTNEKSDmiJYgdGljYWko5L2T5b2pUDMpCiAgICAgIC8vIOWIhuW9qeenjeeahOe7n+iuoeaVsOaNrgogICAgICBmdWNhaU1ldGhvZFJhbmtpbmc6IFtdLCAvLyDnpo/lvanmipXms6jmjpLooYwKICAgICAgZnVjYWlOdW1iZXJSYW5raW5nOiBbXSwgLy8g56aP5b2p54Ot6Zeo5LiJ5L2N5pWw5o6S6KGMCiAgICAgIHRpY2FpTWV0aG9kUmFua2luZzogW10sIC8vIOS9k+W9qeaKleazqOaOkuihjAogICAgICB0aWNhaU51bWJlclJhbmtpbmc6IFtdIC8vIOS9k+W9qeeDremXqOS4ieS9jeaVsOaOkuihjAogICAgfQogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIC8qKiDmmK/lkKbmmK/nrqHnkIblkZggKi8KICAgIGlzQWRtaW4oKSB7CiAgICAgIGNvbnN0IHVzZXJJbmZvID0gdGhpcy4kc3RvcmUuZ2V0dGVycy51c2VySW5mbzsKICAgICAgY29uc3Qgcm9sZXMgPSB0aGlzLiRzdG9yZS5nZXR0ZXJzLnJvbGVzOwoKICAgICAgLy8g5qOA5p+l5piv5ZCm5piv6LaF57qn566h55CG5ZGY77yI55So5oi3SUTkuLox77yJCiAgICAgIGlmICh1c2VySW5mbyAmJiB1c2VySW5mby51c2VySWQgPT09IDEpIHsKICAgICAgICByZXR1cm4gdHJ1ZTsKICAgICAgfQoKICAgICAgLy8g5qOA5p+l5piv5ZCm5pyJ566h55CG5ZGY6KeS6ImyCiAgICAgIGlmIChyb2xlcyAmJiByb2xlcy5sZW5ndGggPiAwKSB7CiAgICAgICAgcmV0dXJuIHJvbGVzLmluY2x1ZGVzKCdhZG1pbicpIHx8IHJvbGVzLmluY2x1ZGVzKCczZF9hZG1pbicpOwogICAgICB9CgogICAgICByZXR1cm4gZmFsc2U7CiAgICB9LAoKICAgIC8qKiDlvZPliY3lvannp43nmoTmipXms6jmjpLooYzmlbDmja4gKi8KICAgIGN1cnJlbnRNZXRob2RSYW5raW5nKCkgewogICAgICByZXR1cm4gdGhpcy5hY3RpdmVMb3R0ZXJ5VHlwZSA9PT0gJ2Z1Y2FpJyA/IHRoaXMuZnVjYWlNZXRob2RSYW5raW5nIDogdGhpcy50aWNhaU1ldGhvZFJhbmtpbmc7CiAgICB9LAoKICAgIC8qKiDlvZPliY3lvannp43nmoTng63pl6jkuInkvY3mlbDmjpLooYzmlbDmja4gKi8KICAgIGN1cnJlbnROdW1iZXJSYW5raW5nKCkgewogICAgICByZXR1cm4gdGhpcy5hY3RpdmVMb3R0ZXJ5VHlwZSA9PT0gJ2Z1Y2FpJyA/IHRoaXMuZnVjYWlOdW1iZXJSYW5raW5nIDogdGhpcy50aWNhaU51bWJlclJhbmtpbmc7CiAgICB9CiAgfSwKICB3YXRjaDogewogICAgdmlzaWJsZSh2YWwpIHsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdmFsOwogICAgICBpZiAodmFsKSB7CiAgICAgICAgLy8g6L+Q6KGM5rWL6K+VCiAgICAgICAgdGhpcy50ZXN0VGhyZWVEaWdpdEV4dHJhY3Rpb24oKTsKCiAgICAgICAgLy8g5aaC5p6c5piv566h55CG5ZGY77yM5Y+q5Yqg6L2955So5oi35YiX6KGo5ZKM546p5rOV5pWw5o2u77yM5LiN6Ieq5Yqo6K6h566X57uf6K6hCiAgICAgICAgaWYgKHRoaXMuaXNBZG1pbikgewogICAgICAgICAgdGhpcy5sb2FkVXNlckxpc3QoKTsKICAgICAgICAgIHRoaXMubG9hZEdhbWVNZXRob2RzKCk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIC8vIOaZrumAmueUqOaIt+iHquWKqOWKoOi9vee7n+iuoQogICAgICAgICAgdGhpcy5sb2FkR2FtZU1ldGhvZHMoKS50aGVuKGFzeW5jICgpID0+IHsKICAgICAgICAgICAgLy8g5pmu6YCa55So5oi35LuOc2Vzc2lvblN0b3JhZ2Xojrflj5boh6rlt7HnmoTmlbDmja4KICAgICAgICAgICAgY29uc3Qgc3lzVXNlcklkID0gc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgnc3lzVXNlcklkJyk7CiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdbQmV0U3RhdGlzdGljc10gc2Vzc2lvblN0b3JhZ2XkuK3nmoRzeXNVc2VySWQ6Jywgc3lzVXNlcklkKTsKCiAgICAgICAgICAgIGlmIChzeXNVc2VySWQpIHsKICAgICAgICAgICAgICB0aGlzLnNlbGVjdGVkVXNlcklkID0gcGFyc2VJbnQoc3lzVXNlcklkKTsKICAgICAgICAgICAgICBjb25zb2xlLmxvZygnW0JldFN0YXRpc3RpY3NdIOiuvue9rnNlbGVjdGVkVXNlcklkOicsIHRoaXMuc2VsZWN0ZWRVc2VySWQpOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIGNvbnNvbGUud2FybignW0JldFN0YXRpc3RpY3NdIOaXoOazleS7jnNlc3Npb25TdG9yYWdl6I635Y+Wc3lzVXNlcklkJyk7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgYXdhaXQgdGhpcy5jYWxjdWxhdGVTdGF0aXN0aWNzKCk7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0KICAgIH0sCiAgICBkaWFsb2dWaXNpYmxlKHZhbCkgewogICAgICB0aGlzLiRlbWl0KCd1cGRhdGU6dmlzaWJsZScsIHZhbCk7CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5Yqg6L29546p5rOV5pWw5o2uICovCiAgICBhc3luYyBsb2FkR2FtZU1ldGhvZHMoKSB7CiAgICAgIHRyeSB7CiAgICAgICAgLy8g55u05o6l5LuOQVBJ6I635Y+W546p5rOV5pWw5o2uCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCByZXF1ZXN0KHsKICAgICAgICAgIHVybDogJy9nYW1lL29kZHMvbGlzdCcsCiAgICAgICAgICBtZXRob2Q6ICdnZXQnLAogICAgICAgICAgcGFyYW1zOiB7IHBhZ2VOdW06IDEsIHBhZ2VTaXplOiAxMDAwIH0KICAgICAgICB9KTsKCiAgICAgICAgaWYgKHJlc3BvbnNlICYmIHJlc3BvbnNlLnJvd3MpIHsKICAgICAgICAgIHRoaXMuZ2FtZU1ldGhvZHNEYXRhID0gcmVzcG9uc2Uucm93czsKICAgICAgICAgCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuZ2FtZU1ldGhvZHNEYXRhID0gW107CiAgICAgICAgICBjb25zb2xlLndhcm4oJ+eOqeazleaVsOaNruS4uuepuicpOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bnjqnms5XmlbDmja7lpLHotKU6JywgZXJyb3IpOwogICAgICAgIC8vIOWmguaenEFQSeWksei0pe+8jOS9v+eUqOWfuuacrOeahOeOqeazleaYoOWwhAogICAgICAgIHRoaXMuZ2FtZU1ldGhvZHNEYXRhID0gdGhpcy5nZXRCYXNpY01ldGhvZHNEYXRhKCk7CiAgICAgIH0KICAgIH0sCiAgICAvKiog6I635Y+W5Z+65pys546p5rOV5pWw5o2u77yIQVBJ5aSx6LSl5pe255qE5aSH55So5pa55qGI77yJICovCiAgICBnZXRCYXNpY01ldGhvZHNEYXRhKCkgewogICAgICByZXR1cm4gWwogICAgICAgIHsgbWV0aG9kSWQ6IDEsIG1ldGhvZE5hbWU6ICfni6zog4YnIH0sCiAgICAgICAgeyBtZXRob2RJZDogMiwgbWV0aG9kTmFtZTogJ+S4gOeggeWumuS9jScgfSwKICAgICAgICB7IG1ldGhvZElkOiAzLCBtZXRob2ROYW1lOiAn5Lik56CB57uE5ZCIJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDQsIG1ldGhvZE5hbWU6ICfkuKTnoIHlr7nlrZAnIH0sCiAgICAgICAgeyBtZXRob2RJZDogNSwgbWV0aG9kTmFtZTogJ+S4ieeggeebtOmAiScgfSwKICAgICAgICB7IG1ldGhvZElkOiA2LCBtZXRob2ROYW1lOiAn5LiJ56CB57uE6YCJJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDcsIG1ldGhvZE5hbWU6ICfkuInnoIHnu4TkuIknIH0sCiAgICAgICAgeyBtZXRob2RJZDogOCwgbWV0aG9kTmFtZTogJ+Wbm+eggee7hOWFrScgfSwKICAgICAgICB7IG1ldGhvZElkOiA5LCBtZXRob2ROYW1lOiAn5Zub56CB57uE5LiJJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDEwLCBtZXRob2ROYW1lOiAn5LqU56CB57uE5YWtJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDExLCBtZXRob2ROYW1lOiAn5LqU56CB57uE5LiJJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDEyLCBtZXRob2ROYW1lOiAn5YWt56CB57uE5YWtJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDEzLCBtZXRob2ROYW1lOiAn5YWt56CB57uE5LiJJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDE0LCBtZXRob2ROYW1lOiAn5LiD56CB57uE5YWtJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDE1LCBtZXRob2ROYW1lOiAn5LiD56CB57uE5LiJJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDE2LCBtZXRob2ROYW1lOiAn5YWr56CB57uE5YWtJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDE3LCBtZXRob2ROYW1lOiAn5YWr56CB57uE5LiJJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDE4LCBtZXRob2ROYW1lOiAn5Lmd56CB57uE5YWtJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDE5LCBtZXRob2ROYW1lOiAn5Lmd56CB57uE5LiJJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDIwLCBtZXRob2ROYW1lOiAn5YyF5omT57uE5YWtJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDIxLCBtZXRob2ROYW1lOiAnN+aIljIw5ZKM5YC8JyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDIyLCBtZXRob2ROYW1lOiAnOOaIljE55ZKM5YC8JyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDIzLCBtZXRob2ROYW1lOiAnOeaIljE45ZKM5YC8JyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDI0LCBtZXRob2ROYW1lOiAnMTDmiJYxN+WSjOWAvCcgfSwKICAgICAgICB7IG1ldGhvZElkOiAyNSwgbWV0aG9kTmFtZTogJzEx5oiWMTblkozlgLwnIH0sCiAgICAgICAgeyBtZXRob2RJZDogMjYsIG1ldGhvZE5hbWU6ICcxMuaIljE15ZKM5YC8JyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDI3LCBtZXRob2ROYW1lOiAnMTPmiJYxNOWSjOWAvCcgfSwKICAgICAgICB7IG1ldGhvZElkOiAyOCwgbWV0aG9kTmFtZTogJ+ebtOmAieWkjeW8jycgfSwKICAgICAgICB7IG1ldGhvZElkOiAyOSwgbWV0aG9kTmFtZTogJ+WSjOWAvOWNleWPjCcgfSwKICAgICAgICB7IG1ldGhvZElkOiAzMCwgbWV0aG9kTmFtZTogJ+WSjOWAvOWkp+WwjycgfSwKICAgICAgICB7IG1ldGhvZElkOiAzMSwgbWV0aG9kTmFtZTogJ+WMheaJk+e7hOS4iScgfSwKICAgICAgICB7IG1ldGhvZElkOiAxMDAsIG1ldGhvZE5hbWU6ICfkuInnoIHpmLLlr7knIH0KICAgICAgXTsKICAgIH0sCiAgICAvKiog6I635Y+W546p5rOV5ZCN56ewICovCiAgICBnZXRNZXRob2ROYW1lKG1ldGhvZElkKSB7CiAgICAgIGNvbnN0IG1ldGhvZCA9IHRoaXMuZ2FtZU1ldGhvZHNEYXRhLmZpbmQobSA9PiBtLm1ldGhvZElkID09PSBtZXRob2RJZCk7CiAgICAgIHJldHVybiBtZXRob2QgPyBtZXRob2QubWV0aG9kTmFtZSA6IGDnjqnms5Uke21ldGhvZElkfWA7CiAgICB9LAogICAgLyoqIOiOt+WPluaOkuWQjeagt+W8j+exuyAqLwogICAgZ2V0UmFua0NsYXNzKHJhbmspIHsKICAgICAgaWYgKHJhbmsgPT09IDEpIHJldHVybiAncmFuay1maXJzdCc7CiAgICAgIGlmIChyYW5rID09PSAyKSByZXR1cm4gJ3Jhbmstc2Vjb25kJzsKICAgICAgaWYgKHJhbmsgPT09IDMpIHJldHVybiAncmFuay10aGlyZCc7CiAgICAgIHJldHVybiAncmFuay1vdGhlcic7CiAgICB9LAogICAgLyoqIOiuoeeul+e7n+iuoeaVsOaNriAqLwogICAgYXN5bmMgY2FsY3VsYXRlU3RhdGlzdGljcygpIHsKICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHRoaXMuZ2V0UmVjb3JkRGF0YSgpOwoKCiAgICAgIGlmICghZGF0YSB8fCBkYXRhLmxlbmd0aCA9PT0gMCkgewogICAgICAgIGlmICh0aGlzLmlzQWRtaW4gJiYgIXRoaXMuc2VsZWN0ZWRVc2VySWQpIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36YCJ5oup6KaB5p+l55yL57uf6K6h55qE55So5oi3Jyk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn5pqC5peg57uf6K6h5pWw5o2u77yM6K+35YWI5Yi35pawcmVjb3Jk6aG16Z2iJyk7CiAgICAgICAgfQogICAgICAgIHRoaXMuY2xlYXJBbGxSYW5raW5nRGF0YSgpOwogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgLy8g5oyJ5b2p56eN5YiG5Yir6K6h566X57uf6K6h5pWw5o2uCiAgICAgIHRoaXMuY2FsY3VsYXRlU3RhdGlzdGljc0J5TG90dGVyeVR5cGUoZGF0YSk7CgogICAgICAvLyDosIPor5Xkv6Hmga8KICAgICAgY29uc29sZS5sb2coJ+e7n+iuoeiuoeeul+WujOaIkDonLCB7CiAgICAgICAg56aP5b2p5oqV5rOo5o6S6KGMOiB0aGlzLmZ1Y2FpTWV0aG9kUmFua2luZy5sZW5ndGgsCiAgICAgICAg56aP5b2p54Ot6Zeo5LiJ5L2N5pWwOiB0aGlzLmZ1Y2FpTnVtYmVyUmFua2luZy5sZW5ndGgsCiAgICAgICAg5L2T5b2p5oqV5rOo5o6S6KGMOiB0aGlzLnRpY2FpTWV0aG9kUmFua2luZy5sZW5ndGgsCiAgICAgICAg5L2T5b2p54Ot6Zeo5LiJ5L2N5pWwOiB0aGlzLnRpY2FpTnVtYmVyUmFua2luZy5sZW5ndGgsCiAgICAgICAg5b2T5YmN6YCJ5Lit5b2p56eNOiB0aGlzLmFjdGl2ZUxvdHRlcnlUeXBlCiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDojrflj5ZyZWNvcmTmlbDmja4gKi8KICAgIGFzeW5jIGdldFJlY29yZERhdGEoKSB7CiAgICAgIHRyeSB7CiAgICAgICAgLy8g5aaC5p6c5pyJ6YCJ5oup55qE55So5oi3SUTvvIjnrqHnkIblkZjpgInmi6nnmoTnlKjmiLfmiJbmma7pgJrnlKjmiLfoh6rlt7HvvInvvIznm7TmjqXku45BUEnojrflj5bmlbDmja4KICAgICAgICBpZiAodGhpcy5zZWxlY3RlZFVzZXJJZCkgewogICAgICAgICAgcmV0dXJuIGF3YWl0IHRoaXMuZmV0Y2hVc2VyUmVjb3JkRGF0YSh0aGlzLnNlbGVjdGVkVXNlcklkKTsKICAgICAgICB9CgogICAgICAgIC8vIOWmguaenOaYr+euoeeQhuWRmOS9huayoeaciemAieaLqeeUqOaIt++8jOaPkOekuumAieaLqeeUqOaItwogICAgICAgIGlmICh0aGlzLmlzQWRtaW4pIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36YCJ5oup6KaB5p+l55yL57uf6K6h55qE55So5oi3Jyk7CiAgICAgICAgICByZXR1cm4gW107CiAgICAgICAgfQoKICAgICAgICAvLyDmma7pgJrnlKjmiLfvvJrnm7TmjqXku45BUEnojrflj5blvZPliY3nlKjmiLfmlbDmja7vvIzku45zZXNzaW9uU3RvcmFnZeiOt+WPlueUqOaIt0lECiAgICAgICAgY29uc29sZS5sb2coJ1tCZXRTdGF0aXN0aWNzXSDmma7pgJrnlKjmiLfvvIznm7TmjqXku45BUEnojrflj5bmlbDmja4nKTsKCiAgICAgICAgLy8g5LuOc2Vzc2lvblN0b3JhZ2Xojrflj5bnlKjmiLdJRAogICAgICAgIGNvbnN0IHN5c1VzZXJJZCA9IHNlc3Npb25TdG9yYWdlLmdldEl0ZW0oJ3N5c1VzZXJJZCcpOwogICAgICAgIGNvbnNvbGUubG9nKCdbQmV0U3RhdGlzdGljc10g5LuOc2Vzc2lvblN0b3JhZ2Xojrflj5bnmoRzeXNVc2VySWQ6Jywgc3lzVXNlcklkKTsKCiAgICAgICAgaWYgKHN5c1VzZXJJZCkgewogICAgICAgICAgY29uc29sZS5sb2coJ1tCZXRTdGF0aXN0aWNzXSDkvb/nlKhzeXNVc2VySWTosIPnlKhBUEk6Jywgc3lzVXNlcklkKTsKICAgICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCB0aGlzLmZldGNoVXNlclJlY29yZERhdGEocGFyc2VJbnQoc3lzVXNlcklkKSk7CiAgICAgICAgICBjb25zb2xlLmxvZygnW0JldFN0YXRpc3RpY3NdIEFQSeiOt+WPluWIsOeahOaVsOaNrjonLCBkYXRhKTsKCiAgICAgICAgICBpZiAoZGF0YSAmJiBkYXRhLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgY29uc29sZS5sb2coYFtCZXRTdGF0aXN0aWNzXSDmiJDlip/liqDovb3kuoYgJHtkYXRhLmxlbmd0aH0g5p2h57uf6K6h5pWw5o2uYCk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBjb25zb2xlLndhcm4oJ1tCZXRTdGF0aXN0aWNzXSBBUEnov5Tlm57nqbrmlbDmja4nKTsKICAgICAgICAgIH0KCiAgICAgICAgICByZXR1cm4gZGF0YTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgY29uc29sZS53YXJuKCdbQmV0U3RhdGlzdGljc10g5peg5rOV5LuOc2Vzc2lvblN0b3JhZ2Xojrflj5ZzeXNVc2VySWQnKTsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn5peg5rOV6I635Y+W55So5oi35L+h5oGv77yM6K+36YeN5paw55m75b2VJyk7CiAgICAgICAgICByZXR1cm4gW107CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+ino+aekOe7n+iuoeaVsOaNruWksei0pTonLCBlcnJvcik7CiAgICAgICAgcmV0dXJuIFtdOwogICAgICB9CiAgICB9LAogICAgLyoqIOa4heepuuaJgOacieaOkuihjOaVsOaNriAqLwogICAgY2xlYXJBbGxSYW5raW5nRGF0YSgpIHsKICAgICAgdGhpcy5mdWNhaU1ldGhvZFJhbmtpbmcgPSBbXTsKICAgICAgdGhpcy5mdWNhaU51bWJlclJhbmtpbmcgPSBbXTsKICAgICAgdGhpcy50aWNhaU1ldGhvZFJhbmtpbmcgPSBbXTsKICAgICAgdGhpcy50aWNhaU51bWJlclJhbmtpbmcgPSBbXTsKICAgIH0sCgogICAgLyoqIOaMieW9qeenjeWIhuWIq+iuoeeul+e7n+iuoeaVsOaNriAqLwogICAgY2FsY3VsYXRlU3RhdGlzdGljc0J5TG90dGVyeVR5cGUoZGF0YSkgewogICAgICAvLyDosIPor5XvvJrmn6XnnIvmlbDmja7nu5PmnoQKICAgICAgaWYgKGRhdGEubGVuZ3RoID4gMCkgewogICAgICAgIGNvbnNvbGUubG9nKCfmlbDmja7moLfmnKw6JywgZGF0YVswXSk7CiAgICAgICAgY29uc29sZS5sb2coJ+WPr+iDveeahOW9qeenjeWtl+autTonLCB7CiAgICAgICAgICBsb3R0ZXJ5VHlwZTogZGF0YVswXS5sb3R0ZXJ5VHlwZSwKICAgICAgICAgIGdhbWVUeXBlOiBkYXRhWzBdLmdhbWVUeXBlLAogICAgICAgICAgbG90dGVyeUlkOiBkYXRhWzBdLmxvdHRlcnlJZCwKICAgICAgICAgIGdhbWVOYW1lOiBkYXRhWzBdLmdhbWVOYW1lLAogICAgICAgICAgbWV0aG9kTmFtZTogZGF0YVswXS5tZXRob2ROYW1lCiAgICAgICAgfSk7CgogICAgICAgIC8vIOe7n+iuoeaJgOacieWPr+iDveeahOW9qeenjeagh+ivhgogICAgICAgIGNvbnN0IGxvdHRlcnlUeXBlcyA9IFsuLi5uZXcgU2V0KGRhdGEubWFwKHJlY29yZCA9PiByZWNvcmQubG90dGVyeVR5cGUpKV07CiAgICAgICAgY29uc3QgZ2FtZVR5cGVzID0gWy4uLm5ldyBTZXQoZGF0YS5tYXAocmVjb3JkID0+IHJlY29yZC5nYW1lVHlwZSkpXTsKICAgICAgICBjb25zdCBsb3R0ZXJ5SWRzID0gWy4uLm5ldyBTZXQoZGF0YS5tYXAocmVjb3JkID0+IHJlY29yZC5sb3R0ZXJ5SWQpKV07CiAgICAgICAgY29uc3QgZ2FtZU5hbWVzID0gWy4uLm5ldyBTZXQoZGF0YS5tYXAocmVjb3JkID0+IHJlY29yZC5nYW1lTmFtZSkpXTsKCiAgICAgICAgY29uc29sZS5sb2coJ+aVsOaNruS4reeahOW9qeenjeagh+ivhjonLCB7CiAgICAgICAgICBsb3R0ZXJ5VHlwZXMsCiAgICAgICAgICBnYW1lVHlwZXMsCiAgICAgICAgICBsb3R0ZXJ5SWRzLAogICAgICAgICAgZ2FtZU5hbWVzCiAgICAgICAgfSk7CiAgICAgIH0KCiAgICAgIC8vIOaMieW9qeenjeWIhue7hOaVsOaNru+8iOS9v+eUqGxvdHRlcnlJZOWtl+aute+8iQogICAgICBjb25zdCBmdWNhaURhdGEgPSBkYXRhLmZpbHRlcihyZWNvcmQgPT4gdGhpcy5pc0Z1Y2FpTG90dGVyeShyZWNvcmQubG90dGVyeUlkKSk7CiAgICAgIGNvbnN0IHRpY2FpRGF0YSA9IGRhdGEuZmlsdGVyKHJlY29yZCA9PiB0aGlzLmlzVGljYWlMb3R0ZXJ5KHJlY29yZC5sb3R0ZXJ5SWQpKTsKCiAgICAgIGNvbnNvbGUubG9nKGDnpo/lvakzROaVsOaNrjogJHtmdWNhaURhdGEubGVuZ3RofeadoSwg5L2T5b2pUDPmlbDmja46ICR7dGljYWlEYXRhLmxlbmd0aH3mnaFgKTsKCiAgICAgIC8vIOWmguaenOayoeacieaYjuehrueahOW9qeenjeWMuuWIhu+8jOWwhuaJgOacieaVsOaNruW9kuexu+S4uuemj+W9qTNE77yI5YW85a655oCn5aSE55CG77yJCiAgICAgIGlmIChmdWNhaURhdGEubGVuZ3RoID09PSAwICYmIHRpY2FpRGF0YS5sZW5ndGggPT09IDAgJiYgZGF0YS5sZW5ndGggPiAwKSB7CiAgICAgICAgY29uc29sZS5sb2coJ+acquajgOa1i+WIsOW9qeenjeWtl+aute+8jOWwhuaJgOacieaVsOaNruW9kuexu+S4uuemj+W9qTNEJyk7CiAgICAgICAgdGhpcy5mdWNhaU1ldGhvZFJhbmtpbmcgPSB0aGlzLmNhbGN1bGF0ZU1ldGhvZFJhbmtpbmdCeUdyb3VwKGRhdGEpOwogICAgICAgIHRoaXMuZnVjYWlOdW1iZXJSYW5raW5nID0gdGhpcy5jYWxjdWxhdGVOdW1iZXJSYW5raW5nKGRhdGEpOwogICAgICAgIHRoaXMudGljYWlNZXRob2RSYW5raW5nID0gW107CiAgICAgICAgdGhpcy50aWNhaU51bWJlclJhbmtpbmcgPSBbXTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIC8vIOWIhuWIq+iuoeeul+emj+W9qeWSjOS9k+W9qeeahOe7n+iuoeaVsOaNrgogICAgICBpZiAoZnVjYWlEYXRhLmxlbmd0aCA+IDApIHsKICAgICAgICB0aGlzLmZ1Y2FpTWV0aG9kUmFua2luZyA9IHRoaXMuY2FsY3VsYXRlTWV0aG9kUmFua2luZ0J5R3JvdXAoZnVjYWlEYXRhKTsKICAgICAgICB0aGlzLmZ1Y2FpTnVtYmVyUmFua2luZyA9IHRoaXMuY2FsY3VsYXRlTnVtYmVyUmFua2luZyhmdWNhaURhdGEpOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuZnVjYWlNZXRob2RSYW5raW5nID0gW107CiAgICAgICAgdGhpcy5mdWNhaU51bWJlclJhbmtpbmcgPSBbXTsKICAgICAgfQoKICAgICAgaWYgKHRpY2FpRGF0YS5sZW5ndGggPiAwKSB7CiAgICAgICAgdGhpcy50aWNhaU1ldGhvZFJhbmtpbmcgPSB0aGlzLmNhbGN1bGF0ZU1ldGhvZFJhbmtpbmdCeUdyb3VwKHRpY2FpRGF0YSk7CiAgICAgICAgdGhpcy50aWNhaU51bWJlclJhbmtpbmcgPSB0aGlzLmNhbGN1bGF0ZU51bWJlclJhbmtpbmcodGljYWlEYXRhKTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnRpY2FpTWV0aG9kUmFua2luZyA9IFtdOwogICAgICAgIHRoaXMudGljYWlOdW1iZXJSYW5raW5nID0gW107CiAgICAgIH0KICAgIH0sCgogICAgLyoqIOWIpOaWreaYr+WQpuS4uuemj+W9qTNEICovCiAgICBpc0Z1Y2FpTG90dGVyeShsb3R0ZXJ5SWQpIHsKICAgICAgLy8g56aP5b2pM0TnmoRsb3R0ZXJ5SWTmmK8xCiAgICAgIHJldHVybiBsb3R0ZXJ5SWQgPT09IDEgfHwgbG90dGVyeUlkID09PSAnMSc7CiAgICB9LAoKICAgIC8qKiDliKTmlq3mmK/lkKbkuLrkvZPlvalQMyAqLwogICAgaXNUaWNhaUxvdHRlcnkobG90dGVyeUlkKSB7CiAgICAgIC8vIOS9k+W9qVAz55qEbG90dGVyeUlk5pivMgogICAgICByZXR1cm4gbG90dGVyeUlkID09PSAyIHx8IGxvdHRlcnlJZCA9PT0gJzInOwogICAgfSwKCiAgICAvKiog5oyJ546p5rOV5YiG57uE6K6h566X5o6S6KGM5qacICovCiAgICBjYWxjdWxhdGVNZXRob2RSYW5raW5nQnlHcm91cChkYXRhKSB7CiAgICAgIGNvbnNvbGUubG9nKCdbY2FsY3VsYXRlTWV0aG9kUmFua2luZ0J5R3JvdXBdIOW8gOWni+iuoeeul+WQhOeOqeazleaKleazqOaOkuihjOamnCcpOwoKICAgICAgLy8g5oyJ546p5rOV5YiG57uE77yM55u45ZCM5Y+356CB5ZCI5bm257Sv6K6h6YeR6aKdCiAgICAgIGNvbnN0IG1ldGhvZEdyb3VwcyA9IHt9OwoKICAgICAgZGF0YS5mb3JFYWNoKHJlY29yZCA9PiB7CiAgICAgICAgY29uc3QgbWV0aG9kSWQgPSByZWNvcmQubWV0aG9kSWQ7CiAgICAgICAgY29uc3QgYW1vdW50ID0gcGFyc2VGbG9hdChyZWNvcmQubW9uZXkpIHx8IDA7CgogICAgICAgIGlmICghbWV0aG9kR3JvdXBzW21ldGhvZElkXSkgewogICAgICAgICAgbWV0aG9kR3JvdXBzW21ldGhvZElkXSA9IG5ldyBNYXAoKTsgLy8g5L2/55SoTWFw5p2l5a2Y5YKo5Y+356CB5ZKM5a+55bqU55qE57Sv6K6h5pWw5o2uCiAgICAgICAgfQoKICAgICAgICAvLyDop6PmnpDmipXms6jlj7fnoIEKICAgICAgICB0cnkgewogICAgICAgICAgY29uc3QgYmV0TnVtYmVycyA9IEpTT04ucGFyc2UocmVjb3JkLmJldE51bWJlcnMgfHwgJ3t9Jyk7CiAgICAgICAgICBjb25zdCBudW1iZXJzID0gYmV0TnVtYmVycy5udW1iZXJzIHx8IFtdOwoKICAgICAgICAgIC8vIOS4uuavj+S4quWPt+eggee0r+iuoemHkeminQogICAgICAgICAgbnVtYmVycy5mb3JFYWNoKChudW1iZXJPYmopID0+IHsKICAgICAgICAgICAgbGV0IHNpbmdsZU51bWJlckRpc3BsYXkgPSAnJzsKCiAgICAgICAgICAgIGlmICh0eXBlb2YgbnVtYmVyT2JqID09PSAnb2JqZWN0JyAmJiBudW1iZXJPYmogIT09IG51bGwpIHsKICAgICAgICAgICAgICAvLyDlpITnkIYge2E6MSwgYjoyLCBjOjMsIGQ6NCwgZTo1fSDmoLzlvI8KICAgICAgICAgICAgICBjb25zdCBrZXlzID0gT2JqZWN0LmtleXMobnVtYmVyT2JqKS5zb3J0KCk7CiAgICAgICAgICAgICAgY29uc3QgdmFsdWVzID0ga2V5cy5tYXAoa2V5ID0+IFN0cmluZyhudW1iZXJPYmpba2V5XSkpOwogICAgICAgICAgICAgIHNpbmdsZU51bWJlckRpc3BsYXkgPSB2YWx1ZXMuam9pbignJyk7CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgc2luZ2xlTnVtYmVyRGlzcGxheSA9IFN0cmluZyhudW1iZXJPYmopOwogICAgICAgICAgICB9CgogICAgICAgICAgICAvLyDlpoLmnpzlj7fnoIHlt7LlrZjlnKjvvIzntK/orqHph5Hpop3lkozorrDlvZXmlbAKICAgICAgICAgICAgaWYgKG1ldGhvZEdyb3Vwc1ttZXRob2RJZF0uaGFzKHNpbmdsZU51bWJlckRpc3BsYXkpKSB7CiAgICAgICAgICAgICAgY29uc3QgZXhpc3RpbmcgPSBtZXRob2RHcm91cHNbbWV0aG9kSWRdLmdldChzaW5nbGVOdW1iZXJEaXNwbGF5KTsKICAgICAgICAgICAgICBleGlzdGluZy50b3RhbEFtb3VudCArPSBhbW91bnQ7CiAgICAgICAgICAgICAgZXhpc3RpbmcucmVjb3JkQ291bnQgKz0gMTsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAvLyDmlrDlj7fnoIHvvIzliJvlu7rorrDlvZUKICAgICAgICAgICAgICBtZXRob2RHcm91cHNbbWV0aG9kSWRdLnNldChzaW5nbGVOdW1iZXJEaXNwbGF5LCB7CiAgICAgICAgICAgICAgICBiZXROdW1iZXJzOiBzaW5nbGVOdW1iZXJEaXNwbGF5LAogICAgICAgICAgICAgICAgdG90YWxBbW91bnQ6IGFtb3VudCwKICAgICAgICAgICAgICAgIHJlY29yZENvdW50OiAxCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgICBjb25zb2xlLndhcm4oJ+ino+aekOaKleazqOWPt+eggeWksei0pTonLCByZWNvcmQuYmV0TnVtYmVycywgZXJyb3IpOwogICAgICAgICAgLy8g5aaC5p6c6Kej5p6Q5aSx6LSl77yM5L2/55So5Y6f5aeL5pWw5o2uCiAgICAgICAgICBjb25zdCBmYWxsYmFja051bWJlcnMgPSByZWNvcmQuYmV0TnVtYmVycyB8fCAn5pyq55+l5Y+356CBJzsKCiAgICAgICAgICBpZiAobWV0aG9kR3JvdXBzW21ldGhvZElkXS5oYXMoZmFsbGJhY2tOdW1iZXJzKSkgewogICAgICAgICAgICBjb25zdCBleGlzdGluZyA9IG1ldGhvZEdyb3Vwc1ttZXRob2RJZF0uZ2V0KGZhbGxiYWNrTnVtYmVycyk7CiAgICAgICAgICAgIGV4aXN0aW5nLnRvdGFsQW1vdW50ICs9IGFtb3VudDsKICAgICAgICAgICAgZXhpc3RpbmcucmVjb3JkQ291bnQgKz0gMTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIG1ldGhvZEdyb3Vwc1ttZXRob2RJZF0uc2V0KGZhbGxiYWNrTnVtYmVycywgewogICAgICAgICAgICAgIGJldE51bWJlcnM6IGZhbGxiYWNrTnVtYmVycywKICAgICAgICAgICAgICB0b3RhbEFtb3VudDogYW1vdW50LAogICAgICAgICAgICAgIHJlY29yZENvdW50OiAxCiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CgogICAgICBjb25zb2xlLmxvZygnW2NhbGN1bGF0ZU1ldGhvZFJhbmtpbmdCeUdyb3VwXSDlvIDlp4vnlJ/miJDmjpLooYzmppwnKTsKCiAgICAgIC8vIOS4uuavj+S4queOqeazleeUn+aIkOaOkuihjOamnOW5tui/lOWbngogICAgICByZXR1cm4gT2JqZWN0LmVudHJpZXMobWV0aG9kR3JvdXBzKS5tYXAoKFttZXRob2RJZCwgbnVtYmVyc01hcF0pID0+IHsKICAgICAgICAvLyDlsIZNYXDovazmjaLkuLrmlbDnu4TlubbmjInmgLvph5Hpop3pmY3luo/mjpLluo8KICAgICAgICBjb25zdCByYW5raW5nID0gQXJyYXkuZnJvbShudW1iZXJzTWFwLnZhbHVlcygpKQogICAgICAgICAgLnNvcnQoKGEsIGIpID0+IGIudG90YWxBbW91bnQgLSBhLnRvdGFsQW1vdW50KQogICAgICAgICAgLm1hcCgoaXRlbSwgaW5kZXgpID0+ICh7CiAgICAgICAgICAgIHJhbms6IGluZGV4ICsgMSwKICAgICAgICAgICAgYmV0TnVtYmVyczogaXRlbS5iZXROdW1iZXJzLAogICAgICAgICAgICB0b3RhbEFtb3VudDogaXRlbS50b3RhbEFtb3VudCwKICAgICAgICAgICAgcmVjb3JkQ291bnQ6IGl0ZW0ucmVjb3JkQ291bnQKICAgICAgICAgIH0pKTsKCiAgICAgICAgY29uc29sZS5sb2coYFtjYWxjdWxhdGVNZXRob2RSYW5raW5nQnlHcm91cF0g546p5rOVJHttZXRob2RJZH0oJHt0aGlzLmdldE1ldGhvZE5hbWUocGFyc2VJbnQobWV0aG9kSWQpKX0p77yaJHtyYW5raW5nLmxlbmd0aH3kuKrkuI3lkIzlj7fnoIFgKTsKCiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgIG1ldGhvZElkOiBwYXJzZUludChtZXRob2RJZCksCiAgICAgICAgICBtZXRob2ROYW1lOiB0aGlzLmdldE1ldGhvZE5hbWUocGFyc2VJbnQobWV0aG9kSWQpKSwKICAgICAgICAgIHJhbmtpbmcKICAgICAgICB9OwogICAgICB9KS5maWx0ZXIoZ3JvdXAgPT4gZ3JvdXAucmFua2luZy5sZW5ndGggPiAwKTsgLy8g5Y+q5pi+56S65pyJ5pWw5o2u55qE546p5rOVCiAgICB9LAogICAgLyoqIOagvOW8j+WMluaKleazqOWPt+eggeeUqOS6juaYvuekuiAqLwogICAgZm9ybWF0QmV0TnVtYmVyc0ZvckRpc3BsYXkoYmV0TnVtYmVyc0tleSkgewogICAgICB0cnkgewogICAgICAgIGNvbnN0IG51bWJlcnMgPSBKU09OLnBhcnNlKGJldE51bWJlcnNLZXkpOwogICAgICAgIGlmIChBcnJheS5pc0FycmF5KG51bWJlcnMpICYmIG51bWJlcnMubGVuZ3RoID4gMCkgewogICAgICAgICAgcmV0dXJuIG51bWJlcnMubWFwKG51bSA9PiB7CiAgICAgICAgICAgIGlmICh0eXBlb2YgbnVtID09PSAnb2JqZWN0JyAmJiBudW0gIT09IG51bGwpIHsKICAgICAgICAgICAgICAvLyDlpITnkIYge2E6MSwgYjoyLCBjOjMsIGQ6NCwgZTo1fSDmoLzlvI8KICAgICAgICAgICAgICBjb25zdCBrZXlzID0gT2JqZWN0LmtleXMobnVtKS5zb3J0KCk7CiAgICAgICAgICAgICAgY29uc3QgdmFsdWVzID0ga2V5cy5tYXAoa2V5ID0+IFN0cmluZyhudW1ba2V5XSkpOwogICAgICAgICAgICAgIHJldHVybiB2YWx1ZXMuam9pbignJyk7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgcmV0dXJuIFN0cmluZyhudW0pOwogICAgICAgICAgfSkuam9pbignLCAnKTsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgLy8g5aaC5p6c6Kej5p6Q5aSx6LSl77yM55u05o6l6L+U5Zue5Y6f5aeL5a2X56ym5LiyCiAgICAgIH0KICAgICAgcmV0dXJuIGJldE51bWJlcnNLZXkubGVuZ3RoID4gMjAgPyBiZXROdW1iZXJzS2V5LnN1YnN0cmluZygwLCAyMCkgKyAnLi4uJyA6IGJldE51bWJlcnNLZXk7CiAgICB9LAogICAgLyoqIOaMieeOqeazleWIhue7hOiuoeeul+WPt+eggeaOkuihjCAtIOWPquS9v+eUqG1vbmV55a2X5q61ICovCiAgICBjYWxjdWxhdGVOdW1iZXJSYW5raW5nKGRhdGEpIHsKICAgICAgLy8g5Yid5aeL5YyW6L+U5Zue5pWw57uECiAgICAgIGNvbnN0IG51bWJlclJhbmtpbmdCeUdyb3VwID0gW107CgogICAgICAvLyDmjInnjqnms5XliIbnu4Tnu5/orqEKICAgICAgY29uc3QgbWV0aG9kR3JvdXBzID0ge307CgogICAgICBkYXRhLmZvckVhY2gocmVjb3JkID0+IHsKICAgICAgICB0cnkgewogICAgICAgICAgY29uc3QgbWV0aG9kSWQgPSByZWNvcmQubWV0aG9kSWQ7CiAgICAgICAgICBjb25zdCBtb25leSA9IHBhcnNlRmxvYXQocmVjb3JkLm1vbmV5IHx8IDApOwogICAgICAgICAgY29uc3QgYmV0TnVtYmVycyA9IEpTT04ucGFyc2UocmVjb3JkLmJldE51bWJlcnMgfHwgJ3t9Jyk7CiAgICAgICAgICBjb25zdCBudW1iZXJzID0gYmV0TnVtYmVycy5udW1iZXJzIHx8IFtdOwoKICAgICAgICAgIC8vIOWIneWni+WMlueOqeazleWIhue7hAogICAgICAgICAgaWYgKCFtZXRob2RHcm91cHNbbWV0aG9kSWRdKSB7CiAgICAgICAgICAgIG1ldGhvZEdyb3Vwc1ttZXRob2RJZF0gPSB7CiAgICAgICAgICAgICAgbWV0aG9kVG90YWw6IDAsCiAgICAgICAgICAgICAgdGhyZWVEaWdpdE1hcDogbmV3IE1hcCgpCiAgICAgICAgICAgIH07CiAgICAgICAgICB9CgogICAgICAgICAgbWV0aG9kR3JvdXBzW21ldGhvZElkXS5tZXRob2RUb3RhbCArPSBtb25leTsKCiAgICAgICAgICAvLyDlpITnkIbmr4/kuKrmipXms6jlj7fnoIEKICAgICAgICAgIG51bWJlcnMuZm9yRWFjaChudW1iZXJPYmogPT4gewogICAgICAgICAgICBjb25zdCB0aHJlZURpZ2l0cyA9IHRoaXMuZXh0cmFjdFRocmVlRGlnaXROdW1iZXJzKG51bWJlck9iaik7CgogICAgICAgICAgICAvLyDlr7nlvZPliY3lj7fnoIHnmoTkuInkvY3mlbDnu4TlkIjljrvph43vvIjpgb/lhY3lkIzkuIDlj7fnoIHlhoXph43lpI3nu4TlkIjlpJrmrKHntK/orqHvvIkKICAgICAgICAgICAgY29uc3QgdW5pcXVlTm9ybWFsaXplZERpZ2l0cyA9IG5ldyBTZXQoKTsKCiAgICAgICAgICAgIHRocmVlRGlnaXRzLmZvckVhY2goZGlnaXQgPT4gewogICAgICAgICAgICAgIC8vIOW9kuS4gOWMluS4ieS9jeaVsO+8muWwhuaVsOWtl+aMieS7juWwj+WIsOWkp+aOkuW6j+S9nOS4uue7n+S4gGtleQogICAgICAgICAgICAgIGNvbnN0IG5vcm1hbGl6ZWREaWdpdCA9IHRoaXMubm9ybWFsaXplVGhyZWVEaWdpdHMoZGlnaXQpOwogICAgICAgICAgICAgIHVuaXF1ZU5vcm1hbGl6ZWREaWdpdHMuYWRkKG5vcm1hbGl6ZWREaWdpdCk7CiAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgLy8g5q+P5Liq5b2S5LiA5YyW5ZCO55qE5LiJ5L2N5pWw57Sv5Yqg6YeR6aKdCiAgICAgICAgICAgIHVuaXF1ZU5vcm1hbGl6ZWREaWdpdHMuZm9yRWFjaChub3JtYWxpemVkRGlnaXQgPT4gewogICAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRBbW91bnQgPSBtZXRob2RHcm91cHNbbWV0aG9kSWRdLnRocmVlRGlnaXRNYXAuZ2V0KG5vcm1hbGl6ZWREaWdpdCkgfHwgMDsKICAgICAgICAgICAgICBtZXRob2RHcm91cHNbbWV0aG9kSWRdLnRocmVlRGlnaXRNYXAuc2V0KG5vcm1hbGl6ZWREaWdpdCwgY3VycmVudEFtb3VudCArIG1vbmV5KTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9KTsKCiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+ino+aekOWksei0pTonLCBlcnJvcik7CiAgICAgICAgfQogICAgICB9KTsKCgoKICAgICAgLy8g55Sf5oiQ5o6S6KGM5qacCiAgIAogICAgICB0aGlzLm51bWJlclJhbmtpbmdCeUdyb3VwID0gW107CgogICAgICBPYmplY3QuZW50cmllcyhtZXRob2RHcm91cHMpLmZvckVhY2goKFttZXRob2RJZCwgZ3JvdXBdKSA9PiB7CiAgICAgICAgLy8g6L2s5o2iTWFw5Li65pWw57uE5bm25o6S5bqP77yM5pi+56S65omA5pyJ5pWw5o2uCiAgICAgICAgY29uc3Qgc29ydGVkVGhyZWVEaWdpdHMgPSBBcnJheS5mcm9tKGdyb3VwLnRocmVlRGlnaXRNYXAuZW50cmllcygpKQogICAgICAgICAgLnNvcnQoKFssYV0sIFssYl0pID0+IGIgLSBhKTsKCiAgICAgICAgY29uc3QgcmFua2luZyA9IHNvcnRlZFRocmVlRGlnaXRzLm1hcCgoW251bWJlciwgYW1vdW50XSwgaW5kZXgpID0+ICh7CiAgICAgICAgICByYW5rOiBpbmRleCArIDEsCiAgICAgICAgICBudW1iZXIsCiAgICAgICAgICBjb3VudDogMSwKICAgICAgICAgIHRvdGFsQW1vdW50OiBhbW91bnQsCiAgICAgICAgICBwZXJjZW50YWdlOiBncm91cC5tZXRob2RUb3RhbCA+IDAgPyAoKGFtb3VudCAvIGdyb3VwLm1ldGhvZFRvdGFsKSAqIDEwMCkudG9GaXhlZCgxKSA6ICcwLjAnCiAgICAgICAgfSkpOwoKICAgICAgICBpZiAocmFua2luZy5sZW5ndGggPiAwKSB7CiAgICAgICAgICBudW1iZXJSYW5raW5nQnlHcm91cC5wdXNoKHsKICAgICAgICAgICAgbWV0aG9kSWQ6IHBhcnNlSW50KG1ldGhvZElkKSwKICAgICAgICAgICAgbWV0aG9kTmFtZTogdGhpcy5nZXRNZXRob2ROYW1lKHBhcnNlSW50KG1ldGhvZElkKSksCiAgICAgICAgICAgIHJhbmtpbmcKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CgogICAgICByZXR1cm4gbnVtYmVyUmFua2luZ0J5R3JvdXA7CiAgICB9LAogICAgLyoqIOS7juaKleazqOWPt+eggeS4reaPkOWPluaJgOacieWPr+iDveeahOS4ieS9jeaVsOe7hOWQiCAqLwogICAgZXh0cmFjdFRocmVlRGlnaXROdW1iZXJzKG51bWJlck9iaikgewogICAgICBjb25zdCBudW1iZXJzID0gW107CgogICAgICAvLyDlpITnkIbkuI3lkIznmoTlj7fnoIHmoLzlvI8KICAgICAgaWYgKHR5cGVvZiBudW1iZXJPYmogPT09ICdzdHJpbmcnKSB7CiAgICAgICAgLy8g55u05o6l5piv5a2X56ym5Liy5qC85byP55qE5Y+356CBCiAgICAgICAgY29uc3QgY2xlYW5TdHIgPSBudW1iZXJPYmoucmVwbGFjZSgvXEQvZywgJycpOyAvLyDnp7vpmaTpnZ7mlbDlrZflrZfnrKYKICAgICAgICB0aGlzLmdlbmVyYXRlVGhyZWVEaWdpdENvbWJpbmF0aW9ucyhjbGVhblN0ci5zcGxpdCgnJyksIG51bWJlcnMpOwogICAgICB9IGVsc2UgaWYgKHR5cGVvZiBudW1iZXJPYmogPT09ICdvYmplY3QnICYmIG51bWJlck9iaiAhPT0gbnVsbCkgewogICAgICAgIC8vIOWvueixoeagvOW8jyB7YTogMSwgYjogMiwgYzogMywgZDogNCwgZTogNX0KICAgICAgICBjb25zdCBrZXlzID0gT2JqZWN0LmtleXMobnVtYmVyT2JqKS5zb3J0KCk7CiAgICAgICAgY29uc3QgZGlnaXRzID0ga2V5cy5tYXAoa2V5ID0+IFN0cmluZyhudW1iZXJPYmpba2V5XSkpOwoKICAgIAoKICAgICAgICAvLyDnlJ/miJDmiYDmnInlj6/og73nmoTkuInkvY3mlbDnu4TlkIgKICAgICAgICB0aGlzLmdlbmVyYXRlVGhyZWVEaWdpdENvbWJpbmF0aW9ucyhkaWdpdHMsIG51bWJlcnMpOwogICAgICB9CgogICAgICByZXR1cm4gbnVtYmVyczsKICAgIH0sCiAgICAvKiog55Sf5oiQ5omA5pyJ5Y+v6IO955qE5LiJ5L2N5pWw57uE5ZCIICovCiAgICBnZW5lcmF0ZVRocmVlRGlnaXRDb21iaW5hdGlvbnMoZGlnaXRzLCBudW1iZXJzKSB7CiAgICAgIGNvbnN0IG4gPSBkaWdpdHMubGVuZ3RoOwoKICAgICAgLy8g5aaC5p6c5pWw5a2X5bCR5LqOM+S4qu+8jOaXoOazlee7hOaIkOS4ieS9jeaVsAogICAgICBpZiAobiA8IDMpIHsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIC8vIOeUn+aIkOaJgOacieWPr+iDveeahOS4ieS9jeaVsOe7hOWQiO+8iEMobiwzKe+8iQogICAgICBmb3IgKGxldCBpID0gMDsgaSA8IG4gLSAyOyBpKyspIHsKICAgICAgICBmb3IgKGxldCBqID0gaSArIDE7IGogPCBuIC0gMTsgaisrKSB7CiAgICAgICAgICBmb3IgKGxldCBrID0gaiArIDE7IGsgPCBuOyBrKyspIHsKICAgICAgICAgICAgY29uc3QgdGhyZWVEaWdpdCA9IGRpZ2l0c1tpXSArIGRpZ2l0c1tqXSArIGRpZ2l0c1trXTsKICAgICAgICAgICAgaWYgKC9eXGR7M30kLy50ZXN0KHRocmVlRGlnaXQpKSB7CiAgICAgICAgICAgICAgbnVtYmVycy5wdXNoKHRocmVlRGlnaXQpOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICB9LAoKICAgIC8qKiDlvZLkuIDljJbkuInkvY3mlbDvvJrlsIbmlbDlrZfmjInku47lsI/liLDlpKfmjpLluo/vvIznu5/kuIDnm7jlkIzmlbDlrZfnmoTkuI3lkIzmjpLliJcgKi8KICAgIG5vcm1hbGl6ZVRocmVlRGlnaXRzKHRocmVlRGlnaXQpIHsKICAgICAgLy8g5bCG5LiJ5L2N5pWw5a2X56ym5Liy6L2s5o2i5Li65pWw57uE77yM5o6S5bqP5ZCO6YeN5paw57uE5ZCICiAgICAgIHJldHVybiB0aHJlZURpZ2l0LnNwbGl0KCcnKS5zb3J0KCkuam9pbignJyk7CiAgICB9LAogICAgLyoqIOWIt+aWsOaVsOaNriAqLwogICAgYXN5bmMgcmVmcmVzaERhdGEoKSB7CiAgICAgIGF3YWl0IHRoaXMuY2FsY3VsYXRlU3RhdGlzdGljcygpOwogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+aVsOaNruW3suWIt+aWsCcpOwogICAgfSwKICAgIC8qKiDmtYvor5XkuInkvY3mlbDnu4TlkIjmj5Dlj5bpgLvovpEgKi8KICAgIHRlc3RUaHJlZURpZ2l0RXh0cmFjdGlvbigpIHsKICAgICAgY29uc29sZS5sb2coJz09PSDmtYvor5XkuInkvY3mlbDnu4TlkIjmj5Dlj5blkozlvZLkuIDljJbpgLvovpEgPT09Jyk7CgogICAgICAvLyDmtYvor5XkuI3lkIzplb/luqbnmoTlj7fnoIEKICAgICAgY29uc3QgdGVzdENhc2VzID0gWwogICAgICAgIHthOiAxLCBiOiAyLCBjOiAzfSwgICAgICAgICAgICAgICAgICAgIC8vIOS4ieS9jeaVsAogICAgICAgIHthOiAxLCBiOiAyLCBjOiAzLCBkOiA0fSwgICAgICAgICAgICAgLy8g5Zub5L2N5pWwCiAgICAgICAge2E6IDAsIGI6IDEsIGM6IDIsIGQ6IDMsIGU6IDQsIGY6IDV9LCAvLyDlha3kvY3mlbAKICAgICAgICB7YTogMSwgYjogMiwgYzogMywgZDogNCwgZTogNX0sICAgICAgIC8vIOS6lOS9jeaVsAogICAgICBdOwoKICAgICAgdGVzdENhc2VzLmZvckVhY2goKHRlc3RDYXNlLCBpbmRleCkgPT4gewogICAgICAgIGNvbnNvbGUubG9nKGBcbua1i+ivleeUqOS+iyAke2luZGV4ICsgMX06YCwgdGVzdENhc2UpOwogICAgICAgIGNvbnN0IHRocmVlRGlnaXRzID0gdGhpcy5leHRyYWN0VGhyZWVEaWdpdE51bWJlcnModGVzdENhc2UpOwogICAgICAgIGNvbnNvbGUubG9nKCfmj5Dlj5bnmoTkuInkvY3mlbDnu4TlkIg6JywgdGhyZWVEaWdpdHMpOwoKICAgICAgICAvLyDmtYvor5XlvZLkuIDljJbmlYjmnpwKICAgICAgICBjb25zdCBub3JtYWxpemVkU2V0ID0gbmV3IFNldCgpOwogICAgICAgIHRocmVlRGlnaXRzLmZvckVhY2goZGlnaXQgPT4gewogICAgICAgICAgY29uc3Qgbm9ybWFsaXplZCA9IHRoaXMubm9ybWFsaXplVGhyZWVEaWdpdHMoZGlnaXQpOwogICAgICAgICAgbm9ybWFsaXplZFNldC5hZGQobm9ybWFsaXplZCk7CiAgICAgICAgICBjb25zb2xlLmxvZyhgJHtkaWdpdH0gLT4gJHtub3JtYWxpemVkfWApOwogICAgICAgIH0pOwogICAgICAgIGNvbnNvbGUubG9nKCflvZLkuIDljJblkI7nmoTllK/kuIDnu4TlkIg6JywgQXJyYXkuZnJvbShub3JtYWxpemVkU2V0KSk7CiAgICAgIH0pOwoKICAgICAgY29uc29sZS5sb2coJz09PSDmtYvor5XlrozmiJAgPT09Jyk7CiAgICB9LAogICAgLyoqIOiuoeeul+e7hOWQiOaVsCBDKG4scikgKi8KICAgIGNhbGN1bGF0ZUNvbWJpbmF0aW9ucyhuLCByKSB7CiAgICAgIGlmIChyID4gbikgcmV0dXJuIDA7CiAgICAgIGlmIChyID09PSAwIHx8IHIgPT09IG4pIHJldHVybiAxOwoKICAgICAgbGV0IHJlc3VsdCA9IDE7CiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgcjsgaSsrKSB7CiAgICAgICAgcmVzdWx0ID0gcmVzdWx0ICogKG4gLSBpKSAvIChpICsgMSk7CiAgICAgIH0KICAgICAgcmV0dXJuIE1hdGgucm91bmQocmVzdWx0KTsKICAgIH0sCiAgICAvKiog5YWz6Zet5a+56K+d5qGGICovCiAgICAvKiog5Yqg6L2955So5oi35YiX6KGoICovCiAgICBhc3luYyBsb2FkVXNlckxpc3QoKSB7CiAgICAgIHRyeSB7CiAgICAgICAgLy8g5YWI6I635Y+W5omA5pyJ55So5oi3CiAgICAgICAgY29uc3QgdXNlclJlc3BvbnNlID0gYXdhaXQgcmVxdWVzdCh7CiAgICAgICAgICB1cmw6ICcvc3lzdGVtL3VzZXIvbGlzdCcsCiAgICAgICAgICBtZXRob2Q6ICdnZXQnLAogICAgICAgICAgcGFyYW1zOiB7IHBhZ2VOdW06IDEsIHBhZ2VTaXplOiAxMDAwIH0KICAgICAgICB9KTsKCiAgICAgICAgaWYgKCF1c2VyUmVzcG9uc2UgfHwgIXVzZXJSZXNwb25zZS5yb3dzKSB7CiAgICAgICAgICB0aGlzLnVzZXJMaXN0ID0gW107CiAgICAgICAgICByZXR1cm47CiAgICAgICAgfQoKICAgICAgICBjb25zdCBhbGxVc2VycyA9IHVzZXJSZXNwb25zZS5yb3dzLmZpbHRlcih1c2VyID0+IHVzZXIuc3RhdHVzID09PSAnMCcpOyAvLyDlj6rojrflj5bmraPluLjnirbmgIHnmoTnlKjmiLcKCiAgICAgICAgLy8g6I635Y+W5pyJ5LiL5rOo6K6w5b2V55qE55So5oi3SUTliJfooagKICAgICAgICBjb25zdCByZWNvcmRSZXNwb25zZSA9IGF3YWl0IHJlcXVlc3QoewogICAgICAgICAgdXJsOiAnL2dhbWUvcmVjb3JkL2xpc3QnLAogICAgICAgICAgbWV0aG9kOiAnZ2V0JywKICAgICAgICAgIHBhcmFtczogewogICAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgICBwYWdlU2l6ZTogMTAwMDAwIC8vIOiOt+WPluaJgOacieiusOW9leadpee7n+iuoeeUqOaItwogICAgICAgICAgfQogICAgICAgIH0pOwoKICAgICAgICBpZiAocmVjb3JkUmVzcG9uc2UgJiYgcmVjb3JkUmVzcG9uc2Uucm93cykgewogICAgICAgICAgLy8g57uf6K6h5q+P5Liq55So5oi355qE5LiL5rOo6K6w5b2V5pWw6YePCiAgICAgICAgICBjb25zdCB1c2VyUmVjb3JkQ291bnRzID0ge307CiAgICAgICAgICByZWNvcmRSZXNwb25zZS5yb3dzLmZvckVhY2gocmVjb3JkID0+IHsKICAgICAgICAgICAgY29uc3QgdXNlcklkID0gcmVjb3JkLnN5c1VzZXJJZDsKICAgICAgICAgICAgdXNlclJlY29yZENvdW50c1t1c2VySWRdID0gKHVzZXJSZWNvcmRDb3VudHNbdXNlcklkXSB8fCAwKSArIDE7CiAgICAgICAgICB9KTsKCiAgICAgICAgICAvLyDlj6rkv53nlZnmnInkuIvms6jorrDlvZXnmoTnlKjmiLfvvIzlubbmt7vliqDorrDlvZXmlbDph4/kv6Hmga8KICAgICAgICAgIHRoaXMudXNlckxpc3QgPSBhbGxVc2VycwogICAgICAgICAgICAuZmlsdGVyKHVzZXIgPT4gdXNlclJlY29yZENvdW50c1t1c2VyLnVzZXJJZF0pCiAgICAgICAgICAgIC5tYXAodXNlciA9PiAoewogICAgICAgICAgICAgIC4uLnVzZXIsCiAgICAgICAgICAgICAgcmVjb3JkQ291bnQ6IHVzZXJSZWNvcmRDb3VudHNbdXNlci51c2VySWRdIHx8IDAKICAgICAgICAgICAgfSkpCiAgICAgICAgICAgIC5zb3J0KChhLCBiKSA9PiBiLnJlY29yZENvdW50IC0gYS5yZWNvcmRDb3VudCk7IC8vIOaMieS4i+azqOiusOW9leaVsOmHj+mZjeW6j+aOkuWIlwoKICAgICAgICAgIGNvbnNvbGUubG9nKGDliqDovb3nlKjmiLfliJfooajlrozmiJDvvIzlhbEgJHt0aGlzLnVzZXJMaXN0Lmxlbmd0aH0g5Liq5pyJ5LiL5rOo6K6w5b2V55qE55So5oi3YCk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMudXNlckxpc3QgPSBbXTsKICAgICAgICAgIGNvbnNvbGUud2Fybign5pyq6I635Y+W5Yiw5LiL5rOo6K6w5b2V5pWw5o2uJyk7CiAgICAgICAgfQoKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bnlKjmiLfliJfooajlpLHotKU6JywgZXJyb3IpOwogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPlueUqOaIt+WIl+ihqOWksei0pScpOwogICAgICAgIHRoaXMudXNlckxpc3QgPSBbXTsKICAgICAgfQogICAgfSwKCiAgICAvKiog6I635Y+W5oyH5a6a55So5oi355qE6K6w5b2V5pWw5o2uICovCiAgICBhc3luYyBmZXRjaFVzZXJSZWNvcmREYXRhKHVzZXJJZCkgewogICAgICB0cnkgewogICAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCByZXF1ZXN0KHsKICAgICAgICAgIHVybDogJy9nYW1lL3JlY29yZC9saXN0JywKICAgICAgICAgIG1ldGhvZDogJ2dldCcsCiAgICAgICAgICBwYXJhbXM6IHsKICAgICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgICAgcGFnZVNpemU6IDEwMDAwMCwKICAgICAgICAgICAgc3lzVXNlcklkOiB1c2VySWQKICAgICAgICAgIH0KICAgICAgICB9KTsKCiAgICAgICAgaWYgKHJlc3BvbnNlICYmIHJlc3BvbnNlLnJvd3MpIHsKICAgICAgICAgIHJldHVybiByZXNwb25zZS5yb3dzOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICByZXR1cm4gW107CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlueUqOaIt+iusOW9leaVsOaNruWksei0pTonLCBlcnJvcik7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+W55So5oi36K6w5b2V5pWw5o2u5aSx6LSlJyk7CiAgICAgICAgcmV0dXJuIFtdOwogICAgICB9IGZpbmFsbHkgewogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9CiAgICB9LAoKICAgIC8qKiDnlKjmiLfpgInmi6nlj5jljJblpITnkIYgKi8KICAgIGFzeW5jIGhhbmRsZVVzZXJDaGFuZ2UodXNlcklkKSB7CiAgICAgIC8vIOa4heepuuW9k+WJjee7n+iuoeaVsOaNrgogICAgICB0aGlzLm1ldGhvZFJhbmtpbmdCeUdyb3VwID0gW107CiAgICAgIHRoaXMubnVtYmVyUmFua2luZ0J5R3JvdXAgPSBbXTsKCiAgICAgIGlmICh1c2VySWQpIHsKICAgICAgICAvLyDnm7TmjqXliqDovb3pgInkuK3nlKjmiLfnmoTnu5/orqHmlbDmja4KICAgICAgICBhd2FpdCB0aGlzLmNhbGN1bGF0ZVN0YXRpc3RpY3MoKTsKICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYOW3suWKoOi9veeUqOaItyAke3RoaXMuZ2V0Q3VycmVudFVzZXJOYW1lKCl9IOeahOe7n+iuoeaVsOaNrmApOwogICAgICB9CiAgICB9LAoKICAgIC8qKiDojrflj5blvZPliY3pgInkuK3nlKjmiLfnmoTlkI3np7AgKi8KICAgIGdldEN1cnJlbnRVc2VyTmFtZSgpIHsKICAgICAgaWYgKCF0aGlzLnNlbGVjdGVkVXNlcklkKSByZXR1cm4gJyc7CiAgICAgIGNvbnN0IHVzZXIgPSB0aGlzLnVzZXJMaXN0LmZpbmQodSA9PiB1LnVzZXJJZCA9PT0gdGhpcy5zZWxlY3RlZFVzZXJJZCk7CiAgICAgIHJldHVybiB1c2VyID8gKHVzZXIubmlja05hbWUgfHwgdXNlci51c2VyTmFtZSkgOiAnJzsKICAgIH0sCgogICAgLyoqIOW9qeenjeWIh+aNouWkhOeQhiAqLwogICAgaGFuZGxlTG90dGVyeVR5cGVDaGFuZ2UodGFiKSB7CiAgICAgIGNvbnNvbGUubG9nKGDliIfmjaLliLDlvannp406ICR7dGFiLm5hbWUgPT09ICdmdWNhaScgPyAn56aP5b2pM0QnIDogJ+S9k+W9qVAzJ31gKTsKICAgICAgLy8g5YiH5o2i5b2p56eN5pe277yM6K6h566X5bGe5oCn5Lya6Ieq5Yqo5pu05paw5pi+56S655qE5pWw5o2uCiAgICB9LAoKICAgIC8qKiDliLfmlrDnu5/orqHmlbDmja4gKi8KICAgIGFzeW5jIHJlZnJlc2hTdGF0aXN0aWNzKCkgewogICAgICBpZiAodGhpcy5pc0FkbWluICYmICF0aGlzLnNlbGVjdGVkVXNlcklkKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7flhYjpgInmi6nopoHmn6XnnIvnu5/orqHnmoTnlKjmiLcnKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIHRyeSB7CiAgICAgICAgYXdhaXQgdGhpcy5sb2FkR2FtZU1ldGhvZHMoKTsKICAgICAgICBhd2FpdCB0aGlzLmNhbGN1bGF0ZVN0YXRpc3RpY3MoKTsKICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+e7n+iuoeaVsOaNruW3suWIt+aWsCcpOwogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WIt+aWsOe7n+iuoeWksei0pTonLCBlcnJvcik7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Yi35paw57uf6K6h5aSx6LSlJyk7CiAgICAgIH0KICAgIH0sCgogICAgY2xvc2UoKSB7CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlOwogICAgICAvLyDmuIXnqbrpgInmi6nnirbmgIEKICAgICAgdGhpcy5zZWxlY3RlZFVzZXJJZCA9IG51bGw7CiAgICAgIHRoaXMuYWN0aXZlTG90dGVyeVR5cGUgPSAnZnVjYWknOyAvLyDph43nva7kuLrnpo/lvakKICAgICAgLy8g5riF56m65omA5pyJ57uf6K6h5pWw5o2uCiAgICAgIHRoaXMuY2xlYXJBbGxSYW5raW5nRGF0YSgpOwogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["BetStatistics.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyKA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;;AAIA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BetStatistics.vue", "sourceRoot": "src/views/game/record/components", "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      title=\"下注统计\"\n      :visible.sync=\"dialogVisible\"\n      width=\"95%\"\n      append-to-body\n      style=\"margin-top: -50px;\"\n      :close-on-click-modal=\"false\"\n      class=\"statistics-dialog\">\n\n      <!-- 管理员用户选择器 -->\n      <div v-if=\"isAdmin\" class=\"admin-controls\" style=\"margin-bottom: 20px; padding: 15px; background: #f5f7fa; border-radius: 4px;\">\n        <div style=\"display: flex; align-items: center; gap: 15px;\">\n          <span style=\"font-weight: 500; color: #606266;\">选择用户:</span>\n          <el-select\n            v-model=\"selectedUserId\"\n            placeholder=\"选择用户查看统计\"\n            @change=\"handleUserChange\"\n            clearable\n            style=\"width: 250px;\">\n            <el-option\n              v-for=\"user in userList\"\n              :key=\"user.userId\"\n              :label=\"`${user.nickName || user.userName} (ID: ${user.userId}) - ${user.recordCount}条记录`\"\n              :value=\"user.userId\">\n            </el-option>\n          </el-select>\n          <el-button\n            type=\"primary\"\n            size=\"small\"\n            @click=\"refreshStatistics\"\n            :loading=\"loading\">\n            <i class=\"el-icon-refresh\"></i> 刷新统计\n          </el-button>\n          <span v-if=\"selectedUserId\" style=\"color: #67c23a; font-size: 12px;\">\n            当前查看: {{ getCurrentUserName() }}\n          </span>\n        </div>\n      </div>\n\n      <!-- 彩种选择标签页 - 所有用户都可见 -->\n      <div class=\"lottery-type-tabs-container\" style=\"margin-bottom: 20px; text-align: center;\">\n        <el-tabs v-model=\"activeLotteryType\" @tab-click=\"handleLotteryTypeChange\" type=\"card\">\n          <el-tab-pane name=\"fucai\">\n            <span slot=\"label\">\n              <i class=\"el-icon-medal-1\"></i>\n              福彩3D\n            </span>\n          </el-tab-pane>\n          <el-tab-pane name=\"ticai\">\n            <span slot=\"label\">\n              <i class=\"el-icon-trophy-1\"></i>\n              体彩排三\n            </span>\n          </el-tab-pane>\n        </el-tabs>\n      </div>\n\n      <!-- 管理员未选择用户时的提示 -->\n      <div v-if=\"isAdmin && !selectedUserId\" class=\"admin-no-selection\" style=\"text-align: center; padding: 60px 20px; color: #909399;\">\n        <i class=\"el-icon-user\" style=\"font-size: 48px; margin-bottom: 20px; display: block;\"></i>\n        <h3 style=\"margin: 0 0 10px 0; color: #606266;\">请选择要查看统计的用户</h3>\n        <p style=\"margin: 0; font-size: 14px;\">选择用户后将自动加载该用户的下注统计数据</p>\n      </div>\n\n      <!-- 统计内容区域 -->\n      <div v-if=\"!isAdmin || (isAdmin && selectedUserId)\" class=\"statistics-content\">\n\n        <!-- 当前彩种无数据时的提示 -->\n        <div v-if=\"currentMethodRanking.length === 0 && currentNumberRanking.length === 0\"\n             class=\"no-data-tip\"\n             style=\"text-align: center; padding: 60px 20px; color: #909399;\">\n          <i class=\"el-icon-data-analysis\" style=\"font-size: 48px; margin-bottom: 20px; display: block;\"></i>\n          <h3 style=\"margin: 0 0 10px 0; color: #606266;\">\n            {{ activeLotteryType === 'fucai' ? '福彩3D' : '体彩排三' }} 暂无统计数据\n          </h3>\n          <p style=\"margin: 0; font-size: 14px;\">\n            {{ activeLotteryType === 'fucai' ? '切换到体彩排三查看数据' : '切换到福彩3D查看数据' }}\n          </p>\n        </div>\n          <!-- 按玩法分组的热门三位数排行 -->\n        <div v-if=\"currentNumberRanking.length > 0\" class=\"numbers-rankings-section\">\n          <h3 class=\"main-section-title\">\n            <i class=\"el-icon-data-line\"></i>\n            各玩法热门三位数排行榜\n          </h3>\n\n          <!-- 使用Grid布局，一行显示五个玩法 -->\n          <div class=\"numbers-grid\">\n            <div\n              v-for=\"methodGroup in currentNumberRanking\"\n              :key=\"methodGroup.methodId\"\n              class=\"numbers-card\">\n\n              <div class=\"numbers-card-header\">\n                <span class=\"method-name\">{{ methodGroup.methodName }}</span>\n                <span class=\"method-count\">共{{ methodGroup.ranking.length }}项</span>\n              </div>\n\n              <div class=\"numbers-ranking-list\">\n                <div\n                  v-for=\"item in methodGroup.ranking\"\n                  :key=\"item.rank\"\n                  class=\"numbers-ranking-item\">\n                  <div class=\"rank-badge\" :class=\"getRankClass(item.rank)\">\n                    {{ item.rank }}\n                  </div>\n                  <div class=\"numbers-info\">\n                    <div class=\"hot-number\">{{ item.number }}</div>\n                    <div class=\"amount\">￥{{ item.totalAmount.toFixed(0) }}</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      <div class=\"statistics-container\">\n        <!-- 按玩法分组的投注金额排行 - 两列布局 -->\n        <div v-if=\"currentMethodRanking.length > 0\" class=\"method-rankings-section\">\n          <h3 class=\"main-section-title\">\n            <i class=\"el-icon-trophy\"></i>\n            各玩法投注排行榜\n          </h3>\n\n          <!-- 使用Grid布局，一行显示两个玩法 -->\n          <div class=\"method-grid\">\n            <div\n              v-for=\"methodGroup in currentMethodRanking\"\n              :key=\"methodGroup.methodId\"\n              class=\"method-card\">\n\n              <div class=\"method-card-header\">\n                <span class=\"method-name\">{{ methodGroup.methodName }}</span>\n                <span class=\"method-count\">共{{ methodGroup.ranking.length }}项</span>\n              </div>\n\n              <div class=\"ranking-list\">\n                <div\n                  v-for=\"item in methodGroup.ranking\"\n                  :key=\"item.rank\"\n                  class=\"ranking-item\">\n                  <div class=\"rank-badge\" :class=\"getRankClass(item.rank)\">\n                    {{ item.rank }}\n                  </div>\n                  <div class=\"bet-info\">\n                    <div class=\"bet-numbers\">{{ item.betNumbers }}</div>\n                    <div class=\"amount\">￥{{ item.totalAmount.toFixed(0) }}</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n\n      </div>\n      <!-- 统计内容区域结束 -->\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"refreshData\" type=\"primary\" icon=\"el-icon-refresh\">刷新数据</el-button>\n        <el-button @click=\"close\">关闭</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport request from '@/utils/request'\n\nexport default {\n  name: 'BetStatistics',\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      dialogVisible: false,\n      methodRankingByGroup: [], // 按玩法分组的排行榜\n      numberRankingByGroup: [], // 按玩法分组的号码排行榜\n      gameMethodsData: [], // 玩法数据\n      // 管理员功能相关\n      selectedUserId: null, // 选中的用户ID\n      userList: [], // 用户列表\n      loading: false, // 加载状态\n      // 彩种相关\n      activeLotteryType: 'fucai', // 当前选中的彩种：fucai(福彩3D) 或 ticai(体彩P3)\n      // 分彩种的统计数据\n      fucaiMethodRanking: [], // 福彩投注排行\n      fucaiNumberRanking: [], // 福彩热门三位数排行\n      ticaiMethodRanking: [], // 体彩投注排行\n      ticaiNumberRanking: [] // 体彩热门三位数排行\n    }\n  },\n  computed: {\n    /** 是否是管理员 */\n    isAdmin() {\n      const userInfo = this.$store.getters.userInfo;\n      const roles = this.$store.getters.roles;\n\n      // 检查是否是超级管理员（用户ID为1）\n      if (userInfo && userInfo.userId === 1) {\n        return true;\n      }\n\n      // 检查是否有管理员角色\n      if (roles && roles.length > 0) {\n        return roles.includes('admin') || roles.includes('3d_admin');\n      }\n\n      return false;\n    },\n\n    /** 当前彩种的投注排行数据 */\n    currentMethodRanking() {\n      return this.activeLotteryType === 'fucai' ? this.fucaiMethodRanking : this.ticaiMethodRanking;\n    },\n\n    /** 当前彩种的热门三位数排行数据 */\n    currentNumberRanking() {\n      return this.activeLotteryType === 'fucai' ? this.fucaiNumberRanking : this.ticaiNumberRanking;\n    }\n  },\n  watch: {\n    visible(val) {\n      this.dialogVisible = val;\n      if (val) {\n        // 运行测试\n        this.testThreeDigitExtraction();\n\n        // 如果是管理员，只加载用户列表和玩法数据，不自动计算统计\n        if (this.isAdmin) {\n          this.loadUserList();\n          this.loadGameMethods();\n        } else {\n          // 普通用户自动加载统计\n          this.loadGameMethods().then(async () => {\n            // 普通用户从sessionStorage获取自己的数据\n            const sysUserId = sessionStorage.getItem('sysUserId');\n            console.log('[BetStatistics] sessionStorage中的sysUserId:', sysUserId);\n\n            if (sysUserId) {\n              this.selectedUserId = parseInt(sysUserId);\n              console.log('[BetStatistics] 设置selectedUserId:', this.selectedUserId);\n            } else {\n              console.warn('[BetStatistics] 无法从sessionStorage获取sysUserId');\n            }\n            await this.calculateStatistics();\n          });\n        }\n      }\n    },\n    dialogVisible(val) {\n      this.$emit('update:visible', val);\n    }\n  },\n  methods: {\n    /** 加载玩法数据 */\n    async loadGameMethods() {\n      try {\n        // 直接从API获取玩法数据\n        const response = await request({\n          url: '/game/odds/list',\n          method: 'get',\n          params: { pageNum: 1, pageSize: 1000 }\n        });\n\n        if (response && response.rows) {\n          this.gameMethodsData = response.rows;\n         \n        } else {\n          this.gameMethodsData = [];\n          console.warn('玩法数据为空');\n        }\n      } catch (error) {\n        console.error('获取玩法数据失败:', error);\n        // 如果API失败，使用基本的玩法映射\n        this.gameMethodsData = this.getBasicMethodsData();\n      }\n    },\n    /** 获取基本玩法数据（API失败时的备用方案） */\n    getBasicMethodsData() {\n      return [\n        { methodId: 1, methodName: '独胆' },\n        { methodId: 2, methodName: '一码定位' },\n        { methodId: 3, methodName: '两码组合' },\n        { methodId: 4, methodName: '两码对子' },\n        { methodId: 5, methodName: '三码直选' },\n        { methodId: 6, methodName: '三码组选' },\n        { methodId: 7, methodName: '三码组三' },\n        { methodId: 8, methodName: '四码组六' },\n        { methodId: 9, methodName: '四码组三' },\n        { methodId: 10, methodName: '五码组六' },\n        { methodId: 11, methodName: '五码组三' },\n        { methodId: 12, methodName: '六码组六' },\n        { methodId: 13, methodName: '六码组三' },\n        { methodId: 14, methodName: '七码组六' },\n        { methodId: 15, methodName: '七码组三' },\n        { methodId: 16, methodName: '八码组六' },\n        { methodId: 17, methodName: '八码组三' },\n        { methodId: 18, methodName: '九码组六' },\n        { methodId: 19, methodName: '九码组三' },\n        { methodId: 20, methodName: '包打组六' },\n        { methodId: 21, methodName: '7或20和值' },\n        { methodId: 22, methodName: '8或19和值' },\n        { methodId: 23, methodName: '9或18和值' },\n        { methodId: 24, methodName: '10或17和值' },\n        { methodId: 25, methodName: '11或16和值' },\n        { methodId: 26, methodName: '12或15和值' },\n        { methodId: 27, methodName: '13或14和值' },\n        { methodId: 28, methodName: '直选复式' },\n        { methodId: 29, methodName: '和值单双' },\n        { methodId: 30, methodName: '和值大小' },\n        { methodId: 31, methodName: '包打组三' },\n        { methodId: 100, methodName: '三码防对' }\n      ];\n    },\n    /** 获取玩法名称 */\n    getMethodName(methodId) {\n      const method = this.gameMethodsData.find(m => m.methodId === methodId);\n      return method ? method.methodName : `玩法${methodId}`;\n    },\n    /** 获取排名样式类 */\n    getRankClass(rank) {\n      if (rank === 1) return 'rank-first';\n      if (rank === 2) return 'rank-second';\n      if (rank === 3) return 'rank-third';\n      return 'rank-other';\n    },\n    /** 计算统计数据 */\n    async calculateStatistics() {\n      const data = await this.getRecordData();\n\n\n      if (!data || data.length === 0) {\n        if (this.isAdmin && !this.selectedUserId) {\n          this.$message.warning('请选择要查看统计的用户');\n        } else {\n          this.$message.warning('暂无统计数据，请先刷新record页面');\n        }\n        this.clearAllRankingData();\n        return;\n      }\n\n      // 按彩种分别计算统计数据\n      this.calculateStatisticsByLotteryType(data);\n\n      // 调试信息\n      console.log('统计计算完成:', {\n        福彩投注排行: this.fucaiMethodRanking.length,\n        福彩热门三位数: this.fucaiNumberRanking.length,\n        体彩投注排行: this.ticaiMethodRanking.length,\n        体彩热门三位数: this.ticaiNumberRanking.length,\n        当前选中彩种: this.activeLotteryType\n      });\n    },\n    /** 获取record数据 */\n    async getRecordData() {\n      try {\n        // 如果有选择的用户ID（管理员选择的用户或普通用户自己），直接从API获取数据\n        if (this.selectedUserId) {\n          return await this.fetchUserRecordData(this.selectedUserId);\n        }\n\n        // 如果是管理员但没有选择用户，提示选择用户\n        if (this.isAdmin) {\n          this.$message.warning('请选择要查看统计的用户');\n          return [];\n        }\n\n        // 普通用户：直接从API获取当前用户数据，从sessionStorage获取用户ID\n        console.log('[BetStatistics] 普通用户，直接从API获取数据');\n\n        // 从sessionStorage获取用户ID\n        const sysUserId = sessionStorage.getItem('sysUserId');\n        console.log('[BetStatistics] 从sessionStorage获取的sysUserId:', sysUserId);\n\n        if (sysUserId) {\n          console.log('[BetStatistics] 使用sysUserId调用API:', sysUserId);\n          const data = await this.fetchUserRecordData(parseInt(sysUserId));\n          console.log('[BetStatistics] API获取到的数据:', data);\n\n          if (data && data.length > 0) {\n            console.log(`[BetStatistics] 成功加载了 ${data.length} 条统计数据`);\n          } else {\n            console.warn('[BetStatistics] API返回空数据');\n          }\n\n          return data;\n        } else {\n          console.warn('[BetStatistics] 无法从sessionStorage获取sysUserId');\n          this.$message.warning('无法获取用户信息，请重新登录');\n          return [];\n        }\n      } catch (error) {\n        console.error('解析统计数据失败:', error);\n        return [];\n      }\n    },\n    /** 清空所有排行数据 */\n    clearAllRankingData() {\n      this.fucaiMethodRanking = [];\n      this.fucaiNumberRanking = [];\n      this.ticaiMethodRanking = [];\n      this.ticaiNumberRanking = [];\n    },\n\n    /** 按彩种分别计算统计数据 */\n    calculateStatisticsByLotteryType(data) {\n      // 调试：查看数据结构\n      if (data.length > 0) {\n        console.log('数据样本:', data[0]);\n        console.log('可能的彩种字段:', {\n          lotteryType: data[0].lotteryType,\n          gameType: data[0].gameType,\n          lotteryId: data[0].lotteryId,\n          gameName: data[0].gameName,\n          methodName: data[0].methodName\n        });\n\n        // 统计所有可能的彩种标识\n        const lotteryTypes = [...new Set(data.map(record => record.lotteryType))];\n        const gameTypes = [...new Set(data.map(record => record.gameType))];\n        const lotteryIds = [...new Set(data.map(record => record.lotteryId))];\n        const gameNames = [...new Set(data.map(record => record.gameName))];\n\n        console.log('数据中的彩种标识:', {\n          lotteryTypes,\n          gameTypes,\n          lotteryIds,\n          gameNames\n        });\n      }\n\n      // 按彩种分组数据（使用lotteryId字段）\n      const fucaiData = data.filter(record => this.isFucaiLottery(record.lotteryId));\n      const ticaiData = data.filter(record => this.isTicaiLottery(record.lotteryId));\n\n      console.log(`福彩3D数据: ${fucaiData.length}条, 体彩P3数据: ${ticaiData.length}条`);\n\n      // 如果没有明确的彩种区分，将所有数据归类为福彩3D（兼容性处理）\n      if (fucaiData.length === 0 && ticaiData.length === 0 && data.length > 0) {\n        console.log('未检测到彩种字段，将所有数据归类为福彩3D');\n        this.fucaiMethodRanking = this.calculateMethodRankingByGroup(data);\n        this.fucaiNumberRanking = this.calculateNumberRanking(data);\n        this.ticaiMethodRanking = [];\n        this.ticaiNumberRanking = [];\n        return;\n      }\n\n      // 分别计算福彩和体彩的统计数据\n      if (fucaiData.length > 0) {\n        this.fucaiMethodRanking = this.calculateMethodRankingByGroup(fucaiData);\n        this.fucaiNumberRanking = this.calculateNumberRanking(fucaiData);\n      } else {\n        this.fucaiMethodRanking = [];\n        this.fucaiNumberRanking = [];\n      }\n\n      if (ticaiData.length > 0) {\n        this.ticaiMethodRanking = this.calculateMethodRankingByGroup(ticaiData);\n        this.ticaiNumberRanking = this.calculateNumberRanking(ticaiData);\n      } else {\n        this.ticaiMethodRanking = [];\n        this.ticaiNumberRanking = [];\n      }\n    },\n\n    /** 判断是否为福彩3D */\n    isFucaiLottery(lotteryId) {\n      // 福彩3D的lotteryId是1\n      return lotteryId === 1 || lotteryId === '1';\n    },\n\n    /** 判断是否为体彩P3 */\n    isTicaiLottery(lotteryId) {\n      // 体彩P3的lotteryId是2\n      return lotteryId === 2 || lotteryId === '2';\n    },\n\n    /** 按玩法分组计算排行榜 */\n    calculateMethodRankingByGroup(data) {\n      console.log('[calculateMethodRankingByGroup] 开始计算各玩法投注排行榜');\n\n      // 按玩法分组，相同号码合并累计金额\n      const methodGroups = {};\n\n      data.forEach(record => {\n        const methodId = record.methodId;\n        const amount = parseFloat(record.money) || 0;\n\n        if (!methodGroups[methodId]) {\n          methodGroups[methodId] = new Map(); // 使用Map来存储号码和对应的累计数据\n        }\n\n        // 解析投注号码\n        try {\n          const betNumbers = JSON.parse(record.betNumbers || '{}');\n          const numbers = betNumbers.numbers || [];\n\n          // 为每个号码累计金额\n          numbers.forEach((numberObj) => {\n            let singleNumberDisplay = '';\n\n            if (typeof numberObj === 'object' && numberObj !== null) {\n              // 处理 {a:1, b:2, c:3, d:4, e:5} 格式\n              const keys = Object.keys(numberObj).sort();\n              const values = keys.map(key => String(numberObj[key]));\n              singleNumberDisplay = values.join('');\n            } else {\n              singleNumberDisplay = String(numberObj);\n            }\n\n            // 如果号码已存在，累计金额和记录数\n            if (methodGroups[methodId].has(singleNumberDisplay)) {\n              const existing = methodGroups[methodId].get(singleNumberDisplay);\n              existing.totalAmount += amount;\n              existing.recordCount += 1;\n            } else {\n              // 新号码，创建记录\n              methodGroups[methodId].set(singleNumberDisplay, {\n                betNumbers: singleNumberDisplay,\n                totalAmount: amount,\n                recordCount: 1\n              });\n            }\n          });\n        } catch (error) {\n          console.warn('解析投注号码失败:', record.betNumbers, error);\n          // 如果解析失败，使用原始数据\n          const fallbackNumbers = record.betNumbers || '未知号码';\n\n          if (methodGroups[methodId].has(fallbackNumbers)) {\n            const existing = methodGroups[methodId].get(fallbackNumbers);\n            existing.totalAmount += amount;\n            existing.recordCount += 1;\n          } else {\n            methodGroups[methodId].set(fallbackNumbers, {\n              betNumbers: fallbackNumbers,\n              totalAmount: amount,\n              recordCount: 1\n            });\n          }\n        }\n      });\n\n      console.log('[calculateMethodRankingByGroup] 开始生成排行榜');\n\n      // 为每个玩法生成排行榜并返回\n      return Object.entries(methodGroups).map(([methodId, numbersMap]) => {\n        // 将Map转换为数组并按总金额降序排序\n        const ranking = Array.from(numbersMap.values())\n          .sort((a, b) => b.totalAmount - a.totalAmount)\n          .map((item, index) => ({\n            rank: index + 1,\n            betNumbers: item.betNumbers,\n            totalAmount: item.totalAmount,\n            recordCount: item.recordCount\n          }));\n\n        console.log(`[calculateMethodRankingByGroup] 玩法${methodId}(${this.getMethodName(parseInt(methodId))})：${ranking.length}个不同号码`);\n\n        return {\n          methodId: parseInt(methodId),\n          methodName: this.getMethodName(parseInt(methodId)),\n          ranking\n        };\n      }).filter(group => group.ranking.length > 0); // 只显示有数据的玩法\n    },\n    /** 格式化投注号码用于显示 */\n    formatBetNumbersForDisplay(betNumbersKey) {\n      try {\n        const numbers = JSON.parse(betNumbersKey);\n        if (Array.isArray(numbers) && numbers.length > 0) {\n          return numbers.map(num => {\n            if (typeof num === 'object' && num !== null) {\n              // 处理 {a:1, b:2, c:3, d:4, e:5} 格式\n              const keys = Object.keys(num).sort();\n              const values = keys.map(key => String(num[key]));\n              return values.join('');\n            }\n            return String(num);\n          }).join(', ');\n        }\n      } catch (error) {\n        // 如果解析失败，直接返回原始字符串\n      }\n      return betNumbersKey.length > 20 ? betNumbersKey.substring(0, 20) + '...' : betNumbersKey;\n    },\n    /** 按玩法分组计算号码排行 - 只使用money字段 */\n    calculateNumberRanking(data) {\n      // 初始化返回数组\n      const numberRankingByGroup = [];\n\n      // 按玩法分组统计\n      const methodGroups = {};\n\n      data.forEach(record => {\n        try {\n          const methodId = record.methodId;\n          const money = parseFloat(record.money || 0);\n          const betNumbers = JSON.parse(record.betNumbers || '{}');\n          const numbers = betNumbers.numbers || [];\n\n          // 初始化玩法分组\n          if (!methodGroups[methodId]) {\n            methodGroups[methodId] = {\n              methodTotal: 0,\n              threeDigitMap: new Map()\n            };\n          }\n\n          methodGroups[methodId].methodTotal += money;\n\n          // 处理每个投注号码\n          numbers.forEach(numberObj => {\n            const threeDigits = this.extractThreeDigitNumbers(numberObj);\n\n            // 对当前号码的三位数组合去重（避免同一号码内重复组合多次累计）\n            const uniqueNormalizedDigits = new Set();\n\n            threeDigits.forEach(digit => {\n              // 归一化三位数：将数字按从小到大排序作为统一key\n              const normalizedDigit = this.normalizeThreeDigits(digit);\n              uniqueNormalizedDigits.add(normalizedDigit);\n            });\n\n            // 每个归一化后的三位数累加金额\n            uniqueNormalizedDigits.forEach(normalizedDigit => {\n              const currentAmount = methodGroups[methodId].threeDigitMap.get(normalizedDigit) || 0;\n              methodGroups[methodId].threeDigitMap.set(normalizedDigit, currentAmount + money);\n            });\n          });\n\n        } catch (error) {\n          console.error('解析失败:', error);\n        }\n      });\n\n\n\n      // 生成排行榜\n   \n      this.numberRankingByGroup = [];\n\n      Object.entries(methodGroups).forEach(([methodId, group]) => {\n        // 转换Map为数组并排序，显示所有数据\n        const sortedThreeDigits = Array.from(group.threeDigitMap.entries())\n          .sort(([,a], [,b]) => b - a);\n\n        const ranking = sortedThreeDigits.map(([number, amount], index) => ({\n          rank: index + 1,\n          number,\n          count: 1,\n          totalAmount: amount,\n          percentage: group.methodTotal > 0 ? ((amount / group.methodTotal) * 100).toFixed(1) : '0.0'\n        }));\n\n        if (ranking.length > 0) {\n          numberRankingByGroup.push({\n            methodId: parseInt(methodId),\n            methodName: this.getMethodName(parseInt(methodId)),\n            ranking\n          });\n        }\n      });\n\n      return numberRankingByGroup;\n    },\n    /** 从投注号码中提取所有可能的三位数组合 */\n    extractThreeDigitNumbers(numberObj) {\n      const numbers = [];\n\n      // 处理不同的号码格式\n      if (typeof numberObj === 'string') {\n        // 直接是字符串格式的号码\n        const cleanStr = numberObj.replace(/\\D/g, ''); // 移除非数字字符\n        this.generateThreeDigitCombinations(cleanStr.split(''), numbers);\n      } else if (typeof numberObj === 'object' && numberObj !== null) {\n        // 对象格式 {a: 1, b: 2, c: 3, d: 4, e: 5}\n        const keys = Object.keys(numberObj).sort();\n        const digits = keys.map(key => String(numberObj[key]));\n\n    \n\n        // 生成所有可能的三位数组合\n        this.generateThreeDigitCombinations(digits, numbers);\n      }\n\n      return numbers;\n    },\n    /** 生成所有可能的三位数组合 */\n    generateThreeDigitCombinations(digits, numbers) {\n      const n = digits.length;\n\n      // 如果数字少于3个，无法组成三位数\n      if (n < 3) {\n        return;\n      }\n\n      // 生成所有可能的三位数组合（C(n,3)）\n      for (let i = 0; i < n - 2; i++) {\n        for (let j = i + 1; j < n - 1; j++) {\n          for (let k = j + 1; k < n; k++) {\n            const threeDigit = digits[i] + digits[j] + digits[k];\n            if (/^\\d{3}$/.test(threeDigit)) {\n              numbers.push(threeDigit);\n            }\n          }\n        }\n      }\n    },\n\n    /** 归一化三位数：将数字按从小到大排序，统一相同数字的不同排列 */\n    normalizeThreeDigits(threeDigit) {\n      // 将三位数字符串转换为数组，排序后重新组合\n      return threeDigit.split('').sort().join('');\n    },\n    /** 刷新数据 */\n    async refreshData() {\n      await this.calculateStatistics();\n      this.$message.success('数据已刷新');\n    },\n    /** 测试三位数组合提取逻辑 */\n    testThreeDigitExtraction() {\n      console.log('=== 测试三位数组合提取和归一化逻辑 ===');\n\n      // 测试不同长度的号码\n      const testCases = [\n        {a: 1, b: 2, c: 3},                    // 三位数\n        {a: 1, b: 2, c: 3, d: 4},             // 四位数\n        {a: 0, b: 1, c: 2, d: 3, e: 4, f: 5}, // 六位数\n        {a: 1, b: 2, c: 3, d: 4, e: 5},       // 五位数\n      ];\n\n      testCases.forEach((testCase, index) => {\n        console.log(`\\n测试用例 ${index + 1}:`, testCase);\n        const threeDigits = this.extractThreeDigitNumbers(testCase);\n        console.log('提取的三位数组合:', threeDigits);\n\n        // 测试归一化效果\n        const normalizedSet = new Set();\n        threeDigits.forEach(digit => {\n          const normalized = this.normalizeThreeDigits(digit);\n          normalizedSet.add(normalized);\n          console.log(`${digit} -> ${normalized}`);\n        });\n        console.log('归一化后的唯一组合:', Array.from(normalizedSet));\n      });\n\n      console.log('=== 测试完成 ===');\n    },\n    /** 计算组合数 C(n,r) */\n    calculateCombinations(n, r) {\n      if (r > n) return 0;\n      if (r === 0 || r === n) return 1;\n\n      let result = 1;\n      for (let i = 0; i < r; i++) {\n        result = result * (n - i) / (i + 1);\n      }\n      return Math.round(result);\n    },\n    /** 关闭对话框 */\n    /** 加载用户列表 */\n    async loadUserList() {\n      try {\n        // 先获取所有用户\n        const userResponse = await request({\n          url: '/system/user/list',\n          method: 'get',\n          params: { pageNum: 1, pageSize: 1000 }\n        });\n\n        if (!userResponse || !userResponse.rows) {\n          this.userList = [];\n          return;\n        }\n\n        const allUsers = userResponse.rows.filter(user => user.status === '0'); // 只获取正常状态的用户\n\n        // 获取有下注记录的用户ID列表\n        const recordResponse = await request({\n          url: '/game/record/list',\n          method: 'get',\n          params: {\n            pageNum: 1,\n            pageSize: 100000 // 获取所有记录来统计用户\n          }\n        });\n\n        if (recordResponse && recordResponse.rows) {\n          // 统计每个用户的下注记录数量\n          const userRecordCounts = {};\n          recordResponse.rows.forEach(record => {\n            const userId = record.sysUserId;\n            userRecordCounts[userId] = (userRecordCounts[userId] || 0) + 1;\n          });\n\n          // 只保留有下注记录的用户，并添加记录数量信息\n          this.userList = allUsers\n            .filter(user => userRecordCounts[user.userId])\n            .map(user => ({\n              ...user,\n              recordCount: userRecordCounts[user.userId] || 0\n            }))\n            .sort((a, b) => b.recordCount - a.recordCount); // 按下注记录数量降序排列\n\n          console.log(`加载用户列表完成，共 ${this.userList.length} 个有下注记录的用户`);\n        } else {\n          this.userList = [];\n          console.warn('未获取到下注记录数据');\n        }\n\n      } catch (error) {\n        console.error('获取用户列表失败:', error);\n        this.$message.error('获取用户列表失败');\n        this.userList = [];\n      }\n    },\n\n    /** 获取指定用户的记录数据 */\n    async fetchUserRecordData(userId) {\n      try {\n        this.loading = true;\n        const response = await request({\n          url: '/game/record/list',\n          method: 'get',\n          params: {\n            pageNum: 1,\n            pageSize: 100000,\n            sysUserId: userId\n          }\n        });\n\n        if (response && response.rows) {\n          return response.rows;\n        } else {\n          return [];\n        }\n      } catch (error) {\n        console.error('获取用户记录数据失败:', error);\n        this.$message.error('获取用户记录数据失败');\n        return [];\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    /** 用户选择变化处理 */\n    async handleUserChange(userId) {\n      // 清空当前统计数据\n      this.methodRankingByGroup = [];\n      this.numberRankingByGroup = [];\n\n      if (userId) {\n        // 直接加载选中用户的统计数据\n        await this.calculateStatistics();\n        this.$message.success(`已加载用户 ${this.getCurrentUserName()} 的统计数据`);\n      }\n    },\n\n    /** 获取当前选中用户的名称 */\n    getCurrentUserName() {\n      if (!this.selectedUserId) return '';\n      const user = this.userList.find(u => u.userId === this.selectedUserId);\n      return user ? (user.nickName || user.userName) : '';\n    },\n\n    /** 彩种切换处理 */\n    handleLotteryTypeChange(tab) {\n      console.log(`切换到彩种: ${tab.name === 'fucai' ? '福彩3D' : '体彩P3'}`);\n      // 切换彩种时，计算属性会自动更新显示的数据\n    },\n\n    /** 刷新统计数据 */\n    async refreshStatistics() {\n      if (this.isAdmin && !this.selectedUserId) {\n        this.$message.warning('请先选择要查看统计的用户');\n        return;\n      }\n\n      try {\n        await this.loadGameMethods();\n        await this.calculateStatistics();\n        this.$message.success('统计数据已刷新');\n      } catch (error) {\n        console.error('刷新统计失败:', error);\n        this.$message.error('刷新统计失败');\n      }\n    },\n\n    close() {\n      this.dialogVisible = false;\n      // 清空选择状态\n      this.selectedUserId = null;\n      this.activeLotteryType = 'fucai'; // 重置为福彩\n      // 清空所有统计数据\n      this.clearAllRankingData();\n    }\n  }\n}\n</script>\n\n<style scoped>\n/* 管理员控制区域样式 */\n.admin-controls {\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n}\n\n.admin-controls .el-select {\n  background: white;\n}\n\n/* 彩种标签页样式 */\n.lottery-type-tabs {\n  margin-left: 20px;\n  flex: 1;\n}\n\n.lottery-type-tabs-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header {\n  border-bottom: none;\n  margin: 0;\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__nav,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__nav {\n  border: none;\n  border-radius: 8px;\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n  padding: 4px;\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__item,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__item {\n  border: none;\n  background: transparent;\n  color: #606266;\n  font-weight: 500;\n  padding: 8px 16px;\n  margin-right: 4px;\n  border-radius: 6px;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__item:hover,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__item:hover {\n  color: #409eff;\n  background: rgba(64, 158, 255, 0.1);\n  transform: translateY(-1px);\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__item.is-active,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__item.is-active {\n  color: white;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  font-weight: 600;\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n  transform: translateY(-1px);\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__item.is-active::before,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__item.is-active::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, transparent 100%);\n  pointer-events: none;\n}\n\n.lottery-type-tabs .el-tabs--card > .el-tabs__header .el-tabs__item i,\n.lottery-type-tabs-container .el-tabs--card > .el-tabs__header .el-tabs__item i {\n  margin-right: 6px;\n  font-size: 16px;\n}\n\n.lottery-type-tabs .el-tabs__content,\n.lottery-type-tabs-container .el-tabs__content {\n  display: none; /* 隐藏内容区域，因为我们只用标签页切换 */\n}\n\n.statistics-dialog {\n  .el-dialog {\n    border-radius: 12px;\n  }\n  \n  .el-dialog__header {\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    color: white;\n    padding: 20px 24px;\n  }\n  \n  .el-dialog__title {\n    color: white;\n    font-weight: 600;\n    font-size: 18px;\n  }\n}\n\n.statistics-container {\n  padding: 10px 0;\n}\n\n.main-section-title {\n  display: flex;\n  align-items: center;\n  font-size: 16px;\n  font-weight: 600;\n  color: #2c3e50;\n  margin-bottom: 8px;\n  padding-bottom: 4px;\n  border-bottom: 1px solid #ecf0f1;\n}\n\n.main-section-title i {\n  margin-right: 4px;\n  color: #667eea;\n  font-size: 14px;\n}\n\n/* 玩法排行榜样式 */\n.method-rankings-section {\n  margin-bottom: 15px;\n}\n\n.method-grid {\n  display: grid;\n  grid-template-columns: repeat(5, 1fr);\n  gap: 6px;\n}\n\n@media (max-width: 1400px) {\n  .method-grid {\n    grid-template-columns: repeat(4, 1fr);\n  }\n}\n\n@media (max-width: 1100px) {\n  .method-grid {\n    grid-template-columns: repeat(3, 1fr);\n  }\n}\n\n@media (max-width: 800px) {\n  .method-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n\n@media (max-width: 500px) {\n  .method-grid {\n    grid-template-columns: 1fr;\n  }\n}\n\n.method-card {\n  background: white;\n  border-radius: 10px;\n  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.12);\n  overflow: hidden;\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n  min-width: 0; /* 防止内容溢出 */\n  min-height: 200px; /* 最小高度 */\n  max-height: 500px; /* 增加最大高度以适应更多数据 */\n}\n\n.method-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.18);\n}\n\n.method-card-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 8px 12px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.method-name {\n  font-size: 15px;\n  font-weight: 700;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.method-count {\n  font-size: 13px;\n  opacity: 0.9;\n}\n\n.ranking-list {\n  padding: 8px;\n  max-height: 420px; /* 增加列表高度以适应更多数据 */\n  overflow-y: auto; /* 添加垂直滚动条 */\n}\n\n/* 滚动条样式 */\n.ranking-list::-webkit-scrollbar {\n  width: 4px;\n}\n\n.ranking-list::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 2px;\n}\n\n.ranking-list::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 2px;\n}\n\n.ranking-list::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n/* 热门三位数排行榜滚动条样式 */\n.numbers-ranking-list::-webkit-scrollbar {\n  width: 4px;\n}\n\n.numbers-ranking-list::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 2px;\n}\n\n.numbers-ranking-list::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 2px;\n}\n\n.numbers-ranking-list::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n.ranking-item {\n  display: flex;\n  align-items: center;\n  padding: 4px 8px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.ranking-item:last-child {\n  border-bottom: none;\n}\n\n.rank-badge {\n  min-width: 18px;\n  height: 18px;\n  padding: 0 4px;\n  border-radius: 9px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  font-size: 10px;\n  color: white;\n  flex: 0 0 auto;\n  margin-right: 8px;\n  white-space: nowrap;\n}\n\n.rank-first {\n  background: linear-gradient(135deg, #ff6b6b, #ee5a24);\n}\n\n.rank-second {\n  background: linear-gradient(135deg, #feca57, #ff9ff3);\n}\n\n.rank-third {\n  background: linear-gradient(135deg, #48dbfb, #0abde3);\n}\n\n.rank-other {\n  background: linear-gradient(135deg, #a4b0be, #747d8c);\n}\n\n.bet-info {\n  display: flex;\n  align-items: center;\n  flex: 1;\n  min-width: 0;\n}\n\n.bet-numbers {\n  font-size: 15px;\n  font-weight: 900;\n  color: #ffffff;\n  font-family: 'Courier New', monospace;\n  background: linear-gradient(135deg, #667eea, #764ba2);\n  padding: 3px 8px;\n  border-radius: 5px;\n  text-align: center;\n  box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);\n  letter-spacing: 0.5px;\n  flex: 2;\n  margin-right: 8px;\n}\n\n.amount {\n  color: #e74c3c;\n  font-weight: 700;\n  font-size: 14px;\n  white-space: nowrap;\n  flex: 1;\n  text-align: right;\n}\n\n.record-count {\n  color: #95a5a6;\n  font-size: 11px;\n  text-align: right;\n  margin-top: 2px;\n}\n\n/* 热门三位数样式 */\n.numbers-section {\n  margin-bottom: 30px;\n}\n\n.ranking-table {\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.amount-text {\n  font-weight: 600;\n  color: #e74c3c;\n}\n\n.percentage-text {\n  font-weight: 500;\n  color: #3498db;\n}\n\n/* 热门三位数卡片样式 */\n.numbers-rankings-section {\n  margin-bottom: 15px;\n}\n\n.numbers-grid {\n  display: grid;\n  grid-template-columns: repeat(5, 1fr);\n  gap: 6px;\n}\n\n@media (max-width: 1400px) {\n  .numbers-grid {\n    grid-template-columns: repeat(4, 1fr);\n  }\n}\n\n@media (max-width: 1100px) {\n  .numbers-grid {\n    grid-template-columns: repeat(3, 1fr);\n  }\n}\n\n@media (max-width: 800px) {\n  .numbers-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n\n@media (max-width: 500px) {\n  .numbers-grid {\n    grid-template-columns: 1fr;\n  }\n}\n\n.numbers-card {\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n  min-width: 0;\n  min-height: 120px;\n  max-height: 400px; /* 增加最大高度以适应更多数据 */\n}\n\n.numbers-card:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.numbers-card-header {\n  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);\n  color: white;\n  padding: 4px 6px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.numbers-ranking-list {\n  padding: 4px;\n  max-height: 320px; /* 限制列表高度以适应更多数据 */\n  overflow-y: auto; /* 添加垂直滚动条 */\n}\n\n.numbers-ranking-item {\n  display: flex;\n  align-items: center;\n  padding: 2px 4px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.numbers-ranking-item:last-child {\n  border-bottom: none;\n}\n\n.numbers-info {\n  display: flex;\n  align-items: center;\n  flex: 1;\n  min-width: 0;\n}\n\n.numbers-info .hot-number {\n  font-family: 'Courier New', monospace;\n  font-weight: 900;\n  font-size: 13px;\n  color: #ffffff;\n  background: linear-gradient(135deg, #27ae60, #2ecc71);\n  padding: 2px 4px;\n  border-radius: 3px;\n  text-align: center;\n  flex: 2;\n  margin-right: 4px;\n}\n\n.numbers-info .amount {\n  color: #e74c3c;\n  font-weight: 700;\n  font-size: 13px;\n  white-space: nowrap;\n  flex: 1;\n  text-align: right;\n}\n\n\n\n.dialog-footer {\n  text-align: right;\n  padding-top: 20px;\n  border-top: 1px solid #ecf0f1;\n}\n</style>\n"]}]}