(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0abc0c"],{"172b":function(e,t,n){"use strict";n.r(t),n.d(t,"FOUR_CODE_PATTERNS",(function(){return I}));var r=n("2909"),d=n("3835"),a=n("b85c"),o=(n("99af"),n("4de4"),n("a15b"),n("d81d"),n("14d9"),n("4ec9"),n("b680"),n("b64b"),n("d3b7"),n("4d63"),n("c607"),n("ac1f"),n("2c3e"),n("00b4"),n("25f0"),n("8a79"),n("3ca3"),n("466d"),n("5319"),n("1276"),n("498a"),n("0643"),n("76d6"),n("2382"),n("4e3e"),n("a573"),n("9a9a"),n("159b"),n("ddb0"),n("f9d6")),I={ZULIU_UNIVERSAL_SUPER:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、—＝=·吊赶打各]+)[\-~～@#￥%…&!、\\/*,，.—＝=·\s+吊赶打各]+([0-9]+)(元|米|块)?$/,handler:function(e,t){var r=n("f9d6"),d=r.SUPER_SEPARATORS,a=r.METHOD_ID,o=e[1].split(d).map((function(e){return e.trim()})).filter(Boolean),I=e[2];return 1===o.length&&4===o[0].length?[{methodId:a.SIMA_ZULIU,betNumbers:o[0],money:I}]:o.length>=2&&o.every((function(e){return 4===e.length}))?[{methodId:a.SIMA_ZULIU,betNumbers:o.join(","),money:I}]:null}},SIMA_MULTI_TRANSFORM_EACH:{pattern:/^(\d{4}(?:[\-~～@#￥%…&!、\\/*一－。,，.＝=·\s+吊赶打各]+\d{4})*)(转|转各|套|套各)(\d+)\+(\d+)$/,handler:function(e,t){var r,d=n("f9d6"),o=d.SUPER_SEPARATORS,I=d.METHOD_ID,u=d.generateTransformNumbers,l=e[1].split(o).filter(Boolean),_=e[3],s=e[4],i=[],f=Object(a["a"])(l);try{for(f.s();!(r=f.n()).done;){var U=r.value,m=u(U);i.push({methodId:I.WUMA_ZULIU,betNumbers:m.join(","),money:_}),i.push({methodId:I.WUMA_ZUSAN,betNumbers:m.join(","),money:s})}}catch(h){f.e(h)}finally{f.f()}return i}},SIMA_TRANSFORM:{pattern:/^(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?(\d{4})[\-—~～@#￥%…&!、\\/*,，.＝=各·\s]*?(转|套|拖)(?:各)?(\d+)(?:\+(\d+))?(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?$/,handler:function(e,t){n("f9d6").parseLotteryType(t);var r=e[2],d=e[4],a=e[5],o=n("f9d6").generateTransformNumbers(r),I=[];return a?(I.push({methodId:n("f9d6").METHOD_ID.WUMA_ZULIU,betNumbers:o.join(","),money:d}),I.push({methodId:n("f9d6").METHOD_ID.WUMA_ZUSAN,betNumbers:o.join(","),money:a})):I.push({methodId:n("f9d6").METHOD_ID.WUMA_ZULIU,betNumbers:o.join(","),money:d}),I}},SIMA_ZULIU:{pattern:/^(\d{4})(?:组六)?(?:[-一－。]?(\d+)(?:\+(\d+))?)?$|^(\d{4})组六(\d+)$/,handler:function(e,t){var n,r,d;if(e[1]&&(e[2]||e[3])?(n=e[1],r=e[2],d=e[3]):e[4]&&e[5]?(n=e[4],r=e[5]):e[1]&&(n=e[1],r=""),!n||4!==n.length)return null;Object(o["parseLotteryType"])(t);var a=[{methodId:o["METHOD_ID"].SIMA_ZULIU,betNumbers:n,money:r}];return d&&a.push({methodId:o["METHOD_ID"].SIMA_ZUSAN,betNumbers:n,money:d}),console.log("四码 handler 返回:",a),a}},SIMA_ZUSAN:{pattern:/^(\d{4})[\-—~～@#￥%…&!、\\/*,，一－.＝=·\s]*组三?[\-—~～@#￥%…&!、\\/*,，。，一－.＝=·\s]*([\d]+)?(元|块|米)?[\-—~～@#￥%…&!、。，一－\\/*,，.＝=·\s]*$/,handler:function(e,t){if(4!==e[1].length)return null;Object(o["parseLotteryType"])(t);return[{methodId:o["METHOD_ID"].SIMA_ZUSAN,betNumbers:e[1],money:e[2]||""}]}},SIMA_FANGDUI:{pattern:/^(\d{4})防对?(?:-?(\d+))?$/,handler:function(e,t){if(4!==e[1].length)return null;Object(o["parseLotteryType"])(t);return[{methodId:o["METHOD_ID"].SIMA_ZUSAN,betNumbers:e[1],money:e[2]||""}]}},MULTI_SIMA:{pattern:/^(\d{4}(?:[^0-9]+\d{4})*)(?:各([一二三四五六七八九十百千万\d]+))?(?:防([一二三四五六七八九十百千万\d]+))?$/,handler:function(e,t){var r=n("f9d6"),d=r.chineseToNumber,a=t.split(o["BASE_PATTERNS"].SEPARATORS).filter(Boolean).filter((function(e){return 4===e.length}));if(!a.length||!a.every((function(e){return 4===e.length})))return null;var I=e[2]?d(e[2]):"",u=e[3]?d(e[3]):"",l=[];return I&&l.push({methodId:o["METHOD_ID"].SIMA_ZULIU,betNumbers:a.join(","),money:I}),u&&l.push({methodId:o["METHOD_ID"].SIMA_ZUSAN,betNumbers:a[a.length-1],money:u}),l.length?l:null}},MULTI_NUMBERS:{pattern:/^(\d{6}(?:\/\d{6})*?)各(?:打)?(\d+)$/,handler:function(e,t){Object(o["parseLotteryType"])(t);var n=e[1].split("/"),r=e[2];return[{methodId:o["METHOD_ID"].LIUMA_ZULIU,betNumbers:n.join(","),money:r}]}},SIMA_COMBO:{pattern:/^(\d{4}(?:\/\d{4})*?)\/(\d+)\+(\d+)$/,handler:function(e,t){var n=t.split("/"),r=n.pop().split("+")[0],d=r,a=n.pop().split("+")[1],I=(Object(o["parseLotteryType"])(t),[]);return n.forEach((function(e){4===e.length&&(I.push({methodId:o["METHOD_ID"].SIMA_ZULIU,betNumbers:e,money:d}),I.push({methodId:o["METHOD_ID"].SIMA_ZUSAN,betNumbers:e,money:a}))})),console.log("四码 handler 返回:",I),I}},SIMA_ZULIU_FANG:{pattern:/^(\d{4})-(\d+)防(\d+)$/,handler:function(e,t){var n=e[1],r=e[2],d=e[3];Object(o["parseLotteryType"])(t);return[{methodId:o["METHOD_ID"].SIMA_ZULIU,betNumbers:n,money:r},{methodId:o["METHOD_ID"].SIMA_ZUSAN,betNumbers:n,money:d}]}},SIMA_MONEY_UNIT_FANGDUI_SUPER:{pattern:/^(\d{4})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(\d+)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(?:元|米|块)?[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*(防|防对|组三)[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]*$/,handler:function(e,t){var n=e[1],r=e[2];e[3];return 4!==n.length?null:[{methodId:o["METHOD_ID"].SIMA_ZUSAN,betNumbers:n,money:r}]}},SIMA_ZULIU_FANG_SUPER:{pattern:/^([0-9]{4}(?:[\-~～@#￥%…&!、\\/*,，一.－＝=·\s+吊赶打各]+[0-9]{4})*)[\-~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各]+(\d+)[\-~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各]*防[\-~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各]*(\d+)$/,handler:function(e,t){var r=n("f9d6"),d=r.SUPER_SEPARATORS,a=e[1].split(d).filter(Boolean),o=e[2],I=e[3];n("f9d6").parseLotteryType(t);return[{methodId:n("f9d6").METHOD_ID.SIMA_ZULIU,betNumbers:a.join(","),money:o},{methodId:n("f9d6").METHOD_ID.SIMA_ZUSAN,betNumbers:a.join(","),money:I}]}},SIMA_ZULIU_SIMPLE_SUPER:{pattern:/^(\d{4})[\-~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各]+(\d+)$/,handler:function(e,t){var r=e[1],d=e[2];n("f9d6").parseLotteryType(t);return[{methodId:n("f9d6").METHOD_ID.SIMA_ZULIU,betNumbers:r,money:d}]}},SIMA_ZULIU_ZU_SUPER:{pattern:/^(\d{4})组(\d+)$/,handler:function(e,t){var r=e[1],d=e[2];n("f9d6").parseLotteryType(t);return[{methodId:n("f9d6").METHOD_ID.SIMA_ZULIU,betNumbers:r,money:d}]}},SIMA_ZULIU_MULTI_SUPER:{pattern:/^(\d{4}(?:[\-~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各]+\d{4})*)[\-~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各]+(\d+)$/,handler:function(e,t){var r=n("f9d6"),d=r.SUPER_SEPARATORS,a=e[1].split(d).filter(Boolean),o=e[2];n("f9d6").parseLotteryType(t);return[{methodId:n("f9d6").METHOD_ID.SIMA_ZULIU,betNumbers:a.join(","),money:o}]}},SIMA_ZHIZU_MULTI_SUPER:{pattern:/^(\d{4}(?:[\-~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各]+\d{4})*)[\-~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各]*(直组|组直|值组|组值)[\s]*各?(\d+)(?:元)?$/,handler:function(e,t){var r=n("f9d6"),d=r.SUPER_SEPARATORS,a=e[1].split(d).filter(Boolean),o=e[3];n("f9d6").parseLotteryType(t);return[{methodId:n("f9d6").METHOD_ID.SIMA_ZULIU,betNumbers:a.join(","),money:o},{methodId:n("f9d6").METHOD_ID.SIMA_ZUSAN,betNumbers:a.join(","),money:o}]}},SIMA_ZULIU_ZUSAN_MULTI_MONEY_SUPER:{pattern:/^(\d{4}(?:[－\-—~～@#￥%…&!、\\/*,一.－。＝=·\s吊赶打各]+\d{4})*)[\-—~～@#￥%…&!、.一－。\\/*,一－。＝=·\s吊赶打各]*(?:各)?(\d+)(?:元)?\+(\d+)(?:元)?$/,handler:function(e,t){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],d=n("f9d6"),a=d.SUPER_SEPARATORS,o=e[1].split(a).filter(Boolean),I=e[2],u=e[3];if(r&&(/^\d{4}$/.test(I)||/^\d{4}$/.test(u)))return null;n("f9d6").parseLotteryType(t);return[{methodId:n("f9d6").METHOD_ID.SIMA_ZULIU,betNumbers:o.join(","),money:I},{methodId:n("f9d6").METHOD_ID.SIMA_ZUSAN,betNumbers:o.join(","),money:u}]}},SIMA_MULTI_ZHIZU_EACH_SUPER:{pattern:/^(直组|组直|值组|组值)各([0-9]+)[\s\-~～@#￥%…&!、\\/*,，.＝=·吊赶打各\n]+([\d\s\-~～@#￥%…&!、\\/*,，.＝=·吊赶打各]+)$/i,handler:function(e,t){var r=n("f9d6"),d=r.SUPER_SEPARATORS,a=e[2],o=e[3].replace(/\n/g," "),I=o.split(d).map((function(e){return e.trim()})).filter((function(e){return/^\d{4}$/.test(e)}));n("f9d6").parseLotteryType(t);return[{methodId:n("f9d6").METHOD_ID.SIMA_ZULIU,betNumbers:I.join(","),money:a},{methodId:n("f9d6").METHOD_ID.SIMA_ZUSAN,betNumbers:I.join(","),money:a}]}},SIMA_MULTI_EACH_SUPER:{pattern:/^(\d{4}(?:[\-~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各]+\d{4})*)各(\d+)$/,handler:function(e,t){var r=n("f9d6"),d=r.SUPER_SEPARATORS,a=e[1].split(d).filter(Boolean);if(!a.length||a.some((function(e){return 4!==e.length})))return[];var o=e[2];n("f9d6").parseLotteryType(t);return[{methodId:n("f9d6").METHOD_ID.SIMA_ZULIU,betNumbers:a.join(","),money:o}]}},SIMA_MULTI_SUPER_SLASH_TWO_MONEY:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、＝=·吊赶打各]+)\/(\d+)\+(\d+)$/,handler:function(e,t){var r=n("f9d6"),d=r.SUPER_SEPARATORS,a=e[1].split(d).map((function(e){return e.trim()})).filter((function(e){return 4===e.length}));if(!a.length)return[];var o=e[2],I=e[3];n("f9d6").parseLotteryType(t);return[{methodId:n("f9d6").METHOD_ID.SIMA_ZULIU,betNumbers:a.join(","),money:o},{methodId:n("f9d6").METHOD_ID.SIMA_ZUSAN,betNumbers:a.join(","),money:I}]}},SIMA_ZULIU_WHOLE_MONEY:{pattern:/^([0-9]{4,})\s+(\d+)(元|块|米)?$/,handler:function(e,t){var r=e[1],d=e[2];n("f9d6").parseLotteryType(t);return[{methodId:n("f9d6").METHOD_ID.SIMA_ZULIU,betNumbers:r,money:d}]}},SIMA_DA_MONEY:{pattern:/^([0-9]{4})打(\d+)(元|块|米)?$/,handler:function(e,t){return[{methodId:n("f9d6").METHOD_ID.SIMA_ZULIU,betNumbers:e[1],money:e[2]}]}},SIMA_MULTI_WITH_MONEY_SLASH_PLUS:{pattern:/^(\d{4}(?:[\s,，\-]+?\d{4})*)\/\s*(\d+)\+(\d+)$/,handler:function(e,t){var r=e[1].split(/[\s,，\-]+/).filter(Boolean),d=e[2],a=e[3];return[{methodId:n("f9d6").METHOD_ID.SIMA_ZULIU,betNumbers:r.join(","),money:d},{methodId:n("f9d6").METHOD_ID.SIMA_ZUSAN,betNumbers:r.join(","),money:a}]}},SIMA_MULTI_LINE_MONEY_PLUS:{pattern:/^((?:\d{4}[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+\d+\+\d+\s*\n?)+)$/m,handler:function(e,t){var r,o=n("f9d6"),I=o.METHOD_ID,u=(o.SUPER_SEPARATORS,t.split(/\n/).map((function(e){return e.trim()})).filter(Boolean)),l=new Map,_=!0,s=Object(a["a"])(u);try{for(s.s();!(r=s.n()).done;){var i=r.value,f=i.match(/^(\d{4})[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+(\d+)\+(\d+)$/);if(!f){_=!1;break}var U=f[1];if(4!==U.length){_=!1;break}var m=i.split(/[\-—~～@#￥%…&!、\\/*,，.＝=·\s+吊赶打各一－。:：]+/)[0];if(4!==m.length||!/^\d{4}$/.test(m)){_=!1;break}var h="".concat(f[2],"+").concat(f[3]);l.has(h)||l.set(h,[]),l.get(h).push(U)}}catch(y){s.e(y)}finally{s.f()}if(!_||0===l.size)return null;var M,S=[],c=Object(a["a"])(l.entries());try{for(c.s();!(M=c.n()).done;){var A=Object(d["a"])(M.value,2),p=A[0],b=A[1],E=p.split("+"),N=Object(d["a"])(E,2),O=N[0],L=N[1];S.push({methodId:I.SIMA_ZULIU,betNumbers:b.join(","),money:O}),S.push({methodId:I.SIMA_ZUSAN,betNumbers:b.join(","),money:L})}}catch(y){c.e(y)}finally{c.f()}return S.length?S:null}},SIMA_MULTI_LINE_MONEY_PLUS_CN:{pattern:/^((?:\d{4}[，,、]?[一二三四五六七八九十百千万\d]+加[一二三四五六七八九十百千万\d]+\s*\n?)+)$/m,handler:function(e,t){var r,o=n("f9d6"),I=o.METHOD_ID,u=o.chineseToNumber,l=t.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),_=new Map,s=Object(a["a"])(l);try{for(s.s();!(r=s.n()).done;){var i=r.value,f=i.match(/^(\d{4})[，,、]?([一二三四五六七八九十百千万\d]+)加([一二三四五六七八九十百千万\d]+)$/);if(f){var U=f[1],m=u(f[2]),h=u(f[3]),M="".concat(m,"+").concat(h);_.has(M)||_.set(M,[]),_.get(M).push(U)}}}catch(D){s.e(D)}finally{s.f()}var S,c=[],A=Object(a["a"])(_.entries());try{for(A.s();!(S=A.n()).done;){var p=Object(d["a"])(S.value,2),b=p[0],E=p[1],N=b.split("+"),O=Object(d["a"])(N,2),L=O[0],y=O[1];c.push({methodId:I.SIMA_ZULIU,betNumbers:E.join(","),money:L}),c.push({methodId:I.SIMA_ZUSAN,betNumbers:E.join(","),money:y})}}catch(D){A.e(D)}finally{A.f()}return c.length?c:null}},SIMA_MULTI_EACH_FANGDUI_SLASH:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、＝=·吊赶打各]+)[/\-~～@#￥%…&!、\\/*,，.＝=·吊赶打各]+各防对([一二三四五六七八九十百千万\d]+)$/,handler:function(e,t){var r=n("f9d6"),d=r.SUPER_SEPARATORS,a=r.METHOD_ID,o=r.chineseToNumber,I=e[1].split(d).map((function(e){return e.trim()})).filter((function(e){return 4===e.length})),u=o(e[2]);return I.length?[{methodId:a.SIMA_ZUSAN,betNumbers:I.join(","),money:u}]:null}},SIMA_MULTI_EACH_FANGDUI:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、＝=·吊赶打各]+)各防对([一二三四五六七八九十百千万\d]+)$/,handler:function(e,t){var r=n("f9d6"),d=r.SUPER_SEPARATORS,a=r.METHOD_ID,o=r.chineseToNumber,I=e[1].split(d).map((function(e){return e.trim()})).filter((function(e){return 4===e.length})),u=o(e[2]);return I.length?[{methodId:a.SIMA_ZUSAN,betNumbers:I.join(","),money:u}]:null}},SIMA_SLASH_MONEY:{pattern:/^\s*(\d{4})\s*\/\s*([一二三四五六七八九十百千万\d]+)\s*$/,handler:function(e,t){var r=n("f9d6"),d=r.METHOD_ID,a=r.chineseToNumber,o=e[1],I=a(e[2]);return[{methodId:d.SIMA_ZULIU,betNumbers:o,money:I}]}},SIMA_ZULIU_ZUSAN_MONEY_PAIR:{pattern:/^(\d{4}[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*)组六[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*(\d+)[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*组三[\-—~～@#￥%…&!、\\/*,一－–。，.＝=·\s]*(\d+)$/,handler:function(e,t){var n=e[1],r=e[2],d=e[3];return[{methodId:o["METHOD_ID"].SIMA_ZULIU,betNumbers:n,money:r},{methodId:o["METHOD_ID"].SIMA_ZUSAN,betNumbers:n,money:d}]}},ZULIU_MULTI_DOT_EACH_SUPER:{pattern:/^(\d{4}(?:[。.．、,，\s]+\d{4})*)各(\d+)[，,。.．、\s]*$/,handler:function(e,t){var r=n("f9d6"),d=r.METHOD_ID,a=e[1].split(/[。.．、,，\s]+/).map((function(e){return e.trim()})).filter(Boolean),o=e[2];if(!a.length)return null;var I=a[0].length;if(4!==I)return null;if(!a.every((function(e){return e.length===I})))return null;var u={4:d.SIMA_ZULIU,5:d.WUMA_ZULIU,6:d.LIUMA_ZULIU,7:d.QIMA_ZULIU,8:d.BAMA_ZULIU,9:d.JIUMA_ZULIU},l=u[I];return l?[{methodId:l,betNumbers:a.join(","),money:o}]:null}},SIMA_ZULIU_ZUSAN_COMBO:{pattern:/^(\d{4}(?:[。.．、,，\s]+\d{4})*)\/(\d+)\+(\d+)$/,handler:function(e,t){var r=n("f9d6"),d=(r.SUPER_SEPARATORS,e[1].split(/[。.．、,，\s]+/).map((function(e){return e.trim()})).filter(Boolean)),a=e[2],o=e[3];return d.length&&d.every((function(e){return 4===e.length}))?[{methodId:n("f9d6").METHOD_ID.SIMA_ZULIU,betNumbers:d.join(","),money:a},{methodId:n("f9d6").METHOD_ID.SIMA_ZUSAN,betNumbers:d.join(","),money:o}]:null}},SIMA_SINGLE_LINE_ZULIU_ZUSAN_PLUS:{pattern:/^(\d{4})\s+(\d+)[+＋](\d+)$/,handler:function(e,t){return[{methodId:n("f9d6").METHOD_ID.SIMA_ZULIU,betNumbers:e[1],money:e[2]},{methodId:n("f9d6").METHOD_ID.SIMA_ZUSAN,betNumbers:e[1],money:e[3]}]}},SIMA_MULTI_LINE_ZULIU_PLUS:{pattern:/^((?:\d{4}[+＋\\-—~～@#￥%…&!、\\/*,，.＝=·吊赶打各]\d+\s*\n?)+)$/m,handler:function(e,t){var r,o=n("f9d6"),I=o.METHOD_ID,u=t.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),l=new Map,_=[],s=Object(a["a"])(u);try{for(s.s();!(r=s.n()).done;){var i=r.value,f=i.match(/^(\d{4})[+＋\\-—~～@#￥%…&!、\\/*,，.＝=·吊赶打各](\d+)$/);if(f){var U=f[1],m=f[2];l.has(m)||l.set(m,[]),l.get(m).push(U)}else _.push(i)}}catch(b){s.e(b)}finally{s.f()}var h,M=[],S=Object(a["a"])(l.entries());try{for(S.s();!(h=S.n()).done;){var c=Object(d["a"])(h.value,2),A=c[0],p=c[1];M.push({methodId:I.SIMA_ZULIU,betNumbers:p.join(","),money:A})}}catch(b){S.e(b)}finally{S.f()}return M}},SIMA_MULTI_LINE_ZULIU_FANG_ZUSAN_GROUP:{pattern:/^([\s\S]+)$/m,handler:function(e,t){var r=performance.now(),o=n("f9d6"),I=o.SUPER_SEPARATORS,u=t.split(I).map((function(e){return e.trim()})).filter(Boolean);if(!u.length||4!==u[0].length){var l=performance.now();return console.log("[耗时统计] SIMA_MULTI_LINE_ZULIU_FANG_ZUSAN_GROUP handler 首位数长度不匹配, 耗时:",(l-r).toFixed(3),"ms, 首位数长度:",u.length?u[0].length:0),null}var _,s=t.split(/[\r\n]+/).map((function(e){return e.trim()})).filter(Boolean),i=new Map,f=Object(a["a"])(s);try{for(f.s();!(_=f.n()).done;){var U=_.value,m=U.match(/^(\d{4})-?(\d+)防(\d+)$/);if(m){var h=m[1],M=m[2],S=m[3],c="".concat(M,"|").concat(S);i.has(c)||i.set(c,[]),i.get(c).push(h)}}}catch(v){f.e(v)}finally{f.f()}var A,p=[],b=Object(a["a"])(i.entries());try{for(b.s();!(A=b.n()).done;){var E=Object(d["a"])(A.value,2),N=E[0],O=E[1],L=N.split("|"),y=Object(d["a"])(L,2),D=y[0],T=y[1];p.push({methodId:n("f9d6").METHOD_ID.SIMA_ZULIU,betNumbers:O.join(","),money:D}),p.push({methodId:n("f9d6").METHOD_ID.SIMA_ZUSAN,betNumbers:O.join(","),money:T})}}catch(v){b.e(v)}finally{b.f()}return p.length?p:null}},SIMA_MULTI_LINE_TRANSFORM:{pattern:/^((?:(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?\d{4}转\d+(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?\s*\n?)+)$/m,handler:function(e,t){var o,I=n("f9d6"),u=I.generateTransformNumbers,l=I.METHOD_ID,_=t.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),s=new Map,i=Object(a["a"])(_);try{for(i.s();!(o=i.n()).done;){var f=o.value,U=f.match(/^(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?(\d{4})转(\d+)(福排|排福|福体|福彩体|体彩福|排列三|排三|排列3|排3|福3D|福彩|福家|3d|3D|福|体J|体彩|体|排)?$/);if(U){var m,h=U[2],M=U[3];s.has(M)||s.set(M,[]),(m=s.get(M)).push.apply(m,Object(r["a"])(u(h)))}}}catch(N){i.e(N)}finally{i.f()}var S,c=[],A=Object(a["a"])(s.entries());try{for(A.s();!(S=A.n()).done;){var p=Object(d["a"])(S.value,2),b=p[0],E=p[1];c.push({methodId:l.WUMA_ZUSAN,betNumbers:E.join(","),money:b})}}catch(N){A.e(N)}finally{A.f()}return c.length?c:null}},SIMA_MULTI_COLON_EACH_SUPER:{pattern:/^(\d{4}(?::|：\d{4})*)各(\d+)$/,handler:function(e,t){var r=t.split(/:|：/).map((function(e){return e.trim()})).filter((function(e){return 4===e.length})),d=e[2];return r.length?[{methodId:n("f9d6").METHOD_ID.SIMA_ZULIU,betNumbers:r.join(","),money:d}]:null}},SIMA_MULTI_YIGE_EACH_PLUS:{pattern:/^([\d一]+)一([\d一]+)一?各?(\d+)\+(\d+)$/,handler:function(e,t){var r=t.split("一").map((function(e){return e.replace(/[^\d]/g,"")})).filter((function(e){return 4===e.length})),d=e[3],a=e[4];return r.length?[{methodId:n("f9d6").METHOD_ID.SIMA_ZULIU,betNumbers:r.join(","),money:d},{methodId:n("f9d6").METHOD_ID.SIMA_ZUSAN,betNumbers:r.join(","),money:a}]:null}},SIMA_DIAN_XMA_ZULIU_FANG_ZUSAN:{pattern:/^(\d{4})[。.．、,，\s]+4码(\d+)防(\d+)$/,handler:function(e,t){return[{methodId:n("f9d6").METHOD_ID.SIMA_ZULIU,betNumbers:e[1],money:e[2]},{methodId:n("f9d6").METHOD_ID.SIMA_ZUSAN,betNumbers:e[1],money:e[3]}]}},SIMA_MULTI_LINE_ZULIU_MONEY_GROUP:{pattern:/^((?:\d{4}\s+\d+\s*\n?)+)$/m,handler:function(e,t){var r,o=n("f9d6"),I=o.METHOD_ID,u=t.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),l=new Map,_=Object(a["a"])(u);try{for(_.s();!(r=_.n()).done;){var s=r.value,i=s.match(/^(\d{4})\s+(\d+)$/);if(i){var f=i[1],U=i[2];l.has(U)||l.set(U,[]),l.get(U).push(f)}}}catch(p){_.e(p)}finally{_.f()}var m,h=[],M=Object(a["a"])(l.entries());try{for(M.s();!(m=M.n()).done;){var S=Object(d["a"])(m.value,2),c=S[0],A=S[1];h.push({methodId:I.SIMA_ZULIU,betNumbers:A.join(","),money:c})}}catch(p){M.e(p)}finally{M.f()}return h.length?h:null}},SIMA_MULTI_NUMBERS_EACH_FANG:{pattern:/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、＝=·吊赶打各]+)各(\d+)(防|防对|放对)(\d+)$/,handler:function(e,t){var r=n("f9d6"),d=r.SUPER_SEPARATORS,a=r.METHOD_ID,o=e[1].split(d).map((function(e){return e.trim()})).filter((function(e){return 4===e.length}));if(!o.length)return null;var I=e[2],u=e[4];return[{methodId:a.SIMA_ZULIU,betNumbers:o.join(","),money:I},{methodId:a.SIMA_ZUSAN,betNumbers:o.join(","),money:u}]}},SIMA_YI_YI_ONE_MONEY:{pattern:/^(\d{4})一(\d{4})一个(\d+)(元|块|米)?$/,handler:function(e,t){var r=e[1],d=e[2],a=e[3];return 4!==r.length||4!==d.length?null:[{methodId:n("f9d6").METHOD_ID.SIMA_ZULIU,betNumbers:r+","+d,money:a}]}},SIMA_GE_MONEY:{pattern:/^(\d{4})[，,、\s]*个(\d+)(元|块|米)?$/,handler:function(e,t){var r=e[1],d=e[2];return 4!==r.length?null:[{methodId:n("f9d6").METHOD_ID.SIMA_ZULIU,betNumbers:r,money:d}]}},MULTI_LINE_SIMA_MONEY_GROUP:{pattern:/^((?:\d{4}[\-—~～@#￥%…&!、\\/*,一－。.＝=·吊赶打各\s]+\d+\s*\n?)+)$/m,handler:function(e,t){var r,o=n("f9d6"),I=o.METHOD_ID,u=t.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),l=new Map,_=Object(a["a"])(u);try{for(_.s();!(r=_.n()).done;){var s=r.value,i=s.match(/^(\d{4})[\-—~～@#￥%…&!、\\/*,一－。.＝=·吊赶打各\s]+([0-9]+)$/);if(i){var f=i[1],U=i[2];l.has(U)||l.set(U,[]),l.get(U).push(f)}}}catch(p){_.e(p)}finally{_.f()}var m,h=[],M=Object(a["a"])(l.entries());try{for(M.s();!(m=M.n()).done;){var S=Object(d["a"])(m.value,2),c=S[0],A=S[1];h.push({methodId:I.SIMA_ZULIU,betNumbers:A.join(","),money:c})}}catch(p){M.e(p)}finally{M.f()}return h.length?h:null}},SIMA_MULTI_LINE_ZULIU_MONEY_SUPER:{pattern:/^((?:\d{4}[\-—~～@#￥%…&!、一－。\\/*,，.＝=·\s]+\d+(元|米|块)?[，,./\\*\-\=。·、\s]*\n?)+)$/m,handler:function(e,t){var r,o=n("f9d6"),I=o.METHOD_ID,u=t.split(/\n/).map((function(e){return e.trim()})).filter(Boolean),l=new Map,_=/^(\d{4})[\-—~～@#￥%…&!、一－。\\/*,，.＝=·\s]+(\d+)(元|米|块)?[，,./\\*\-\=。·、\s]*$/,s=[],i=Object(a["a"])(u);try{for(i.s();!(r=i.n()).done;){var f=r.value,U=f.match(_);if(!U)return console.log('【SIMA_MULTI_LINE_ZULIU_MONEY_SUPER】行 "'.concat(f,'" 不匹配四码格式，返回null')),null;s.push({line:f,match:U})}}catch(L){i.e(L)}finally{i.f()}console.log("【SIMA_MULTI_LINE_ZULIU_MONEY_SUPER】所有 ".concat(s.length," 行都是四码格式，继续处理"));for(var m=0,h=s;m<h.length;m++){var M=h[m].match,S=M[1],c=M[2];l.has(c)||l.set(c,[]),l.get(c).push(S)}var A,p=[],b=Object(a["a"])(l.entries());try{for(b.s();!(A=b.n()).done;){var E=Object(d["a"])(A.value,2),N=E[0],O=E[1];p.push({methodId:I.SIMA_ZULIU,betNumbers:O.join(","),money:N})}}catch(L){b.e(L)}finally{b.f()}return console.log("【SIMA_MULTI_LINE_ZULIU_MONEY_SUPER】处理完成，返回 ".concat(p.length," 个结果组")),p.length?p:null}},SIMA_MULTI_FANGDUI_SUPER:{pattern:/^([0-9]{4}(?:[\-—–―－﹣﹘⸺⸻~～@#￥%…&!、\\/*,，.一－。＝=·\s+吊赶打各]+[0-9]{4})+)[\-—–―－﹣﹘⸺⸻~～@#￥%…&!、\\/*,，.一－。＝=·\s+吊赶打各]*(防对|防|组三)([0-9]+)$/,handler:function(e,t){var r=n("f9d6"),d=r.SUPER_SEPARATORS,a=r.METHOD_ID,o=e[1].split(d).map((function(e){return e.trim()})).filter((function(e){return 4===e.length})),I=e[3];return o.length<2?null:[{methodId:a.SIMA_ZUSAN,betNumbers:o.join(","),money:I}]}},SIMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER:{pattern:/^([\s\S]+)$/m,handler:function(e,t){var r=/(福彩|福|3D|3d)/.test(t),o=/(体彩|体|排|排三)/.test(t);if(r||o)return null;var I,u=t.split(/\r?\n/).map((function(e){return e.trim()})).filter(Boolean),l=new Map,_=new Map,s=/^([0-9]{4})[\-—~～@#￥%…&!、\\/*,一－–.。＝=·\s\/]+(\d+)\++(\d+)[元米块]*$/,i=/^([0-9]{4})[\-—~～@#￥%…&!、\\/*,一－–.。＝=·\s\/]+(\d+)[元米块]*$/,f=[],U=Object(a["a"])(u);try{for(U.s();!(I=U.n()).done;){var m=I.value,h=m.match(s);if(h)f.push({line:m,type:"zuliuZusan",match:h});else{if(h=m.match(i),!h)return console.log('【SIMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】行 "'.concat(m,'" 不匹配四码格式，返回null')),null;f.push({line:m,type:"zuliu",match:h})}}}catch(B){U.e(B)}finally{U.f()}console.log("【SIMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】所有 ".concat(f.length," 行都是四码格式，继续处理"));for(var M=0,S=f;M<S.length;M++){var c=S[M],A=c.match,p=c.type;if("zuliuZusan"===p){var b=A[1],E=A[2],N=A[3];l.has(E)||l.set(E,[]),l.get(E).push(b),_.has(N)||_.set(N,[]),_.get(N).push(b)}else if("zuliu"===p){var O=A[1],L=A[2];l.has(L)||l.set(L,[]),l.get(L).push(O)}}var y,D=[],T=Object(a["a"])(l.entries());try{for(T.s();!(y=T.n()).done;){var v=Object(d["a"])(y.value,2),Z=v[0],j=v[1];D.push({methodId:n("f9d6").METHOD_ID.SIMA_ZULIU,betNumbers:j.join(","),money:Z})}}catch(B){T.e(B)}finally{T.f()}var R,g=Object(a["a"])(_.entries());try{for(g.s();!(R=g.n()).done;){var H=Object(d["a"])(R.value,2),P=H[0],$=H[1];D.push({methodId:n("f9d6").METHOD_ID.SIMA_ZUSAN,betNumbers:$.join(","),money:P})}}catch(B){g.e(B)}finally{g.f()}return console.log("【SIMA_MULTI_LINE_ZULIU_ZUSAN_PLUS_SUPER】处理完成，返回 ".concat(D.length," 个结果组")),D.length?D:null}},SIMA_MULTI_LINE_ZULIU_ZUSAN_KEYWORD:{pattern:/^([0-9]{4}(组六|组三)\d+(元|米|块)?[，,./\*\-\=。一－。·、\s]*\n?)+$/m,handler:function(e,t){var n,r=t.split(/\r?\n/).map((function(e){return e.trim()})).filter(Boolean),I=new Map,u=Object(a["a"])(r);try{for(u.s();!(n=u.n()).done;){var l=n.value,_=l.match(/^([0-9]{4})(组六|组三)(\d+)(元|米|块)?[，,./\*\-\=。一－。·、\s]*$/);if(_&&4===_[1].length){var s=_[1],i=_[2],f=_[3],U=i+"_"+f;I.has(U)||I.set(U,[]),I.get(U).push(s)}}}catch(O){u.e(O)}finally{u.f()}var m,h=[],M=Object(a["a"])(I.entries());try{for(M.s();!(m=M.n()).done;){var S=Object(d["a"])(m.value,2),c=S[0],A=S[1],p=c.split("_"),b=Object(d["a"])(p,2),E=b[0],N=b[1];h.push({methodId:"组六"===E?o["METHOD_ID"].SIMA_ZULIU:o["METHOD_ID"].SIMA_ZUSAN,betNumbers:A.join(","),money:N})}}catch(O){M.e(O)}finally{M.f()}return h.length?h:null}}};Object.keys(I).forEach((function(e){var t=I[e].pattern;"string"===typeof t&&t.endsWith("$")?I[e].pattern=t.replace(/\$$/,"[，,./*-=。·、s]*$"):t instanceof RegExp&&t.source.endsWith("$")&&(I[e].pattern=new RegExp(t.source.replace(/\$$/,"[，,./*-=。·、s]*$")))}))}}]);