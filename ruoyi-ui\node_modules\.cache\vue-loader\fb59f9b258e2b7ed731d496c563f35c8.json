{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetStatistics.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetStatistics.vue", "mtime": 1756000107447}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750942928175}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750942928768}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCByZXF1ZXN0IGZyb20gJ0AvdXRpbHMvcmVxdWVzdCcKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnQmV0U3RhdGlzdGljcycsCiAgcHJvcHM6IHsKICAgIHZpc2libGU6IHsKICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgZGVmYXVsdDogZmFsc2UKICAgIH0KICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgbWV0aG9kUmFua2luZ0J5R3JvdXA6IFtdLCAvLyDmjInnjqnms5XliIbnu4TnmoTmjpLooYzmppwKICAgICAgbnVtYmVyUmFua2luZ0J5R3JvdXA6IFtdLCAvLyDmjInnjqnms5XliIbnu4TnmoTlj7fnoIHmjpLooYzmppwKICAgICAgZ2FtZU1ldGhvZHNEYXRhOiBbXSwgLy8g546p5rOV5pWw5o2uCiAgICAgIC8vIOeuoeeQhuWRmOWKn+iDveebuOWFswogICAgICBzZWxlY3RlZFVzZXJJZDogbnVsbCwgLy8g6YCJ5Lit55qE55So5oi3SUQKICAgICAgdXNlckxpc3Q6IFtdLCAvLyDnlKjmiLfliJfooagKICAgICAgbG9hZGluZzogZmFsc2UsIC8vIOWKoOi9veeKtuaAgQogICAgICAvLyDlvannp43nm7jlhbMKICAgICAgYWN0aXZlTG90dGVyeVR5cGU6ICdmdWNhaScsIC8vIOW9k+WJjemAieS4reeahOW9qeenje+8mmZ1Y2FpKOemj+W9qTNEKSDmiJYgdGljYWko5L2T5b2pUDMpCiAgICAgIC8vIOWIhuW9qeenjeeahOe7n+iuoeaVsOaNrgogICAgICBmdWNhaU1ldGhvZFJhbmtpbmc6IFtdLCAvLyDnpo/lvanmipXms6jmjpLooYwKICAgICAgZnVjYWlOdW1iZXJSYW5raW5nOiBbXSwgLy8g56aP5b2p54Ot6Zeo5LiJ5L2N5pWw5o6S6KGMCiAgICAgIHRpY2FpTWV0aG9kUmFua2luZzogW10sIC8vIOS9k+W9qeaKleazqOaOkuihjAogICAgICB0aWNhaU51bWJlclJhbmtpbmc6IFtdIC8vIOS9k+W9qeeDremXqOS4ieS9jeaVsOaOkuihjAogICAgfQogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIC8qKiDmmK/lkKbmmK/nrqHnkIblkZggKi8KICAgIGlzQWRtaW4oKSB7CiAgICAgIGNvbnN0IHVzZXJJbmZvID0gdGhpcy4kc3RvcmUuZ2V0dGVycy51c2VySW5mbzsKICAgICAgY29uc3Qgcm9sZXMgPSB0aGlzLiRzdG9yZS5nZXR0ZXJzLnJvbGVzOwoKICAgICAgLy8g5qOA5p+l5piv5ZCm5piv6LaF57qn566h55CG5ZGY77yI55So5oi3SUTkuLox77yJCiAgICAgIGlmICh1c2VySW5mbyAmJiB1c2VySW5mby51c2VySWQgPT09IDEpIHsKICAgICAgICByZXR1cm4gdHJ1ZTsKICAgICAgfQoKICAgICAgLy8g5qOA5p+l5piv5ZCm5pyJ566h55CG5ZGY6KeS6ImyCiAgICAgIGlmIChyb2xlcyAmJiByb2xlcy5sZW5ndGggPiAwKSB7CiAgICAgICAgcmV0dXJuIHJvbGVzLmluY2x1ZGVzKCdhZG1pbicpIHx8IHJvbGVzLmluY2x1ZGVzKCczZF9hZG1pbicpOwogICAgICB9CgogICAgICByZXR1cm4gZmFsc2U7CiAgICB9LAoKICAgIC8qKiDlvZPliY3lvannp43nmoTmipXms6jmjpLooYzmlbDmja4gKi8KICAgIGN1cnJlbnRNZXRob2RSYW5raW5nKCkgewogICAgICByZXR1cm4gdGhpcy5hY3RpdmVMb3R0ZXJ5VHlwZSA9PT0gJ2Z1Y2FpJyA/IHRoaXMuZnVjYWlNZXRob2RSYW5raW5nIDogdGhpcy50aWNhaU1ldGhvZFJhbmtpbmc7CiAgICB9LAoKICAgIC8qKiDlvZPliY3lvannp43nmoTng63pl6jkuInkvY3mlbDmjpLooYzmlbDmja4gKi8KICAgIGN1cnJlbnROdW1iZXJSYW5raW5nKCkgewogICAgICByZXR1cm4gdGhpcy5hY3RpdmVMb3R0ZXJ5VHlwZSA9PT0gJ2Z1Y2FpJyA/IHRoaXMuZnVjYWlOdW1iZXJSYW5raW5nIDogdGhpcy50aWNhaU51bWJlclJhbmtpbmc7CiAgICB9CiAgfSwKICB3YXRjaDogewogICAgdmlzaWJsZSh2YWwpIHsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdmFsOwogICAgICBpZiAodmFsKSB7CiAgICAgICAgLy8g6L+Q6KGM5rWL6K+VCiAgICAgICAgdGhpcy50ZXN0VGhyZWVEaWdpdEV4dHJhY3Rpb24oKTsKCiAgICAgICAgLy8g5aaC5p6c5piv566h55CG5ZGY77yM5Y+q5Yqg6L2955So5oi35YiX6KGo5ZKM546p5rOV5pWw5o2u77yM5LiN6Ieq5Yqo6K6h566X57uf6K6hCiAgICAgICAgaWYgKHRoaXMuaXNBZG1pbikgewogICAgICAgICAgdGhpcy5sb2FkVXNlckxpc3QoKTsKICAgICAgICAgIHRoaXMubG9hZEdhbWVNZXRob2RzKCk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIC8vIOaZrumAmueUqOaIt+iHquWKqOWKoOi9vee7n+iuoQogICAgICAgICAgdGhpcy5sb2FkR2FtZU1ldGhvZHMoKS50aGVuKGFzeW5jICgpID0+IHsKICAgICAgICAgICAgLy8g5pmu6YCa55So5oi35LuOc2Vzc2lvblN0b3JhZ2Xojrflj5boh6rlt7HnmoTmlbDmja4KICAgICAgICAgICAgY29uc3Qgc3lzVXNlcklkID0gc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgnc3lzVXNlcklkJyk7CiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdbQmV0U3RhdGlzdGljc10gc2Vzc2lvblN0b3JhZ2XkuK3nmoRzeXNVc2VySWQ6Jywgc3lzVXNlcklkKTsKCiAgICAgICAgICAgIGlmIChzeXNVc2VySWQpIHsKICAgICAgICAgICAgICB0aGlzLnNlbGVjdGVkVXNlcklkID0gcGFyc2VJbnQoc3lzVXNlcklkKTsKICAgICAgICAgICAgICBjb25zb2xlLmxvZygnW0JldFN0YXRpc3RpY3NdIOiuvue9rnNlbGVjdGVkVXNlcklkOicsIHRoaXMuc2VsZWN0ZWRVc2VySWQpOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIGNvbnNvbGUud2FybignW0JldFN0YXRpc3RpY3NdIOaXoOazleS7jnNlc3Npb25TdG9yYWdl6I635Y+Wc3lzVXNlcklkJyk7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgYXdhaXQgdGhpcy5jYWxjdWxhdGVTdGF0aXN0aWNzKCk7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0KICAgIH0sCiAgICBkaWFsb2dWaXNpYmxlKHZhbCkgewogICAgICB0aGlzLiRlbWl0KCd1cGRhdGU6dmlzaWJsZScsIHZhbCk7CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5Yqg6L29546p5rOV5pWw5o2uICovCiAgICBhc3luYyBsb2FkR2FtZU1ldGhvZHMoKSB7CiAgICAgIHRyeSB7CiAgICAgICAgLy8g55u05o6l5LuOQVBJ6I635Y+W546p5rOV5pWw5o2uCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCByZXF1ZXN0KHsKICAgICAgICAgIHVybDogJy9nYW1lL29kZHMvbGlzdCcsCiAgICAgICAgICBtZXRob2Q6ICdnZXQnLAogICAgICAgICAgcGFyYW1zOiB7IHBhZ2VOdW06IDEsIHBhZ2VTaXplOiAxMDAwIH0KICAgICAgICB9KTsKCiAgICAgICAgaWYgKHJlc3BvbnNlICYmIHJlc3BvbnNlLnJvd3MpIHsKICAgICAgICAgIHRoaXMuZ2FtZU1ldGhvZHNEYXRhID0gcmVzcG9uc2Uucm93czsKICAgICAgICAgCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuZ2FtZU1ldGhvZHNEYXRhID0gW107CiAgICAgICAgICBjb25zb2xlLndhcm4oJ+eOqeazleaVsOaNruS4uuepuicpOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bnjqnms5XmlbDmja7lpLHotKU6JywgZXJyb3IpOwogICAgICAgIC8vIOWmguaenEFQSeWksei0pe+8jOS9v+eUqOWfuuacrOeahOeOqeazleaYoOWwhAogICAgICAgIHRoaXMuZ2FtZU1ldGhvZHNEYXRhID0gdGhpcy5nZXRCYXNpY01ldGhvZHNEYXRhKCk7CiAgICAgIH0KICAgIH0sCiAgICAvKiog6I635Y+W5Z+65pys546p5rOV5pWw5o2u77yIQVBJ5aSx6LSl5pe255qE5aSH55So5pa55qGI77yJICovCiAgICBnZXRCYXNpY01ldGhvZHNEYXRhKCkgewogICAgICByZXR1cm4gWwogICAgICAgIHsgbWV0aG9kSWQ6IDEsIG1ldGhvZE5hbWU6ICfni6zog4YnIH0sCiAgICAgICAgeyBtZXRob2RJZDogMiwgbWV0aG9kTmFtZTogJ+S4gOeggeWumuS9jScgfSwKICAgICAgICB7IG1ldGhvZElkOiAzLCBtZXRob2ROYW1lOiAn5Lik56CB57uE5ZCIJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDQsIG1ldGhvZE5hbWU6ICfkuKTnoIHlr7nlrZAnIH0sCiAgICAgICAgeyBtZXRob2RJZDogNSwgbWV0aG9kTmFtZTogJ+S4ieeggeebtOmAiScgfSwKICAgICAgICB7IG1ldGhvZElkOiA2LCBtZXRob2ROYW1lOiAn5LiJ56CB57uE6YCJJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDcsIG1ldGhvZE5hbWU6ICfkuInnoIHnu4TkuIknIH0sCiAgICAgICAgeyBtZXRob2RJZDogOCwgbWV0aG9kTmFtZTogJ+Wbm+eggee7hOWFrScgfSwKICAgICAgICB7IG1ldGhvZElkOiA5LCBtZXRob2ROYW1lOiAn5Zub56CB57uE5LiJJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDEwLCBtZXRob2ROYW1lOiAn5LqU56CB57uE5YWtJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDExLCBtZXRob2ROYW1lOiAn5LqU56CB57uE5LiJJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDEyLCBtZXRob2ROYW1lOiAn5YWt56CB57uE5YWtJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDEzLCBtZXRob2ROYW1lOiAn5YWt56CB57uE5LiJJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDE0LCBtZXRob2ROYW1lOiAn5LiD56CB57uE5YWtJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDE1LCBtZXRob2ROYW1lOiAn5LiD56CB57uE5LiJJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDE2LCBtZXRob2ROYW1lOiAn5YWr56CB57uE5YWtJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDE3LCBtZXRob2ROYW1lOiAn5YWr56CB57uE5LiJJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDE4LCBtZXRob2ROYW1lOiAn5Lmd56CB57uE5YWtJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDE5LCBtZXRob2ROYW1lOiAn5Lmd56CB57uE5LiJJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDIwLCBtZXRob2ROYW1lOiAn5YyF5omT57uE5YWtJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDIxLCBtZXRob2ROYW1lOiAnN+aIljIw5ZKM5YC8JyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDIyLCBtZXRob2ROYW1lOiAnOOaIljE55ZKM5YC8JyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDIzLCBtZXRob2ROYW1lOiAnOeaIljE45ZKM5YC8JyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDI0LCBtZXRob2ROYW1lOiAnMTDmiJYxN+WSjOWAvCcgfSwKICAgICAgICB7IG1ldGhvZElkOiAyNSwgbWV0aG9kTmFtZTogJzEx5oiWMTblkozlgLwnIH0sCiAgICAgICAgeyBtZXRob2RJZDogMjYsIG1ldGhvZE5hbWU6ICcxMuaIljE15ZKM5YC8JyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDI3LCBtZXRob2ROYW1lOiAnMTPmiJYxNOWSjOWAvCcgfSwKICAgICAgICB7IG1ldGhvZElkOiAyOCwgbWV0aG9kTmFtZTogJ+ebtOmAieWkjeW8jycgfSwKICAgICAgICB7IG1ldGhvZElkOiAyOSwgbWV0aG9kTmFtZTogJ+WSjOWAvOWNleWPjCcgfSwKICAgICAgICB7IG1ldGhvZElkOiAzMCwgbWV0aG9kTmFtZTogJ+WSjOWAvOWkp+WwjycgfSwKICAgICAgICB7IG1ldGhvZElkOiAzMSwgbWV0aG9kTmFtZTogJ+WMheaJk+e7hOS4iScgfSwKICAgICAgICB7IG1ldGhvZElkOiAxMDAsIG1ldGhvZE5hbWU6ICfkuInnoIHpmLLlr7knIH0KICAgICAgXTsKICAgIH0sCiAgICAvKiog6I635Y+W546p5rOV5ZCN56ewICovCiAgICBnZXRNZXRob2ROYW1lKG1ldGhvZElkKSB7CiAgICAgIGNvbnN0IG1ldGhvZCA9IHRoaXMuZ2FtZU1ldGhvZHNEYXRhLmZpbmQobSA9PiBtLm1ldGhvZElkID09PSBtZXRob2RJZCk7CiAgICAgIHJldHVybiBtZXRob2QgPyBtZXRob2QubWV0aG9kTmFtZSA6IGDnjqnms5Uke21ldGhvZElkfWA7CiAgICB9LAogICAgLyoqIOiOt+WPluaOkuWQjeagt+W8j+exuyAqLwogICAgZ2V0UmFua0NsYXNzKHJhbmspIHsKICAgICAgaWYgKHJhbmsgPT09IDEpIHJldHVybiAncmFuay1maXJzdCc7CiAgICAgIGlmIChyYW5rID09PSAyKSByZXR1cm4gJ3Jhbmstc2Vjb25kJzsKICAgICAgaWYgKHJhbmsgPT09IDMpIHJldHVybiAncmFuay10aGlyZCc7CiAgICAgIHJldHVybiAncmFuay1vdGhlcic7CiAgICB9LAogICAgLyoqIOiuoeeul+e7n+iuoeaVsOaNriAqLwogICAgYXN5bmMgY2FsY3VsYXRlU3RhdGlzdGljcygpIHsKICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHRoaXMuZ2V0UmVjb3JkRGF0YSgpOwoKCiAgICAgIGlmICghZGF0YSB8fCBkYXRhLmxlbmd0aCA9PT0gMCkgewogICAgICAgIGlmICh0aGlzLmlzQWRtaW4gJiYgIXRoaXMuc2VsZWN0ZWRVc2VySWQpIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36YCJ5oup6KaB5p+l55yL57uf6K6h55qE55So5oi3Jyk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn5pqC5peg57uf6K6h5pWw5o2u77yM6K+35YWI5Yi35pawcmVjb3Jk6aG16Z2iJyk7CiAgICAgICAgfQogICAgICAgIHRoaXMuY2xlYXJBbGxSYW5raW5nRGF0YSgpOwogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgLy8g5oyJ5b2p56eN5YiG5Yir6K6h566X57uf6K6h5pWw5o2uCiAgICAgIHRoaXMuY2FsY3VsYXRlU3RhdGlzdGljc0J5TG90dGVyeVR5cGUoZGF0YSk7CgogICAgICAvLyDosIPor5Xkv6Hmga8KICAgICAgY29uc29sZS5sb2coJ+e7n+iuoeiuoeeul+WujOaIkDonLCB7CiAgICAgICAg56aP5b2p5oqV5rOo5o6S6KGMOiB0aGlzLmZ1Y2FpTWV0aG9kUmFua2luZy5sZW5ndGgsCiAgICAgICAg56aP5b2p54Ot6Zeo5LiJ5L2N5pWwOiB0aGlzLmZ1Y2FpTnVtYmVyUmFua2luZy5sZW5ndGgsCiAgICAgICAg5L2T5b2p5oqV5rOo5o6S6KGMOiB0aGlzLnRpY2FpTWV0aG9kUmFua2luZy5sZW5ndGgsCiAgICAgICAg5L2T5b2p54Ot6Zeo5LiJ5L2N5pWwOiB0aGlzLnRpY2FpTnVtYmVyUmFua2luZy5sZW5ndGgsCiAgICAgICAg5b2T5YmN6YCJ5Lit5b2p56eNOiB0aGlzLmFjdGl2ZUxvdHRlcnlUeXBlCiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDojrflj5ZyZWNvcmTmlbDmja4gKi8KICAgIGFzeW5jIGdldFJlY29yZERhdGEoKSB7CiAgICAgIHRyeSB7CiAgICAgICAgLy8g5aaC5p6c5pyJ6YCJ5oup55qE55So5oi3SUTvvIjnrqHnkIblkZjpgInmi6nnmoTnlKjmiLfmiJbmma7pgJrnlKjmiLfoh6rlt7HvvInvvIznm7TmjqXku45BUEnojrflj5bmlbDmja4KICAgICAgICBpZiAodGhpcy5zZWxlY3RlZFVzZXJJZCkgewogICAgICAgICAgcmV0dXJuIGF3YWl0IHRoaXMuZmV0Y2hVc2VyUmVjb3JkRGF0YSh0aGlzLnNlbGVjdGVkVXNlcklkKTsKICAgICAgICB9CgogICAgICAgIC8vIOWmguaenOaYr+euoeeQhuWRmOS9huayoeaciemAieaLqeeUqOaIt++8jOaPkOekuumAieaLqeeUqOaItwogICAgICAgIGlmICh0aGlzLmlzQWRtaW4pIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36YCJ5oup6KaB5p+l55yL57uf6K6h55qE55So5oi3Jyk7CiAgICAgICAgICByZXR1cm4gW107CiAgICAgICAgfQoKICAgICAgICAvLyDmma7pgJrnlKjmiLfvvJrnm7TmjqXku45BUEnojrflj5blvZPliY3nlKjmiLfmlbDmja7vvIzku45zZXNzaW9uU3RvcmFnZeiOt+WPlueUqOaIt0lECiAgICAgICAgY29uc29sZS5sb2coJ1tCZXRTdGF0aXN0aWNzXSDmma7pgJrnlKjmiLfvvIznm7TmjqXku45BUEnojrflj5bmlbDmja4nKTsKCiAgICAgICAgLy8g5LuOc2Vzc2lvblN0b3JhZ2Xojrflj5bnlKjmiLdJRAogICAgICAgIGNvbnN0IHN5c1VzZXJJZCA9IHNlc3Npb25TdG9yYWdlLmdldEl0ZW0oJ3N5c1VzZXJJZCcpOwogICAgICAgIGNvbnNvbGUubG9nKCdbQmV0U3RhdGlzdGljc10g5LuOc2Vzc2lvblN0b3JhZ2Xojrflj5bnmoRzeXNVc2VySWQ6Jywgc3lzVXNlcklkKTsKCiAgICAgICAgaWYgKHN5c1VzZXJJZCkgewogICAgICAgICAgY29uc29sZS5sb2coJ1tCZXRTdGF0aXN0aWNzXSDkvb/nlKhzeXNVc2VySWTosIPnlKhBUEk6Jywgc3lzVXNlcklkKTsKICAgICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCB0aGlzLmZldGNoVXNlclJlY29yZERhdGEocGFyc2VJbnQoc3lzVXNlcklkKSk7CiAgICAgICAgICBjb25zb2xlLmxvZygnW0JldFN0YXRpc3RpY3NdIEFQSeiOt+WPluWIsOeahOaVsOaNrjonLCBkYXRhKTsKCiAgICAgICAgICBpZiAoZGF0YSAmJiBkYXRhLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgY29uc29sZS5sb2coYFtCZXRTdGF0aXN0aWNzXSDmiJDlip/liqDovb3kuoYgJHtkYXRhLmxlbmd0aH0g5p2h57uf6K6h5pWw5o2uYCk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBjb25zb2xlLndhcm4oJ1tCZXRTdGF0aXN0aWNzXSBBUEnov5Tlm57nqbrmlbDmja4nKTsKICAgICAgICAgIH0KCiAgICAgICAgICByZXR1cm4gZGF0YTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgY29uc29sZS53YXJuKCdbQmV0U3RhdGlzdGljc10g5peg5rOV5LuOc2Vzc2lvblN0b3JhZ2Xojrflj5ZzeXNVc2VySWQnKTsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn5peg5rOV6I635Y+W55So5oi35L+h5oGv77yM6K+36YeN5paw55m75b2VJyk7CiAgICAgICAgICByZXR1cm4gW107CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+ino+aekOe7n+iuoeaVsOaNruWksei0pTonLCBlcnJvcik7CiAgICAgICAgcmV0dXJuIFtdOwogICAgICB9CiAgICB9LAogICAgLyoqIOa4heepuuaJgOacieaOkuihjOaVsOaNriAqLwogICAgY2xlYXJBbGxSYW5raW5nRGF0YSgpIHsKICAgICAgdGhpcy5mdWNhaU1ldGhvZFJhbmtpbmcgPSBbXTsKICAgICAgdGhpcy5mdWNhaU51bWJlclJhbmtpbmcgPSBbXTsKICAgICAgdGhpcy50aWNhaU1ldGhvZFJhbmtpbmcgPSBbXTsKICAgICAgdGhpcy50aWNhaU51bWJlclJhbmtpbmcgPSBbXTsKICAgIH0sCgogICAgLyoqIOaMieW9qeenjeWIhuWIq+iuoeeul+e7n+iuoeaVsOaNriAqLwogICAgY2FsY3VsYXRlU3RhdGlzdGljc0J5TG90dGVyeVR5cGUoZGF0YSkgewogICAgICAvLyDosIPor5XvvJrmn6XnnIvmlbDmja7nu5PmnoQKICAgICAgaWYgKGRhdGEubGVuZ3RoID4gMCkgewogICAgICAgIGNvbnNvbGUubG9nKCfmlbDmja7moLfmnKw6JywgZGF0YVswXSk7CiAgICAgICAgY29uc29sZS5sb2coJ+WPr+iDveeahOW9qeenjeWtl+autTonLCB7CiAgICAgICAgICBsb3R0ZXJ5VHlwZTogZGF0YVswXS5sb3R0ZXJ5VHlwZSwKICAgICAgICAgIGdhbWVUeXBlOiBkYXRhWzBdLmdhbWVUeXBlLAogICAgICAgICAgbG90dGVyeUlkOiBkYXRhWzBdLmxvdHRlcnlJZCwKICAgICAgICAgIGdhbWVOYW1lOiBkYXRhWzBdLmdhbWVOYW1lLAogICAgICAgICAgbWV0aG9kTmFtZTogZGF0YVswXS5tZXRob2ROYW1lCiAgICAgICAgfSk7CgogICAgICAgIC8vIOe7n+iuoeaJgOacieWPr+iDveeahOW9qeenjeagh+ivhgogICAgICAgIGNvbnN0IGxvdHRlcnlUeXBlcyA9IFsuLi5uZXcgU2V0KGRhdGEubWFwKHJlY29yZCA9PiByZWNvcmQubG90dGVyeVR5cGUpKV07CiAgICAgICAgY29uc3QgZ2FtZVR5cGVzID0gWy4uLm5ldyBTZXQoZGF0YS5tYXAocmVjb3JkID0+IHJlY29yZC5nYW1lVHlwZSkpXTsKICAgICAgICBjb25zdCBsb3R0ZXJ5SWRzID0gWy4uLm5ldyBTZXQoZGF0YS5tYXAocmVjb3JkID0+IHJlY29yZC5sb3R0ZXJ5SWQpKV07CiAgICAgICAgY29uc3QgZ2FtZU5hbWVzID0gWy4uLm5ldyBTZXQoZGF0YS5tYXAocmVjb3JkID0+IHJlY29yZC5nYW1lTmFtZSkpXTsKCiAgICAgICAgY29uc29sZS5sb2coJ+aVsOaNruS4reeahOW9qeenjeagh+ivhjonLCB7CiAgICAgICAgICBsb3R0ZXJ5VHlwZXMsCiAgICAgICAgICBnYW1lVHlwZXMsCiAgICAgICAgICBsb3R0ZXJ5SWRzLAogICAgICAgICAgZ2FtZU5hbWVzCiAgICAgICAgfSk7CiAgICAgIH0KCiAgICAgIC8vIOaMieW9qeenjeWIhue7hOaVsOaNru+8iOS9v+eUqGxvdHRlcnlJZOWtl+aute+8iQogICAgICBjb25zdCBmdWNhaURhdGEgPSBkYXRhLmZpbHRlcihyZWNvcmQgPT4gdGhpcy5pc0Z1Y2FpTG90dGVyeShyZWNvcmQubG90dGVyeUlkKSk7CiAgICAgIGNvbnN0IHRpY2FpRGF0YSA9IGRhdGEuZmlsdGVyKHJlY29yZCA9PiB0aGlzLmlzVGljYWlMb3R0ZXJ5KHJlY29yZC5sb3R0ZXJ5SWQpKTsKCiAgICAgIGNvbnNvbGUubG9nKGDnpo/lvakzROaVsOaNrjogJHtmdWNhaURhdGEubGVuZ3RofeadoSwg5L2T5b2pUDPmlbDmja46ICR7dGljYWlEYXRhLmxlbmd0aH3mnaFgKTsKCiAgICAgIC8vIOWmguaenOayoeacieaYjuehrueahOW9qeenjeWMuuWIhu+8jOWwhuaJgOacieaVsOaNruW9kuexu+S4uuemj+W9qTNE77yI5YW85a655oCn5aSE55CG77yJCiAgICAgIGlmIChmdWNhaURhdGEubGVuZ3RoID09PSAwICYmIHRpY2FpRGF0YS5sZW5ndGggPT09IDAgJiYgZGF0YS5sZW5ndGggPiAwKSB7CiAgICAgICAgY29uc29sZS5sb2coJ+acquajgOa1i+WIsOW9qeenjeWtl+aute+8jOWwhuaJgOacieaVsOaNruW9kuexu+S4uuemj+W9qTNEJyk7CiAgICAgICAgdGhpcy5mdWNhaU1ldGhvZFJhbmtpbmcgPSB0aGlzLmNhbGN1bGF0ZU1ldGhvZFJhbmtpbmdCeUdyb3VwKGRhdGEpOwogICAgICAgIHRoaXMuZnVjYWlOdW1iZXJSYW5raW5nID0gdGhpcy5jYWxjdWxhdGVOdW1iZXJSYW5raW5nKGRhdGEpOwogICAgICAgIHRoaXMudGljYWlNZXRob2RSYW5raW5nID0gW107CiAgICAgICAgdGhpcy50aWNhaU51bWJlclJhbmtpbmcgPSBbXTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIC8vIOWIhuWIq+iuoeeul+emj+W9qeWSjOS9k+W9qeeahOe7n+iuoeaVsOaNrgogICAgICBpZiAoZnVjYWlEYXRhLmxlbmd0aCA+IDApIHsKICAgICAgICB0aGlzLmZ1Y2FpTWV0aG9kUmFua2luZyA9IHRoaXMuY2FsY3VsYXRlTWV0aG9kUmFua2luZ0J5R3JvdXAoZnVjYWlEYXRhKTsKICAgICAgICB0aGlzLmZ1Y2FpTnVtYmVyUmFua2luZyA9IHRoaXMuY2FsY3VsYXRlTnVtYmVyUmFua2luZyhmdWNhaURhdGEpOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuZnVjYWlNZXRob2RSYW5raW5nID0gW107CiAgICAgICAgdGhpcy5mdWNhaU51bWJlclJhbmtpbmcgPSBbXTsKICAgICAgfQoKICAgICAgaWYgKHRpY2FpRGF0YS5sZW5ndGggPiAwKSB7CiAgICAgICAgdGhpcy50aWNhaU1ldGhvZFJhbmtpbmcgPSB0aGlzLmNhbGN1bGF0ZU1ldGhvZFJhbmtpbmdCeUdyb3VwKHRpY2FpRGF0YSk7CiAgICAgICAgdGhpcy50aWNhaU51bWJlclJhbmtpbmcgPSB0aGlzLmNhbGN1bGF0ZU51bWJlclJhbmtpbmcodGljYWlEYXRhKTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnRpY2FpTWV0aG9kUmFua2luZyA9IFtdOwogICAgICAgIHRoaXMudGljYWlOdW1iZXJSYW5raW5nID0gW107CiAgICAgIH0KICAgIH0sCgogICAgLyoqIOWIpOaWreaYr+WQpuS4uuemj+W9qTNEICovCiAgICBpc0Z1Y2FpTG90dGVyeShsb3R0ZXJ5SWQpIHsKICAgICAgLy8g56aP5b2pM0TnmoRsb3R0ZXJ5SWTmmK8xCiAgICAgIHJldHVybiBsb3R0ZXJ5SWQgPT09IDEgfHwgbG90dGVyeUlkID09PSAnMSc7CiAgICB9LAoKICAgIC8qKiDliKTmlq3mmK/lkKbkuLrkvZPlvalQMyAqLwogICAgaXNUaWNhaUxvdHRlcnkobG90dGVyeUlkKSB7CiAgICAgIC8vIOS9k+W9qVAz55qEbG90dGVyeUlk5pivMgogICAgICByZXR1cm4gbG90dGVyeUlkID09PSAyIHx8IGxvdHRlcnlJZCA9PT0gJzInOwogICAgfSwKCiAgICAvKiog5oyJ546p5rOV5YiG57uE6K6h566X5o6S6KGM5qacICovCiAgICBjYWxjdWxhdGVNZXRob2RSYW5raW5nQnlHcm91cChkYXRhKSB7CiAgICAgIGNvbnNvbGUubG9nKCdbY2FsY3VsYXRlTWV0aG9kUmFua2luZ0J5R3JvdXBdIOW8gOWni+iuoeeul+WQhOeOqeazleaKleazqOaOkuihjOamnCcpOwoKICAgICAgLy8g5oyJ546p5rOV5YiG57uE77yM55u45ZCM5Y+356CB5ZCI5bm257Sv6K6h6YeR6aKdCiAgICAgIGNvbnN0IG1ldGhvZEdyb3VwcyA9IHt9OwoKICAgICAgZGF0YS5mb3JFYWNoKHJlY29yZCA9PiB7CiAgICAgICAgY29uc3QgbWV0aG9kSWQgPSByZWNvcmQubWV0aG9kSWQ7CiAgICAgICAgY29uc3QgYW1vdW50ID0gcGFyc2VGbG9hdChyZWNvcmQubW9uZXkpIHx8IDA7CgogICAgICAgIGlmICghbWV0aG9kR3JvdXBzW21ldGhvZElkXSkgewogICAgICAgICAgbWV0aG9kR3JvdXBzW21ldGhvZElkXSA9IG5ldyBNYXAoKTsgLy8g5L2/55SoTWFw5p2l5a2Y5YKo5Y+356CB5ZKM5a+55bqU55qE57Sv6K6h5pWw5o2uCiAgICAgICAgfQoKICAgICAgICAvLyDop6PmnpDmipXms6jlj7fnoIEKICAgICAgICB0cnkgewogICAgICAgICAgY29uc3QgYmV0TnVtYmVycyA9IEpTT04ucGFyc2UocmVjb3JkLmJldE51bWJlcnMgfHwgJ3t9Jyk7CiAgICAgICAgICBjb25zdCBudW1iZXJzID0gYmV0TnVtYmVycy5udW1iZXJzIHx8IFtdOwoKICAgICAgICAgIC8vIOS4uuavj+S4quWPt+eggee0r+iuoemHkeminQogICAgICAgICAgbnVtYmVycy5mb3JFYWNoKChudW1iZXJPYmopID0+IHsKICAgICAgICAgICAgbGV0IHNpbmdsZU51bWJlckRpc3BsYXkgPSAnJzsKCiAgICAgICAgICAgIGlmICh0eXBlb2YgbnVtYmVyT2JqID09PSAnb2JqZWN0JyAmJiBudW1iZXJPYmogIT09IG51bGwpIHsKICAgICAgICAgICAgICAvLyDlpITnkIYge2E6MSwgYjoyLCBjOjMsIGQ6NCwgZTo1fSDmoLzlvI8KICAgICAgICAgICAgICBjb25zdCBrZXlzID0gT2JqZWN0LmtleXMobnVtYmVyT2JqKS5zb3J0KCk7CiAgICAgICAgICAgICAgY29uc3QgdmFsdWVzID0ga2V5cy5tYXAoa2V5ID0+IFN0cmluZyhudW1iZXJPYmpba2V5XSkpOwogICAgICAgICAgICAgIHNpbmdsZU51bWJlckRpc3BsYXkgPSB2YWx1ZXMuam9pbignJyk7CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgc2luZ2xlTnVtYmVyRGlzcGxheSA9IFN0cmluZyhudW1iZXJPYmopOwogICAgICAgICAgICB9CgogICAgICAgICAgICAvLyDlpoLmnpzlj7fnoIHlt7LlrZjlnKjvvIzntK/orqHph5Hpop3lkozorrDlvZXmlbAKICAgICAgICAgICAgaWYgKG1ldGhvZEdyb3Vwc1ttZXRob2RJZF0uaGFzKHNpbmdsZU51bWJlckRpc3BsYXkpKSB7CiAgICAgICAgICAgICAgY29uc3QgZXhpc3RpbmcgPSBtZXRob2RHcm91cHNbbWV0aG9kSWRdLmdldChzaW5nbGVOdW1iZXJEaXNwbGF5KTsKICAgICAgICAgICAgICBleGlzdGluZy50b3RhbEFtb3VudCArPSBhbW91bnQ7CiAgICAgICAgICAgICAgZXhpc3RpbmcucmVjb3JkQ291bnQgKz0gMTsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAvLyDmlrDlj7fnoIHvvIzliJvlu7rorrDlvZUKICAgICAgICAgICAgICBtZXRob2RHcm91cHNbbWV0aG9kSWRdLnNldChzaW5nbGVOdW1iZXJEaXNwbGF5LCB7CiAgICAgICAgICAgICAgICBiZXROdW1iZXJzOiBzaW5nbGVOdW1iZXJEaXNwbGF5LAogICAgICAgICAgICAgICAgdG90YWxBbW91bnQ6IGFtb3VudCwKICAgICAgICAgICAgICAgIHJlY29yZENvdW50OiAxCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgICBjb25zb2xlLndhcm4oJ+ino+aekOaKleazqOWPt+eggeWksei0pTonLCByZWNvcmQuYmV0TnVtYmVycywgZXJyb3IpOwogICAgICAgICAgLy8g5aaC5p6c6Kej5p6Q5aSx6LSl77yM5L2/55So5Y6f5aeL5pWw5o2uCiAgICAgICAgICBjb25zdCBmYWxsYmFja051bWJlcnMgPSByZWNvcmQuYmV0TnVtYmVycyB8fCAn5pyq55+l5Y+356CBJzsKCiAgICAgICAgICBpZiAobWV0aG9kR3JvdXBzW21ldGhvZElkXS5oYXMoZmFsbGJhY2tOdW1iZXJzKSkgewogICAgICAgICAgICBjb25zdCBleGlzdGluZyA9IG1ldGhvZEdyb3Vwc1ttZXRob2RJZF0uZ2V0KGZhbGxiYWNrTnVtYmVycyk7CiAgICAgICAgICAgIGV4aXN0aW5nLnRvdGFsQW1vdW50ICs9IGFtb3VudDsKICAgICAgICAgICAgZXhpc3RpbmcucmVjb3JkQ291bnQgKz0gMTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIG1ldGhvZEdyb3Vwc1ttZXRob2RJZF0uc2V0KGZhbGxiYWNrTnVtYmVycywgewogICAgICAgICAgICAgIGJldE51bWJlcnM6IGZhbGxiYWNrTnVtYmVycywKICAgICAgICAgICAgICB0b3RhbEFtb3VudDogYW1vdW50LAogICAgICAgICAgICAgIHJlY29yZENvdW50OiAxCiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CgogICAgICBjb25zb2xlLmxvZygnW2NhbGN1bGF0ZU1ldGhvZFJhbmtpbmdCeUdyb3VwXSDlvIDlp4vnlJ/miJDmjpLooYzmppwnKTsKCiAgICAgIC8vIOS4uuavj+S4queOqeazleeUn+aIkOaOkuihjOamnOW5tui/lOWbngogICAgICByZXR1cm4gT2JqZWN0LmVudHJpZXMobWV0aG9kR3JvdXBzKS5tYXAoKFttZXRob2RJZCwgbnVtYmVyc01hcF0pID0+IHsKICAgICAgICAvLyDlsIZNYXDovazmjaLkuLrmlbDnu4TlubbmjInmgLvph5Hpop3pmY3luo/mjpLluo8KICAgICAgICBjb25zdCByYW5raW5nID0gQXJyYXkuZnJvbShudW1iZXJzTWFwLnZhbHVlcygpKQogICAgICAgICAgLnNvcnQoKGEsIGIpID0+IGIudG90YWxBbW91bnQgLSBhLnRvdGFsQW1vdW50KQogICAgICAgICAgLm1hcCgoaXRlbSwgaW5kZXgpID0+ICh7CiAgICAgICAgICAgIHJhbms6IGluZGV4ICsgMSwKICAgICAgICAgICAgYmV0TnVtYmVyczogaXRlbS5iZXROdW1iZXJzLAogICAgICAgICAgICB0b3RhbEFtb3VudDogaXRlbS50b3RhbEFtb3VudCwKICAgICAgICAgICAgcmVjb3JkQ291bnQ6IGl0ZW0ucmVjb3JkQ291bnQKICAgICAgICAgIH0pKTsKCiAgICAgICAgY29uc29sZS5sb2coYFtjYWxjdWxhdGVNZXRob2RSYW5raW5nQnlHcm91cF0g546p5rOVJHttZXRob2RJZH0oJHt0aGlzLmdldE1ldGhvZE5hbWUocGFyc2VJbnQobWV0aG9kSWQpKX0p77yaJHtyYW5raW5nLmxlbmd0aH3kuKrkuI3lkIzlj7fnoIFgKTsKCiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgIG1ldGhvZElkOiBwYXJzZUludChtZXRob2RJZCksCiAgICAgICAgICBtZXRob2ROYW1lOiB0aGlzLmdldE1ldGhvZE5hbWUocGFyc2VJbnQobWV0aG9kSWQpKSwKICAgICAgICAgIHJhbmtpbmcKICAgICAgICB9OwogICAgICB9KS5maWx0ZXIoZ3JvdXAgPT4gZ3JvdXAucmFua2luZy5sZW5ndGggPiAwKTsgLy8g5Y+q5pi+56S65pyJ5pWw5o2u55qE546p5rOVCiAgICB9LAogICAgLyoqIOagvOW8j+WMluaKleazqOWPt+eggeeUqOS6juaYvuekuiAqLwogICAgZm9ybWF0QmV0TnVtYmVyc0ZvckRpc3BsYXkoYmV0TnVtYmVyc0tleSkgewogICAgICB0cnkgewogICAgICAgIGNvbnN0IG51bWJlcnMgPSBKU09OLnBhcnNlKGJldE51bWJlcnNLZXkpOwogICAgICAgIGlmIChBcnJheS5pc0FycmF5KG51bWJlcnMpICYmIG51bWJlcnMubGVuZ3RoID4gMCkgewogICAgICAgICAgcmV0dXJuIG51bWJlcnMubWFwKG51bSA9PiB7CiAgICAgICAgICAgIGlmICh0eXBlb2YgbnVtID09PSAnb2JqZWN0JyAmJiBudW0gIT09IG51bGwpIHsKICAgICAgICAgICAgICAvLyDlpITnkIYge2E6MSwgYjoyLCBjOjMsIGQ6NCwgZTo1fSDmoLzlvI8KICAgICAgICAgICAgICBjb25zdCBrZXlzID0gT2JqZWN0LmtleXMobnVtKS5zb3J0KCk7CiAgICAgICAgICAgICAgY29uc3QgdmFsdWVzID0ga2V5cy5tYXAoa2V5ID0+IFN0cmluZyhudW1ba2V5XSkpOwogICAgICAgICAgICAgIHJldHVybiB2YWx1ZXMuam9pbignJyk7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgcmV0dXJuIFN0cmluZyhudW0pOwogICAgICAgICAgfSkuam9pbignLCAnKTsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgLy8g5aaC5p6c6Kej5p6Q5aSx6LSl77yM55u05o6l6L+U5Zue5Y6f5aeL5a2X56ym5LiyCiAgICAgIH0KICAgICAgcmV0dXJuIGJldE51bWJlcnNLZXkubGVuZ3RoID4gMjAgPyBiZXROdW1iZXJzS2V5LnN1YnN0cmluZygwLCAyMCkgKyAnLi4uJyA6IGJldE51bWJlcnNLZXk7CiAgICB9LAogICAgLyoqIOaMieeOqeazleWIhue7hOiuoeeul+WPt+eggeaOkuihjCAtIOWPquS9v+eUqG1vbmV55a2X5q61ICovCiAgICBjYWxjdWxhdGVOdW1iZXJSYW5raW5nKGRhdGEpIHsKICAgICAgLy8g5Yid5aeL5YyW6L+U5Zue5pWw57uECiAgICAgIGNvbnN0IG51bWJlclJhbmtpbmdCeUdyb3VwID0gW107CgogICAgICAvLyDmjInnjqnms5XliIbnu4Tnu5/orqEKICAgICAgY29uc3QgbWV0aG9kR3JvdXBzID0ge307CgogICAgICBkYXRhLmZvckVhY2gocmVjb3JkID0+IHsKICAgICAgICB0cnkgewogICAgICAgICAgY29uc3QgbWV0aG9kSWQgPSByZWNvcmQubWV0aG9kSWQ7CiAgICAgICAgICBjb25zdCBtb25leSA9IHBhcnNlRmxvYXQocmVjb3JkLm1vbmV5IHx8IDApOwogICAgICAgICAgY29uc3QgYmV0TnVtYmVycyA9IEpTT04ucGFyc2UocmVjb3JkLmJldE51bWJlcnMgfHwgJ3t9Jyk7CiAgICAgICAgICBjb25zdCBudW1iZXJzID0gYmV0TnVtYmVycy5udW1iZXJzIHx8IFtdOwoKICAgICAgICAgIC8vIOWIneWni+WMlueOqeazleWIhue7hAogICAgICAgICAgaWYgKCFtZXRob2RHcm91cHNbbWV0aG9kSWRdKSB7CiAgICAgICAgICAgIG1ldGhvZEdyb3Vwc1ttZXRob2RJZF0gPSB7CiAgICAgICAgICAgICAgbWV0aG9kVG90YWw6IDAsCiAgICAgICAgICAgICAgdGhyZWVEaWdpdE1hcDogbmV3IE1hcCgpCiAgICAgICAgICAgIH07CiAgICAgICAgICB9CgogICAgICAgICAgbWV0aG9kR3JvdXBzW21ldGhvZElkXS5tZXRob2RUb3RhbCArPSBtb25leTsKCiAgICAgICAgICAvLyDlpITnkIbmr4/kuKrmipXms6jlj7fnoIEKICAgICAgICAgIG51bWJlcnMuZm9yRWFjaChudW1iZXJPYmogPT4gewogICAgICAgICAgICBjb25zdCB0aHJlZURpZ2l0cyA9IHRoaXMuZXh0cmFjdFRocmVlRGlnaXROdW1iZXJzKG51bWJlck9iaik7CgogICAgICAgICAgICAvLyDlr7nlvZPliY3lj7fnoIHnmoTkuInkvY3mlbDnu4TlkIjljrvph43vvIjpgb/lhY3lkIzkuIDlj7fnoIHlhoXph43lpI3nu4TlkIjlpJrmrKHntK/orqHvvIkKICAgICAgICAgICAgY29uc3QgdW5pcXVlTm9ybWFsaXplZERpZ2l0cyA9IG5ldyBTZXQoKTsKCiAgICAgICAgICAgIHRocmVlRGlnaXRzLmZvckVhY2goZGlnaXQgPT4gewogICAgICAgICAgICAgIC8vIOW9kuS4gOWMluS4ieS9jeaVsO+8muWwhuaVsOWtl+aMieS7juWwj+WIsOWkp+aOkuW6j+S9nOS4uue7n+S4gGtleQogICAgICAgICAgICAgIGNvbnN0IG5vcm1hbGl6ZWREaWdpdCA9IHRoaXMubm9ybWFsaXplVGhyZWVEaWdpdHMoZGlnaXQpOwogICAgICAgICAgICAgIHVuaXF1ZU5vcm1hbGl6ZWREaWdpdHMuYWRkKG5vcm1hbGl6ZWREaWdpdCk7CiAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgLy8g5q+P5Liq5b2S5LiA5YyW5ZCO55qE5LiJ5L2N5pWw57Sv5Yqg6YeR6aKdCiAgICAgICAgICAgIHVuaXF1ZU5vcm1hbGl6ZWREaWdpdHMuZm9yRWFjaChub3JtYWxpemVkRGlnaXQgPT4gewogICAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRBbW91bnQgPSBtZXRob2RHcm91cHNbbWV0aG9kSWRdLnRocmVlRGlnaXRNYXAuZ2V0KG5vcm1hbGl6ZWREaWdpdCkgfHwgMDsKICAgICAgICAgICAgICBtZXRob2RHcm91cHNbbWV0aG9kSWRdLnRocmVlRGlnaXRNYXAuc2V0KG5vcm1hbGl6ZWREaWdpdCwgY3VycmVudEFtb3VudCArIG1vbmV5KTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9KTsKCiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+ino+aekOWksei0pTonLCBlcnJvcik7CiAgICAgICAgfQogICAgICB9KTsKCgoKICAgICAgLy8g55Sf5oiQ5o6S6KGM5qacCiAgIAogICAgICB0aGlzLm51bWJlclJhbmtpbmdCeUdyb3VwID0gW107CgogICAgICBPYmplY3QuZW50cmllcyhtZXRob2RHcm91cHMpLmZvckVhY2goKFttZXRob2RJZCwgZ3JvdXBdKSA9PiB7CiAgICAgICAgLy8g6L2s5o2iTWFw5Li65pWw57uE5bm25o6S5bqP77yM5pi+56S65omA5pyJ5pWw5o2uCiAgICAgICAgY29uc3Qgc29ydGVkVGhyZWVEaWdpdHMgPSBBcnJheS5mcm9tKGdyb3VwLnRocmVlRGlnaXRNYXAuZW50cmllcygpKQogICAgICAgICAgLnNvcnQoKFssYV0sIFssYl0pID0+IGIgLSBhKTsKCiAgICAgICAgY29uc3QgcmFua2luZyA9IHNvcnRlZFRocmVlRGlnaXRzLm1hcCgoW251bWJlciwgYW1vdW50XSwgaW5kZXgpID0+ICh7CiAgICAgICAgICByYW5rOiBpbmRleCArIDEsCiAgICAgICAgICBudW1iZXIsCiAgICAgICAgICBjb3VudDogMSwKICAgICAgICAgIHRvdGFsQW1vdW50OiBhbW91bnQsCiAgICAgICAgICBwZXJjZW50YWdlOiBncm91cC5tZXRob2RUb3RhbCA+IDAgPyAoKGFtb3VudCAvIGdyb3VwLm1ldGhvZFRvdGFsKSAqIDEwMCkudG9GaXhlZCgxKSA6ICcwLjAnCiAgICAgICAgfSkpOwoKICAgICAgICBpZiAocmFua2luZy5sZW5ndGggPiAwKSB7CiAgICAgICAgICBudW1iZXJSYW5raW5nQnlHcm91cC5wdXNoKHsKICAgICAgICAgICAgbWV0aG9kSWQ6IHBhcnNlSW50KG1ldGhvZElkKSwKICAgICAgICAgICAgbWV0aG9kTmFtZTogdGhpcy5nZXRNZXRob2ROYW1lKHBhcnNlSW50KG1ldGhvZElkKSksCiAgICAgICAgICAgIHJhbmtpbmcKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CgogICAgICByZXR1cm4gbnVtYmVyUmFua2luZ0J5R3JvdXA7CiAgICB9LAogICAgLyoqIOS7juaKleazqOWPt+eggeS4reaPkOWPluaJgOacieWPr+iDveeahOS4ieS9jeaVsOe7hOWQiCAqLwogICAgZXh0cmFjdFRocmVlRGlnaXROdW1iZXJzKG51bWJlck9iaikgewogICAgICBjb25zdCBudW1iZXJzID0gW107CgogICAgICAvLyDlpITnkIbkuI3lkIznmoTlj7fnoIHmoLzlvI8KICAgICAgaWYgKHR5cGVvZiBudW1iZXJPYmogPT09ICdzdHJpbmcnKSB7CiAgICAgICAgLy8g55u05o6l5piv5a2X56ym5Liy5qC85byP55qE5Y+356CBCiAgICAgICAgY29uc3QgY2xlYW5TdHIgPSBudW1iZXJPYmoucmVwbGFjZSgvXEQvZywgJycpOyAvLyDnp7vpmaTpnZ7mlbDlrZflrZfnrKYKICAgICAgICB0aGlzLmdlbmVyYXRlVGhyZWVEaWdpdENvbWJpbmF0aW9ucyhjbGVhblN0ci5zcGxpdCgnJyksIG51bWJlcnMpOwogICAgICB9IGVsc2UgaWYgKHR5cGVvZiBudW1iZXJPYmogPT09ICdvYmplY3QnICYmIG51bWJlck9iaiAhPT0gbnVsbCkgewogICAgICAgIC8vIOWvueixoeagvOW8jyB7YTogMSwgYjogMiwgYzogMywgZDogNCwgZTogNX0KICAgICAgICBjb25zdCBrZXlzID0gT2JqZWN0LmtleXMobnVtYmVyT2JqKS5zb3J0KCk7CiAgICAgICAgY29uc3QgZGlnaXRzID0ga2V5cy5tYXAoa2V5ID0+IFN0cmluZyhudW1iZXJPYmpba2V5XSkpOwoKICAgIAoKICAgICAgICAvLyDnlJ/miJDmiYDmnInlj6/og73nmoTkuInkvY3mlbDnu4TlkIgKICAgICAgICB0aGlzLmdlbmVyYXRlVGhyZWVEaWdpdENvbWJpbmF0aW9ucyhkaWdpdHMsIG51bWJlcnMpOwogICAgICB9CgogICAgICByZXR1cm4gbnVtYmVyczsKICAgIH0sCiAgICAvKiog55Sf5oiQ5omA5pyJ5Y+v6IO955qE5LiJ5L2N5pWw57uE5ZCIICovCiAgICBnZW5lcmF0ZVRocmVlRGlnaXRDb21iaW5hdGlvbnMoZGlnaXRzLCBudW1iZXJzKSB7CiAgICAgIGNvbnN0IG4gPSBkaWdpdHMubGVuZ3RoOwoKICAgICAgLy8g5aaC5p6c5pWw5a2X5bCR5LqOM+S4qu+8jOaXoOazlee7hOaIkOS4ieS9jeaVsAogICAgICBpZiAobiA8IDMpIHsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIC8vIOeUn+aIkOaJgOacieWPr+iDveeahOS4ieS9jeaVsOe7hOWQiO+8iEMobiwzKe+8iQogICAgICBmb3IgKGxldCBpID0gMDsgaSA8IG4gLSAyOyBpKyspIHsKICAgICAgICBmb3IgKGxldCBqID0gaSArIDE7IGogPCBuIC0gMTsgaisrKSB7CiAgICAgICAgICBmb3IgKGxldCBrID0gaiArIDE7IGsgPCBuOyBrKyspIHsKICAgICAgICAgICAgY29uc3QgdGhyZWVEaWdpdCA9IGRpZ2l0c1tpXSArIGRpZ2l0c1tqXSArIGRpZ2l0c1trXTsKICAgICAgICAgICAgaWYgKC9eXGR7M30kLy50ZXN0KHRocmVlRGlnaXQpKSB7CiAgICAgICAgICAgICAgbnVtYmVycy5wdXNoKHRocmVlRGlnaXQpOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICB9LAoKICAgIC8qKiDlvZLkuIDljJbkuInkvY3mlbDvvJrlsIbmlbDlrZfmjInku47lsI/liLDlpKfmjpLluo/vvIznu5/kuIDnm7jlkIzmlbDlrZfnmoTkuI3lkIzmjpLliJcgKi8KICAgIG5vcm1hbGl6ZVRocmVlRGlnaXRzKHRocmVlRGlnaXQpIHsKICAgICAgLy8g5bCG5LiJ5L2N5pWw5a2X56ym5Liy6L2s5o2i5Li65pWw57uE77yM5o6S5bqP5ZCO6YeN5paw57uE5ZCICiAgICAgIHJldHVybiB0aHJlZURpZ2l0LnNwbGl0KCcnKS5zb3J0KCkuam9pbignJyk7CiAgICB9LAogICAgLyoqIOWIt+aWsOaVsOaNriAqLwogICAgYXN5bmMgcmVmcmVzaERhdGEoKSB7CiAgICAgIGF3YWl0IHRoaXMuY2FsY3VsYXRlU3RhdGlzdGljcygpOwogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+aVsOaNruW3suWIt+aWsCcpOwogICAgfSwKICAgIC8qKiDmtYvor5XkuInkvY3mlbDnu4TlkIjmj5Dlj5bpgLvovpEgKi8KICAgIHRlc3RUaHJlZURpZ2l0RXh0cmFjdGlvbigpIHsKICAgICAgY29uc29sZS5sb2coJz09PSDmtYvor5XkuInkvY3mlbDnu4TlkIjmj5Dlj5blkozlvZLkuIDljJbpgLvovpEgPT09Jyk7CgogICAgICAvLyDmtYvor5XkuI3lkIzplb/luqbnmoTlj7fnoIEKICAgICAgY29uc3QgdGVzdENhc2VzID0gWwogICAgICAgIHthOiAxLCBiOiAyLCBjOiAzfSwgICAgICAgICAgICAgICAgICAgIC8vIOS4ieS9jeaVsAogICAgICAgIHthOiAxLCBiOiAyLCBjOiAzLCBkOiA0fSwgICAgICAgICAgICAgLy8g5Zub5L2N5pWwCiAgICAgICAge2E6IDAsIGI6IDEsIGM6IDIsIGQ6IDMsIGU6IDQsIGY6IDV9LCAvLyDlha3kvY3mlbAKICAgICAgICB7YTogMSwgYjogMiwgYzogMywgZDogNCwgZTogNX0sICAgICAgIC8vIOS6lOS9jeaVsAogICAgICBdOwoKICAgICAgdGVzdENhc2VzLmZvckVhY2goKHRlc3RDYXNlLCBpbmRleCkgPT4gewogICAgICAgIGNvbnNvbGUubG9nKGBcbua1i+ivleeUqOS+iyAke2luZGV4ICsgMX06YCwgdGVzdENhc2UpOwogICAgICAgIGNvbnN0IHRocmVlRGlnaXRzID0gdGhpcy5leHRyYWN0VGhyZWVEaWdpdE51bWJlcnModGVzdENhc2UpOwogICAgICAgIGNvbnNvbGUubG9nKCfmj5Dlj5bnmoTkuInkvY3mlbDnu4TlkIg6JywgdGhyZWVEaWdpdHMpOwoKICAgICAgICAvLyDmtYvor5XlvZLkuIDljJbmlYjmnpwKICAgICAgICBjb25zdCBub3JtYWxpemVkU2V0ID0gbmV3IFNldCgpOwogICAgICAgIHRocmVlRGlnaXRzLmZvckVhY2goZGlnaXQgPT4gewogICAgICAgICAgY29uc3Qgbm9ybWFsaXplZCA9IHRoaXMubm9ybWFsaXplVGhyZWVEaWdpdHMoZGlnaXQpOwogICAgICAgICAgbm9ybWFsaXplZFNldC5hZGQobm9ybWFsaXplZCk7CiAgICAgICAgICBjb25zb2xlLmxvZyhgJHtkaWdpdH0gLT4gJHtub3JtYWxpemVkfWApOwogICAgICAgIH0pOwogICAgICAgIGNvbnNvbGUubG9nKCflvZLkuIDljJblkI7nmoTllK/kuIDnu4TlkIg6JywgQXJyYXkuZnJvbShub3JtYWxpemVkU2V0KSk7CiAgICAgIH0pOwoKICAgICAgY29uc29sZS5sb2coJz09PSDmtYvor5XlrozmiJAgPT09Jyk7CiAgICB9LAogICAgLyoqIOiuoeeul+e7hOWQiOaVsCBDKG4scikgKi8KICAgIGNhbGN1bGF0ZUNvbWJpbmF0aW9ucyhuLCByKSB7CiAgICAgIGlmIChyID4gbikgcmV0dXJuIDA7CiAgICAgIGlmIChyID09PSAwIHx8IHIgPT09IG4pIHJldHVybiAxOwoKICAgICAgbGV0IHJlc3VsdCA9IDE7CiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgcjsgaSsrKSB7CiAgICAgICAgcmVzdWx0ID0gcmVzdWx0ICogKG4gLSBpKSAvIChpICsgMSk7CiAgICAgIH0KICAgICAgcmV0dXJuIE1hdGgucm91bmQocmVzdWx0KTsKICAgIH0sCiAgICAvKiog5YWz6Zet5a+56K+d5qGGICovCiAgICAvKiog5Yqg6L2955So5oi35YiX6KGoICovCiAgICBhc3luYyBsb2FkVXNlckxpc3QoKSB7CiAgICAgIHRyeSB7CiAgICAgICAgLy8g5YWI6I635Y+W5omA5pyJ55So5oi3CiAgICAgICAgY29uc3QgdXNlclJlc3BvbnNlID0gYXdhaXQgcmVxdWVzdCh7CiAgICAgICAgICB1cmw6ICcvc3lzdGVtL3VzZXIvbGlzdCcsCiAgICAgICAgICBtZXRob2Q6ICdnZXQnLAogICAgICAgICAgcGFyYW1zOiB7IHBhZ2VOdW06IDEsIHBhZ2VTaXplOiAxMDAwIH0KICAgICAgICB9KTsKCiAgICAgICAgaWYgKCF1c2VyUmVzcG9uc2UgfHwgIXVzZXJSZXNwb25zZS5yb3dzKSB7CiAgICAgICAgICB0aGlzLnVzZXJMaXN0ID0gW107CiAgICAgICAgICByZXR1cm47CiAgICAgICAgfQoKICAgICAgICBjb25zdCBhbGxVc2VycyA9IHVzZXJSZXNwb25zZS5yb3dzLmZpbHRlcih1c2VyID0+IHVzZXIuc3RhdHVzID09PSAnMCcpOyAvLyDlj6rojrflj5bmraPluLjnirbmgIHnmoTnlKjmiLcKCiAgICAgICAgLy8g6I635Y+W5pyJ5LiL5rOo6K6w5b2V55qE55So5oi3SUTliJfooagKICAgICAgICBjb25zdCByZWNvcmRSZXNwb25zZSA9IGF3YWl0IHJlcXVlc3QoewogICAgICAgICAgdXJsOiAnL2dhbWUvcmVjb3JkL2xpc3QnLAogICAgICAgICAgbWV0aG9kOiAnZ2V0JywKICAgICAgICAgIHBhcmFtczogewogICAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgICBwYWdlU2l6ZTogMTAwMDAwIC8vIOiOt+WPluaJgOacieiusOW9leadpee7n+iuoeeUqOaItwogICAgICAgICAgfQogICAgICAgIH0pOwoKICAgICAgICBpZiAocmVjb3JkUmVzcG9uc2UgJiYgcmVjb3JkUmVzcG9uc2Uucm93cykgewogICAgICAgICAgLy8g57uf6K6h5q+P5Liq55So5oi355qE5LiL5rOo6K6w5b2V5pWw6YePCiAgICAgICAgICBjb25zdCB1c2VyUmVjb3JkQ291bnRzID0ge307CiAgICAgICAgICByZWNvcmRSZXNwb25zZS5yb3dzLmZvckVhY2gocmVjb3JkID0+IHsKICAgICAgICAgICAgY29uc3QgdXNlcklkID0gcmVjb3JkLnN5c1VzZXJJZDsKICAgICAgICAgICAgdXNlclJlY29yZENvdW50c1t1c2VySWRdID0gKHVzZXJSZWNvcmRDb3VudHNbdXNlcklkXSB8fCAwKSArIDE7CiAgICAgICAgICB9KTsKCiAgICAgICAgICAvLyDlj6rkv53nlZnmnInkuIvms6jorrDlvZXnmoTnlKjmiLfvvIzlubbmt7vliqDorrDlvZXmlbDph4/kv6Hmga8KICAgICAgICAgIHRoaXMudXNlckxpc3QgPSBhbGxVc2VycwogICAgICAgICAgICAuZmlsdGVyKHVzZXIgPT4gdXNlclJlY29yZENvdW50c1t1c2VyLnVzZXJJZF0pCiAgICAgICAgICAgIC5tYXAodXNlciA9PiAoewogICAgICAgICAgICAgIC4uLnVzZXIsCiAgICAgICAgICAgICAgcmVjb3JkQ291bnQ6IHVzZXJSZWNvcmRDb3VudHNbdXNlci51c2VySWRdIHx8IDAKICAgICAgICAgICAgfSkpCiAgICAgICAgICAgIC5zb3J0KChhLCBiKSA9PiBiLnJlY29yZENvdW50IC0gYS5yZWNvcmRDb3VudCk7IC8vIOaMieS4i+azqOiusOW9leaVsOmHj+mZjeW6j+aOkuWIlwoKICAgICAgICAgIGNvbnNvbGUubG9nKGDliqDovb3nlKjmiLfliJfooajlrozmiJDvvIzlhbEgJHt0aGlzLnVzZXJMaXN0Lmxlbmd0aH0g5Liq5pyJ5LiL5rOo6K6w5b2V55qE55So5oi3YCk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMudXNlckxpc3QgPSBbXTsKICAgICAgICAgIGNvbnNvbGUud2Fybign5pyq6I635Y+W5Yiw5LiL5rOo6K6w5b2V5pWw5o2uJyk7CiAgICAgICAgfQoKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bnlKjmiLfliJfooajlpLHotKU6JywgZXJyb3IpOwogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPlueUqOaIt+WIl+ihqOWksei0pScpOwogICAgICAgIHRoaXMudXNlckxpc3QgPSBbXTsKICAgICAgfQogICAgfSwKCiAgICAvKiog6I635Y+W5oyH5a6a55So5oi355qE6K6w5b2V5pWw5o2uICovCiAgICBhc3luYyBmZXRjaFVzZXJSZWNvcmREYXRhKHVzZXJJZCkgewogICAgICB0cnkgewogICAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCByZXF1ZXN0KHsKICAgICAgICAgIHVybDogJy9nYW1lL3JlY29yZC9saXN0JywKICAgICAgICAgIG1ldGhvZDogJ2dldCcsCiAgICAgICAgICBwYXJhbXM6IHsKICAgICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgICAgcGFnZVNpemU6IDEwMDAwMCwKICAgICAgICAgICAgc3lzVXNlcklkOiB1c2VySWQKICAgICAgICAgIH0KICAgICAgICB9KTsKCiAgICAgICAgaWYgKHJlc3BvbnNlICYmIHJlc3BvbnNlLnJvd3MpIHsKICAgICAgICAgIHJldHVybiByZXNwb25zZS5yb3dzOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICByZXR1cm4gW107CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlueUqOaIt+iusOW9leaVsOaNruWksei0pTonLCBlcnJvcik7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+W55So5oi36K6w5b2V5pWw5o2u5aSx6LSlJyk7CiAgICAgICAgcmV0dXJuIFtdOwogICAgICB9IGZpbmFsbHkgewogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9CiAgICB9LAoKICAgIC8qKiDnlKjmiLfpgInmi6nlj5jljJblpITnkIYgKi8KICAgIGFzeW5jIGhhbmRsZVVzZXJDaGFuZ2UodXNlcklkKSB7CiAgICAgIC8vIOa4heepuuW9k+WJjee7n+iuoeaVsOaNrgogICAgICB0aGlzLm1ldGhvZFJhbmtpbmdCeUdyb3VwID0gW107CiAgICAgIHRoaXMubnVtYmVyUmFua2luZ0J5R3JvdXAgPSBbXTsKCiAgICAgIGlmICh1c2VySWQpIHsKICAgICAgICAvLyDnm7TmjqXliqDovb3pgInkuK3nlKjmiLfnmoTnu5/orqHmlbDmja4KICAgICAgICBhd2FpdCB0aGlzLmNhbGN1bGF0ZVN0YXRpc3RpY3MoKTsKICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYOW3suWKoOi9veeUqOaItyAke3RoaXMuZ2V0Q3VycmVudFVzZXJOYW1lKCl9IOeahOe7n+iuoeaVsOaNrmApOwogICAgICB9CiAgICB9LAoKICAgIC8qKiDojrflj5blvZPliY3pgInkuK3nlKjmiLfnmoTlkI3np7AgKi8KICAgIGdldEN1cnJlbnRVc2VyTmFtZSgpIHsKICAgICAgaWYgKCF0aGlzLnNlbGVjdGVkVXNlcklkKSByZXR1cm4gJyc7CiAgICAgIGNvbnN0IHVzZXIgPSB0aGlzLnVzZXJMaXN0LmZpbmQodSA9PiB1LnVzZXJJZCA9PT0gdGhpcy5zZWxlY3RlZFVzZXJJZCk7CiAgICAgIHJldHVybiB1c2VyID8gKHVzZXIubmlja05hbWUgfHwgdXNlci51c2VyTmFtZSkgOiAnJzsKICAgIH0sCgogICAgLyoqIOW9qeenjeWIh+aNouWkhOeQhiAqLwogICAgaGFuZGxlTG90dGVyeVR5cGVDaGFuZ2UodGFiKSB7CiAgICAgIGNvbnNvbGUubG9nKGDliIfmjaLliLDlvannp406ICR7dGFiLm5hbWUgPT09ICdmdWNhaScgPyAn56aP5b2pM0QnIDogJ+S9k+W9qVAzJ31gKTsKICAgICAgLy8g5YiH5o2i5b2p56eN5pe277yM6K6h566X5bGe5oCn5Lya6Ieq5Yqo5pu05paw5pi+56S655qE5pWw5o2uCiAgICB9LAoKICAgIC8qKiDliLfmlrDnu5/orqHmlbDmja4gKi8KICAgIGFzeW5jIHJlZnJlc2hTdGF0aXN0aWNzKCkgewogICAgICBpZiAodGhpcy5pc0FkbWluICYmICF0aGlzLnNlbGVjdGVkVXNlcklkKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7flhYjpgInmi6nopoHmn6XnnIvnu5/orqHnmoTnlKjmiLcnKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIHRyeSB7CiAgICAgICAgYXdhaXQgdGhpcy5sb2FkR2FtZU1ldGhvZHMoKTsKICAgICAgICBhd2FpdCB0aGlzLmNhbGN1bGF0ZVN0YXRpc3RpY3MoKTsKICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+e7n+iuoeaVsOaNruW3suWIt+aWsCcpOwogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WIt+aWsOe7n+iuoeWksei0pTonLCBlcnJvcik7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Yi35paw57uf6K6h5aSx6LSlJyk7CiAgICAgIH0KICAgIH0sCgogICAgY2xvc2UoKSB7CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlOwogICAgICAvLyDmuIXnqbrpgInmi6nnirbmgIEKICAgICAgdGhpcy5zZWxlY3RlZFVzZXJJZCA9IG51bGw7CiAgICAgIHRoaXMuYWN0aXZlTG90dGVyeVR5cGUgPSAnZnVjYWknOyAvLyDph43nva7kuLrnpo/lvakKICAgICAgLy8g5riF56m65omA5pyJ57uf6K6h5pWw5o2uCiAgICAgIHRoaXMuY2xlYXJBbGxSYW5raW5nRGF0YSgpOwogICAgfQogIH0KfQo="}, null]}