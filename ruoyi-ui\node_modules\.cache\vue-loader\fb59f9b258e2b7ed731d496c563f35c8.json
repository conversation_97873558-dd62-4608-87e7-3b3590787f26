{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetStatistics.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetStatistics.vue", "mtime": 1756001841596}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750942928175}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750942928768}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCByZXF1ZXN0IGZyb20gJ0AvdXRpbHMvcmVxdWVzdCcKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnQmV0U3RhdGlzdGljcycsCiAgcHJvcHM6IHsKICAgIHZpc2libGU6IHsKICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgZGVmYXVsdDogZmFsc2UKICAgIH0KICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgbWV0aG9kUmFua2luZ0J5R3JvdXA6IFtdLCAvLyDmjInnjqnms5XliIbnu4TnmoTmjpLooYzmppwKICAgICAgbnVtYmVyUmFua2luZ0J5R3JvdXA6IFtdLCAvLyDmjInnjqnms5XliIbnu4TnmoTlj7fnoIHmjpLooYzmppwKICAgICAgZ2FtZU1ldGhvZHNEYXRhOiBbXSwgLy8g546p5rOV5pWw5o2uCiAgICAgIC8vIOeuoeeQhuWRmOWKn+iDveebuOWFswogICAgICBzZWxlY3RlZFVzZXJJZDogbnVsbCwgLy8g6YCJ5Lit55qE55So5oi3SUQKICAgICAgdXNlckxpc3Q6IFtdLCAvLyDnlKjmiLfliJfooagKICAgICAgbG9hZGluZzogZmFsc2UsIC8vIOWKoOi9veeKtuaAgQogICAgICAvLyDlvannp43nm7jlhbMKICAgICAgYWN0aXZlTG90dGVyeVR5cGU6ICdmdWNhaScsIC8vIOW9k+WJjemAieS4reeahOW9qeenje+8mmZ1Y2FpKOemj+W9qTNEKSDmiJYgdGljYWko5L2T5b2pUDMpCiAgICAgIC8vIOWIhuW9qeenjeeahOe7n+iuoeaVsOaNrgogICAgICBmdWNhaU1ldGhvZFJhbmtpbmc6IFtdLCAvLyDnpo/lvanmipXms6jmjpLooYwKICAgICAgZnVjYWlOdW1iZXJSYW5raW5nOiBbXSwgLy8g56aP5b2p54Ot6Zeo5LiJ5L2N5pWw5o6S6KGMCiAgICAgIHRpY2FpTWV0aG9kUmFua2luZzogW10sIC8vIOS9k+W9qeaKleazqOaOkuihjAogICAgICB0aWNhaU51bWJlclJhbmtpbmc6IFtdLCAvLyDkvZPlvanng63pl6jkuInkvY3mlbDmjpLooYwKICAgICAgLy8g5pCc57Si5Yqf6IO955u45YWzCiAgICAgIHNlYXJjaE51bWJlcjogJycsIC8vIOaQnOe0oueahOWPt+eggQogICAgICBpc1NlYXJjaE1vZGU6IGZhbHNlLCAvLyDmmK/lkKblpITkuo7mkJzntKLmqKHlvI8KICAgICAgc2VhcmNoTG9hZGluZzogZmFsc2UsIC8vIOaQnOe0ouWKoOi9veeKtuaAgQogICAgICBzZWFyY2hSZXN1bHRDb3VudDogMCwgLy8g5pCc57Si57uT5p6c5pWw6YePCiAgICAgIC8vIOWOn+Wni+WujOaVtOaVsOaNru+8iOeUqOS6juaQnOe0oui/h+a7pO+8iQogICAgICBvcmlnaW5hbEZ1Y2FpTWV0aG9kUmFua2luZzogW10sCiAgICAgIG9yaWdpbmFsRnVjYWlOdW1iZXJSYW5raW5nOiBbXSwKICAgICAgb3JpZ2luYWxUaWNhaU1ldGhvZFJhbmtpbmc6IFtdLAogICAgICBvcmlnaW5hbFRpY2FpTnVtYmVyUmFua2luZzogW10KICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICAvKiog5piv5ZCm5piv566h55CG5ZGYICovCiAgICBpc0FkbWluKCkgewogICAgICBjb25zdCB1c2VySW5mbyA9IHRoaXMuJHN0b3JlLmdldHRlcnMudXNlckluZm87CiAgICAgIGNvbnN0IHJvbGVzID0gdGhpcy4kc3RvcmUuZ2V0dGVycy5yb2xlczsKCiAgICAgIC8vIOajgOafpeaYr+WQpuaYr+i2hee6p+euoeeQhuWRmO+8iOeUqOaIt0lE5Li6Me+8iQogICAgICBpZiAodXNlckluZm8gJiYgdXNlckluZm8udXNlcklkID09PSAxKSB7CiAgICAgICAgcmV0dXJuIHRydWU7CiAgICAgIH0KCiAgICAgIC8vIOajgOafpeaYr+WQpuacieeuoeeQhuWRmOinkuiJsgogICAgICBpZiAocm9sZXMgJiYgcm9sZXMubGVuZ3RoID4gMCkgewogICAgICAgIHJldHVybiByb2xlcy5pbmNsdWRlcygnYWRtaW4nKSB8fCByb2xlcy5pbmNsdWRlcygnM2RfYWRtaW4nKTsKICAgICAgfQoKICAgICAgcmV0dXJuIGZhbHNlOwogICAgfSwKCiAgICAvKiog5b2T5YmN5b2p56eN55qE5oqV5rOo5o6S6KGM5pWw5o2uICovCiAgICBjdXJyZW50TWV0aG9kUmFua2luZygpIHsKICAgICAgcmV0dXJuIHRoaXMuYWN0aXZlTG90dGVyeVR5cGUgPT09ICdmdWNhaScgPyB0aGlzLmZ1Y2FpTWV0aG9kUmFua2luZyA6IHRoaXMudGljYWlNZXRob2RSYW5raW5nOwogICAgfSwKCiAgICAvKiog5b2T5YmN5b2p56eN55qE54Ot6Zeo5LiJ5L2N5pWw5o6S6KGM5pWw5o2uICovCiAgICBjdXJyZW50TnVtYmVyUmFua2luZygpIHsKICAgICAgcmV0dXJuIHRoaXMuYWN0aXZlTG90dGVyeVR5cGUgPT09ICdmdWNhaScgPyB0aGlzLmZ1Y2FpTnVtYmVyUmFua2luZyA6IHRoaXMudGljYWlOdW1iZXJSYW5raW5nOwogICAgfSwKCiAgICAvKiog5pCc57Si57uT5p6c5qCH6aKYICovCiAgICBzZWFyY2hSZXN1bHRUaXRsZSgpIHsKICAgICAgcmV0dXJuIGDmkJzntKLlj7fnoIEgIiR7dGhpcy5zZWFyY2hOdW1iZXJ9IiDnmoTnu5PmnpzvvJrlhbHmib7liLAgJHt0aGlzLnNlYXJjaFJlc3VsdENvdW50fSDkuKrljLnphY3poblgOwogICAgfQogIH0sCiAgd2F0Y2g6IHsKICAgIHZpc2libGUodmFsKSB7CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHZhbDsKICAgICAgaWYgKHZhbCkgewogICAgICAgIC8vIOi/kOihjOa1i+ivlQogICAgICAgIHRoaXMudGVzdFRocmVlRGlnaXRFeHRyYWN0aW9uKCk7CgogICAgICAgIC8vIOWmguaenOaYr+euoeeQhuWRmO+8jOWPquWKoOi9veeUqOaIt+WIl+ihqOWSjOeOqeazleaVsOaNru+8jOS4jeiHquWKqOiuoeeul+e7n+iuoQogICAgICAgIGlmICh0aGlzLmlzQWRtaW4pIHsKICAgICAgICAgIHRoaXMubG9hZFVzZXJMaXN0KCk7CiAgICAgICAgICB0aGlzLmxvYWRHYW1lTWV0aG9kcygpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAvLyDmma7pgJrnlKjmiLfoh6rliqjliqDovb3nu5/orqEKICAgICAgICAgIHRoaXMubG9hZEdhbWVNZXRob2RzKCkudGhlbihhc3luYyAoKSA9PiB7CiAgICAgICAgICAgIC8vIOaZrumAmueUqOaIt+S7jnNlc3Npb25TdG9yYWdl6I635Y+W6Ieq5bex55qE5pWw5o2uCiAgICAgICAgICAgIGNvbnN0IHN5c1VzZXJJZCA9IHNlc3Npb25TdG9yYWdlLmdldEl0ZW0oJ3N5c1VzZXJJZCcpOwogICAgICAgICAgICBjb25zb2xlLmxvZygnW0JldFN0YXRpc3RpY3NdIHNlc3Npb25TdG9yYWdl5Lit55qEc3lzVXNlcklkOicsIHN5c1VzZXJJZCk7CgogICAgICAgICAgICBpZiAoc3lzVXNlcklkKSB7CiAgICAgICAgICAgICAgdGhpcy5zZWxlY3RlZFVzZXJJZCA9IHBhcnNlSW50KHN5c1VzZXJJZCk7CiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ1tCZXRTdGF0aXN0aWNzXSDorr7nva5zZWxlY3RlZFVzZXJJZDonLCB0aGlzLnNlbGVjdGVkVXNlcklkKTsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICBjb25zb2xlLndhcm4oJ1tCZXRTdGF0aXN0aWNzXSDml6Dms5Xku45zZXNzaW9uU3RvcmFnZeiOt+WPlnN5c1VzZXJJZCcpOwogICAgICAgICAgICB9CiAgICAgICAgICAgIGF3YWl0IHRoaXMuY2FsY3VsYXRlU3RhdGlzdGljcygpOwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgZGlhbG9nVmlzaWJsZSh2YWwpIHsKICAgICAgdGhpcy4kZW1pdCgndXBkYXRlOnZpc2libGUnLCB2YWwpOwogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgLyoqIOWKoOi9veeOqeazleaVsOaNriAqLwogICAgYXN5bmMgbG9hZEdhbWVNZXRob2RzKCkgewogICAgICB0cnkgewogICAgICAgIC8vIOebtOaOpeS7jkFQSeiOt+WPlueOqeazleaVsOaNrgogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgcmVxdWVzdCh7CiAgICAgICAgICB1cmw6ICcvZ2FtZS9vZGRzL2xpc3QnLAogICAgICAgICAgbWV0aG9kOiAnZ2V0JywKICAgICAgICAgIHBhcmFtczogeyBwYWdlTnVtOiAxLCBwYWdlU2l6ZTogMTAwMCB9CiAgICAgICAgfSk7CgogICAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS5yb3dzKSB7CiAgICAgICAgICB0aGlzLmdhbWVNZXRob2RzRGF0YSA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgIAogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLmdhbWVNZXRob2RzRGF0YSA9IFtdOwogICAgICAgICAgY29uc29sZS53YXJuKCfnjqnms5XmlbDmja7kuLrnqbonKTsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W546p5rOV5pWw5o2u5aSx6LSlOicsIGVycm9yKTsKICAgICAgICAvLyDlpoLmnpxBUEnlpLHotKXvvIzkvb/nlKjln7rmnKznmoTnjqnms5XmmKDlsIQKICAgICAgICB0aGlzLmdhbWVNZXRob2RzRGF0YSA9IHRoaXMuZ2V0QmFzaWNNZXRob2RzRGF0YSgpOwogICAgICB9CiAgICB9LAogICAgLyoqIOiOt+WPluWfuuacrOeOqeazleaVsOaNru+8iEFQSeWksei0peaXtueahOWkh+eUqOaWueahiO+8iSAqLwogICAgZ2V0QmFzaWNNZXRob2RzRGF0YSgpIHsKICAgICAgcmV0dXJuIFsKICAgICAgICB7IG1ldGhvZElkOiAxLCBtZXRob2ROYW1lOiAn54us6IOGJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDIsIG1ldGhvZE5hbWU6ICfkuIDnoIHlrprkvY0nIH0sCiAgICAgICAgeyBtZXRob2RJZDogMywgbWV0aG9kTmFtZTogJ+S4pOeggee7hOWQiCcgfSwKICAgICAgICB7IG1ldGhvZElkOiA0LCBtZXRob2ROYW1lOiAn5Lik56CB5a+55a2QJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDUsIG1ldGhvZE5hbWU6ICfkuInnoIHnm7TpgIknIH0sCiAgICAgICAgeyBtZXRob2RJZDogNiwgbWV0aG9kTmFtZTogJ+S4ieeggee7hOmAiScgfSwKICAgICAgICB7IG1ldGhvZElkOiA3LCBtZXRob2ROYW1lOiAn5LiJ56CB57uE5LiJJyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDgsIG1ldGhvZE5hbWU6ICflm5vnoIHnu4Tlha0nIH0sCiAgICAgICAgeyBtZXRob2RJZDogOSwgbWV0aG9kTmFtZTogJ+Wbm+eggee7hOS4iScgfSwKICAgICAgICB7IG1ldGhvZElkOiAxMCwgbWV0aG9kTmFtZTogJ+S6lOeggee7hOWFrScgfSwKICAgICAgICB7IG1ldGhvZElkOiAxMSwgbWV0aG9kTmFtZTogJ+S6lOeggee7hOS4iScgfSwKICAgICAgICB7IG1ldGhvZElkOiAxMiwgbWV0aG9kTmFtZTogJ+WFreeggee7hOWFrScgfSwKICAgICAgICB7IG1ldGhvZElkOiAxMywgbWV0aG9kTmFtZTogJ+WFreeggee7hOS4iScgfSwKICAgICAgICB7IG1ldGhvZElkOiAxNCwgbWV0aG9kTmFtZTogJ+S4g+eggee7hOWFrScgfSwKICAgICAgICB7IG1ldGhvZElkOiAxNSwgbWV0aG9kTmFtZTogJ+S4g+eggee7hOS4iScgfSwKICAgICAgICB7IG1ldGhvZElkOiAxNiwgbWV0aG9kTmFtZTogJ+WFq+eggee7hOWFrScgfSwKICAgICAgICB7IG1ldGhvZElkOiAxNywgbWV0aG9kTmFtZTogJ+WFq+eggee7hOS4iScgfSwKICAgICAgICB7IG1ldGhvZElkOiAxOCwgbWV0aG9kTmFtZTogJ+S5neeggee7hOWFrScgfSwKICAgICAgICB7IG1ldGhvZElkOiAxOSwgbWV0aG9kTmFtZTogJ+S5neeggee7hOS4iScgfSwKICAgICAgICB7IG1ldGhvZElkOiAyMCwgbWV0aG9kTmFtZTogJ+WMheaJk+e7hOWFrScgfSwKICAgICAgICB7IG1ldGhvZElkOiAyMSwgbWV0aG9kTmFtZTogJzfmiJYyMOWSjOWAvCcgfSwKICAgICAgICB7IG1ldGhvZElkOiAyMiwgbWV0aG9kTmFtZTogJzjmiJYxOeWSjOWAvCcgfSwKICAgICAgICB7IG1ldGhvZElkOiAyMywgbWV0aG9kTmFtZTogJznmiJYxOOWSjOWAvCcgfSwKICAgICAgICB7IG1ldGhvZElkOiAyNCwgbWV0aG9kTmFtZTogJzEw5oiWMTflkozlgLwnIH0sCiAgICAgICAgeyBtZXRob2RJZDogMjUsIG1ldGhvZE5hbWU6ICcxMeaIljE25ZKM5YC8JyB9LAogICAgICAgIHsgbWV0aG9kSWQ6IDI2LCBtZXRob2ROYW1lOiAnMTLmiJYxNeWSjOWAvCcgfSwKICAgICAgICB7IG1ldGhvZElkOiAyNywgbWV0aG9kTmFtZTogJzEz5oiWMTTlkozlgLwnIH0sCiAgICAgICAgeyBtZXRob2RJZDogMjgsIG1ldGhvZE5hbWU6ICfnm7TpgInlpI3lvI8nIH0sCiAgICAgICAgeyBtZXRob2RJZDogMjksIG1ldGhvZE5hbWU6ICflkozlgLzljZXlj4wnIH0sCiAgICAgICAgeyBtZXRob2RJZDogMzAsIG1ldGhvZE5hbWU6ICflkozlgLzlpKflsI8nIH0sCiAgICAgICAgeyBtZXRob2RJZDogMzEsIG1ldGhvZE5hbWU6ICfljIXmiZPnu4TkuIknIH0sCiAgICAgICAgeyBtZXRob2RJZDogMTAwLCBtZXRob2ROYW1lOiAn5LiJ56CB6Ziy5a+5JyB9CiAgICAgIF07CiAgICB9LAogICAgLyoqIOiOt+WPlueOqeazleWQjeensCAqLwogICAgZ2V0TWV0aG9kTmFtZShtZXRob2RJZCkgewogICAgICBjb25zdCBtZXRob2QgPSB0aGlzLmdhbWVNZXRob2RzRGF0YS5maW5kKG0gPT4gbS5tZXRob2RJZCA9PT0gbWV0aG9kSWQpOwogICAgICByZXR1cm4gbWV0aG9kID8gbWV0aG9kLm1ldGhvZE5hbWUgOiBg546p5rOVJHttZXRob2RJZH1gOwogICAgfSwKICAgIC8qKiDojrflj5bmjpLlkI3moLflvI/nsbsgKi8KICAgIGdldFJhbmtDbGFzcyhyYW5rKSB7CiAgICAgIGlmIChyYW5rID09PSAxKSByZXR1cm4gJ3JhbmstZmlyc3QnOwogICAgICBpZiAocmFuayA9PT0gMikgcmV0dXJuICdyYW5rLXNlY29uZCc7CiAgICAgIGlmIChyYW5rID09PSAzKSByZXR1cm4gJ3JhbmstdGhpcmQnOwogICAgICByZXR1cm4gJ3Jhbmstb3RoZXInOwogICAgfSwKICAgIC8qKiDorqHnrpfnu5/orqHmlbDmja4gKi8KICAgIGFzeW5jIGNhbGN1bGF0ZVN0YXRpc3RpY3MoKSB7CiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCB0aGlzLmdldFJlY29yZERhdGEoKTsKCgogICAgICBpZiAoIWRhdGEgfHwgZGF0YS5sZW5ndGggPT09IDApIHsKICAgICAgICBpZiAodGhpcy5pc0FkbWluICYmICF0aGlzLnNlbGVjdGVkVXNlcklkKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+mAieaLqeimgeafpeeci+e7n+iuoeeahOeUqOaItycpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+aaguaXoOe7n+iuoeaVsOaNru+8jOivt+WFiOWIt+aWsHJlY29yZOmhtemdoicpOwogICAgICAgIH0KICAgICAgICB0aGlzLmNsZWFyQWxsUmFua2luZ0RhdGEoKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIC8vIOaMieW9qeenjeWIhuWIq+iuoeeul+e7n+iuoeaVsOaNrgogICAgICB0aGlzLmNhbGN1bGF0ZVN0YXRpc3RpY3NCeUxvdHRlcnlUeXBlKGRhdGEpOwoKICAgICAgLy8g6LCD6K+V5L+h5oGvCiAgICAgIGNvbnNvbGUubG9nKCfnu5/orqHorqHnrpflrozmiJA6JywgewogICAgICAgIOemj+W9qeaKleazqOaOkuihjDogdGhpcy5mdWNhaU1ldGhvZFJhbmtpbmcubGVuZ3RoLAogICAgICAgIOemj+W9qeeDremXqOS4ieS9jeaVsDogdGhpcy5mdWNhaU51bWJlclJhbmtpbmcubGVuZ3RoLAogICAgICAgIOS9k+W9qeaKleazqOaOkuihjDogdGhpcy50aWNhaU1ldGhvZFJhbmtpbmcubGVuZ3RoLAogICAgICAgIOS9k+W9qeeDremXqOS4ieS9jeaVsDogdGhpcy50aWNhaU51bWJlclJhbmtpbmcubGVuZ3RoLAogICAgICAgIOW9k+WJjemAieS4reW9qeenjTogdGhpcy5hY3RpdmVMb3R0ZXJ5VHlwZQogICAgICB9KTsKICAgIH0sCiAgICAvKiog6I635Y+WcmVjb3Jk5pWw5o2uICovCiAgICBhc3luYyBnZXRSZWNvcmREYXRhKCkgewogICAgICB0cnkgewogICAgICAgIC8vIOWmguaenOaciemAieaLqeeahOeUqOaIt0lE77yI566h55CG5ZGY6YCJ5oup55qE55So5oi35oiW5pmu6YCa55So5oi36Ieq5bex77yJ77yM55u05o6l5LuOQVBJ6I635Y+W5pWw5o2uCiAgICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRVc2VySWQpIHsKICAgICAgICAgIHJldHVybiBhd2FpdCB0aGlzLmZldGNoVXNlclJlY29yZERhdGEodGhpcy5zZWxlY3RlZFVzZXJJZCk7CiAgICAgICAgfQoKICAgICAgICAvLyDlpoLmnpzmmK/nrqHnkIblkZjkvYbmsqHmnInpgInmi6nnlKjmiLfvvIzmj5DnpLrpgInmi6nnlKjmiLcKICAgICAgICBpZiAodGhpcy5pc0FkbWluKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+mAieaLqeimgeafpeeci+e7n+iuoeeahOeUqOaItycpOwogICAgICAgICAgcmV0dXJuIFtdOwogICAgICAgIH0KCiAgICAgICAgLy8g5pmu6YCa55So5oi377ya55u05o6l5LuOQVBJ6I635Y+W5b2T5YmN55So5oi35pWw5o2u77yM5LuOc2Vzc2lvblN0b3JhZ2Xojrflj5bnlKjmiLdJRAogICAgICAgIGNvbnNvbGUubG9nKCdbQmV0U3RhdGlzdGljc10g5pmu6YCa55So5oi377yM55u05o6l5LuOQVBJ6I635Y+W5pWw5o2uJyk7CgogICAgICAgIC8vIOS7jnNlc3Npb25TdG9yYWdl6I635Y+W55So5oi3SUQKICAgICAgICBjb25zdCBzeXNVc2VySWQgPSBzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKCdzeXNVc2VySWQnKTsKICAgICAgICBjb25zb2xlLmxvZygnW0JldFN0YXRpc3RpY3NdIOS7jnNlc3Npb25TdG9yYWdl6I635Y+W55qEc3lzVXNlcklkOicsIHN5c1VzZXJJZCk7CgogICAgICAgIGlmIChzeXNVc2VySWQpIHsKICAgICAgICAgIGNvbnNvbGUubG9nKCdbQmV0U3RhdGlzdGljc10g5L2/55Soc3lzVXNlcklk6LCD55SoQVBJOicsIHN5c1VzZXJJZCk7CiAgICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgdGhpcy5mZXRjaFVzZXJSZWNvcmREYXRhKHBhcnNlSW50KHN5c1VzZXJJZCkpOwogICAgICAgICAgY29uc29sZS5sb2coJ1tCZXRTdGF0aXN0aWNzXSBBUEnojrflj5bliLDnmoTmlbDmja46JywgZGF0YSk7CgogICAgICAgICAgaWYgKGRhdGEgJiYgZGF0YS5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgIGNvbnNvbGUubG9nKGBbQmV0U3RhdGlzdGljc10g5oiQ5Yqf5Yqg6L295LqGICR7ZGF0YS5sZW5ndGh9IOadoee7n+iuoeaVsOaNrmApOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgY29uc29sZS53YXJuKCdbQmV0U3RhdGlzdGljc10gQVBJ6L+U5Zue56m65pWw5o2uJyk7CiAgICAgICAgICB9CgogICAgICAgICAgcmV0dXJuIGRhdGE7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIGNvbnNvbGUud2FybignW0JldFN0YXRpc3RpY3NdIOaXoOazleS7jnNlc3Npb25TdG9yYWdl6I635Y+Wc3lzVXNlcklkJyk7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+aXoOazleiOt+WPlueUqOaIt+S/oeaBr++8jOivt+mHjeaWsOeZu+W9lScpOwogICAgICAgICAgcmV0dXJuIFtdOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCfop6PmnpDnu5/orqHmlbDmja7lpLHotKU6JywgZXJyb3IpOwogICAgICAgIHJldHVybiBbXTsKICAgICAgfQogICAgfSwKICAgIC8qKiDmuIXnqbrmiYDmnInmjpLooYzmlbDmja4gKi8KICAgIGNsZWFyQWxsUmFua2luZ0RhdGEoKSB7CiAgICAgIHRoaXMuZnVjYWlNZXRob2RSYW5raW5nID0gW107CiAgICAgIHRoaXMuZnVjYWlOdW1iZXJSYW5raW5nID0gW107CiAgICAgIHRoaXMudGljYWlNZXRob2RSYW5raW5nID0gW107CiAgICAgIHRoaXMudGljYWlOdW1iZXJSYW5raW5nID0gW107CiAgICAgIC8vIOa4heepuuWOn+Wni+aVsOaNrgogICAgICB0aGlzLm9yaWdpbmFsRnVjYWlNZXRob2RSYW5raW5nID0gW107CiAgICAgIHRoaXMub3JpZ2luYWxGdWNhaU51bWJlclJhbmtpbmcgPSBbXTsKICAgICAgdGhpcy5vcmlnaW5hbFRpY2FpTWV0aG9kUmFua2luZyA9IFtdOwogICAgICB0aGlzLm9yaWdpbmFsVGljYWlOdW1iZXJSYW5raW5nID0gW107CiAgICAgIC8vIOmHjee9ruaQnOe0oueKtuaAgQogICAgICB0aGlzLmNsZWFyU2VhcmNoKCk7CiAgICB9LAoKICAgIC8qKiDmjInlvannp43liIbliKvorqHnrpfnu5/orqHmlbDmja4gKi8KICAgIGNhbGN1bGF0ZVN0YXRpc3RpY3NCeUxvdHRlcnlUeXBlKGRhdGEpIHsKICAgICAgLy8g6LCD6K+V77ya5p+l55yL5pWw5o2u57uT5p6ECiAgICAgIGlmIChkYXRhLmxlbmd0aCA+IDApIHsKICAgICAgICBjb25zb2xlLmxvZygn5pWw5o2u5qC35pysOicsIGRhdGFbMF0pOwogICAgICAgIGNvbnNvbGUubG9nKCflj6/og73nmoTlvannp43lrZfmrrU6JywgewogICAgICAgICAgbG90dGVyeVR5cGU6IGRhdGFbMF0ubG90dGVyeVR5cGUsCiAgICAgICAgICBnYW1lVHlwZTogZGF0YVswXS5nYW1lVHlwZSwKICAgICAgICAgIGxvdHRlcnlJZDogZGF0YVswXS5sb3R0ZXJ5SWQsCiAgICAgICAgICBnYW1lTmFtZTogZGF0YVswXS5nYW1lTmFtZSwKICAgICAgICAgIG1ldGhvZE5hbWU6IGRhdGFbMF0ubWV0aG9kTmFtZQogICAgICAgIH0pOwoKICAgICAgICAvLyDnu5/orqHmiYDmnInlj6/og73nmoTlvannp43moIfor4YKICAgICAgICBjb25zdCBsb3R0ZXJ5VHlwZXMgPSBbLi4ubmV3IFNldChkYXRhLm1hcChyZWNvcmQgPT4gcmVjb3JkLmxvdHRlcnlUeXBlKSldOwogICAgICAgIGNvbnN0IGdhbWVUeXBlcyA9IFsuLi5uZXcgU2V0KGRhdGEubWFwKHJlY29yZCA9PiByZWNvcmQuZ2FtZVR5cGUpKV07CiAgICAgICAgY29uc3QgbG90dGVyeUlkcyA9IFsuLi5uZXcgU2V0KGRhdGEubWFwKHJlY29yZCA9PiByZWNvcmQubG90dGVyeUlkKSldOwogICAgICAgIGNvbnN0IGdhbWVOYW1lcyA9IFsuLi5uZXcgU2V0KGRhdGEubWFwKHJlY29yZCA9PiByZWNvcmQuZ2FtZU5hbWUpKV07CgogICAgICAgIGNvbnNvbGUubG9nKCfmlbDmja7kuK3nmoTlvannp43moIfor4Y6JywgewogICAgICAgICAgbG90dGVyeVR5cGVzLAogICAgICAgICAgZ2FtZVR5cGVzLAogICAgICAgICAgbG90dGVyeUlkcywKICAgICAgICAgIGdhbWVOYW1lcwogICAgICAgIH0pOwogICAgICB9CgogICAgICAvLyDmjInlvannp43liIbnu4TmlbDmja7vvIjkvb/nlKhsb3R0ZXJ5SWTlrZfmrrXvvIkKICAgICAgY29uc3QgZnVjYWlEYXRhID0gZGF0YS5maWx0ZXIocmVjb3JkID0+IHRoaXMuaXNGdWNhaUxvdHRlcnkocmVjb3JkLmxvdHRlcnlJZCkpOwogICAgICBjb25zdCB0aWNhaURhdGEgPSBkYXRhLmZpbHRlcihyZWNvcmQgPT4gdGhpcy5pc1RpY2FpTG90dGVyeShyZWNvcmQubG90dGVyeUlkKSk7CgogICAgICBjb25zb2xlLmxvZyhg56aP5b2pM0TmlbDmja46ICR7ZnVjYWlEYXRhLmxlbmd0aH3mnaEsIOS9k+W9qVAz5pWw5o2uOiAke3RpY2FpRGF0YS5sZW5ndGh95p2hYCk7CgogICAgICAvLyDlpoLmnpzmsqHmnInmmI7noa7nmoTlvannp43ljLrliIbvvIzlsIbmiYDmnInmlbDmja7lvZLnsbvkuLrnpo/lvakzRO+8iOWFvOWuueaAp+WkhOeQhu+8iQogICAgICBpZiAoZnVjYWlEYXRhLmxlbmd0aCA9PT0gMCAmJiB0aWNhaURhdGEubGVuZ3RoID09PSAwICYmIGRhdGEubGVuZ3RoID4gMCkgewogICAgICAgIGNvbnNvbGUubG9nKCfmnKrmo4DmtYvliLDlvannp43lrZfmrrXvvIzlsIbmiYDmnInmlbDmja7lvZLnsbvkuLrnpo/lvakzRCcpOwogICAgICAgIHRoaXMuZnVjYWlNZXRob2RSYW5raW5nID0gdGhpcy5jYWxjdWxhdGVNZXRob2RSYW5raW5nQnlHcm91cChkYXRhKTsKICAgICAgICB0aGlzLmZ1Y2FpTnVtYmVyUmFua2luZyA9IHRoaXMuY2FsY3VsYXRlTnVtYmVyUmFua2luZyhkYXRhKTsKICAgICAgICB0aGlzLnRpY2FpTWV0aG9kUmFua2luZyA9IFtdOwogICAgICAgIHRoaXMudGljYWlOdW1iZXJSYW5raW5nID0gW107CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICAvLyDliIbliKvorqHnrpfnpo/lvanlkozkvZPlvannmoTnu5/orqHmlbDmja4KICAgICAgaWYgKGZ1Y2FpRGF0YS5sZW5ndGggPiAwKSB7CiAgICAgICAgdGhpcy5vcmlnaW5hbEZ1Y2FpTWV0aG9kUmFua2luZyA9IHRoaXMuY2FsY3VsYXRlTWV0aG9kUmFua2luZ0J5R3JvdXAoZnVjYWlEYXRhKTsKICAgICAgICB0aGlzLm9yaWdpbmFsRnVjYWlOdW1iZXJSYW5raW5nID0gdGhpcy5jYWxjdWxhdGVOdW1iZXJSYW5raW5nKGZ1Y2FpRGF0YSk7CiAgICAgICAgdGhpcy5mdWNhaU1ldGhvZFJhbmtpbmcgPSBbLi4udGhpcy5vcmlnaW5hbEZ1Y2FpTWV0aG9kUmFua2luZ107CiAgICAgICAgdGhpcy5mdWNhaU51bWJlclJhbmtpbmcgPSBbLi4udGhpcy5vcmlnaW5hbEZ1Y2FpTnVtYmVyUmFua2luZ107CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5vcmlnaW5hbEZ1Y2FpTWV0aG9kUmFua2luZyA9IFtdOwogICAgICAgIHRoaXMub3JpZ2luYWxGdWNhaU51bWJlclJhbmtpbmcgPSBbXTsKICAgICAgICB0aGlzLmZ1Y2FpTWV0aG9kUmFua2luZyA9IFtdOwogICAgICAgIHRoaXMuZnVjYWlOdW1iZXJSYW5raW5nID0gW107CiAgICAgIH0KCiAgICAgIGlmICh0aWNhaURhdGEubGVuZ3RoID4gMCkgewogICAgICAgIHRoaXMub3JpZ2luYWxUaWNhaU1ldGhvZFJhbmtpbmcgPSB0aGlzLmNhbGN1bGF0ZU1ldGhvZFJhbmtpbmdCeUdyb3VwKHRpY2FpRGF0YSk7CiAgICAgICAgdGhpcy5vcmlnaW5hbFRpY2FpTnVtYmVyUmFua2luZyA9IHRoaXMuY2FsY3VsYXRlTnVtYmVyUmFua2luZyh0aWNhaURhdGEpOwogICAgICAgIHRoaXMudGljYWlNZXRob2RSYW5raW5nID0gWy4uLnRoaXMub3JpZ2luYWxUaWNhaU1ldGhvZFJhbmtpbmddOwogICAgICAgIHRoaXMudGljYWlOdW1iZXJSYW5raW5nID0gWy4uLnRoaXMub3JpZ2luYWxUaWNhaU51bWJlclJhbmtpbmddOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMub3JpZ2luYWxUaWNhaU1ldGhvZFJhbmtpbmcgPSBbXTsKICAgICAgICB0aGlzLm9yaWdpbmFsVGljYWlOdW1iZXJSYW5raW5nID0gW107CiAgICAgICAgdGhpcy50aWNhaU1ldGhvZFJhbmtpbmcgPSBbXTsKICAgICAgICB0aGlzLnRpY2FpTnVtYmVyUmFua2luZyA9IFtdOwogICAgICB9CiAgICB9LAoKICAgIC8qKiDliKTmlq3mmK/lkKbkuLrnpo/lvakzRCAqLwogICAgaXNGdWNhaUxvdHRlcnkobG90dGVyeUlkKSB7CiAgICAgIC8vIOemj+W9qTNE55qEbG90dGVyeUlk5pivMQogICAgICByZXR1cm4gbG90dGVyeUlkID09PSAxIHx8IGxvdHRlcnlJZCA9PT0gJzEnOwogICAgfSwKCiAgICAvKiog5Yik5pat5piv5ZCm5Li65L2T5b2pUDMgKi8KICAgIGlzVGljYWlMb3R0ZXJ5KGxvdHRlcnlJZCkgewogICAgICAvLyDkvZPlvalQM+eahGxvdHRlcnlJZOaYrzIKICAgICAgcmV0dXJuIGxvdHRlcnlJZCA9PT0gMiB8fCBsb3R0ZXJ5SWQgPT09ICcyJzsKICAgIH0sCgogICAgLyoqIOaMieeOqeazleWIhue7hOiuoeeul+aOkuihjOamnCAqLwogICAgY2FsY3VsYXRlTWV0aG9kUmFua2luZ0J5R3JvdXAoZGF0YSkgewogICAgICBjb25zb2xlLmxvZygnW2NhbGN1bGF0ZU1ldGhvZFJhbmtpbmdCeUdyb3VwXSDlvIDlp4vorqHnrpflkITnjqnms5XmipXms6jmjpLooYzmppwnKTsKCiAgICAgIC8vIOaMieeOqeazleWIhue7hO+8jOebuOWQjOWPt+eggeWQiOW5tue0r+iuoemHkeminQogICAgICBjb25zdCBtZXRob2RHcm91cHMgPSB7fTsKCiAgICAgIGRhdGEuZm9yRWFjaChyZWNvcmQgPT4gewogICAgICAgIGNvbnN0IG1ldGhvZElkID0gcmVjb3JkLm1ldGhvZElkOwogICAgICAgIGNvbnN0IGFtb3VudCA9IHBhcnNlRmxvYXQocmVjb3JkLm1vbmV5KSB8fCAwOwoKICAgICAgICBpZiAoIW1ldGhvZEdyb3Vwc1ttZXRob2RJZF0pIHsKICAgICAgICAgIG1ldGhvZEdyb3Vwc1ttZXRob2RJZF0gPSBuZXcgTWFwKCk7IC8vIOS9v+eUqE1hcOadpeWtmOWCqOWPt+eggeWSjOWvueW6lOeahOe0r+iuoeaVsOaNrgogICAgICAgIH0KCiAgICAgICAgLy8g6Kej5p6Q5oqV5rOo5Y+356CBCiAgICAgICAgdHJ5IHsKICAgICAgICAgIGNvbnN0IGJldE51bWJlcnMgPSBKU09OLnBhcnNlKHJlY29yZC5iZXROdW1iZXJzIHx8ICd7fScpOwogICAgICAgICAgY29uc3QgbnVtYmVycyA9IGJldE51bWJlcnMubnVtYmVycyB8fCBbXTsKCiAgICAgICAgICAvLyDkuLrmr4/kuKrlj7fnoIHntK/orqHph5Hpop0KICAgICAgICAgIG51bWJlcnMuZm9yRWFjaCgobnVtYmVyT2JqKSA9PiB7CiAgICAgICAgICAgIGxldCBzaW5nbGVOdW1iZXJEaXNwbGF5ID0gJyc7CgogICAgICAgICAgICBpZiAodHlwZW9mIG51bWJlck9iaiA9PT0gJ29iamVjdCcgJiYgbnVtYmVyT2JqICE9PSBudWxsKSB7CiAgICAgICAgICAgICAgLy8g5aSE55CGIHthOjEsIGI6MiwgYzozLCBkOjQsIGU6NX0g5qC85byPCiAgICAgICAgICAgICAgY29uc3Qga2V5cyA9IE9iamVjdC5rZXlzKG51bWJlck9iaikuc29ydCgpOwogICAgICAgICAgICAgIGNvbnN0IHZhbHVlcyA9IGtleXMubWFwKGtleSA9PiBTdHJpbmcobnVtYmVyT2JqW2tleV0pKTsKICAgICAgICAgICAgICBzaW5nbGVOdW1iZXJEaXNwbGF5ID0gdmFsdWVzLmpvaW4oJycpOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIHNpbmdsZU51bWJlckRpc3BsYXkgPSBTdHJpbmcobnVtYmVyT2JqKTsKICAgICAgICAgICAgfQoKICAgICAgICAgICAgLy8g5aaC5p6c5Y+356CB5bey5a2Y5Zyo77yM57Sv6K6h6YeR6aKd5ZKM6K6w5b2V5pWwCiAgICAgICAgICAgIGlmIChtZXRob2RHcm91cHNbbWV0aG9kSWRdLmhhcyhzaW5nbGVOdW1iZXJEaXNwbGF5KSkgewogICAgICAgICAgICAgIGNvbnN0IGV4aXN0aW5nID0gbWV0aG9kR3JvdXBzW21ldGhvZElkXS5nZXQoc2luZ2xlTnVtYmVyRGlzcGxheSk7CiAgICAgICAgICAgICAgZXhpc3RpbmcudG90YWxBbW91bnQgKz0gYW1vdW50OwogICAgICAgICAgICAgIGV4aXN0aW5nLnJlY29yZENvdW50ICs9IDE7CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgLy8g5paw5Y+356CB77yM5Yib5bu66K6w5b2VCiAgICAgICAgICAgICAgbWV0aG9kR3JvdXBzW21ldGhvZElkXS5zZXQoc2luZ2xlTnVtYmVyRGlzcGxheSwgewogICAgICAgICAgICAgICAgYmV0TnVtYmVyczogc2luZ2xlTnVtYmVyRGlzcGxheSwKICAgICAgICAgICAgICAgIHRvdGFsQW1vdW50OiBhbW91bnQsCiAgICAgICAgICAgICAgICByZWNvcmRDb3VudDogMQogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgICAgY29uc29sZS53YXJuKCfop6PmnpDmipXms6jlj7fnoIHlpLHotKU6JywgcmVjb3JkLmJldE51bWJlcnMsIGVycm9yKTsKICAgICAgICAgIC8vIOWmguaenOino+aekOWksei0pe+8jOS9v+eUqOWOn+Wni+aVsOaNrgogICAgICAgICAgY29uc3QgZmFsbGJhY2tOdW1iZXJzID0gcmVjb3JkLmJldE51bWJlcnMgfHwgJ+acquefpeWPt+eggSc7CgogICAgICAgICAgaWYgKG1ldGhvZEdyb3Vwc1ttZXRob2RJZF0uaGFzKGZhbGxiYWNrTnVtYmVycykpIHsKICAgICAgICAgICAgY29uc3QgZXhpc3RpbmcgPSBtZXRob2RHcm91cHNbbWV0aG9kSWRdLmdldChmYWxsYmFja051bWJlcnMpOwogICAgICAgICAgICBleGlzdGluZy50b3RhbEFtb3VudCArPSBhbW91bnQ7CiAgICAgICAgICAgIGV4aXN0aW5nLnJlY29yZENvdW50ICs9IDE7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBtZXRob2RHcm91cHNbbWV0aG9kSWRdLnNldChmYWxsYmFja051bWJlcnMsIHsKICAgICAgICAgICAgICBiZXROdW1iZXJzOiBmYWxsYmFja051bWJlcnMsCiAgICAgICAgICAgICAgdG90YWxBbW91bnQ6IGFtb3VudCwKICAgICAgICAgICAgICByZWNvcmRDb3VudDogMQogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pOwoKICAgICAgY29uc29sZS5sb2coJ1tjYWxjdWxhdGVNZXRob2RSYW5raW5nQnlHcm91cF0g5byA5aeL55Sf5oiQ5o6S6KGM5qacJyk7CgogICAgICAvLyDkuLrmr4/kuKrnjqnms5XnlJ/miJDmjpLooYzmppzlubbov5Tlm54KICAgICAgcmV0dXJuIE9iamVjdC5lbnRyaWVzKG1ldGhvZEdyb3VwcykubWFwKChbbWV0aG9kSWQsIG51bWJlcnNNYXBdKSA9PiB7CiAgICAgICAgLy8g5bCGTWFw6L2s5o2i5Li65pWw57uE5bm25oyJ5oC76YeR6aKd6ZmN5bqP5o6S5bqPCiAgICAgICAgY29uc3QgcmFua2luZyA9IEFycmF5LmZyb20obnVtYmVyc01hcC52YWx1ZXMoKSkKICAgICAgICAgIC5zb3J0KChhLCBiKSA9PiBiLnRvdGFsQW1vdW50IC0gYS50b3RhbEFtb3VudCkKICAgICAgICAgIC5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoewogICAgICAgICAgICByYW5rOiBpbmRleCArIDEsCiAgICAgICAgICAgIGJldE51bWJlcnM6IGl0ZW0uYmV0TnVtYmVycywKICAgICAgICAgICAgdG90YWxBbW91bnQ6IGl0ZW0udG90YWxBbW91bnQsCiAgICAgICAgICAgIHJlY29yZENvdW50OiBpdGVtLnJlY29yZENvdW50CiAgICAgICAgICB9KSk7CgogICAgICAgIGNvbnNvbGUubG9nKGBbY2FsY3VsYXRlTWV0aG9kUmFua2luZ0J5R3JvdXBdIOeOqeazlSR7bWV0aG9kSWR9KCR7dGhpcy5nZXRNZXRob2ROYW1lKHBhcnNlSW50KG1ldGhvZElkKSl9Ke+8miR7cmFua2luZy5sZW5ndGh95Liq5LiN5ZCM5Y+356CBYCk7CgogICAgICAgIHJldHVybiB7CiAgICAgICAgICBtZXRob2RJZDogcGFyc2VJbnQobWV0aG9kSWQpLAogICAgICAgICAgbWV0aG9kTmFtZTogdGhpcy5nZXRNZXRob2ROYW1lKHBhcnNlSW50KG1ldGhvZElkKSksCiAgICAgICAgICByYW5raW5nCiAgICAgICAgfTsKICAgICAgfSkuZmlsdGVyKGdyb3VwID0+IGdyb3VwLnJhbmtpbmcubGVuZ3RoID4gMCk7IC8vIOWPquaYvuekuuacieaVsOaNrueahOeOqeazlQogICAgfSwKICAgIC8qKiDmoLzlvI/ljJbmipXms6jlj7fnoIHnlKjkuo7mmL7npLogKi8KICAgIGZvcm1hdEJldE51bWJlcnNGb3JEaXNwbGF5KGJldE51bWJlcnNLZXkpIHsKICAgICAgdHJ5IHsKICAgICAgICBjb25zdCBudW1iZXJzID0gSlNPTi5wYXJzZShiZXROdW1iZXJzS2V5KTsKICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShudW1iZXJzKSAmJiBudW1iZXJzLmxlbmd0aCA+IDApIHsKICAgICAgICAgIHJldHVybiBudW1iZXJzLm1hcChudW0gPT4gewogICAgICAgICAgICBpZiAodHlwZW9mIG51bSA9PT0gJ29iamVjdCcgJiYgbnVtICE9PSBudWxsKSB7CiAgICAgICAgICAgICAgLy8g5aSE55CGIHthOjEsIGI6MiwgYzozLCBkOjQsIGU6NX0g5qC85byPCiAgICAgICAgICAgICAgY29uc3Qga2V5cyA9IE9iamVjdC5rZXlzKG51bSkuc29ydCgpOwogICAgICAgICAgICAgIGNvbnN0IHZhbHVlcyA9IGtleXMubWFwKGtleSA9PiBTdHJpbmcobnVtW2tleV0pKTsKICAgICAgICAgICAgICByZXR1cm4gdmFsdWVzLmpvaW4oJycpOwogICAgICAgICAgICB9CiAgICAgICAgICAgIHJldHVybiBTdHJpbmcobnVtKTsKICAgICAgICAgIH0pLmpvaW4oJywgJyk7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIC8vIOWmguaenOino+aekOWksei0pe+8jOebtOaOpei/lOWbnuWOn+Wni+Wtl+espuS4sgogICAgICB9CiAgICAgIHJldHVybiBiZXROdW1iZXJzS2V5Lmxlbmd0aCA+IDIwID8gYmV0TnVtYmVyc0tleS5zdWJzdHJpbmcoMCwgMjApICsgJy4uLicgOiBiZXROdW1iZXJzS2V5OwogICAgfSwKICAgIC8qKiDmjInnjqnms5XliIbnu4TorqHnrpflj7fnoIHmjpLooYwgLSDlj6rkvb/nlKhtb25leeWtl+autSAqLwogICAgY2FsY3VsYXRlTnVtYmVyUmFua2luZyhkYXRhKSB7CiAgICAgIC8vIOWIneWni+WMlui/lOWbnuaVsOe7hAogICAgICBjb25zdCBudW1iZXJSYW5raW5nQnlHcm91cCA9IFtdOwoKICAgICAgLy8g5oyJ546p5rOV5YiG57uE57uf6K6hCiAgICAgIGNvbnN0IG1ldGhvZEdyb3VwcyA9IHt9OwoKICAgICAgZGF0YS5mb3JFYWNoKHJlY29yZCA9PiB7CiAgICAgICAgdHJ5IHsKICAgICAgICAgIGNvbnN0IG1ldGhvZElkID0gcmVjb3JkLm1ldGhvZElkOwogICAgICAgICAgY29uc3QgbW9uZXkgPSBwYXJzZUZsb2F0KHJlY29yZC5tb25leSB8fCAwKTsKICAgICAgICAgIGNvbnN0IGJldE51bWJlcnMgPSBKU09OLnBhcnNlKHJlY29yZC5iZXROdW1iZXJzIHx8ICd7fScpOwogICAgICAgICAgY29uc3QgbnVtYmVycyA9IGJldE51bWJlcnMubnVtYmVycyB8fCBbXTsKCiAgICAgICAgICAvLyDliJ3lp4vljJbnjqnms5XliIbnu4QKICAgICAgICAgIGlmICghbWV0aG9kR3JvdXBzW21ldGhvZElkXSkgewogICAgICAgICAgICBtZXRob2RHcm91cHNbbWV0aG9kSWRdID0gewogICAgICAgICAgICAgIG1ldGhvZFRvdGFsOiAwLAogICAgICAgICAgICAgIHRocmVlRGlnaXRNYXA6IG5ldyBNYXAoKQogICAgICAgICAgICB9OwogICAgICAgICAgfQoKICAgICAgICAgIG1ldGhvZEdyb3Vwc1ttZXRob2RJZF0ubWV0aG9kVG90YWwgKz0gbW9uZXk7CgogICAgICAgICAgLy8g5aSE55CG5q+P5Liq5oqV5rOo5Y+356CBCiAgICAgICAgICBudW1iZXJzLmZvckVhY2gobnVtYmVyT2JqID0+IHsKICAgICAgICAgICAgY29uc3QgdGhyZWVEaWdpdHMgPSB0aGlzLmV4dHJhY3RUaHJlZURpZ2l0TnVtYmVycyhudW1iZXJPYmopOwoKICAgICAgICAgICAgLy8g5a+55b2T5YmN5Y+356CB55qE5LiJ5L2N5pWw57uE5ZCI5Y676YeN77yI6YG/5YWN5ZCM5LiA5Y+356CB5YaF6YeN5aSN57uE5ZCI5aSa5qyh57Sv6K6h77yJCiAgICAgICAgICAgIGNvbnN0IHVuaXF1ZU5vcm1hbGl6ZWREaWdpdHMgPSBuZXcgU2V0KCk7CgogICAgICAgICAgICB0aHJlZURpZ2l0cy5mb3JFYWNoKGRpZ2l0ID0+IHsKICAgICAgICAgICAgICAvLyDlvZLkuIDljJbkuInkvY3mlbDvvJrlsIbmlbDlrZfmjInku47lsI/liLDlpKfmjpLluo/kvZzkuLrnu5/kuIBrZXkKICAgICAgICAgICAgICBjb25zdCBub3JtYWxpemVkRGlnaXQgPSB0aGlzLm5vcm1hbGl6ZVRocmVlRGlnaXRzKGRpZ2l0KTsKICAgICAgICAgICAgICB1bmlxdWVOb3JtYWxpemVkRGlnaXRzLmFkZChub3JtYWxpemVkRGlnaXQpOwogICAgICAgICAgICB9KTsKCiAgICAgICAgICAgIC8vIOavj+S4quW9kuS4gOWMluWQjueahOS4ieS9jeaVsOe0r+WKoOmHkeminQogICAgICAgICAgICB1bmlxdWVOb3JtYWxpemVkRGlnaXRzLmZvckVhY2gobm9ybWFsaXplZERpZ2l0ID0+IHsKICAgICAgICAgICAgICBjb25zdCBjdXJyZW50QW1vdW50ID0gbWV0aG9kR3JvdXBzW21ldGhvZElkXS50aHJlZURpZ2l0TWFwLmdldChub3JtYWxpemVkRGlnaXQpIHx8IDA7CiAgICAgICAgICAgICAgbWV0aG9kR3JvdXBzW21ldGhvZElkXS50aHJlZURpZ2l0TWFwLnNldChub3JtYWxpemVkRGlnaXQsIGN1cnJlbnRBbW91bnQgKyBtb25leSk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSk7CgogICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgICBjb25zb2xlLmVycm9yKCfop6PmnpDlpLHotKU6JywgZXJyb3IpOwogICAgICAgIH0KICAgICAgfSk7CgoKCiAgICAgIC8vIOeUn+aIkOaOkuihjOamnAogICAKICAgICAgdGhpcy5udW1iZXJSYW5raW5nQnlHcm91cCA9IFtdOwoKICAgICAgT2JqZWN0LmVudHJpZXMobWV0aG9kR3JvdXBzKS5mb3JFYWNoKChbbWV0aG9kSWQsIGdyb3VwXSkgPT4gewogICAgICAgIC8vIOi9rOaNok1hcOS4uuaVsOe7hOW5tuaOkuW6j++8jOaYvuekuuaJgOacieaVsOaNrgogICAgICAgIGNvbnN0IHNvcnRlZFRocmVlRGlnaXRzID0gQXJyYXkuZnJvbShncm91cC50aHJlZURpZ2l0TWFwLmVudHJpZXMoKSkKICAgICAgICAgIC5zb3J0KChbLGFdLCBbLGJdKSA9PiBiIC0gYSk7CgogICAgICAgIGNvbnN0IHJhbmtpbmcgPSBzb3J0ZWRUaHJlZURpZ2l0cy5tYXAoKFtudW1iZXIsIGFtb3VudF0sIGluZGV4KSA9PiAoewogICAgICAgICAgcmFuazogaW5kZXggKyAxLAogICAgICAgICAgbnVtYmVyLAogICAgICAgICAgY291bnQ6IDEsCiAgICAgICAgICB0b3RhbEFtb3VudDogYW1vdW50LAogICAgICAgICAgcGVyY2VudGFnZTogZ3JvdXAubWV0aG9kVG90YWwgPiAwID8gKChhbW91bnQgLyBncm91cC5tZXRob2RUb3RhbCkgKiAxMDApLnRvRml4ZWQoMSkgOiAnMC4wJwogICAgICAgIH0pKTsKCiAgICAgICAgaWYgKHJhbmtpbmcubGVuZ3RoID4gMCkgewogICAgICAgICAgbnVtYmVyUmFua2luZ0J5R3JvdXAucHVzaCh7CiAgICAgICAgICAgIG1ldGhvZElkOiBwYXJzZUludChtZXRob2RJZCksCiAgICAgICAgICAgIG1ldGhvZE5hbWU6IHRoaXMuZ2V0TWV0aG9kTmFtZShwYXJzZUludChtZXRob2RJZCkpLAogICAgICAgICAgICByYW5raW5nCiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwoKICAgICAgcmV0dXJuIG51bWJlclJhbmtpbmdCeUdyb3VwOwogICAgfSwKICAgIC8qKiDku47mipXms6jlj7fnoIHkuK3mj5Dlj5bmiYDmnInlj6/og73nmoTkuInkvY3mlbDnu4TlkIggKi8KICAgIGV4dHJhY3RUaHJlZURpZ2l0TnVtYmVycyhudW1iZXJPYmopIHsKICAgICAgY29uc3QgbnVtYmVycyA9IFtdOwoKICAgICAgLy8g5aSE55CG5LiN5ZCM55qE5Y+356CB5qC85byPCiAgICAgIGlmICh0eXBlb2YgbnVtYmVyT2JqID09PSAnc3RyaW5nJykgewogICAgICAgIC8vIOebtOaOpeaYr+Wtl+espuS4suagvOW8j+eahOWPt+eggQogICAgICAgIGNvbnN0IGNsZWFuU3RyID0gbnVtYmVyT2JqLnJlcGxhY2UoL1xEL2csICcnKTsgLy8g56e76Zmk6Z2e5pWw5a2X5a2X56ymCiAgICAgICAgdGhpcy5nZW5lcmF0ZVRocmVlRGlnaXRDb21iaW5hdGlvbnMoY2xlYW5TdHIuc3BsaXQoJycpLCBudW1iZXJzKTsKICAgICAgfSBlbHNlIGlmICh0eXBlb2YgbnVtYmVyT2JqID09PSAnb2JqZWN0JyAmJiBudW1iZXJPYmogIT09IG51bGwpIHsKICAgICAgICAvLyDlr7nosaHmoLzlvI8ge2E6IDEsIGI6IDIsIGM6IDMsIGQ6IDQsIGU6IDV9CiAgICAgICAgY29uc3Qga2V5cyA9IE9iamVjdC5rZXlzKG51bWJlck9iaikuc29ydCgpOwogICAgICAgIGNvbnN0IGRpZ2l0cyA9IGtleXMubWFwKGtleSA9PiBTdHJpbmcobnVtYmVyT2JqW2tleV0pKTsKCiAgICAKCiAgICAgICAgLy8g55Sf5oiQ5omA5pyJ5Y+v6IO955qE5LiJ5L2N5pWw57uE5ZCICiAgICAgICAgdGhpcy5nZW5lcmF0ZVRocmVlRGlnaXRDb21iaW5hdGlvbnMoZGlnaXRzLCBudW1iZXJzKTsKICAgICAgfQoKICAgICAgcmV0dXJuIG51bWJlcnM7CiAgICB9LAogICAgLyoqIOeUn+aIkOaJgOacieWPr+iDveeahOS4ieS9jeaVsOe7hOWQiCAqLwogICAgZ2VuZXJhdGVUaHJlZURpZ2l0Q29tYmluYXRpb25zKGRpZ2l0cywgbnVtYmVycykgewogICAgICBjb25zdCBuID0gZGlnaXRzLmxlbmd0aDsKCiAgICAgIC8vIOWmguaenOaVsOWtl+WwkeS6jjPkuKrvvIzml6Dms5Xnu4TmiJDkuInkvY3mlbAKICAgICAgaWYgKG4gPCAzKSB7CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICAvLyDnlJ/miJDmiYDmnInlj6/og73nmoTkuInkvY3mlbDnu4TlkIjvvIhDKG4sMynvvIkKICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBuIC0gMjsgaSsrKSB7CiAgICAgICAgZm9yIChsZXQgaiA9IGkgKyAxOyBqIDwgbiAtIDE7IGorKykgewogICAgICAgICAgZm9yIChsZXQgayA9IGogKyAxOyBrIDwgbjsgaysrKSB7CiAgICAgICAgICAgIGNvbnN0IHRocmVlRGlnaXQgPSBkaWdpdHNbaV0gKyBkaWdpdHNbal0gKyBkaWdpdHNba107CiAgICAgICAgICAgIGlmICgvXlxkezN9JC8udGVzdCh0aHJlZURpZ2l0KSkgewogICAgICAgICAgICAgIG51bWJlcnMucHVzaCh0aHJlZURpZ2l0KTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQogICAgfSwKCiAgICAvKiog5b2S5LiA5YyW5LiJ5L2N5pWw77ya5bCG5pWw5a2X5oyJ5LuO5bCP5Yiw5aSn5o6S5bqP77yM57uf5LiA55u45ZCM5pWw5a2X55qE5LiN5ZCM5o6S5YiXICovCiAgICBub3JtYWxpemVUaHJlZURpZ2l0cyh0aHJlZURpZ2l0KSB7CiAgICAgIC8vIOWwhuS4ieS9jeaVsOWtl+espuS4sui9rOaNouS4uuaVsOe7hO+8jOaOkuW6j+WQjumHjeaWsOe7hOWQiAogICAgICByZXR1cm4gdGhyZWVEaWdpdC5zcGxpdCgnJykuc29ydCgpLmpvaW4oJycpOwogICAgfSwKCiAgICAvKiog5pCc57Si6L6T5YWl5aSE55CGICovCiAgICBoYW5kbGVTZWFyY2hJbnB1dCh2YWx1ZSkgewogICAgICAvLyDlj6rlhYHorrjovpPlhaXmlbDlrZcKICAgICAgdGhpcy5zZWFyY2hOdW1iZXIgPSB2YWx1ZS5yZXBsYWNlKC9cRC9nLCAnJyk7CiAgICB9LAoKICAgIC8qKiDmiafooYzmkJzntKIgKi8KICAgIHBlcmZvcm1TZWFyY2goKSB7CiAgICAgIGlmICghdGhpcy5zZWFyY2hOdW1iZXIudHJpbSgpKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7fovpPlhaXopoHmkJzntKLnmoTlj7fnoIEnKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIHRoaXMuc2VhcmNoTG9hZGluZyA9IHRydWU7CiAgICAgIHRoaXMuaXNTZWFyY2hNb2RlID0gdHJ1ZTsKCiAgICAgIHRyeSB7CiAgICAgICAgLy8g5pCc57Si5b2T5YmN5b2p56eN55qE5pWw5o2uCiAgICAgICAgY29uc3Qgc2VhcmNoUmVzdWx0cyA9IHRoaXMuc2VhcmNoSW5SYW5raW5nRGF0YSh0aGlzLnNlYXJjaE51bWJlcik7CgogICAgICAgIC8vIOabtOaWsOaYvuekuuaVsOaNruS4uuaQnOe0oue7k+aenAogICAgICAgIGlmICh0aGlzLmFjdGl2ZUxvdHRlcnlUeXBlID09PSAnZnVjYWknKSB7CiAgICAgICAgICB0aGlzLmZ1Y2FpTWV0aG9kUmFua2luZyA9IHNlYXJjaFJlc3VsdHMubWV0aG9kUmFua2luZzsKICAgICAgICAgIHRoaXMuZnVjYWlOdW1iZXJSYW5raW5nID0gc2VhcmNoUmVzdWx0cy5udW1iZXJSYW5raW5nOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLnRpY2FpTWV0aG9kUmFua2luZyA9IHNlYXJjaFJlc3VsdHMubWV0aG9kUmFua2luZzsKICAgICAgICAgIHRoaXMudGljYWlOdW1iZXJSYW5raW5nID0gc2VhcmNoUmVzdWx0cy5udW1iZXJSYW5raW5nOwogICAgICAgIH0KCiAgICAgICAgLy8g6K6h566X5pCc57Si57uT5p6c5pWw6YePCiAgICAgICAgdGhpcy5zZWFyY2hSZXN1bHRDb3VudCA9IHRoaXMuY2FsY3VsYXRlU2VhcmNoUmVzdWx0Q291bnQoc2VhcmNoUmVzdWx0cyk7CgogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg5pCc57Si5a6M5oiQ77yM5om+5YiwICR7dGhpcy5zZWFyY2hSZXN1bHRDb3VudH0g5Liq5Yy56YWN6aG5YCk7CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign5pCc57Si5aSx6LSlOicsIGVycm9yKTsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmkJzntKLlpLHotKXvvIzor7fph43or5UnKTsKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICB0aGlzLnNlYXJjaExvYWRpbmcgPSBmYWxzZTsKICAgICAgfQogICAgfSwKCiAgICAvKiog5riF6Zmk5pCc57SiICovCiAgICBjbGVhclNlYXJjaCgpIHsKICAgICAgdGhpcy5zZWFyY2hOdW1iZXIgPSAnJzsKICAgICAgdGhpcy5pc1NlYXJjaE1vZGUgPSBmYWxzZTsKICAgICAgdGhpcy5zZWFyY2hSZXN1bHRDb3VudCA9IDA7CgogICAgICAvLyDmgaLlpI3ljp/lp4vmlbDmja4KICAgICAgdGhpcy5mdWNhaU1ldGhvZFJhbmtpbmcgPSBbLi4udGhpcy5vcmlnaW5hbEZ1Y2FpTWV0aG9kUmFua2luZ107CiAgICAgIHRoaXMuZnVjYWlOdW1iZXJSYW5raW5nID0gWy4uLnRoaXMub3JpZ2luYWxGdWNhaU51bWJlclJhbmtpbmddOwogICAgICB0aGlzLnRpY2FpTWV0aG9kUmFua2luZyA9IFsuLi50aGlzLm9yaWdpbmFsVGljYWlNZXRob2RSYW5raW5nXTsKICAgICAgdGhpcy50aWNhaU51bWJlclJhbmtpbmcgPSBbLi4udGhpcy5vcmlnaW5hbFRpY2FpTnVtYmVyUmFua2luZ107CiAgICB9LAoKICAgIC8qKiDlnKjmjpLooYzmlbDmja7kuK3mkJzntKLljIXlkKvmjIflrprlj7fnoIHnmoTpobnnm64gKi8KICAgIHNlYXJjaEluUmFua2luZ0RhdGEoc2VhcmNoTnVtYmVyKSB7CiAgICAgIGNvbnN0IG9yaWdpbmFsTWV0aG9kUmFua2luZyA9IHRoaXMuYWN0aXZlTG90dGVyeVR5cGUgPT09ICdmdWNhaScKICAgICAgICA/IHRoaXMub3JpZ2luYWxGdWNhaU1ldGhvZFJhbmtpbmcKICAgICAgICA6IHRoaXMub3JpZ2luYWxUaWNhaU1ldGhvZFJhbmtpbmc7CgogICAgICBjb25zdCBvcmlnaW5hbE51bWJlclJhbmtpbmcgPSB0aGlzLmFjdGl2ZUxvdHRlcnlUeXBlID09PSAnZnVjYWknCiAgICAgICAgPyB0aGlzLm9yaWdpbmFsRnVjYWlOdW1iZXJSYW5raW5nCiAgICAgICAgOiB0aGlzLm9yaWdpbmFsVGljYWlOdW1iZXJSYW5raW5nOwoKICAgICAgLy8g5pCc57Si5oqV5rOo5o6S6KGM5qacCiAgICAgIGNvbnN0IGZpbHRlcmVkTWV0aG9kUmFua2luZyA9IG9yaWdpbmFsTWV0aG9kUmFua2luZy5tYXAobWV0aG9kR3JvdXAgPT4gewogICAgICAgIGNvbnN0IGZpbHRlcmVkUmFua2luZyA9IG1ldGhvZEdyb3VwLnJhbmtpbmcuZmlsdGVyKGl0ZW0gPT4gewogICAgICAgICAgcmV0dXJuIHRoaXMuaXNOdW1iZXJNYXRjaChpdGVtLmJldE51bWJlcnMsIHNlYXJjaE51bWJlcik7CiAgICAgICAgfSk7CgogICAgICAgIHJldHVybiB7CiAgICAgICAgICAuLi5tZXRob2RHcm91cCwKICAgICAgICAgIHJhbmtpbmc6IGZpbHRlcmVkUmFua2luZwogICAgICAgIH07CiAgICAgIH0pLmZpbHRlcihtZXRob2RHcm91cCA9PiBtZXRob2RHcm91cC5yYW5raW5nLmxlbmd0aCA+IDApOwoKICAgICAgLy8g5pCc57Si54Ot6Zeo5LiJ5L2N5pWw5o6S6KGM5qacCiAgICAgIGNvbnN0IGZpbHRlcmVkTnVtYmVyUmFua2luZyA9IG9yaWdpbmFsTnVtYmVyUmFua2luZy5tYXAobWV0aG9kR3JvdXAgPT4gewogICAgICAgIGNvbnN0IGZpbHRlcmVkUmFua2luZyA9IG1ldGhvZEdyb3VwLnJhbmtpbmcuZmlsdGVyKGl0ZW0gPT4gewogICAgICAgICAgcmV0dXJuIHRoaXMuaXNOdW1iZXJNYXRjaChpdGVtLm51bWJlciwgc2VhcmNoTnVtYmVyKTsKICAgICAgICB9KTsKCiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgIC4uLm1ldGhvZEdyb3VwLAogICAgICAgICAgcmFua2luZzogZmlsdGVyZWRSYW5raW5nCiAgICAgICAgfTsKICAgICAgfSkuZmlsdGVyKG1ldGhvZEdyb3VwID0+IG1ldGhvZEdyb3VwLnJhbmtpbmcubGVuZ3RoID4gMCk7CgogICAgICByZXR1cm4gewogICAgICAgIG1ldGhvZFJhbmtpbmc6IGZpbHRlcmVkTWV0aG9kUmFua2luZywKICAgICAgICBudW1iZXJSYW5raW5nOiBmaWx0ZXJlZE51bWJlclJhbmtpbmcKICAgICAgfTsKICAgIH0sCgogICAgLyoqIOWIpOaWreWPt+eggeaYr+WQpuWMuemFjeaQnOe0ouadoeS7tiAqLwogICAgaXNOdW1iZXJNYXRjaCh0YXJnZXROdW1iZXIsIHNlYXJjaE51bWJlcikgewogICAgICBpZiAoIXRhcmdldE51bWJlciB8fCAhc2VhcmNoTnVtYmVyKSByZXR1cm4gZmFsc2U7CgogICAgICBjb25zdCB0YXJnZXQgPSBTdHJpbmcodGFyZ2V0TnVtYmVyKTsKICAgICAgY29uc3Qgc2VhcmNoID0gU3RyaW5nKHNlYXJjaE51bWJlcik7CiAgICAgIGNvbnN0IHNlYXJjaERpZ2l0cyA9IHNlYXJjaC5zcGxpdCgnJyk7CiAgICAgIGNvbnN0IHRhcmdldERpZ2l0cyA9IHRhcmdldC5zcGxpdCgnJyk7CgogICAgICAvLyDmoLjlv4PpgLvovpHvvJrmo4Dmn6XmkJzntKLlj7fnoIHkuK3nmoTmr4/kuKrmlbDlrZfmmK/lkKbpg73lnKjnm67moIflj7fnoIHkuK3lh7rnjrAKICAgICAgLy8g6L+Z5qC35Y+v5Lul5Yy56YWN5ZCE56eN6ZW/5bqm55qE55uu5qCH5Y+356CBCgogICAgICAvLyDmlrnmoYgx77ya5Lu75oSP5Yy56YWNIC0g5pCc57Si5Y+356CB5Lit55qE5Lu75LiA5pWw5a2X5Zyo55uu5qCH5Y+356CB5Lit5Ye6546w5Y2z5Yy56YWNCiAgICAgIC8vIOmAgueUqOS6jueLrOiDhuetieWNleaVsOWtl+eOqeazlQogICAgICBjb25zdCBoYXNBbnlEaWdpdCA9IHNlYXJjaERpZ2l0cy5zb21lKGRpZ2l0ID0+IHRhcmdldERpZ2l0cy5pbmNsdWRlcyhkaWdpdCkpOwoKICAgICAgLy8g5pa55qGIMu+8muWujOWFqOWMuemFjSAtIOaQnOe0ouWPt+eggeS4reeahOaJgOacieaVsOWtl+mDveWcqOebruagh+WPt+eggeS4reWHuueOsAogICAgICAvLyDpgILnlKjkuo7lpJrmlbDlrZfnu4TlkIjnjqnms5UKICAgICAgY29uc3QgaGFzQWxsRGlnaXRzID0gc2VhcmNoRGlnaXRzLmV2ZXJ5KGRpZ2l0ID0+IHRhcmdldERpZ2l0cy5pbmNsdWRlcyhkaWdpdCkpOwoKICAgICAgLy8g5pa55qGIM++8mueyvuehruWMuemFjSAtIOebruagh+WPt+eggeWMheWQq+aQnOe0ouWPt+eggeeahOi/nue7reWtkOS4sgogICAgICBjb25zdCBoYXNFeGFjdFNlcXVlbmNlID0gdGFyZ2V0LmluY2x1ZGVzKHNlYXJjaCk7CgogICAgICAvLyDmoLnmja7kuI3lkIzmg4XlhrXph4fnlKjkuI3lkIznmoTljLnphY3nrZbnlaUKICAgICAgaWYgKHNlYXJjaC5sZW5ndGggPT09IDEpIHsKICAgICAgICAvLyDljZXmlbDlrZfmkJzntKLvvJrlj6ropoHnm67moIflj7fnoIHljIXlkKvor6XmlbDlrZfljbPljLnphY0KICAgICAgICByZXR1cm4gaGFzQW55RGlnaXQ7CiAgICAgIH0gZWxzZSBpZiAoc2VhcmNoLmxlbmd0aCA9PT0gMikgewogICAgICAgIC8vIOWPjOaVsOWtl+aQnOe0ou+8muS8mOWFiOeyvuehruWMuemFje+8jOWFtuasoeS7u+aEj+WMuemFjQogICAgICAgIHJldHVybiBoYXNFeGFjdFNlcXVlbmNlIHx8IGhhc0FueURpZ2l0OwogICAgICB9IGVsc2UgaWYgKHNlYXJjaC5sZW5ndGggPj0gMykgewogICAgICAgIC8vIOS4ieS9jeaVsOWPiuS7peS4iuaQnOe0ou+8mumHh+eUqOWkmuenjeWMuemFjeetlueVpQoKICAgICAgICAvLyDnrZbnlaUx77ya57K+56Gu5bqP5YiX5Yy56YWN77yI5aaC5pCc57SiMTI177yM55uu5qCHMTI1MzTljLnphY3vvIkKICAgICAgICBpZiAoaGFzRXhhY3RTZXF1ZW5jZSkgewogICAgICAgICAgcmV0dXJuIHRydWU7CiAgICAgICAgfQoKICAgICAgICAvLyDnrZbnlaUy77ya5a+55LqO55+t55uu5qCH5Y+356CB77yIMS0y5L2N77yJ77yM6YeH55So5Lu75oSP5Yy56YWNCiAgICAgICAgaWYgKHRhcmdldC5sZW5ndGggPD0gMikgewogICAgICAgICAgcmV0dXJuIGhhc0FueURpZ2l0OwogICAgICAgIH0KCiAgICAgICAgLy8g562W55WlM++8muWvueS6juS4ieS9jeaVsOebruagh++8jOajgOafpeaYr+WQpuS4uuebuOWQjOaVsOWtl+e7hOWQiO+8iOW9kuS4gOWMluavlOi+g++8iQogICAgICAgIGlmICh0YXJnZXQubGVuZ3RoID09PSAzICYmIHNlYXJjaC5sZW5ndGggPT09IDMpIHsKICAgICAgICAgIGNvbnN0IG5vcm1hbGl6ZWRTZWFyY2ggPSB0aGlzLm5vcm1hbGl6ZVRocmVlRGlnaXRzKHNlYXJjaCk7CiAgICAgICAgICBjb25zdCBub3JtYWxpemVkVGFyZ2V0ID0gdGhpcy5ub3JtYWxpemVUaHJlZURpZ2l0cyh0YXJnZXQpOwogICAgICAgICAgcmV0dXJuIG5vcm1hbGl6ZWRTZWFyY2ggPT09IG5vcm1hbGl6ZWRUYXJnZXQ7CiAgICAgICAgfQoKICAgICAgICAvLyDnrZbnlaU077ya5a+55LqO6ZW/55uu5qCH5Y+356CB77yM6KaB5rGC5YyF5ZCr5omA5pyJ5pCc57Si5pWw5a2XCiAgICAgICAgaWYgKHRhcmdldC5sZW5ndGggPj0gc2VhcmNoLmxlbmd0aCkgewogICAgICAgICAgcmV0dXJuIGhhc0FsbERpZ2l0czsKICAgICAgICB9CgogICAgICAgIC8vIOetlueVpTXvvJrlr7nkuo7kuK3nrYnplb/luqbnm67moIflj7fnoIHvvIzph4fnlKjku7vmhI/ljLnphY0KICAgICAgICByZXR1cm4gaGFzQW55RGlnaXQ7CiAgICAgIH0KCiAgICAgIHJldHVybiBmYWxzZTsKICAgIH0sCgogICAgLyoqIOiuoeeul+aQnOe0oue7k+aenOaVsOmHjyAqLwogICAgY2FsY3VsYXRlU2VhcmNoUmVzdWx0Q291bnQoc2VhcmNoUmVzdWx0cykgewogICAgICBsZXQgY291bnQgPSAwOwoKICAgICAgLy8g57uf6K6h5oqV5rOo5o6S6KGM5qac5Yy56YWN6aG5CiAgICAgIHNlYXJjaFJlc3VsdHMubWV0aG9kUmFua2luZy5mb3JFYWNoKG1ldGhvZEdyb3VwID0+IHsKICAgICAgICBjb3VudCArPSBtZXRob2RHcm91cC5yYW5raW5nLmxlbmd0aDsKICAgICAgfSk7CgogICAgICAvLyDnu5/orqHng63pl6jkuInkvY3mlbDmjpLooYzmppzljLnphY3pobkKICAgICAgc2VhcmNoUmVzdWx0cy5udW1iZXJSYW5raW5nLmZvckVhY2gobWV0aG9kR3JvdXAgPT4gewogICAgICAgIGNvdW50ICs9IG1ldGhvZEdyb3VwLnJhbmtpbmcubGVuZ3RoOwogICAgICB9KTsKCiAgICAgIHJldHVybiBjb3VudDsKICAgIH0sCiAgICAvKiog5Yi35paw5pWw5o2uICovCiAgICBhc3luYyByZWZyZXNoRGF0YSgpIHsKICAgICAgYXdhaXQgdGhpcy5jYWxjdWxhdGVTdGF0aXN0aWNzKCk7CiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5pWw5o2u5bey5Yi35pawJyk7CiAgICB9LAogICAgLyoqIOa1i+ivleS4ieS9jeaVsOe7hOWQiOaPkOWPlumAu+i+kSAqLwogICAgdGVzdFRocmVlRGlnaXRFeHRyYWN0aW9uKCkgewogICAgICBjb25zb2xlLmxvZygnPT09IOa1i+ivleS4ieS9jeaVsOe7hOWQiOaPkOWPluWSjOW9kuS4gOWMlumAu+i+kSA9PT0nKTsKCiAgICAgIC8vIOa1i+ivleS4jeWQjOmVv+W6pueahOWPt+eggQogICAgICBjb25zdCB0ZXN0Q2FzZXMgPSBbCiAgICAgICAge2E6IDEsIGI6IDIsIGM6IDN9LCAgICAgICAgICAgICAgICAgICAgLy8g5LiJ5L2N5pWwCiAgICAgICAge2E6IDEsIGI6IDIsIGM6IDMsIGQ6IDR9LCAgICAgICAgICAgICAvLyDlm5vkvY3mlbAKICAgICAgICB7YTogMCwgYjogMSwgYzogMiwgZDogMywgZTogNCwgZjogNX0sIC8vIOWFreS9jeaVsAogICAgICAgIHthOiAxLCBiOiAyLCBjOiAzLCBkOiA0LCBlOiA1fSwgICAgICAgLy8g5LqU5L2N5pWwCiAgICAgIF07CgogICAgICB0ZXN0Q2FzZXMuZm9yRWFjaCgodGVzdENhc2UsIGluZGV4KSA9PiB7CiAgICAgICAgY29uc29sZS5sb2coYFxu5rWL6K+V55So5L6LICR7aW5kZXggKyAxfTpgLCB0ZXN0Q2FzZSk7CiAgICAgICAgY29uc3QgdGhyZWVEaWdpdHMgPSB0aGlzLmV4dHJhY3RUaHJlZURpZ2l0TnVtYmVycyh0ZXN0Q2FzZSk7CiAgICAgICAgY29uc29sZS5sb2coJ+aPkOWPlueahOS4ieS9jeaVsOe7hOWQiDonLCB0aHJlZURpZ2l0cyk7CgogICAgICAgIC8vIOa1i+ivleW9kuS4gOWMluaViOaenAogICAgICAgIGNvbnN0IG5vcm1hbGl6ZWRTZXQgPSBuZXcgU2V0KCk7CiAgICAgICAgdGhyZWVEaWdpdHMuZm9yRWFjaChkaWdpdCA9PiB7CiAgICAgICAgICBjb25zdCBub3JtYWxpemVkID0gdGhpcy5ub3JtYWxpemVUaHJlZURpZ2l0cyhkaWdpdCk7CiAgICAgICAgICBub3JtYWxpemVkU2V0LmFkZChub3JtYWxpemVkKTsKICAgICAgICAgIGNvbnNvbGUubG9nKGAke2RpZ2l0fSAtPiAke25vcm1hbGl6ZWR9YCk7CiAgICAgICAgfSk7CiAgICAgICAgY29uc29sZS5sb2coJ+W9kuS4gOWMluWQjueahOWUr+S4gOe7hOWQiDonLCBBcnJheS5mcm9tKG5vcm1hbGl6ZWRTZXQpKTsKICAgICAgfSk7CgogICAgICBjb25zb2xlLmxvZygnPT09IOa1i+ivleWujOaIkCA9PT0nKTsKICAgIH0sCiAgICAvKiog6K6h566X57uE5ZCI5pWwIEMobixyKSAqLwogICAgY2FsY3VsYXRlQ29tYmluYXRpb25zKG4sIHIpIHsKICAgICAgaWYgKHIgPiBuKSByZXR1cm4gMDsKICAgICAgaWYgKHIgPT09IDAgfHwgciA9PT0gbikgcmV0dXJuIDE7CgogICAgICBsZXQgcmVzdWx0ID0gMTsKICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCByOyBpKyspIHsKICAgICAgICByZXN1bHQgPSByZXN1bHQgKiAobiAtIGkpIC8gKGkgKyAxKTsKICAgICAgfQogICAgICByZXR1cm4gTWF0aC5yb3VuZChyZXN1bHQpOwogICAgfSwKICAgIC8qKiDlhbPpl63lr7nor53moYYgKi8KICAgIC8qKiDliqDovb3nlKjmiLfliJfooaggKi8KICAgIGFzeW5jIGxvYWRVc2VyTGlzdCgpIHsKICAgICAgdHJ5IHsKICAgICAgICAvLyDlhYjojrflj5bmiYDmnInnlKjmiLcKICAgICAgICBjb25zdCB1c2VyUmVzcG9uc2UgPSBhd2FpdCByZXF1ZXN0KHsKICAgICAgICAgIHVybDogJy9zeXN0ZW0vdXNlci9saXN0JywKICAgICAgICAgIG1ldGhvZDogJ2dldCcsCiAgICAgICAgICBwYXJhbXM6IHsgcGFnZU51bTogMSwgcGFnZVNpemU6IDEwMDAgfQogICAgICAgIH0pOwoKICAgICAgICBpZiAoIXVzZXJSZXNwb25zZSB8fCAhdXNlclJlc3BvbnNlLnJvd3MpIHsKICAgICAgICAgIHRoaXMudXNlckxpc3QgPSBbXTsKICAgICAgICAgIHJldHVybjsKICAgICAgICB9CgogICAgICAgIGNvbnN0IGFsbFVzZXJzID0gdXNlclJlc3BvbnNlLnJvd3MuZmlsdGVyKHVzZXIgPT4gdXNlci5zdGF0dXMgPT09ICcwJyk7IC8vIOWPquiOt+WPluato+W4uOeKtuaAgeeahOeUqOaItwoKICAgICAgICAvLyDojrflj5bmnInkuIvms6jorrDlvZXnmoTnlKjmiLdJROWIl+ihqAogICAgICAgIGNvbnN0IHJlY29yZFJlc3BvbnNlID0gYXdhaXQgcmVxdWVzdCh7CiAgICAgICAgICB1cmw6ICcvZ2FtZS9yZWNvcmQvbGlzdCcsCiAgICAgICAgICBtZXRob2Q6ICdnZXQnLAogICAgICAgICAgcGFyYW1zOiB7CiAgICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICAgIHBhZ2VTaXplOiAxMDAwMDAgLy8g6I635Y+W5omA5pyJ6K6w5b2V5p2l57uf6K6h55So5oi3CiAgICAgICAgICB9CiAgICAgICAgfSk7CgogICAgICAgIGlmIChyZWNvcmRSZXNwb25zZSAmJiByZWNvcmRSZXNwb25zZS5yb3dzKSB7CiAgICAgICAgICAvLyDnu5/orqHmr4/kuKrnlKjmiLfnmoTkuIvms6jorrDlvZXmlbDph48KICAgICAgICAgIGNvbnN0IHVzZXJSZWNvcmRDb3VudHMgPSB7fTsKICAgICAgICAgIHJlY29yZFJlc3BvbnNlLnJvd3MuZm9yRWFjaChyZWNvcmQgPT4gewogICAgICAgICAgICBjb25zdCB1c2VySWQgPSByZWNvcmQuc3lzVXNlcklkOwogICAgICAgICAgICB1c2VyUmVjb3JkQ291bnRzW3VzZXJJZF0gPSAodXNlclJlY29yZENvdW50c1t1c2VySWRdIHx8IDApICsgMTsKICAgICAgICAgIH0pOwoKICAgICAgICAgIC8vIOWPquS/neeVmeacieS4i+azqOiusOW9leeahOeUqOaIt++8jOW5tua3u+WKoOiusOW9leaVsOmHj+S/oeaBrwogICAgICAgICAgdGhpcy51c2VyTGlzdCA9IGFsbFVzZXJzCiAgICAgICAgICAgIC5maWx0ZXIodXNlciA9PiB1c2VyUmVjb3JkQ291bnRzW3VzZXIudXNlcklkXSkKICAgICAgICAgICAgLm1hcCh1c2VyID0+ICh7CiAgICAgICAgICAgICAgLi4udXNlciwKICAgICAgICAgICAgICByZWNvcmRDb3VudDogdXNlclJlY29yZENvdW50c1t1c2VyLnVzZXJJZF0gfHwgMAogICAgICAgICAgICB9KSkKICAgICAgICAgICAgLnNvcnQoKGEsIGIpID0+IGIucmVjb3JkQ291bnQgLSBhLnJlY29yZENvdW50KTsgLy8g5oyJ5LiL5rOo6K6w5b2V5pWw6YeP6ZmN5bqP5o6S5YiXCgogICAgICAgICAgY29uc29sZS5sb2coYOWKoOi9veeUqOaIt+WIl+ihqOWujOaIkO+8jOWFsSAke3RoaXMudXNlckxpc3QubGVuZ3RofSDkuKrmnInkuIvms6jorrDlvZXnmoTnlKjmiLdgKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy51c2VyTGlzdCA9IFtdOwogICAgICAgICAgY29uc29sZS53YXJuKCfmnKrojrflj5bliLDkuIvms6jorrDlvZXmlbDmja4nKTsKICAgICAgICB9CgogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlueUqOaIt+WIl+ihqOWksei0pTonLCBlcnJvcik7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+W55So5oi35YiX6KGo5aSx6LSlJyk7CiAgICAgICAgdGhpcy51c2VyTGlzdCA9IFtdOwogICAgICB9CiAgICB9LAoKICAgIC8qKiDojrflj5bmjIflrprnlKjmiLfnmoTorrDlvZXmlbDmja4gKi8KICAgIGFzeW5jIGZldGNoVXNlclJlY29yZERhdGEodXNlcklkKSB7CiAgICAgIHRyeSB7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHJlcXVlc3QoewogICAgICAgICAgdXJsOiAnL2dhbWUvcmVjb3JkL2xpc3QnLAogICAgICAgICAgbWV0aG9kOiAnZ2V0JywKICAgICAgICAgIHBhcmFtczogewogICAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgICBwYWdlU2l6ZTogMTAwMDAwLAogICAgICAgICAgICBzeXNVc2VySWQ6IHVzZXJJZAogICAgICAgICAgfQogICAgICAgIH0pOwoKICAgICAgICBpZiAocmVzcG9uc2UgJiYgcmVzcG9uc2Uucm93cykgewogICAgICAgICAgcmV0dXJuIHJlc3BvbnNlLnJvd3M7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHJldHVybiBbXTsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W55So5oi36K6w5b2V5pWw5o2u5aSx6LSlOicsIGVycm9yKTsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5bnlKjmiLforrDlvZXmlbDmja7lpLHotKUnKTsKICAgICAgICByZXR1cm4gW107CiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0KICAgIH0sCgogICAgLyoqIOeUqOaIt+mAieaLqeWPmOWMluWkhOeQhiAqLwogICAgYXN5bmMgaGFuZGxlVXNlckNoYW5nZSh1c2VySWQpIHsKICAgICAgLy8g5riF56m65b2T5YmN57uf6K6h5pWw5o2uCiAgICAgIHRoaXMubWV0aG9kUmFua2luZ0J5R3JvdXAgPSBbXTsKICAgICAgdGhpcy5udW1iZXJSYW5raW5nQnlHcm91cCA9IFtdOwoKICAgICAgaWYgKHVzZXJJZCkgewogICAgICAgIC8vIOebtOaOpeWKoOi9vemAieS4reeUqOaIt+eahOe7n+iuoeaVsOaNrgogICAgICAgIGF3YWl0IHRoaXMuY2FsY3VsYXRlU3RhdGlzdGljcygpOwogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg5bey5Yqg6L2955So5oi3ICR7dGhpcy5nZXRDdXJyZW50VXNlck5hbWUoKX0g55qE57uf6K6h5pWw5o2uYCk7CiAgICAgIH0KICAgIH0sCgogICAgLyoqIOiOt+WPluW9k+WJjemAieS4reeUqOaIt+eahOWQjeensCAqLwogICAgZ2V0Q3VycmVudFVzZXJOYW1lKCkgewogICAgICBpZiAoIXRoaXMuc2VsZWN0ZWRVc2VySWQpIHJldHVybiAnJzsKICAgICAgY29uc3QgdXNlciA9IHRoaXMudXNlckxpc3QuZmluZCh1ID0+IHUudXNlcklkID09PSB0aGlzLnNlbGVjdGVkVXNlcklkKTsKICAgICAgcmV0dXJuIHVzZXIgPyAodXNlci5uaWNrTmFtZSB8fCB1c2VyLnVzZXJOYW1lKSA6ICcnOwogICAgfSwKCiAgICAvKiog5b2p56eN5YiH5o2i5aSE55CGICovCiAgICBoYW5kbGVMb3R0ZXJ5VHlwZUNoYW5nZSh0YWIpIHsKICAgICAgY29uc29sZS5sb2coYOWIh+aNouWIsOW9qeenjTogJHt0YWIubmFtZSA9PT0gJ2Z1Y2FpJyA/ICfnpo/lvakzRCcgOiAn5L2T5b2pUDMnfWApOwogICAgICAvLyDliIfmjaLlvannp43ml7bvvIzorqHnrpflsZ7mgKfkvJroh6rliqjmm7TmlrDmmL7npLrnmoTmlbDmja4KICAgIH0sCgogICAgLyoqIOWIt+aWsOe7n+iuoeaVsOaNriAqLwogICAgYXN5bmMgcmVmcmVzaFN0YXRpc3RpY3MoKSB7CiAgICAgIGlmICh0aGlzLmlzQWRtaW4gJiYgIXRoaXMuc2VsZWN0ZWRVc2VySWQpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+WFiOmAieaLqeimgeafpeeci+e7n+iuoeeahOeUqOaItycpOwogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgdHJ5IHsKICAgICAgICBhd2FpdCB0aGlzLmxvYWRHYW1lTWV0aG9kcygpOwogICAgICAgIGF3YWl0IHRoaXMuY2FsY3VsYXRlU3RhdGlzdGljcygpOwogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn57uf6K6h5pWw5o2u5bey5Yi35pawJyk7CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign5Yi35paw57uf6K6h5aSx6LSlOicsIGVycm9yKTsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfliLfmlrDnu5/orqHlpLHotKUnKTsKICAgICAgfQogICAgfSwKCiAgICBjbG9zZSgpIHsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2U7CiAgICAgIC8vIOa4heepuumAieaLqeeKtuaAgQogICAgICB0aGlzLnNlbGVjdGVkVXNlcklkID0gbnVsbDsKICAgICAgdGhpcy5hY3RpdmVMb3R0ZXJ5VHlwZSA9ICdmdWNhaSc7IC8vIOmHjee9ruS4uuemj+W9qQogICAgICAvLyDmuIXnqbrmiYDmnInnu5/orqHmlbDmja4KICAgICAgdGhpcy5jbGVhckFsbFJhbmtpbmdEYXRhKCk7CiAgICB9CiAgfQp9Cg=="}, null]}