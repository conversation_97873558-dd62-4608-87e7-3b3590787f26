(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0917f0be"],{"07ac":function(e,t,i){"use strict";var n=i("23e7"),a=i("6f53").values;n({target:"Object",stat:!0},{values:function(e){return a(e)}})},"0901":function(e,t,i){"use strict";i.d(t,"e",(function(){return a})),i.d(t,"d",(function(){return s})),i.d(t,"a",(function(){return r})),i.d(t,"f",(function(){return o})),i.d(t,"c",(function(){return l})),i.d(t,"b",(function(){return c}));var n=i("b775");function a(e){return Object(n["a"])({url:"/game/customer/list",method:"get",params:e})}function s(e){return Object(n["a"])({url:"/game/customer/"+e,method:"get"})}function r(e){return Object(n["a"])({url:"/game/customer",method:"post",data:e})}function o(e){return Object(n["a"])({url:"/game/customer",method:"put",data:e})}function l(e){return Object(n["a"])({url:"/game/customer/"+e,method:"delete"})}function c(){return Object(n["a"])({url:"/game/customer/checkAndSetDefault",method:"get"})}},"0fad":function(e,t,i){"use strict";i.d(t,"f",(function(){return a})),i.d(t,"d",(function(){return s})),i.d(t,"a",(function(){return r})),i.d(t,"h",(function(){return o})),i.d(t,"c",(function(){return l})),i.d(t,"g",(function(){return c})),i.d(t,"b",(function(){return u})),i.d(t,"e",(function(){return d}));var n=i("b775");function a(e){return Object(n["a"])({url:"/game/draw/list",method:"get",params:e})}function s(e){return Object(n["a"])({url:"/game/draw/"+e,method:"get"})}function r(e){return Object(n["a"])({url:"/game/draw",method:"post",data:e})}function o(e){return Object(n["a"])({url:"/game/draw",method:"put",data:e})}function l(e){return Object(n["a"])({url:"/game/draw/"+e,method:"delete"})}function c(){return Object(n["a"])({url:"/game/draw/kaijiangjiesuan",method:"post"})}function u(e){return Object(n["a"])({url:"/game/draw/cancelPay/"+e,method:"put"})}function d(){return Object(n["a"])({url:"/game/draw/insertNextDraw",method:"post"})}},"15df":function(e,t,i){"use strict";i("3e61")},"3e61":function(e,t,i){},7284:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"app-container"},[i("div",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],staticClass:"search-container"},[i("el-form",{ref:"queryForm",staticClass:"search-form",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"80px"}},[i("el-form-item",{attrs:{label:"中奖用户",prop:"userId"}},[i("el-select",{staticStyle:{width:"200px"},attrs:{placeholder:"请选择中奖用户",clearable:"",filterable:"","prefix-icon":"el-icon-user"},on:{change:e.handleQuery},model:{value:e.queryParams.userId,callback:function(t){e.$set(e.queryParams,"userId",t)},expression:"queryParams.userId"}},e._l(e.userList,(function(e){return i("el-option",{key:e.userId,attrs:{label:e.name,value:e.userId}})})),1)],1),i("el-form-item",{attrs:{label:"流水号",prop:"serialNumber"}},[i("el-select",{staticStyle:{width:"200px"},attrs:{placeholder:"请选择流水号",clearable:"",filterable:"","prefix-icon":"el-icon-document"},on:{change:e.handleQuery},model:{value:e.queryParams.serialNumber,callback:function(t){e.$set(e.queryParams,"serialNumber",t)},expression:"queryParams.serialNumber"}},e._l(e.serialNumberList,(function(e){return i("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1),i("el-form-item",{attrs:{label:"识别框",prop:"shibie"}},[i("el-input",{staticStyle:{width:"350px"},attrs:{placeholder:"请输入识别内容（支持模糊搜索）",clearable:"",type:"textarea","prefix-icon":"el-icon-search"},on:{input:e.handleShiBieInput,clear:e.handleShiBieClear},model:{value:e.queryParams.shibie,callback:function(t){e.$set(e.queryParams,"shibie",t)},expression:"queryParams.shibie"}}),e.queryParams.shibie&&e.queryParams.shibie.trim()?i("div",{staticClass:"search-tip"},[i("i",{staticClass:"el-icon-info"}),e._v(' 模糊搜索模式：将显示所有包含"'+e._s(e.queryParams.shibie.trim())+'"的记录 ')]):e._e()],1),i("el-form-item",[i("el-button",{staticClass:"search-btn",attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleQuery}},[e._v("搜索")]),i("el-button",{staticClass:"reset-btn",attrs:{icon:"el-icon-refresh"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),i("div",{staticClass:"toolbar-container"},[i("div",{staticClass:"toolbar-content"},[i("div",{staticClass:"toolbar-left"},[i("el-button",{staticClass:"toolbar-btn expand-btn",attrs:{type:"primary",icon:e.allExpanded?"el-icon-minus":"el-icon-plus",size:"small"},on:{click:e.toggleExpandAll}},[e._v(e._s(e.allExpanded?"收起全部":"展开全部"))]),i("el-button",{staticClass:"toolbar-btn refresh-btn",attrs:{type:"info",icon:"el-icon-refresh",size:"small"},on:{click:e.getList}},[e._v("刷新数据")]),i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["game:winning:export"],expression:"['game:winning:export']"}],staticClass:"toolbar-btn export-btn",attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"small"},on:{click:e.handleExport}},[e._v("导出")]),i("el-button",{staticClass:"toolbar-btn reconciliation-btn",attrs:{type:e.isReconciliationMode?"danger":"success",icon:e.isReconciliationMode?"el-icon-close":"el-icon-check",size:"small",loading:e.reconciliationLoading},on:{click:e.toggleReconciliationMode}},[e._v(e._s(e.isReconciliationMode?"退出对账":"对账"))])],1),i("div",{staticClass:"toolbar-right"},[i("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1)])]),e.isReconciliationMode?i("div",{ref:"reconciliationNotice",staticClass:"reconciliation-notice",class:{"reconciliation-fixed":e.isReconciliationFixed}},[i("div",{staticClass:"reconciliation-alert"},[e._m(0),i("div",{staticClass:"reconciliation-content"},[e._m(1),i("div",{staticClass:"reconciliation-stats"},[i("span",{staticClass:"stats-text"},[e._v(" 已对账中奖记录数量： "),i("span",{staticClass:"reconciled-count"},[e._v(e._s(e.reconciledWinningIds.length))]),e._v(" / "),i("span",{staticClass:"total-count"},[e._v(e._s(e.winningList.length))])]),i("span",{staticClass:"stats-text",staticStyle:{"margin-left":"20px"}},[e._v(" 已对账中奖金额： "),i("span",{staticClass:"reconciled-amount"},[e._v("￥"+e._s(e.formatAmount(e.reconciledWinningAmount)))]),e._v(" / "),i("span",{staticClass:"total-amount"},[e._v("￥"+e._s(e.formatAmount(e.totalWinningAmount)))])]),e.isReconciliationFixed?i("el-button",{staticClass:"exit-reconciliation-btn",attrs:{type:"danger",size:"mini",icon:"el-icon-close"},on:{click:e.exitReconciliationMode}},[e._v(" 退出对账 ")]):e._e()],1)])])]):e._e(),i("div",{staticClass:"table-container"},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"winningTable",attrs:{data:e.winningList,border:"","header-cell-style":{background:"#f8f9fa",color:"#606266",fontWeight:"bold"}},on:{"selection-change":e.handleSelectionChange,"row-click":e.handleRowClick}},[i("el-table-column",{attrs:{type:"expand",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("div",{staticClass:"expand-content"},[i("div",{staticClass:"expand-header"},[i("i",{staticClass:"el-icon-info"}),i("span",[e._v("详细信息")])]),i("el-form",{staticClass:"table-expand",attrs:{"label-position":"left",inline:""}},[i("el-form-item",{staticClass:"full-width",attrs:{label:"号码识别"}},[i("div",{staticClass:"expand-value"},[e._v(e._s(t.row.shibie||"无"))])]),i("el-form-item",{staticClass:"full-width",attrs:{label:"下注号码"}},[i("div",{staticClass:"expand-value"},[t.row.betNumber?i("div",{staticClass:"number-tags"},[t.row.methodId>=44&&t.row.methodId<=59?i("div",[JSON.parse(t.row.betNumber).numbers.length>0?[i("el-tag",{staticStyle:{margin:"4px","font-size":"16px","font-weight":"bold"},attrs:{type:"danger",effect:"dark",size:"medium"}},[e._v(" 胆码: "+e._s(JSON.parse(t.row.betNumber).numbers[0].danma)+" ")]),i("el-tag",{staticStyle:{margin:"4px","font-size":"16px","font-weight":"bold"},attrs:{type:"info",effect:"dark",size:"medium"}},[e._v(" 拖码: "+e._s(JSON.parse(t.row.betNumber).numbers[0].tuoma)+" ")])]:e._e()],2):t.row.methodId>=60&&t.row.methodId<=69?i("div",[i("el-tag",{staticStyle:{margin:"4px","font-size":"16px","font-weight":"bold"},attrs:{type:"warning",effect:"dark",size:"medium"}},[e._v(" 跨度值: "+e._s(t.row.methodId-60)+" ")])],1):i("div",e._l(JSON.parse(t.row.betNumber).numbers,(function(t,n){return i("el-tag",{key:n,staticClass:"number-tag bet-tag",attrs:{type:"primary",effect:"dark",size:"small"}},[e._v(" "+e._s(Object.values(t).join("."))+" ")])})),1)]):i("span",{staticClass:"no-data"},[e._v(e._s(t.row.betNumbers||"无"))])])]),i("el-form-item",{staticClass:"full-width",attrs:{label:"中奖号码"}},[i("div",{staticClass:"expand-value"},[t.row.winningNumbers?i("div",{staticClass:"number-tags"},["string"===typeof t.row.winningNumbers?e._l(JSON.parse(t.row.winningNumbers).winning,(function(t,n){return i("el-tag",{key:n,staticClass:"number-tag winning-tag",attrs:{type:"success",effect:"dark",size:"small"}},[void 0===t.b&&void 0===t.c?[e._v(" "+e._s(t.a)+" ")]:void 0===t.c?[e._v(" "+e._s(t.a)+"."+e._s(t.b)+" ")]:[e._v(" "+e._s(t.a)+"."+e._s(t.b)+"."+e._s(t.c)+" ")]],2)})):[i("span",{staticClass:"no-data"},[e._v(e._s(t.row.winningNumbers))])]],2):i("span",{staticClass:"no-data"},[e._v(e._s(t.row.winningNumbers||"无"))])])])],1)],1)]}}])}),i("el-table-column",{attrs:{label:"中奖用户",align:"center",prop:"userId","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("div",{staticClass:"user-info"},[i("i",{staticClass:"el-icon-user"}),i("span",[e._v(e._s(e.getUserName(t.row.userId)))])])]}}])}),i("el-table-column",{attrs:{label:"彩种",align:"center",prop:"lotteryId",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("div",{staticClass:"lottery-type"},[1===t.row.lotteryId?i("el-tag",{staticClass:"lottery-tag",attrs:{type:"danger",effect:"dark",size:"small"}},[e._v("福彩")]):2===t.row.lotteryId?i("el-tag",{staticClass:"lottery-tag",attrs:{type:"primary",effect:"dark",size:"small"}},[e._v("体彩")]):i("span",{staticClass:"lottery-unknown"},[e._v(e._s(t.row.lotteryId))])],1)]}}])}),i("el-table-column",{attrs:{label:"玩法名称",align:"center",prop:"methodId","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("div",{staticClass:"method-info"},[i("i",{staticClass:"el-icon-star-on"}),i("span",[e._v(e._s(e.getMethodName(t.row.methodId)))])])]}}])}),i("el-table-column",{attrs:{label:"下注号码",align:"center",prop:"betNumber",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.betNumber?i("div",[t.row.methodId>=44&&t.row.methodId<=59?i("div",{staticStyle:{"white-space":"nowrap"}},[JSON.parse(t.row.betNumber).numbers.length>0?[i("el-tag",{staticStyle:{margin:"1px","font-weight":"bold"},attrs:{type:"danger",effect:"dark",size:"mini"}},[e._v(" 胆"+e._s(JSON.parse(t.row.betNumber).numbers[0].danma)+" ")]),i("el-tag",{staticStyle:{margin:"1px","font-weight":"bold"},attrs:{effect:"dark",size:"mini"}},[e._v(" 拖"+e._s(JSON.parse(t.row.betNumber).numbers[0].tuoma)+" ")])]:e._e()],2):t.row.methodId>=60&&t.row.methodId<=69?i("div",{staticStyle:{"white-space":"nowrap"}},[i("el-tag",{staticStyle:{margin:"1px","font-weight":"bold"},attrs:{effect:"dark",size:"mini"}},[e._v(" 跨度"+e._s(t.row.methodId-60)+" ")])],1):i("div",{staticStyle:{"white-space":"nowrap",overflow:"hidden","text-overflow":"ellipsis"}},e._l(JSON.parse(t.row.betNumber).numbers,(function(t,n){return i("div",{key:n,staticStyle:{display:"inline-block"}},[i("el-tag",{staticStyle:{margin:"2px","font-weight":"bold"},attrs:{type:"primary",effect:"dark",size:"small"}},[e._v(" "+e._s(Object.values(t).join("."))+" ")])],1)})),0)]):i("span",{staticStyle:{"font-size":"14px","font-weight":"bold"}},[e._v(e._s(t.row.betNumbers))])]}}])}),i("el-table-column",{attrs:{label:"中奖号码",align:"center",prop:"winningNumbers",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.winningNumbers?i("div",{staticStyle:{"white-space":"nowrap",overflow:"hidden","text-overflow":"ellipsis"}},["string"===typeof t.row.winningNumbers?e._l(JSON.parse(t.row.winningNumbers).winning,(function(t,n){return i("div",{key:n,staticStyle:{display:"inline-block"}},[i("el-tag",{staticStyle:{margin:"2px","font-weight":"bold"},attrs:{type:"success",effect:"dark",size:"small"}},[void 0===t.b&&void 0===t.c?[e._v(" "+e._s(t.a)+" ")]:void 0===t.c?[e._v(" "+e._s(t.a)+"."+e._s(t.b)+" ")]:[e._v(" "+e._s(t.a)+"."+e._s(t.b)+"."+e._s(t.c)+" ")]],2)],1)})):[i("span",{staticStyle:{"font-size":"14px","font-weight":"bold"}},[e._v(e._s(t.row.winningNumbers))])]],2):i("span",{staticStyle:{"font-size":"14px","font-weight":"bold"}},[e._v(e._s(t.row.winningNumbers))])]}}])}),i("el-table-column",{attrs:{label:"流水号",align:"center",prop:"serialNumber",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("div",{staticClass:"serial-number"},[i("span",{staticClass:"serial-value"},[e._v(e._s(t.row.serialNumber))])])]}}])}),i("el-table-column",{attrs:{label:"中奖金额",align:"center",prop:"winAmount","min-width":"120",sortable:"","sort-method":e.sortByAmount},scopedSlots:e._u([{key:"default",fn:function(t){return[e.isReconciliationMode?i("div",{staticClass:"reconciliation-amount-container",class:{reconciled:e.isWinningReconciled(t.row.winId)},on:{click:function(i){return i.stopPropagation(),e.toggleWinningReconciliation(t.row.winId)}}},[i("span",{staticClass:"reconciliation-amount-text"},[e._v(" 中奖金额: "+e._s(e.formatAmount(t.row.winAmount))+" ")]),i("span",{staticClass:"reconciliation-status-badge",class:{reconciled:e.isWinningReconciled(t.row.winId)}},[e._v(" "+e._s(e.isWinningReconciled(t.row.winId)?"已对账":"待对账")+" ")])]):i("span",{staticClass:"amount-simple"},[e._v(e._s(e.formatAmount(t.row.winAmount)))])]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0&&!e.isReconciliationMode,expression:"total > 0 && !isReconciliationMode"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1)},a=[function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"reconciliation-header"},[i("i",{staticClass:"el-icon-info"}),i("span",{staticClass:"reconciliation-title"},[e._v("对账模式")])])},function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("p",{staticClass:"reconciliation-instruction"},[i("strong",{staticStyle:{color:"#ff4d4f"}},[e._v("操作提示：")]),e._v("点击表格中蓝色虚线框的 "),i("span",{staticClass:"highlight-amount"},[e._v('"中奖金额: ￥xxx.xx"')]),e._v(" 区域即可标记该记录为已对账！ ")])}],s=i("2909"),r=i("5530"),o=(i("99af"),i("4de4"),i("7db0"),i("caad"),i("a15b"),i("d81d"),i("14d9"),i("13d5"),i("4e82"),i("a434"),i("b0c0"),i("e9c4"),i("a9e3"),i("b680"),i("b64b"),i("d3b7"),i("07ac"),i("6062"),i("1e70"),i("79a4"),i("c1a1"),i("8b00"),i("a4e7"),i("1e5a"),i("72c3"),i("2532"),i("3ca3"),i("498a"),i("0643"),i("2382"),i("fffc"),i("4e3e"),i("a573"),i("9d4a"),i("159b"),i("ddb0"),i("b775"));function l(e){return Object(o["a"])({url:"/game/winning/list",method:"get",params:e})}function c(e){return Object(o["a"])({url:"/game/winning/"+e,method:"get"})}function u(e){return Object(o["a"])({url:"/game/winning",method:"post",data:e})}function d(e){return Object(o["a"])({url:"/game/winning",method:"put",data:e})}function m(e){return Object(o["a"])({url:"/game/winning/"+e,method:"delete"})}function h(e){return Object(o["a"])({url:"/game/winning/cancelPay/"+e,method:"put"})}var g=i("0901"),f=i("2f62"),b=(i("c0c7"),i("2449")),p=i("0fad"),w=(i("c38a"),i("f97d")),v={name:"Winning",data:function(){return{loading:!0,exportLoading:!1,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,winningList:[],allExpanded:!1,title:"",open:!1,userList:[],issueList:[],serialNumberList:[],shiBieSearchTimer:null,isReconciliationMode:!1,reconciliationLoading:!1,reconciledWinningIds:[],originalQueryParams:null,isReconciliationFixed:!1,reconciliationOriginalTop:0,scrollDebounceTimer:null,queryParams:{pageNum:1,pageSize:50,userId:null,methodId:null,betNumbers:null,lotteryId:null,issueNumber:null,winAmount:null,isPaid:null,winningNumbers:null,shibie:null,serialNumber:null},form:{},rules:{userId:[{required:!0,message:"中奖用户不能为空",trigger:"blur"}],methodId:[{required:!0,message:"玩法名称不能为空",trigger:"blur"}],lotteryId:[{required:!0,message:"彩种名称不能为空",trigger:"blur"}],issueNumber:[{required:!0,message:"中奖期号不能为空",trigger:"blur"}]}}},computed:Object(r["a"])(Object(r["a"])({},Object(f["c"])({gameMethodsData:function(e){return e.game.methodsData}})),{},{reconciledWinningAmount:function(){var e=this;return this.isReconciliationMode&&this.winningList&&0!==this.winningList.length?this.winningList.filter((function(t){return e.isWinningReconciled(t.winId)})).reduce((function(e,t){var i=parseFloat(t.winAmount)||0;return e+i}),0):0},totalWinningAmount:function(){return this.winningList&&0!==this.winningList.length?this.winningList.reduce((function(e,t){var i=parseFloat(t.winAmount)||0;return e+i}),0):0}}),created:function(){this.getList(),this.getCustomerList(),this.getGameMethods(),this.getIssueList(),this.getSerialNumberList(),this.restoreReconciliationState()},mounted:function(){window.addEventListener("scroll",this.handleScroll),window.addEventListener("clearAllData",this.handleClearAllData)},beforeDestroy:function(){window.removeEventListener("scroll",this.handleScroll),window.removeEventListener("clearAllData",this.handleClearAllData),this.scrollDebounceTimer&&(clearTimeout(this.scrollDebounceTimer),this.scrollDebounceTimer=null)},methods:{getIssueList:function(){var e=this;Object(p["f"])().then((function(t){e.issueList=t.rows}))},getCustomerList:function(){var e=this;Object(g["e"])().then((function(t){e.userList=t.rows}))},getList:function(){var e=this;this.loading=!0;var t=Object(r["a"])({},this.queryParams);this.isReconciliationMode&&(t.pageNum=1,t.pageSize=999999);var i=t.shibie&&""!==t.shibie.trim();i&&(t.shibie=t.shibie.trim(),delete t.shibie),l(t).then((function(t){var n=t.rows;if(i&&n.length>0){var a=e.queryParams.shibie.trim();n=n.filter((function(e){return e.shibie&&e.shibie.includes(a)})),t.total=n.length}e.winningList=n,e.total=t.total,e.isReconciliationMode&&e.sortWinningListForReconciliation(),e.loading=!1,i?e.$nextTick((function(){e.allExpanded=!0,e.winningList.forEach((function(t){e.$refs.winningTable.toggleRowExpansion(t,!0)}))})):e.resetExpandState()}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={winId:null,userId:null,methodId:null,betNumber:null,lotteryId:null,issueNumber:null,winAmount:null,isPaid:null,winningNumbers:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.winId})),this.single=1!==e.length,this.multiple=!e.length},handleRowClick:function(e,t,i){t&&"selection"===t.type||this.isReconciliationMode&&t&&"winAmount"===t.property||(console.log("中奖记录表格行点击:",e.winId||"unknown"),this.$refs.winningTable&&this.$refs.winningTable.toggleRowExpansion(e))},handleAdd:function(){this.reset(),this.open=!0,this.title="添加中奖管理"},handleUpdate:function(e){var t=this;this.reset();var i=e.winId||this.ids;c(i).then((function(e){t.form=e.data,t.open=!0,t.title="修改中奖管理"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.winId?d(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):u(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,i=e.winId||this.ids;this.$modal.confirm('是否确认删除中奖管理编号为"'+i+'"的数据项？').then((function(){return m(i)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){var e=this;this.$modal.confirm("是否确认导出所有中奖管理数据项？").then((function(){e.exportLoading=!0;var t=Object(r["a"])(Object(r["a"])({},e.queryParams),{},{pageNum:1,pageSize:1e4});l(t).then((function(t){var i=t.rows,n=e.formatDataForExport(i);e.exportToExcel(n)})).catch((function(){e.$modal.msgError("导出失败")})).finally((function(){e.exportLoading=!1}))})).catch((function(){}))},formatDataForExport:function(e){var t=this;return e.map((function(e){var i="";if(e.betNumber)try{var n=JSON.parse(e.betNumber);if(n.numbers&&Array.isArray(n.numbers))if(e.methodId>=44&&e.methodId<=59){if(n.numbers.length>0){var a=n.numbers[0];i="胆".concat(a.danma," 拖").concat(a.tuoma)}}else i=e.methodId>=60&&e.methodId<=69?"跨度".concat(e.methodId-60):n.numbers.map((function(e){return Object.values(e).join(".")})).join(", ")}catch(l){i=e.betNumbers||""}else i=e.betNumbers||"";var s="";if(e.winningNumbers)try{if("string"===typeof e.winningNumbers){var r=JSON.parse(e.winningNumbers);r.winning&&Array.isArray(r.winning)&&(s=r.winning.map((function(e){return void 0===e.b&&void 0===e.c?e.a:void 0===e.c?"".concat(e.a,".").concat(e.b):"".concat(e.a,".").concat(e.b,".").concat(e.c)})).join(", "))}else s=e.winningNumbers}catch(l){s=e.winningNumbers||""}var o=function(e){if(!e&&0!==e)return"0.00";var t=parseFloat(e);return t.toFixed(2)};return{"中奖用户":t.getUserName(e.userId),"彩种":t.getLotteryName(e.lotteryId),"玩法名称":t.getMethodName(e.methodId),"下注号码":i,"中奖号码":s,"流水号":e.serialNumber,"中奖金额":o(e.winAmount),"号码识别":e.shibie||""}}))},exportToExcel:function(e){var t=this;Promise.all([i.e("chunk-70ca55ea"),i.e("chunk-6fdfa19d")]).then(i.bind(null,"8530")).then((function(i){var n=["中奖用户","彩种","玩法名称","下注号码","中奖号码","流水号","中奖金额","号码识别"],a=["中奖用户","彩种","玩法名称","下注号码","中奖号码","流水号","中奖金额","号码识别"],s=e.map((function(e){return a.map((function(t){return e[t]}))}));i.export_json_to_excel({header:n,data:s,filename:"中奖管理_".concat(t.parseTime(new Date,"{y}{m}{d}_{h}{i}{s}")),autoWidth:!0,bookType:"xlsx"}),t.$modal.msgSuccess("导出成功")})).catch((function(){t.$modal.msgError("导出失败")}))},getMethodName:function(e){if(!this.gameMethodsData||!this.gameMethodsData.length)return e;var t=this.gameMethodsData.find((function(t){return Number(t.methodId)===Number(e)}));return t?t.methodName:e},getUserName:function(e){if(!this.userList||!this.userList.length)return e;var t=this.userList.find((function(t){return Number(t.userId)===Number(e)}));return t?t.name:e},getLotteryName:function(e){var t={1:"福彩3D",2:"体彩排三"};return t[e]||e},handlePay:function(e){var t=this;d({winId:e.winId,isPaid:1}).then((function(e){t.$modal.msgSuccess("赔付成功"),t.getList()}))},handleBatchPay:function(){var e=this;if(0!==this.ids.length){this.loading=!0;var t=this.ids.map((function(e){return d({winId:e,isPaid:1})}));Promise.all(t).then((function(){e.$modal.msgSuccess("批量赔付成功"),e.getList()})).catch((function(t){e.$modal.msgError("批量赔付失败："+(t.message||"未知错误"))})).finally((function(){e.loading=!1}))}else this.$modal.msgError("请选择要赔付的记录")},formatAmount:function(e){return null===e||void 0===e?"￥0.00":"￥"+parseFloat(e).toFixed(2)},toggleReconciliationMode:function(){this.isReconciliationMode?this.exitReconciliationMode():this.enterReconciliationMode()},enterReconciliationMode:function(){var e=this;this.originalQueryParams=Object(r["a"])({},this.queryParams),this.reconciledWinningIds=[],this.isReconciliationFixed=!1,this.reconciliationOriginalTop=0,this.scrollDebounceTimer&&(clearTimeout(this.scrollDebounceTimer),this.scrollDebounceTimer=null);var t=Object(r["a"])(Object(r["a"])({},this.queryParams),{},{pageNum:1,pageSize:999999});this.loading=!0,l(t).then((function(t){e.winningList=t.rows,e.total=t.rows.length,e.sortWinningListForReconciliation(),e.isReconciliationMode=!0,e.saveReconciliationState(),e.loading=!1,e.reconciliationLoading=!1,e.$modal.msgSuccess("已进入对账模式，共加载 ".concat(t.rows.length," 条中奖记录，按流水号正序排列显示"))})).catch((function(t){e.loading=!1,e.reconciliationLoading=!1,e.$modal.msgError("进入对账模式失败："+t.message)}))},exitReconciliationMode:function(){this.originalQueryParams&&(this.queryParams=Object(r["a"])({},this.originalQueryParams)),this.reconciledWinningIds=[],this.isReconciliationFixed=!1,this.reconciliationOriginalTop=0,this.scrollDebounceTimer&&(clearTimeout(this.scrollDebounceTimer),this.scrollDebounceTimer=null),this.isReconciliationMode=!1,this.clearReconciliationState(),this.getList(),this.$modal.msgSuccess("已退出对账模式")},sortWinningListForReconciliation:function(){var e=this;this.winningList&&0!==this.winningList.length&&(this.winningList.sort((function(e,t){var i=e.serialNumber||0,n=t.serialNumber||0;return i-n})),this.$forceUpdate(),this.$nextTick((function(){e.$refs.winningTable&&e.$refs.winningTable.doLayout()})))},markWinningAsReconciled:function(e){this.reconciledWinningIds.includes(e)||(this.reconciledWinningIds.push(e),this.saveReconciliationState())},unmarkWinningAsReconciled:function(e){var t=this.reconciledWinningIds.indexOf(e);t>-1&&(this.reconciledWinningIds.splice(t,1),this.saveReconciliationState())},toggleWinningReconciliation:function(e){this.isWinningReconciled(e)?this.unmarkWinningAsReconciled(e):this.markWinningAsReconciled(e)},isWinningReconciled:function(e){return this.reconciledWinningIds.includes(e)},saveReconciliationState:function(){try{var e={isReconciliationMode:this.isReconciliationMode,reconciledWinningIds:this.reconciledWinningIds,originalQueryParams:this.originalQueryParams,timestamp:Date.now()};localStorage.setItem("winning_reconciliation_state",JSON.stringify(e))}catch(t){console.error("保存winning对账状态失败:",t)}},restoreReconciliationState:function(){var e=this;try{var t=localStorage.getItem("winning_reconciliation_state");if(t){var i=JSON.parse(t);i.isReconciliationMode&&(this.isReconciliationMode=!0,this.reconciledWinningIds=i.reconciledWinningIds||[],this.originalQueryParams=i.originalQueryParams||null,console.log("恢复winning对账模式状态:",{reconciledCount:this.reconciledWinningIds.length,hasOriginalParams:!!this.originalQueryParams}),this.$nextTick((function(){e.getList()})))}}catch(n){console.error("恢复winning对账状态失败:",n),localStorage.removeItem("winning_reconciliation_state")}},clearReconciliationState:function(){try{localStorage.removeItem("winning_reconciliation_state")}catch(e){console.error("清除winning对账状态失败:",e)}},handleClearAllData:function(e){var t=this;console.log("winning页面收到一键清空事件:",e.detail),this.isReconciliationMode&&setTimeout((function(){t.exitReconciliationMode(),console.log("winning页面因一键清空操作退出对账模式"),t.$message.info("检测到一键清空操作，已自动退出对账模式")}),100)},handleScroll:function(){var e=this;if(this.isReconciliationMode&&this.$refs.reconciliationNotice){var t=this.$refs.reconciliationNotice,i=t.getBoundingClientRect(),n=window.pageYOffset||document.documentElement.scrollTop;0!==this.reconciliationOriginalTop||this.isReconciliationFixed||(this.reconciliationOriginalTop=n+i.top),this.scrollDebounceTimer||(this.scrollDebounceTimer=setTimeout((function(){i.top<=0&&!e.isReconciliationFixed?e.isReconciliationFixed=!0:n<=e.reconciliationOriginalTop-20&&e.isReconciliationFixed&&(e.isReconciliationFixed=!1),e.scrollDebounceTimer=null}),16))}},handleCancelPay:function(e){var t=this;this.$modal.confirm("是否确认撤销该笔奖金的结算？").then((function(){return h(e.winId)})).then((function(){t.getList(),t.$modal.msgSuccess("撤销结算成功")})).catch((function(){}))},getGameMethods:function(){var e=this;Object(b["c"])().then((function(t){e.$store.commit("game/SET_METHODS_DATA",t.rows)}))},getSerialNumberList:function(){var e=this;Object(w["b"])({pageNum:1,pageSize:1e3}).then((function(t){t&&t.rows&&t.rows.length>0?e.serialNumberList=Object(s["a"])(new Set(t.rows.map((function(e){return e.serialNumbers})))).filter((function(e){return null!=e})).sort((function(e,t){return t-e})):(console.log("没有找到流水号数据，尝试从中奖记录获取"),e.getSerialNumberFromWinning())})).catch((function(t){console.error("获取流水号列表失败，尝试从中奖记录获取:",t),e.getSerialNumberFromWinning()}))},getSerialNumberFromWinning:function(){var e=this;l({pageNum:1,pageSize:1e3}).then((function(t){t&&t.rows&&t.rows.length>0&&(e.serialNumberList=Object(s["a"])(new Set(t.rows.map((function(e){return e.serialNumber})))).filter((function(e){return null!=e})).sort((function(e,t){return t-e})),console.log("从中奖记录获取的流水号列表:",e.serialNumberList))})).catch((function(e){console.error("从中奖记录获取流水号失败:",e)}))},toggleExpandAll:function(){var e=this;this.allExpanded=!this.allExpanded,this.$nextTick((function(){e.allExpanded?e.winningList.forEach((function(t,i){e.$refs.winningTable.toggleRowExpansion(t,!0)})):e.winningList.forEach((function(t,i){e.$refs.winningTable.toggleRowExpansion(t,!1)}))}))},resetExpandState:function(){var e=this;this.allExpanded=!1,this.$nextTick((function(){e.winningList.forEach((function(t){e.$refs.winningTable.toggleRowExpansion(t,!1)}))}))},handleShiBieInput:function(){var e=this;this.shiBieSearchTimer&&clearTimeout(this.shiBieSearchTimer),this.shiBieSearchTimer=setTimeout((function(){e.queryParams.shibie&&e.queryParams.shibie.trim()&&e.handleQuery()}),500)},handleShiBieClear:function(){this.queryParams.shibie="",this.handleQuery()},sortByAmount:function(e,t){var i=parseFloat(e.winAmount)||0,n=parseFloat(t.winAmount)||0;return i-n}}},y=v,_=(i("15df"),i("2877")),N=Object(_["a"])(y,n,a,!1,null,"f91ca112",null);t["default"]=N.exports},f97d:function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"a",(function(){return s}));var n=i("b775");function a(e){return Object(n["a"])({url:"/game/serial/list",method:"get",params:e})}function s(){return Object(n["a"])({url:"/game/serial/list",method:"get",params:{pageNum:1,pageSize:1,sortField:"serialNumbers",sortOrder:"desc"}}).then((function(e){return e&&e.length>0?e[0].serialNumbers:e&&e.rows&&e.rows.length>0?e.rows[0].serialNumbers:null}))}}}]);