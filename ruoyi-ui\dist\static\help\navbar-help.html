<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>顶部导航使用说明 - 彩票系统帮助文档</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        .header h1 {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        .back-btn {
            position: absolute;
            left: 30px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-50%) translateY(-2px);
        }
        .content {
            padding: 40px;
        }
        .section-toggle {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 12px;
            cursor: pointer;
            user-select: none;
            margin: 20px 0 10px 0;
            font-size: 18px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        .section-toggle:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        .toggle-icon {
            transition: transform 0.3s ease;
            font-size: 14px;
        }
        .toggle-icon.collapsed {
            transform: rotate(-90deg);
        }
        .section-content {
            max-height: 2000px;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 20px;
            padding: 20px;
        }
        .section-content.collapsed {
            max-height: 0;
            padding: 0 20px;
        }
        .subsection-toggle {
            cursor: pointer;
            user-select: none;
            display: flex;
            align-items: center;
            gap: 8px;
            color: #667eea;
            margin-top: 20px;
            margin-bottom: 10px;
            font-size: 16px;
            font-weight: 500;
        }
        .subsection-toggle:hover {
            color: #5a6fd8;
        }
        .subsection-toggle-icon {
            transition: transform 0.3s ease;
            font-size: 12px;
        }
        .subsection-toggle-icon.collapsed {
            transform: rotate(-90deg);
        }
        .subsection-content {
            max-height: 500px;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        .subsection-content.collapsed {
            max-height: 0;
        }
        .function-item {
            background: white;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            border-left: 4px solid #667eea;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        .function-title {
            font-weight: 600;
            color: #667eea;
            margin-bottom: 8px;
            font-size: 15px;
        }
        .function-desc {
            color: #666;
            margin-bottom: 8px;
            line-height: 1.6;
        }
        .function-usage {
            color: #888;
            font-size: 14px;
            font-style: italic;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }
        .feature-card {
            background: white;
            border-radius: 8px;
            padding: 16px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .tip {
            background: #e8f4fd;
            border: 1px solid #b3d8ff;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            color: #0066cc;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            color: #856404;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            color: #155724;
        }
        .highlight {
            background: #e8f4fd;
            padding: 2px 6px;
            border-radius: 4px;
            color: #0066cc;
            font-weight: 500;
        }
        .expand-all-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
        }
        .expand-all-btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
        }
        .step-list {
            counter-reset: step-counter;
        }
        .step-item {
            counter-increment: step-counter;
            position: relative;
            padding-left: 40px;
            margin-bottom: 16px;
        }
        .step-item::before {
            content: counter(step-counter);
            position: absolute;
            left: 0;
            top: 0;
            width: 24px;
            height: 24px;
            background: #667eea;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            margin: 8px 0;
            overflow-x: auto;
        }
        ul, ol {
            padding-left: 20px;
            margin: 8px 0;
        }
        li {
            margin-bottom: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <button class="back-btn" onclick="goBack()">← 返回</button>
            <h1>🧭 顶部导航使用说明</h1>
            <p>详细介绍顶部导航栏的所有功能和使用方法</p>
        </div>

        <div class="content">
            <div class="tip">
                <strong>页面概述：</strong>顶部导航栏是系统的主要导航区域，提供菜单导航、用户信息、系统设置、快捷操作等核心功能，是用户操作系统的主要入口。
            </div>

            <h2 class="section-toggle" onclick="toggleSection('menu')">
                <span class="toggle-icon collapsed" id="menu-icon">▼</span>
                一、主菜单导航
            </h2>
            <div class="feature-section section-content collapsed" id="menu-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('menu-1-1')">
                    <span class="subsection-toggle-icon collapsed" id="menu-1-1-icon">▼</span>
                    1.1 菜单结构
                </h3>
                <div class="subsection-content collapsed" id="menu-1-1-content">
                    <div class="function-item">
                        <div class="function-title">系统首页</div>
                        <div class="function-desc">返回系统主页面，查看数据概览</div>
                        <div class="function-usage">点击Logo或首页按钮快速返回</div>
                    </div>
                    <div class="feature-grid">
                        <div class="feature-card">
                            <div class="function-title">🎮 游戏管理</div>
                            <div class="function-desc">包含下注记录、开奖管理、中奖管理等功能</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">👥 用户管理</div>
                            <div class="function-desc">玩家信息管理和权限设置</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">⚙️ 系统设置</div>
                            <div class="function-desc">系统配置和参数设置</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">📊 统计报表</div>
                            <div class="function-desc">数据统计和报表分析</div>
                        </div>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('menu-1-2')">
                    <span class="subsection-toggle-icon collapsed" id="menu-1-2-icon">▼</span>
                    1.2 菜单操作
                </h3>
                <div class="subsection-content collapsed" id="menu-1-2-content">
                    <div class="function-item">
                        <div class="function-title">菜单展开收起</div>
                        <div class="function-desc">点击菜单项展开或收起子菜单</div>
                        <div class="function-usage">支持多级菜单结构，便于功能分类管理</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">快速搜索</div>
                        <div class="function-desc">在菜单中快速搜索功能模块</div>
                        <div class="function-usage">输入关键词快速定位目标功能</div>
                    </div>
                </div>
            </div>

            <h2 class="section-toggle" onclick="toggleSection('user')">
                <span class="toggle-icon collapsed" id="user-icon">▼</span>
                二、用户信息区域
            </h2>
            <div class="feature-section section-content collapsed" id="user-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('user-2-1')">
                    <span class="subsection-toggle-icon collapsed" id="user-2-1-icon">▼</span>
                    2.1 用户头像和信息
                </h3>
                <div class="subsection-content collapsed" id="user-2-1-content">
                    <div class="function-item">
                        <div class="function-title">用户头像显示</div>
                        <div class="function-desc">显示当前登录用户的头像</div>
                        <div class="function-usage">点击头像可打开用户菜单</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">用户名显示</div>
                        <div class="function-desc">显示当前登录用户的用户名或昵称</div>
                        <div class="function-usage">便于确认当前登录账户</div>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('user-2-2')">
                    <span class="subsection-toggle-icon collapsed" id="user-2-2-icon">▼</span>
                    2.2 用户下拉菜单
                </h3>
                <div class="subsection-content collapsed" id="user-2-2-content">
                    <div class="function-item">
                        <div class="function-title">个人中心</div>
                        <div class="function-desc">进入个人信息管理页面</div>
                        <div class="function-usage">修改个人资料、查看个人统计等</div>
                    </div>
                    <div class="feature-grid">
                        <div class="feature-card">
                            <div class="function-title">👤 个人资料</div>
                            <div class="function-desc">查看和修改个人信息</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">🔒 修改密码</div>
                            <div class="function-desc">更改登录密码</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">📊 个人统计</div>
                            <div class="function-desc">查看个人投注统计</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">🚪 退出登录</div>
                            <div class="function-desc">安全退出系统</div>
                        </div>
                    </div>
                </div>
            </div>

            <h2 class="section-toggle" onclick="toggleSection('tools')">
                <span class="toggle-icon collapsed" id="tools-icon">▼</span>
                三、工具栏功能
            </h2>
            <div class="feature-section section-content collapsed" id="tools-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('tools-3-1')">
                    <span class="subsection-toggle-icon collapsed" id="tools-3-1-icon">▼</span>
                    3.1 通知消息
                </h3>
                <div class="subsection-content collapsed" id="tools-3-1-content">
                    <div class="function-item">
                        <div class="function-title">消息提醒</div>
                        <div class="function-desc">显示系统通知和消息数量</div>
                        <div class="function-usage">红色数字标识未读消息数量</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">消息列表</div>
                        <div class="function-desc">查看详细的通知消息内容</div>
                        <div class="function-usage">点击消息图标打开消息列表</div>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('tools-3-2')">
                    <span class="subsection-toggle-icon collapsed" id="tools-3-2-icon">▼</span>
                    3.2 系统设置
                </h3>
                <div class="subsection-content collapsed" id="tools-3-2-content">
                    <div class="function-item">
                        <div class="function-title">主题切换</div>
                        <div class="function-desc">切换系统界面主题</div>
                        <div class="function-usage">支持明亮模式和暗黑模式</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">语言设置</div>
                        <div class="function-desc">切换系统显示语言</div>
                        <div class="function-usage">支持中文、英文等多种语言</div>
                    </div>
                </div>
            </div>

            <h2 class="section-toggle" onclick="toggleSection('shortcuts')">
                <span class="toggle-icon collapsed" id="shortcuts-icon">▼</span>
                四、快捷操作
            </h2>
            <div class="feature-section section-content collapsed" id="shortcuts-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('shortcuts-4-1')">
                    <span class="subsection-toggle-icon collapsed" id="shortcuts-4-1-icon">▼</span>
                    4.1 快速投注
                </h3>
                <div class="subsection-content collapsed" id="shortcuts-4-1-content">
                    <div class="function-item">
                        <div class="function-title">投注按钮</div>
                        <div class="function-desc">快速打开投注弹窗</div>
                        <div class="function-usage">无需进入具体页面即可快速投注</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">最新开奖</div>
                        <div class="function-desc">查看最新一期开奖结果</div>
                        <div class="function-usage">实时显示开奖信息</div>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('shortcuts-4-2')">
                    <span class="subsection-toggle-icon collapsed" id="shortcuts-4-2-icon">▼</span>
                    4.2 系统状态
                </h3>
                <div class="subsection-content collapsed" id="shortcuts-4-2-content">
                    <div class="function-item">
                        <div class="function-title">在线状态</div>
                        <div class="function-desc">显示系统在线状态和连接情况</div>
                        <div class="function-usage">绿色表示正常，红色表示异常</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">时间显示</div>
                        <div class="function-desc">显示当前系统时间</div>
                        <div class="function-usage">便于用户了解当前时间</div>
                    </div>
                </div>
            </div>

            <h2 class="section-toggle" onclick="toggleSection('tips')">
                <span class="toggle-icon collapsed" id="tips-icon">▼</span>
                五、使用技巧与注意事项
            </h2>
            <div class="feature-section section-content collapsed" id="tips-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('tips-5-1')">
                    <span class="subsection-toggle-icon collapsed" id="tips-5-1-icon">▼</span>
                    5.1 使用技巧
                </h3>
                <div class="subsection-content collapsed" id="tips-5-1-content">
                    <div class="success">
                        <strong>💡 实用技巧：</strong>
                        <ul>
                            <li>使用菜单搜索功能快速找到目标功能</li>
                            <li>定期查看通知消息，及时了解系统动态</li>
                            <li>利用快捷操作提高工作效率</li>
                            <li>根据使用习惯调整主题和语言设置</li>
                        </ul>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('tips-5-2')">
                    <span class="subsection-toggle-icon collapsed" id="tips-5-2-icon">▼</span>
                    5.2 注意事项
                </h3>
                <div class="subsection-content collapsed" id="tips-5-2-content">
                    <div class="warning">
                        <strong>⚠️ 重要提醒：</strong>
                        <ul>
                            <li>退出登录前请确保已保存所有工作</li>
                            <li>定期查看和处理系统通知消息</li>
                            <li>如发现系统状态异常请及时联系管理员</li>
                            <li>不要在公共场所使用快捷投注功能</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <button class="expand-all-btn" onclick="toggleAllSections()">
        <span id="expandAllText">📖 展开全部</span>
    </button>

    <script>
        // 切换章节显示/隐藏
        function toggleSection(sectionId) {
            const content = document.getElementById(sectionId + '-content');
            const icon = document.getElementById(sectionId + '-icon');
            
            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                icon.classList.remove('collapsed');
            } else {
                content.classList.add('collapsed');
                icon.classList.add('collapsed');
            }
        }

        // 切换子章节显示/隐藏
        function toggleSubsection(subsectionId) {
            const content = document.getElementById(subsectionId + '-content');
            const icon = document.getElementById(subsectionId + '-icon');
            
            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                icon.classList.remove('collapsed');
            } else {
                content.classList.add('collapsed');
                icon.classList.add('collapsed');
            }
        }

        // 切换全部章节和子章节
        function toggleAllSections() {
            const sections = ['menu', 'user', 'tools', 'shortcuts', 'tips'];
            const subsections = [
                'menu-1-1', 'menu-1-2',
                'user-2-1', 'user-2-2',
                'tools-3-1', 'tools-3-2',
                'shortcuts-4-1', 'shortcuts-4-2',
                'tips-5-1', 'tips-5-2'
            ];

            const expandAllText = document.getElementById('expandAllText');
            const allCollapsed = sections.every(sectionId =>
                document.getElementById(sectionId + '-content').classList.contains('collapsed')
            );

            sections.forEach(sectionId => {
                const content = document.getElementById(sectionId + '-content');
                const icon = document.getElementById(sectionId + '-icon');

                if (allCollapsed) {
                    content.classList.remove('collapsed');
                    icon.classList.remove('collapsed');
                } else {
                    content.classList.add('collapsed');
                    icon.classList.add('collapsed');
                }
            });

            subsections.forEach(subsectionId => {
                const content = document.getElementById(subsectionId + '-content');
                const icon = document.getElementById(subsectionId + '-icon');

                if (allCollapsed) {
                    content.classList.remove('collapsed');
                    icon.classList.remove('collapsed');
                } else {
                    content.classList.add('collapsed');
                    icon.classList.add('collapsed');
                }
            });

            expandAllText.textContent = allCollapsed ? '📕 收起全部' : '📖 展开全部';
        }

        // 根据URL参数自动展开指定内容
        function autoExpandFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            const expandTarget = urlParams.get('expand');

            if (expandTarget) {
                // 查找包含目标文本的元素
                const allElements = document.querySelectorAll('.function-title, .subsection-toggle, .section-toggle');

                for (let element of allElements) {
                    const text = element.textContent || element.innerText;
                    if (text.includes(expandTarget)) {
                        // 找到匹配的元素，展开其所在的章节和子章节
                        expandToTarget(element);
                        // 滚动到目标位置
                        setTimeout(() => {
                            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            // 高亮显示目标元素
                            highlightElement(element);
                        }, 300);
                        break;
                    }
                }
            }
        }

        // 展开到目标元素
        function expandToTarget(targetElement) {
            let current = targetElement;

            // 向上查找并展开所有父级章节
            while (current) {
                if (current.classList && current.classList.contains('section-content')) {
                    // 展开章节
                    const sectionId = current.id.replace('-content', '');
                    const icon = document.getElementById(sectionId + '-icon');
                    if (icon && icon.classList.contains('collapsed')) {
                        toggleSection(sectionId);
                    }
                }

                if (current.classList && current.classList.contains('subsection-content')) {
                    // 展开子章节
                    const subsectionId = current.id.replace('-content', '');
                    const icon = document.getElementById(subsectionId + '-icon');
                    if (icon && icon.classList.contains('collapsed')) {
                        toggleSubsection(subsectionId);
                    }
                }

                current = current.parentElement;
            }
        }

        // 高亮显示元素
        function highlightElement(element) {
            element.style.backgroundColor = '#fff3cd';
            element.style.border = '2px solid #ffc107';
            element.style.borderRadius = '4px';
            element.style.padding = '8px';
            element.style.transition = 'all 0.3s ease';

            // 3秒后移除高亮
            setTimeout(() => {
                element.style.backgroundColor = '';
                element.style.border = '';
                element.style.borderRadius = '';
                element.style.padding = '';
            }, 3000);
        }

        // 返回功能
        function goBack() {
            const fromHelp = sessionStorage.getItem('helpFromPage');
            if (fromHelp) {
                sessionStorage.removeItem('helpFromPage');
                window.location.href = fromHelp;
            } else {
                window.location.href = 'index.html';
            }
        }

        // 页面加载时处理URL参数
        document.addEventListener('DOMContentLoaded', function() {
            autoExpandFromUrl();
        });
    </script>
</body>
</html>
