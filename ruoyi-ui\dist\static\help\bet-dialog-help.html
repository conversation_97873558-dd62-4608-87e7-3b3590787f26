<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下注弹窗使用说明 - 彩票系统帮助文档</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        .header h1 {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        .back-btn {
            position: absolute;
            left: 30px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-50%) translateY(-2px);
        }
        .content {
            padding: 40px;
        }
        .section-toggle {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px 20px;
            background: linear-gradient(135deg, #409EFF, #67C23A);
            color: white;
            border-radius: 12px;
            cursor: pointer;
            user-select: none;
            margin: 20px 0 10px 0;
            font-size: 18px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        }
        .section-toggle:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
        }
        .toggle-icon {
            transition: transform 0.3s ease;
            font-size: 14px;
        }
        .toggle-icon.collapsed {
            transform: rotate(-90deg);
        }
        .section-content {
            max-height: 2000px;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 20px;
            padding: 20px;
        }
        .section-content.collapsed {
            max-height: 0;
            padding: 0 20px;
        }
        .subsection-toggle {
            cursor: pointer;
            user-select: none;
            display: flex;
            align-items: center;
            gap: 8px;
            color: #67C23A;
            margin-top: 20px;
            margin-bottom: 10px;
            font-size: 16px;
            font-weight: 500;
        }
        .subsection-toggle:hover {
            color: #529b2e;
        }
        .subsection-toggle-icon {
            transition: transform 0.3s ease;
            font-size: 12px;
        }
        .subsection-toggle-icon.collapsed {
            transform: rotate(-90deg);
        }
        .subsection-content {
            max-height: 500px;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        .subsection-content.collapsed {
            max-height: 0;
        }
        .function-item {
            background: white;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            border-left: 4px solid #409EFF;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        .function-title {
            font-weight: 600;
            color: #409EFF;
            margin-bottom: 8px;
            font-size: 15px;
        }
        .function-desc {
            color: #666;
            margin-bottom: 8px;
            line-height: 1.6;
        }
        .function-usage {
            color: #888;
            font-size: 14px;
            font-style: italic;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }
        .feature-card {
            background: white;
            border-radius: 8px;
            padding: 16px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .tip {
            background: #e8f4fd;
            border: 1px solid #b3d8ff;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            color: #0066cc;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            color: #856404;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            color: #155724;
        }
        .highlight {
            background: #fff3cd;
            padding: 2px 6px;
            border-radius: 4px;
            color: #856404;
            font-weight: 500;
        }
        .expand-all-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, #409EFF, #67C23A);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
        }
        .expand-all-btn:hover {
            background: #337ecc;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
        }
        .step-list {
            counter-reset: step-counter;
        }
        .step-item {
            counter-increment: step-counter;
            position: relative;
            padding-left: 40px;
            margin-bottom: 16px;
        }
        .step-item::before {
            content: counter(step-counter);
            position: absolute;
            left: 0;
            top: 0;
            width: 24px;
            height: 24px;
            background: #409EFF;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            margin: 8px 0;
            overflow-x: auto;
        }
        ul, ol {
            padding-left: 20px;
            margin: 8px 0;
        }
        li {
            margin-bottom: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <button class="back-btn" onclick="goBack()">← 返回</button>
            <h1>📝 下注弹窗使用说明</h1>
            <p>详细介绍下注弹窗的所有功能和使用方法</p>
        </div>

        <div class="content">
            <div class="tip">
                <strong>功能概述：</strong>下注弹窗是彩票系统的核心投注界面，支持福彩3D和体彩排三的投注操作，提供智能号码识别、多玩法选择、批量投注等高级功能。
            </div>

            <h2 class="section-toggle" onclick="toggleSection('basic')">
                <span class="toggle-icon collapsed" id="basic-icon">▼</span>
                一、基础功能区域
            </h2>
            <div class="feature-section section-content collapsed" id="basic-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('basic-1-1')">
                    <span class="subsection-toggle-icon collapsed" id="basic-1-1-icon">▼</span>
                    1.1 福体开关
                </h3>
                <div class="subsection-content collapsed" id="basic-1-1-content">
                    <div class="function-item">
                        <div class="function-title">功能说明</div>
                        <div class="function-desc">控制彩票类型的全局开关，可以选择"全部彩种"或"仅体彩"</div>
                        <div class="function-usage">使用方法：点击开关切换，设置会自动保存到浏览器会话中</div>
                    </div>
                    <div class="feature-grid">
                        <div class="feature-card">
                            <div class="function-title">全部彩种模式</div>
                            <div class="function-desc">支持福彩3D和体彩排三两种彩票类型的投注</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">仅体彩模式</div>
                            <div class="function-desc">只支持体彩排三投注，所有投注组自动设置为体彩</div>
                        </div>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('basic-1-2')">
                    <span class="subsection-toggle-icon collapsed" id="basic-1-2-icon">▼</span>
                    1.2 期号设置
                </h3>
                <div class="subsection-content collapsed" id="basic-1-2-content">
                    <div class="function-item">
                        <div class="function-title">福彩3D期号</div>
                        <div class="function-desc">设置福彩3D的投注期号，支持手动输入</div>
                        <div class="function-usage">使用方法：在输入框中输入期号，格式如"2024001"</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">体彩排三期号</div>
                        <div class="function-desc">设置体彩排三的投注期号，支持手动输入</div>
                        <div class="function-usage">使用方法：在输入框中输入期号，格式如"2024001"</div>
                    </div>
                    <div class="tip">
                        <strong>期号提示：</strong>期号格式通常为年份+期数，如2024年第1期为"2024001"
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('basic-1-3')">
                    <span class="subsection-toggle-icon collapsed" id="basic-1-3-icon">▼</span>
                    1.3 用户选择
                </h3>
                <div class="subsection-content collapsed" id="basic-1-3-content">
                    <div class="function-item">
                        <div class="function-title">当前用户显示</div>
                        <div class="function-desc">显示当前登录用户的姓名，用于确认投注身份</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">用户切换功能</div>
                        <div class="function-usage">使用方法：从下拉列表中选择用户，点击"切换用户"按钮</div>
                    </div>
                </div>
            </div>

            <h2 class="section-toggle" onclick="toggleSection('recognition')">
                <span class="toggle-icon collapsed" id="recognition-icon">▼</span>
                二、号码识别功能
            </h2>
            <div class="feature-section section-content collapsed" id="recognition-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('recognition-2-1')">
                    <span class="subsection-toggle-icon collapsed" id="recognition-2-1-icon">▼</span>
                    2.1 智能号码识别
                </h3>
                <div class="subsection-content collapsed" id="recognition-2-1-content">
                    <div class="function-item">
                        <div class="function-title">功能说明</div>
                        <div class="function-desc">支持自然语言输入投注内容，系统自动识别号码、金额、玩法等信息</div>
                        <div class="function-usage">使用方法：在号码识别框中输入投注内容，系统会自动解析并生成投注组</div>
                    </div>
                    <div class="code-block">
示例输入格式：
123 456 789 十元直选
012 345 678 五元组六
和值15 二十元
大小单双 大单 十元
                    </div>
                    <div class="tip">
                        <strong>识别提示：</strong>系统支持多种输入格式，包括号码+金额+玩法的组合输入
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('recognition-2-2')">
                    <span class="subsection-toggle-icon collapsed" id="recognition-2-2-icon">▼</span>
                    2.2 号码数量限制
                </h3>
                <div class="subsection-content collapsed" id="recognition-2-2-content">
                    <div class="function-item">
                        <div class="function-title">数量监控</div>
                        <div class="function-desc">实时显示当前输入的号码数量，最多支持30个号码</div>
                        <div class="function-usage">显示格式：当前号码数/最大限制数 (如：15/30)</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">超限提醒</div>
                        <div class="function-desc">当号码数量超过30个时，显示红色警告并停止识别处理</div>
                    </div>
                    <div class="warning">
                        <strong>限制说明：</strong>为保证系统性能，单次投注最多支持30个号码，超出部分不会被处理
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('recognition-2-3')">
                    <span class="subsection-toggle-icon collapsed" id="recognition-2-3-icon">▼</span>
                    2.3 格式转换工具
                </h3>
                <div class="subsection-content collapsed" id="recognition-2-3-content">
                    <div class="function-item">
                        <div class="function-title">格式转换按钮</div>
                        <div class="function-desc">提供专门的格式转换工具，支持多种输入格式的转换</div>
                        <div class="function-usage">使用方法：点击"格式转换"按钮打开转换工具弹窗</div>
                    </div>
                    <div class="feature-grid">
                        <div class="feature-card">
                            <div class="function-title">金额格式转换</div>
                            <div class="function-desc">将"333 888二十"格式转换为标准投注格式</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">冒号格式转换</div>
                            <div class="function-desc">处理带冒号分隔的号码格式</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">加号格式转换</div>
                            <div class="function-desc">处理带加号连接的号码格式</div>
                        </div>
                    </div>
                </div>
            </div>

            <h2 class="section-toggle" onclick="toggleSection('betting')">
                <span class="toggle-icon collapsed" id="betting-icon">▼</span>
                三、投注组管理
            </h2>
            <div class="feature-section section-content collapsed" id="betting-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('betting-3-1')">
                    <span class="subsection-toggle-icon collapsed" id="betting-3-1-icon">▼</span>
                    3.1 投注组操作
                </h3>
                <div class="subsection-content collapsed" id="betting-3-1-content">
                    <div class="function-item">
                        <div class="function-title">添加投注组</div>
                        <div class="function-desc">支持添加多个投注组，每个组可以设置不同的玩法和号码</div>
                        <div class="function-usage">使用方法：点击"添加投注组"按钮，系统会自动创建新的投注组</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">删除投注组</div>
                        <div class="function-desc">可以删除不需要的投注组，但至少保留一个投注组</div>
                        <div class="function-usage">使用方法：点击投注组右上角的删除按钮</div>
                    </div>
                    <div class="tip">
                        <strong>组管理提示：</strong>投注组支持无限添加，每个组都有独立的设置和统计
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('betting-3-2')">
                    <span class="subsection-toggle-icon collapsed" id="betting-3-2-icon">▼</span>
                    3.2 玩法选择
                </h3>
                <div class="subsection-content collapsed" id="betting-3-2-content">
                    <div class="function-item">
                        <div class="function-title">玩法下拉选择</div>
                        <div class="function-desc">支持搜索和筛选的玩法选择器，包含所有可用玩法</div>
                        <div class="function-usage">使用方法：点击下拉框，输入关键词搜索或直接选择玩法</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">彩种自动匹配</div>
                        <div class="function-desc">根据福体开关设置，自动匹配对应的彩种玩法</div>
                    </div>
                    <div class="feature-grid">
                        <div class="feature-card">
                            <div class="function-title">直选玩法</div>
                            <div class="function-desc">号码必须按位置完全匹配</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">组选玩法</div>
                            <div class="function-desc">号码不分位置，组合匹配</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">和值玩法</div>
                            <div class="function-desc">根据号码和值进行投注</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">大小单双</div>
                            <div class="function-desc">根据号码属性进行投注</div>
                        </div>
                    </div>
                </div>
            </div>

            <h2 class="section-toggle" onclick="toggleSection('special')">
                <span class="toggle-icon collapsed" id="special-icon">▼</span>
                四、特殊玩法设置
            </h2>
            <div class="feature-section section-content collapsed" id="special-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('special-4-1')">
                    <span class="subsection-toggle-icon collapsed" id="special-4-1-icon">▼</span>
                    4.1 大小单双设置
                </h3>
                <div class="subsection-content collapsed" id="special-4-1-content">
                    <div class="function-item">
                        <div class="function-title">单双选择</div>
                        <div class="function-desc">支持选择单数或双数进行投注</div>
                        <div class="function-usage">使用方法：在单双选项中选择"单"或"双"</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">大小选择</div>
                        <div class="function-desc">支持选择大数或小数进行投注</div>
                        <div class="function-usage">使用方法：在大小选项中选择"大"或"小"</div>
                    </div>
                    <div class="tip">
                        <strong>判断标准：</strong>通常0-4为小，5-9为大；奇数为单，偶数为双
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('special-4-2')">
                    <span class="subsection-toggle-icon collapsed" id="special-4-2-icon">▼</span>
                    4.2 定位玩法
                </h3>
                <div class="subsection-content collapsed" id="special-4-2-content">
                    <div class="function-item">
                        <div class="function-title">定位选择</div>
                        <div class="function-desc">支持选择特定位置进行投注，如百位、十位、个位</div>
                        <div class="function-usage">使用方法：在定位选项中选择要投注的位置</div>
                    </div>
                    <div class="feature-grid">
                        <div class="feature-card">
                            <div class="function-title">百位定位</div>
                            <div class="function-desc">只对百位数字进行投注</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">十位定位</div>
                            <div class="function-desc">只对十位数字进行投注</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">个位定位</div>
                            <div class="function-desc">只对个位数字进行投注</div>
                        </div>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('special-4-3')">
                    <span class="subsection-toggle-icon collapsed" id="special-4-3-icon">▼</span>
                    4.3 和值玩法
                </h3>
                <div class="subsection-content collapsed" id="special-4-3-content">
                    <div class="function-item">
                        <div class="function-title">和值选择</div>
                        <div class="function-desc">根据选择的玩法显示可用的和值选项</div>
                        <div class="function-usage">使用方法：从和值按钮组中选择要投注的和值</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">动态和值范围</div>
                        <div class="function-desc">系统根据不同玩法自动显示对应的和值范围</div>
                    </div>
                    <div class="code-block">
和值范围示例：
直选和值：0-27
组三和值：1-26
组六和值：3-24
                    </div>
                </div>
            </div>

            <h2 class="section-toggle" onclick="toggleSection('amount')">
                <span class="toggle-icon collapsed" id="amount-icon">▼</span>
                五、金额与统计
            </h2>
            <div class="feature-section section-content collapsed" id="amount-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('amount-5-1')">
                    <span class="subsection-toggle-icon collapsed" id="amount-5-1-icon">▼</span>
                    5.1 金额设置
                </h3>
                <div class="subsection-content collapsed" id="amount-5-1-content">
                    <div class="function-item">
                        <div class="function-title">单注金额</div>
                        <div class="function-desc">为每个投注组设置单注投注金额</div>
                        <div class="function-usage">使用方法：在金额输入框中输入数字，支持小数</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">金额验证</div>
                        <div class="function-desc">系统会验证金额格式和范围的合法性</div>
                    </div>
                    <div class="tip">
                        <strong>金额提示：</strong>金额必须大于0，支持最多两位小数
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('amount-5-2')">
                    <span class="subsection-toggle-icon collapsed" id="amount-5-2-icon">▼</span>
                    5.2 实时统计
                </h3>
                <div class="subsection-content collapsed" id="amount-5-2-content">
                    <div class="function-item">
                        <div class="function-title">总金额统计</div>
                        <div class="function-desc">实时计算并显示所有投注组的总投注金额</div>
                        <div class="function-usage">显示位置：弹窗底部的统计区域</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">总注数统计</div>
                        <div class="function-desc">实时计算并显示所有投注组的总注数</div>
                        <div class="function-usage">计算方式：根据号码数量和玩法自动计算</div>
                    </div>
                    <div class="feature-grid">
                        <div class="feature-card">
                            <div class="function-title">💰 总金额</div>
                            <div class="function-desc">蓝色显示，包含货币符号</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">🎯 总注数</div>
                            <div class="function-desc">橙色显示，包含注数单位</div>
                        </div>
                    </div>
                </div>
            </div>

            <h2 class="section-toggle" onclick="toggleSection('operation')">
                <span class="toggle-icon collapsed" id="operation-icon">▼</span>
                六、操作与提交
            </h2>
            <div class="feature-section section-content collapsed" id="operation-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('operation-6-1')">
                    <span class="subsection-toggle-icon collapsed" id="operation-6-1-icon">▼</span>
                    6.1 表单验证
                </h3>
                <div class="subsection-content collapsed" id="operation-6-1-content">
                    <div class="function-item">
                        <div class="function-title">必填项验证</div>
                        <div class="function-desc">系统会验证玩法选择和金额输入是否完整</div>
                        <div class="function-usage">验证时机：点击确定按钮时进行验证</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">数据格式验证</div>
                        <div class="function-desc">验证号码格式、金额格式等数据的合法性</div>
                    </div>
                    <div class="warning">
                        <strong>验证提示：</strong>如果验证失败，系统会显示具体的错误信息
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('operation-6-2')">
                    <span class="subsection-toggle-icon collapsed" id="operation-6-2-icon">▼</span>
                    6.2 提交操作
                </h3>
                <div class="subsection-content collapsed" id="operation-6-2-content">
                    <div class="function-item">
                        <div class="function-title">确定按钮</div>
                        <div class="function-desc">提交投注数据，执行新增或修改操作</div>
                        <div class="function-usage">使用方法：填写完所有信息后点击"确定"按钮</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">取消按钮</div>
                        <div class="function-desc">关闭弹窗，不保存任何修改</div>
                        <div class="function-usage">使用方法：点击"取消"按钮或弹窗右上角的关闭按钮</div>
                    </div>
                    <div class="step-list">
                        <div class="step-item">验证表单数据的完整性和合法性</div>
                        <div class="step-item">格式化投注数据为系统要求的格式</div>
                        <div class="step-item">发送请求到后端API进行处理</div>
                        <div class="step-item">根据响应结果显示成功或错误信息</div>
                        <div class="step-item">成功后刷新列表数据并清空表单</div>
                    </div>
                </div>
            </div>

            <div class="success">
                <strong>使用提示：</strong>下注弹窗支持复杂的投注操作，建议先熟悉基础功能再使用高级特性。如遇问题可联系系统管理员。
            </div>
        </div>
    </div>

    <button class="expand-all-btn" onclick="toggleAllSections()">
        <span id="expandAllText">📖 展开全部</span>
    </button>

    <script>
        let allExpanded = false;
        let allSubsectionsExpanded = false;

        // 切换单个章节
        function toggleSection(sectionId) {
            const content = document.getElementById(sectionId + '-content');
            const icon = document.getElementById(sectionId + '-icon');
            
            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                icon.classList.remove('collapsed');
            } else {
                content.classList.add('collapsed');
                icon.classList.add('collapsed');
            }
        }

        // 切换三级子章节
        function toggleSubsection(subsectionId) {
            const content = document.getElementById(subsectionId + '-content');
            const icon = document.getElementById(subsectionId + '-icon');
            
            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                icon.classList.remove('collapsed');
            } else {
                content.classList.add('collapsed');
                icon.classList.add('collapsed');
            }
        }

        // 切换全部章节和子章节
        function toggleAllSections() {
            const sections = ['basic', 'recognition', 'betting', 'special', 'amount', 'operation'];
            const subsections = [
                // 第一章节：基础功能区域
                'basic-1-1', 'basic-1-2', 'basic-1-3',
                // 第二章节：号码识别功能
                'recognition-2-1', 'recognition-2-2', 'recognition-2-3',
                // 第三章节：投注组管理
                'betting-3-1', 'betting-3-2',
                // 第四章节：特殊玩法设置
                'special-4-1', 'special-4-2', 'special-4-3',
                // 第五章节：金额与统计
                'amount-5-1', 'amount-5-2',
                // 第六章节：操作与提交
                'operation-6-1', 'operation-6-2'
            ];
            const expandAllText = document.getElementById('expandAllText');
            
            allExpanded = !allExpanded;
            allSubsectionsExpanded = allExpanded;
            
            // 切换主章节
            sections.forEach(sectionId => {
                const content = document.getElementById(sectionId + '-content');
                const icon = document.getElementById(sectionId + '-icon');
                
                if (allExpanded) {
                    content.classList.remove('collapsed');
                    icon.classList.remove('collapsed');
                } else {
                    content.classList.add('collapsed');
                    icon.classList.add('collapsed');
                }
            });
            
            // 切换子章节
            subsections.forEach(subsectionId => {
                const content = document.getElementById(subsectionId + '-content');
                const icon = document.getElementById(subsectionId + '-icon');
                
                if (content && icon) {
                    if (allSubsectionsExpanded) {
                        content.classList.remove('collapsed');
                        icon.classList.remove('collapsed');
                    } else {
                        content.classList.add('collapsed');
                        icon.classList.add('collapsed');
                    }
                }
            });
            
            if (allExpanded) {
                expandAllText.textContent = '📕 收起全部';
            } else {
                expandAllText.textContent = '📖 展开全部';
            }
        }

        // 根据URL参数自动展开指定内容
        function autoExpandFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            const expandTarget = urlParams.get('expand');

            if (expandTarget) {
                // 查找包含目标文本的元素
                const allElements = document.querySelectorAll('.function-title, .subsection-toggle, .section-toggle');

                for (let element of allElements) {
                    const text = element.textContent || element.innerText;
                    if (text.includes(expandTarget)) {
                        // 找到匹配的元素，展开其所在的章节和子章节
                        expandToTarget(element);
                        // 滚动到目标位置
                        setTimeout(() => {
                            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            // 高亮显示目标元素
                            highlightElement(element);
                        }, 300);
                        break;
                    }
                }
            }
        }

        // 展开到目标元素
        function expandToTarget(targetElement) {
            let current = targetElement;

            // 向上查找并展开所有父级章节
            while (current) {
                if (current.classList && current.classList.contains('section-content')) {
                    // 展开章节
                    const sectionId = current.id.replace('-content', '');
                    const icon = document.getElementById(sectionId + '-icon');
                    if (icon && icon.classList.contains('collapsed')) {
                        toggleSection(sectionId);
                    }
                }

                if (current.classList && current.classList.contains('subsection-content')) {
                    // 展开子章节
                    const subsectionId = current.id.replace('-content', '');
                    const icon = document.getElementById(subsectionId + '-icon');
                    if (icon && icon.classList.contains('collapsed')) {
                        toggleSubsection(subsectionId);
                    }
                }

                current = current.parentElement;
            }
        }

        // 高亮显示元素
        function highlightElement(element) {
            element.style.backgroundColor = '#fff3cd';
            element.style.border = '2px solid #ffc107';
            element.style.borderRadius = '4px';
            element.style.padding = '8px';
            element.style.transition = 'all 0.3s ease';

            // 3秒后移除高亮
            setTimeout(() => {
                element.style.backgroundColor = '';
                element.style.border = '';
                element.style.borderRadius = '';
                element.style.padding = '';
            }, 3000);
        }

        // 返回功能
        function goBack() {
            const fromHelp = sessionStorage.getItem('helpFromPage');
            if (fromHelp) {
                sessionStorage.removeItem('helpFromPage');
                window.location.href = fromHelp;
            } else {
                window.location.href = 'index.html';
            }
        }

        // 页面加载时处理URL参数
        document.addEventListener('DOMContentLoaded', function() {
            autoExpandFromUrl();
        });
    </script>
</body>
</html>
