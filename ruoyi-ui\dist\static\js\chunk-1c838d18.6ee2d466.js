(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1c838d18"],{dd7b:function(e,t,s){"use strict";s.r(t);var a=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"login"},[e._m(0),e._m(1),s("div",{staticClass:"login-right"},[s("div",{staticClass:"login-container"},[e._m(2),s("el-form",{ref:"loginForm",staticClass:"login-form",attrs:{model:e.loginForm,rules:e.loginRules}},[s("el-form-item",{attrs:{prop:"username"}},[s("div",{staticClass:"input-wrapper"},[s("el-input",{staticClass:"login-input",attrs:{type:"text","auto-complete":"off",placeholder:"请输入账号"},model:{value:e.loginForm.username,callback:function(t){e.$set(e.loginForm,"username",t)},expression:"loginForm.username"}},[s("i",{staticClass:"el-icon-user input-icon",attrs:{slot:"prefix"},slot:"prefix"})])],1)]),s("el-form-item",{attrs:{prop:"password"}},[s("div",{staticClass:"input-wrapper"},[s("el-input",{staticClass:"login-input",attrs:{type:"password","auto-complete":"off",placeholder:"请输入密码"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleLogin(t)}},model:{value:e.loginForm.password,callback:function(t){e.$set(e.loginForm,"password",t)},expression:"loginForm.password"}},[s("i",{staticClass:"el-icon-lock input-icon",attrs:{slot:"prefix"},slot:"prefix"})])],1)]),e.captchaEnabled?s("el-form-item",{attrs:{prop:"code"}},[s("div",{staticClass:"captcha-wrapper"},[s("div",{staticClass:"input-wrapper captcha-input"},[s("el-input",{staticClass:"login-input",attrs:{"auto-complete":"off",placeholder:"验证码"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleLogin(t)}},model:{value:e.loginForm.code,callback:function(t){e.$set(e.loginForm,"code",t)},expression:"loginForm.code"}},[s("i",{staticClass:"el-icon-picture input-icon",attrs:{slot:"prefix"},slot:"prefix"})])],1),s("div",{staticClass:"captcha-image",on:{click:e.getCode}},[s("img",{staticClass:"login-code-img",attrs:{src:e.codeUrl}}),s("div",{staticClass:"captcha-refresh"},[s("i",{staticClass:"el-icon-refresh"})])])])]):e._e(),s("div",{staticClass:"login-options"},[s("el-checkbox",{staticClass:"remember-checkbox",model:{value:e.loginForm.rememberMe,callback:function(t){e.$set(e.loginForm,"rememberMe",t)},expression:"loginForm.rememberMe"}},[e._v(" 记住密码 ")])],1),s("el-form-item",{staticClass:"login-button-item"},[s("el-button",{staticClass:"login-button",attrs:{loading:e.loading,type:"primary"},nativeOn:{click:function(t){return t.preventDefault(),e.handleLogin(t)}}},[e.loading?s("span",[s("i",{staticClass:"el-icon-loading"}),e._v(" 登录中... ")]):s("span",[s("i",{staticClass:"el-icon-right"}),e._v(" 立即登录 ")])])],1)],1),e._m(3)],1)])])},i=[function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"login-background"},[s("div",{staticClass:"bg-shape shape-1"}),s("div",{staticClass:"bg-shape shape-2"}),s("div",{staticClass:"bg-shape shape-3"})])},function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"login-left"},[s("div",{staticClass:"welcome-content"},[s("div",{staticClass:"logo-section"},[s("div",{staticClass:"logo-icon"},[s("i",{staticClass:"el-icon-data-analysis"})]),s("h1",{staticClass:"system-name"},[e._v("游戏管理系统")])]),s("div",{staticClass:"welcome-text"},[s("h2",[e._v("欢迎回来")]),s("p",[e._v("专业数据管理平台")]),s("div",{staticClass:"feature-list"},[s("div",{staticClass:"feature-item"},[s("i",{staticClass:"el-icon-check"}),s("span",[e._v("安全可靠的数据管理")])]),s("div",{staticClass:"feature-item"},[s("i",{staticClass:"el-icon-check"}),s("span",[e._v("实时的处理数据")])]),s("div",{staticClass:"feature-item"},[s("i",{staticClass:"el-icon-check"}),s("span",[e._v("完善的权限控制")])])])])])])},function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"login-header"},[s("h3",{staticClass:"login-title"},[e._v("用户登录")]),s("p",{staticClass:"login-subtitle"},[e._v("请输入您的账号和密码")])])},function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"login-footer"},[s("p",{staticClass:"copyright"},[e._v("© 2024 管理系统 版权所有")])])}],o=(s("caad"),s("14d9"),s("2532"),s("7ded")),n=s("852e"),r=s.n(n),l=s("24e5"),c=s.n(l),d="MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdH\nnzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ==",u="MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAqhHyZfSsYourNxaY\n7Nt+PrgrxkiA50efORdI5U5lsW79MmFnusUA355oaSXcLhu5xxB38SMSyP2KvuKN\nPuH3owIDAQABAkAfoiLyL+Z4lf4Myxk6xUDgLaWGximj20CUf+5BKKnlrK+Ed8gA\nkM0HqoTt2UZwA5E2MzS4EI2gjfQhz5X28uqxAiEA3wNFxfrCZlSZHb0gn2zDpWow\ncSxQAgiCstxGUoOqlW8CIQDDOerGKH5OmCJ4Z21v+F25WaHYPxCFMvwxpcw99Ecv\nDQIgIdhDTIqD2jfYjPTY8Jj3EDGPbH2HHuffvflECt3Ek60CIQCFRlCkHpi7hthh\nYhovyloRYsM+IS9h/0BzlEAuO0ktMQIgSPT3aFAgJYwKpqRYKlLDVcflZFCKY7u3\nUP8iWi1Qw0Y=";function m(e){var t=new c.a;return t.setPublicKey(d),t.encrypt(e)}function g(e){var t=new c.a;return t.setPrivateKey(u),t.decrypt(e)}var p={name:"Login",data:function(){return{title:"游戏管理系统",codeUrl:"",loginForm:{username:"",password:"",rememberMe:!1,code:"",uuid:""},loginRules:{username:[{required:!0,trigger:"blur",message:"请输入您的账号"}],password:[{required:!0,trigger:"blur",message:"请输入您的密码"}],code:[{required:!0,trigger:"change",message:"请输入验证码"}]},loading:!1,captchaEnabled:!0,register:!1,redirect:void 0}},watch:{$route:{handler:function(e){this.redirect=e.query&&e.query.redirect},immediate:!0}},created:function(){this.getCode(),this.getCookie()},methods:{getCode:function(){var e=this;Object(o["a"])().then((function(t){e.captchaEnabled=void 0===t.captchaEnabled||t.captchaEnabled,e.captchaEnabled&&(e.codeUrl="data:image/gif;base64,"+t.img,e.loginForm.uuid=t.uuid)}))},getCookie:function(){var e=r.a.get("username"),t=r.a.get("password"),s=r.a.get("rememberMe");this.loginForm={username:void 0===e?this.loginForm.username:e,password:void 0===t?this.loginForm.password:g(t),rememberMe:void 0!==s&&Boolean(s)}},handleLogin:function(){var e=this;this.$refs.loginForm.validate((function(t){t&&(e.loading=!0,e.loginForm.rememberMe?(r.a.set("username",e.loginForm.username,{expires:30}),r.a.set("password",m(e.loginForm.password),{expires:30}),r.a.set("rememberMe",e.loginForm.rememberMe,{expires:30})):(r.a.remove("username"),r.a.remove("password"),r.a.remove("rememberMe")),e.$store.dispatch("Login",e.loginForm).then((function(){var t=e.$store.state.user.token;t&&t.defaultLotteryId&&t.defaultMethodId&&(sessionStorage.setItem("defaultLotteryId",t.defaultLotteryId),sessionStorage.setItem("defaultMethodId",t.defaultMethodId),console.log("Stored defaultLotteryId:",t.defaultLotteryId),console.log("Stored defaultMethodId:",t.defaultMethodId)),e.$router.push({path:e.redirect||"/"}).catch((function(){}))})).catch((function(t){e.loading=!1,e.captchaEnabled&&e.getCode(),t&&t.msg&&t.msg.includes("授权到期请联系客服")&&e.$alert("您的账户授权已到期，请联系客服续费后再登录。","授权到期提醒",{confirmButtonText:"确定",type:"warning",showClose:!1,closeOnClickModal:!1,closeOnPressEscape:!1})})))}))}}},f=p,h=(s("eb37"),s("2877")),v=Object(h["a"])(f,a,i,!1,null,null,null);t["default"]=v.exports},df92:function(e,t,s){},eb37:function(e,t,s){"use strict";s("df92")}}]);