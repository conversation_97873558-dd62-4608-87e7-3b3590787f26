{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\layout\\components\\Navbar.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\layout\\components\\Navbar.vue", "mtime": 1755765016708}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\babel.config.js", "mtime": 1750852368688}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750942928175}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750942928768}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}