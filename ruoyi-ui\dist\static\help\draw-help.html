<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开奖管理使用说明 - 彩票系统帮助文档</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #E6A23C 0%, #F56C6C 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #E6A23C 0%, #F56C6C 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        .header h1 {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        .back-btn {
            position: absolute;
            left: 30px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-50%) translateY(-2px);
        }
        .content {
            padding: 40px;
        }
        .section-toggle {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px 20px;
            background: linear-gradient(135deg, #E6A23C, #F56C6C);
            color: white;
            border-radius: 12px;
            cursor: pointer;
            user-select: none;
            margin: 20px 0 10px 0;
            font-size: 18px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(230, 162, 60, 0.3);
        }
        .section-toggle:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(230, 162, 60, 0.4);
        }
        .toggle-icon {
            transition: transform 0.3s ease;
            font-size: 14px;
        }
        .toggle-icon.collapsed {
            transform: rotate(-90deg);
        }
        .section-content {
            max-height: 2000px;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 20px;
            padding: 20px;
        }
        .section-content.collapsed {
            max-height: 0;
            padding: 0 20px;
        }
        .subsection-toggle {
            cursor: pointer;
            user-select: none;
            display: flex;
            align-items: center;
            gap: 8px;
            color: #F56C6C;
            margin-top: 20px;
            margin-bottom: 10px;
            font-size: 16px;
            font-weight: 500;
        }
        .subsection-toggle:hover {
            color: #e6453c;
        }
        .subsection-toggle-icon {
            transition: transform 0.3s ease;
            font-size: 12px;
        }
        .subsection-toggle-icon.collapsed {
            transform: rotate(-90deg);
        }
        .subsection-content {
            max-height: 500px;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        .subsection-content.collapsed {
            max-height: 0;
        }
        .function-item {
            background: white;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            border-left: 4px solid #E6A23C;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        .function-title {
            font-weight: 600;
            color: #E6A23C;
            margin-bottom: 8px;
            font-size: 15px;
        }
        .function-desc {
            color: #666;
            margin-bottom: 8px;
            line-height: 1.6;
        }
        .function-usage {
            color: #888;
            font-size: 14px;
            font-style: italic;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }
        .feature-card {
            background: white;
            border-radius: 8px;
            padding: 16px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .tip {
            background: #fff7e6;
            border: 1px solid #ffd591;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            color: #d48806;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            color: #856404;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
            color: #155724;
        }
        .highlight {
            background: #fff3cd;
            padding: 2px 6px;
            border-radius: 4px;
            color: #856404;
            font-weight: 500;
        }
        .expand-all-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, #E6A23C, #F56C6C);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(230, 162, 60, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
        }
        .expand-all-btn:hover {
            background: #d48806;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(230, 162, 60, 0.4);
        }
        .step-list {
            counter-reset: step-counter;
        }
        .step-item {
            counter-increment: step-counter;
            position: relative;
            padding-left: 40px;
            margin-bottom: 16px;
        }
        .step-item::before {
            content: counter(step-counter);
            position: absolute;
            left: 0;
            top: 0;
            width: 24px;
            height: 24px;
            background: #E6A23C;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            margin: 8px 0;
            overflow-x: auto;
        }
        ul, ol {
            padding-left: 20px;
            margin: 8px 0;
        }
        li {
            margin-bottom: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <button class="back-btn" onclick="goBack()">← 返回</button>
            <h1>🎯 开奖管理使用说明</h1>
            <p>详细介绍开奖管理页面的所有功能和使用方法</p>
        </div>

        <div class="content">
            <div class="tip">
                <strong>页面概述：</strong>开奖管理页面用于管理彩票开奖信息，包括开奖号码录入、开奖结果查询、中奖计算等功能。普通用户可以查看开奖记录、录入开奖号码和导出数据。
            </div>

            <h2 class="section-toggle" onclick="toggleSection('search')">
                <span class="toggle-icon collapsed" id="search-icon">▼</span>
                一、搜索功能区域
            </h2>
            <div class="feature-section section-content collapsed" id="search-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('search-1-1')">
                    <span class="subsection-toggle-icon collapsed" id="search-1-1-icon">▼</span>
                    1.1 开奖期号搜索
                </h3>
                <div class="subsection-content collapsed" id="search-1-1-content">
                    <div class="function-item">
                        <div class="function-title">期号输入框</div>
                        <div class="function-desc">支持输入具体的开奖期号进行精确搜索</div>
                        <div class="function-usage">使用方法：在搜索框中输入期号，按回车或点击搜索按钮</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">快速清空</div>
                        <div class="function-desc">点击输入框右侧的清空按钮可快速清除搜索条件</div>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('search-1-2')">
                    <span class="subsection-toggle-icon collapsed" id="search-1-2-icon">▼</span>
                    1.2 彩种筛选
                </h3>
                <div class="subsection-content collapsed" id="search-1-2-content">
                    <div class="function-item">
                        <div class="function-title">彩种选择</div>
                        <div class="function-desc">支持按彩种类型筛选开奖记录</div>
                        <div class="function-usage">可选择：福彩3D、体彩排三</div>
                    </div>
                    <div class="feature-grid">
                        <div class="feature-card">
                            <div class="function-title">🎲 福彩3D</div>
                            <div class="function-desc">福利彩票3D游戏开奖记录</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">🎯 体彩排三</div>
                            <div class="function-desc">体育彩票排列三游戏开奖记录</div>
                        </div>
                    </div>
                </div>
            </div>

            <h2 class="section-toggle" onclick="toggleSection('toolbar')">
                <span class="toggle-icon collapsed" id="toolbar-icon">▼</span>
                二、工具栏功能
            </h2>
            <div class="feature-section section-content collapsed" id="toolbar-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('toolbar-2-1')">
                    <span class="subsection-toggle-icon collapsed" id="toolbar-2-1-icon">▼</span>
                    2.1 新增开奖
                </h3>
                <div class="subsection-content collapsed" id="toolbar-2-1-content">
                    <div class="function-item">
                        <div class="function-title">新增开奖按钮</div>
                        <div class="function-desc">点击可打开新增开奖弹窗，录入新的开奖信息</div>
                        <div class="function-usage">使用方法：点击"新增开奖"按钮，填写开奖信息后提交</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">开奖信息录入</div>
                        <div class="function-desc">需要填写彩种、期号、开奖号码等基本信息</div>
                    </div>
                    <div class="step-list">
                        <div class="step-item">选择彩种类型（福彩3D或体彩排三）</div>
                        <div class="step-item">输入开奖期号</div>
                        <div class="step-item">录入开奖号码（百位、十位、个位）</div>
                        <div class="step-item">系统自动计算相关属性（单双、大小、和值等）</div>
                        <div class="step-item">确认信息无误后提交保存</div>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('toolbar-2-2')">
                    <span class="subsection-toggle-icon collapsed" id="toolbar-2-2-icon">▼</span>
                    2.2 批量操作
                </h3>
                <div class="subsection-content collapsed" id="toolbar-2-2-content">
                    <div class="function-item">
                        <div class="function-title">批量删除</div>
                        <div class="function-desc">选择多条开奖记录后可进行批量删除操作</div>
                        <div class="function-usage">使用方法：勾选要删除的记录，点击"批量删除"按钮</div>
                    </div>
                    <div class="warning">
                        <strong>注意：</strong>批量删除操作不可恢复，请谨慎操作
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('toolbar-2-3')">
                    <span class="subsection-toggle-icon collapsed" id="toolbar-2-3-icon">▼</span>
                    2.3 数据导出
                </h3>
                <div class="subsection-content collapsed" id="toolbar-2-3-content">
                    <div class="function-item">
                        <div class="function-title">导出Excel</div>
                        <div class="function-desc">将当前筛选条件下的开奖数据导出为Excel文件</div>
                        <div class="function-usage">使用方法：设置筛选条件后点击"导出数据"按钮</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">导出内容</div>
                        <div class="function-desc">包含开奖期号、彩种、开奖号码、开奖时间等信息</div>
                    </div>
                </div>
            </div>

            <h2 class="section-toggle" onclick="toggleSection('table')">
                <span class="toggle-icon collapsed" id="table-icon">▼</span>
                三、数据表格功能
            </h2>
            <div class="feature-section section-content collapsed" id="table-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('table-3-1')">
                    <span class="subsection-toggle-icon collapsed" id="table-3-1-icon">▼</span>
                    3.1 表格显示
                </h3>
                <div class="subsection-content collapsed" id="table-3-1-content">
                    <div class="function-item">
                        <div class="function-title">开奖信息展示</div>
                        <div class="function-desc">表格显示开奖期号、彩种、开奖号码、开奖时间等详细信息</div>
                    </div>
                    <div class="feature-grid">
                        <div class="feature-card">
                            <div class="function-title">📅 开奖期号</div>
                            <div class="function-desc">显示具体的开奖期数</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">🎲 开奖号码</div>
                            <div class="function-desc">显示百位、十位、个位号码</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">⏰ 开奖时间</div>
                            <div class="function-desc">显示开奖的具体时间</div>
                        </div>
                        <div class="feature-card">
                            <div class="function-title">📊 统计信息</div>
                            <div class="function-desc">显示单双、大小、和值等统计</div>
                        </div>
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('table-3-2')">
                    <span class="subsection-toggle-icon collapsed" id="table-3-2-icon">▼</span>
                    3.2 操作功能
                </h3>
                <div class="subsection-content collapsed" id="table-3-2-content">
                    <div class="function-item">
                        <div class="function-title">编辑开奖</div>
                        <div class="function-desc">点击编辑按钮可修改开奖信息</div>
                        <div class="function-usage">使用方法：点击操作列的"编辑"按钮</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">删除开奖</div>
                        <div class="function-desc">删除单条开奖记录</div>
                        <div class="function-usage">使用方法：点击操作列的"删除"按钮</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">多选功能</div>
                        <div class="function-desc">通过复选框选择多条记录进行批量操作</div>
                    </div>
                </div>
            </div>

            <h2 class="section-toggle" onclick="toggleSection('tips')">
                <span class="toggle-icon collapsed" id="tips-icon">▼</span>
                四、使用技巧与注意事项
            </h2>
            <div class="feature-section section-content collapsed" id="tips-content">
                <h3 class="subsection-toggle" onclick="toggleSubsection('tips-4-1')">
                    <span class="subsection-toggle-icon collapsed" id="tips-4-1-icon">▼</span>
                    4.1 开奖录入技巧
                </h3>
                <div class="subsection-content collapsed" id="tips-4-1-content">
                    <div class="function-item">
                        <div class="function-title">号码格式</div>
                        <div class="function-desc">开奖号码必须是0-9的数字，每位一个数字</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">自动计算</div>
                        <div class="function-desc">录入号码后系统会自动计算单双、大小、和值、对子、豹子等属性</div>
                    </div>
                    <div class="code-block">
开奖号码示例：
百位：3，十位：5，个位：7
系统自动生成：357
单双：单，大小：大，和值：15
                    </div>
                </div>

                <h3 class="subsection-toggle" onclick="toggleSubsection('tips-4-2')">
                    <span class="subsection-toggle-icon collapsed" id="tips-4-2-icon">▼</span>
                    4.2 注意事项
                </h3>
                <div class="subsection-content collapsed" id="tips-4-2-content">
                    <div class="function-item">
                        <div class="function-title">期号唯一性</div>
                        <div class="function-desc">同一彩种的期号不能重复，系统会进行验证</div>
                    </div>
                    <div class="function-item">
                        <div class="function-title">数据准确性</div>
                        <div class="function-desc">开奖号码一旦录入并结算，修改会影响中奖计算，请谨慎操作</div>
                    </div>
                    <div class="warning">
                        <strong>重要提醒：</strong>开奖信息录入后请仔细核对，确保准确无误
                    </div>
                </div>
            </div>

            <div class="success">
                <strong>使用提示：</strong>开奖管理是彩票系统的核心功能，建议在录入开奖信息时仔细核对，确保数据准确性。如遇问题可联系系统管理员。
            </div>
        </div>
    </div>

    <button class="expand-all-btn" onclick="toggleAllSections()">
        <span id="expandAllText">📖 展开全部</span>
    </button>

    <script>
        // 切换章节显示/隐藏
        function toggleSection(sectionId) {
            const content = document.getElementById(sectionId + '-content');
            const icon = document.getElementById(sectionId + '-icon');
            
            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                icon.classList.remove('collapsed');
            } else {
                content.classList.add('collapsed');
                icon.classList.add('collapsed');
            }
        }

        // 切换子章节显示/隐藏
        function toggleSubsection(subsectionId) {
            const content = document.getElementById(subsectionId + '-content');
            const icon = document.getElementById(subsectionId + '-icon');
            
            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                icon.classList.remove('collapsed');
            } else {
                content.classList.add('collapsed');
                icon.classList.add('collapsed');
            }
        }

        // 切换全部章节和子章节
        function toggleAllSections() {
            const sections = ['search', 'toolbar', 'table', 'tips'];
            const subsections = [
                // 第一章节：搜索功能区域
                'search-1-1', 'search-1-2',
                // 第二章节：工具栏功能
                'toolbar-2-1', 'toolbar-2-2', 'toolbar-2-3',
                // 第三章节：数据表格功能
                'table-3-1', 'table-3-2',
                // 第四章节：使用技巧与注意事项
                'tips-4-1', 'tips-4-2'
            ];

            const expandAllText = document.getElementById('expandAllText');
            const allCollapsed = sections.every(sectionId => 
                document.getElementById(sectionId + '-content').classList.contains('collapsed')
            );

            sections.forEach(sectionId => {
                const content = document.getElementById(sectionId + '-content');
                const icon = document.getElementById(sectionId + '-icon');
                
                if (allCollapsed) {
                    content.classList.remove('collapsed');
                    icon.classList.remove('collapsed');
                } else {
                    content.classList.add('collapsed');
                    icon.classList.add('collapsed');
                }
            });

            subsections.forEach(subsectionId => {
                const content = document.getElementById(subsectionId + '-content');
                const icon = document.getElementById(subsectionId + '-icon');
                
                if (allCollapsed) {
                    content.classList.remove('collapsed');
                    icon.classList.remove('collapsed');
                } else {
                    content.classList.add('collapsed');
                    icon.classList.add('collapsed');
                }
            });

            expandAllText.textContent = allCollapsed ? '📕 收起全部' : '📖 展开全部';
        }

        // 根据URL参数自动展开指定内容
        function autoExpandFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            const expandTarget = urlParams.get('expand');

            if (expandTarget) {
                // 查找包含目标文本的元素
                const allElements = document.querySelectorAll('.function-title, .subsection-toggle, .section-toggle');

                for (let element of allElements) {
                    const text = element.textContent || element.innerText;
                    if (text.includes(expandTarget)) {
                        // 找到匹配的元素，展开其所在的章节和子章节
                        expandToTarget(element);
                        // 滚动到目标位置
                        setTimeout(() => {
                            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            // 高亮显示目标元素
                            highlightElement(element);
                        }, 300);
                        break;
                    }
                }
            }
        }

        // 展开到目标元素
        function expandToTarget(targetElement) {
            let current = targetElement;

            // 向上查找并展开所有父级章节
            while (current) {
                if (current.classList && current.classList.contains('section-content')) {
                    // 展开章节
                    const sectionId = current.id.replace('-content', '');
                    const icon = document.getElementById(sectionId + '-icon');
                    if (icon && icon.classList.contains('collapsed')) {
                        toggleSection(sectionId);
                    }
                }

                if (current.classList && current.classList.contains('subsection-content')) {
                    // 展开子章节
                    const subsectionId = current.id.replace('-content', '');
                    const icon = document.getElementById(subsectionId + '-icon');
                    if (icon && icon.classList.contains('collapsed')) {
                        toggleSubsection(subsectionId);
                    }
                }

                current = current.parentElement;
            }
        }

        // 高亮显示元素
        function highlightElement(element) {
            element.style.backgroundColor = '#fff3cd';
            element.style.border = '2px solid #ffc107';
            element.style.borderRadius = '4px';
            element.style.padding = '8px';
            element.style.transition = 'all 0.3s ease';

            // 3秒后移除高亮
            setTimeout(() => {
                element.style.backgroundColor = '';
                element.style.border = '';
                element.style.borderRadius = '';
                element.style.padding = '';
            }, 3000);
        }

        // 返回功能
        function goBack() {
            const fromHelp = sessionStorage.getItem('helpFromPage');
            if (fromHelp) {
                sessionStorage.removeItem('helpFromPage');
                window.location.href = fromHelp;
            } else {
                window.location.href = 'index.html';
            }
        }

        // 页面加载时处理URL参数
        document.addEventListener('DOMContentLoaded', function() {
            autoExpandFromUrl();
        });
    </script>
</body>
</html>
