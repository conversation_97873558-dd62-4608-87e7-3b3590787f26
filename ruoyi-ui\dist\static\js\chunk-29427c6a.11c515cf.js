(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-29427c6a"],{"063e":function(t,e,a){"use strict";a("ee59")},"0901":function(t,e,a){"use strict";a.d(e,"e",(function(){return n})),a.d(e,"d",(function(){return o})),a.d(e,"a",(function(){return l})),a.d(e,"f",(function(){return i})),a.d(e,"c",(function(){return r})),a.d(e,"b",(function(){return c}));var s=a("b775");function n(t){return Object(s["a"])({url:"/game/customer/list",method:"get",params:t})}function o(t){return Object(s["a"])({url:"/game/customer/"+t,method:"get"})}function l(t){return Object(s["a"])({url:"/game/customer",method:"post",data:t})}function i(t){return Object(s["a"])({url:"/game/customer",method:"put",data:t})}function r(t){return Object(s["a"])({url:"/game/customer/"+t,method:"delete"})}function c(){return Object(s["a"])({url:"/game/customer/checkAndSetDefault",method:"get"})}},cde6:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container customer-container"},[a("div",{directives:[{name:"show",rawName:"v-show",value:t.showSearch,expression:"showSearch"}],staticClass:"search-container"},[a("el-form",{ref:"queryForm",attrs:{model:t.queryParams,size:"small",inline:!0,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"玩家名称",prop:"name"}},[a("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入玩家名称",clearable:"","prefix-icon":"el-icon-search"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.name,callback:function(e){t.$set(t.queryParams,"name",e)},expression:"queryParams.name"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:t.handleQuery}},[t._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"small"},on:{click:t.resetQuery}},[t._v("重置")])],1)],1)],1),a("div",{staticClass:"toolbar-container"},[a("div",{staticClass:"toolbar-left"},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["game:customer:add"],expression:"['game:customer:add']"}],staticClass:"action-btn",attrs:{type:"primary",icon:"el-icon-plus",size:"small"},on:{click:t.handleAdd}},[t._v("新增玩家")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["game:customer:edit"],expression:"['game:customer:edit']"}],staticClass:"action-btn",attrs:{type:"success",icon:"el-icon-edit",size:"small",disabled:t.single},on:{click:t.handleUpdate}},[t._v("批量修改")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["game:customer:remove"],expression:"['game:customer:remove']"}],staticClass:"action-btn",attrs:{type:"danger",icon:"el-icon-delete",size:"small",disabled:t.multiple},on:{click:t.handleDelete}},[t._v("批量删除")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["game:customer:export"],expression:"['game:customer:export']"}],staticClass:"action-btn",attrs:{type:"info",icon:"el-icon-download",size:"small"},on:{click:t.handleExport}},[t._v("导出数据")])],1),a("div",{staticClass:"toolbar-right"},[a("right-toolbar",{attrs:{showSearch:t.showSearch},on:{"update:showSearch":function(e){t.showSearch=e},"update:show-search":function(e){t.showSearch=e},queryTable:t.getList}})],1)]),a("div",{staticClass:"table-container"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"customer-table",attrs:{data:t.customerList,stripe:"",border:"","header-cell-style":{background:"#f8f9fa",color:"#606266",fontWeight:"bold"}},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),t.isAdmin?a("el-table-column",{attrs:{label:"用户ID",align:"center",prop:"sysUserId","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"user-id"},[a("i",{staticClass:"el-icon-postcard"}),a("span",[t._v(t._s(e.row.sysUserId))])])]}}],null,!1,3770044936)}):t._e(),a("el-table-column",{attrs:{label:"玩家名称",align:"center",prop:"name","min-width":"150"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"player-name"},[a("i",{staticClass:"el-icon-user"}),a("span",[t._v(t._s(e.row.name))])])]}}])}),a("el-table-column",{attrs:{label:"福彩下注",align:"center",prop:"fcTouzhu","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"amount-display fc-bet"},[a("span",{staticClass:"amount-value"},[t._v(t._s(t.formatAmount(e.row.fcTouzhu)))])])]}}])}),a("el-table-column",{attrs:{label:"体彩下注",align:"center",prop:"tcTouzhu","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"amount-display tc-bet"},[a("span",{staticClass:"amount-value"},[t._v(t._s(t.formatAmount(e.row.tcTouzhu)))])])]}}])}),a("el-table-column",{attrs:{label:"福彩中奖",align:"center",prop:"fcZj","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"amount-display fc-win"},[a("span",{staticClass:"amount-value"},[t._v(t._s(t.formatAmount(e.row.fcZj)))])])]}}])}),a("el-table-column",{attrs:{label:"体彩中奖",align:"center",prop:"tcZj","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"amount-display tc-win"},[a("span",{staticClass:"amount-value"},[t._v(t._s(t.formatAmount(e.row.tcZj)))])])]}}])}),a("el-table-column",{attrs:{label:"总下注金额",align:"center",prop:"totalBetAmount","min-width":"140"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"amount-display total-bet"},[a("span",{staticClass:"amount-value total"},[t._v(t._s(t.formatAmount(e.row.totalBetAmount)))])])]}}])}),a("el-table-column",{attrs:{label:"总中奖金额",align:"center",prop:"totalWinAmount","min-width":"140"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"amount-display total-win"},[a("span",{staticClass:"amount-value total"},[t._v(t._s(t.formatAmount(e.row.totalWinAmount)))])])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width","min-width":"140"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"action-buttons"},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["game:customer:edit"],expression:"['game:customer:edit']"}],staticClass:"action-btn-mini edit-btn",attrs:{size:"mini",type:"primary"},on:{click:function(a){return t.handleUpdate(e.row)}}},[t._v("修改")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["game:customer:remove"],expression:"['game:customer:remove']"}],staticClass:"action-btn-mini delete-btn",attrs:{size:"mini",type:"danger"},on:{click:function(a){return t.handleDelete(e.row)}}},[t._v("删除")])],1)]}}])})],1)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total>0"}],attrs:{total:t.total,page:t.queryParams.pageNum,limit:t.queryParams.pageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"pageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"pageSize",e)},pagination:t.getList}}),a("el-dialog",{staticClass:"customer-dialog",attrs:{title:t.title,visible:t.open,width:"600px","append-to-body":"","close-on-click-modal":!1,"before-close":t.handleDialogClose},on:{"update:visible":function(e){t.open=e}}},[a("div",{staticClass:"dialog-header-custom"},[a("div",{staticClass:"dialog-icon"},[a("i",{staticClass:"el-icon-user"})]),a("div",{staticClass:"dialog-title-info"},[a("h3",[t._v(t._s(t.title))]),a("p",[t._v(t._s(t.form.userId?"修改玩家的详细信息和数据统计":"创建新的玩家账户和初始数据"))])])]),a("el-form",{ref:"form",staticClass:"customer-form",attrs:{model:t.form,rules:t.rules,"label-width":"120px"}},[a("div",{staticClass:"form-section"},[a("div",{staticClass:"section-title"},[a("i",{staticClass:"el-icon-user-solid"}),a("span",[t._v("基本信息")])]),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"玩家名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入玩家名称","prefix-icon":"el-icon-user",clearable:""},model:{value:t.form.name,callback:function(e){t.$set(t.form,"name",e)},expression:"form.name"}})],1)],1)],1)],1),a("div",{staticClass:"form-section"},[a("div",{staticClass:"section-title"},[a("i",{staticClass:"el-icon-coin"}),a("span",[t._v("下注数据")])]),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"福彩下注",prop:"fcTouzhu"}},[a("el-input",{attrs:{placeholder:"0.00",clearable:""},model:{value:t.form.fcTouzhu,callback:function(e){t.$set(t.form,"fcTouzhu",e)},expression:"form.fcTouzhu"}},[a("template",{slot:"prepend"},[t._v("￥")])],2)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"体彩下注",prop:"tcTouzhu"}},[a("el-input",{attrs:{placeholder:"0.00",clearable:""},model:{value:t.form.tcTouzhu,callback:function(e){t.$set(t.form,"tcTouzhu",e)},expression:"form.tcTouzhu"}},[a("template",{slot:"prepend"},[t._v("￥")])],2)],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"总下注金额",prop:"totalBetAmount"}},[a("el-input",{attrs:{placeholder:"0.00","prefix-icon":"el-icon-money",clearable:""},model:{value:t.form.totalBetAmount,callback:function(e){t.$set(t.form,"totalBetAmount",e)},expression:"form.totalBetAmount"}},[a("template",{slot:"prepend"},[t._v("￥")])],2)],1)],1)],1)],1),a("div",{staticClass:"form-section"},[a("div",{staticClass:"section-title"},[a("i",{staticClass:"el-icon-trophy"}),a("span",[t._v("中奖数据")])]),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"福彩中奖",prop:"fcZj"}},[a("el-input",{attrs:{placeholder:"0.00",clearable:""},model:{value:t.form.fcZj,callback:function(e){t.$set(t.form,"fcZj",e)},expression:"form.fcZj"}},[a("template",{slot:"prepend"},[t._v("￥")])],2)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"体彩中奖",prop:"tcZj"}},[a("el-input",{attrs:{placeholder:"0.00",clearable:""},model:{value:t.form.tcZj,callback:function(e){t.$set(t.form,"tcZj",e)},expression:"form.tcZj"}},[a("template",{slot:"prepend"},[t._v("￥")])],2)],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"总中奖金额",prop:"totalWinAmount"}},[a("el-input",{attrs:{placeholder:"0.00","prefix-icon":"el-icon-star-on",clearable:""},model:{value:t.form.totalWinAmount,callback:function(e){t.$set(t.form,"totalWinAmount",e)},expression:"form.totalWinAmount"}},[a("template",{slot:"prepend"},[t._v("￥")])],2)],1)],1)],1)],1)]),a("div",{staticClass:"dialog-footer-custom",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{staticClass:"cancel-btn",on:{click:t.cancel}},[a("i",{staticClass:"el-icon-close"}),t._v(" 取消 ")]),a("el-button",{staticClass:"submit-btn",attrs:{type:"primary"},on:{click:t.submitForm}},[a("i",{staticClass:"el-icon-check"}),t._v(" "+t._s(t.form.userId?"保存修改":"创建玩家")+" ")])],1)],1)],1)},n=[],o=a("5530"),l=(a("caad"),a("d81d"),a("b0c0"),a("b680"),a("d3b7"),a("2532"),a("0643"),a("a573"),a("0901")),i={name:"Customer",data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,customerList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,name:null,totalBetAmount:null,totalWinAmount:null},form:{},rules:{name:[{required:!0,message:"玩家名称不能为空",trigger:"blur"}]}}},computed:{isAdmin:function(){return this.$store.getters.roles.includes("admin")||"admin"===this.$store.getters.name}},created:function(){this.getList()},methods:{getList:function(){var t=this;this.loading=!0,Object(l["e"])(this.queryParams).then((function(e){t.customerList=e.rows,t.total=e.total,t.loading=!1}))},cancel:function(){this.open=!1,this.reset()},handleDialogClose:function(t){var e=this;this.$confirm("确认关闭？未保存的数据将丢失。","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.reset(),t()})).catch((function(){}))},reset:function(){this.form={userId:null,name:null,totalBetAmount:null,totalWinAmount:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(t){this.ids=t.map((function(t){return t.userId})),this.single=1!==t.length,this.multiple=!t.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加玩家管理"},handleUpdate:function(t){var e=this;this.reset();var a=t.userId||this.ids;Object(l["d"])(a).then((function(t){e.form=t.data,e.open=!0,e.title="修改玩家管理"}))},submitForm:function(){var t=this;this.$refs["form"].validate((function(e){e&&(null!=t.form.userId?Object(l["f"])(t.form).then((function(e){t.$modal.msgSuccess("修改成功"),t.open=!1,t.getList()})):Object(l["a"])(t.form).then((function(e){t.$modal.msgSuccess("新增成功"),t.open=!1,t.getList()})))}))},handleDelete:function(t){var e=this,a=t.userId||this.ids;this.$modal.confirm('是否确认删除玩家管理编号为"'+a+'"的数据项？').then((function(){return Object(l["c"])(a)})).then((function(){e.getList(),e.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("game/customer/export",Object(o["a"])({},this.queryParams),"customer_".concat((new Date).getTime(),".xlsx"))},formatAmount:function(t){if(!t&&0!==t)return"￥0.00";var e=parseFloat(t);return"￥"+e.toFixed(2)}}},r=i,c=(a("063e"),a("2877")),u=Object(c["a"])(r,s,n,!1,null,"6e760cd2",null);e["default"]=u.exports},ee59:function(t,e,a){}}]);