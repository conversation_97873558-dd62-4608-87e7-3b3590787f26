<!DOCTYPE html><html><head><meta charset=utf-8><meta http-equiv=X-UA-Compatible content="IE=edge,chrome=1"><meta name=renderer content=webkit><meta name=viewport content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no"><link rel=icon href=/favicon.ico><title>游戏管理系统</title><!--[if lt IE 11]><script>window.location.href='/html/ie.html';</script><![endif]--><style>html,
    body,
    #app {
      height: 100%;
      margin: 0px;
      padding: 0px;
    }
    .chromeframe {
      margin: 0.2em 0;
      background: #ccc;
      color: #000;
      padding: 0.2em 0;
    }

    #loader-wrapper {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 999999;
    }

    #loader {
      display: block;
      position: relative;
      left: 50%;
      top: 50%;
      width: 150px;
      height: 150px;
      margin: -75px 0 0 -75px;
      border-radius: 50%;
      border: 3px solid transparent;
      border-top-color: #FFF;
      -webkit-animation: spin 2s linear infinite;
      -ms-animation: spin 2s linear infinite;
      -moz-animation: spin 2s linear infinite;
      -o-animation: spin 2s linear infinite;
      animation: spin 2s linear infinite;
      z-index: 1001;
    }

    #loader:before {
      content: "";
      position: absolute;
      top: 5px;
      left: 5px;
      right: 5px;
      bottom: 5px;
      border-radius: 50%;
      border: 3px solid transparent;
      border-top-color: #FFF;
      -webkit-animation: spin 3s linear infinite;
      -moz-animation: spin 3s linear infinite;
      -o-animation: spin 3s linear infinite;
      -ms-animation: spin 3s linear infinite;
      animation: spin 3s linear infinite;
    }

    #loader:after {
      content: "";
      position: absolute;
      top: 15px;
      left: 15px;
      right: 15px;
      bottom: 15px;
      border-radius: 50%;
      border: 3px solid transparent;
      border-top-color: #FFF;
      -moz-animation: spin 1.5s linear infinite;
      -o-animation: spin 1.5s linear infinite;
      -ms-animation: spin 1.5s linear infinite;
      -webkit-animation: spin 1.5s linear infinite;
      animation: spin 1.5s linear infinite;
    }


    @-webkit-keyframes spin {
      0% {
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
      }
      100% {
        -webkit-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
      }
    }

    @keyframes spin {
      0% {
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
      }
      100% {
        -webkit-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
      }
    }


    #loader-wrapper .loader-section {
      position: fixed;
      top: 0;
      width: 51%;
      height: 100%;
      background: #7171C6;
      z-index: 1000;
      -webkit-transform: translateX(0);
      -ms-transform: translateX(0);
      transform: translateX(0);
    }

    #loader-wrapper .loader-section.section-left {
      left: 0;
    }

    #loader-wrapper .loader-section.section-right {
      right: 0;
    }


    .loaded #loader-wrapper .loader-section.section-left {
      -webkit-transform: translateX(-100%);
      -ms-transform: translateX(-100%);
      transform: translateX(-100%);
      -webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
      transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
    }

    .loaded #loader-wrapper .loader-section.section-right {
      -webkit-transform: translateX(100%);
      -ms-transform: translateX(100%);
      transform: translateX(100%);
      -webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
      transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
    }

    .loaded #loader {
      opacity: 0;
      -webkit-transition: all 0.3s ease-out;
      transition: all 0.3s ease-out;
    }

    .loaded #loader-wrapper {
      visibility: hidden;
      -webkit-transform: translateY(-100%);
      -ms-transform: translateY(-100%);
      transform: translateY(-100%);
      -webkit-transition: all 0.3s 1s ease-out;
      transition: all 0.3s 1s ease-out;
    }

    .no-js #loader-wrapper {
      display: none;
    }

    .no-js h1 {
      color: #222222;
    }

    #loader-wrapper .load_title {
      font-family: 'Open Sans';
      color: #FFF;
      font-size: 19px;
      width: 100%;
      text-align: center;
      z-index: 9999999999999;
      position: absolute;
      top: 60%;
      opacity: 1;
      line-height: 30px;
    }

    #loader-wrapper .load_title span {
      font-weight: normal;
      font-style: italic;
      font-size: 13px;
      color: #FFF;
      opacity: 0.5;
    }</style><link href=/static/css/chunk-libs.20020923.css rel=stylesheet><link href=/static/css/app.a62ec67a.css rel=stylesheet></head><body><div id=app><div id=loader-wrapper><div id=loader></div><div class="loader-section section-left"></div><div class="loader-section section-right"></div><div class=load_title>正在加载系统资源，请耐心等待</div></div></div><script>(function(c){function e(e){for(var d,u,a=e[0],k=e[1],t=e[2],b=0,r=[];b<a.length;b++)u=a[b],Object.prototype.hasOwnProperty.call(f,u)&&f[u]&&r.push(f[u][0]),f[u]=0;for(d in k)Object.prototype.hasOwnProperty.call(k,d)&&(c[d]=k[d]);o&&o(e);while(r.length)r.shift()();return h.push.apply(h,t||[]),n()}function n(){for(var c,e=0;e<h.length;e++){for(var n=h[e],d=!0,u=1;u<n.length;u++){var a=n[u];0!==f[a]&&(d=!1)}d&&(h.splice(e--,1),c=k(k.s=n[0]))}return c}var d={},u={runtime:0},f={runtime:0},h=[];function a(c){return k.p+"static/js/"+({}[c]||c)+"."+{"chunk-005cb0c7":"82607ac7","chunk-0132f831":"616a49df","chunk-014340bb":"36d43c17","chunk-0238e9b0":"dcdc5cc0","chunk-0917f0be":"2ea894b1","chunk-0a804b6f":"63a0b0a8","chunk-0d7b6c3a":"1736650c","chunk-11aeb4ab":"fad94df5","chunk-12a0756a":"d2ced2cc","chunk-17313620":"535c04b9","chunk-1d7d97ec":"553344c8","chunk-0e6b74b6":"ea20e506","chunk-22425499":"081b4a67","chunk-2b02de32":"cd271ade","chunk-69cfa8fc":"cfacee69","chunk-6c306530":"4a4fcbd7","chunk-f4845968":"7dc14b4b","chunk-210ca3e9":"8508fc67","chunk-210ce324":"aa3d4308","chunk-240d66cb":"96c64c74","chunk-29427c6a":"11c515cf","chunk-2d0a3b04":"5457908c","chunk-2d0abc0c":"58cefa70","chunk-2d22e11b":"86d30abf","chunk-2d0b23c7":"28e95b88","chunk-2d0b2b28":"6267aaf1","chunk-1c838d18":"6ee2d466","chunk-2d0bce05":"442c9976","chunk-2d0c1f75":"6b163753","chunk-2d0c8e18":"64c86e91","chunk-2d0d0818":"a74019ee","chunk-2d0d9f81":"e5c53c73","chunk-2d0da2ea":"83ff3da8","chunk-2d0e1f8f":"b16dec74","chunk-2d0e2366":"9b139797","chunk-269fa9d1":"73e35f8d","chunk-70f5b262":"3039c4fb","chunk-2d20955d":"100d04a8","chunk-2d2102b6":"90b53344","chunk-31eae13f":"4db4200f","chunk-3339808c":"31e3971d","chunk-0d5b0085":"a66e67f0","chunk-60006966":"98ead684","chunk-527d2176":"140633cf","chunk-2d22252c":"e077c22d","chunk-2d230898":"3844d967","chunk-3b69bc00":"7f571b31","chunk-440315e0":"cd0d4390","chunk-440459d6":"1bba1e62","chunk-442b6d8a":"256ebc49","chunk-442bcfd2":"c6caa58b","chunk-442e3a7e":"4d3c7f2e","chunk-4430e4fa":"77e59315","chunk-443166e8":"66ac407e","chunk-46f2cf5c":"104ae546","chunk-4a3a3d92":"8427f467","chunk-4f55a4ac":"08f43ac2","chunk-582b2a7a":"500f45f7","chunk-5b83c289":"317b0eed","chunk-27d58c84":"08876964","chunk-5bb73842":"dfe42524","chunk-202bad4a":"f5e78ab8","chunk-25ccdf6b":"166f0e2f","chunk-2d0d38ff":"dfa22205","chunk-2d0de3b1":"124ce560","chunk-3f93175c":"40f76b9e","chunk-5c5a08c7":"12b2cd6d","chunk-6746b265":"1c1a5b2c","chunk-68702101":"b7796f1b","chunk-77c40dc3":"c1267b6f","chunk-78d2d26b":"146980f2","chunk-7910d0ad":"849a4e7f","chunk-8579d4da":"36613540","chunk-8ee3fc10":"ac913141","chunk-b1812390":"7e5f8a74","chunk-ba58f81a":"d9a18b88","chunk-c032f9a2":"d0c2a5e9","chunk-0e3c7648":"c08caf8f","chunk-7dd7cb19":"e3a082a7","chunk-8adb838a":"f2315444","chunk-d19c1a98":"89ce38a2","chunk-daf6ef8a":"5be3a599","chunk-e1a6d904":"2cd6dda6","chunk-e648d5fe":"2ca75a65","chunk-70ca55ea":"4a3dd968","chunk-6fdfa19d":"1af94cfc","chunk-2d0de310":"677437b8"}[c]+".js"}function k(e){if(d[e])return d[e].exports;var n=d[e]={i:e,l:!1,exports:{}};return c[e].call(n.exports,n,n.exports,k),n.l=!0,n.exports}k.e=function(c){var e=[],n={"chunk-0917f0be":1,"chunk-0a804b6f":1,"chunk-0d7b6c3a":1,"chunk-11aeb4ab":1,"chunk-17313620":1,"chunk-0e6b74b6":1,"chunk-240d66cb":1,"chunk-29427c6a":1,"chunk-1c838d18":1,"chunk-269fa9d1":1,"chunk-70f5b262":1,"chunk-3339808c":1,"chunk-527d2176":1,"chunk-46f2cf5c":1,"chunk-4a3a3d92":1,"chunk-4f55a4ac":1,"chunk-5b83c289":1,"chunk-5bb73842":1,"chunk-202bad4a":1,"chunk-25ccdf6b":1,"chunk-5c5a08c7":1,"chunk-6746b265":1,"chunk-77c40dc3":1,"chunk-7910d0ad":1,"chunk-ba58f81a":1,"chunk-0e3c7648":1,"chunk-7dd7cb19":1,"chunk-daf6ef8a":1,"chunk-e648d5fe":1};u[c]?e.push(u[c]):0!==u[c]&&n[c]&&e.push(u[c]=new Promise((function(e,n){for(var d="static/css/"+({}[c]||c)+"."+{"chunk-005cb0c7":"31d6cfe0","chunk-0132f831":"31d6cfe0","chunk-014340bb":"31d6cfe0","chunk-0238e9b0":"31d6cfe0","chunk-0917f0be":"5f308efd","chunk-0a804b6f":"e2727898","chunk-0d7b6c3a":"3f5ce03c","chunk-11aeb4ab":"b6ec4146","chunk-12a0756a":"31d6cfe0","chunk-17313620":"cd7901ef","chunk-1d7d97ec":"31d6cfe0","chunk-0e6b74b6":"74496202","chunk-22425499":"31d6cfe0","chunk-2b02de32":"31d6cfe0","chunk-69cfa8fc":"31d6cfe0","chunk-6c306530":"31d6cfe0","chunk-f4845968":"31d6cfe0","chunk-210ca3e9":"31d6cfe0","chunk-210ce324":"31d6cfe0","chunk-240d66cb":"2d96dbd1","chunk-29427c6a":"896fa819","chunk-2d0a3b04":"31d6cfe0","chunk-2d0abc0c":"31d6cfe0","chunk-2d22e11b":"31d6cfe0","chunk-2d0b23c7":"31d6cfe0","chunk-2d0b2b28":"31d6cfe0","chunk-1c838d18":"4abfb2e7","chunk-2d0bce05":"31d6cfe0","chunk-2d0c1f75":"31d6cfe0","chunk-2d0c8e18":"31d6cfe0","chunk-2d0d0818":"31d6cfe0","chunk-2d0d9f81":"31d6cfe0","chunk-2d0da2ea":"31d6cfe0","chunk-2d0e1f8f":"31d6cfe0","chunk-2d0e2366":"31d6cfe0","chunk-269fa9d1":"f2078e28","chunk-70f5b262":"1f26b476","chunk-2d20955d":"31d6cfe0","chunk-2d2102b6":"31d6cfe0","chunk-31eae13f":"31d6cfe0","chunk-3339808c":"6dfe926d","chunk-0d5b0085":"31d6cfe0","chunk-60006966":"31d6cfe0","chunk-527d2176":"c5292c00","chunk-2d22252c":"31d6cfe0","chunk-2d230898":"31d6cfe0","chunk-3b69bc00":"31d6cfe0","chunk-440315e0":"31d6cfe0","chunk-440459d6":"31d6cfe0","chunk-442b6d8a":"31d6cfe0","chunk-442bcfd2":"31d6cfe0","chunk-442e3a7e":"31d6cfe0","chunk-4430e4fa":"31d6cfe0","chunk-443166e8":"31d6cfe0","chunk-46f2cf5c":"17fbdb6b","chunk-4a3a3d92":"7234e304","chunk-4f55a4ac":"5a402cd2","chunk-582b2a7a":"31d6cfe0","chunk-5b83c289":"ce2a2394","chunk-27d58c84":"31d6cfe0","chunk-5bb73842":"84f98409","chunk-202bad4a":"53c68006","chunk-25ccdf6b":"ecea2c5f","chunk-2d0d38ff":"31d6cfe0","chunk-2d0de3b1":"31d6cfe0","chunk-3f93175c":"31d6cfe0","chunk-5c5a08c7":"1261495f","chunk-6746b265":"3e10cd59","chunk-68702101":"31d6cfe0","chunk-77c40dc3":"6c13694c","chunk-78d2d26b":"31d6cfe0","chunk-7910d0ad":"e72935c1","chunk-8579d4da":"31d6cfe0","chunk-8ee3fc10":"31d6cfe0","chunk-b1812390":"31d6cfe0","chunk-ba58f81a":"e8624ecc","chunk-c032f9a2":"31d6cfe0","chunk-0e3c7648":"ae25d191","chunk-7dd7cb19":"d7de9816","chunk-8adb838a":"31d6cfe0","chunk-d19c1a98":"31d6cfe0","chunk-daf6ef8a":"bf8f9dd9","chunk-e1a6d904":"31d6cfe0","chunk-e648d5fe":"bbc9fa95","chunk-70ca55ea":"31d6cfe0","chunk-6fdfa19d":"31d6cfe0","chunk-2d0de310":"31d6cfe0"}[c]+".css",f=k.p+d,h=document.getElementsByTagName("link"),a=0;a<h.length;a++){var t=h[a],b=t.getAttribute("data-href")||t.getAttribute("href");if("stylesheet"===t.rel&&(b===d||b===f))return e()}var r=document.getElementsByTagName("style");for(a=0;a<r.length;a++){t=r[a],b=t.getAttribute("data-href");if(b===d||b===f)return e()}var o=document.createElement("link");o.rel="stylesheet",o.type="text/css",o.onload=e,o.onerror=function(e){var d=e&&e.target&&e.target.src||f,h=new Error("Loading CSS chunk "+c+" failed.\n("+d+")");h.code="CSS_CHUNK_LOAD_FAILED",h.request=d,delete u[c],o.parentNode.removeChild(o),n(h)},o.href=f;var i=document.getElementsByTagName("head")[0];i.appendChild(o)})).then((function(){u[c]=0})));var d=f[c];if(0!==d)if(d)e.push(d[2]);else{var h=new Promise((function(e,n){d=f[c]=[e,n]}));e.push(d[2]=h);var t,b=document.createElement("script");b.charset="utf-8",b.timeout=120,k.nc&&b.setAttribute("nonce",k.nc),b.src=a(c);var r=new Error;t=function(e){b.onerror=b.onload=null,clearTimeout(o);var n=f[c];if(0!==n){if(n){var d=e&&("load"===e.type?"missing":e.type),u=e&&e.target&&e.target.src;r.message="Loading chunk "+c+" failed.\n("+d+": "+u+")",r.name="ChunkLoadError",r.type=d,r.request=u,n[1](r)}f[c]=void 0}};var o=setTimeout((function(){t({type:"timeout",target:b})}),12e4);b.onerror=b.onload=t,document.head.appendChild(b)}return Promise.all(e)},k.m=c,k.c=d,k.d=function(c,e,n){k.o(c,e)||Object.defineProperty(c,e,{enumerable:!0,get:n})},k.r=function(c){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(c,"__esModule",{value:!0})},k.t=function(c,e){if(1&e&&(c=k(c)),8&e)return c;if(4&e&&"object"===typeof c&&c&&c.__esModule)return c;var n=Object.create(null);if(k.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:c}),2&e&&"string"!=typeof c)for(var d in c)k.d(n,d,function(e){return c[e]}.bind(null,d));return n},k.n=function(c){var e=c&&c.__esModule?function(){return c["default"]}:function(){return c};return k.d(e,"a",e),e},k.o=function(c,e){return Object.prototype.hasOwnProperty.call(c,e)},k.p="/",k.oe=function(c){throw console.error(c),c};var t=window["webpackJsonp"]=window["webpackJsonp"]||[],b=t.push.bind(t);t.push=e,t=t.slice();for(var r=0;r<t.length;r++)e(t[r]);var o=b;n()})([]);</script><script src=/static/js/chunk-elementUI.ef7743f1.js></script><script src=/static/js/chunk-libs.22d0f198.js></script><script src=/static/js/app.2baf9869.js></script></body></html>