{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetStatistics.vue?vue&type=template&id=10ce3258&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetStatistics.vue", "mtime": 1756002066115}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750942930085}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750942928768}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}